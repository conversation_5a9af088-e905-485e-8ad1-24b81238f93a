import java.util.*;

// Mock classes for testing
class ConstraintViolation {
    private String message;
    public ConstraintViolation(String message) { this.message = message; }
    public String getMessage() { return message; }
}

class ConstraintViolationException extends Exception {
    private Set<ConstraintViolation> violations = new HashSet<>();
    public ConstraintViolationException(Set<ConstraintViolation> violations) { this.violations = violations; }
    public Set<ConstraintViolation> getConstraintViolations() { return violations; }
}

public class test_khcn_syntax {
    public static void main(String[] args) {
        try {
            // Test the problematic code pattern
            Set<ConstraintViolation> violations = new HashSet<>();
            violations.add(new ConstraintViolation("Ngay error"));
            violations.add(new ConstraintViolation("Normal error"));
            
            ConstraintViolationException ex = new ConstraintViolationException(violations);
            
            List<String> errors = new ArrayList<>();
            List<String> dateErrors = new ArrayList<>();
            
            for (ConstraintViolation constraintViolation : ex.getConstraintViolations()) {
                if (constraintViolation.getMessage().contains("Ngay") || 
                    constraintViolation.getMessage().contains("ThoiDiemXuLy")) {
                    dateErrors.add(constraintViolation.getMessage());
                } else {
                    errors.add(constraintViolation.getMessage());
                }
            }
            
            // Test the String.join with trim
            String result1 = "Truong" + "<<" + String.join(",", dateErrors).trim() + ">>" + " co dinh dang ngay khong dung";
            String result2 = "Truong" + "<<" + String.join(",", errors).trim() + ">>" + " khong hop le";
            
            System.out.println("Date errors result: " + result1);
            System.out.println("Normal errors result: " + result2);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
