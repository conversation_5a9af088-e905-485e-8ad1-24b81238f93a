# API Liên thông Bộ Khoa học và Công nghệ (KHCN)

## Tổng quan

API này được phát triển để hỗ trợ liên thông hồ sơ với Bộ Khoa học và <PERSON>ô<PERSON> nghệ (KHCN), tư<PERSON><PERSON> tự như API DVCLT hiện có.

## Cấu trúc thành phần

### 1. DTO Classes
- `KHCNDossierDataDto` - DTO cho dữ liệu hồ sơ KHCN
- `KHCNDossierTrackingDataDto` - DTO cho dữ liệu tracking hồ sơ KHCN  
- `KHCNResultDto` - DTO cho response result
- `KHCNHoSoRequestDto` - DTO cho request nhận hồ sơ
- `KHCNHoSoResponseDto` - DTO cho response nhận hồ sơ
- `KHCNPostDto` - DTO cho cập nhật trạng thái

### 2. Service & Repository
- `KHCNService` - Service xử lý logic nghiệp vụ
- `KHCNLog` - Entity để lưu log
- `KHCNLogRepository` - Repository để truy cập dữ liệu

### 3. Controller
- `KHCNController` - REST Controller xử lý các API endpoints

## API Endpoints

### 1. Nhận hồ sơ KHCN
```
POST /api/lienthongKHCN/nhanHoSoKHCN
```

**Headers:**
- `securityKey`: Khóa bảo mật (optional)
- `Content-Type`: application/json

**Request Body:**
```json
[
  {
    "maDonVi": "string",
    "maHoSo": "string", 
    "maHoSoLienThong": "string",
    "maTTHC": "string",
    "tenTTHC": "string",
    "module": "string",
    "ngayTiepNhan": "20231201120000",
    "techId": "string",
    "data": "string",
    "fileDinhKem": [
      {
        "id": 1,
        "loaiGiayTo": 1,
        "tenGiayTo": "string",
        "tenTepDinhKem": "string",
        "huyGiayTo": 0,
        "duLieuTepDinhKem": "base64_string",
        "size": 1024,
        "mimeType": "application/pdf"
      }
    ],
    "nguoiNop": "string",
    "soDienThoai": "string",
    "email": "string",
    "diaChi": "string",
    "ghiChu": "string"
  }
]
```

**Response:**
```json
{
  "status": "1",
  "message": "Đồng bộ hồ sơ KHCN thành công",
  "errorCode": null,
  "errorMessage": null,
  "data": null
}
```

### 2. Cập nhật trạng thái hồ sơ KHCN
```
POST /api/lienthongKHCN/capNhatTrangThaiHoSoKHCN
```

**Headers:**
- `securityKey`: Khóa bảo mật (optional)
- `Content-Type`: application/json

**Request Body:**
```json
[
  {
    "maDonVi": "string",
    "maHoSo": "string",
    "maHoSoLienThong": "string", 
    "maTTHC": "string",
    "trangThai": 1,
    "tenTrangThai": "string",
    "thoiDiemXuLy": "20231201120000",
    "nguoiXuLy": "string",
    "phongBanXuLy": "string",
    "noiDungXuLy": "string",
    "ketQuaXuLy": "string",
    "ghiChu": "string",
    "ngayHenTra": "string",
    "lyDoTuChoi": "string",
    "yeuCauBoSung": "string"
  }
]
```

### 3. Nhận hồ sơ KHCN (receiveRecord)
```
POST /api/lienthongKHCN/receiveRecord?kafkaEnable=true
```

**Headers:**
- `securityKey`: Khóa bảo mật (optional)
- `Content-Type`: application/json

**Request Body:**
```json
{
  "maDonVi": "string",
  "maHoSo": "string",
  "maHoSoMCDT": "string",
  "maTTHC": "string", 
  "module": "string",
  "ngayTiepNhan": "2023-12-01T12:00:00",
  "techId": "string",
  "data": "string",
  "fileDinhKem": [
    {
      "id": 1,
      "loaiGiayTo": 1,
      "tenGiayTo": "string",
      "tenTepDinhKem": "string",
      "huyGiayTo": 0,
      "duLieuTepDinhKem": "base64_string",
      "size": 1024
    }
  ]
}
```

**Response:**
```json
{
  "status": "200",
  "statusDescription": "Nhận hồ sơ KHCN thành công",
  "errorCode": "200", 
  "errorDescription": "Nhận hồ sơ KHCN thành công"
}
```

### 4. Cập nhật trạng thái hồ sơ KHCN (Post)
```
POST /api/lienthongKHCN/capNhatTrangThaiHoSoKHCNPost
```

**Request Body:**
```json
{
  "maTTHC": "string",
  "soHoSoLT": "string",
  "coQuanXuLy": 6,
  "maHoSo": "string",
  "trangThai": 1,
  "thoiGianThucHien": "string",
  "ghiChu": "string",
  "ketQuaXuLy": "string",
  "nguoiXuLy": "string",
  "chucDanh": "string",
  "phongBanXuLy": "string",
  "noiDungXuLy": "string",
  "ngayBatDau": "string",
  "ngayKetThucTheoQuyDinh": "string",
  "ngayHenTraTruoc": "string",
  "ngayHenTraMoi": "string",
  "hanBoSungHoSo": "string",
  "lyDoTuChoi": "string",
  "yeuCauBoSung": "string"
}
```

### 5. Lấy log KHCN
```
GET /api/lienthongKHCN/getLog?code=xxx&nationCode=xxx&api=xxx&status=1&error=false&page=0&size=20
```

### 6. Lấy log KHCN theo ID
```
GET /api/lienthongKHCN/getLog/{id}
```

### 7. Xử lý hồ sơ lỗi
```
POST /api/lienthongKHCN/nhanHoSoKHCNLoi
```

**Request Body:**
```json
["logId1", "logId2", "logId3"]
```

### 8. Xử lý cập nhật trạng thái lỗi
```
POST /api/lienthongKHCN/capNhatTrangThaiHoSoKHCNLoi
```

**Request Body:**
```json
["logId1", "logId2", "logId3"]
```

## Cấu hình

### Application Properties
```properties
# KHCN Configuration
digo.khcn.security-key=igate2.service.khcn08062023.test#TransformINS
digo.khcn.enable=true
digo.khcn.configid=khcn-config-id-placeholder

# Permission
vnpt.permission.khcn=manageDigo

# Kafka Configuration
spring.cloud.stream.bindings.KHCNDossierTrackingOut.destination=KHCNDossierTracking
spring.cloud.stream.bindings.KHCNDossierTrackingOut.group=KHCN
spring.cloud.stream.bindings.KHCNDossierOut.destination=KHCNDossier
spring.cloud.stream.bindings.KHCNDossierOut.group=KHCN
spring.cloud.stream.bindings.KHCNDossierLogIn.destination=KHCNDossierLog
spring.cloud.stream.bindings.KHCNDossierLogIn.group=KHCN

# Kafka Enable
digo.http.khcn.kafka.enable=true
digo.http.khcn.new.enable=false
```

## Bảo mật

API không yêu cầu xác thực đặc biệt:
- Header `securityKey` là tùy chọn, có thể truyền hoặc không
- API sẽ ghi nhận security key nếu có để phục vụ logging và tracking

## Logging

Tất cả các request/response được lưu vào MongoDB collection `khcnLog` với các thông tin:
- API endpoint
- Thời gian gọi
- Mã hồ sơ
- Request/Response body
- Trạng thái xử lý
- Thông tin lỗi (nếu có)

## Testing

Để test API, cần:
1. Gửi request với đúng format JSON
2. Header securityKey có thể có hoặc không
3. Kiểm tra response và log trong database
4. Verify các file đính kèm được upload thành công

## Lưu ý

- API tương tự như DVCLT nhưng dành riêng cho Bộ KHCN
- Không yêu cầu xác thực security key phức tạp
- Hỗ trợ upload file đính kèm dạng base64
- Có thể enable/disable Kafka streaming
- Hỗ trợ xử lý lại các hồ sơ lỗi
- Có đầy đủ validation và error handling
