import java.util.*;

public class test_syntax {
    public static void main(String[] args) {
        // Test String.join with List<String>
        List<String> errors = new ArrayList<>();
        errors.add("error1");
        errors.add("error2");
        
        String result = String.join(",", errors);
        System.out.println("Result: " + result);
        
        // Test with trim
        String resultWithTrim = String.join(",", errors).trim();
        System.out.println("Result with trim: " + resultWithTrim);
    }
}
