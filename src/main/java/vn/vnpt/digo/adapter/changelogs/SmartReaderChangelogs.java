package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2023-11-07-15-23")
public class SmartReaderChangelogs {
    @ChangeSet(order = "2023-11-07-15-43", id = "SmartReaderChangelogs::createSmartReaderService", author = "huytq")
    public void createSmartReaderService(MongoTemplate mongoTemplate, Environment environment){
        IntegrationService service = new IntegrationService();
        ObjectId deploymentId = new ObjectId(environment.getProperty("digo.microservice.deployment.id"));
        service.setId(new ObjectId("5f7c16069abb62f511890042"));
        service.setName("SmartReader service");
        service.setIntegratedUnit("SmartReader service");
        List<Parameters> params = new ArrayList<>();

        Parameters addFileURL = new Parameters("add_file_url", ListType.STRING, "https://api.idg.vnpt.vn/file-service/v1/addFile");
        params.add(addFileURL);

        Parameters ocrURL = new Parameters("ocr_url", ListType.STRING, "https://api.idg.vnpt.vn/rpa-service/aidigdoc/v1/ocr/{key}");
        params.add(ocrURL);

        Parameters tokenId = new Parameters("token_id", ListType.STRING, "05ad0b9f-a4b6-6fd0-e063-62199f0a0c6f");
        params.add(tokenId);

        Parameters tokenKey = new Parameters("token_key", ListType.STRING, "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJF1W1amNDjVQt5rU/5sSeC8diIlnBG9MUPXGt5TkMv5KFiexMQo4UL8trT6dNYjKBy+URxRKNXwpJNlb02e89UCAwEAAQ==");
        params.add(tokenKey);

        Parameters accessToken = new Parameters("access_token", ListType.STRING, "");
        params.add(accessToken);

        service.setParameters(params);
        service.setStatus(1);
        service.setIgnoreDeployment(true);
        mongoTemplate.save(service);

        IntegratedConfiguration config = new IntegratedConfiguration();
        IntegratedService integratedService = new IntegratedService(service);
        config.setService(integratedService);
        config.setParameters(params);
        config.setDeploymentId(deploymentId);
        config.setStatus(1);
        config.setDeleted(false);
        config.setName("SmartReader service");
        config.setApplyAll(true);

        // Set subsystem
        Subsystem subSystem = new Subsystem();
        subSystem.setId(new ObjectId("5f7c16069abb62f511880003"));
        SubsystemTrans subSystemTrans = new SubsystemTrans();
        subSystemTrans.setLanguageId((short)228);
        subSystemTrans.setName("VNPT iGate 2.0");
        subSystem.setTrans(Arrays.asList(subSystemTrans));
        config.setSubsystem(Arrays.asList(subSystem));

        // Set tag
        Tag tag = new Tag();
        TransTag tagTrans = new TransTag();
        tag.setId(new ObjectId("62fdab49d13be03598afb8fc"));
        tagTrans.setLanguageId((short)228);
        tagTrans.setName("Nhóm chức năng liên thông");
        tag.setTrans(Arrays.asList(tagTrans));
        config.setTag(tag);
        mongoTemplate.save(config);
    }

    @ChangeSet(order = "2023-11-23-09-35", id = "ItemChangeLogs::createSmartReaderLogIndex", author = "huytq")
    public void createSmartReaderLogIndex(MongoTemplate mongoTemplate) {
        try {
            Index hashIndex = new Index().on("hash", Sort.Direction.ASC);
            Index keyIndex = new Index().on("key", Sort.Direction.ASC);
            Index firstIndex = new Index().on("first", Sort.Direction.ASC);
            Index ownerFullNameIndex = new Index().on("owner.fullname", Sort.Direction.ASC);
            Index ownerIdentityNumberIndex = new Index().on("owner.identityNumber", Sort.Direction.ASC);
            Index ownerFullNameIdentityNumberIndex = new Index().on("owner.fullname", Sort.Direction.ASC).on("owner.identityNumber", Sort.Direction.ASC);
            Index agencyCodeIndex = new Index().on("agency.code", Sort.Direction.ASC);
            Index agencyNameIndex = new Index().on("agency.name", Sort.Direction.ASC);
            Index agencySubCodeIndex = new Index().on("agency.subCode", Sort.Direction.ASC);
            Index agencySubNameIndex = new Index().on("agency.subName", Sort.Direction.ASC);
            Index agencyCodeNameIndex = new Index().on("agency.code", Sort.Direction.ASC).on("agency.name", Sort.Direction.ASC);
            Index agencySubCodeSubNameIndex = new Index().on("agency.subCode", Sort.Direction.ASC).on("agency.subName", Sort.Direction.ASC);

            mongoTemplate.indexOps("smartReaderLog").ensureIndex(hashIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(keyIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(firstIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(ownerFullNameIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(ownerIdentityNumberIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(ownerFullNameIdentityNumberIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(agencyCodeIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(agencyNameIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(agencySubCodeIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(agencySubNameIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(agencyCodeNameIndex);
            mongoTemplate.indexOps("smartReaderLog").ensureIndex(agencySubCodeSubNameIndex);

        } catch (Exception ex){}
    }
}
