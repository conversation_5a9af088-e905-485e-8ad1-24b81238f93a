/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.MappingType;
import vn.vnpt.digo.adapter.pojo.MappingTypeDataAccess;
import vn.vnpt.digo.adapter.pojo.MappingTypeTrans;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2021-07-07-16-51")
public class PetitionNpsRMethodChangeLogs {
    
    @ChangeSet(order = "2021-07-07-16-52", id = "PetitionNpsRMethodChangeLogs::create", author = "mongtt")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        MappingType national = new MappingType();
        national.setId(new ObjectId("5fc0707a62681a8bef000017"));
        
        MappingTypeTrans vi = new MappingTypeTrans((short)228, "Mã hình thức tiếp nhận PAKN Cổng DVCQG", "Mapping hình thức tiếp nhận PAKN Cổng DVCQG");
        MappingTypeTrans en = new MappingTypeTrans((short)46, "Petition reception method DVCQG Portal", "Mapping reception method DVCQG Portal");
        List<MappingTypeTrans> trans = new ArrayList<>();
        trans.add(vi);
        trans.add(en);
        national.setTrans(trans);
        
        MappingTypeDataAccess source = new MappingTypeDataAccess();
        source.setEnable(Boolean.TRUE);
        source.setEndpoint("");
        national.setSourceDataAccess(source);
        
        MappingTypeDataAccess dest = new MappingTypeDataAccess();
        source.setEnable(Boolean.FALSE);
        national.setDestDataAccess(dest);
        
        national.setStatus(1);
        
        try {
            national.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2021-06-25T10:33:44.165+0100"));
            national.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2021-06-25T10:33:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            national.setCreatedDate(new Date());
            national.setUpdatedDate(new Date());
        }
        
        national.setIgnoreDeployment(true);
        
        national.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        
        mongoTemplate.insert(national);
    }
}
