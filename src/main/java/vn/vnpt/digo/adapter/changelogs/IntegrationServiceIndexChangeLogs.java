package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.BasicDBObject;
import com.mongodb.DB;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-10-20-09-44")
public class IntegrationServiceIndexChangeLogs {

    @ChangeSet(order = "2020-10-20-09-44", id = "IntegrationServiceIndexChangeLogs::createIndex1", author = "haimn")
    public void createIndex1(DB db) {
        Map<String, Object> textIndex = new HashMap<>();
        textIndex.put("name", "text");
        textIndex.put("integratedUnit", "text");
        textIndex.put("parameters.key", "text");
        textIndex.put("parameters.type.name", "text");
        db.getCollection("integrationService").createIndex(new BasicDBObject(textIndex), "searchIndex");
        db.getCollection("integrationService").createIndex(new BasicDBObject("status", 1), "statusIndex");
        db.getCollection("integrationService").createIndex(new BasicDBObject("createdDate", 1), "createTimeIndex");
    }
}
