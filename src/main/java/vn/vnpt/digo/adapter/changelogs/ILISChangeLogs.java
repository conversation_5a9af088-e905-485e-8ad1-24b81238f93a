package vn.vnpt.digo.adapter.changelogs;


import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ChangeLog(order = "2022-12-22-15-22")
public class ILISChangeLogs {

    @ChangeSet(order = "2022-12-22-15-22", id = "ILISChangeLogs::createMigrateTichHop", author = "thuannm")
    public void createMigrateTichHop(MongoTemplate mongoTemplate) {
        IntegrationService nationalPublicService = new IntegrationService();
        nationalPublicService.setId(new ObjectId("5f7c16069abb62f511890055"));
        nationalPublicService.setName("ILIS Service");
        nationalPublicService.setIntegratedUnit("ILIS Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters tokenUrl = new Parameters("token-url", ListType.STRING, "https://api.ngsp.gov.vn/token");
        lstParams.add(tokenUrl);

        Parameters consumerKey = new Parameters("consumer-key", ListType.STRING, "n_iA4SYv1Sxcd7T8tL8GDp33odsa");
        lstParams.add(consumerKey);

        Parameters consumerSecret = new Parameters("consumer-secret", ListType.STRING, "o6EhXLU5NJRcDwcdti37OOU_WFUa");
        lstParams.add(consumerSecret);

        Parameters tokenValue = new Parameters("token-value", ListType.STRING, "");
        lstParams.add(tokenValue);

        Parameters tiepNhanHoSoUrl = new Parameters("tiep-nhan-ho-so-url", ListType.STRING, "https://am.quangngai.gov.vn/vbdlis/1.0.0/hosomotcua/tiepnhan");
        lstParams.add(tiepNhanHoSoUrl);

        Parameters capNhatKetQuaUrl = new Parameters("cap-nhat-ket-qua-url", ListType.STRING, "https://am.quangngai.gov.vn/vbdlis/1.0.0/hosomotcua/capnhattrangthaitraketquahoso");
        lstParams.add(capNhatKetQuaUrl);

        Parameters capNhatTrangThaiBoSungUrl = new Parameters("cap-nhat-trang-thai-bo-sung-url", ListType.STRING, "https://am.quangngai.gov.vn/vbdlis/1.0.0/hosomotcua/capnhattrangthaibosunghoso");
        lstParams.add(capNhatTrangThaiBoSungUrl);

        Parameters thongBaoUrl = new Parameters("thong-bao-url", ListType.STRING, "https://am.quangngai.gov.vn/vbdlis/1.0.0/hosomotcua/thongbao");
        lstParams.add(thongBaoUrl);

        Parameters phanHoiSaiKetQuaUrl = new Parameters("phan-hoi-sai-ket-qua-url", ListType.STRING, "https://am.quangngai.gov.vn/vbdlis/1.0.0/hosomotcua/phanhoihososaiketqua");
        lstParams.add(phanHoiSaiKetQuaUrl);

        Parameters linkFileIlis = new Parameters("link-file-ilis", ListType.STRING, "https://dt.mplis.gov.vn/dc/Handlers/PdfHandler.ashx?DocId=");
        lstParams.add(linkFileIlis);

        Parameters agencyId = new Parameters("agency-id", ListType.STRING, "");
        lstParams.add(agencyId);

        Parameters dossierTaskStatusWaitWithHadresult = new Parameters("dossier-task-status-wait-with-had-result-id", ListType.STRING, "6206911d677d1a2e1527bcb3");
        lstParams.add(dossierTaskStatusWaitWithHadresult);

        Parameters isGetTokenLGSP = new Parameters("is-get-token-lgsp", ListType.STRING, false);
        lstParams.add(isGetTokenLGSP);

        Parameters clientId = new Parameters("client-id", ListType.STRING, "vilis-mobile-client");
        lstParams.add(clientId);

        Parameters clientSecret = new Parameters("client-secret", ListType.STRING, "n)3b^Q7g]Jd6T&$^");
        lstParams.add(clientSecret);

        Parameters userName = new Parameters("username", ListType.STRING, "integrate.qnm");
        lstParams.add(userName);

        Parameters passWord = new Parameters("password", ListType.STRING, "6v@mTbq4iD07Xu&$");
        lstParams.add(passWord);

        nationalPublicService.setParameters(lstParams);
        nationalPublicService.setStatus(1);
        try {
            nationalPublicService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2022-08-17T10:47:44.165+0100"));
            nationalPublicService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-08-17T10:47:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            nationalPublicService.setCreatedDate(new Date());
            nationalPublicService.setUpdatedDate(new Date());
        }
        nationalPublicService.setIgnoreDeployment(true);
        nationalPublicService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(nationalPublicService);
    }

}
