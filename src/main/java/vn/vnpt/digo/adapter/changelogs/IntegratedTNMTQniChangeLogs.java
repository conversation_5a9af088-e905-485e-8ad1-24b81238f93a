package vn.vnpt.digo.adapter.changelogs;


import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.util.ArrayList;
import java.util.List;

@ChangeLog(order = "2024-04-24-09-30")
public class IntegratedTNMTQniChangeLogs {
    @ChangeSet(order = "2024-04-24-09-30", id = "IntegratedTNMTQniChangeLogs::createIntegratedTNMTQni", author = "hoavhqni")
    public void createIntegratedTNMTQni(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(new ObjectId("5f8d0d55b54764421b7156a6"));
        integrationService.setName("Integrated TNMT Qni");
        integrationService.setIntegratedUnit("Integrated TNMT Qni Service");

        List<Parameters> params = new ArrayList<>();

        params.add(new Parameters("gateway-token", ListType.STRING, "https://api.monre.gov.vn/token"));
        params.add(new Parameters("dongbohoso-url", ListType.STRING, "https://api.monre.gov.vn/dvcthietyeu/demo/1.0/hoso/dongbohoso"));
        params.add(new Parameters("consumer-key", ListType.STRING, "cSiqiDDi2y0EPVE8EakuXo1yzY4a"));
        params.add(new Parameters("consumer-secret", ListType.STRING, "LMKsWvXU4ie0s3Jr10ydK0Com68a"));

        params.add(new Parameters("gateway-token-lgsp", ListType.STRING, "https://api.monre.gov.vn/token"));
        params.add(new Parameters("dongbohoso-url-lgsp", ListType.STRING, "https://api.monre.gov.vn/dvcthietyeu/demo/1.0/hoso/dongbohoso"));
        params.add(new Parameters("consumer-key-lgsp", ListType.STRING, "cSiqiDDi2y0EPVE8EakuXo1yzY4a"));
        params.add(new Parameters("consumer-secret-lgsp", ListType.STRING, "LMKsWvXU4ie0s3Jr10ydK0Com68a"));

        params.add(new Parameters("ignore-lgsp", ListType.STRING, "false"));
        params.add(new Parameters("ma-tinh", ListType.STRING, "51"));
        params.add(new Parameters("sso-url", ListType.STRING, "https://sso.quangngai.gov.vn/auth/realms/digo"));
        params.add(new Parameters("client-id", ListType.STRING, "svc-adapter"));
        params.add(new Parameters("client-secret", ListType.STRING, "0e2f17b7-e12d-448f-98c5-10941c417884"));
        integrationService.setParameters(params);
        integrationService.setStatus(1);
        integrationService.setWriteLog(true);

        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(integrationService);
    }
}
