package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.google.gson.Gson;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.dto.so_tnmt_agg.TrangThaiAGESBDto;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.util.ArrayList;
import java.util.List;

@ChangeLog(order = "2024-01-25-01-01")
public class AGGSoTNMTAGESBChangelogs {
    @ChangeSet(order = "2024-01-25-01-01", id = "AGGSoTNMTAGESBChangelogs::createIntegrationAGGSoTNMT", author = "linhnvv")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(new ObjectId());
        integrationService.setName("Integrated AGESB So TNMT AGG");
        integrationService.setIntegratedUnit("Integrated AGESB So TNMT AGG Service");

        List<Parameters> params = new ArrayList<>();

        // Api send data AG-ESB So TNMT
        params.add(new Parameters("url", ListType.STRING, "http://mekosoft.vn/motcua/ngoaitinh/nhan-ho-so/1.0/ho-so-moi"));
        // gateway-token
        params.add(new Parameters("gateway-token", ListType.STRING, "https://mekosoft.vn/oauth2/token"));
        // consumer-key
        params.add(new Parameters("consumer-key", ListType.STRING, "3vNRyKToDCrfzfQAfrupBliM600a"));
        // consumer-secret
        params.add(new Parameters("consumer-secret", ListType.STRING, "PG2VXHrumDJ8c6Xbt10bDfNgKTka"));


        List<TrangThaiAGESBDto> dsTrangThaiAGESB = new ArrayList<>();
        dsTrangThaiAGESB.add(new TrangThaiAGESBDto("traketqua", "12"));
        dsTrangThaiAGESB.add(new TrangThaiAGESBDto("tamdung", "15"));
        dsTrangThaiAGESB.add(new TrangThaiAGESBDto("giahan", "14"));
        dsTrangThaiAGESB.add(new TrangThaiAGESBDto("tieptuc", "16"));
        // Chuyển đổi danh sách đối tượng thành chuỗi JSON
        Gson gson = new Gson();
        String json = gson.toJson(dsTrangThaiAGESB);
        params.add(new Parameters("agesb-trangthai", ListType.STRING, json));
        integrationService.setParameters(params);
        integrationService.setStatus(1);

        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("646ab854f137d0251281610e"));
        mongoTemplate.insert(integrationService);
    }

}
