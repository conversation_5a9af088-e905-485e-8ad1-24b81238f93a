/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.MappingType;
import vn.vnpt.digo.adapter.pojo.MappingTypeDataAccess;
import vn.vnpt.digo.adapter.pojo.MappingTypeTrans;

@ChangeLog(order = "2020-11-30-10-18")
public class MapProvIdBRChangeLogs {
    @ChangeSet(order = "2020-11-30-10-18", id = "MapProvinceIdBusinessRegistrationChangeLogs::create", author = "datnt")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        MappingType national = new MappingType();
        national.setId(new ObjectId("5fc0707a62681a8bef000008"));
        
        MappingTypeTrans vi = new MappingTypeTrans((short)228, "Tỉnh thành ĐKDN", "Mapping danh mục tỉnh thành ĐKDN");
        MappingTypeTrans en = new MappingTypeTrans((short)46, "Province Business Registration", "Mapping province category business registration");
        List<MappingTypeTrans> trans = new ArrayList<>();
        trans.add(vi);
        trans.add(en);
        national.setTrans(trans);
        
        MappingTypeDataAccess source = new MappingTypeDataAccess();
        source.setEnable(Boolean.TRUE);
        source.setEndpoint("");
        national.setSourceDataAccess(source);
        
        MappingTypeDataAccess dest = new MappingTypeDataAccess();
        source.setEnable(Boolean.FALSE);
        national.setDestDataAccess(dest);
        
        national.setStatus(1);
        
        try {
            national.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-30T10:33:44.165+0100"));
            national.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-30T10:33:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            national.setCreatedDate(new Date());
            national.setUpdatedDate(new Date());
        }
        
        national.setIgnoreDeployment(true);
        
        national.setDeploymentId(new ObjectId("5dd2a75c2078ed3388ab043d"));
        
        mongoTemplate.insert(national);
    }
}
