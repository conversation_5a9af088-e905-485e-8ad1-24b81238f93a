package vn.vnpt.digo.adapter.changelogs.AGG_MonreIntegration;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2024-01-25-01-01")
public class AGGMonreIntegrationChangeLogs {

    @ChangeSet(order = "2024-01-25-01-01", id = "AGGMonreIntegrationChangeLogs::createAGGMonreIntegration", author = "dinhnq.agg")
    public void createAGGMonreIntegration(MongoTemplate mongoTemplate) {
        IntegrationService monreService = new IntegrationService();
        monreService.setId(new ObjectId("65b36ced21f1aeb5f2731f16"));
        monreService.setName("AGG Monre Integration Service");
        monreService.setIntegratedUnit("AGG Monre Integration Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters enableGetDossier = new Parameters("enable-get-dossier", ListType.BOOLEAN, false);
        lstParams.add(enableGetDossier);

        Parameters enableSendDossier = new Parameters("enable-send-dossier", ListType.BOOLEAN, false);
        lstParams.add(enableSendDossier);

        Parameters tokenUrl = new Parameters("token-url", ListType.STRING, "http://esb.angiang.gov.vn/getTokenDVCTYTNMT");
        lstParams.add(tokenUrl);

        Parameters consumerKey = new Parameters("consumer-key", ListType.STRING, "");
        lstParams.add(consumerKey);

        Parameters consumerSecret = new Parameters("consumer-secret", ListType.STRING, "");
        lstParams.add(consumerSecret);

        Parameters provinceCode = new Parameters("province-code", ListType.STRING, "89");
        lstParams.add(provinceCode);

        Parameters tokenValue = new Parameters("token-value", ListType.STRING, "");
        lstParams.add(tokenValue);

        Parameters getApplyDossierUrl = new Parameters("get-dossier-url", ListType.STRING, "http://esb.angiang.gov.vn/DVCTYTNMT/danhsachhoso");
        lstParams.add(getApplyDossierUrl);

        Parameters pushApplyDossierUrl = new Parameters("push-dossier-url", ListType.STRING, "http://esb.angiang.gov.vn/DVCTYTNMT/capnhathoso");
        lstParams.add(pushApplyDossierUrl);

        Parameters receivingVNPostId = new Parameters("receiving-vnpost-id", ListType.STRING, "5f8969018fffa53e4c073dee");
        lstParams.add(receivingVNPostId);

        Parameters receivingDirectlyId = new Parameters("receiving-directly-id", ListType.STRING, "5f8968888fffa53e4c073ded");
        lstParams.add(receivingDirectlyId);

        Parameters connectType = new Parameters("connect-type", ListType.STRING, "LGSP_ESB");
        lstParams.add(connectType);

        Parameters integratedType = new Parameters("integrated-type", ListType.STRING, "BTNMT");
        lstParams.add(integratedType);

        Parameters syncLocalDossier = new Parameters("sync-local-dossier", ListType.BOOLEAN, false);
        lstParams.add(syncLocalDossier);

        Parameters financialObligationId = new Parameters("financial-obligation-id", ListType.STRING, "60f6364e09cbf91d41f88859");
        lstParams.add(financialObligationId);

        Parameters dossierReceivedStatusId = new Parameters("dossier-received-status-id", ListType.STRING, "5ff8256594a7fa67df706ef0");
        lstParams.add(dossierReceivedStatusId);

        Parameters dossierRefusedStatusId = new Parameters("dossier-refused-status-id", ListType.STRING, "60ebf14b09cbf91d41f87f8d");
        lstParams.add(dossierRefusedStatusId);

        Parameters fromDate = new Parameters("from_date", ListType.STRING, "2022/04/12");
        lstParams.add(fromDate);

        Parameters procedureForm = new Parameters("procedure-form", ListType.STRING, "624ea3f1ce65a21305f3516b");
        lstParams.add(procedureForm);

        Parameters ProcedureFormDetail = new Parameters("procedure-form-detail", ListType.STRING, "623462c0f2e2ad4ed5787167");
        lstParams.add(ProcedureFormDetail);

        Parameters procedureProcessDefinitionId = new Parameters("procedure-process-definition-id", ListType.STRING, "");
        lstParams.add(procedureProcessDefinitionId);

        monreService.setParameters(lstParams);
        monreService.setStatus(1);
        try {
            monreService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-22T10:47:44.165+0100"));
            monreService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-22T10:47:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            monreService.setCreatedDate(new Date());
            monreService.setUpdatedDate(new Date());
        }
        monreService.setIgnoreDeployment(true);
        monreService.setDeploymentId(new ObjectId("646ab854f137d0251281610e"));
        mongoTemplate.insert(monreService);
    }
}
