package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.util.ArrayList;
import java.util.List;

@ChangeLog(order = "2024-10-10")
public class LienThongTSDCChangelogs {
    @ChangeSet(order = "2024-10-10", id = "LienThongTSDCChangelogs::createIntegrationLienThongTSDCChangelogs", author = "haudp.agg")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(new ObjectId());
        integrationService.setName("Integrated Liên thông TSDC");
        integrationService.setIntegratedUnit("Integrated Liên thông TSDC Service");

        List<Parameters> params = new ArrayList<>();

        params.add(new Parameters("AGG_TSDC_CONNID", ListType.STRING, "65d5c2259ed65"));
        params.add(new Parameters("AGG_TSDC_SET_INFO_PARAM_CALL", ListType.STRING, "app.apis.tttt.setTrangThai"));
        params.add(new Parameters("AGG_TSDC_DS_TRUONG_THPT", ListType.STRING, "[{\"MA_TRUONG\": \"2037793988\", \"TEN_TRUONG\": \"TRƯỜNG TRUNG CẤP KINH TẾ - KỸ THUẬT AN GIANG\"}]"));
        params.add(new Parameters("AGG_TSDC_KTS", ListType.STRING, "607701"));
        params.add(new Parameters("AGG_TSDC_GET_INFO_COOKIE", ListType.STRING, "PHPSESSID=h9juijrko8nghfsjppnbd4g4q6; PHPSESSID=h9juijrko8nghfsjppnbd4g4q6"));
        params.add(new Parameters("AGG_TSDC_SET_STATUS_API", ListType.STRING, "https://s19.vnedu.vn/v3/?call=app.apis.tttt.setTrangThai"));
        params.add(new Parameters("AGG_TSDC_SALT", ListType.STRING, "0009b558d9fff63735963af6ad030e85"));
        params.add(new Parameters("AGG_TSDC_SET_STATUS_COOKIE", ListType.STRING, "PHPSESSID=h9juijrko8nghfsjppnbd4g4q6; PHPSESSID=h9juijrko8nghfsjppnbd4g4q6; BIGipServerAPP_EDU_S19=!4E5dWoTGEPcuMsT/B2sWbtYh3fzvdvYdgb/9Xlfcwn5PWkGOCFusa7SZolaB6RjU56iN+2D031Js+Q=="));
        params.add(new Parameters("AGG_TSDC_GET_INFO_PARAM_CALL", ListType.STRING, "app.apis.tttt.getHoSoTheoMaDinhDanh"));
        params.add(new Parameters("AGG_TSDC_GET_INFO_API", ListType.STRING, "https://s19.vnedu.vn/v3/?call=app.apis.tttt.getHoSoTheoMaDinhDanh"));
        params.add(new Parameters("AGG_TSDC_THU_TUC", ListType.STRING, "39709"));

        integrationService.setParameters(params);
        integrationService.setStatus(1);

        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("646ab854f137d0251281610e"));
        mongoTemplate.insert(integrationService);
    }

}
