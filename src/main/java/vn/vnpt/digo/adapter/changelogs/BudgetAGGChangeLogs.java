package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ChangeLog(order = "2024-03-20-13-05")
public class BudgetAGGChangeLogs {
    @ChangeSet(order = "2024-03-20-13-05", id = "BudgetAGGChangeLogs::createBudgetAGG", author = "dinhnq.agg")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(new ObjectId("65fa397227cf14d852627f63"));
        integrationService.setName("AGG Ngân sách");
        integrationService.setIntegratedUnit("AGG Ngân sách Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters param1 = new Parameters("dossier-list-url", ListType.STRING, "http://esb.angiang.gov.vn/ngsp-api/apiCapMSDVQHNS/getDanhSachHoSoTheoNgay");
        lstParams.add(param1);

        Parameters param2 = new Parameters("dossier-detail-url", ListType.STRING, "http://esb.angiang.gov.vn/ngsp-api/apiCapMSDVQHNS/chiTietHoSo");
        lstParams.add(param2);

        Parameters param3 = new Parameters("dossier-by-period-url", ListType.STRING, "http://esb.angiang.gov.vn/ngsp-api/apiCapMSDVQHNS/getHoSoTheoKyTn");
        lstParams.add(param3);

        integrationService.setParameters(lstParams);
        integrationService.setStatus(1);
        try {
            integrationService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2024-03-20T01:05:18.133Z"));
            integrationService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2024-03-20T01:05:18.133Z"));
        } catch (ParseException | NullPointerException e) {
            integrationService.setCreatedDate(new Date());
            integrationService.setUpdatedDate(new Date());
        }
        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("646ab854f137d0251281610e"));
        mongoTemplate.insert(integrationService);
    }

}

