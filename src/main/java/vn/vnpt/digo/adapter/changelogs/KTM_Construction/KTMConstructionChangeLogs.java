package vn.vnpt.digo.adapter.changelogs.KTM_Construction;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;

import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ChangeLog(order = "2023-04-18")
public class KTMConstructionChangeLogs {
    @ChangeSet(order = "2023-04-18", id = "KTMConstructionChangeLogs::createKTMConstructIntegration", author = "cuongktm")
    public void createKTMConstructIntegration(MongoTemplate mongoTemplate ){
        IntegrationService constructKTMService = new IntegrationService();
        constructKTMService.setId(new ObjectId("643f9849e72fb063475e9483"));
        constructKTMService.setName("ConstructKTM Service");
        constructKTMService.setIntegratedUnit("ConstructKTM Service");
        List<Parameters> lstParams = new ArrayList<>();
        Parameters tokenUrl = new Parameters("token-url", ListType.STRING, "https://am.kontum.gov.vn/token");
        lstParams.add(tokenUrl);

        Parameters consumerKey = new Parameters("consumer-key", ListType.STRING, "GJbFfb2XnDMCJoTq0OfCD1kO3Zga");
        lstParams.add(consumerKey);

        Parameters consumerSecret = new Parameters("consumer-secret", ListType.STRING, "d3S6ZYqUGyqASLKrqKA41WhuiCka");
        lstParams.add(consumerSecret);

        Parameters tokenValue = new Parameters("token-value", ListType.STRING, "");
        lstParams.add(tokenValue);

        Parameters getDataFromProvince = new Parameters("getDataFromProvince", ListType.STRING, "https://am.kontum.gov.vn/cpxd/1.0.0/forward/rest/NhanThongTinHSTuDP");
        lstParams.add(getDataFromProvince);
        Parameters getProcessFromMinistry = new Parameters("getProcessFromMinistry", ListType.STRING, "https://am.kontum.gov.vn/cpxd/1.0.0/forward/rest/NhanTienTrinhHSTuBo");
        lstParams.add(getProcessFromMinistry);
        Parameters syncGPXDFromProvince = new Parameters("syncGPXDFromProvince", ListType.STRING, "https://am.kontum.gov.vn/cpxd/1.0.0/forward/rest/DongBoGPXDTuDP");
        lstParams.add(syncGPXDFromProvince);

        constructKTMService.setParameters(lstParams);
        constructKTMService.setStatus(1);
        try {
            constructKTMService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2022-08-17T10:47:44.165+0100"));
            constructKTMService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-08-17T10:47:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            constructKTMService.setCreatedDate(new Date());
            constructKTMService.setUpdatedDate(new Date());
        }
        constructKTMService.setIgnoreDeployment(true);
        constructKTMService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(constructKTMService);

    }

}

