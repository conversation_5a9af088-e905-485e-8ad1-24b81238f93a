package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.core.env.Environment;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@ChangeLog(order = "2023-09-15-08-01")
public class IWorkplaceChangeLogs {
    @ChangeSet(order = "2023-09-15-08-01", id = "IWorkplaceChangeLogs::createIWorkplaceParams", author = "tuqn")
    public void createIWorkplaceParams(MongoTemplate mongoTemplate, Environment environment) {
        ObjectId deploymentId = new ObjectId(environment.getProperty("digo.microservice.deployment.id"));
        IntegrationService service = new IntegrationService();
        service.setId(new ObjectId("5f7c16069abb62f511890041"));
        service.setName("IWORKPLACE service");
        service.setIntegratedUnit("IWORKPLACE service");
        List<Parameters> params = new ArrayList<>();

        Parameters url = new Parameters("url", ListType.STRING, "https://gateway-onegov.vnpt.vn");
        params.add(url);

        Parameters grantType = new Parameters("grant_type", ListType.STRING, "password");
        params.add(grantType);

        Parameters username = new Parameters("username", ListType.STRING, "user.igate");
        params.add(username);

        Parameters password = new Parameters("password", ListType.STRING, "Igate@1234");
        params.add(password);

        Parameters clientId = new Parameters("client_id", ListType.STRING, "k9hGBEZD1KfnHXoWkfX01kOahnwa");
        params.add(clientId);

        Parameters clientSecret = new Parameters("client_secret", ListType.STRING, "ASkiOT2TqNBfcuZJweUZ46juoSoa");
        params.add(clientSecret);

        Parameters scope = new Parameters("scope", ListType.STRING, "openid");
        params.add(scope);

        service.setParameters(params);
        service.setStatus(1);
        service.setIgnoreDeployment(true);
        mongoTemplate.save(service);

        IntegratedConfiguration config = new IntegratedConfiguration();
        IntegratedService integratedService = new IntegratedService(service);
        config.setService(integratedService);
        config.setParameters(params);
        config.setDeploymentId(deploymentId);
        config.setStatus(1);
        config.setDeleted(false);
//        config.setId(new ObjectId("5fc704bc86940eab2fb45998"));
        config.setName("IWorkplace service");
        config.setApplyAll(true);

        // Set subsystem
        Subsystem subSystem = new Subsystem();
        subSystem.setId(new ObjectId("5f7c16069abb62f511880003"));
        SubsystemTrans subSystemTrans = new SubsystemTrans();
        subSystemTrans.setLanguageId((short)228);
        subSystemTrans.setName("VNPT iGate 2.0");
        subSystem.setTrans(Arrays.asList(subSystemTrans));
        config.setSubsystem(Arrays.asList(subSystem));

        // Set tag
        Tag tag = new Tag();
        TransTag tagTrans = new TransTag();
        tag.setId(new ObjectId("62fdab49d13be03598afb8fc"));
        tagTrans.setLanguageId((short)228);
        tagTrans.setName("Nhóm chức năng liên thông");
        tag.setTrans(Arrays.asList(tagTrans));
        config.setTag(tag);
        mongoTemplate.save(config);
    }
}
