/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2022-05-05-16-57")
public class ViettelPKIChangeLogs {
    @ChangeSet(order = "2020-11-04-13-50", id = "ViettelPKIChangeLogs::createViettelKPIService", author = "hunghs")
    public void createViettelKPIService(MongoTemplate mongoTemplate) {
        IntegrationService viettelKPIService = new IntegrationService();
        viettelKPIService.setId(new ObjectId("62739fc4922aa6d0cda7d065"));
        viettelKPIService.setName("VIETTEL SIM PKI");
        viettelKPIService.setIntegratedUnit("VIETTEL PKI");
        List<Parameters> lstParams = new ArrayList<>();
        
        //Mode_sync
        Parameters modeSync = new Parameters("modeSync", ListType.STRING, "SYNC");
        lstParams.add(modeSync);
        
        //process code Sign
        Parameters codeSign = new Parameters("codeSign", ListType.STRING, "000001");
        lstParams.add(codeSign);
        
        //process code Querycer
        Parameters codeQueryCer = new Parameters("codeQueryCer", ListType.STRING, "000006");
        lstParams.add(codeQueryCer);
        
        //URL_WS
        Parameters urlWS = new Parameters("urlWS", ListType.STRING, "http://171.244.149.211:8080/apws.asmx?wsdl");
        lstParams.add(urlWS);
        
        //PRIVATE KEY
        Parameters privateKey = new Parameters("privateKey", ListType.STRING, "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKJ403qSG8gbbIOwzsCABQNVSucToSukuMZRximkLxyYA042g20cyAEgMmtbnaiu4A8uJWvHgYg8y8FTllGhKcvOvAv0fMJLxS1cndNPpXCJuAiQ3kcfw2xtBUbx9YqkZp7x9x0UD3xk5OzB8plJC+9MPzc61m3Yk9i0PEhY742NAgMBAAECgYAA9uCieJ2LmYYSXglMITZmZ6ozJu2s+aRTIQchFiVlcQ2L+EzdZ+9T+mHyOy/7YVZZILf+JMgDDAFVm25n+6Ho3luK/bwgUBAPl/r0QwzdpKeb47RMDw+qBwUpxHq83DfOJVQmE+T3VvGQPy4Ao0kNNVTdSZa1BtZ0bFRKLAm6IQJBANdwM42RCZ1mZNqju0nfSaTB6/7pO+AnvrLo9p/14WxVSS+V/vx1UNHMcNDrce3Pu6HDCEB6TVyfEyeoQejP7QUCQQDBD7qa1VCTzOlir5HEic8GW8I0YnGtCN1RWP+cB/4ki4ByjkJCWYXLGQRWNf+uXB+rwOBdvHZWILFKTJ7mpcTpAkEAytJcBoQsPB4BBwGsOhDX4R+cUSE8+HwSgZbnUJMIhZgJTZ52LzE3RDynXVi7qnqul8zoQOOjX3Z3bxdt7YOLDQJBAJSLPp/NS189RcFJpxWalvCkf0uBB0gEzwy9NobeW3vQ3jbGIFMewGPhTsXqbFzCwxeKotdcn4xrQR4S0BxVJakCQEcwnwlw7uTZGsVXkX+sS4zFEMHWDiYfSWLJ1leFsDbQktY3z9TE2klvi3plYnS8VMJlGfVm45bnJPjLtBGuVfw=");
        lstParams.add(privateKey);
        
        //AppId
        Parameters appId = new Parameters("appId", ListType.STRING, "T04");
        lstParams.add(appId);
        
        //mssp
        Parameters msspId = new Parameters("msspId", ListType.STRING, "Viettel");
        lstParams.add(msspId);
        
        //force pin 
        Parameters forcePIN = new Parameters("forcePin", ListType.INTEGER, 1);
        lstParams.add(forcePIN);
        
        //run imm 
        Parameters runImm = new Parameters("runImm", ListType.INTEGER, 0);
        lstParams.add(runImm);
        
        //sig_type 
        Parameters sigType = new Parameters("signType", ListType.INTEGER, 0);
        lstParams.add(sigType);

        //has_typoe
        Parameters hashType = new Parameters("hashType", ListType.STRING, "SHA1");
        lstParams.add(hashType);
        
        viettelKPIService.setParameters(lstParams);
        viettelKPIService.setStatus(1);
        try {
            viettelKPIService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-20T10:47:44.165+0100"));
            viettelKPIService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-20T10:47:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            viettelKPIService.setCreatedDate(new Date());
            viettelKPIService.setUpdatedDate(new Date());
        }
        viettelKPIService.setIgnoreDeployment(true);
        viettelKPIService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(viettelKPIService);
    }
    @ChangeSet(order = "2022-07-25-16-57", id = "ViettelPKIChangeLogs::createViettelKPIQNiService", author = "minhnguyenhuu")
    public void createViettelKPIQNiService(MongoTemplate mongoTemplate) {
        IntegrationService viettelKPIService = new IntegrationService();
        viettelKPIService.setId(new ObjectId("62739fc4922aa6d0cda7d067"));
        viettelKPIService.setName("VIETTEL SIM PKI QNI");
        viettelKPIService.setIntegratedUnit("VIETTEL PKI QNI");
        List<Parameters> lstParams = new ArrayList<>();
        
        //Mode_sync
        Parameters modeSync = new Parameters("modeSync", ListType.STRING, "SYNC");
        lstParams.add(modeSync);
        
        //process code Sign
        Parameters codeSign = new Parameters("codeSign", ListType.STRING, "000001");
        lstParams.add(codeSign);
        
        //process code Querycer
        Parameters codeQueryCer = new Parameters("codeQueryCer", ListType.STRING, "000006");
        lstParams.add(codeQueryCer);
        
        //URL_WS
        Parameters urlWS = new Parameters("urlWS", ListType.STRING, "http://210.211.98.203:8080/apws.asmx?wsdl");
        lstParams.add(urlWS);
        
        //PRIVATE KEY
        //Parameters privateKey = new Parameters("privateKey", ListType.STRING, "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKJ403qSG8gbbIOwzsCABQNVSucToSukuMZRximkLxyYA042g20cyAEgMmtbnaiu4A8uJWvHgYg8y8FTllGhKcvOvAv0fMJLxS1cndNPpXCJuAiQ3kcfw2xtBUbx9YqkZp7x9x0UD3xk5OzB8plJC+9MPzc61m3Yk9i0PEhY742NAgMBAAECgYAA9uCieJ2LmYYSXglMITZmZ6ozJu2s+aRTIQchFiVlcQ2L+EzdZ+9T+mHyOy/7YVZZILf+JMgDDAFVm25n+6Ho3luK/bwgUBAPl/r0QwzdpKeb47RMDw+qBwUpxHq83DfOJVQmE+T3VvGQPy4Ao0kNNVTdSZa1BtZ0bFRKLAm6IQJBANdwM42RCZ1mZNqju0nfSaTB6/7pO+AnvrLo9p/14WxVSS+V/vx1UNHMcNDrce3Pu6HDCEB6TVyfEyeoQejP7QUCQQDBD7qa1VCTzOlir5HEic8GW8I0YnGtCN1RWP+cB/4ki4ByjkJCWYXLGQRWNf+uXB+rwOBdvHZWILFKTJ7mpcTpAkEAytJcBoQsPB4BBwGsOhDX4R+cUSE8+HwSgZbnUJMIhZgJTZ52LzE3RDynXVi7qnqul8zoQOOjX3Z3bxdt7YOLDQJBAJSLPp/NS189RcFJpxWalvCkf0uBB0gEzwy9NobeW3vQ3jbGIFMewGPhTsXqbFzCwxeKotdcn4xrQR4S0BxVJakCQEcwnwlw7uTZGsVXkX+sS4zFEMHWDiYfSWLJ1leFsDbQktY3z9TE2klvi3plYnS8VMJlGfVm45bnJPjLtBGuVfw=");
        Parameters privateKey = new Parameters("privateKey", ListType.STRING, "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBALhrHRACD4HditIrALvjCifPfA8SXB5o1d7c/zDW3t4jzlCfvjWXz9EjEx6tr7Q35NUS630CjZlBhQhJyWQUMMZMQEUHYzMnJNsFtobgK31MBgd5hnz/Ip9mGW+86H/M7PLgbBABSKJxvxl5wNoA1YGTv+GwKY7GEq6BoVWY57cNAgMBAAECgYBPvqmjiXCRNG5Z8RRxKnzyuAqqLcM9txF8PE3xHYjjv9HwFRNX81x+z87tviczI9REutivhtIaCjXvXpDCQOTRrKviv0QAwHqlw/OCeVYQ3dQZwcuL0TjodoC5HTBc/Ue1KBTUpvL6t7lSDOyk2oP0ZjLF9NpwhttnRyeWapDYlQJBAPuCbX6wrmjJutFah0k0ZbMRdagVwnuLNcGopBcs/2T96LuZDI6OkRTsRX7p/1npk5lIp1THNWDEMn7WT+5CATMCQQC7tgiAzehse1rmEptD42yoYoUZlDvSSy6Rk7aglDs9Y4XCjuBT8/wJhoGS++lWy9o60+j+56A3MPY1/K7fF+a/AkEA3aAjCF58cc5BlzWkvPc1HsQl34W9KMyVBB+s6Nc3/jBYIudMz1oFsmA5JQrhfsN60rnY6bXY7Xo13NrQ+GylTQJBAKR1oU+QmCFrlkFbbfN17z48SxcVG7BstFlRjMF1/c0URB4o5fxIGLsTwLTRxyeHpSTDgzDKKUIRwLWeNGw72asCQQDwEkrGcefxW5JOqWbVCRStRTbL82MykrrH63voHt5LWSlwopIpqrBgqlsjvr03vEIe4T0zJ+5e5PlZA2fzCYRI");
        lstParams.add(privateKey);
        
        //AppId
        Parameters appId = new Parameters("appId", ListType.STRING, "T02");
        lstParams.add(appId);
        
        //mssp
        Parameters msspId = new Parameters("msspId", ListType.STRING, "Viettel");
        lstParams.add(msspId);
        
        //force pin 
        Parameters forcePIN = new Parameters("forcePin", ListType.INTEGER, 1);
        lstParams.add(forcePIN);
        
        //run imm 
        Parameters runImm = new Parameters("runImm", ListType.INTEGER, 0);
        lstParams.add(runImm);
        
        //sig_type 
        Parameters sigType = new Parameters("signType", ListType.INTEGER, 0);
        lstParams.add(sigType);

        //has_typoe
        Parameters hashType = new Parameters("hashType", ListType.STRING, "SHA1");
        lstParams.add(hashType);
        
        viettelKPIService.setParameters(lstParams);
        viettelKPIService.setStatus(1);
        try {
            viettelKPIService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-20T10:47:44.165+0100"));
            viettelKPIService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-20T10:47:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            viettelKPIService.setCreatedDate(new Date());
            viettelKPIService.setUpdatedDate(new Date());
        }
        viettelKPIService.setIgnoreDeployment(true);
        viettelKPIService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fb"));
        mongoTemplate.insert(viettelKPIService);
    }
}
