package vn.vnpt.digo.adapter.changelogs;


import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.util.ArrayList;
import java.util.List;

@ChangeLog(order = "2023-03-03-09-54")
public class DanhMucDungChungLGSPMinhTueChangeLogs {
    @ChangeSet(order = "2023-03-24-10-30", id = "DanhMucDungChungLGSPMinhTueChangeLogs::createIntegratedBoTTTTMinhTue", author = "thangnguyenquoc")
    public void createIntegratedBoTTTTMinhTue(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(new ObjectId("8fd6cee2346b5a09174671a2"));
        integrationService.setName("Danh Muc TTTT Minh Tue");
        integrationService.setIntegratedUnit("Danh Mục Minh Tue Service");

        List<Parameters> params = new ArrayList<>();
        // Url api lấy token
        params.add(new Parameters("gateway-token", ListType.STRING, "https://api.quangnam.gov.vn/token"));
        // Cac URL lay danh muc dung chung
        params.add(new Parameters("dmcd-cap-1", ListType.STRING, "https://api.quangnam.gov.vn/NGSP-DMDTDC/1.0/Category/qlvanbandieuhanhcap1"));
        params.add(new Parameters("dmcd-cap-2", ListType.STRING, "https://api.quangnam.gov.vn/NGSP-DMDTDC/1.0/Category/qlvanbandieuhanhcap2"));
        params.add(new Parameters("dmcd-cap-3", ListType.STRING, "https://api.quangnam.gov.vn/NGSP-DMDTDC/1.0/Category/qlvanbandieuhanhcap3"));
        // consumer-key
        params.add(new Parameters("consumer-key", ListType.STRING, "GJbFfb2XnDMCJoTq0OfCD1kO3Zga"));
        // consumer-secret
        params.add(new Parameters("consumer-secret", ListType.STRING, "d3S6ZYqUGyqASLKrqKA41WhuiCka"));

        integrationService.setParameters(params);
        integrationService.setStatus(1);

        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("1a744fc1874b2bb6960745a2"));
        mongoTemplate.insert(integrationService);
    }
}
