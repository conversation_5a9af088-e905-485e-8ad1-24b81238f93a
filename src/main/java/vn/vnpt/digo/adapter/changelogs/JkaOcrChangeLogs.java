package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2021-07-21-15-35")
public class JkaOcrChangeLogs {
    @ChangeSet(order = "2021-07-21-15-35", id = "JkaOcrChangeLogs::migrate", author = "vuonglvq")
    public void migrate(MongoTemplate mongoTemplate) {
        IntegrationService jkaService = new IntegrationService();
        jkaService.setId(new ObjectId("5f7c16069abb62f511890023"));
        jkaService.setName("JKA OCR Service");
        jkaService.setIntegratedUnit("JKA OCR Service");
        
        List<Parameters> lstParams = new ArrayList<>();
        
        lstParams.add(new Parameters("username", ListType.STRING, "vnpt"));
        lstParams.add(new Parameters("password", ListType.STRING, "vNPt@6789"));
        lstParams.add(new Parameters("jkaTokenUrl", ListType.STRING, "http://171.244.22.16:8080/api/v1/docpros/token"));
        lstParams.add(new Parameters("jkaScanFirstUrl", ListType.STRING, "http://171.244.22.16:8080/api/v1/docpros/extracts/docviet_first/"));
        lstParams.add(new Parameters("jkaScanAllUrl", ListType.STRING, "http://171.244.22.16:8080/api/v1/docpros/extracts/docviet_all/"));
        
        jkaService.setParameters(lstParams);
        jkaService.setStatus(1);
        try {
            jkaService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2021-07-21T10:33:44.165+0100"));
            jkaService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2021-07-21T10:33:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            jkaService.setCreatedDate(new Date());
            jkaService.setUpdatedDate(new Date());
        }
        jkaService.setIgnoreDeployment(true);
        jkaService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(jkaService);
    }
}
