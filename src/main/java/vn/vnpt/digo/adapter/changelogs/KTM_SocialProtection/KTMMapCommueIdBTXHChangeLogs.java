package vn.vnpt.digo.adapter.changelogs.KTM_SocialProtection;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.MappingType;
import vn.vnpt.digo.adapter.pojo.MappingTypeDataAccess;
import vn.vnpt.digo.adapter.pojo.MappingTypeTrans;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2023-02-24-01-01")
public class KTMMapCommueIdBTXHChangeLogs {
    @ChangeSet(order = "2023-02-24-01-01", id = "KTMMapCommueIdBTXHChangeLogs::createKTMMapCommueIdBTXHMapping", author = "phuongtnd")
    public void createKTMMapCommueIdBTXHMapping(MongoTemplate mongoTemplate) {
        MappingType mapping = new MappingType();
        mapping.setId(new ObjectId("5fcd8b5d92b5c3aa6f9e1104"));

        MappingTypeTrans vi = new MappingTypeTrans((short)228, "Xã/Phường BTXH", "Mapping danh mục Xã/Phường BTXH");
        MappingTypeTrans en = new MappingTypeTrans((short)46, "Commue Social Protection", "Mapping commue Social Protection");
        List<MappingTypeTrans> trans = new ArrayList<>();
        trans.add(vi);
        trans.add(en);
        mapping.setTrans(trans);

        MappingTypeDataAccess source = new MappingTypeDataAccess();
        source.setEnable(Boolean.TRUE);
        source.setEndpoint("");
        mapping.setSourceDataAccess(source);

        MappingTypeDataAccess dest = new MappingTypeDataAccess();
        source.setEnable(Boolean.FALSE);
        mapping.setDestDataAccess(dest);

        mapping.setStatus(1);

        try {
            mapping.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-22T10:47:44.165+0100"));
            mapping.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-22T10:47:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            mapping.setCreatedDate(new Date());
            mapping.setUpdatedDate(new Date());
        }

        mapping.setIgnoreDeployment(true);

        mapping.setDeploymentId(new ObjectId("60b8799a975e7143acdeb2fe"));

        mongoTemplate.insert(mapping);
    }
}
