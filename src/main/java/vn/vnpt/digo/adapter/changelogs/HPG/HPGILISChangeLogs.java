package vn.vnpt.digo.adapter.changelogs.HPG;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2023-02-21-02-20")
public class HPGILISChangeLogs {

  @ChangeSet(order = "2023-02-21-15-21", id = "HPGILISChangeLogs::createHPGILISIntegration", author = "anhlp")
  public void createHPGILISIntegration(MongoTemplate mongoTemplate) {
    IntegrationService hpgILISService = new IntegrationService();
    hpgILISService.setId(new ObjectId("63419edf92d8f30e4bc19730"));
    hpgILISService.setName("HPG ILIS Service");
    hpgILISService.setIntegratedUnit("HPG ILIS Service");
    List<Parameters> lstParams = new ArrayList<>();

    Parameters tokenUrl = new Parameters("token-url", ListType.STRING, "https://amnew.haiphong.gov.vn/token?grant_type=client_credentials");
    lstParams.add(tokenUrl);

    Parameters consumerKey = new Parameters("consumer-key", ListType.STRING, "wOobxrTc0vukdWqes3seGqJvfL4a");
    lstParams.add(consumerKey);

    Parameters consumerSecret = new Parameters("consumer-secret", ListType.STRING, "hlrQvWyOe5FIBCz3fs0iNBlzmu0a");
    lstParams.add(consumerSecret);

    Parameters getLaydslinhvuc = new Parameters("get-lay-ds-linh-vuc", ListType.STRING, "https://amnew.haiphong.gov.vn/ilis/1.0/api/lgsp_cong_public/Public/laydslinhvuc");
    lstParams.add(getLaydslinhvuc);

    Parameters getLayNhomDuLieuTheoLinhVuc = new Parameters("get-lay-nhom-du-lieu-theo-linh-vuc", ListType.STRING, "https://amnew.haiphong.gov.vn/ilis/1.0/api/lgsp_cong_public/Public/laynhomdulieutheolinhvuc?idlinhvuc=%s");
    lstParams.add(getLayNhomDuLieuTheoLinhVuc);

    Parameters getLayDanhSachDichVu = new Parameters("get-lay-danh-sach-dich-vu", ListType.STRING, "https://amnew.haiphong.gov.vn/ilis/1.0/api/lgsp_cong_public/Public/laydanhsachdichvu?nhomdulieu_id=%s");
    lstParams.add(getLayDanhSachDichVu);

    hpgILISService.setParameters(lstParams);
    hpgILISService.setStatus(1);
    try {
      hpgILISService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-10-06T10:33:44.165+0100"));
      hpgILISService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-10-06T10:33:44.165+0100"));
    } catch (ParseException | NullPointerException e) {
      hpgILISService.setCreatedDate(new Date());
      hpgILISService.setUpdatedDate(new Date());
    }
    hpgILISService.setIgnoreDeployment(true);
    hpgILISService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
    mongoTemplate.insert(hpgILISService);
  }
}
