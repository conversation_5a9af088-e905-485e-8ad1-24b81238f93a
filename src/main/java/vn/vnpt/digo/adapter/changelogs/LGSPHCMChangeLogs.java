    package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.query.Criteria;
import com.mongodb.client.result.UpdateResult;

@ChangeLog(order = "2022-05-18-14-19")
public class LGSPHCMChangeLogs {
    @ChangeSet(order = "2022-05-18-14-19", id = "LGSPHCMChangeLogs::createLGSPHCMIntegration", author = "huytq")
    public void createLGSPHCMIntegration(MongoTemplate mongoTemplate) {
        IntegrationService lgspHCMService = new IntegrationService();
        lgspHCMService.setId(new ObjectId("5f7c16069abb62f511890035"));
        lgspHCMService.setName("LGSP HCM Service");
        lgspHCMService.setIntegratedUnit("LGSP HCM Service");

        List<Parameters> lstParams = new ArrayList<>();


        //Adapter kết nối
        Parameters adapterURL = new Parameters("adapter", ListType.STRING, "https://hcmlgsp.tphcm.gov.vn/");
        lstParams.add(adapterURL);

        //Access key
        Parameters accessKey = new Parameters("accessKey", ListType.STRING, "rTkhYCBwHM");
        lstParams.add(accessKey);

        //Secret key
        Parameters secretKey = new Parameters("secretKey", ListType.STRING, "DWkQgY1YSS");
        lstParams.add(secretKey);

        //�?ơn vị kết nối (AppName)
        Parameters appName = new Parameters("appName", ListType.STRING, "TPHCM");
        lstParams.add(appName);

        //Mã đơn vị cung cấp ứng dụng (PartnerCode)
        Parameters partnerCode = new Parameters("partnerCode", ListType.STRING, "000.00.01.H29");
        lstParams.add(partnerCode);

        //Mã đơn vị sử dụng dịch vụ (PartnerCodeCus)
        Parameters partnerCodeCus = new Parameters("partnerCodeCus", ListType.STRING, "000.00.01.H29");
        lstParams.add(partnerCodeCus);

        //Authorization
        String authorizationCode = "ewoiQWNjZXNzS2V5IjoiclRraFlDQndITSIsCiJTZWNyZXRLZXkiOiJEV2tRZ1kxWVNTIiwKIkFwcE5hbWUiOiAiVFBIQ00iLAoiUGFydG5lckNvZGUiOiAiMDAwLjAwLjAxLkgyOSIsCiJQYXJ0bmVyQ29kZUN1cyI6ICIwMDAuMDAuMDEuSDI5Igp9Cg==";
        Parameters authorization = new Parameters("authorization", ListType.STRING, authorizationCode);
        lstParams.add(authorization);

        //API tra cứu thông tin hồ sơ theo mã đơn vị
        Parameters traCuuHoSoGetByMaDonVi = new Parameters("traCuuHoSoGetByMaDonVi", ListType.STRING, "TraCuuHoSo_GetByMaDonVi");
        lstParams.add(traCuuHoSoGetByMaDonVi);

        lgspHCMService.setParameters(lstParams);
        lgspHCMService.setStatus(1);

        lgspHCMService.setIgnoreDeployment(true);
        lgspHCMService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(lgspHCMService);
    }
    
    @ChangeSet(order = "2022-06-07-10-18", id = "LGSPHCMChangeLogs::updateLGSPHCMIntegration", author = "quannna")
    public void updateHCMLGSPIntegration(MongoTemplate mongoTemplate) {
        IntegrationService LGSPHCM = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511890035"),IntegrationService.class);
        List<Parameters> params = LGSPHCM.getParameters();

//        Parameters documentEnable = new Parameters("documentEnable", ListType.STRING, 0);
//        params.add(documentEnable);

        //api 1
        Parameters serviceDongBoHoSoChuyenNganh = new Parameters("serviceDongBoHoSoChuyenNganh", ListType.STRING, "dongbohosochuyennganh");
        params.add(serviceDongBoHoSoChuyenNganh);
        
        Parameters sso_uri = new Parameters("sso-uri", ListType.STRING, "https://ssotest.vnptigate.vn/auth/realms/digo/protocol/openid-connect/token");
        params.add(sso_uri);
        
        Parameters client_id = new Parameters("client_id", ListType.STRING, "svc-padman");
        params.add(client_id);
        
        Parameters client_secret = new Parameters("client_secret", ListType.STRING, "efe42c1f-ac2f-47e6-a065-b37ea012d66d");
        params.add(client_secret);

        LGSPHCM.setParameters(params);



        mongoTemplate.save(LGSPHCM);
    }
    
    @ChangeSet(order = "2022-06-08-10-17", id = "LGSPHCMChangeLogs::updateLGSPHCMParametersConfig", author = "quannna")
    public void updateJudicialRecordsParametersConfig(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));
        Parameters serviceDongBoHoSoChuyenNganh = new Parameters("serviceDongBoHoSoChuyenNganh", ListType.STRING, "dongbohosochuyennganh");
        Parameters sso_uri = new Parameters("sso-uri", ListType.STRING, "https://ssotest.vnptigate.vn/auth/realms/digo/protocol/openid-connect/token");
        Parameters client_id = new Parameters("client_id", ListType.STRING, "svc-padman");
        Parameters client_secret = new Parameters("client_secret", ListType.STRING, "efe42c1f-ac2f-47e6-a065-b37ea012d66d");
   
        
        Update update = new Update();
        update.addToSet("parameters").each(serviceDongBoHoSoChuyenNganh);
        UpdateResult updatedServiceDongBoHoSoChuyenNganh = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceDongBoHoSoChuyenNganh" + updatedServiceDongBoHoSoChuyenNganh);
        
        update.addToSet("parameters").each(sso_uri);
        UpdateResult updatedSsoUri = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedSsoUri" + updatedSsoUri);
        
        update.addToSet("parameters").each(client_id);
        UpdateResult updatedClientId = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedClientId" + updatedClientId);
        
        update.addToSet("parameters").each(client_secret);
        UpdateResult updatedClientSecret = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedClientSecret" + updatedClientSecret);
        
    }
    
    @ChangeSet(order = "2022-06-28-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration2", author = "viethq")
    public void updateHCMLGSPIntegration2(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));
        
        Parameters serviceDongBoHoSoChuyenNganh = new Parameters("serviceDongBoHoSoChuyenNganh", ListType.STRING, "dongbohosochuyennganh");
        Parameters urlXacNhanGoiTinLGSP = new Parameters("urlXacNhanGoiTinLGSP", ListType.STRING, "dvctp/XacNhanGoiTinTienDoXuLyHoSoChuyenNganh");
        Parameters vnPostSyncConverInfo_Url = new Parameters("vnPostSyncConverInfo_Url", ListType.STRING, "DongBoThongTinChuyenPhat");
        
        Update update = new Update();        
        update.addToSet("parameters").each(urlXacNhanGoiTinLGSP);
        UpdateResult updatedUrlXacNhanGoiTinLGSP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlXacNhanGoiTinLGSP " + updatedUrlXacNhanGoiTinLGSP);
        
        update.addToSet("parameters").each(vnPostSyncConverInfo_Url);
        UpdateResult updatedVNPostSyncConverInfo_Url = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedVNPostSyncConverInfo_Url" + updatedVNPostSyncConverInfo_Url);
    }
    
    @ChangeSet(order = "2022-07-01-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration3", author = "phucnh.it2")
    public void updateHCMLGSPIntegration3(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));
              
       
        Parameters vnPostGetInfo_Url = new Parameters("vnPostGetInfo_Url", ListType.STRING, "GetThongTinChuyenPhat");
        
        Update update = new Update();  
        update.addToSet("parameters").each(vnPostGetInfo_Url);
        UpdateResult updatedVNPostSyncConverInfo_Url = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("LGSPHCMChangeLogs: updatedVNPostSyncConverInfo_Url" + updatedVNPostSyncConverInfo_Url);
    }
    
    @ChangeSet(order = "2022-07-01-10-19", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration4", author = "nguyenkhactri")
    public void updateHCMLGSPIntegration4(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));
            
        Parameters urlGetDanhMucDonVi = new Parameters("urlGetDanhMucDonVi", ListType.STRING, "GetDanhMucDonVi");    
        
        Update update = new Update();        
        update.addToSet("parameters").each(urlGetDanhMucDonVi);
        UpdateResult updatedUrlGetDanhMucDonVi = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlXacNhanGoiTinLGSP " + updatedUrlGetDanhMucDonVi);
    }
    
    @ChangeSet(order = "2022-06-30-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration5", author = "viethq")
    public void updateHCMLGSPIntegration5(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters urlGetTokenLLTP = new Parameters("urlGetTokenLLTP", ListType.STRING, "lltp/token");
        Parameters urlNhanTrangThaiHoSoLLTP = new Parameters("urlNhanTrangThaiHoSoLLTP", ListType.STRING, "NhanTrangThaiHoSoLLTP");
        Parameters urlNhanTrangThaiHoSoThanhCongLLTP = new Parameters("urlNhanTrangThaiHoSoThanhCongLLTP", ListType.STRING, "NhanTrangThaiHoSoThanhCongLLTP");
        Parameters urlNhanHoSoDangKyThanhCongLLTP = new Parameters("urlNhanHoSoDangKyThanhCongLLTP", ListType.STRING, "NhanHoSoDangKyThanhCongLLTP");
        
        Update update = new Update();
        update.addToSet("parameters").each(urlGetTokenLLTP);
        UpdateResult updatedUrlGetTokenLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlGetTokenLLTP" + updatedUrlGetTokenLLTP);
        
        update.addToSet("parameters").each(urlNhanTrangThaiHoSoLLTP);
        UpdateResult updatedUrlNhanTrangThaiHoSoLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlNhanTrangThaiHoSoLLTP" + updatedUrlNhanTrangThaiHoSoLLTP);
        
        update.addToSet("parameters").each(urlNhanTrangThaiHoSoThanhCongLLTP);
        UpdateResult updatedUrlNhanTrangThaiHoSoThanhCongLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlNhanTrangThaiHoSoThanhCongLLTP" + updatedUrlNhanTrangThaiHoSoThanhCongLLTP);
        
        update.addToSet("parameters").each(urlNhanHoSoDangKyThanhCongLLTP);
        UpdateResult updatedUrlNhanHoSoDangKyThanhCongLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlNhanHoSoDangKyThanhCongLLTP" + updatedUrlNhanHoSoDangKyThanhCongLLTP);
    }
    
    @ChangeSet(order = "2022-06-30-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration6", author = "viethq")
    public void updateHCMLGSPIntegration6(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters urlGetTokenLLTP = new Parameters("urlGetTokenLLTP", ListType.STRING, "lltp/token");
        Parameters urlNhanHoSoDangKyLLTP = new Parameters("urlNhanHoSoDangKyLLTP", ListType.STRING, "lltp/apiLLTP/st/1.0/LLTP-API-nhanHoSoDangKy");
        Parameters urlTraTrangThaiHoSoLLTP = new Parameters("urlTraTrangThaiHoSoLLTP", ListType.STRING, "lltp/apiLLTP/st/1.0/LLTP-API-traTrangThaiHs");
        Parameters urlTraHoSoLLTP = new Parameters("urlTraHoSoLLTP", ListType.STRING, "lltp/apiLLTP/st/1.0/LLTP-API-traHoSo");
        Parameters urlTraThongTinDanhMucLLTP = new Parameters("urlTraThongTinDanhMucLLTP", ListType.STRING, "lltp/apiLLTP/st/1.0/LLTP-API-traDanhMuc");
        Parameters urlDanhDauHoSoNhanThanhCongLLTP = new Parameters("urlDanhDauHoSoNhanThanhCongLLTP", ListType.STRING, "lltp/apiLLTP/st/1.0/LLTP-API-danhDauHsThanhCong");
        Parameters urlTraDanhSachTrangThaiHoSoLLTP = new Parameters("urlTraDanhSachTrangThaiHoSoLLTP", ListType.STRING, "lltp/apiLLTP/st/1.0/LLTP-API-traDsTrangThaiHs");
      
        Update update = new Update();
        update.addToSet("parameters").each(urlGetTokenLLTP);
        UpdateResult updatedUrlGetTokenLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlGetTokenLLTP" + updatedUrlGetTokenLLTP);
        
        update.addToSet("parameters").each(urlNhanHoSoDangKyLLTP);
        UpdateResult updatedUrlNhanHoSoDangKyLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlNhanHoSoDangKyLLTP" + updatedUrlNhanHoSoDangKyLLTP);
        
        update.addToSet("parameters").each(urlTraTrangThaiHoSoLLTP);
        UpdateResult updatedUrlTraTrangThaiHoSoLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlTraTrangThaiHoSoLLTP" + updatedUrlTraTrangThaiHoSoLLTP);
        
        update.addToSet("parameters").each(urlTraHoSoLLTP);
        UpdateResult updatedUrlTraHoSoLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlTraHoSoLLTP" + updatedUrlTraHoSoLLTP);
        
        update.addToSet("parameters").each(urlTraThongTinDanhMucLLTP);
        UpdateResult updatedUrlTraThongTinDanhMucLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlTraThongTinDanhMucLLTP" + updatedUrlTraThongTinDanhMucLLTP);
        
        update.addToSet("parameters").each(urlDanhDauHoSoNhanThanhCongLLTP);
        UpdateResult updatedUrlDanhDauHoSoNhanThanhCongLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlDanhDauHoSoNhanThanhCongLLTP" + updatedUrlDanhDauHoSoNhanThanhCongLLTP);
        
        update.addToSet("parameters").each(urlTraDanhSachTrangThaiHoSoLLTP);
        UpdateResult updatedUrlTraDanhSachTrangThaiHoSoLLTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlTraDanhSachTrangThaiHoSoLLTP" + updatedUrlTraDanhSachTrangThaiHoSoLLTP);
    }
    
    @ChangeSet(order = "2022-07-13-10-19", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration7", author = "quannna")
    public void updateHCMLGSPIntegration7(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));
            
        Parameters downloadfileUrl = new Parameters("downloadfile-url", ListType.STRING, "https://apitest.vnptigate.vn/ad/nps-dossier/{configId}/downloadfile?fileid=");    
        
        Update update = new Update();        
        update.addToSet("parameters").each(downloadfileUrl);
        UpdateResult updatedUrlGetDanhMucDonVi = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("downloadfile-url " + updatedUrlGetDanhMucDonVi);
    }

    @ChangeSet(order = "2022-07-19-10-19", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration8", author = "viethq")
    public void updateHCMLGSPIntegration8(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters urlGetTokenVNPOST = new Parameters("urlGetTokenVNPOST", ListType.STRING, "vnpost/token");
        Parameters urlSTPriceHCCVNPOST = new Parameters("urlPriceHCCVNPOST", ListType.STRING, "apiVNPostNGSP/st/p1.0/info/PriceHCC");
        Parameters urlGetInfomationPostVNPOST = new Parameters("urlGetInfomationPostVNPOST", ListType.STRING, "apiVNPostNGSP/p1.0/info/GetInfomationPost");
        Parameters urlGetPostageVasVNPOST = new Parameters("urlGetPostageVasVNPOST", ListType.STRING, "apiVNPostNGSP/p1.0/info/GetPostageVas");
        Parameters urlOrderTrackingVNPOST = new Parameters("urlOrderTrackingVNPOST", ListType.STRING, "apiVNPostNGSP/p1.0/order/tracking");
        Parameters urlOrderCancelVNPOST = new Parameters("urlOrderCancelVNPOST", ListType.STRING, "apiVNPostNGSP/p1.0/order/cancel");
        Parameters urlSTOrderPostVNPOST = new Parameters("urlOrderPostVNPOST", ListType.STRING, "apiVNPostNGSP/st/p1.0/order/post");
        Parameters urlSTGetPriceVNPOST = new Parameters("urlGetPriceVNPOST", ListType.STRING, "apiVNPostNGSP/st/p1.0/info/getPrice");
        Parameters urlSTOrderTrackingVNPOST = new Parameters("urlOrderTrackingVNPOST", ListType.STRING, "apiVNPostNGSP/st/p1.0/order/tracking");

        Update update = new Update();
        update.addToSet("parameters").each(urlGetTokenVNPOST);
        UpdateResult updatedUrlGetTokenVNPOST = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlGetTokenVNPOST " + updatedUrlGetTokenVNPOST);
        update.addToSet("parameters").each(urlSTPriceHCCVNPOST);
        UpdateResult updatedUrlSTPriceHCCVNPOST = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlSTPriceHCCVNPOST " + updatedUrlSTPriceHCCVNPOST);
        update.addToSet("parameters").each(urlGetInfomationPostVNPOST);
        UpdateResult updatedUrlGetInfomationPostVNPOST = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlGetInfomationPostVNPOST " + updatedUrlGetInfomationPostVNPOST);
        update.addToSet("parameters").each(urlGetPostageVasVNPOST);
        UpdateResult updatedUrlGetPostageVasVNPOST = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlGetPostageVasVNPOST " + updatedUrlGetPostageVasVNPOST);
        update.addToSet("parameters").each(urlOrderTrackingVNPOST);
        UpdateResult updatedUrlOrderTrackingVNPOST = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlOrderTrackingVNPOST " + updatedUrlOrderTrackingVNPOST);
        update.addToSet("parameters").each(urlOrderCancelVNPOST);
        UpdateResult updatedUrlOrderCancelVNPOST = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlOrderCancelVNPOST " + updatedUrlOrderCancelVNPOST);
        update.addToSet("parameters").each(urlSTOrderPostVNPOST);
        UpdateResult updatedUrlSTOrderPostVNPOST = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlSTOrderPostVNPOST " + updatedUrlSTOrderPostVNPOST);
        update.addToSet("parameters").each(urlSTGetPriceVNPOST);
        UpdateResult updatedUrlSTGetPriceVNPOST = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlSTGetPriceVNPOST " + updatedUrlSTGetPriceVNPOST);
        update.addToSet("parameters").each(urlSTOrderTrackingVNPOST);
        UpdateResult updatedUrlSTOrderTrackingVNPOST = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlSTOrderTrackingVNPOST " + updatedUrlSTOrderTrackingVNPOST);
    }
    
    @ChangeSet(order = "2022-07-28-10-19", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration9", author = "thuongld")
    public void updateHCMLGSPIntegration9(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters urlPaymentHCMPaygate = new Parameters("urlPaymentHCMPaygate", ListType.STRING, "paygate");
        Parameters urlPaymentHCMRefund = new Parameters("urlPaymentHCMRefund", ListType.STRING, "refund");
        Parameters urlPaymentHCMGetOrderInfo = new Parameters("urlPaymentHCMGetOrderInfo", ListType.STRING, "GetOrderInfo");
        Parameters urlPaymentHCMGenderQRCodeImage = new Parameters("urlPaymentHCMGenderQRCodeImage", ListType.STRING, "paygate/genderQRCodeImage");
        Parameters paymentHCMServiceCode = new Parameters("paymentHCMServiceCode", ListType.STRING, "hcm_dvcstttt");
        Parameters paymentHCMAccountCode = new Parameters("paymentHCMAccountCode", ListType.STRING, "dvcstttt");
        Parameters paymentKeyChecksum = new Parameters("paymentKeyChecksum", ListType.STRING, "");
        
        Update update = new Update();
        update.addToSet("parameters").each(urlPaymentHCMPaygate);
        UpdateResult updatedUrlPaymentHCMPaygate = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlPaymentHCMPaygate " + updatedUrlPaymentHCMPaygate);
        update.addToSet("parameters").each(urlPaymentHCMRefund);
        UpdateResult updatedUrlPaymentHCMRefund = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlPaymentHCMRefund " + updatedUrlPaymentHCMRefund);
        update.addToSet("parameters").each(urlPaymentHCMGetOrderInfo);
        UpdateResult updatedUrlPaymentHCMGetOrderInfo = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlPaymentHCMGetOrderInfo " + updatedUrlPaymentHCMGetOrderInfo);
        update.addToSet("parameters").each(urlPaymentHCMGenderQRCodeImage);
        UpdateResult updatedUrlPaymentHCMGenderQRCodeImage = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlPaymentHCMGenderQRCodeImage " + updatedUrlPaymentHCMGenderQRCodeImage);
        update.addToSet("parameters").each(paymentHCMServiceCode);
        UpdateResult updatedPaymentHCMServiceCode = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedPaymentHCMServiceCode " + updatedPaymentHCMServiceCode);
        update.addToSet("parameters").each(paymentHCMAccountCode);
        UpdateResult updatedPaymentHCMAccountCode = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedPaymentHCMAccountCode " + updatedPaymentHCMAccountCode);
        update.addToSet("parameters").each(paymentKeyChecksum);
        UpdateResult updatedPaymentKeyChecksum = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedPaymentKeyChecksum " + updatedPaymentKeyChecksum);
    }
    
    @ChangeSet(order = "2022-08-03-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration10", author = "quannna")
    public void updateHCMLGSPIntegration10(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters madichvuCSDLDCLGSP = new Parameters("madichvuCSDLDCLGSP", ListType.STRING, "038");
        Parameters macanboCSDLDCLGSP = new Parameters("macanboCSDLDCLGSP", ListType.STRING, "NGSP_BTTTT");
        Parameters svURLCSDLDC = new Parameters("svURLCSDLDC", ListType.STRING, "/integration/createRequest/CommonService/XTCongDan");
        Parameters usernameCSDLDC = new Parameters("usernameCSDLDC", ListType.STRING, "NGSP_BTTTT");
        Parameters passwordCSDLDC = new Parameters("passwordCSDLDC", ListType.STRING, "DU876DWY#t");
        Parameters serviceXTCongDan = new Parameters("serviceXTCongDan", ListType.STRING, "XTCongDan");
        Parameters serviceGetTokenCSLDDC = new Parameters("serviceGetTokenCSLDDC", ListType.STRING, "apiCSDLDanCu/Token");
        
        Update update = new Update();
        update.addToSet("parameters").each(madichvuCSDLDCLGSP);
        UpdateResult updatedMaDichVuCSDLDCLGSP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedMaDichVuCSDLDCLGSP" + updatedMaDichVuCSDLDCLGSP);
        
        update.addToSet("parameters").each(macanboCSDLDCLGSP);
        UpdateResult updatedMaCnBoCSDLDCLGSP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedMaCnBoCSDLDCLGSP" + updatedMaCnBoCSDLDCLGSP);
        
        update.addToSet("parameters").each(svURLCSDLDC);
        UpdateResult updatedSvURLCSDLDC = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedSvURLCSDLDC" + updatedSvURLCSDLDC);
        
        update.addToSet("parameters").each(usernameCSDLDC);
        UpdateResult updatedUsernameCSDLDC = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUsernameCSDLDC" + updatedUsernameCSDLDC);
        
        update.addToSet("parameters").each(passwordCSDLDC);
        UpdateResult updatedPasswordCSDLDC = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedPasswordCSDLDC" + updatedPasswordCSDLDC);
        
        update.addToSet("parameters").each(serviceXTCongDan);
        UpdateResult updatedServiceXTCongDan = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceXTCongDan" + updatedServiceXTCongDan);
        
        update.addToSet("parameters").each(serviceGetTokenCSLDDC);
        UpdateResult updatedServiceGetTokenCSLDDC = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceGetTokenCSLDDC" + updatedServiceGetTokenCSLDDC);
    }
    
    @ChangeSet(order = "2022-08-04-10-19", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration11", author = "quannna")
    public void updateHCMLGSPIntegration11(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters serviceProceduresUrl = new Parameters("serviceProceduresUrl", ListType.STRING, "DVCQG/LayDanhSachTTHC");
        Parameters serviceGetTokenDVCUrl = new Parameters("serviceGetTokenDVCUrl", ListType.STRING, "dvcqg/token");
        Parameters serviceBodyProcedures = new Parameters("serviceBodyProcedures", ListType.STRING, "LayDanhSachTTHC");
        Parameters usernameGetTokenDVC = new Parameters("usernameGetTokenDVC", ListType.STRING, "000.00.00.H29");
        Parameters passwordGetTokenDVC = new Parameters("passwordGetTokenDVC", ListType.STRING, "000.00.00.H29@123456");
        Parameters serviceProceduresDetailUrl = new Parameters("serviceProceduresDetailUrl", ListType.STRING, "DVCQG/LayThuTuc");
        Parameters serviceBodyProceduresDetail = new Parameters("serviceBodyProceduresDetail", ListType.STRING, "LayThuTuc");

        Update update = new Update();
        update.addToSet("parameters").each(serviceProceduresUrl);
        UpdateResult updatedServiceProceduresUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceProceduresUrl " + updatedServiceProceduresUrl);
        update.addToSet("parameters").each(serviceGetTokenDVCUrl);
        UpdateResult updatedServiceGetTokenDVCUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceGetTokenDVCUrl " + updatedServiceGetTokenDVCUrl);
        update.addToSet("parameters").each(serviceBodyProcedures);
        UpdateResult updatedServiceBodyProcedures = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceBodyProcedures " + updatedServiceBodyProcedures);
        update.addToSet("parameters").each(usernameGetTokenDVC);
        UpdateResult updatedUsernameGetTokenDVC = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUsernameGetTokenDVC " + updatedUsernameGetTokenDVC);
        update.addToSet("parameters").each(passwordGetTokenDVC);
        UpdateResult updatedPasswordGetTokenDVC = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedPasswordGetTokenDVC " + updatedPasswordGetTokenDVC);
        update.addToSet("parameters").each(serviceProceduresDetailUrl);
        UpdateResult updatedServiceProceduresDetailUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceProceduresDetailUrl " + updatedServiceProceduresDetailUrl);
        update.addToSet("parameters").each(serviceBodyProceduresDetail);
        UpdateResult updatedServiceBodyProceduresDetail = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceBodyProceduresDetail " + updatedServiceBodyProceduresDetail);
        
    }
    
    @ChangeSet(order = "2022-08-09-10-19", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration12_phucnh", author = "phucnh")
    public void updateHCMLGSPIntegration12_phucnh(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));
        
        Parameters urlSTOrderTrackingVNPOST = new Parameters("urlSTOrderTrackingVNPOST", ListType.STRING, "apiVNPostNGSP/st/p1.0/order/tracking");

        Update update = new Update();    
        update.addToSet("parameters").each(urlSTOrderTrackingVNPOST);
        UpdateResult updatedUrlSTOrderTrackingVNPOST = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedurlSTOrderTrackingVNPOST " + updatedUrlSTOrderTrackingVNPOST);        
    }
    
    @ChangeSet(order = "2022-08-04-10-19", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration12Tri", author = "tri")
    public void updateHCMLGSPIntegration12Tri(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters adapterUrl = new Parameters("adapterUrl", ListType.STRING, "http://14.225.12.119:80/VXPAdapter/RestService/forward/mapi/call");
        Parameters serviceBodyForms = new Parameters("serviceBodyForms", ListType.STRING, "LayDanhMucThanhPhanHoSo");
        Parameters serviceBodyTraCuuHoSo = new Parameters("serviceBodyTraCuuHoSo", ListType.STRING, "TraCuuHoSo");
        Parameters GetDanhMucLinhVucURL = new Parameters("GetDanhMucLinhVucUrl", ListType.STRING, "GetDanhMucLinhVuc");
        Parameters urlGetTokenDkdn = new Parameters("urlGetTokenDkdn", ListType.STRING, "csdldkdn/token");
        Parameters chiTietDoanhNghiepUrl = new Parameters("chiTietDoanhNghiepUrl", ListType.STRING, "csdldkdn/chiTietDoanhNghiep");
        Parameters danhSachHoSoTrongNgayUrl = new Parameters("danhSachHoSoTrongNgayUrl", ListType.STRING, "csdldkdn/danhSachHoSoTrongNgay");
        Parameters danhSachHoSoUrl = new Parameters("danhSachHoSoUrl", ListType.STRING, "csdldkdn/danhSachHoSo");
        Parameters tinhTrangHoSoUrl = new Parameters("tinhTrangHoSoUrl", ListType.STRING, "csdldkdn/tinhTrangHoSo");
        Parameters DanhMucGiayToUpdateSize = new Parameters("DanhMucTphsUpdateSize", ListType.INTEGER, 10000);
        Parameters DanhMucGiayToLastPage = new Parameters("DanhMucTphsLastPage", ListType.INTEGER, 9);
        Update update = new Update();
        update.addToSet("parameters").each(adapterUrl);
        UpdateResult updatedAdapterUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedAdapterUrl " + updatedAdapterUrl);
        
        update.addToSet("parameters").each(serviceBodyForms);
        UpdateResult updatedServiceBodyForms = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceBodyForms " + updatedServiceBodyForms);
        
        update.addToSet("parameters").each(serviceBodyTraCuuHoSo);
        UpdateResult updatedServiceBodyTraCuuHoSo = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceBodyTraCuuHoSo " + updatedServiceBodyTraCuuHoSo);
        
        update.addToSet("parameters").each(GetDanhMucLinhVucURL);
        UpdateResult updatedGetDanhMucLinhVucURL = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedGetDanhMucLinhVucURL " + updatedGetDanhMucLinhVucURL);
        
        update.addToSet("parameters").each(urlGetTokenDkdn);
        UpdateResult updateUrlGetTokenDkdn = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateUrlGetTokenDkdn " + updateUrlGetTokenDkdn);
        
        update.addToSet("parameters").each(chiTietDoanhNghiepUrl);
        UpdateResult updateChiTietDoanhNghiepUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateChiTietDoanhNghiepUrl " + updateChiTietDoanhNghiepUrl);
        
        update.addToSet("parameters").each(danhSachHoSoTrongNgayUrl);
        UpdateResult updateDanhSachHoSoTrongNgayUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateDanhSachHoSoTrongNgayUrl " + updateDanhSachHoSoTrongNgayUrl);
        
        update.addToSet("parameters").each(danhSachHoSoUrl);
        UpdateResult updateDanhSachHoSoUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateDanhSachHoSoUrl " + updateDanhSachHoSoUrl);
        
        update.addToSet("parameters").each(tinhTrangHoSoUrl);
        UpdateResult updateTinhTrangHoSoUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateTinhTrangHoSoUrl " + updateTinhTrangHoSoUrl);
        
        update.addToSet("parameters").each(DanhMucGiayToUpdateSize);
        UpdateResult updateDanhMucGiayToUpdateSize = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateDanhMucGiayToUpdateSize " + updateDanhMucGiayToUpdateSize);
        
        update.addToSet("parameters").each(DanhMucGiayToLastPage);
        UpdateResult updateDanhMucGiayToLastPage = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateDanhMucGiayToLastPage " + updateDanhMucGiayToLastPage);
    }
    
    @ChangeSet(order = "2022-08-05-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSP_PhiLePhi_paquoc74", author = "paquoc74")
    public void updateHCMLGSP_PhiLePhi_paquoc74(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters urlDanhMucPhiLePhiDVCQG = new Parameters("urlDanhMucPhiLePhiDVCQG", ListType.STRING, "DVCQG/LayDanhMucLePhi");
        Parameters getTokenDVCUrl = new Parameters("getTokenDVCUrl", ListType.STRING, "dvcqg/token");
        Parameters userTokenDVC_LePhi = new Parameters("userTokenDVC_LePhi", ListType.STRING, "000.00.00.H29");
        Parameters passTokenDVC_LePhi = new Parameters("passTokenDVC_LePhi", ListType.STRING, "000.00.00.H29@123456");
        Parameters serviceBody_LePhi = new Parameters("serviceBody_LePhi", ListType.STRING, "LayDanhMucLePhi");
        
        Update update = new Update();
        update.addToSet("parameters").each(urlDanhMucPhiLePhiDVCQG);
        UpdateResult updatedUrlDanhMucPhiLePhiDVCQG = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlDanhMucPhiLePhiDVCQG" + updatedUrlDanhMucPhiLePhiDVCQG);
        
        update.addToSet("parameters").each(getTokenDVCUrl);
        UpdateResult updatedGetTokenDVCUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedgetTokenDVCUrl" + updatedGetTokenDVCUrl);
        
        update.addToSet("parameters").each(userTokenDVC_LePhi);
        UpdateResult updatedUserTokenDVC_LePhi = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUsernameCSDLDC" + updatedUserTokenDVC_LePhi);
        
        update.addToSet("parameters").each(passTokenDVC_LePhi);
        UpdateResult updatedPassTokenDVC_LePhi = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedPasswordCSDLDC" + updatedPassTokenDVC_LePhi);

        update.addToSet("parameters").each(serviceBody_LePhi);
        UpdateResult updatedServiceBody_LePhi = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceGetTokenCSLDDC" + updatedServiceBody_LePhi);
    }
    
    @ChangeSet(order = "2022-08-18-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration20220818", author = "tri")
    public void updateHCMLGSPIntegration20220818(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));
        
        Parameters serviceTraCuuHoUrl = new Parameters("serviceTraCuuHoUrl", ListType.STRING, "/DVCQG/CapNhatTienDoHoSoMC");
        Parameters serviceFormsUrl = new Parameters("serviceFormsUrl", ListType.STRING, "/DVCQG/LayDanhMucThanhPhanHoSo");
        
        Update update = new Update();
        update.addToSet("parameters").each(serviceTraCuuHoUrl);
        UpdateResult updateServiceTraCuuHoUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateServiceTraCuuHoUrl " + updateServiceTraCuuHoUrl);
        
        update.addToSet("parameters").each(serviceFormsUrl);
        UpdateResult updateServiceFormsUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateServiceFormsUrl " + updateServiceFormsUrl);
        

    }
    
    @ChangeSet(order = "2022-08-18-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration13", author = "quannna")
    public void updateHCMLGSPIntegration13(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters serviceQuestionUrl = new Parameters("serviceQuestionUrl", ListType.STRING, "DVCQG/LayDanhSachHoiDapBoCoQuan");
        Parameters serviceBodyQuestion = new Parameters("serviceBodyQuestion", ListType.STRING, "LayDanhSachHoiDapBoCoQuan");
        Parameters serviceAnswerUrl = new Parameters("serviceAnswerUrl", ListType.STRING, "DVCQG/LayNoiDungTraLoiTheoCauHoi");
        Parameters serviceBodyAnswer = new Parameters("serviceBodyAnswer", ListType.STRING, "LayNoiDungTraLoiTheoCauHoi");
        
        Update update = new Update();
        update.addToSet("parameters").each(serviceQuestionUrl);
        UpdateResult updatedServiceQuestionUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceQuestionUrl" + updatedServiceQuestionUrl);
        
        update.addToSet("parameters").each(serviceBodyQuestion);
        UpdateResult updatedServiceBodyQuestion = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceBodyQuestion" + updatedServiceBodyQuestion);
        
        update.addToSet("parameters").each(serviceAnswerUrl);
        UpdateResult updatedServiceAnswerUrl = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceAnswerUrl" + updatedServiceAnswerUrl);
        
        update.addToSet("parameters").each(serviceBodyAnswer);
        UpdateResult updatedServiceBodyAnswer = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceBodyAnswer" + updatedServiceBodyAnswer);

    }
    
    @ChangeSet(order = "2022-08-22-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration14", author = "hoangpnn")
    public void updateHCMLGSPIntegration14(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters urlGetTokenHTTP = new Parameters("urlGetTokenHTTP", ListType.STRING, "http/token");
        Parameters urlTraThongTinDanhMucHTTP = new Parameters("urlTraThongTinDanhMucHTTP", ListType.STRING, "http/apiHTTP/st/2.0/danhMuc");
        Parameters urlDangKyHoTich = new Parameters("urlDangKyHoTich", ListType.STRING, "http/apiHTTP/st/2.0/dangKyHoTich");
        Parameters urlTraTrangThaiHoSoHTTP = new Parameters("urlTraTrangThaiHoSoHTTP", ListType.STRING, "http/apiHTTP/st/2.0/traTrangThaiHoSo");
        Parameters urlKetQuaDangKyHSHTTP = new Parameters("urlKetQuaDangKyHSHTTP", ListType.STRING, "http/apiHTTP/st/2.0/ketQuaDangKyHS");
        Parameters urlDSHoSoDangKyHTTP = new Parameters("urlDSHoSoDangKyHTTP", ListType.STRING, "http/apiHTTP/st/2.0/dsHoSoDangKy");
        Parameters urlTraHoSoHTTP = new Parameters("urlTraHoSoHTTP", ListType.STRING, "http/apiHTTP/st/2.0/traHoSo");
        Parameters urlTraDanhSachHoSoHTTP = new Parameters("urlTraDanhSachHoSoHTTP", ListType.STRING, "http/apiHTTP/st/2.0/traDanhSachHoSo");
      
        Update update = new Update();
        update.addToSet("parameters").each(urlGetTokenHTTP);
        UpdateResult updatedUrlGetTokenHTTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlGetTokenHTTP" + updatedUrlGetTokenHTTP);
        
        update.addToSet("parameters").each(urlTraThongTinDanhMucHTTP);
        UpdateResult updatedUrlTraThongTinDanhMucHTTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlTraThongTinDanhMucHTTP" + updatedUrlTraThongTinDanhMucHTTP);
        
        update.addToSet("parameters").each(urlDangKyHoTich);
        UpdateResult updatedDangKyHoTich = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedDangKyHoTich" + updatedDangKyHoTich);
        
        update.addToSet("parameters").each(urlTraTrangThaiHoSoHTTP);
        UpdateResult updatedUrlTraTrangThaiHoSoHTTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlTraTrangThaiHoSoHTTP" + updatedUrlTraTrangThaiHoSoHTTP);
        
        update.addToSet("parameters").each(urlKetQuaDangKyHSHTTP);
        UpdateResult updatedUrlKetQuaDangKyHSHTTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlKetQuaDangKyHSHTTP" + updatedUrlKetQuaDangKyHSHTTP);
        
        update.addToSet("parameters").each(urlDSHoSoDangKyHTTP);
        UpdateResult updatedUrlDSHoSoDangKyHTTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlDSHoSoDangKyHTTP" + updatedUrlDSHoSoDangKyHTTP);
        
        update.addToSet("parameters").each(urlTraHoSoHTTP);
        UpdateResult updatedUrlTraHoSoHTTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlTraHoSoHTTP" + updatedUrlTraHoSoHTTP);
        
        update.addToSet("parameters").each(urlTraDanhSachHoSoHTTP);
        UpdateResult updatedUrlTraDanhSachHoSoHTTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlTraDanhSachHoSoHTTP" + updatedUrlTraDanhSachHoSoHTTP);
    }
    
    @ChangeSet(order = "2022-08-22-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration15", author = "duypd.hcm")
    public void updateHCMLGSPIntegration15(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters getToken = new Parameters("getToken", ListType.STRING, "nganSach/token");
        Parameters getDanhSachHoSoTheoNgay = new Parameters("getDanhSachHoSoTheoNgay", ListType.STRING, "nganSach/GetDanhSachHoSoTheoNgay?tuNgay=%s&denNgay=%s");
        Parameters getChiTietHoSo = new Parameters("getChiTietHoSo", ListType.STRING, "nganSach/ChiTietHoSo?id=%s");
        Parameters getHoSoTheoKyTn = new Parameters("getHoSoTheoKyTn", ListType.STRING, "nganSach/GetHoSoTheoKyTn?tuNgay=%s&denNgay=%s");
        Parameters authorLGSP = new Parameters("authorLGSP", ListType.STRING, "ewoiQWNjZXNzS2V5IjoiNTc4OGFhYmNlNGIwODM2ZGVmMzY3YTI3IiwKIlNlY3JldEtleSI6IkE4OGpzRjFLMUZ2Z1djZjl2V2IzMGVDQno4NFVVaXdSVlNLZEppeUc3diIsCiJBcHBOYW1lIjogInZucHRoY20iLAoiUGFydG5lckNvZGUiOiAiMDAwLjAwLjE1LkgyOSIsCiJQYXJ0bmVyQ29kZUN1cyI6ICIwMDAuMDAuMTUuSDI5Igp9");
      
        Update update = new Update();
        update.addToSet("parameters").each(getToken);
        UpdateResult updatedgetToken = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("getToken" + updatedgetToken);
        
        update.addToSet("parameters").each(getDanhSachHoSoTheoNgay);
        UpdateResult updatedgetDanhSachHoSoTheoNgay = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("getDanhSachHoSoTheoNgay" + updatedgetDanhSachHoSoTheoNgay);
        
        update.addToSet("parameters").each(getChiTietHoSo);
        UpdateResult updatedgetChiTietHoSo = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("getChiTietHoSo" + updatedgetChiTietHoSo);
        
        update.addToSet("parameters").each(getHoSoTheoKyTn);
        UpdateResult updatedgetHoSoTheoKyTn = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("getHoSoTheoKyTn" + updatedgetHoSoTheoKyTn);
        
        update.addToSet("parameters").each(authorLGSP);
        UpdateResult updatedAuthorLGSP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("authorLGSP" + updatedAuthorLGSP);
    }
    
    @ChangeSet(order = "2022-09-13-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration16", author = "quannna")
    public void updateHCMLGSPIntegration16(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters serviceGetTechnicalId = new Parameters("serviceGetTechnicalId", ListType.STRING, "/DVCQG/TraCuuTechID");
        Parameters techidProvider = new Parameters("techid-provider", ListType.STRING, "wso2is");
        Parameters serviceSyncDossier = new Parameters("serviceSyncDossier", ListType.STRING, "/DVCQG/DongBoHoSoMC");
        Parameters serviceSyncStatus = new Parameters("serviceSyncStatus", ListType.STRING, "/DVCQG/CapNhatTienDoHoSoMC");
        
        Update update = new Update();
        update.addToSet("parameters").each(serviceGetTechnicalId);
        UpdateResult updatedServiceGetTechnicalId = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceGetTechnicalId" + updatedServiceGetTechnicalId);
        
        update.addToSet("parameters").each(techidProvider);
        UpdateResult updatedTechidProvider = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedTechidProvider" + updatedTechidProvider);
        
        update.addToSet("parameters").each(serviceSyncDossier);
        UpdateResult updatedServiceSyncDossier = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceSyncDossier" + updatedServiceSyncDossier);
        
        update.addToSet("parameters").each(serviceSyncStatus);
        UpdateResult updatedServiceSyncStatus = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceSyncStatus" + updatedServiceSyncStatus);

    }
    
    @ChangeSet(order = "2022-09-16-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration17", author = "hoangpnn")
    public void updateHCMLGSPIntegration17(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters urlDongBoThongKeHoSo = new Parameters("urlDongBoThongKeHoSo", ListType.STRING, "DVCQG/DongBoThongKeHoSo");
        Parameters urlDongBoSoLieuTongHopTinhHinhGQTTHCCapTinh = new Parameters("urlDongBoSoLieuTongHopTinhHinhGQTTHCCapTinh", ListType.STRING, "DVCQG/DongBoSoLieuTongHopTinhHinhGQTTHCCapTinh");
        Parameters urlDongBoSoLieuTongHopTinhHinhGQTTHCCapBo = new Parameters("urlDongBoSoLieuTongHopTinhHinhGQTTHCCapBo", ListType.STRING, "DVCQG/DongBoSoLieuTongHopTinhHinhGQTTHCCapBo");
      
        Update update = new Update();
        update.addToSet("parameters").each(urlDongBoThongKeHoSo);
        UpdateResult updatedUrlDongBoThongKeHoSo = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("getToken" + updatedUrlDongBoThongKeHoSo);
        
        update.addToSet("parameters").each(urlDongBoSoLieuTongHopTinhHinhGQTTHCCapTinh);
        UpdateResult updatedUrlDongBoSoLieuTongHopTinhHinhGQTTHCCapTinh = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("getDanhSachHoSoTheoNgay" + updatedUrlDongBoSoLieuTongHopTinhHinhGQTTHCCapTinh);
        
        update.addToSet("parameters").each(urlDongBoSoLieuTongHopTinhHinhGQTTHCCapBo);
        UpdateResult updatedUrlDongBoSoLieuTongHopTinhHinhGQTTHCCapBo = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("getChiTietHoSo" + updatedUrlDongBoSoLieuTongHopTinhHinhGQTTHCCapBo);
    }
    
    @ChangeSet(order = "2022-09-29-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration18", author = "duypd.hcm")
    public void updateHCMLGSPIntegration18(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));
        
        Parameters authorLGSP = new Parameters("authorLGSP", ListType.STRING, "ewoiQWNjZXNzS2V5IjoiNTc4OGFhYmNlNGIwODM2ZGVmMzY3YTI3IiwKIlNlY3JldEtleSI6IkE4OGpzRjFLMUZ2Z1djZjl2V2IzMGVDQno4NFVVaXdSVlNLZEppeUc3diIsCiJBcHBOYW1lIjogInZucHRoY20iLAoiUGFydG5lckNvZGUiOiAiMDAwLjAwLjE1LkgyOSIsCiJQYXJ0bmVyQ29kZUN1cyI6ICIwMDAuMDAuMTUuSDI5Igp9");
      
        Update update = new Update();
        
        update.addToSet("parameters").each(authorLGSP);
        UpdateResult updatedAuthorLGSP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("authorLGSP" + updatedAuthorLGSP);
    }
    
    @ChangeSet(order = "2023-05-29-10-17", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration19", author = "quannna")
    public void updateHCMLGSPIntegration19(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));
        Parameters serviceGetTokenGTVT = new Parameters("serviceGetTokenGTVT", ListType.STRING, "bgtvt/token");
        Parameters serviceGetDossierListGTVT = new Parameters("serviceGetDossierListGTVT", ListType.STRING, "apiGTVT/1.2/d/api/btttt/ngsp/qlvtdata/danhsachhoso");
        Parameters lgspAccessTokenGTVT = new Parameters("lgspAccessTokenGTVT", ListType.STRING, "ewoiQWNjZXNzS2V5IjoiNTc4OGE1ZTNlNGIwODM2ZGVmMzY2ZjE0IiwKIlNlY3JldEtleSI6IkEzdGxCZVJYVHhIZndodFhjUFBmWDRZWGNPcG5SS296dGNvS0E0REtvbjg3IiwKIkFwcE5hbWUiOiJkdmNzZ3R2dCIsCiJQYXJ0bmVyQ29kZSI6IjAwMC4wMC4wNS5IMjkiLAoiUGFydG5lckNvZGVDdXMiOiIwMDAuMDAuMDUuSDI5Igp9");
        Parameters serviceGetDossierDetailGTVT = new Parameters("serviceGetDossierDetailGTVT", ListType.STRING, "apiGTVT/1.2/d/api/btttt/ngsp/qlvtdata/tracuuhoso");
        Parameters serviceGetDossierStatisticGTVT = new Parameters("serviceGetDossierStatisticGTVT", ListType.STRING, "apiGTVT/1.2/d/api/btttt/ngsp/qlvtdata/thongkehoso");
      
        
        Update update = new Update();
        update.addToSet("parameters").each(serviceGetTokenGTVT);
        UpdateResult updatedserviceGetTokenGTVT = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceGetTokenGTVT" + updatedserviceGetTokenGTVT);
        
        update.addToSet("parameters").each(serviceGetDossierListGTVT);
        UpdateResult updatedServiceGetDossierListGTVT = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceGetDossierListGTVT" + updatedServiceGetDossierListGTVT);
        
        update.addToSet("parameters").each(lgspAccessTokenGTVT);
        UpdateResult updatedLgspAccessTokenGTVT = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedLgspAccessTokenGTVT" + updatedLgspAccessTokenGTVT);
        
        update.addToSet("parameters").each(serviceGetDossierDetailGTVT);
        UpdateResult updatedServiceGetDossierDetailGTVT = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceGetDossierDetailGTVT" + updatedServiceGetDossierDetailGTVT);
        

        update.addToSet("parameters").each(serviceGetDossierStatisticGTVT);
        UpdateResult updatedServiceGetDossierStatisticGTVT = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedServiceGetDossierStatistic" + updatedServiceGetDossierStatisticGTVT);
    }
    
    @ChangeSet(order = "2023-06-02-10-17", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration20", author = "quannna")
    public void updateHCMLGSPIntegration20(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));
        Parameters checkautosyndatagtvt = new Parameters("checkautosyndatagtvt", ListType.STRING, "false");
        
      
        
        Update update = new Update();
        update.addToSet("parameters").each(checkautosyndatagtvt);
        UpdateResult updatecheckautosyndatagtvt = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatecheckautosyndatagtvt" + updatecheckautosyndatagtvt);
        
    }
    
    @ChangeSet(order = "2024-02-29-14-15", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration22", author = "hoangpnn")
    public void updateHCMLGSPIntegration22(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters urlGetTokenHTTPLTKH = new Parameters("urlGetTokenHTTPLTKH", ListType.STRING, "ngsp/apiLienThongHoTich/token");
        Parameters authKeyLTKHHTTP = new Parameters("authKeyLTKHHTTP", ListType.STRING, "hochiminh@test#79");
        Parameters urlDangKyHoTichLTKH = new Parameters("urlDangKyHoTichLTKH", ListType.STRING, "ngsp/apiLienThongHoTich/st/1.0/dangKyHoTich");
        Parameters adapterLTKH = new Parameters("adapterLTKH", ListType.STRING, "https://hcmlgsp.tphcm.gov.vn/");
//        Parameters accessKeyLTKH = new Parameters("accessKeyLTKH", ListType.STRING, "rTkhYCBwHM");
//        Parameters secretKeyLTKH = new Parameters("secretKeyLTKH", ListType.STRING, "DWkQgY1YSS");
//        Parameters appNameLTKH = new Parameters("appNameLTKH", ListType.STRING, "TPHCM");
//        Parameters partnerCodeLTKH = new Parameters("partnerCodeLTKH", ListType.STRING, "000.00.01.H29");
//        Parameters partnerCodeCusLTKH = new Parameters("partnerCodeCusLTKH", ListType.STRING, "000.00.01.H29");
      
        Update update = new Update();
        update.addToSet("parameters").each(urlGetTokenHTTPLTKH);
        UpdateResult updatedUrlGetTokenHTTPLTKH = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlGetTokenHTTPLTKH" + updatedUrlGetTokenHTTPLTKH);
        
        update.addToSet("parameters").each(authKeyLTKHHTTP);
        UpdateResult updatedAuthKeyLTKHHTTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedAuthKeyLTKHHTTP" + updatedAuthKeyLTKHHTTP);
        
        update.addToSet("parameters").each(urlDangKyHoTichLTKH);
        UpdateResult updatedUrlDangKyHoTichLTKH = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlDangKyHoTichLTKH" + updatedUrlDangKyHoTichLTKH);
        
        update.addToSet("parameters").each(adapterLTKH);
        UpdateResult updatedadapterLTKH = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedadapterLTKH" + updatedadapterLTKH);
        
//        update.addToSet("parameters").each(accessKeyLTKH);
//        UpdateResult updatedAccessKeyLTKH = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
//        System.out.println("updatedAccessKeyLTKH" + updatedAccessKeyLTKH);
//        
//        update.addToSet("parameters").each(secretKeyLTKH);
//        UpdateResult updatedSecretKeyLTKH = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
//        System.out.println("updatedSecretKeyLTKH" + updatedSecretKeyLTKH);
//        
//        update.addToSet("parameters").each(appNameLTKH);
//        UpdateResult updatedAppNameLTKH = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
//        System.out.println("updatedAppNameLTKH" + updatedAppNameLTKH);
//        
//        update.addToSet("parameters").each(partnerCodeLTKH);
//        UpdateResult updatedPartnerCodeLTKH = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
//        System.out.println("updatedPartnerCodeLTKH" + updatedPartnerCodeLTKH);
//        
//        update.addToSet("parameters").each(partnerCodeCusLTKH);
//        UpdateResult updatedPartnerCodeCusLTKH = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
//        System.out.println("updatedPartnerCodeCusLTKH" + updatedPartnerCodeCusLTKH);
    }

    @ChangeSet(order = "2024-04-07-10-18", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration23", author = "nguyenttai.hcm")
    public void updateHCMLGSPIntegration23(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters adapterGPLX = new Parameters("adapterGPLX", ListType.STRING, "https://hcmlgsp.tphcm.gov.vn/gplx/");
        Parameters lgspaccesstokenGPLX = new Parameters("lgspaccesstokenGPLX", ListType.STRING, "eyJBY2Nlc3NLZXkiOiJlYTg3OGVmZi1lYWRkLTQxZWYtYjc0NS0xMGRkOThmNzRhZjUiLCJTZWNyZXRLZXkiOiI5OTMyZDE2Yy1mMjUyLTQyZTMtYTM0NC0yZTA4ZGEzZDliZmQiLCJBcHBOYW1lIjoiZGVmYXVsdF9hY2MiLCJQYXJ0bmVyQ29kZSI6IjAwMC4wMC4xNS5IMjkifQ==");
        Parameters usernameGPLX = new Parameters("usernameGPLX", ListType.STRING, "000.00.05.H29");
        Parameters passwordGPLX = new Parameters("passwordGPLX", ListType.STRING, "000.00.05.H29!20240124#Q@zWsx@12379!!");

        Update update = new Update();

        update.addToSet("parameters").each(adapterGPLX);
        UpdateResult updateAdapterGPLX = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateAdapterGPLX " + updateAdapterGPLX);

        update.addToSet("parameters").each(lgspaccesstokenGPLX);
        UpdateResult updateLgspaccesstokenGPLX = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateLgspaccesstokenGPLX " + updateLgspaccesstokenGPLX);

        update.addToSet("parameters").each(usernameGPLX);
        UpdateResult updateUsernameGPLX = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateUsernameGPLX " + updateUsernameGPLX);

        update.addToSet("parameters").each(passwordGPLX);
        UpdateResult updatePasswordGPLX = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatePasswordGPLX " + updatePasswordGPLX);
    }
    
    @ChangeSet(order = "2024-04-16-14-15", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration24", author = "hoangpnn")
    public void updateHCMLGSPIntegration24(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters urlGetKetQuaDangKyHSHTTPLTKH = new Parameters("ulrLTKHKetQuaDangKyHS", ListType.STRING, "ngsp/apiLienThongHoTich/st/1.0/ketQuaDangKyHS");
        Parameters urlGetTraTrangThaiHoSoLTKHHTTP = new Parameters("urlTraTrangThaiHoSoLTKHHTTP", ListType.STRING, "ngsp/apiLienThongHoTich/st/1.0/traTrangThaiHoSo");
      
        Update update = new Update();
        update.addToSet("parameters").each(urlGetKetQuaDangKyHSHTTPLTKH);
        UpdateResult updatedUrlGetKetQuaDangKyHSHTTPLTKH = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedUrlGetTokenHTTPLTKH" + updatedUrlGetKetQuaDangKyHSHTTPLTKH);
        
        update.addToSet("parameters").each(urlGetTraTrangThaiHoSoLTKHHTTP);
        UpdateResult updatedUrlGetTraTrangThaiHoSoLTKHHTTP = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updatedAuthKeyLTKHHTTP" + updatedUrlGetTraTrangThaiHoSoLTKHHTTP);
    }


    @ChangeSet(order = "2024-06-09-22-08", id = "LGSPHCMChangeLogs::updateHCMLGSPIntegration25", author = "khanhpn.hcm")
    public void updateHCMLGSPIntegration25(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters lgspaccesstokenBusinessRegistration = new Parameters("lgspaccesstokenBusinessRegistration", ListType.STRING, "ewoiQWNjZXNzS2V5IjoiUE5UUVRIR09KRElPTlBDSkxFUUQiLAoiU2VjcmV0S2V5IjoiQ1dRT0RaQVpiTlJLVUhOQ0JQUkdTTUlNTllLWkROIiwKIkFwcE5hbWUiOiJodHR0Z3F0dGhjdHBoY20iLAoiUGFydG5lckNvZGUiOiIwMDAuMDAuMTUuSDI5IiwKIlBhcnRuZXJDb2RlQ3VzIjoiMDAwLjAwLjE1LkgyOSIKfQ==");
        Parameters urlGetTokenBusinessRegistration = new Parameters("urlGetTokenBusinessRegistration", ListType.STRING, "dkdn/token");
        Parameters urlKHDTBusinessRegistrationDetails = new Parameters("urlKHDTBusinessRegistrationDetails", ListType.STRING, "dkdn/chiTietDoanhNghiep/st/1.0");
        Parameters urlKHDTDetailsOfBusinessHouseholdRegistration = new Parameters("urlKHDTDetailsOfBusinessHouseholdRegistration", ListType.STRING, "dkdn/api/get/ttdk/st/1.0/HoKinhDoanh/chiTietHoKinhDoanh");
        Parameters urlKHDTDetailsOfCooperativeBusinessRegistration = new Parameters("urlKHDTDetailsOfCooperativeBusinessRegistration", ListType.STRING,"dkdn/api/get/ttdk/st/1.0/HopTacXa/chiTietHopTacXa");

        Update update = new Update();

        update.addToSet("parameters").each(lgspaccesstokenBusinessRegistration);
        UpdateResult updateLgspaccesstokenBusinessRegistration= mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("lgspaccesstokenBusinessRegistration " + updateLgspaccesstokenBusinessRegistration);


        update.addToSet("parameters").each(urlGetTokenBusinessRegistration);
        UpdateResult updateUrlGetTokenBusinessRegistration= mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("urlGetTokenBusinessRegistration " + updateUrlGetTokenBusinessRegistration);

        update.addToSet("parameters").each(urlKHDTBusinessRegistrationDetails);
        UpdateResult updateUrlKHDTBusinessRegistrationDetails= mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("urlKHDTBusinessRegistrationDetails " + updateUrlKHDTBusinessRegistrationDetails);

        update.addToSet("parameters").each(urlKHDTDetailsOfBusinessHouseholdRegistration);
        UpdateResult updateUrlKHDTDetailsOfBusinessHouseholdRegistration= mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("urlKHDTDetailsOfBusinessHouseholdRegistration " + updateUrlKHDTDetailsOfBusinessHouseholdRegistration);

        update.addToSet("parameters").each(urlKHDTDetailsOfCooperativeBusinessRegistration);
        UpdateResult updateUrlKHDTDetailsOfCooperativeBusinessRegistration= mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("urlKHDTDetailsOfCooperativeBusinessRegistration " + updateUrlKHDTDetailsOfCooperativeBusinessRegistration);
    }


    @ChangeSet(order = "2024-10-30-17-00", id = "LGSPHCMChangeLogs::createVPUBHCMChangeLog", author = "vominhhien")
    public void createVPUBHCMChangeLog(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890035")));

        Parameters vpubQLVBURl = new Parameters("vpubGateway", ListType.STRING, "https://apitest.vpub.vn/");

        Update update = new Update();
        update.addToSet("parameters").each(vpubQLVBURl);
        UpdateResult updatedUrlGetDanhMucDonVi = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("downloadfile-url " + updatedUrlGetDanhMucDonVi);
    }

}
