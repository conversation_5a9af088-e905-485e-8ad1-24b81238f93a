package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2023-02-21-02-20")
public class SocialProtectionChangeLogs {

    @ChangeSet(order = "2023-02-21-15-21", id = "SocialProtectionChangeLogs::createSocialProtectionIntegration", author = "phuongtnd")
    public void createSocialProtectionIntegration(MongoTemplate mongoTemplate) {
        IntegrationService socialProtectionService = new IntegrationService();
        socialProtectionService.setId(new ObjectId("9f7c11069afc62f511897022"));
        socialProtectionService.setName("SocialProtection Service");
        socialProtectionService.setIntegratedUnit("SocialProtection Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters tokenUrl = new Parameters("token-url", ListType.STRING, "https://api.quangnam.gov.vn/token");
        lstParams.add(tokenUrl);

        Parameters consumerKey = new Parameters("consumer-key", ListType.STRING, "GJbFfb2XnDMCJoTq0OfCD1kO3Zga");
        lstParams.add(consumerKey);

        Parameters consumerSecret = new Parameters("consumer-secret", ListType.STRING, "d3S6ZYqUGyqASLKrqKA41WhuiCka");
        lstParams.add(consumerSecret);

        Parameters acceptKey = new Parameters("accept-key", ListType.STRING, "57575072-1f1e-4c62-a902-3e5bbe6e854a");
        lstParams.add(acceptKey);

        Parameters provinceCode = new Parameters("province-code", ListType.STRING, "49");
        lstParams.add(provinceCode);

        Parameters tokenValue = new Parameters("token-value", ListType.STRING, "");
        lstParams.add(tokenValue);

        Parameters getApplyDossierUrl = new Parameters("get-apply-dossier-url", ListType.STRING, "https://api.quangnam.gov.vn/NGSP-BTXT/1.0/DanhSachHoSo");
        lstParams.add(getApplyDossierUrl);

        Parameters pushApplyDossierUrl = new Parameters("push-apply-dossier-url", ListType.STRING, "https://api.quangnam.gov.vn/NGSP-BTXT/1.0/DongBoDVC");
        lstParams.add(pushApplyDossierUrl);

        Parameters pushDossierProcessUrl = new Parameters("push-dossier-status-url", ListType.STRING, "https://api.quangnam.gov.vn/NGSP-BTXT/1.0/XuLyHoSo");
        lstParams.add(pushDossierProcessUrl);

        Parameters pushDossierResultUrl = new Parameters("push-dossier-result-url", ListType.STRING, "https://api.quangnam.gov.vn/NGSP-BTXT/1.0/DongBoDVC");
        lstParams.add(pushDossierResultUrl);

        Parameters receivingVNPostId = new Parameters("receiving-vnpost-id", ListType.STRING, "6163f1e20f363b04cf60b224");
        lstParams.add(receivingVNPostId);

        Parameters receivingDirectlyId = new Parameters("receiving-directly-id", ListType.STRING, "5f8968888fffa53e4c073ded");
        lstParams.add(receivingDirectlyId);

        socialProtectionService.setParameters(lstParams);
        socialProtectionService.setStatus(1);
        try {
            socialProtectionService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-22T10:47:44.165+0100"));
            socialProtectionService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-22T10:47:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            socialProtectionService.setCreatedDate(new Date());
            socialProtectionService.setUpdatedDate(new Date());
        }
        socialProtectionService.setIgnoreDeployment(true);
        socialProtectionService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(socialProtectionService);
    }

    @ChangeSet(order = "2024-03-18-10-06", id = "SocialProtectionChangeLogs::updateConfigParams", author = "phuongnt")
    public void updateConfigParams(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = mongoTemplate.findById(new ObjectId("9f7c11069afc62f511897022"),IntegrationService.class);
        List<Parameters> params = integrationService.getParameters();

        Parameters numDay = new Parameters("SoNgayLayHoSo", ListType.STRING, "1");
        params.add(numDay);

        integrationService.setParameters(params);
        mongoTemplate.save(integrationService);

        List<IntegratedConfiguration> configs = mongoTemplate.find(new Query().addCriteria(Criteria.where("service.id").is(new ObjectId("9f7c11069afc62f511897022"))),IntegratedConfiguration.class);
        for (IntegratedConfiguration config:configs) {
            params = config.getParameters();
            params.add(numDay);
            config.setParameters(params);
            mongoTemplate.save(config);
        }
    }
}
