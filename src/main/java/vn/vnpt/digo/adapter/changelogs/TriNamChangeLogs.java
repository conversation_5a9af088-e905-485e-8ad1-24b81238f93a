/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@ChangeLog(order = "2023-08-09-10-17")
public class TriNamChangeLogs {

    @ChangeSet(order = "2023-08-22-09-00", id = "TriNamChangeLogs::addEventLogCollectionName", author = "haipv")
    public void addEventLogCollectionName(MongoTemplate mongoTemplate) {
        IntegrationService service = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511819259"), IntegrationService.class);
        if(service == null) {
            return;
        }
        service.setCollectionName("eventLogHbhLgsp");
        mongoTemplate.save(service);
    }

    @ChangeSet(order = "2023-08-09-10-17", id = "TriNamChangeLogs::createRecordIntegration", author = "phuongnt")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService criminalRecordService = new IntegrationService();
        criminalRecordService.setId(new ObjectId("5f7c16069abb62f511819259"));
        criminalRecordService.setName("LGSP Trí Nam");
        criminalRecordService.setIntegratedUnit("LGSP Trí Nam Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters consumerKey = new Parameters("client_id", ListType.STRING, "igate-vnpt");
        lstParams.add(consumerKey);

        Parameters consumerSecret = new Parameters("client_secret", ListType.STRING, "083c426a-a3f7-f4ea-a70a-290c401b558f");
        lstParams.add(consumerSecret);

        Parameters gatewayToken = new Parameters("gateway-token", ListType.STRING, "https://lgspaccount.hoabinh.gov.vn/connect/token");
        lstParams.add(gatewayToken);

        Parameters gateway = new Parameters("gateway", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn/ngsp");
        lstParams.add(gateway);
        
        
        criminalRecordService.setParameters(lstParams);
        criminalRecordService.setStatus(1);
        try {
            criminalRecordService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-08-15T10:33:44.165+0100"));
            criminalRecordService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-08-15T10:33:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            criminalRecordService.setCreatedDate(new Date());
            criminalRecordService.setUpdatedDate(new Date());
        }
        criminalRecordService.setIgnoreDeployment(true);
        criminalRecordService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(criminalRecordService);
    }

    @ChangeSet(order = "2023-11-22-11-02", id = "TriNamChangeLogs::updateParamsService", author = "phuongntit1")
    public void updateParamsService(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511819259"),IntegrationService.class);
        List<Parameters> params = integrationService.getParameters();

        Parameters enableJob = new Parameters("hash-key", ListType.STRING, "1");
        params.add(enableJob);
        integrationService.setParameters(params);
        mongoTemplate.save(integrationService);
    }

    @ChangeSet(order = "2025-13-02-09-45", id = "TriNamChangeLogs::updateParamsServiceNew", author = "donglm.hbh")
    public void updateParamsServiceNew(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511819259"),IntegrationService.class);
        List<Parameters> params = integrationService.getParameters();

        Parameters enableJob = new Parameters("gateway-root", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn");
        params.add(enableJob);
        integrationService.setParameters(params);
        mongoTemplate.save(integrationService);
    }
}
