package vn.vnpt.digo.adapter.changelogs.KTM_MonreIntegration;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.MappingType;
import vn.vnpt.digo.adapter.pojo.MappingTypeDataAccess;
import vn.vnpt.digo.adapter.pojo.MappingTypeTrans;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2023-04-03-01-01")
public class KTMMonreMappingDistrictIdChangeLog {
    @ChangeSet(order = "2023-04-03-01-01", id = "KTMMonreMappingDistrictIdChangeLog::createKTMMonreMapDistrictId", author = "phuongtnd")
    public void createKTMMonreMapDistrictId(MongoTemplate mongoTemplate) {
        MappingType mapping = new MappingType();
        mapping.setId(new ObjectId("88ae807a62681a8b4f9b1c3f"));

        MappingTypeTrans vi = new MappingTypeTrans((short)228, "Huyện/TP KTM - Bộ TNMT", "Mapping ID danh mục Huyện/TP KTM - Bộ TNMT");
        MappingTypeTrans en = new MappingTypeTrans((short)46, "KTM - Monre District Mapping", "Mapping KTM - Monre District ID");
        List<MappingTypeTrans> trans = new ArrayList<>();
        trans.add(vi);
        trans.add(en);
        mapping.setTrans(trans);

        MappingTypeDataAccess source = new MappingTypeDataAccess();
        source.setEnable(Boolean.TRUE);
        source.setEndpoint("");
        mapping.setSourceDataAccess(source);

        MappingTypeDataAccess dest = new MappingTypeDataAccess();
        source.setEnable(Boolean.FALSE);
        mapping.setDestDataAccess(dest);

        mapping.setStatus(1);

        try {
            mapping.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-22T10:47:44.165+0100"));
            mapping.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-22T10:47:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            mapping.setCreatedDate(new Date());
            mapping.setUpdatedDate(new Date());
        }

        mapping.setIgnoreDeployment(true);

        mapping.setDeploymentId(new ObjectId("60b8799a975e7143acdeb2fe"));

        mongoTemplate.insert(mapping);
    }
}
