package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.client.result.UpdateResult;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.util.ArrayList;
import java.util.List;

@ChangeLog(order = "2022-06-14-14-13")
public class LGSPMinhTueChangeLogs {
    @ChangeSet(order = "2022-06-14-14-13", id = "LGSPMinhTueChangeLogs::createLGSPMinhTueIntegration", author = "nmthien.bdg")
    public void createLGSPMinhTueIntegration(MongoTemplate mongoTemplate) {
        IntegrationService lgspMinhTueService = new IntegrationService();
        lgspMinhTueService.setId(new ObjectId("5f7c16069abb62f511891037"));
        lgspMinhTueService.setName("LGSP Minh Tuệ");
        lgspMinhTueService.setIntegratedUnit("LGSP Minh Tuệ Service");

        List<Parameters> params = new ArrayList<>();

        // Url gateway
        params.add(new Parameters("gateway", ListType.STRING, "https://api.binhduong.gov.vn:8687/"));

        // Url api lấy token
        params.add(new Parameters("gateway-token", ListType.STRING, "https://api.binhduong.gov.vn:8687/token"));

        // Consumer key
        params.add(new Parameters("consumer-key", ListType.STRING, "0O30fC2pDCehbIryAtGMORDyBmAa"));

        // Consumer secret
        params.add(new Parameters("consumer-secret", ListType.STRING, "iABJfvHbw3cxuinYtIg4ZMUvAFYa"));

        lgspMinhTueService.setParameters(params);
        lgspMinhTueService.setStatus(1);

        lgspMinhTueService.setIgnoreDeployment(true);
        lgspMinhTueService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(lgspMinhTueService);
    }

    @ChangeSet(order = "2022-06-28-10-45", id = "LGSPMinhTueChangeLogs::updateLGSPMinhTueIntegration1", author = "nmthien.bdg")
    public void updateLGSPMinhTueIntegration1(MongoTemplate mongoTemplate) {
        IntegrationService lgspMinhTueService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511891037"), IntegrationService.class);

        List<Parameters> params = lgspMinhTueService.getParameters();

        // Api đăng ký hộ tịch
        params.add(new Parameters("registerCivilUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/NGSP-HTTP/2.0/dangKyHoTich"));

        // Api lấy danh mục
        params.add(new Parameters("generalCategoryUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/NGSP-HTTP/2.0/danhMuc"));

        // Api lấy kết quả đăng ký hồ sơ
        params.add(new Parameters("resultRegisterDossierUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/NGSP-HTTP/2.0/ketQuaDangKyHS"));

        // Api lấy danh sách hồ sơ đã đăng ký
        params.add(new Parameters("listRegisteredDossierUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/NGSP-HTTP/2.0/dsHoSoDangKy"));

        // Api trả thông tin chi tiết từng hồ sơ đã lưu
        params.add(new Parameters("searchDossierUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/NGSP-HTTP/2.0/traHoSo"));

        // Api trả thông tin chi tiết danh sách các hồ sơ
        params.add(new Parameters("searchDossierByListUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/NGSP-HTTP/2.0/traDanhSachHoSo"));

        lgspMinhTueService.setParameters(params);
        mongoTemplate.save(lgspMinhTueService);
    }

    @ChangeSet(order = "2022-07-01-15-39", id = "LGSPMinhTueChangeLogs::updateLGSPMinhTueIntegration2", author = "nmthien.bdg")
    public void updateLGSPMinhTueIntegration2(MongoTemplate mongoTemplate) {
        IntegrationService lgspMinhTueService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511891037"), IntegrationService.class);

        List<Parameters> params = lgspMinhTueService.getParameters();

        // Api nhanHoSoDangKy
        params.add(new Parameters("lltpNhanHsDangKyUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/NGSP-LLTP/1.0/nhanHoSoDangKy"));

        // Api traHoSo
        params.add(new Parameters("lltpTraHsUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/NGSP-LLTP/1.0/traHoSo"));

        // Api traDsTrangThaiHs
        params.add(new Parameters("lltpTraDsTtHsUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/NGSP-LLTP/1.0/traDsTrangThaiHs"));

        // Api traDsTrangThaiHs
        params.add(new Parameters("lltpDanhDauHsThanhCongUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/NGSP-LLTP/1.0/danhDauHsThanhCong"));

        lgspMinhTueService.setParameters(params);
        mongoTemplate.save(lgspMinhTueService);
    }

    @ChangeSet(order = "2022-08-19-13-30", id = "LGSPMinhTueChangeLogs::updateLGSPMinhTueIntegration3", author = "binhlq.bdg")
    public void updateLGSPMinhTueIntegration3(MongoTemplate mongoTemplate) {
        IntegrationService lgspMinhTueService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511891037"), IntegrationService.class);

        List<Parameters> params = lgspMinhTueService.getParameters();

        params.add(new Parameters("authorization-basic", ListType.STRING, "ME8zMGZDMnBEQ2VoYklyeUF0R01PUkR5Qm1BYTppQUJKZnZIYnczY3h1aW5ZdElnNFpNVXZBRllh"));

        params.add(new Parameters("url-get-messageids", ListType.STRING, "https://api.binhduong.gov.vn:8687/ISODVQLVB/1.0/get-messageids"));

        params.add(new Parameters("url-get-message-by-id", ListType.STRING, "https://api.binhduong.gov.vn:8687/ISODVQLVB/1.0/get-message-by-id"));

        params.add(new Parameters("url-download-file", ListType.STRING, "https://api.binhduong.gov.vn:8687/ISODVQLVB/1.0/download-file"));

        params.add(new Parameters("url-update-status", ListType.STRING, "https://api.binhduong.gov.vn:8687/ISODVQLVB/1.0/update-status"));

        params.add(new Parameters("menutaskremind-received", ListType.STRING, "62f4d7fb1a6d6b4ce7305650"));

        params.add(new Parameters("enable-schedule", ListType.BOOLEAN, true));

        lgspMinhTueService.setParameters(params);
        mongoTemplate.save(lgspMinhTueService);
    }

    @ChangeSet(order = "2022-08-19-13-45", id = "LGSPMinhTueChangeLogs::updateLGSPMinhTueQLVBParametersConfig", author = "binhlq.bdg")
    public void updateIntegratedQLVBParametersConfig(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511891037")));

        Parameters authorization_basic = new Parameters("authorization-basic", ListType.STRING, "ME8zMGZDMnBEQ2VoYklyeUF0R01PUkR5Qm1BYTppQUJKZnZIYnczY3h1aW5ZdElnNFpNVXZBRllh");
        Parameters url_get_messageids = new Parameters("url-get-messageids", ListType.STRING, "https://api.binhduong.gov.vn:8687/ISODVQLVB/1.0/get-messageids");
        Parameters url_get_message_by_id = new Parameters("url-get-message-by-id", ListType.STRING, "https://api.binhduong.gov.vn:8687/ISODVQLVB/1.0/get-message-by-id");
        Parameters url_download_file = new Parameters("url-download-file", ListType.STRING, "https://api.binhduong.gov.vn:8687/ISODVQLVB/1.0/download-file");
        Parameters url_update_status = new Parameters("url-update-status", ListType.STRING, "https://api.binhduong.gov.vn:8687/ISODVQLVB/1.0/update-status");
        Parameters menutaskremind_received = new Parameters("menutaskremind-received", ListType.STRING, "62f4d7fb1a6d6b4ce7305650");
        Parameters enable_schedule = new Parameters("enable-schedule", ListType.BOOLEAN, true);

        Update update = new Update();
        update.addToSet("parameters").each(authorization_basic);
        UpdateResult updatedauthorization_basic = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);

        update.addToSet("parameters").each(url_get_messageids);
        UpdateResult updatedurl_get_messageids = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);

        update.addToSet("parameters").each(url_get_message_by_id);
        UpdateResult updatedurl_get_message_by_id = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);

        update.addToSet("parameters").each(url_download_file);
        UpdateResult updatedurl_download_file = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);

        update.addToSet("parameters").each(url_update_status);
        UpdateResult updatedurl_update_status = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);

        update.addToSet("parameters").each(menutaskremind_received);
        UpdateResult updatedmenutaskremind_received = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);

        update.addToSet("parameters").each(enable_schedule);
        UpdateResult updatedenable_schedule = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
    }

    @ChangeSet(order = "2022-12-15-09-22", id = "LGSPMinhTueChangeLogs::updateLGSPMinhTueInfoGPKD", author = "ductp.bdg")
    public void updateLGSPMinhTueInfoGPKD(MongoTemplate mongoTemplate) {
        IntegrationService lgspMinhTueService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511891037"), IntegrationService.class);

        List<Parameters> params = lgspMinhTueService.getParameters();

        params.add(new Parameters("mapping-type-id", ListType.STRING, "0612812b6b0b8a3006d2b11f"));

        params.add(new Parameters("GetInfoGPKDbySoGiayPhep", ListType.STRING, "https://api.binhduong.gov.vn:8687/DVC-MC/1.0/GetInfoGPKDbySoGiayPhep"));

        lgspMinhTueService.setParameters(params);
        mongoTemplate.save(lgspMinhTueService);
    }

    @ChangeSet(order = "2022-12-15-09-23", id = "LGSPMinhTueChangeLogs::updateLGSPMinhTueParametersConfigInfoGPKD", author = "ductp.bdg")
    public void updateLGSPMinhTueParametersConfigInfoGPKD(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511891037")));

        Parameters mapping_type_id = new Parameters("mapping-type-id", ListType.STRING, "0612812b6b0b8a3006d2b11f");
        Parameters getInfoGPKDbySoGiayPhep = new Parameters("GetInfoGPKDbySoGiayPhep", ListType.STRING, "https://api.binhduong.gov.vn:8687/DVC-MC/1.0/GetInfoGPKDbySoGiayPhep");

        Update update = new Update();
        update.addToSet("parameters").each(mapping_type_id);
        UpdateResult updatemapping_type_id = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);

        update.addToSet("parameters").each(getInfoGPKDbySoGiayPhep);
        UpdateResult updatedGetInfoGPKDbySoGiayPhep = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
    }

    @ChangeSet(order = "2022-12-19-09-22", id = "LGSPMinhTueChangeLogs::updateLGSPMinhTueInfoGPXD", author = "ductp.bdg")
    public void updateLGSPMinhTueInfoGPXD(MongoTemplate mongoTemplate) {
        IntegrationService lgspMinhTueService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511891037"), IntegrationService.class);

        List<Parameters> params = lgspMinhTueService.getParameters();

        params.add(new Parameters("GetInfoGPXDbySoGiayPhep", ListType.STRING, "https://api.binhduong.gov.vn:8687/DVC-MC/1.0/GetInfoGPXDbySoGiayPhep"));

        lgspMinhTueService.setParameters(params);
        mongoTemplate.save(lgspMinhTueService);
    }

    @ChangeSet(order = "2022-12-19-09-23", id = "LGSPMinhTueChangeLogs::updateLGSPMinhTueParametersConfigInfoGPXD", author = "ductp.bdg")
    public void updateLGSPMinhTueParametersConfigInfoGPXD(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511891037")));

        Parameters getInfoGPXDbySoGiayPhep = new Parameters("GetInfoGPXDbySoGiayPhep", ListType.STRING, "https://api.binhduong.gov.vn:8687/DVC-MC/1.0/GetInfoGPXDbySoGiayPhep");

        Update update = new Update();

        update.addToSet("parameters").each(getInfoGPXDbySoGiayPhep);
        UpdateResult updatedGetInfoGPXDbySoGiayPhep = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
    }

    @ChangeSet(order = "2022-12-29-08-45", id = "LGSPMinhTueChangeLogs::updateLGSPMinhTueParametersConfigTransportationData", author = "nmthien.bdg")
    public void updateLGSPMinhTueParametersConfigTransportationData(MongoTemplate mongoTemplate) {
        IntegrationService lgspMinhTueService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511891037"), IntegrationService.class);
        List<Parameters> params = lgspMinhTueService.getParameters();

        params.add(new Parameters("getListDossierTransportationUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/apiQLVT/1.0/danhsachhoso"));
        params.add(new Parameters("searchDossierTransportationUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/apiQLVT/1.0/tracuuhoso"));
        params.add(new Parameters("getStatisticalTransportation", ListType.STRING, "https://api.binhduong.gov.vn:8687/apiQLVT/1.0/thongkehoso"));
        params.add(new Parameters("transportationGovAgencyCode", ListType.STRING, "SGTVT50"));

        lgspMinhTueService.setParameters(params);
        mongoTemplate.save(lgspMinhTueService);

        // Update all configuration
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511891037")));
        Update update = new Update();
        params = new ArrayList<>();

        params.add(new Parameters("getListDossierTransportationUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/apiQLVT/1.0/danhsachhoso"));
        params.add(new Parameters("searchDossierTransportationUrl", ListType.STRING, "https://api.binhduong.gov.vn:8687/apiQLVT/1.0/tracuuhoso"));
        params.add(new Parameters("getStatisticalTransportation", ListType.STRING, "https://api.binhduong.gov.vn:8687/apiQLVT/1.0/thongkehoso"));
        params.add(new Parameters("transportationGovAgencyCode", ListType.STRING, "SGTVT50"));

        update.addToSet("parameters").each(params);
        UpdateResult updateAllConfigTransportationData = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateAllConfigTransportationData" + updateAllConfigTransportationData);
    }

    @ChangeSet(order = "2023-04-04-10-09", id = "LGSPMinhTueChangeLogs::updateLGSPMinhTueParametersConfigTransportationData2", author = "nmthien.bdg")
    public void updateLGSPMinhTueParametersConfigTransportationData2(MongoTemplate mongoTemplate) {
        IntegrationService lgspMinhTueService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511891037"), IntegrationService.class);
        List<Parameters> params = lgspMinhTueService.getParameters();

        params.add(new Parameters("transportationApiKey", ListType.STRING, "u2aKFpOu1oZfkwnWx/Lv3dnhCRtBgDL6oYjzOzK2N467uMdKI2elI3mdjQeU8FLiEN3xW7Hlr/OH+s07UWHl7IBH7HEkwZwy"));

        lgspMinhTueService.setParameters(params);
        mongoTemplate.save(lgspMinhTueService);

        // Update all configuration
        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511891037")));
        Update update = new Update();
        params = new ArrayList<>();

        params.add(new Parameters("transportationApiKey", ListType.STRING, "u2aKFpOu1oZfkwnWx/Lv3dnhCRtBgDL6oYjzOzK2N467uMdKI2elI3mdjQeU8FLiEN3xW7Hlr/OH+s07UWHl7IBH7HEkwZwy"));

        update.addToSet("parameters").each(params);
        UpdateResult updateAllConfigTransportationData = mongoTemplate.updateMulti(query, update, IntegratedConfiguration.class);
        System.out.println("updateAllConfigTransportationData2" + updateAllConfigTransportationData);
    }
}
