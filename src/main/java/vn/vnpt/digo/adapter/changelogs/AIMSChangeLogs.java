package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.core.env.Environment;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@ChangeLog(order = "2023-11-30-10-22")
public class AIMSChangeLogs {
    @ChangeSet(order = "2023-11-30-10-22", id = "AIMSChangeLogs::createMigrateTichHop", author = "tuqn")
    public void createMigrateTichHop(MongoTemplate mongoTemplate, Environment environment) {
        ObjectId deploymentId = new ObjectId(environment.getProperty("digo.microservice.deployment.id"));
        IntegrationService service = new IntegrationService();
        service.setId(new ObjectId("5f7c16069abb62f511890043"));
        service.setName("AIMS Service");
        service.setIntegratedUnit("AIMS Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters tokenUrl = new Parameters("token-url", ListType.STRING, "https://iscsoidcdemo.digigov.vn/realms/iscs/protocol/openid-connect/token");
        lstParams.add(tokenUrl);

        Parameters type = new Parameters("grant_type", ListType.STRING, "password");
        lstParams.add(type);

        Parameters username = new Parameters("username", ListType.STRING, "admintbh/aims");
        lstParams.add(username);

        Parameters password = new Parameters("password", ListType.STRING, "NngN#1990Ttp");
        lstParams.add(password);

        Parameters usernameAuth = new Parameters("client_id", ListType.STRING, "aims-web-aims");
        lstParams.add(usernameAuth);

        Parameters themMoiHoSoUrl = new Parameters("aims-url", ListType.STRING, "http://api-aims.vnptics.vn/nnptnt/he-thong");
        lstParams.add(themMoiHoSoUrl);

        service.setParameters(lstParams);
        service.setStatus(1);
        service.setIgnoreDeployment(true);
        service.setDeploymentId(deploymentId);
        mongoTemplate.insert(service);

        //set config
        IntegratedConfiguration config = new IntegratedConfiguration();
        IntegratedService integratedService = new IntegratedService(service);
        config.setService(integratedService);
        config.setParameters(lstParams);
        config.setDeploymentId(deploymentId);
        config.setStatus(1);
        config.setDeleted(false);
        config.setName("AIMS service");
        config.setApplyAll(true);

        // Set subsystem
        Subsystem subSystem = new Subsystem();
        subSystem.setId(new ObjectId("5f7c16069abb62f511880003"));
        SubsystemTrans subSystemTrans = new SubsystemTrans();
        subSystemTrans.setLanguageId((short)228);
        subSystemTrans.setName("VNPT iGate 2.0");
        subSystem.setTrans(Arrays.asList(subSystemTrans));
        config.setSubsystem(Arrays.asList(subSystem));

        // Set tag
        Tag tag = new Tag();
        TransTag tagTrans = new TransTag();
        tag.setId(new ObjectId("62fdab49d13be03598afb8fc"));
        tagTrans.setLanguageId((short)228);
        tagTrans.setName("Nhóm chức năng liên thông");
        tag.setTrans(Arrays.asList(tagTrans));
        config.setTag(tag);
        mongoTemplate.save(config);
    }
}
