/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

@ChangeLog(order = "2020-11-05-10-17")
public class JudicialRecordsChangeLongs {
    @ChangeSet(order = "2020-11-05-10-17", id = "JudicialRecordsChangeLogs::createCriminalRecordIntegration", author = "datnt")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService criminalRecordService = new IntegrationService();
        criminalRecordService.setId(new ObjectId("5f7c16069abb62f511890009"));
        criminalRecordService.setName("VNPT Criminal Record");
        criminalRecordService.setIntegratedUnit("VNPT Criminal Record Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters consumerKey = new Parameters("consumer-key", ListType.STRING, "eNclO_Hqb682bPAREHbAbVepKWMa");
        lstParams.add(consumerKey);

        Parameters consumerSecret = new Parameters("consumer-secret", ListType.STRING, "Y1joLcSKYeceuFfG21NzE_4bqM0a");
        lstParams.add(consumerSecret);

        Parameters gatewayToken = new Parameters("gateway-token", ListType.STRING, "http://14.238.8.34:8280/token");
        lstParams.add(gatewayToken);

        Parameters gateway = new Parameters("gateway", ListType.STRING, "http://14.238.8.34:8280/dvctp/1.0.0/tuphap");
        lstParams.add(gateway);
        
        
        criminalRecordService.setParameters(lstParams);
        criminalRecordService.setStatus(1);
        try {
            criminalRecordService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-05T10:33:44.165+0100"));
            criminalRecordService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-05T10:33:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            criminalRecordService.setCreatedDate(new Date());
            criminalRecordService.setUpdatedDate(new Date());
        }
        criminalRecordService.setIgnoreDeployment(true);
        criminalRecordService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(criminalRecordService);
    }


    @ChangeSet(order = "2022-10-04-14-413", id = "CivilStatusJusticeChangeLogs::updateCivilStatusJusticeIntegration3", author = "duyenpnc")
    public void UpdateIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService civilStatusJusticeService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511890009"),IntegrationService.class);

        List<Parameters> lstParams = civilStatusJusticeService.getParameters();

        Parameters statusReturn = new Parameters("statusReturn", ListType.STRING, "traHoSo");
        lstParams.add(statusReturn);
        Parameters searchCatalog = new Parameters("searchCatalog", ListType.STRING, "traDanhMuc");
        lstParams.add(searchCatalog);
        Parameters dossiersStatus = new Parameters("dossiersStatus", ListType.STRING, "traDsTrangThaiHs");
        lstParams.add(dossiersStatus);
        civilStatusJusticeService.setParameters(lstParams);
        try {
            civilStatusJusticeService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-05T10:33:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            civilStatusJusticeService.setUpdatedDate(new Date());
        }
        mongoTemplate.save(civilStatusJusticeService);
    }
}
