package vn.vnpt.digo.adapter.changelogs.vpc;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.util.ArrayList;
import java.util.List;

@ChangeLog(order = "2024-09-05-00-10")
public class GtvtChangeLogs {
    private static final ObjectId GTVT_SERVICE_ID = new ObjectId("66d918cad850957c9bd74d3f");

    @ChangeSet(order = "2024-09-05-00-10", id = "GtvtChangeLogs::createIntegrationService", author = "haipv")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(GTVT_SERVICE_ID);
        integrationService.setName("VPC kết nối Bộ giao thông vận tải Service");
        integrationService.setIntegratedUnit("VPC kết nối Bộ giao thông vận tải Service");
        List<Parameters> params = new ArrayList<>();
        Parameters p1 = new Parameters("gtvtApiKey", ListType.STRING, "");
        params.add(p1);
        Parameters p2 = new Parameters("gtvtMaDonVi", ListType.STRING, "SGTVT211");
        params.add(p2);

        Parameters p3 = new Parameters("traCuuHoSo", ListType.STRING, "https://lgsp-api.vinhphuc.gov.vn/lgsp-gtvt-phuhieu/1.0.0/tracuuhoso");
        params.add(p3);
        Parameters p4 = new Parameters("danhSachHoSo", ListType.STRING, "https://lgsp-api.vinhphuc.gov.vn/lgsp-gtvt-phuhieu/1.0.0/danhsachhoso");
        params.add(p4);
        Parameters p5 = new Parameters("thongKeHoSo", ListType.STRING, "https://lgsp-api.vinhphuc.gov.vn/lgsp-gtvt-phuhieu/1.0.0/thongkehoso");
        params.add(p5);

        integrationService.setParameters(params);
        integrationService.setStatus(1);
        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(integrationService);
    }
}
