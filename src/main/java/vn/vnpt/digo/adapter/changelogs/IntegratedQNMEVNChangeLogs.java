package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;
import vn.vnpt.digo.adapter.util.Context;

import java.util.ArrayList;
import java.util.List;

@ChangeLog(order = "2023-03-06-16-51")
public class IntegratedQNMEVNChangeLogs {
    @ChangeSet(order = "2023-03-06-16-51", id = "IntegratedQNMEVNChangeLogs::createIntegratedQNMEVNChangeLogs", author = "lvhquan")
    public void createIntegratedQNMEVNChangeLogs(MongoTemplate mongoTemplate) {
        IntegrationService service = new IntegrationService();
        service.setId(new ObjectId("c0034a1cae8145bfc0335ee3"));
        service.setName("Integrated QNM EVN");
        service.setIntegratedUnit("Integrated QNM EVN");

        List<Parameters> params = new ArrayList<>();
        // Url api lấy token
        params.add(new Parameters("gateway-token", ListType.STRING, "https://api.quangnam.gov.vn/token"));
        // consumer-key
        params.add(new Parameters("consumer-key", ListType.STRING, "GJbFfb2XnDMCJoTq0OfCD1kO3Zga"));
        // consumer-secret
        params.add(new Parameters("consumer-secret", ListType.STRING, "d3S6ZYqUGyqASLKrqKA41WhuiCka"));
        // API tra cứu hóa đơn
        params.add(new Parameters("lookup-invoice", ListType.STRING, "https://api.quangnam.gov.vn/apiEVN/1.0/api/tc/hoadon"));
        // API tra cứu tiến độ cấp điện
        params.add(new Parameters("lookup-progress", ListType.STRING, "https://api.quangnam.gov.vn/apiEVN/1.0/api/tc/tiendocapdien"));
        // API tra cứu điện tiêu thụ tỉnh
        params.add(new Parameters("lookup-consume-province", ListType.STRING, "https://api.quangnam.gov.vn/apiEVN/1.0/api/tc/tieuthudientinh"));
        // API tra cứu điện tiêu thụ huyện
        params.add(new Parameters("lookup-consume-district", ListType.STRING, "https://api.quangnam.gov.vn/apiEVN/1.0/api/tc/tieuthudienhuyen"));

        service.setParameters(params);
        service.setStatus(1);
        service.setIgnoreDeployment(true);
//        service.setDeploymentId(new ObjectId(String.valueOf(Context.getDeploymentId())));
        service.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(service);
    }

    @ChangeSet(order = "2023-03-07-14-28", id = "IntegratedQNMEVNChangeLogs::updateIntegratedQNMEVNChangeLogs", author = "lvhquan")
    public void updateIntegratedQNMEVNChangeLogs(MongoTemplate mongoTemplate) {
        IntegrationService service = mongoTemplate.findById(new ObjectId("c0034a1cae8145bfc0335ee3"), IntegrationService.class);
        List<Parameters> params = service.getParameters();
        params.add(new Parameters("lookup-power-cut", ListType.STRING, "https://api.quangnam.gov.vn/apiEVN/1.0/api/tc/catdien"));
        service.setParameters(params);
        mongoTemplate.save(service);
    }
}
