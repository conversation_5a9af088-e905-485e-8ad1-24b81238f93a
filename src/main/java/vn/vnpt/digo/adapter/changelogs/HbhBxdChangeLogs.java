package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ChangeLog(order = "2023-10-18-13-05")
public class HbhBxdChangeLogs {
    @ChangeSet(order = "2023-10-18-13-05", id = "HbhBxdChangeLogs::create", author = "haipv")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(new ObjectId("652e548aacaf0c2f6ed7a353"));
        integrationService.setName("HBH Bộ xây dựng Service");
        integrationService.setIntegratedUnit("HBH Ministry of Construction Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters param2 = new Parameters("LayThongTinHoSo", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn/ngsp/CongDVCBoXayDung/DongBoHoSoNopTaiDVCBoXayDung");
        lstParams.add(param2);

        Parameters param3 = new Parameters("GuiThongTinHoSo", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn/ngsp/CongDVCBoXayDung/DongBoHoSoNopTaiDiaPhuong");
        lstParams.add(param3);

        Parameters param4 = new Parameters("GuiThongTinXuLyHoSo", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn/ngsp/CongDVCBoXayDung/DongBoTienTrinhXuLyHoSoNopTaiDiaPhuong");
        lstParams.add(param4);

        Parameters param5 = new Parameters("LanCuoiDongBoLayHoSo", ListType.STRING, "");
        lstParams.add(param5);

        Parameters param6 = new Parameters("MaDonViDongBo", ListType.STRING, "");
        lstParams.add(param6);

        Parameters param7 = new Parameters("QuaTruc", ListType.STRING, "LGSP_TRINAM");
        lstParams.add(param7);

        Parameters param8 = new Parameters("procedure-id", ListType.STRING, "");
        lstParams.add(param8);

        Parameters param9 = new Parameters("process-id", ListType.STRING, "");
        lstParams.add(param9);

        Parameters param10 = new Parameters("agency-reception-id", ListType.STRING, "");
        lstParams.add(param10);

        integrationService.setParameters(lstParams);
        integrationService.setStatus(1);
        try {
            integrationService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-18-17T01:05:18.133Z"));
            integrationService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-18-17T01:05:18.133Z"));
        } catch (ParseException | NullPointerException e) {
            integrationService.setCreatedDate(new Date());
            integrationService.setUpdatedDate(new Date());
        }
        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(integrationService);
    }

}
