/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2021-05-10-04-30")
public class VilisChangeLogs {
    
    @ChangeSet(order = "2021-05-10-04-30", id = "VilisChangeLogs::createVilisService", author = "mongtt")
    public void createVilisService(MongoTemplate mongoTemplate) {
        IntegrationService vilisService = new IntegrationService();
        vilisService.setId(new ObjectId("5f7c16069abb62f511890017"));
        vilisService.setName("Vilis");
        vilisService.setIntegratedUnit("VNPT TGG");
        List<Parameters> lstParams = new ArrayList<>();
        //Vilis domain
        Parameters vilisDomain = new Parameters("vilis-domain", ListType.STRING, "http://113.163.202.18:10001/Service.asmx");
        lstParams.add(vilisDomain);
        
        //username
        Parameters username = new Parameters("username", ListType.STRING, "TVG_0001");
        lstParams.add(username);
        
        //password
        Parameters password = new Parameters("password", ListType.STRING, "TVG@12345");
        lstParams.add(password);

        vilisService.setParameters(lstParams);
        vilisService.setStatus(1);
        try {
            vilisService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2021-04-07T10:30:44.165+0100"));
            vilisService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2021-04-07T10:30:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            vilisService.setCreatedDate(new Date());
            vilisService.setUpdatedDate(new Date());
        }
        vilisService.setIgnoreDeployment(true);
        vilisService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(vilisService);
    }
}
