package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.util.ArrayList;
import java.util.List;

@ChangeLog(order = "2024-10-28-01-01")
public class AGGDVCLTChangelogs {
    @ChangeSet(order = "2024-10-28-01-01", id = "AGGDVCLTChangelogs::createIntegrationAGGDVCLT", author = "haudp")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(new ObjectId());
        integrationService.setName("Integrated AGESB DVCLT AGG");
        integrationService.setIntegratedUnit("Integrated AGESB DVCLT AGG Service");

        List<Parameters> params = new ArrayList<>();

        // url-lay-ket-qua
        params.add(new Parameters("url-lay-ket-qua", ListType.STRING, "https://lgsp.angiang.gov.vn/l2ngspgw/1.0/test/apiLienThongKSKT/layKetQua"));
        // url-nhan-ho-so
        params.add(new Parameters("url-nhan-ho-so", ListType.STRING, "https://lgsp.angiang.gov.vn/agesb-hotich/test/nhanHoSoDKHT"));
        // soapaction
        params.add(new Parameters("soapaction", ListType.STRING, "http://angiang.gov.vn/operation/agesb/GuiHoSoMoi"));
        // url-get-token
        params.add(new Parameters("url-get-token", ListType.STRING, "https://lgsp.angiang.gov.vn/token"));
        // consumer-key
        params.add(new Parameters("consumer-key", ListType.STRING, "JbBrOuLcs7Kwo2Itp_U80RP5m6Ya"));
        // consumer-secret
        params.add(new Parameters("consumer-secret", ListType.STRING, "WBLLLKYVEQiGa2YRX7pm0dN_yyga"));
        // ma don vi
        params.add(new Parameters("ma-dv", ListType.STRING, "30502"));

        integrationService.setParameters(params);
        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("646ab854f137d0251281610e"));
        integrationService.setStatus(1);
        mongoTemplate.save(integrationService);
    }

}
