/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.util.ArrayList;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2023-08-28-15-55")
public class ChungThucDienTuChangeLogs {
    @ChangeSet(order = "2023-08-28-15-55", id = "ChungThucDienTuChangeLogs::createChungThucDienTuChangeLogs", author = "tiennb.it4")
    public void createChungThucDienTuChangeLogs(MongoTemplate mongoTemplate) {
        IntegrationService service = new IntegrationService();
        service.setId(new ObjectId("5f7c16069abb62f511891708"));
        service.setName("Chứng thực điện tử");
        service.setIntegratedUnit("VNPT Chứng thực điện tử");

        List<Parameters> params = new ArrayList<>();
        params.add(new Parameters("api_get_token", ListType.STRING, "https://gateway-chungthuc.vnpt.vn/api/chungthuc/authenticate"));
        params.add(new Parameters("api_init", ListType.STRING, "https://gateway-chungthuc.vnpt.vn/api/chungthuc/init"));
        params.add(new Parameters("account_username", ListType.STRING, "igate"));
        params.add(new Parameters("account_password", ListType.STRING, "ctDT@2023"));
        params.add(new Parameters("api_download_file", ListType.STRING, "https://gateway-chungthuc.vnpt.vn/api/chungthuc/file/download"));
        params.add(new Parameters("api_preview_file", ListType.STRING, "https://gateway-chungthuc.vnpt.vn/api/chungthuc/file/preview"));
        params.add(new Parameters("api_update_fee", ListType.STRING, "https://gateway-chungthuc.vnpt.vn/api/chungthuc/update-fee"));
//        params.add(new Parameters("danh_sach_thu_tuc", ListType.STRING, "2.000815"));
//        params.add(new Parameters("bat_lien_thong", ListType.STRING, "0"));
//        params.add(new Parameters("domain_chung_thuc", ListType.STRING, "https://chungthuc.vnpt.vn/web/login.jsp"));
        params.add(new Parameters("igate_url_get_token", ListType.STRING, "https://ssotest.vnptigate.vn/auth/realms/digo/protocol/openid-connect/token"));
        params.add(new Parameters("igate_grant_type", ListType.STRING, "password"));
        params.add(new Parameters("igate_client_id", ListType.STRING, "svc-adapter"));
        params.add(new Parameters("igate_client_secret", ListType.STRING, "62ca3e28-2878-473c-8956-22f5cf288b3a"));
        params.add(new Parameters("igate_url_verify", ListType.STRING, "https://ssotest.vnptigate.vn/auth/realms/digo/protocol/openid-connect/userinfo"));

        service.setParameters(params);
        service.setStatus(1);
        service.setIgnoreDeployment(true);
        service.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(service);
    }
}
