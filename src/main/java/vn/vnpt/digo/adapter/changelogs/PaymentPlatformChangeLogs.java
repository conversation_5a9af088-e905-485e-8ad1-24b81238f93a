/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-11-25-11-10")
public class PaymentPlatformChangeLogs {
    @ChangeSet(order = "2020-11-25-11-11", id = "PaymentPlatformChangeLogs::createPaymentPlatformService", author = "mongtt")
    public void createDigitalSignatureService(MongoTemplate mongoTemplate) {
        IntegrationService digitalSignatureService = new IntegrationService();
        digitalSignatureService.setId(new ObjectId("5f7c16069abb62f511890013"));
        digitalSignatureService.setName("Payment Platform");
        digitalSignatureService.setIntegratedUnit("National Public Service");
        List<Parameters> lstParams = new ArrayList<>();
        //Init url
        Parameters initUrl = new Parameters("init-url", ListType.STRING, "http://*************:80/VXPAdapter/RestService/forward/payment-api/rest/payment/v1.0.6/init?providerurl={provideUrl}&dstcode={dstCode}");
        lstParams.add(initUrl);
        //Get bill url
        Parameters getBillUrl = new Parameters("get-bill-url", ListType.STRING, "http://*************:80/VXPAdapter/RestService/forward/payment-api/rest/payment/v1.0.6/get_bill?providerurl={provideUrl}&dstcode={dstCode}");
        lstParams.add(getBillUrl);
        //Get bill replace regex
        Parameters getBillReplaceRegex = new Parameters("get-bill-replace-regex", ListType.STRING, "http://ip-ss-donvi:8080/XrdAdapter/RestService/forward/");
        lstParams.add(getBillReplaceRegex);
        //Get bill replace regex
        Parameters getBillReplaceBy = new Parameters("get-bill-get-provider-url", ListType.STRING, "&providerurl=");
        lstParams.add(getBillReplaceBy);
        //Query url
        Parameters queryUrl = new Parameters("query-url", ListType.STRING, "http://*************:80/VXPAdapter/RestService/forward/payment-api/rest/payment/v1.0.6/query_transaction?providerurl={provideUrl}&dstcode={dstCode}");
        lstParams.add(queryUrl);
        //Version API
        Parameters version = new Parameters("version", ListType.STRING, "1.0.6");
        lstParams.add(version);
        //Partner code
        Parameters partnerCode = new Parameters("partner-code", ListType.STRING, "000.00.00.G12");
        lstParams.add(partnerCode);
        //Secret Code
        Parameters secretCode = new Parameters("secret-code", ListType.STRING, "5901fe6483310c89c35b54455f2a5557");
        lstParams.add(secretCode);
        //Beneficiary account
        Parameters beneficiaryAccount = new Parameters("beneficiary-account", ListType.STRING, "*****************");
        lstParams.add(beneficiaryAccount);
        //VNPT algorithm
        Parameters beneficiaryAccountName = new Parameters("beneficiary-account-name", ListType.STRING, "TRUONG MINH THUAN");
        lstParams.add(beneficiaryAccountName);
        //Beneficiary Bank Code
        Parameters beneficiaryBankCode = new Parameters("beneficiary-bank-code", ListType.STRING, "********");
        lstParams.add(beneficiaryBankCode);
        //dstcode
        Parameters dstCode = new Parameters("dstcode", ListType.STRING, "VN:GOV:000.00.00.G22:vpcpcdvcqgsub01");
        lstParams.add(dstCode);
        //Provide Url
        Parameters provideUrl = new Parameters("provide-url", ListType.STRING, "https://testapipc.dichvucong.gov.vn");
        lstParams.add(provideUrl);
        //Provide Url
        Parameters padSsoUrl = new Parameters("pad-sso-url", ListType.STRING, "https://oidctest.vncitizens.vn");
        lstParams.add(padSsoUrl);
        //Provide Url
        Parameters padClientId = new Parameters("pad-client-id", ListType.STRING, "pad-adapter");
        lstParams.add(padClientId);
        //Provide Url
        Parameters padClientSecret = new Parameters("pad-client-secret", ListType.STRING, "abc");
        lstParams.add(padClientSecret);
        //Provide Url
        Parameters padApi = new Parameters("pad-api", ListType.STRING, "https://apitest.vncitizens.vn/pa");
        lstParams.add(padApi);
        
        digitalSignatureService.setParameters(lstParams);
        digitalSignatureService.setStatus(1);
        try {
            digitalSignatureService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-25T11:10:44.165+0100"));
            digitalSignatureService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-11-25T11:10:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            digitalSignatureService.setCreatedDate(new Date());
            digitalSignatureService.setUpdatedDate(new Date());
        }
        digitalSignatureService.setIgnoreDeployment(true);
        digitalSignatureService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(digitalSignatureService);
    }
    
    @ChangeSet(order = "2022-06-01-11-11", id = "PaymentPlatformChangeLogs::upDatePaymentPlatformService", author = "nvtoan")
    public void updateDigitalSignatureService(MongoTemplate mongoTemplate) {
        IntegrationService PaymentService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511890013"),IntegrationService.class);
        List<Parameters> params = PaymentService.getParameters();
        for(Parameters aParam : params){
            if(aParam.getKey().equals("get-bill-replace-regex")){
                aParam.setOriginValue("http://ip-ss-donvi:8080/XrdAdapter");
            }
            if(aParam.getKey().equals("get-bill-get-provider-url")){
                aParam.setOriginValue("http://*************:80/VXPAdapter");
            }
        }
        
        PaymentService.setParameters(params);
        
        mongoTemplate.save(PaymentService);
    }
    
    @ChangeSet(order = "2022-10-04-23-35", id = "PaymentPlatformChangeLogs::upDateAddPaymentPlatformService1", author = "nvtoan")
    public void upDateAddPaymentPlatformService(MongoTemplate mongoTemplate) {
        IntegrationService paymentService = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511890013"),IntegrationService.class);
        List<Parameters> params = paymentService.getParameters();
        
        Parameters updatePromotionalProcedureId = new Parameters("check-lgsp-HCM", ListType.STRING, "0");
        params.add(updatePromotionalProcedureId);

        paymentService.setParameters(params);
        mongoTemplate.save(paymentService);
    }
}

