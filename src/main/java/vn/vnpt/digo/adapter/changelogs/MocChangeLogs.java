package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ChangeLog(order = "2023-02-20-09-21")
public class MocChangeLogs {
    @ChangeSet(order = "2023-02-20-09-21", id = "MocChangeLogs::create", author = "nghiadd.bdg")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(new ObjectId("b5661e31f0d263d1a63120c7"));
        integrationService.setName("Bộ xây dựng (MOC) Service");
        integrationService.setIntegratedUnit("Ministry of Construction (MOC) Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters param1 = new Parameters("LayThongTinHoSo", ListType.STRING, "https://api.binhduong.gov.vn:8687/apiBXD/1.0/layThongTinHoSo");
        lstParams.add(param1);

        Parameters param2 = new Parameters("GuiThongTinHoSo", ListType.STRING, "https://api.binhduong.gov.vn:8687/apiBXD/1.0/guiThongTinHoSo");
        lstParams.add(param2);

        Parameters param3 = new Parameters("GuiThongTinXuLyHoSo", ListType.STRING, "https://api.binhduong.gov.vn:8687/apiBXD/1.0/guiThongTinXuLyHoSo");
        lstParams.add(param3);

        Parameters param4 = new Parameters("procedure-id", ListType.STRING, "");
        lstParams.add(param4);

//        Parameters param9 = new Parameters("agency-reception-id", ListType.STRING, "");
//        lstParams.add(param9);

        Parameters param5 = new Parameters("process-id", ListType.STRING, "");
        lstParams.add(param5);

        Parameters param7 = new Parameters("LanCuoiDongBoLayHoSo", ListType.STRING, "");
        lstParams.add(param7);

        Parameters param8 = new Parameters("QuaTruc", ListType.STRING, "");
        lstParams.add(param8);

        integrationService.setParameters(lstParams);
        integrationService.setStatus(1);
        try {
            integrationService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-20T10:33:44.165+0100"));
            integrationService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-20T10:33:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            integrationService.setCreatedDate(new Date());
            integrationService.setUpdatedDate(new Date());
        }
        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(integrationService);
    }

    @ChangeSet(order = "2023-02-20-09-23", id = "MocChangeLogs::updateIntegrationService1", author = "nghiadd.bdg")
    public void updateIntegrationService1(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = mongoTemplate.findById(new ObjectId("b5661e31f0d263d1a63120c7"), IntegrationService.class);
        List<Parameters> params = integrationService.getParameters();

        params.add(new Parameters("agency-reception-id", ListType.STRING, ""));
        integrationService.setParameters(params);
        mongoTemplate.save(integrationService);
    }

    @ChangeSet(order = "2023-03-17-09-23", id = "MocChangeLogs::updateIntegrationService2", author = "nghiadd.bdg")
    public void updateIntegrationService2(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = mongoTemplate.findById(new ObjectId("b5661e31f0d263d1a63120c7"), IntegrationService.class);
        List<Parameters> params = integrationService.getParameters();

        params.add(new Parameters("userSystemId", ListType.STRING, ""));
        integrationService.setParameters(params);
        mongoTemplate.save(integrationService);
    }
}
