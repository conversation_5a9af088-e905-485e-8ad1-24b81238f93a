package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.BasicDBObject;
import com.mongodb.DB;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2020-10-20-09-44")
public class ICIndexChangeLogs {

    @ChangeSet(order = "2020-10-20-09-44", id = "IntegratedConfigurationIndexChangeLogs::createIndex1", author = "haimn")
    public void createIndex1(DB db) {
        Map<String, Object> textIndex = new HashMap<>();
        textIndex.put("name", "text");
        textIndex.put("integratedUnit", "text");
        textIndex.put("parameters.key", "text");
        textIndex.put("parameters.type.name", "text");
        db.getCollection("integratedConfiguration").createIndex(new BasicDBObject(textIndex), "searchIndex");
        db.getCollection("integratedConfiguration").createIndex(new BasicDBObject("status", 1), "statusIndex");
        db.getCollection("integratedConfiguration").createIndex(new BasicDBObject("createdDate", 1), "createTimeIndex");
//        Map<String, Object> applyIndex = new HashMap<>();
//        applyIndex.put("subsystem.id", "text");
//        applyIndex.put("service.id", "text");
//        applyIndex.put("applyAgencies.id", "text");
//        db.getCollection("integratedConfiguration").createIndex(new BasicDBObject(applyIndex), "applyIndex1");
//        Map<String, Object> exceptIndex = new HashMap<>();
//        exceptIndex.put("subsystem.id", "except");
//        exceptIndex.put("service.id", "except");
//        exceptIndex.put("exceptAgencies.id", "except");
//        db.getCollection("integratedConfiguration").createIndex(new BasicDBObject(exceptIndex), "exceptIndex");
    }
}
