package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ChangeLog(order = "2023-10-18-13-05")
public class HbhLdTbXhChangeLogs {
    public final static ObjectId SERVICE_ID = new ObjectId("653b286160f44e6e2eaf86a2");
    @ChangeSet(order = "2023-10-18-13-05", id = "HbhLdTbXhChangeLogs::create", author = "haipv")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(SERVICE_ID);
        integrationService.setName("Bộ Lao động và thương binh xã hội Service");
        integrationService.setIntegratedUnit("Bộ Lao động và thương binh xã hội Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters param1 = new Parameters("URL_LayHoSoDangKy", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn/ngsp/BaoTroXaHoi/DanhSachHoSo");
        lstParams.add(param1);

        Parameters param2 = new Parameters("URL_LayKetQuaCuoiCung", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn/ngsp/BaoTroXaHoi/DanhSachHoSo");
        lstParams.add(param2);

        Parameters param3 = new Parameters("URL_LayTienTrinhXuLy", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn/ngsp/BaoTroXaHoi/TienTrinhXuLy");
        lstParams.add(param3);

        Parameters param4 = new Parameters("URL_DongBoHoSoDangKy", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn/ngsp/BaoTroXaHoi/DongBoDVC");
        lstParams.add(param4);

        Parameters param5 = new Parameters("URL_CapNhatKetQuaCuoiCung", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn/ngsp/BaoTroXaHoi/DongBoDVC");
        lstParams.add(param5);

        Parameters param6 = new Parameters("URL_DongBoTienTrinhXuLy", ListType.STRING, "https://lgspgateway.hoabinh.gov.vn/ngsp/BaoTroXaHoi/XuLyHoSo");
        lstParams.add(param6);

        Parameters param7 = new Parameters("AccessKey", ListType.STRING, "");
        lstParams.add(param7);

        Parameters param8 = new Parameters("MaTinh", ListType.STRING, "17");
        lstParams.add(param8);

        integrationService.setParameters(lstParams);
        integrationService.setStatus(1);
        try {
            integrationService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-18-17T01:05:18.133Z"));
            integrationService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-18-17T01:05:18.133Z"));
        } catch (ParseException | NullPointerException e) {
            integrationService.setCreatedDate(new Date());
            integrationService.setUpdatedDate(new Date());
        }
        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(integrationService);
    }

    @ChangeSet(order = "2023-11-03-00-00", id = "HbhLdTbXhChangeLogs::updateMappingServiceId", author = "haipv")
    public void updateMappingServiceId(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = mongoTemplate.findById(SERVICE_ID,IntegrationService.class);
        List<Parameters> params = integrationService.getParameters();
        Parameters param0 = new Parameters("EnableSync_LayKetQuaCuoiCung", ListType.BOOLEAN, false);
        params.add(param0);

        Parameters param1 = new Parameters("LastSync_LayKetQuaCuoiCung", ListType.STRING, "");
        params.add(param1);

        Parameters param2 = new Parameters("MappingStatusServiceId", ListType.STRING, "");
        params.add(param2);

        Parameters param3 = new Parameters("MappingProvinceServiceId", ListType.STRING, "");
        params.add(param3);

        Parameters param4 = new Parameters("MappingDistrictServiceId", ListType.STRING, "");
        params.add(param4);

        Parameters param5 = new Parameters("MappingCommuneServiceId", ListType.STRING, "");
        params.add(param5);

        integrationService.setParameters(params);
        mongoTemplate.save(integrationService);
    }

    @ChangeSet(order = "2023-11-03-10-00", id = "HbhLdTbXhChangeLogs::updateConfigObjectId", author = "haipv")
    public void updateConfigObjectId(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = mongoTemplate.findById(SERVICE_ID,IntegrationService.class);
        List<Parameters> params = integrationService.getParameters();
        Parameters param0 = new Parameters("NhanQuaBuuChinhId", ListType.STRING, "60b87f7ce29af072543360d3");
        params.add(param0);

        Parameters param1 = new Parameters("NhanTrucTiepId", ListType.STRING, "5f8968888fffa53e4c073ded");
        params.add(param1);

        integrationService.setParameters(params);
        mongoTemplate.save(integrationService);
    }

    @ChangeSet(order = "2023-11-03-11-00", id = "HbhLdTbXhChangeLogs::updateConfigLayHsDangKy", author = "haipv")
    public void updateConfigLayHsDangKy(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = mongoTemplate.findById(SERVICE_ID,IntegrationService.class);
        List<Parameters> params = integrationService.getParameters();
        Parameters param0 = new Parameters("LastSync_LayHoSoDangKy", ListType.STRING, "");
        params.add(param0);

        Parameters param1 = new Parameters("EnableSync_LayHoSoDangKy", ListType.BOOLEAN, false);
        params.add(param1);

        integrationService.setParameters(params);
        mongoTemplate.save(integrationService);
    }

    @ChangeSet(order = "2023-11-03-12-00", id = "HbhLdTbXhChangeLogs::updateConfigEformId", author = "haipv")
    public void updateConfigEformId(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = mongoTemplate.findById(SERVICE_ID,IntegrationService.class);
        List<Parameters> params = integrationService.getParameters();
        Parameters param0 = new Parameters("eFormId", ListType.STRING, "");
        params.add(param0);

        integrationService.setParameters(params);
        mongoTemplate.save(integrationService);
    }

    @ChangeSet(order = "2023-11-03-13-00", id = "HbhLdTbXhChangeLogs::updateConfigLayTienTrinhXuLy", author = "haipv")
    public void updateConfigLayTienTrinhXuLy(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = mongoTemplate.findById(SERVICE_ID,IntegrationService.class);
        List<Parameters> params = integrationService.getParameters();
        Parameters param0 = new Parameters("LastSync_LayTienTrinhXuLy", ListType.STRING, "");
        params.add(param0);

        Parameters param1 = new Parameters("EnableSync_LayTienTrinhXuLy", ListType.BOOLEAN, false);
        params.add(param1);

        integrationService.setParameters(params);
        mongoTemplate.save(integrationService);
    }
}
