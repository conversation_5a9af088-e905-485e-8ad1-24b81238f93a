package vn.vnpt.digo.adapter.changelogs;


import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

import java.util.ArrayList;
import java.util.List;

@ChangeLog(order = "2024-05-03-09-20")
public class IntegratedGPLXQNIChangeLogs {
    @ChangeSet(order = "2024-05-03-09-20", id = "IntegratedGPLXQNIChangeLogs::createIntegratedGPLXQNIChangeLogs", author = "hoavh")
    public void createIntegratedGPLXQNIChangeLogs(MongoTemplate mongoTemplate) {
        IntegrationService integrationService = new IntegrationService();
        integrationService.setId(new ObjectId("8fd6dff2346b5a09174671a9"));
        integrationService.setName("Integrated GPLX QNI");
        integrationService.setIntegratedUnit("Integrated GPLX QNI");

        List<Parameters> params = new ArrayList<>();

        // api lấy token
        params.add(new Parameters("gateway-token", ListType.STRING, "https://am.quangngai.gov.vn/token"));

        // 3.2	API lấy thông tin Session
        params.add(new Parameters("session-url", ListType.STRING, "https://am.quangngai.gov.vn/apiDVC-GPLX/1.0/d/api/btttt/ngsp/tcdbdvcdata/mapi/login"));
        // 3.3	API Tra cứu hồ sơ
        params.add(new Parameters("search-detail-url", ListType.STRING, "https://am.quangngai.gov.vn/apiDVC-GPLX/1.0/d/api/btttt/ngsp/tcdbdvcdata/mapi/g"));
        // 3.4	API Lấy Danh sách hồ sơ
        params.add(new Parameters("search-url", ListType.STRING, "https://am.quangngai.gov.vn/apiDVC-GPLX/1.0/d/api/btttt/ngsp/tcdbdvcdata/mapi/g"));
        // 3.5	API Lấy tài liệu hồ sơ
        params.add(new Parameters("search-file-url", ListType.STRING, "https://am.quangngai.gov.vn/apiDVC-GPLX/1.0/d/api/btttt/ngsp/tcdbdvcdata/mapi/g"));

        // service lấy danh sách hồ sơ
        params.add(new Parameters("session-url-service-name", ListType.STRING, "DanhSachHoSo"));
        // service lấy chi tiết hồ sơ
        params.add(new Parameters("session-detail-url-service-name", ListType.STRING, "TraCuuHoSo"));
        // service lấy chi tiết file
        params.add(new Parameters("search-file-url-service-name", ListType.STRING, "LayTaiLieuHoSo"));

        // username
        params.add(new Parameters("username", ListType.STRING, "000.00.29.H48"));
        // password
        params.add(new Parameters("password", ListType.STRING, "000.00.29.H48!20240314#Q@zWsx@12351!!"));
        // username
        params.add(new Parameters("consumer-key", ListType.STRING, "4xpCnx8gp3BnYkd9lq8ywFcJAqoa"));
        // password
        params.add(new Parameters("consumer-secret", ListType.STRING, "TIBE8Y4xTRaJF04fVCICjeFDProa"));

        // password
        params.add(new Parameters("ma-tinh", ListType.STRING, "51"));

        integrationService.setParameters(params);
        integrationService.setStatus(1);

        integrationService.setIgnoreDeployment(true);
        integrationService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(integrationService);
    }
}
