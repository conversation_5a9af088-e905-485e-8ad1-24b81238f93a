/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2021-06-24-11-34")
public class FptPadserviceChangeLogs {
    
    @ChangeSet(order = "2021-06-24-11-35", id = "FptPadserviceChangeLogs::createIntegrationService", author = "mongtt")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService ipccService = new IntegrationService();
        ipccService.setId(new ObjectId("5f7c16069abb62f511890021"));
        ipccService.setName("FPT Public Service");
        ipccService.setIntegratedUnit("FPT");
        List<Parameters> lstParams = new ArrayList<>();
        //gateway
        Parameters sendUrl = new Parameters("gateway", ListType.STRING, "http://motcua-service.tayninh.gov.vn/test/WebServiceMotCua.asmx");
        lstParams.add(sendUrl);
        //key
        Parameters acceptKey = new Parameters("get-dossier-key", ListType.STRING, "Hl82Nk2hvbihflgvhpfh289t%n!8lm2rVbi0jd");
        lstParams.add(acceptKey);
        //key
        Parameters attachmentKey = new Parameters("get-dossier-attachment-key", ListType.STRING, "O2na01319!(jx9je%261bxGWo1mLOjoj1lJsu72f9ni");
        lstParams.add(attachmentKey);
        //attachment-url
        Parameters linkAttachment = new Parameters("attachment-url", ListType.STRING, "http://taphuandvc.tayninh.gov.vn:1008/");
        lstParams.add(linkAttachment);
        //list-hs
        Parameters listHs = new Parameters("list-procedure", ListType.STRING, "[37857, 37858]");
        lstParams.add(listHs);
        //user-id
        Parameters userId = new Parameters("user-id", ListType.STRING, "9573");
        lstParams.add(userId);
        //sso-url
        Parameters ssoUrl = new Parameters("sso-url", ListType.STRING, "https://digo-oidc.vnptigate.vn/auth/realms/digo/protocol/openid-connect/token");
        lstParams.add(ssoUrl);
        //client-id
        Parameters clientId = new Parameters("client-id", ListType.STRING, "svc-adapter");
        lstParams.add(clientId);
        //client-secret
        Parameters clientSecret = new Parameters("client-secret", ListType.STRING, "1baa13a3-af2e-435a-a45b-7068d6d05972");
        lstParams.add(clientSecret);
        
        ipccService.setParameters(lstParams);
        ipccService.setStatus(1);
        try {
            ipccService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2021-06-24T11:33:44.165+0100"));
            ipccService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2021-06-24T11:33:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            ipccService.setCreatedDate(new Date());
            ipccService.setUpdatedDate(new Date());
        }
        ipccService.setIgnoreDeployment(true);
        ipccService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(ipccService);
    }
}
