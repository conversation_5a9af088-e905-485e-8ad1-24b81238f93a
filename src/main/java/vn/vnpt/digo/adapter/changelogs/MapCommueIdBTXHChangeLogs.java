package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.MappingType;
import vn.vnpt.digo.adapter.pojo.MappingTypeDataAccess;
import vn.vnpt.digo.adapter.pojo.MappingTypeTrans;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ChangeLog(order = "2023-02-24-01-01")
public class MapCommueIdBTXHChangeLogs {
    @ChangeSet(order = "2023-02-24-01-01", id = "MapCommueIdBTXHChangeLogs::createMapCommueIdBTXHMapping", author = "phuongtnd")
    public void createMapCommueIdBTXHMapping(MongoTemplate mongoTemplate) {
        MappingType national = new MappingType();
        national.setId(new ObjectId("5fc07a5d92b5c38bef9e5f04"));

        MappingTypeTrans vi = new MappingTypeTrans((short)228, "Xã/Phường BTXH", "Mapping danh mục Xã/Phường BTXH");
        MappingTypeTrans en = new MappingTypeTrans((short)46, "Commue Social Protection", "Mapping commue Social Protection");
        List<MappingTypeTrans> trans = new ArrayList<>();
        trans.add(vi);
        trans.add(en);
        national.setTrans(trans);

        MappingTypeDataAccess source = new MappingTypeDataAccess();
        source.setEnable(Boolean.TRUE);
        source.setEndpoint("");
        national.setSourceDataAccess(source);

        MappingTypeDataAccess dest = new MappingTypeDataAccess();
        source.setEnable(Boolean.FALSE);
        national.setDestDataAccess(dest);

        national.setStatus(1);

        try {
            national.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-22T10:47:44.165+0100"));
            national.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2023-02-22T10:47:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            national.setCreatedDate(new Date());
            national.setUpdatedDate(new Date());
        }

        national.setIgnoreDeployment(true);

        national.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));

        mongoTemplate.insert(national);
    }
}