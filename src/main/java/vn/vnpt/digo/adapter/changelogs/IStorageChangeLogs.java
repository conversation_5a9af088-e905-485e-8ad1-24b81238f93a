package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.core.env.Environment;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@ChangeLog(order = "2024-09-01-08-01")
public class IStorageChangeLogs {
    @ChangeSet(order = "2024-09-01-08-01", id = "IStorageChangeLogs::createIStorageParams", author = "tuqn")
    public void createIStorageParams(MongoTemplate mongoTemplate, Environment environment) {
        ObjectId deploymentId = new ObjectId(environment.getProperty("digo.microservice.deployment.id"));
        IntegrationService service = new IntegrationService();
        service.setId(new ObjectId("5f7c16069abb62f511890045"));
        service.setName("iStorage");
        service.setIntegratedUnit("iStorage");
        List<Parameters> params = new ArrayList<>();

        Parameters url = new Parameters("domain", ListType.STRING, "https://dev-vpdt.vnptioffice.vn/qlvb");
        params.add(url);

        Parameters grantType = new Parameters("password", ListType.STRING, "fd4197a2-7bf4-4635-9bee-89156d78fabf");
        params.add(grantType);

        Parameters username = new Parameters("username", ListType.STRING, "dannt6");
        params.add(username);

        Parameters xAuthenticationToken = new Parameters("x-authentication-token", ListType.STRING, "3682e3f32d3f0d45b4a5bf2cf4e8d7ee");
        params.add(xAuthenticationToken);

        service.setParameters(params);
        service.setStatus(1);
        service.setIgnoreDeployment(true);
        mongoTemplate.save(service);

//        IntegratedConfiguration config = new IntegratedConfiguration();
//        IntegratedService integratedService = new IntegratedService(service);
//        config.setService(integratedService);
//        config.setParameters(params);
//        config.setDeploymentId(deploymentId);
//        config.setStatus(1);
//        config.setDeleted(false);
////        config.setId(new ObjectId("5fc704bc86940eab2fb45998"));
//        config.setName("iStorage");
//        config.setApplyAll(true);
//
//        // Set subsystem
//        Subsystem subSystem = new Subsystem();
//        subSystem.setId(new ObjectId("5f7c16069abb62f511880003"));
//        SubsystemTrans subSystemTrans = new SubsystemTrans();
//        subSystemTrans.setLanguageId((short)228);
//        subSystemTrans.setName("VNPT iGate 2.0");
//        subSystem.setTrans(Arrays.asList(subSystemTrans));
//        config.setSubsystem(Arrays.asList(subSystem));
//
//        // Set tag
//        Tag tag = new Tag();
//        TransTag tagTrans = new TransTag();
//        tag.setId(new ObjectId("62fdab49d13be03598afb8fc"));
//        tagTrans.setLanguageId((short)228);
//        tagTrans.setName("Nhóm chức năng liên thông");
//        tag.setTrans(Arrays.asList(tagTrans));
//        config.setTag(tag);
//        mongoTemplate.save(config);
    }
}
