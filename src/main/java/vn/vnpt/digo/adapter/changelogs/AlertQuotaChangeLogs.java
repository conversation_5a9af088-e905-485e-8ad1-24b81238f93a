package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import com.mongodb.BasicDBObject;
import com.mongodb.DB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ChangeLog(order = "2023-05-30-09-44")
public class AlertQuotaChangeLogs {

    private final Logger logger = LoggerFactory.getLogger(AlertQuotaChangeLogs.class);
    @ChangeSet(order = "2023-05-30-09-44", id = "AlertQuotaChangeLogs::createIndex1", author = "tuqn")
    public void createIndex1(DB db) {
        try {
            db.getCollection("alertQuota").createIndex(new BasicDBObject("target",1));
            db.getCollection("alertQuota").createIndex(new BasicDBObject("function", 1));
        } catch (Exception e) {
            logger.error("createIndex1 - Exception: " + e.getMessage());
        }
    }
    
    @ChangeSet(order = "2023-06-03-17-44", id = "AlertQuotaChangeLogs::createIndex2", author = "nghialt")
    public void createIndex2(DB db) {
        try {
            db.getCollection("alertQuota").createIndex(new BasicDBObject("target", 1).append("function", 1), new BasicDBObject("unique", true));
        } catch (Exception e) {
            logger.error("createIndex2 - Exception: " + e.getMessage());
        }
    }
}
