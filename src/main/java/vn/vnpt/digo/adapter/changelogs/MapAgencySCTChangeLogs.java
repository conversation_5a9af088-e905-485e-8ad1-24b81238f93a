/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.MappingType;
import vn.vnpt.digo.adapter.pojo.MappingTypeDataAccess;
import vn.vnpt.digo.adapter.pojo.MappingTypeTrans;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ChangeLog(order = "2022-08-10-09-21")
public class MapAgencySCTChangeLogs {
    @ChangeSet(order = "2022-08-10-09-21", id = "MapAgencySCTChangeLogs::create", author = "nghiadd.bdg")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        MappingType national = new MappingType();
        national.setId(new ObjectId("5fc0707a62681a8bef001001"));
        
        MappingTypeTrans vi = new MappingTypeTrans((short)228, "Cơ quan, đơn vị SCT SCT", "Mapping danh mục Cơ quan, đơn vị SCT");
        MappingTypeTrans en = new MappingTypeTrans((short)46, "Agency SCT", "Mapping Agency SCT");
        List<MappingTypeTrans> trans = new ArrayList<>();
        trans.add(vi);
        trans.add(en);
        national.setTrans(trans);
        
        MappingTypeDataAccess source = new MappingTypeDataAccess();
        source.setEnable(Boolean.TRUE);
        source.setEndpoint("");
        national.setSourceDataAccess(source);
        
        MappingTypeDataAccess dest = new MappingTypeDataAccess();
        source.setEnable(Boolean.FALSE);
        national.setDestDataAccess(dest);
        
        national.setStatus(1);
        
        try {
            national.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2022-08-10T10:33:44.165+0100"));
            national.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2022-08-10T10:33:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            national.setCreatedDate(new Date());
            national.setUpdatedDate(new Date());
        }
        
        national.setIgnoreDeployment(true);
        
        national.setDeploymentId(new ObjectId("70402431383b57f1a3f31979"));
        
        mongoTemplate.insert(national);
    }
}
