/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2021-08-10-15-40")
public class UbmttqChangeLogs {

    @ChangeSet(order = "2021-08-10-15-41", id = "UbmttqChangeLogs::createUbmttqIntegration", author = "mongtt")
    public void createIntegrationService(MongoTemplate mongoTemplate) {
        IntegrationService ipccService = new IntegrationService();
        ipccService.setId(new ObjectId("5f7c16069abb62f511890024"));
        ipccService.setName("UBMTQG HCM");
        ipccService.setIntegratedUnit("Ủy ban mặt trận tổ quốc HCM");
        List<Parameters> lstParams = new ArrayList<>();
        //gateway
        Parameters sendUrl = new Parameters("gateway", ListType.STRING, "https://safeid-external.oneicc.vn");
        lstParams.add(sendUrl);
        //username
        Parameters apiuser = new Parameters("username", ListType.STRING, "hotline1202");
        lstParams.add(apiuser);
        //password
        Parameters apipass = new Parameters("password", ListType.STRING, "ed932927");
        lstParams.add(apipass);
        //login path
        Parameters loginPath = new Parameters("login-path", ListType.STRING, "/api/Authen/m/login");
        lstParams.add(loginPath);
        //add ticket path
        Parameters addticketPath = new Parameters("add-ticket-path", ListType.STRING, "/api/Document/ticket/add");
        lstParams.add(addticketPath);
        //logout path
        Parameters logoutPath = new Parameters("logout-path", ListType.STRING, "/api/Authen/logout");
        lstParams.add(logoutPath);
        //source-code
        Parameters sourceCode = new Parameters("source-code", ListType.STRING, "HOTLINE1022");
        lstParams.add(sourceCode);
        //command-style
        Parameters commandStyle = new Parameters("command-style", ListType.INTEGER, 0);
        lstParams.add(commandStyle);
        //command-id
        Parameters commandId = new Parameters("command-id", ListType.STRING, "string");
        lstParams.add(commandId);
        
        ipccService.setParameters(lstParams);
        ipccService.setStatus(1);
        try {
            ipccService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-10-20T10:33:44.165+0100"));
            ipccService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-10-20T10:33:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            ipccService.setCreatedDate(new Date());
            ipccService.setUpdatedDate(new Date());
        }
        ipccService.setIgnoreDeployment(true);
        ipccService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(ipccService);
    }
}
