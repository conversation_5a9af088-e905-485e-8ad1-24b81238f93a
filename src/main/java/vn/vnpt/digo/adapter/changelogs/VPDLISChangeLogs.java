package vn.vnpt.digo.adapter.changelogs;

import com.github.mongobee.changeset.ChangeLog;
import com.github.mongobee.changeset.ChangeSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import com.mongodb.client.result.UpdateResult;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;

/**
 *
 * <AUTHOR>
 */
@ChangeLog(order = "2022-09-19-10-22")
public class VPDLISChangeLogs {

    @ChangeSet(order = "2022-09-19-10-22", id = "VPDLISChangeLogs::createMigrateTichHop", author = "khadd")
    public void createMigrateTichHop(MongoTemplate mongoTemplate) {
        IntegrationService nationalPublicService = new IntegrationService();
        nationalPublicService.setId(new ObjectId("5f7c16069abb62f511890038"));
        nationalPublicService.setName("VBDLIS Service");
        nationalPublicService.setIntegratedUnit("VBDLIS Service");
        List<Parameters> lstParams = new ArrayList<>();

        Parameters tokenUrl = new Parameters("token-url", ListType.STRING, "https://api.ngsp.gov.vn/token");
        lstParams.add(tokenUrl);

        Parameters consumerKey = new Parameters("consumer-key", ListType.STRING, "n_iA4SYv1Sxcd7T8tL8GDp33odsa");
        lstParams.add(consumerKey);

        Parameters consumerSecret = new Parameters("consumer-secret", ListType.STRING, "o6EhXLU5NJRcDwcdti37OOU_WFUa");
        lstParams.add(consumerSecret);
        
        Parameters tokenValue = new Parameters("token-value", ListType.STRING, "");
        lstParams.add(tokenValue);

        Parameters tiepNhanHoSoUrl = new Parameters("tiep-nhan-ho-so-url", ListType.STRING, "https://am.quangngai.gov.vn/vbdlis/1.0.0/hosomotcua/tiepnhan");
        lstParams.add(tiepNhanHoSoUrl);

        Parameters capNhatKetQuaUrl = new Parameters("cap-nhat-ket-qua-url", ListType.STRING, "https://am.quangngai.gov.vn/vbdlis/1.0.0/hosomotcua/capnhattrangthaitraketquahoso");
        lstParams.add(capNhatKetQuaUrl);
        
        Parameters capNhatTrangThaiBoSungUrl = new Parameters("cap-nhat-trang-thai-bo-sung-url", ListType.STRING, "https://am.quangngai.gov.vn/vbdlis/1.0.0/hosomotcua/capnhattrangthaibosunghoso");
        lstParams.add(capNhatTrangThaiBoSungUrl);
        
        Parameters thongBaoUrl = new Parameters("thong-bao-url", ListType.STRING, "https://am.quangngai.gov.vn/vbdlis/1.0.0/hosomotcua/thongbao");
        lstParams.add(thongBaoUrl);
        
        Parameters phanHoiSaiKetQuaUrl = new Parameters("phan-hoi-sai-ket-qua-url", ListType.STRING, "https://am.quangngai.gov.vn/vbdlis/1.0.0/hosomotcua/phanhoihososaiketqua");
        lstParams.add(phanHoiSaiKetQuaUrl);                
        
        Parameters dossierTaskStatusWaitWithHadresult = new Parameters("dossier-task-status-wait-with-had-result-id", ListType.STRING, "6206911d677d1a2e1527bcb3");
        lstParams.add(dossierTaskStatusWaitWithHadresult);       
        
        Parameters dossierMenuTaskRemindWithHadresult = new Parameters("dossier-menu-task-remind-with-had-result-id", ListType.STRING, "621de31f63bc5e78e3944502");
        lstParams.add(dossierMenuTaskRemindWithHadresult);  
        
        Parameters dossierTaskStatusWaitWithdrawn = new Parameters("dossier-task-status-wait-withdrawn-id", ListType.STRING, "6151c771ba2a04299f949875");
        lstParams.add(dossierTaskStatusWaitWithdrawn);  
        
        Parameters dossierMenuTaskRemindWithdrawalRequest = new Parameters("dossier-menu-task-remind-withdrawal-request-id", ListType.STRING, "6147e72bebfba925f1e89bf4");
        lstParams.add(dossierMenuTaskRemindWithdrawalRequest);
        
        Parameters dossierTaskStatusWaitWithCancle = new Parameters("dossier-task-status-wait-with-cancle-id", ListType.STRING, "61ee30eada2d36b037e00004");
        lstParams.add(dossierTaskStatusWaitWithCancle);
        
        Parameters dossierMenuTaskRemindWithCancle = new Parameters("dossier-menu-task-remind-with-cancle-id", ListType.STRING, "61ee30eada2d36b037e00004");
        lstParams.add(dossierMenuTaskRemindWithCancle);
        
        Parameters linkFileVbdlis = new Parameters("link-file-vbdlis", ListType.STRING, "https://dt.mplis.gov.vn/dc/Handlers/PdfHandler.ashx?DocId=");
        lstParams.add(linkFileVbdlis);

        Parameters agencyId = new Parameters("agency-id", ListType.STRING, "");
        lstParams.add(agencyId);
        
        nationalPublicService.setParameters(lstParams);
        nationalPublicService.setStatus(1);
        try {
            nationalPublicService.setCreatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2022-08-17T10:47:44.165+0100"));
            nationalPublicService.setUpdatedDate(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").parse("2020-08-17T10:47:44.165+0100"));
        } catch (ParseException | NullPointerException e) {
            nationalPublicService.setCreatedDate(new Date());
            nationalPublicService.setUpdatedDate(new Date());
        }
        nationalPublicService.setIgnoreDeployment(true);
        nationalPublicService.setDeploymentId(new ObjectId("5ee091507d567c9fe29f82fa"));
        mongoTemplate.insert(nationalPublicService);
    } 

    @ChangeSet(order = "2022-12-05-16-35", id = "VBDLISchangeLogs::updateIntegrationServiceVBDLIS", author = "thuannm")
    public void updateIntegrationServiceVBDLIS(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(new ObjectId("5f7c16069abb62f511890038")));

        Parameters tokenValue = new Parameters("token-value", ListType.STRING, "");
        
        Update update = new Update();
        update.addToSet("parameters").each(tokenValue);
        UpdateResult updatedConnectorType = mongoTemplate.updateMulti(query, update, IntegrationService.class);
        System.out.println("updatedConnectorType" + updatedConnectorType);
        
    }

    @ChangeSet(order = "2022-12-05-16-35", id = "VBDLISchangeLogs::updateIntegrationConfigVBDLIS", author = "thuannm")
    public void updateIntegrationConfigVBDLIS(MongoTemplate mongoTemplate){
        IntegrationService service = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511890038"),IntegrationService.class);
        List<Parameters> params = service.getParameters();
        params = params.stream().filter(i->i.getKey() != "timestamp").collect(Collectors.toList());
        service.setParameters(params);
        mongoTemplate.save(service);
        List<IntegratedConfiguration> configs = mongoTemplate.find(new Query().addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890038"))),IntegratedConfiguration.class);
        for (IntegratedConfiguration config:configs) {
            params = config.getParameters();
            params = params.stream().filter(i->i.getKey() != "timestamp").collect(Collectors.toList());
            config.setParameters(params);
            mongoTemplate.save(config);
        }
    }

    @ChangeSet(order = "2023-02-28-14-30", id = "VBDLISchangeLogs::updateIntegrationServiceVBDLIS1", author = "thuannm")
    public void updateIntegrationServiceVBDLIS1(MongoTemplate mongoTemplate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(new ObjectId("5f7c16069abb62f511890038")));

        Parameters tokenValue = new Parameters("timesheet", ListType.STRING, "");

        Update update = new Update();
        update.addToSet("parameters").each(tokenValue);
        UpdateResult updatedConnectorType = mongoTemplate.updateMulti(query, update, IntegrationService.class);
        System.out.println("updatedConnectorType" + updatedConnectorType);

    }

    @ChangeSet(order = "2023-02-28-14-35", id = "VBDLISchangeLogs::updateIntegrationConfigVBDLIS1", author = "thuannm")
    public void updateIntegrationConfigVBDLIS1(MongoTemplate mongoTemplate){
        IntegrationService service = mongoTemplate.findById(new ObjectId("5f7c16069abb62f511890038"),IntegrationService.class);
        List<Parameters> params = service.getParameters();
        params = params.stream().filter(i->i.getKey() != "timestamp").collect(Collectors.toList());
        service.setParameters(params);
        mongoTemplate.save(service);
        List<IntegratedConfiguration> configs = mongoTemplate.find(new Query().addCriteria(Criteria.where("service.id").is(new ObjectId("5f7c16069abb62f511890038"))),IntegratedConfiguration.class);
        for (IntegratedConfiguration config:configs) {
            params = config.getParameters();
            params = params.stream().filter(i->i.getKey() != "timestamp").collect(Collectors.toList());
            config.setParameters(params);
            mongoTemplate.save(config);
        }
    }
}
