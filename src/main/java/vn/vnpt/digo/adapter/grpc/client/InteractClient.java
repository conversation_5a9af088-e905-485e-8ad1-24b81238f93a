package vn.vnpt.digo.adapter.grpc.client;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.grpc.auth.BearerToken;
import vn.vnpt.digo.adapter.grpc.util.GrpcMicroservice;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.igate.Interact.InteracterGrpc;
import vn.vnpt.igate.Interact.Reply;
import vn.vnpt.igate.Interact.Request;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class InteractClient {

    @Autowired
    private GrpcMicroservice grpcMicroservice;

    public Object getDossierData(String requestParam) {
        String shortUri = "/api-integration/--data" + (requestParam != null ? "?" + requestParam : "");
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(shortUri);
        String response = grpcMicroservice.padmanUri(uriBuilder, "GET");
        System.out.println(response);

        return GsonUtils.copyObject(response);
    }

}