package vn.vnpt.digo.adapter.grpc.preprocessor;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponents;
import vn.vnpt.digo.adapter.grpc.util.UriHelper;
import vn.vnpt.digo.adapter.service.GRPCSample.GRPCSampleService;
import vn.vnpt.digo.adapter.util.GsonUtils;

import java.util.Date;

@Component
public class GRPCSamplePreprocess {
    Logger logger = LoggerFactory.getLogger(GRPCSamplePreprocess.class);

    @Autowired
    private GRPCSampleService grpcSample;

    public String handleInput(UriComponents uriComponents, String requestBody, String httpMethod) {
        if (httpMethod.equals("GET") && uriComponents.getPath().contains("/sample/test-grpc-adapter")) {
            return testGRPCSample(uriComponents);
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timestamp", new Date());
        jsonObject.put("status", 404);
        jsonObject.put("error", "Not Found");
        jsonObject.put("message", "Không tìm thấy API");
        jsonObject.put("path", uriComponents.getPath());

        return jsonObject.toString();
    }

    private String testGRPCSample(UriComponents uriComponents){
        String input = UriHelper.getStringParam(uriComponents,"input");
        try {
            String result = grpcSample.testGRPCSample(input);

            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(result);

            return jsonString;
        } catch (Exception e) {
            return GsonUtils.getJson(e);
        }
    }
}
