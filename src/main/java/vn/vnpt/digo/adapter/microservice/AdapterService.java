package vn.vnpt.digo.adapter.microservice;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.Microservice;

import java.util.HashMap;
import java.util.Map;

@Service
public class AdapterService {
    @Autowired
    private Microservice microservice;

    private static RestTemplate restTemplate = new RestTemplate();

    Logger logger = LoggerFactory.getLogger(AdapterService.class);

    public Map<String, Object> getAPICauHinh(String domainAPI, HashMap<String, Object> jsonPayload) {
        System.out.println(jsonPayload);
        Map<String, Object> responseMap  = new HashMap<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            String token = Context.getJwtAuthenticationTokenValue();

            UriComponentsBuilder uriBuilder = microservice.adapterUri(domainAPI);
//            uriBuilder.uri(URI.create("http://localhost:8083/service/judicial-records/--update-list-status"));
            UriComponents uriComponents = uriBuilder.build();


            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<?> request = new HttpEntity<>(headers);
            if (jsonPayload != null && !jsonPayload.isEmpty()){
                request = new HttpEntity<>(jsonPayload, headers);
            }
            logger.info("url-api: " + uriComponents.toUriString());
            logger.info("request-api: " + request);

            ResponseEntity<Map> result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST,
                    request,
                    Map.class
            );

            logger.info("result: " + result);
            responseMap = result.getBody();

            return responseMap;
        } catch (Exception e) {
            responseMap.put("statusCode",500);
            responseMap.put("message", e.getMessage());
            return responseMap;
        }
    }
}
