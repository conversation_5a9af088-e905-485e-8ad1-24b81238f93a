package vn.vnpt.digo.adapter.microservice;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.dto.dvclt.DVCLTHoTichProducerDataDto;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Oauth2RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.net.URI;
import java.util.Objects;

@Service
public class PadmanService {
    @Autowired
    private Microservice microservice;

    @Value(value = "${digo.http.dvclt.new.enable}")
    private Boolean httpDvcltNewEnable;

    @Autowired
    private Oauth2RestTemplate oauth2RestTemplate;

    private static RestTemplate restTemplate = new RestTemplate();

    Logger logger = LoggerFactory.getLogger(PadmanService.class);

    public Map<String, Object> setConfigDossier(Map<String, Object> jsonPayload, String padmanUrl, String adapterUrl) {
//        System.out.println(jsonPayload);
        Map<String, Object> responseMap  = new HashMap<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            String token = null;
            if (jsonPayload.containsKey("token")){
                token = jsonPayload.get("token").toString();
            }else {
                token =  Context.getJwtAuthenticationTokenValue();
            }
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.newInstance();


            if (padmanUrl != null && !padmanUrl.isEmpty()) {
                uriBuilder = microservice.padmanUri(padmanUrl);
            }
            if (adapterUrl != null && !adapterUrl.isEmpty()){
                uriBuilder = microservice.adapterUri(adapterUrl);
            }
//            uriBuilder.uri(URI.create("http://localhost:8081/service/environmental-resources/--new-dossier?option=6735b5c9431f1b0019aeaeb9-5f86700730ea36001c3825f9"));
            UriComponents uriComponents = uriBuilder.build();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(token);
            RequestEntity<?> request = RequestEntity
                    .post(uriComponents.toUri())
                    .headers(headers)
                    .body(jsonPayload);
            logger.info("url: " + uriComponents.toUriString());
            logger.info("request: " + request);
            String url = uriComponents.toUriString();
//            if (url.contains("https://apitest.vnptigate.vn")) {
//                url = url.replace("https://apitest.vnptigate.vn", "https://apidvc.dongnai.gov.vn");
//            }
            ResponseEntity<Map> result = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    request,
                    Map.class
            );

            logger.info("result: " + result);
            responseMap = result.getBody();

            return responseMap;
        } catch (Exception e) {
            responseMap.put("statusCode",500);
            responseMap.put("message", e.getMessage());
            return responseMap;
        }
    }


    public Map<String, Object> getToken(HashMap<String, Object> jsonPayload) {
        System.out.println(jsonPayload);
        Map<String, Object> responseMap  = new HashMap<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            UriComponentsBuilder uriBuilder = microservice.padmanUri("/v2/getToken");
//            uriBuilder.uri(URI.create("http://localhost:8083/service/judicial-records/--update-list-status"));
            UriComponents uriComponents = uriBuilder.build();


            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<?> request = new HttpEntity<>(jsonPayload, headers);

            logger.info("url: " + uriComponents.toUriString());
            logger.info("request: " + request);

            ResponseEntity<Map> result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST,
                    request,
                    Map.class
            );

            logger.info("result: " + result);
            responseMap = result.getBody();

            return responseMap;
        } catch (Exception e) {
            logger.info("Failed to AdapterService.sendLLTPNewVNeID: " + e.getMessage());
            responseMap.put("statusCode",500);
            responseMap.put("message", e.getMessage());
            return responseMap;
        }
    }

    public Map<String, Object> capMaSoHoSo(HashMap<String, Object> jsonPayload) {
        System.out.println(jsonPayload);
        Map<String, Object> responseMap  = new HashMap<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            String token = Context.getJwtAuthenticationTokenValue();

            UriComponentsBuilder uriBuilder = microservice.padmanUri("/v2/lyLichTuPhap/capMaHoSo");
//            uriBuilder.uri(URI.create("http://localhost:8083/service/judicial-records/--update-list-status"));
            UriComponents uriComponents = uriBuilder.build();


            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(jsonPayload, headers);

            logger.info("url: " + uriComponents.toUriString());
            logger.info("request: " + request);

            ResponseEntity<Map> result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST,
                    request,
                    Map.class
            );

            logger.info("result: " + result);
            responseMap = result.getBody();

            return responseMap;
        } catch (Exception e) {
            responseMap.put("statusCode",500);
            responseMap.put("message", e.getMessage());
            return responseMap;
        }
    }

    public Map<String, Object> nhanHoSo(HashMap<String, Object> jsonPayload) {
        Map<String, Object> responseMap  = new HashMap<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            String token = Context.getJwtAuthenticationTokenValue();

            UriComponentsBuilder uriBuilder = microservice.padmanUri("/v2/lyLichTuPhap/nhanHoSo");
//            uriBuilder.uri(URI.create("http://localhost:8083/service/judicial-records/--update-list-status"));
            UriComponents uriComponents = uriBuilder.build();


            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(jsonPayload, headers);

            logger.info("url: " + uriComponents.toUriString());
            logger.info("request: " + request);

            ResponseEntity<Map> result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST,
                    request,
                    Map.class
            );

            logger.info("result: " + result);
            responseMap = result.getBody();
            return responseMap;
        } catch (Exception e) {
            responseMap.put("statusCode",500);
            responseMap.put("message", e.getMessage());
            return responseMap;
        }
    }

    public Map<String, Object> nhanHoSoDVCLT(DVCLTHoTichProducerDataDto jsonPayload) {
        Map<String, Object> responseMap  = new HashMap<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            OAuth2RestTemplate template = oauth2RestTemplate.getToken();
            String token = template.getAccessToken().toString();
            UriComponentsBuilder uriBuilder = microservice.padmanUri("/dvclt-dossier/nhanHoSoNew");
//            uriBuilder.uri(URI.create("http://localhost:8083/service/judicial-records/--update-list-status"));
            UriComponents uriComponents = uriBuilder.build();


            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(jsonPayload, headers);

            logger.info("url: " + uriComponents.toUriString());
            logger.info("request: " + request);

            ResponseEntity<Map> result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST,
                    request,
                    Map.class
            );

            logger.info("result: " + result);
            responseMap = result.getBody();
            return responseMap;
        } catch (Exception e) {
            responseMap.put("statusCode",500);
            responseMap.put("message", e.getMessage());
            return responseMap;
        }
    }

}
