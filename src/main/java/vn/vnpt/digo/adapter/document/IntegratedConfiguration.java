package vn.vnpt.digo.adapter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.kha.IntegratedConfigurationKHAExtend;
import vn.vnpt.digo.adapter.pojo.Agency;
import vn.vnpt.digo.adapter.pojo.IntegratedService;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.pojo.Parameters;
import vn.vnpt.digo.adapter.pojo.ParametersType;
import vn.vnpt.digo.adapter.pojo.Subsystem;
import vn.vnpt.digo.adapter.pojo.Tag;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "integratedConfiguration")
public class IntegratedConfiguration implements Serializable {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String name;

    private IntegratedService service;

    private List<Subsystem> subsystem;

    private Tag tag;

    private List<Parameters> parameters;

    private Boolean applyAll;

    private List<Agency> applyAgencies;

    private List<Agency> exceptAgencies;

    private IntegratedConfigurationKHAExtend extendKHA;

    private ObjectId file;

    private Integer status;

    private Boolean deleted;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

    private Boolean ignoreDeployment;
    
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;

    private String code;

    private List<String> encryptedFieldList = new ArrayList<>();

    public void setParametersValue(String key, String value, ParametersType type) {
        for (Parameters item : this.parameters) {
            if (Objects.equals(key, item.getKey())) {
                if (Objects.equals(item.getType(), type)){
                    if (Objects.equals(item.getType(), ListType.INTEGER)) {
                        item.setIntegerValue(Integer.parseInt(value));
                    } else if (Objects.equals(item.getType(), ListType.DOUBLE)) {
                        item.setDoubleValue(Double.parseDouble(value));
                    } else if (Objects.equals(item.getType(), ListType.BOOLEAN)) {
                        item.setBooleanValue(Boolean.parseBoolean(value));
                    } else {
                        item.setOriginValue(value.toString());
                    }
                }
            }
        }
    }
}
