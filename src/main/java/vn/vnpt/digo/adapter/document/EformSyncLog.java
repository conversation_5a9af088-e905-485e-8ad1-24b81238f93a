package vn.vnpt.digo.adapter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "eformSyncLog")
public class EformSyncLog {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String proceduceCode;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId commonEformId;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId detailEformId;
}
