package vn.vnpt.digo.adapter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "NBRSLog")
public class NBRSLog {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private int status;  // 0 = fail, 1 = sucess
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updateDate = new Date();
    private String dossierAcceptedDay;
    private HashMap<String, ServiceLog> serviceLogs;
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ServiceLog {
        private int status; // 0 = fail, 1 = processing, 2 = success
        private List<ProcessLog> processLogs;
        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class ProcessLog {
            private String id;
            private int status; // 0 = fail, 1 = sucess
            private String errMsg= "";
        }
    }
}
