package vn.vnpt.digo.adapter.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "integratedGetBillEvent")
public class IntegratedGetBillEvent implements Serializable {

    private ObjectId id;
    private Integer status = 0; //0: created, 1: processing, 2: completed
    private Date createdDate = new Date();
    private Date updatedDate = new Date();
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId configId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subsystemId;
    @JsonProperty("MaThamChieu")
    private String maThamChieu;
    @JsonProperty("ThoiGianGD")
    private String thoiGianGD;
    private String message = "";
}
