/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "lgspDLKLog")
public class LGSPDLKLog implements Serializable {
    @Id
    private ObjectId id;
    
    @NotNull    
    @JsonProperty("APIFunction")
    private String APIFunction;
    
    @NotNull
    @JsonProperty("DossierCode")
    private String DossierCode;
    
    @NotNull
    @JsonProperty("AgencyCode")
    private String AgencyCode;
    
    @NotNull
    @JsonProperty("CallTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date CallTime;
    
    @NotNull
    @JsonProperty("Result")
    private Boolean Result;
   
    @NotNull
    @JsonProperty("ResponseBody")
    private String ResponseBody;
    
    @JsonProperty("RequestBody")
    private String RequestBody;
    
    @NotNull
    @JsonProperty("RequestID")
    private String RequestID;
    
    @NotNull
    @JsonProperty("ApiType")
    private String ApiType;
    
    @NotNull
    @JsonProperty("ACK")
    private String ACK;
}
