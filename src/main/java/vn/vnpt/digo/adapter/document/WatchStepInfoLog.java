package vn.vnpt.digo.adapter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;

import java.util.Date;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "watchStepInfoLog")
public class WatchStepInfoLog {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private IdCodeNameSimpleDto item;

    private String className;

    private String functionName;

    private Map<String, Object> param;

    private Map<String, Object> body;

    private String response;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createDate;

    public WatchStepInfoLog(IdCodeNameSimpleDto item, String className, String functionName, Map<String, Object> param, Map<String, Object> body, String response){
        this.item = item;
        this.className = className;
        this.functionName = functionName;
        this.param = param;
        this.body = body;
        this.response = response;
        this.createDate = new Date();
    }

}
