package vn.vnpt.digo.adapter.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Document(collection = "dossierSyncGTVT")
public class DossierSyncGTVT implements Serializable {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String maDangKyCu;
    private String maDangKyDvc;
    private String maHoSo;
    private String hoTen;
    private String hoTenIn;
    private String hoDem;
    private String ten;
    private String gioiTinh;
    private String ngaySinh;
    private String quocTich;
    private String soCmndCu;
    private String soCmnd;
    private String noiCapCmnd;
    private String ngayCapCmnd;
    private String noiCuTru;
    private String dvhcNoiCuTru;
    private String noiThuongTru;
    private String dvhcNoiThuongTru;
    private String anhChanDung;
    private String maDonVi;
    private String ngayNhanHoSo;
    private String nguoiNhanHoSo;
    private String ngayHenTraGplx;
    private String loaiHoSo;
    private String noiHocLaiXe;
    private String namHocLaiXe;
    private String hangDaoTao;
    private String soNamLaiXe;
    private String soKmLxAnToan;
    private String lyDoDoiGplx;
    private String mucDichDoiGplx;
    private String maDonViVpdk;
    private ArrayList<vn.vnpt.digo.adapter.dto.lgsphcm.GPLXDanhSachHoSoOutputDto.GplxDaCap> gplxDaCap;
    private ArrayList<vn.vnpt.digo.adapter.dto.lgsphcm.GPLXDanhSachHoSoOutputDto.HoSoDinhKem> hoSoDinhKem;
    private String strGplxDaCap;
    private DetailDossierGTVT chiTietHoSo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GplxDaCap {
        private String sogplx;
        private String soserigplx;
        private String noicapgplx;
        private String ngaycapgplx;
        private String ngayhethangplx;
        private String hanggplxgplx;
        private String ngaytrungtuyen;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HoSoDinhKem{
        private String magiayto;
        private String tengiayto;
        private String filedinhkem;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetailDossierGTVT {
        @JsonProperty("MaHoSo")
        private String MaHoSo;

        @JsonProperty("MaTTHC")
        private String MaTTHC;

        @JsonProperty("MaDVC")
        private String MaDVC;

        @JsonProperty("TenDVC")
        private String TenDVC;

        @JsonProperty("HoTenNguoiNop")
        private String HoTenNguoiNop;

        @JsonProperty("MaTrangThai")
        private String MaTrangThai;

        @JsonProperty("TrangThaiHoSo")
        private String TrangThaiHoSo;

        @JsonProperty("DonViXuLy")
        private String DonViXuLy;

        @JsonProperty("MaDonViXuLy")
        private String MaDonViXuLy;

        @JsonProperty("NgayNop")
        private String NgayNop;

        @JsonProperty("NgayTiepNhan")
        private String NgayTiepNhan;

        @JsonProperty("NgayHenTra")
        private String NgayHenTra;

        @JsonProperty("HinhThucNhanKQ")
        private String HinhThucNhanKQ;
    }

}
