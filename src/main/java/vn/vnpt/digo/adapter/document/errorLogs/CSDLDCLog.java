package vn.vnpt.digo.adapter.document.errorLogs;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.pojo.AgencyCodeName;
import vn.vnpt.digo.adapter.util.GsonUtils;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "csdldcLog")
public class CSDLDCLog {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String identityNumber;
    private String fullname;
    private Object request;
    private Object response;
    private String sendUrl;
    private String type;
    private String bodyContent;
    private String token;
    private String officerUserName;
    private String officerId;
    private Integer status;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    private Integer logHour;
    private Integer logDayOfWeek;
    private Boolean isSpam = false;
    private String birthday;
    private Boolean isLogV2;
    private AgencyCodeName agency;
    private String officerName;
    private String ipAddress;
    private String menu;
    private String menuCode;
    private String menuUrl;
    private List<String> actions;
    private String dossierCode;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId dossierId;

    public void setBodyContent () {
        this.bodyContent = null;
        this.secure();
    }

    public void secure(){
        try {
            if (identityNumber != null && !identityNumber.isBlank() && identityNumber.length() > 4) {
                int lengthToHide = identityNumber.length() - 4;
                identityNumber = "*".repeat(lengthToHide) + identityNumber.substring(lengthToHide);
            }
            if (birthday != null && !birthday.isBlank() && birthday.length() > 4) {
                String visiblePart = birthday.substring(0, 4);
                String hiddenPart = "*".repeat(birthday.length() - 4);
                this.birthday = visiblePart + hiddenPart;
            }
            if (!fullname.isBlank()) {
                fullname = fullname.trim();
                int lastSpaceIndex = fullname.lastIndexOf(' ');
                if (lastSpaceIndex != -1) {
                    String[] parts = fullname.substring(0, lastSpaceIndex).split(" ");
                    StringBuilder hiddenPart = new StringBuilder();
                    for (String part : parts) {
                        hiddenPart.append("*".repeat(part.length())).append(" ");
                    }
                    fullname = hiddenPart + fullname.substring(lastSpaceIndex + 1);
                }
            }
        } catch (Exception e) {
            System.out.println("csdldcLog.secure() error: " + e);
        }
    } 
    
}
