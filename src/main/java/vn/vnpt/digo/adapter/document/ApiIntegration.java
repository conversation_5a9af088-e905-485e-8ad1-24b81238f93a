package vn.vnpt.digo.adapter.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.Integration.Connect;
import vn.vnpt.digo.adapter.dto.Integration.ErrorDefine;
import vn.vnpt.digo.adapter.dto.Integration.IgateConfig;
import vn.vnpt.digo.adapter.dto.request.Input;
import vn.vnpt.digo.adapter.dto.request.Permission;
import vn.vnpt.digo.adapter.dto.request.Request;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * type:
 *     0: Là dịch vụ
 *     1: Là api lấy token
 *     2: Là api tính năng
 *     3: Là cấu hình map api
 * code: mã của mỗi dòng dữ liệu
 * pcode: khi type là 1/2 mới có trường này, là code của dòng có type = 0
 * fcode = pcode/code hoặc code (khi pcode NULL)
 * description: mô tả
 * responseKey: nếu type = 1 sẽ có trường này
 * input:
 *      key: tên trường của api được sinh ra
 *      text: mô tả trường
 * outputType: 0: JSON/XML, 1: Xem tập tin, 2: Tải tập tin
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Document(collection = "apiIntegration")
public class ApiIntegration implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    @NonNull
    private Integer type;
    private String fcode;
    private String pfcode;
    private Integer ignoreToken;
    private String option;
    private String responseTokenKey;
    private Integer expiredNumber;
    private String responseRefreshTokenKey;
    private String refreshTokenDefaultValue;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date refreshTokenDefaultValueTime;
    private List<Input> inputs;
    private HashMap<String,Object> inputRaw;
    private Integer outputIsFile;
    private HashMap<String,Object> output;
    private Request request;
    private Permission permission;
    private Integer requiredContext;
    private Integer requiredToken;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updateTime;
    private String description;
    private Integer throughCenter;
    private IgateConfig igateConfig;
    private String js;
    private Connect connect;
    private Integer requiredOption;
    private List<ErrorDefine> errs;
    private Integer retryLimit;
    private Boolean enableRetry;
}
