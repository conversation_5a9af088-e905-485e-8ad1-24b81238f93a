package vn.vnpt.digo.adapter.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "khcnLog")
public class KHCNLog implements Serializable {
    
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    @NotNull
    private String api;
    
    @NotNull
    private String dossierCode;
    
    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date callTime;

    private Object responseBody;
    
    private Object requestBody;

    private String securityKey;

    private String agencyCode;

    private String procedureCode;

    private Integer status; // 0: error, 1: success

    private Boolean error;

    private String nationCode;
}
