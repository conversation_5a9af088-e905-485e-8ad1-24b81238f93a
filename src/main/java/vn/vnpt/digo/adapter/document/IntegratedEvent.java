package vn.vnpt.digo.adapter.document;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "integratedEvent")
public class IntegratedEvent implements Serializable {

    private ObjectId id;
    private Integer type = 1; //1: nps-dossier, 2: dossier chuyen nganh HCM, 3: dossier sync EMC
    private Integer status = 0; //0: created, 1: processing, 2: completed, 3: error
    private Date createdDate = new Date();
    private Date updatedDate = new Date();
    private Object data;
    private List<Object> child = new ArrayList<>();
    private String message = "";
    private Boolean isLGSPHCM = false;
    private Boolean isEMC = false;
    private Integer resync = 0;
}
