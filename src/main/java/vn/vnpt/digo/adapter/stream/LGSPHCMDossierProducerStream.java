package vn.vnpt.digo.adapter.stream;

import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCDossierPaymentReqDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCDossierReqDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCDossierTrackingReqDto;
import vn.vnpt.digo.adapter.service.LGSPHCMService;
import vn.vnpt.digo.adapter.stream.messaging.LGSPHCMDossierRequest;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVC_V2TrangthaiHoSoReqDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVC_V2DongBoHoSoReqDto;

/**
 *
 * <AUTHOR>
 */
@Component
@EnableBinding(LGSPHCMDossierRequest.class)
public class LGSPHCMDossierProducerStream {

    Logger logger = LoggerFactory.getLogger(LGSPHCMDossierProducerStream.class);

    @Autowired
    private LGSPHCMDossierRequest lgspHCMDossierRequest;
    
    @Autowired
    LGSPHCMService lgspHCMService;
    
    public void sendNotificationDossier(DVCDossierReqDto dossierDto) {
        try {
            Message<DVCDossierReqDto> message = MessageBuilder.withPayload(dossierDto).build();
            lgspHCMDossierRequest.dossierOutput().send(message);
        } catch (Exception ex) {
            logger.info("sendNotificationDossier - Exception:" + ex.getMessage());
        }
    }
    
    public void sendNotificationDossierTracking(DVCDossierTrackingReqDto dossierTrackingDto) {
        try {
            Message<DVCDossierTrackingReqDto> message = MessageBuilder.withPayload(dossierTrackingDto).build();
            lgspHCMDossierRequest.dossierTrackingOutput().send(message);
        } catch (Exception ex) {
            logger.info("sendNotificationDossierTracking - Exception:" + ex.getMessage());
        }
    }
    
    public void sendNotificationDossierPayment(DVCDossierPaymentReqDto dossierPaymentDto) {
        try {
            Message<DVCDossierPaymentReqDto> message = MessageBuilder.withPayload(dossierPaymentDto).build();
            lgspHCMDossierRequest.dossierPaymentOutput().send(message);
        } catch (Exception ex) {
            logger.info("sendNotificationDossierPayment - Exception:" + ex.getMessage());
        }
    }
    
    //paquoc7405/10/2022 WednesDay
    public void send_NotificationDossierSyncV2(DVC_V2DongBoHoSoReqDto dossierSyncDto) {
        try {
            Message<DVC_V2DongBoHoSoReqDto> thong_bao = MessageBuilder.withPayload(dossierSyncDto).build();
            lgspHCMDossierRequest.dongBoHoSoV2().send(thong_bao);
        } catch(Exception ex) {
            logger.info("send_NotificationDossierSyncV2 - Exception : " + ex.getMessage());
        }
    }

    //paquoc74 ThursDay 29/09/2022
    public void send_NotificationDossierStatusV2(DVC_V2TrangthaiHoSoReqDto dossierStatusDto) {
        try {
            Message<DVC_V2TrangthaiHoSoReqDto> thong_bao = MessageBuilder.withPayload(dossierStatusDto).build();
            lgspHCMDossierRequest.trangThaiHoSoV2().send(thong_bao);
        }catch(Exception ex){
            logger.info("send_NotificationDossierStatusV2 - Exception : " + ex.getMessage());
        }
    }
    
}
