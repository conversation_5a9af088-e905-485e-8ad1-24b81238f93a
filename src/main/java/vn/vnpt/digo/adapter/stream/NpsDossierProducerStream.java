package vn.vnpt.digo.adapter.stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.adapter.dto.KafkaNpsSyncDossierDto;
import vn.vnpt.digo.adapter.stream.messaging.NpsDossierProducer;

/**
 *
 * <AUTHOR>
 */
@Component
@EnableBinding(NpsDossierProducer.class)
public class NpsDossierProducerStream {

    @Autowired
    private NpsDossierProducer npsDossierProducer;

    public boolean push(KafkaNpsSyncDossierDto input) {
        Message<KafkaNpsSyncDossierDto> message = MessageBuilder.withPayload(input).build();
        return npsDossierProducer.output().send(message);
    }
}
