/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.stream.messaging;

import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 */
public interface LGSPHCMDossierProducer {
    public static final String OUTPUT = "LGSPHCMDossierProducerRequestOut";

    @Output(value = OUTPUT)
    public MessageChannel output();
}
