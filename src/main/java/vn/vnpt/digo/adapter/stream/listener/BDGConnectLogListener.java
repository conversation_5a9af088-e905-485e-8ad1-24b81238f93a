package vn.vnpt.digo.adapter.stream.listener;

import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.adapter.document.BDGConnectLogLock;
import vn.vnpt.digo.adapter.dto.bdg.ConnectLogDto;
import vn.vnpt.digo.adapter.service.BDGConnectLogService;
import vn.vnpt.digo.adapter.stream.event.BDGConnectLogEvent;

import java.util.Date;

@Component
public class BDGConnectLogListener {

    @Autowired
    BDGConnectLogService bdgConnectLogService;

    Logger logger = LoggerFactory.getLogger(BDGConnectLogListener.class);

    @Async
    @EventListener
    public void sync(BDGConnectLogEvent event){
        logger.info("Vilis BDG: Listener event - Insert Dossier");
        ConnectLogDto connectLog = event.getConnectLog();
        try{
            bdgConnectLogService.saveLog(connectLog);
        }catch (Exception e){
            BDGConnectLogLock bdgConnectLogLock = new BDGConnectLogLock();
            bdgConnectLogLock.setData(new Gson().toJson(connectLog));
            bdgConnectLogLock.setCreateAt(new Date());
            bdgConnectLogLock.setDetailError(e.getMessage());
            bdgConnectLogLock.setShortError("Error when save log by event - listener ");
            bdgConnectLogService.lockdown(bdgConnectLogLock);
        }


    }
}
