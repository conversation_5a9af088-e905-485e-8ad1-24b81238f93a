/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.stream;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.nps.NpsAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDetailDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.dto.utils.ProcedureDetailDto;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.repository.IntegratedEventRepository;
import vn.vnpt.digo.adapter.service.HCMLGSPDossierSyncService;
import vn.vnpt.digo.adapter.service.IntegratedConfigurationService;
import vn.vnpt.digo.adapter.service.IntegratedLogsService;
import static vn.vnpt.digo.adapter.stream.NpsDossierConsumerStream.dossierReceive;

import vn.vnpt.digo.adapter.service.UtilService;
import vn.vnpt.digo.adapter.stream.messaging.LGSPHCMNpsDossierStatusSyncRequest;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;

/**
 *
 * <AUTHOR>
 */
@EnableBinding(LGSPHCMNpsDossierStatusSyncRequest.class)
public class LGSPHCMNpsDossierStatusConsumerStream {
    @Autowired
    private HCMLGSPDossierSyncService npsService;

    @Autowired
    private IntegratedLogsService logService;

    @Autowired
    private IntegratedEventRepository eventRepository;
    
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private IntegratedConfigurationService configurationService;
    @Value(value = "${digo.thread.lgsphcm.dossier-sync-config-id}")
    private ObjectId lgspHCMDossierSyncConfigId;
    @Autowired
    private UtilService utilService;
    
    @Value(value = "${thread.maxpoolsize}")
    private int threadMaxpoolsize;
    @Value(value = "${digo.thread.sync.lgsphcm.dossier.max.poll}")
    private int maxpollConsumerlgsphcmdossier;
    @Value(value = "${digo.thread.sync.lgsphcm.dossier.max.poll.enable}")
    private Boolean enableMaxpollConsumerlgsphcmdossierHCM;
    
    @Value(value = "${thread.sleeptime}")
    private int threadSleeptime;
    
    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890030");
    
//    public static BlockingQueue<IntegratedEvent> dossierStatusReceive;

    Gson gson = new Gson();

    Logger logger = LoggerFactory.getLogger(LGSPHCMNpsDossierStatusConsumerStream.class);

    //Receive message from Kafka
    @StreamListener(LGSPHCMNpsDossierStatusSyncRequest.INPUT)
    public void receiveDossierStatusLGSPHCM(Message<?> message) {
        logger.info("Vua nhan duoc goi tin -- LGSPHCM1");
        try{
            logger.info("Debug 1.1");
            if (LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCM == null) {
                LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCM = new LinkedBlockingQueue<>();
            }
//            dossierStatusReceive = new LinkedBlockingQueue<>();
            ObjectMapper objectMapper = new ObjectMapper();
            NpsAsyncReceiveDto inputObj = objectMapper.readValue(message.getPayload().toString(), NpsAsyncReceiveDto.class);
            IntegratedEvent event = new IntegratedEvent();
            event.setIsLGSPHCM(Boolean.TRUE);
            event.setData(inputObj);

            Acknowledgment acknowledgment = message.getHeaders().get(KafkaHeaders.ACKNOWLEDGMENT, Acknowledgment.class);
            if(enableMaxpollConsumerlgsphcmdossierHCM)
            {
                if (LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCM.size() > maxpollConsumerlgsphcmdossier) {
                    logger.info(Thread.currentThread().getName() + "[NpsDossierReceive] nack to kafka");
                    acknowledgment.nack(threadSleeptime);
                    return;
                }
            }else{
                if (LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCM.size() > threadMaxpoolsize) {
                    logger.info(Thread.currentThread().getName() + "[NpsDossierReceive] nack to kafka");
                    acknowledgment.nack(threadSleeptime);
                    return;
                }
            }
            if(Objects.nonNull(inputObj.getDossier().getProcedureNationCodeSync()))
            {
                if(inputObj.getDossier().getProcedureNationCodeSync())
                {
                    if (LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCM.offer(inputObj)) {
                        logger.info("LGSPHCM-NPS-Sync-Dossier: Offer dossier successfully: " + inputObj.getDossier().getCode() + " - Data: " + inputObj);
                        logger.info("LGSPHCM-NPS-Sync-Dossier: Queue length: " + LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCM.stream().count());
                    } else {
                        logger.error("LGSPHCM-NPS-Sync-Dossier: Offer dossier failed: " + inputObj.getDossier().getCode() + " - Data: " + inputObj);
                    }
                    if (acknowledgment != null) {
                        System.out.println("LGSPHCM-NPS-Status-Sync: Acknowledgment provided");
                        acknowledgment.acknowledge();
                    }
                }
            }else{
                if (LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCM.offer(inputObj)) {
                    logger.info("LGSPHCM-NPS-Sync-Dossier: Offer dossier successfully: " + inputObj.getDossier().getCode() + " - Data: " + inputObj);
                    logger.info("LGSPHCM-NPS-Sync-Dossier: Queue length: " + LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCM.stream().count());
                } else {
                    logger.error("LGSPHCM-NPS-Sync-Dossier: Offer dossier failed: " + inputObj.getDossier().getCode() + " - Data: " + inputObj);
                }
                if (acknowledgment != null) {
                    System.out.println("LGSPHCM-NPS-Status-Sync: Acknowledgment provided");
                    acknowledgment.acknowledge();
                }
            }
        } catch (Exception e){
            logService.save(null, new IdCodeNameSimpleDto(new ObjectId(), "NPSSyncDossier", ""), 0, e.getMessage(), message.getPayload().toString());
            logger.info("LGSPHCM-NPS-Status-Exception: Kafka receiving failed with " + e.getMessage() + ", " + message.getPayload());
        }
    }
    
//    @Scheduled(cron = "0 */30 * * * ?")
//    public String resyncDossierStatus () {
//        String msg = "";
//        try {
//            IntegratedEvent event = this.getEvent();
//            if (Objects.nonNull(event)) {
//                dossierStatusReceive = new LinkedBlockingQueue<>();
//                if (Objects.nonNull(event.getResync())) {
//                    event.setResync(event.getResync() + 1);
//                } else {
//                    event.setResync(1);
//                }
//                if (dossierStatusReceive.offer(event)) {
//                    msg = "Re-offer event successfully: " + event.getId();
//                } else {
//                    msg = "Re-offer event failed: {}" + event.getId();
//                }
//            }
//        } catch (Exception e) {
//            msg = "Re-offer error: " + e;
//        }
//        logger.info(msg);
//        return msg;
//    }
//
//    public String resyncAllDossierStatus () {
//        System.out.println("resync status ne");
//        int counter = 0;
//        while (true) {
//            if (resyncDossierStatus().equals("")) break;
//            else counter++;
//        }
//        return "Re-offer total: " + counter + " event";
//    }
//    IntegratedEvent getEvent(){
//        Query query = new Query();
//        query.addCriteria(Criteria.where("status").in(0,4));
//        query.addCriteria(Criteria.where("type").is(1));
//        query.addCriteria(Criteria.where("resync").lte(5));
//        query.addCriteria(Criteria.where("data.type").is(2));
//        Criteria criteria = new Criteria();
//        criteria.andOperator(
//                Criteria.where("isLGSPHCM").exists(true),
//                Criteria.where("isLGSPHCM").is(true));
//        query.addCriteria(criteria);
//        query.with(Sort.by(Sort.Direction.ASC, "_id"));
//        IntegratedEvent ret = this.mongoTemplate.findOne(query, IntegratedEvent.class);
//        return ret;
//    }
//    @Scheduled(cron = "0 */30 * * * ?")
//    public String resyncDossierStatus () {
//        String msg = "";
//        try {
//            IntegratedEvent event = this.getEvent();
//            if (Objects.nonNull(event)) {
//                dossierStatusReceive = new LinkedBlockingQueue<>();
//                if (Objects.nonNull(event.getResync())) {
//                    event.setResync(event.getResync() + 1);
//                } else {
//                    event.setResync(1);
//                }
//                if (dossierStatusReceive.offer(event)) {
//                    msg = "Re-offer event successfully: " + event.getId();
//                } else {
//                    msg = "Re-offer event failed: {}" + event.getId();
//                }
//            }
//        } catch (Exception e) {
//            msg = "Re-offer error: " + e;
//        }
//        logger.info(msg);
//        return msg;
//    }
//
//    public String resyncAllDossierStatus () {
//        System.out.println("resync status ne");
//        int counter = 0;
//        while (true) {
//            if (resyncDossierStatus().equals("")) break;
//            else counter++;
//        }
//        return "Re-offer total: " + counter + " event";
//    }
    
    public int resyncDossierStatusLGSPHCM(int limit){
        int count = 0;
        try {
            if(Objects.isNull(limit)){
                limit = 1000;
            }
            List<IntegratedEvent> eventList = this.getEventList(limit);
            List<IntegratedEvent> updateStatusList = new ArrayList<>();
            if(Objects.nonNull(eventList)){
               for(IntegratedEvent event : eventList){
                    NpsAsyncReceiveDto data = (NpsAsyncReceiveDto) event.getData();
                    if(data.getDossier().getProcedureNationCodeSync()){
                        if (LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCM.offer(data)) {
                            //updateList status of event
                            event.setStatus(1000);
                            event.setMessage("resyncDossierLGSPHCM");
                            updateStatusList.add(event);
                            count = count + 1;
                            logger.info("LGSPHCM-NPS-Re-Sync-Dossier-Status: Offer dossier successfully: " + data.getDossier().getCode() + " - Data: " + data);
                            logger.info("LGSPHCM-NPS-Re-Sync-Dossier-Status: Queue length: " + LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCM.stream().count());
                        } else {
                            logger.error("LGSPHCM-NPS-Re-Sync-Dossier-Status: Offer dossier failed: " + data.getDossier().getCode() + " - Data: " + data);
                        }
                    }else{
                        event.setStatus(3000);
                        event.setMessage("missDossierNationCodeLGSPHCM");
                        updateStatusList.add(event);
                    }
                }   
                try {
                    //update status
                    eventRepository.saveAll(updateStatusList);
                } catch (Exception e) {
                    logger.error("LGSPHCM-NPS-Re-Sync-Dossier-Status: Update Stauts Exception: " + updateStatusList.toString());
                }
            }
        } catch (Exception e) {
            logService.save(null, new IdCodeNameSimpleDto(new ObjectId(), "LGSPHCMNPSSyncDossier", ""), 0, e.getMessage(), "resyncDossierLGSPHCM");
            logger.info("LGSPHCM-NPS-Dossier-Exception: Kafka receiving failed with " + e.getMessage() + ", " + "resyncDossierLGSPHCM");
        }
        return count;
    }
    
    List<IntegratedEvent> getEventList(int limit){
        Query query = new Query();
        query.addCriteria(Criteria.where("status").in(0,4));
        query.addCriteria(Criteria.where("type").is(1));
        query.addCriteria(Criteria.where("resync").lte(5));
        query.addCriteria(Criteria.where("data.type").is(2));
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("isLGSPHCM").exists(true),
                Criteria.where("isLGSPHCM").is(true));
        query.addCriteria(criteria);
        query.limit(limit);
        query.with(Sort.by(Sort.Direction.ASC, "_id"));
        List<IntegratedEvent> ret = this.mongoTemplate.find(query, IntegratedEvent.class);
        return ret;
    }
}
