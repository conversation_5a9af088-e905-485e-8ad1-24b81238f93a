package vn.vnpt.digo.adapter.stream;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.adapter.document.BDGConnectLogLock;
import vn.vnpt.digo.adapter.dto.bdg.ConnectLogDto;
import vn.vnpt.digo.adapter.dto.bdg.moc.DossierStreamDto;
import vn.vnpt.digo.adapter.pojo.Dossier;
import vn.vnpt.digo.adapter.pojo.bdg.DossierExtend;
import vn.vnpt.digo.adapter.service.BDGConnectLogService;
import vn.vnpt.digo.adapter.service.BDGConnectService;
import vn.vnpt.digo.adapter.stream.messaging.BDGConnectLogReceive;
import vn.vnpt.digo.adapter.stream.messaging.MinistryConnectReceive;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Objects;

@Component
@EnableBinding(MinistryConnectReceive.class)
public class MinistryConnectStream {

    @Autowired
    private BDGConnectService bdgConnectService;


    @StreamListener(MinistryConnectReceive.INPUT)
    public void ministryConnect(Message<?> message) {
        try{
            ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);;
            DossierStreamDto dossier = objectMapper.readValue(message.getPayload().toString(), DossierStreamDto.class);
            DossierExtend extendBDG  = dossier.getExtendBDG();
            if (Objects.nonNull(extendBDG) && extendBDG.getIsConnect()){
                if (extendBDG.getReceivedConnect()){
                    bdgConnectService.updateProcessDossierTo(dossier.getId());
                }
            }
            bdgConnectService.writeLog(dossier.getCode(), "Connect", 200, null, dossier.toString(), "Nhận dữ liệu từ kafka", null);
        }catch (Exception e){
            bdgConnectService.writeLog("Connect", "Connect", 200, null, message.getPayload().toString(), "lỗi nhận dữ liệu từ kafka hoặc xử lý dữ liệu", null);
        }
    }
}
