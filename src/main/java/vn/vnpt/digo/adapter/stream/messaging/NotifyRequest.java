package vn.vnpt.digo.adapter.stream.messaging;

import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;

/**
 *
 * <AUTHOR>
 */
public interface NotifyRequest {
    /**
     * Name of the output channel.
     */
    public static final String OUTPUT = "notifyEventRequest";

    /**
     * @return output channel
     */
    @Output(value = OUTPUT)
    public MessageChannel output();
}
