package vn.vnpt.digo.adapter.stream.payload;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.pojo.TransAgency;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NpsAsyncTest {
    public static final Integer SYNC_DOSSIER = 1;
    public static final Integer SYNC_STATUS = 2;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId configId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subsystemId;

    private Integer type; // 1: sync dossier, 2: sync status

    private DossierDto dossier;
    private StatusDto status;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DossierDto {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String code;
        private Boolean updated;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StatusDto {

        private String assignee;
        private String position = "";

        private AgencyDto agency;
        private String content = "Complete task";

        private IdDto status;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date updatedDate;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date startDate;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date endDate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AgencyDto {
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String code;
        private List<TransAgency> name;
        private AgencyDto parent;
    }
}
