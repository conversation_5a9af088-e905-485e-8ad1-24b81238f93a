package vn.vnpt.digo.adapter.stream.messaging;
import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;

/**
* <AUTHOR>
 * <PERSON><PERSON><PERSON><PERSON> chỉnh đẩy call sms, cập nhật history, comment vào kafka để thực thi sau, không làm ảnh hưởng performance API chính
 */
public interface NpsSendNotificationReceive {
    public static final String INPUT = "npsSendNotificationSyncReceive";

    @Input(value = INPUT)
    public SubscribableChannel input();
}
