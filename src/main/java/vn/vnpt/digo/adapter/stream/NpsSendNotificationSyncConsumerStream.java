package vn.vnpt.digo.adapter.stream;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.hcm.NpsSendNotificationReceiveDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.service.IntegratedLogsService;
import vn.vnpt.digo.adapter.service.SmsService;
import vn.vnpt.digo.adapter.stream.messaging.NpsSendNotificationReceive;

/**
 * <AUTHOR> Điều chỉnh đẩy call sms, cập nhật history,
 * comment vào kafka để thực thi sau, không làm ảnh hưởng performance API chính
 */
@Component
@EnableBinding(NpsSendNotificationReceive.class)
public class NpsSendNotificationSyncConsumerStream {

    @Autowired
    private IntegratedLogsService logService;

    @Autowired
    private SmsService smsService;

    @Autowired
    RestTemplate rest;

    Logger logger = LoggerFactory.getLogger(NpsSendNotificationSyncConsumerStream.class);

    //Receive message from Kafka
    @StreamListener(NpsSendNotificationReceive.INPUT)
    public void receiveSendNotificationData(Message<?> message) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            NpsSendNotificationReceiveDto inputObj = objectMapper.readValue(message.getPayload().toString(), NpsSendNotificationReceiveDto.class);
            // Set offer
            logger.info("NPS-Sync: Kafka receive send notification: " + inputObj.toString());
            //System.out.println("NPS-Sync: Kafka receive send notification: " + inputObj.toString());

            //send SMS
            if (inputObj.getSms() != null && !inputObj.getSms().getContent().isBlank() && !inputObj.getSms().getPhoneNumber().isEmpty()) {
                try {
                    IdDto res = smsService.sendBatch(inputObj.getSms());
                    //System.out.println("sendSMS - Completed - id sms: " + res.toString());
                    logger.info("sendSMS - Completed - id sms: " + res.toString() + "; dossier-id: " + inputObj.getDossierId());
                } catch (Exception ex) {
                    //System.out.println("NPS-Exception: sendSMS failed with: " + ex.getMessage());
                    logger.info("NPS-Exception: sendSMS failed with " + ex.getMessage() + ", contentSMS: " + inputObj.getSms().toString());
                }
            }

            Acknowledgment acknowledgment = message.getHeaders().get(KafkaHeaders.ACKNOWLEDGMENT, Acknowledgment.class);
            if (acknowledgment != null) {
                //System.out.println("Acknowledgment provided");
                acknowledgment.acknowledge();
            }
        } catch (Exception ex) {
            logService.save(null, new IdCodeNameSimpleDto(new ObjectId(), "SendNotificationSyncConsumer", ""), 0, ex.getMessage(), message.getPayload().toString());
            //System.out.println("NPS-Exception: Kafka receiving failed with " + ex.getMessage() + ", " + message.getPayload());
            logger.info("NPS-Exception: Kafka receiving failed with " + ex.getMessage() + ", " + message.getPayload());
        }

    }
}
