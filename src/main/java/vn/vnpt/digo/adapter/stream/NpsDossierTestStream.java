package vn.vnpt.digo.adapter.stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import vn.vnpt.digo.adapter.stream.messaging.NpsDossierTest;
import vn.vnpt.digo.adapter.stream.payload.NpsAsyncTest;

@Component
@EnableBinding(NpsDossierTest.class)
public class NpsDossierTestStream {
    
    @Autowired
    private NpsDossierTest producer;

    public boolean push(NpsAsyncTest output){
        Message<NpsAsyncTest> message = MessageBuilder.withPayload(output).build();
        return producer.output().send(message);
    }
}
