package vn.vnpt.digo.adapter.stream.messaging;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;


public interface DVCLTDossierRequest {
    public static final String DOSSIER_OUTPUT = "DVCLTDossierOut";
    public static final String DOSSIER_HOTICH_OUTPUT = "DVCLTDossierHoTichOut";
    public static final String DOSSIER_TRACKING_OUTPUT = "DVCLTDossierTrackingOut";
    public static final String DOSSIER_LOG_IN = "DVCLTDossierLogIn";
    
    @Output(value = DOSSIER_OUTPUT)
    public MessageChannel dossierOutput();

    @Output(value = DOSSIER_HOTICH_OUTPUT)
    public MessageChannel dossierHoTichOutput();
    
    @Output(value = DOSSIER_TRACKING_OUTPUT)
    public MessageChannel dossierTrackingOutput();

    @Input(value = DOSSIER_LOG_IN)
    public SubscribableChannel dossierLogInput();
    
}
