package vn.vnpt.digo.adapter.stream;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.adapter.dto.emc.EmcAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.service.IntegratedLogsService;
import vn.vnpt.digo.adapter.stream.messaging.EMCDossierSyncReceive;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

@Component
@EnableBinding(EMCDossierSyncReceive.class)
public class EMCDossierSyncConsumerStream
{
    @Autowired
    private IntegratedLogsService logService;
    @Value(value = "${thread.maxpoolsize}")
    private int threadMaxpoolsize;
    @Value(value = "${thread.sleeptime}")
    private int threadSleeptime;
    Logger logger = LoggerFactory.getLogger(EMCDossierSyncConsumerStream.class);

    public static  BlockingQueue<EmcAsyncReceiveDto>  emcDossierReceive = new LinkedBlockingQueue<>();

    @StreamListener(EMCDossierSyncReceive.INPUT)
    public void receiveEMCDossierHCM(Message<?> message)
    {
        Acknowledgment acknowledgment = message.getHeaders().get(KafkaHeaders.ACKNOWLEDGMENT, Acknowledgment.class);
         try
         {
             ObjectMapper objectMapper = new ObjectMapper();
             EmcAsyncReceiveDto inputObj = objectMapper.readValue(message.getPayload().toString(), EmcAsyncReceiveDto.class);
             logger.info("NPS-Sync: Kafka receive dossier: " + inputObj.getDossier().getCode());
             if (emcDossierReceive.size() > threadMaxpoolsize) {
                 logger.info(Thread.currentThread().getName() + "[EmcDossierSync] nack to kafka");
                 acknowledgment.nack(threadSleeptime);
                 return ;
             }
             if (emcDossierReceive.offer(inputObj)) {
                 logger.info("Offer dossier successfully: " + inputObj.getDossier().getCode() + " - Data: " + inputObj);
                 logger.info("Queue length: " + emcDossierReceive.stream().count());
             } else {
                 logger.error("Offer dossier failed: " + inputObj.getDossier().getCode() + " - Data: " + inputObj);
             }
             if (acknowledgment != null) {
                 System.out.println("Acknowledgment provided");
                 acknowledgment.acknowledge();
             }

         }catch(Exception e)
         {
             logService.save(null, new IdCodeNameSimpleDto(new ObjectId(), "EMCSyncDossier", ""), 0, e.getMessage(), message.getPayload().toString());
             logger.info("EMC-Exception: Kafka receiving failed with " + e.getMessage() + ", " + message.getPayload());
             acknowledgment.nack(threadSleeptime);
         }
    }
}
