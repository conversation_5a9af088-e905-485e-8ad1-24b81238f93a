/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.stream.messaging;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;

/**
 *
 * <AUTHOR>
 */
public interface ProcedureReceive {
    public static final String INPUT = "procedureSyncRequestOut";

    @Input(value = INPUT)
    public SubscribableChannel input();
}
