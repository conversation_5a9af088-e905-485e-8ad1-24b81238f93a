package vn.vnpt.digo.adapter.stream;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import vn.vnpt.digo.adapter.dto.dvclt.DVCLTHoTichProducerDataDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.*;
import vn.vnpt.digo.adapter.repository.DVCLTLogRepository;
import vn.vnpt.digo.adapter.stream.messaging.DVCLTDossierRequest;


@Component
@EnableBinding(DVCLTDossierRequest.class)
public class DVCLTDossierProducerStream {

    Logger logger = LoggerFactory.getLogger(DVCLTDossierProducerStream.class);

    @Autowired
    private DVCLTDossierRequest dvcltDossierRequest;
    
    @Autowired
    private DVCLTLogRepository dvcltLogRepository;
    
    public void sendNotificationDossier(DVCLTDossierReqDto dossierDto) {
        try {
            Message<DVCLTDossierReqDto> message = MessageBuilder.withPayload(dossierDto).build();
            dvcltDossierRequest.dossierOutput().send(message);
        } catch (Exception ex) {
            logger.info("sendNotificationDossier - Exception:" + ex.getMessage());
        }
    }
    
    public void sendNotificationHoTichDossier(DVCLTHoTichProducerDataDto dossierDto) {
        try {
            Message<DVCLTHoTichProducerDataDto> message = MessageBuilder.withPayload(dossierDto).build();
            dvcltDossierRequest.dossierHoTichOutput().send(message);
        } catch (Exception ex) {
            logger.info("sendNotificationDossier - Exception:" + ex.getMessage());
        }
    }

    public void sendNotificationDossierTracking(DVCLTDossierTrackingReqDto dossierTrackingDto) {
        try {
            Message<DVCLTDossierTrackingReqDto> message = MessageBuilder.withPayload(dossierTrackingDto).build();
            dvcltDossierRequest.dossierTrackingOutput().send(message);
        } catch (Exception ex) {
            logger.info("sendNotificationDossierTracking - Exception:" + ex.getMessage());
        }
    }

    @StreamListener(DVCLTDossierRequest.DOSSIER_LOG_IN)
    @Transactional
    public void getDVCLTDossierLogRequestStream(String logId) {
        try {
        dvcltLogRepository.deleteById(new ObjectId(logId));
        logger.info("getDVCLTDossierRequestStream - Dossier log delete with id: " + logId);
        } catch (Exception ex) {
            logger.info("getDVCLTDossierRequestStream - Dossier log delete with exception:" + ex.getMessage());
        }
    }
    
    
}
