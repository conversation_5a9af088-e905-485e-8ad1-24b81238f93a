package vn.vnpt.digo.adapter.stream;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.dto.nps.NpsAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.repository.IntegratedEventRepository;
import vn.vnpt.digo.adapter.service.IntegratedLogsService;
import vn.vnpt.digo.adapter.service.NpsDossierSyncService;
import vn.vnpt.digo.adapter.stream.messaging.BdgNpsDossierStatusSyncRequest;

@EnableBinding({BdgNpsDossierStatusSyncRequest.class})
public class BdgNpsDossierStatusSyncConsumerStream {
    @Autowired
    private NpsDossierSyncService service;

    @Autowired
    private IntegratedLogsService logService;

    @Autowired
    private IntegratedEventRepository eventRepository;

    Gson gson = new Gson();

    Logger logger = LoggerFactory.getLogger(NpsDossierConsumerStream.class);

    @StreamListener(BdgNpsDossierStatusSyncRequest.INPUT)
    public void receiveDossierStatusSyncBdg(Message<?> message) {
        try{
            ObjectMapper objectMapper = new ObjectMapper();
            NpsAsyncReceiveDto inputObj = objectMapper.readValue(message.getPayload().toString(), NpsAsyncReceiveDto.class);

            IntegratedEvent event = new IntegratedEvent();
            event.setData(inputObj);
            IntegratedEvent saved = eventRepository.save(event);
            if(!EventProperties.DOSSIER_EVENT_AVAILABLE_BDG){
                EventProperties.DOSSIER_EVENT_AVAILABLE_BDG = true;
            }
            logger.info("NPS-Sync: Kafka event saved" + saved.getId().toHexString());
            Acknowledgment acknowledgment = message.getHeaders().get(KafkaHeaders.ACKNOWLEDGMENT, Acknowledgment.class);
            if (acknowledgment != null) {
                System.out.println("Acknowledgment provided");
                acknowledgment.acknowledge();
            }
        } catch (Exception e){
            logService.save(null, new IdCodeNameSimpleDto(new ObjectId(), "NPSSyncDossier", ""), 0, e.getMessage(), message.getPayload().toString());
            logger.info("NPS-Exception: Kafka receiving failed with " + e.getMessage() + ", " + message.getPayload());
        }

    }
}
