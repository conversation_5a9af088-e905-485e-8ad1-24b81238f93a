package vn.vnpt.digo.adapter.stream.messaging;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

/**
 *
 * <AUTHOR>
 */
public interface LGSPHCMDossierRequest {
    public static final String DOSSIER_OUTPUT = "LGSPHCMDossierOut";
    public static final String DOSSIER_TRACKING_OUTPUT = "LGSPHCMDossierTrackingOut";
    public static final String DOSSIER_PAYMENT_OUTPUT = "LGSPHCMDossierPaymentOut";
    public static final String DOSSIER_CHUYEN_NGANH_INPUT = "LGSPHCMDossierChuyenNganhIn";
    
    
    public static final String DongBoHosoV2 = "LGSPHCM_SyncHoSoV2";//paquoc74 ThursDay 06/10/2022
    public static final String TrangThaiHoSoV2 = "LGSPHCM_StatusHoSoV2";//paquoc74 ThursDay 06/10/2022
    
    @Output(value = DOSSIER_OUTPUT)
    public MessageChannel dossierOutput();
    
    @Output(value = DOSSIER_TRACKING_OUTPUT)
    public MessageChannel dossierTrackingOutput();
    
    @Output(value = DOSSIER_PAYMENT_OUTPUT)
    public MessageChannel dossierPaymentOutput();
    
    @Input(value = DOSSIER_CHUYEN_NGANH_INPUT)
    public SubscribableChannel dossierChuyenNganhInput();
    
    @Output(value = DongBoHosoV2)
    public MessageChannel dongBoHoSoV2();//paquoc74 ThursDay 06/10/2022
    
    @Output(value = TrangThaiHoSoV2)
    public MessageChannel trangThaiHoSoV2();//paquoc74 ThursDay 06/10/2022
    
}
