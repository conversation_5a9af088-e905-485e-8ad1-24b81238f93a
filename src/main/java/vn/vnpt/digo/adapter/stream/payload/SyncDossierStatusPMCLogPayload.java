package vn.vnpt.digo.adapter.stream.payload;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.SyncDossierStatusPMCRequestDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierStatusSyncReqDto;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SyncDossierStatusPMCLogPayload implements Serializable {

    SyncDossierStatusPMCRequestDto request;
    String response;

}
