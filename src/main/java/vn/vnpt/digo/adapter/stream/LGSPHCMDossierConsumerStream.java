package vn.vnpt.digo.adapter.stream;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.net.URI;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpMethod;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.transaction.annotation.Transactional;
import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.nps.NpsAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.repository.IntegratedEventRepository;
import vn.vnpt.digo.adapter.service.IntegratedLogsService;
import vn.vnpt.digo.adapter.stream.messaging.LGSPHCMDossierRequest;

/**
 *
 * <AUTHOR>
 */
@Component
@EnableBinding(LGSPHCMDossierRequest.class)
public class LGSPHCMDossierConsumerStream {

    Logger logger = LoggerFactory.getLogger(LGSPHCMDossierConsumerStream.class);

    @Autowired
    private IntegratedLogsService logService;

    @Autowired
    private IntegratedEventRepository eventRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    public static final BlockingQueue<HCMLGSPAsyncReceiveDto> integratedEventBatchList = new LinkedBlockingQueue<>();

    @Value("${digo.batch.processing.size}")
    private int batchSize;
    @Value(value = "${thread.sleeptime}")
    private int threadSleeptime;

    @StreamListener(LGSPHCMDossierRequest.DOSSIER_CHUYEN_NGANH_INPUT)
    @Transactional
    public void getDossierChuyenNganhRequestStream(Message<?> message) {
        try {
            Acknowledgment acknowledgment = message.getHeaders().get(KafkaHeaders.ACKNOWLEDGMENT, Acknowledgment.class);
            ObjectMapper objectMapper = new ObjectMapper();
            HCMLGSPAsyncReceiveDto inputObj = objectMapper.readValue(message.getPayload().toString(), HCMLGSPAsyncReceiveDto.class);
            logger.info("LGSPDossierChuyenNganh-Sync: data dossier " + inputObj.toString());
            if(integratedEventBatchList.size() > batchSize){
                logger.info("LGSPDossierChuyenNganh-Sync: NACK");
                acknowledgment.nack(threadSleeptime);
                return;
            }
            if(integratedEventBatchList.offer(inputObj)){
                logger.info("Offer LGSPDossierChuyenNganh-Sync successfully: " + inputObj.getDossier().getCode() + " - Data: " + inputObj);
                logger.info("Queue LGSPDossierChuyenNganh-Sync length: " + integratedEventBatchList.size());
            }else{
                logger.error("Offer LGSPDossierChuyenNganh-Sync failed: " + inputObj.getDossier().getCode() + " - Data: " + inputObj);
            }

            if (acknowledgment != null) {
                System.out.println("LGSPDossierChuyenNganh-Sync: Acknowledgment provided");
                acknowledgment.acknowledge();
            }
//            }
        } catch (Exception e){
            logService.save(null, new IdCodeNameSimpleDto(new ObjectId(), "LGSPDossierChuyenNganh", ""), 0, e.getMessage(), message.getPayload().toString());
            logger.info("LGSPDossierChuyenNganh-Exception: Kafka receiving failed with " + e.getMessage() + ", " + message.getPayload());
        }
    }

//    @Scheduled(fixedDelay = 10000) // Chạy sau mỗi 10 giây
//    public void scheduledBatchProcessing() {
//        if (!integratedEventBatchList.isEmpty()) {
//            processBatch();
//        }
//    }
//
//    private void processBatch() {
//        Iterable<IntegratedEvent> list =  eventRepository.saveAll(integratedEventBatchList);
//        logger.info("LGSPDossierChuyenNganh-Sync: Kafka events saved" + list.toString());
//        integratedEventBatchList.clear();
//    }
}
