package vn.vnpt.digo.adapter.stream.messaging;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;

public interface AuthenticateDossierHCMProducer
{
  public static final String OUTPUT ="authenticateDossierHCMRequestOut";
  public static final String OUTPUT_FROM_AUTHENSYSTEM ="authenticateDossierHCMFromAuthenSystemRequestOut";
  @Output(value = OUTPUT)
  public MessageChannel output_hcm();
  @Output(value = OUTPUT_FROM_AUTHENSYSTEM)
  public MessageChannel output_from_authensystem();
}
