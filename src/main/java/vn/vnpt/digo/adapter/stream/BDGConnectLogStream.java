package vn.vnpt.digo.adapter.stream;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import vn.vnpt.digo.adapter.document.BDGConnectLogLock;
import vn.vnpt.digo.adapter.dto.bdg.ConnectLogDto;
import vn.vnpt.digo.adapter.service.BDGConnectLogService;
import vn.vnpt.digo.adapter.stream.messaging.BDGConnectLogReceive;

import java.util.Date;

@Component
@EnableBinding(BDGConnectLogReceive.class)
public class BDGConnectLogStream {
    @Autowired
    BDGConnectLogService bdgConnectLogService;
    Logger logger = LoggerFactory.getLogger(ProcedureConsumerStream.class);

    @StreamListener(BDGConnectLogReceive.INPUT)
    public void receiveLog(Message<?> message) {
        try{
            ObjectMapper objectMapper = new ObjectMapper();
            ConnectLogDto connectLogDto = objectMapper.readValue(message.getPayload().toString(), ConnectLogDto.class);
            bdgConnectLogService.saveLog(connectLogDto);
        }catch (Exception e){
            logger.info("Error when write log");
            BDGConnectLogLock bdgConnectLogLock = new BDGConnectLogLock();
            bdgConnectLogLock.setData(message.getPayload().toString());
            bdgConnectLogLock.setCreateAt(new Date());
            bdgConnectLogLock.setDetailError(e.getMessage());
            bdgConnectLogLock.setShortError("Error when write log by kafka ");
            bdgConnectLogService.lockdown(bdgConnectLogLock);
        }
    }

}
