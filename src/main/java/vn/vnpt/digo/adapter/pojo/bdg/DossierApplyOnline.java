package vn.vnpt.digo.adapter.pojo.bdg;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.PadPApplyDto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierApplyOnline implements Serializable {
    private ProcedureDto procedure;
    private RecvKindDto dossierReceivingKind = new RecvKindDto(); //static code
    private List<AttachmentDto> attachment;
    private ApplicantDto applicant; ///ba/place/{id}/--address
    private AgencyDto agency; ///ba/agency/{id}/name+code+parent+ancestor/--fully
    @JsonProperty("eForm")
    private FormDto eForm;

    private ProcessDto procedureProcessDefinition;
    private RecvKindDto dossierMenuTaskRemind;
    private String code;
    private IdNameDto procedureLevel;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplicantDto implements Serializable {
        private String eformId;
        private Object data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressDto implements Serializable {

        private String address;
        private PlaceDto place;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressDetailDto implements Serializable {

        private String label;
        private String value;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PlaceDto implements Serializable {

        private String id;
        private String name;
        private IdNameTypeDto parent;
        private IdNameDto type;
        private List<IdNameTypeDto> ancestors;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IdentityDto implements Serializable {

        private String number;
        private IdNameDto agency;
        private String date;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdNameTypeDto implements Serializable {

        private String id;
        private String name;
        private PadPApplyDto.IdNameDto type;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdNameDto implements Serializable {

        private String id;
        private List<NameDto> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdNameCodeDto implements Serializable {

        private String id;
        private String code;
        private List<NameDto> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcedureDtoTemp implements Serializable {
        private String id;
        private String code;
        private List<NameDto> translate;
        private IdNameCodeDto sector;
        private IdNameDto level;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcedureDto implements Serializable {

        private String id;
        private String code;
        private List<NameDto> translate;
        private IdNameCodeDto sector;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecvKindDto implements Serializable {

        private String id = "5fd1c7591b53d8779bc9ea78";
        private List<NameDto> name = new ArrayList<>() {
            {
                add(new NameDto((short) 46, "Online"));
                add(new NameDto((short) 228, "Trực tuyến"));
            }
        };
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttachmentDto implements Serializable {

        private String id;
        private String filename;
        private Integer size;
        private String group;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgencyDto implements Serializable {

        private String id;
        private String code;
        private List<NameDto> name;
        private AgencyDto parent;
        private List<AgencyDto> ancestors;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NameDto implements Serializable {

        private Short languageId;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormDto implements Serializable {

        private String id;
        private Object data;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProcessDto implements Serializable {

        private String id;
        private Object processDefinition;
    }
}
