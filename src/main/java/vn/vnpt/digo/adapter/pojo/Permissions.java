/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Permissions implements Serializable {

    Permission[] permissions = new Permission[0];

    public Permission getPermission(String permission) {
        for (Permission item : this.permissions) {
            try {
                if (Objects.equals(item.getPermission().getCode(), permission)) {
                    return item;
                }
            } catch (Exception e) {
                //Do nothing
            }
        }
        return null;
    }
}
