/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SynthesisReport implements Serializable {
    @JsonProperty("TongChuaXuLyTreHan")
    private String TongChuaXuLyTreHan = "";
    @JsonProperty("Thang")
    private String Thang = "";
    @JsonProperty("LoaiThongKe")
    private String LoaiThongKe= "";
    @JsonProperty("Nam")
    private String Nam = "";
    @JsonProperty("SoTonKyTruoc")
    private String SoTonKyTruoc = "";
    @JsonProperty("Madonvi")
    private String Madonvi= "";
    @JsonProperty("TongXuLyDungHan")
    private String TongXuLyDungHan= "";
    @JsonProperty("PhanTramXuLyDungHan")
    private String PhanTramXuLyDungHan= "";
    @JsonProperty("PhanTramXuLyTreHan")
    private String PhanTramXuLyTreHan= "";
    @JsonProperty("PhanTramChuaXuLyTrongHan")
    private String PhanTramChuaXuLyTrongHan= "";
    @JsonProperty("TongChuaXuLyTrongHan")
    private String TongChuaXuLyTrongHan= "";
    
    @JsonProperty("SoNhanTrongKy")
    private String SoNhanTrongKy= "";
    @JsonProperty("TongChuaXuLy")
    private String TongChuaXuLy= "";
    @JsonProperty("TongSoXuLy")
    private String TongSoXuLy= "";
    @JsonProperty("TongXuLyTreHan")
    private String TongXuLyTreHan= "";
    @JsonProperty("TenDonVi")
    private String TenDonVi= "";
    @JsonProperty("TongDaXuLy")
    private String TongDaXuLy= "";
    @JsonProperty("GhiChu")
    private String GhiChu= "";
    @JsonProperty("PhanTramChuaXuLyTreHan")
    private String PhanTramChuaXuLyTreHan= "";
    
}
