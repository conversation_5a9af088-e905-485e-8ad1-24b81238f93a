package vn.vnpt.digo.adapter.pojo;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Translate implements Serializable{

    private Short languageId;

    private String name;

    public static String toTranslate(List<Translate> translateNames, Short languageId){
        String name = "";
        if (translateNames == null){
            return name;
        }
        for (Translate n : translateNames){
            if (n.getLanguageId() == languageId){
                name =  n.getName();
            }
        }
        return name;
    }
    public static String toTranslate(List<Translate> translateNames){
        String name = "";
        if (translateNames == null){
            return name;
        }
        for (Translate n : translateNames){
            if (n.getLanguageId() == 228){
                name =  n.getName();
            }
        }
        return name;
    }
}
