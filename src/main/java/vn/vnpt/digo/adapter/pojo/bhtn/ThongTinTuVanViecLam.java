/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.pojo.bhtn;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThongTinTuVanViecLam implements Serializable {

    @JsonProperty("mucLuong")
    public Float mucLuong;

    @JsonProperty("lyDoNghi")
    public String lyDoNghi;

    @JsonProperty("daTimViecTai")
    public String daTimViecTai;

    @JsonProperty("kinhNghiem")
    public String kinhNghiem;

    @JsonProperty("kyNangNghe")
    public String kyNangNghe;

    @JsonProperty("khaNangNoiTroi")
    public String khaNangNoiTroi;

    @JsonProperty("idCongViec")
    public Integer idCongViec;

    @JsonProperty("idMucLuong")
    public Integer idMucLuong;

    @JsonProperty("idViTriCongViec")
    public Integer idViTriCongViec;

    @JsonProperty("maTinh")
    public String maTinh;

    @JsonProperty("maHuyen")
    public String maHuyen;

    @JsonProperty("idLoaiDoanhNghiep")
    public Integer idLoaiDoanhNghiep;

    @JsonProperty("dieuKienLv")
    public String dieuKienLv;

    @JsonProperty("khac")
    public String khac;

    @JsonProperty("uuidNguoiNhan")
    public String uuidNguoiNhan;

    @JsonProperty("idVeTinh")
    public Integer idVeTinh;

    @JsonProperty("idNguoiNhan")
    public String idNguoiNhan;

}
