/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CategoryResultResponse implements Serializable {
    @JsonProperty("MaKetQua")
    private String MaKetQua;

    @JsonProperty("TenGiayTo")
    private String TenGiayTo;

    @JsonProperty("SoKyHieu")
    private String SoKyHieu;

    @JsonProperty("CoQuanChuQuan")
    private String CoQuanChuQuan;

    @JsonProperty("DanhSachTepDinhKem")
    private List<CategoryResultResponseFile> DanhSachTepDinhKem;

}
