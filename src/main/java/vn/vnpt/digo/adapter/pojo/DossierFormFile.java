package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DossierFormFile implements Serializable {
    private ObjectId id;

    private vn.vnpt.digo.adapter.pojo.Id dossier;

    private ProcedureForm procedureForm;

    private int order;
    private Object authentication;

    private Object caze;

    private Object detail;

    private ArrayList<String> urlFile;

    private ArrayList<FileOCR> file;

    private  Boolean isCheckFileKiosk;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

    private ObjectId deploymentId;

    private Boolean isDirectFormFile;
}
