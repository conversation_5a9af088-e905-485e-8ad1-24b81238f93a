package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.UUID;


@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileOCR implements Serializable {

    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String filename;
    private Integer size;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId storageFileId;
    private UUID uuid;
    private int additionalFlag = 0;
    private int updateCount = 0;
    private Integer isNational;
    private Integer reused;
    private String repCode;
    private String downloadURL = "";
}