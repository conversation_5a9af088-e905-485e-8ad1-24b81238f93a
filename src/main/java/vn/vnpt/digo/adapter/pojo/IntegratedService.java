/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;
import vn.vnpt.digo.adapter.document.IntegrationService;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IntegratedService implements Serializable {
    
    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String name;

    public IntegratedService(IntegrationService sv){
        this.id = sv.getId();
        this.name = sv.getName();
    }
}
