/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.pojo.tbnohtttl_qni;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThongTinCongTrinh implements Serializable {

    @NotNull
    @JsonProperty("DiaDiemXayDung")
    public String diaDiemXayDung;

    @NotNull
    @JsonProperty("LoDatSo")
    public String loDatSo;

    @NotNull
    @JsonProperty("DienTich")
    public String dienTich;

    @NotNull
    @JsonProperty("MaTinhThanh")
    public String maTinhThanh;

    @NotNull
    @JsonProperty("MaQuanHuyen")
    public String maQuanHuyen;

    @NotNull
    @JsonProperty("MaPhuongXa")
    public String maPhuongXa;

    @NotNull
    @JsonProperty("SoNhaDuongPho")
    public String soNhaDuongPho;

}
