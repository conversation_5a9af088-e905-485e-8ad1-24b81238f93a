/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.pojo.vbdlis;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DanhSachGiayTo implements Serializable {

    @NotNull
    @JsonProperty("TenGiayTo")
    public String tenGiayTo;

    @JsonProperty("SoBanChinh")
    public Integer soBanChinh;

    @JsonProperty("SoBanSao")
    public Integer soBanSao;

    @JsonProperty("TapTin")
    public TapTin tapTin;

}
