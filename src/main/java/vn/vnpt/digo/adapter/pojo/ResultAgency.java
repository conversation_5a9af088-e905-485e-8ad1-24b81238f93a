package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultAgency implements Serializable {

    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    @JsonProperty("name")
    private String transName;

    @JsonIgnore
    private List<TransResultAgency> trans;

    @JsonGetter("name")
    public void setTransName(String name){
        this.transName = name;
    }
    
    public void setValue(Short localeId) {
        if (Objects.nonNull(trans)) {
            trans.forEach(tran -> {
                if (tran.getLanguageId().equals(localeId)) {
                    this.transName = tran.getName();
                }
            });
        }
    }
}
