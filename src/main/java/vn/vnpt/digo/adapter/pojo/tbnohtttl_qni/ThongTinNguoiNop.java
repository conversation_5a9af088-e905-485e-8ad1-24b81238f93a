/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.pojo.tbnohtttl_qni;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.vbdlis.TapTin;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThongTinNguoiNop implements Serializable {

    @NotNull
    @JsonProperty("HoTenNguoiNop")
    public String hoTenNguoiNop;

    @JsonProperty("SoCMND")
    public String soCMND;

    @JsonProperty("EmailNguoiNop")
    public String emailNguoiNop;

    @NotNull
    @JsonProperty("SoDienThoaiNguoiNop")
    public String soDienThoaiNguoiNop;

    @NotNull
    @JsonProperty("DiaChiThuongTruNguoiNop")
    public String diaChiThuongTruNguoiNop;

}
