
package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierReportResult implements Serializable {
    @JsonProperty("TenGiayTo")
    private String tenGiayTo;
    
    @JsonProperty("MaThanhPhanHoSo")
    private String maThanhPhanHoSo;
    
    @JsonProperty("GiayToId")
    private String giayToId;
    
    @JsonProperty("DuongDanTepTinKetQua")
    private String duongDanTepTinKetQua;
}
