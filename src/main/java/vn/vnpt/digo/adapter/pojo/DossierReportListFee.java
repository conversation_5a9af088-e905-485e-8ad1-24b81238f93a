
package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierReportListFee implements Serializable {
    @JsonProperty("TenPhiLePhi")
    private String tenPhiLePhi;
    
    @JsonProperty("MaPhiLePhi")
    private String maPhiLePhi;
    
    @JsonProperty("HinhThucThu")
    private String hinhThucThu;
    
    @JsonProperty("Gia")
    private String gia;
    
    @JsonProperty("LoaiPhiLePhi")
    private String LoaiPhiLePhi;
}
