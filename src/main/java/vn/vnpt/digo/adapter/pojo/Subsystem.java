/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;
import vn.vnpt.digo.adapter.dto.SubsystemDto;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Subsystem implements Serializable {

    @Field("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String code;

    @JsonIgnore
    private List<SubsystemTrans> trans;

    private String name;

    private String description;

    public Subsystem(ObjectId id, List<SubsystemTrans> trans) {
        this.id = id;
        this.trans = trans;
    }

    public Subsystem(SubsystemDto subsystem, Short languageId) {
        id = subsystem.getId();
        if (Objects.isNull(this.trans)) {
            this.trans = new ArrayList<>();
        }
        this.trans.add(new SubsystemTrans(languageId, subsystem.getName(), null));
    }

    public void setValue(Short languageId) {
        trans.forEach(tran -> {
            if (Objects.equals(tran.getLanguageId(), languageId)) {
                this.name = tran.getName();
                this.description = tran.getDescription();
            }
        });
    }

    public boolean equalsLanguage(Short localeId) {

        for (SubsystemTrans item : this.trans) {
            if (Objects.equals(localeId, item.getLanguageId())) {
                return true;
            }
        }

        return false;
    }
}
