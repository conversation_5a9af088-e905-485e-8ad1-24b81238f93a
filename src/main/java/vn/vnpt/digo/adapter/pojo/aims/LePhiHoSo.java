package vn.vnpt.digo.adapter.pojo.aims;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LePhiHoSo {
    @NonNull
    @JsonProperty("tenPhiLePhi")
    private String tenPhiLePhi;

    @JsonProperty("maPhiLePhi")
    private String maPhiLePhi;

    @NonNull
    @JsonProperty("hinhThucThu")
    public Integer hinhThucThu;

    @NonNull
    @JsonProperty("gia")
    public Integer gia;

    @NonNull
    @JsonProperty("loaiPhiLePhi")
    public Integer loaiPhiLePhi;
}
