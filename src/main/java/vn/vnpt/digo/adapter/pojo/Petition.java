package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Petition implements Serializable {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String code; //

    private String title;

    private String description;

    private TakePlaceAt takePlaceAt;

    private ReporterLocation reporterLocation;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date takePlaceOn;

    private Integer status = 1;

    private Integer confirm = 0;

    private PetitionTag tag;

    private List<File> file;

    private Reporter reporter;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId thumbnailId;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate = new Date();

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate = new Date(); //

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date dueDate = new Date(); //

    private Boolean isPublic = false;

    private Boolean isAnonymous = false;

    private Result result;

    private String processInstanceId;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;

    private Agency agency;

    private ReceptionMethod receptionMethod;

    private String step = "";

    private Satisfied reaction = new Satisfied();

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date feedbackDate = new Date();

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId callId;
    
    private Integer progress = 0;
    
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId typeRequest;

    private String typeRequestName;

    private NpsSync sync;
    
    public String getReporterEmail() {
        return this.reporter.getEmail();
    }
}
