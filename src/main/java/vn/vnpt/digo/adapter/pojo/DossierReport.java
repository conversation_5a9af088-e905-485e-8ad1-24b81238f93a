/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.dbn.statistic.ReportDossierDto;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierReport implements Serializable {
    private String name;
    private String code;
    private Number tiepNhanTrongKy; 
    private Number kyTruocChuyenSang;
    private Number tongXuLy;
    private Number dangXuLyTrongHan;
    private Number dangXuLyQuaHan;
    private Number daXuLyDungHan;
    private Number daXuLyQuaHan;
    private Number tamDungXuLy;
    private Number traLai;

    public void setData(ReportDossierDto dossierDto) {
        if (Objects.isNull(dossierDto)) {
            this.tiepNhanTrongKy = 0;
            this.kyTruocChuyenSang = 0;
            this.tongXuLy = 0;
            this.dangXuLyTrongHan = 0;
            this.dangXuLyQuaHan = 0;
            this.daXuLyDungHan = 0;
            this.daXuLyQuaHan = 0;
            this.tamDungXuLy = 0;
            this.traLai = 0;
        } else {
            this.tiepNhanTrongKy = dossierDto.getOnline_receiver() + dossierDto.getOffline_receiver();
            this.kyTruocChuyenSang = dossierDto.getMove_receiver();
            this.tongXuLy = dossierDto.getTotal() + dossierDto.getTotal_complete();
            this.dangXuLyTrongHan = dossierDto.getOn_time();
            this.dangXuLyQuaHan = dossierDto.getOver_due();
            this.daXuLyDungHan = dossierDto.getEarly_complete() + dossierDto.getOn_time_complete();
            this.daXuLyQuaHan = dossierDto.getDue_complete();
            this.tamDungXuLy = dossierDto.getReturn_dossier();
            this.traLai = dossierDto.getWithdraw();
        }
    }
}
