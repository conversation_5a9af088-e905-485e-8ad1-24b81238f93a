package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierFee implements Serializable {

    private ObjectId id;

    private Id dossier;

    private DossierPayment.Procost procost;

    private Double amount;

    private int quantity;

    private Double paid;
    private Double currencyRate = null;

    private int required = 0;

    private ObjectId deploymentId;

    private Boolean recover;

    private ArrayList<IdDto> oldFeeId;

    private Boolean isSyncFeeVneid = false;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

    private String extent_hgi;

    public DossierFee(DossierPayment.Procost procost, Double amount, Integer quantity, Id id) {
        this.dossier = id;
        this.procost = procost;
        this.amount = amount;
        this.quantity = quantity;
        this.paid = 0D;
        this.createdDate = new Date();
        this.updatedDate = new Date();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class Id implements Serializable {
        private ObjectId id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class IdDto implements Serializable {
        private ObjectId id;
    }
}
