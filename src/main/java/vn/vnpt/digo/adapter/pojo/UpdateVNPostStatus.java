
package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import org.springframework.data.mongodb.core.mapping.Field;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateVNPostStatus implements Serializable {
    @Field("id")
    @JsonProperty("id")
    private String id;
    private String customerCode;
    private String statusMessage;
    private Number statusCode;
    private DataPost data;
    private String itemCode;
    private String soDonHang;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataPost {       
        private String customerCode;
        private String orderNumber;
        private String cODAmount;
        private String senderProvince;
        private String senderDistrict;
        private String senderAddress;
        private String senderName;
        private String senderEmail;
        private String senderTel;
        private String senderDesc;
        private String description;
        private String receiverName;
        private String receiverAddress;
        private String receiverTel;
        private String receiverProvince;
        private String receiverDistrict;
        private String receiverEmail;
    }    
}
