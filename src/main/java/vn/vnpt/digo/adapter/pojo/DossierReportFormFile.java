
package vn.vnpt.digo.adapter.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierReportFormFile implements Serializable {
    @JsonProperty("TepDinhKemId")
    private String tepDinhKemId;
    
    @JsonProperty("TenTepDinhKem")
    private String tenTepDinhKem;
    
    @JsonProperty("IsDeleted")
    private String isDeleted;
    
    @JsonProperty("MaThanhPhanHoSo")
    private String maThanhPhanHoSo;
    
    @JsonProperty("DuongDanTaiTepTin")
    private String duongDanTaiTepTin;
}
