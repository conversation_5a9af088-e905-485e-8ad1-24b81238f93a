package vn.vnpt.digo.adapter.pojo.enterprise_tax;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class EnterpriseTaxResultData implements Serializable {
    private String status;
    private String errorMessage;
    private ResultData data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Document(collection = "enterpriseTax")
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResultData implements Serializable {
        @Id
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        @JsonProperty("MaHoSo")
        private String MaHoSo;

        @JsonProperty("ThongTinChungTu")
        private List<Cert> ThongTinChungTu = new ArrayList<>();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Cert implements Serializable {
        @JsonProperty("MaSoThue")
        private String MaSoThue;

        @JsonProperty("ChungTu")
        private List<CertData> ChungTu = new ArrayList<>();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CertData implements Serializable {
        @JsonProperty("MaThamChieu")
        private String MaThamChieu;

        @JsonProperty("URLXML")
        private String URLXML;

        @JsonProperty("URLPDF")
        private String URLPDF;

        @JsonProperty("NgayNopThue")
        private String NgayNopThue;

        @JsonProperty("SoTien")
        private String SoTien;
    }
}

