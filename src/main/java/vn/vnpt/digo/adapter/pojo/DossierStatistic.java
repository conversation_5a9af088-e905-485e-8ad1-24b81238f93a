/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.dbn.statistic.ReportDossierDto;

import java.io.Serializable;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierStatistic implements Serializable {
    private String name;
    private String code;
    private String parentCode;
    private String typeCode;
    private Number tiepNhanTrongKy; 
    private Number kyTruocChuyenSang;
    private Number tongXuLy;
    private Number dangXuLyTrongHan;
    private Number dangXuLyQuaHan;
    private Number daXuLyDungHan;
    private Number daXuLyQuaHan;
    private Number tamDungXuLy;
    private Number traLai;

    public void setData(ReportDossierDto dossierDto) {
        this.name = dossierDto.getAgency().getName();
        this.code = Objects.nonNull(dossierDto.getAgency().getCode()) ? dossierDto.getAgency().getCode() : "";
        this.parentCode = Objects.nonNull(dossierDto.getAgency().getParentCode()) ? dossierDto.getAgency().getParentCode() : "";
        this.typeCode = dossierDto.getAgency().getTypeCode();
        this.tiepNhanTrongKy = dossierDto.getOnline_receiver() + dossierDto.getOffline_receiver();
        this.kyTruocChuyenSang = dossierDto.getMove_receiver();
        this.tongXuLy = dossierDto.getTotal() + dossierDto.getTotal_complete();
        this.dangXuLyTrongHan = dossierDto.getOn_time();
        this.dangXuLyQuaHan = dossierDto.getOver_due();
        this.daXuLyDungHan = dossierDto.getEarly_complete() + dossierDto.getOn_time_complete();
        this.daXuLyQuaHan = dossierDto.getDue_complete();
        this.tamDungXuLy = dossierDto.getReturn_dossier();
        this.traLai = dossierDto.getWithdraw();
    }
}
