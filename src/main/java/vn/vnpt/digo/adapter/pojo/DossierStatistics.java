
package vn.vnpt.digo.adapter.pojo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierStatistics implements Serializable {
    private Temp procedure;
    private Temp sector;

    private Number tiepNhanTrongKy;
    private Number tiepNhanTrucTuyen;
    private Number tiepNhanTrucTiep;
    private Number kyTruocChuyenSang;
    private Number daXuLy; // tongXuLy
    private Number daXuLyTruocHan;
    private Number daXuLyDungHan;
    private Number daXuLyQuaHan;
    private Number dangXuLy;
    private Number dangXuLyTrongHan;
    private Number dangXuLyQuaHan;
    private Number daHuy; // traLai
    private Number dungXuLy;
    private Number tamDungXuLy;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Temp {
        private String id;
        private String code;
        private String name;
    }

}
