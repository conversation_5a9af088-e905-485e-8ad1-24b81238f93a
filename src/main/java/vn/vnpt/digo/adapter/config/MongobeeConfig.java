/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.config;

import com.github.mongobee.Mongobee;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.mongodb.core.MongoTemplate;

/**
 *
 * <AUTHOR>
 */
@RefreshScope
@Configuration
public class MongobeeConfig {
    
    @Value("${spring.data.mongodb.uri}")
    private String dburi;
    
    @Value("${mongobee.enable}")
    private boolean enable;
    
    @Value("${mongobee.change-logs-scan-package}")
    private String changeLogsScanPackage;
    
    @Value("${mongobee.change-log-collection-name:mongobeeChangeLogs}")
    private String changeLogCollectionName;
    
    @Value("${mongobee.lock-collection-name:mongobeeLock}")
    private String lockCollectionName;
    
    @Bean
    @Autowired
    public Mongobee mongobee(MongoTemplate mongoTemplate, Environment environment) {
        Mongobee runner = new Mongobee(dburi);
        runner.setEnabled(enable);
        runner.setChangeLogsScanPackage(changeLogsScanPackage);
        runner.setChangelogCollectionName(changeLogCollectionName);
        runner.setLockCollectionName(lockCollectionName);
        runner.setMongoTemplate(mongoTemplate);
        runner.setSpringEnvironment(environment);
        return runner;
    }
    
}
