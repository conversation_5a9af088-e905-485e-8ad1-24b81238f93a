package vn.vnpt.digo.adapter.schedule;

import lombok.NoArgsConstructor;
import org.apache.kafka.common.protocol.types.Field;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.HistorySendTanDanDoc;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.tandan.ListDataResponseDto;
import vn.vnpt.digo.adapter.service.HistorySendTanDanDocService;
import vn.vnpt.digo.adapter.service.IntegratedConfigurationService;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Component
@NoArgsConstructor
@Service
public class LGSPTanDanSchedule {
    private ObjectId serviceId = new ObjectId("5f7c16069abb62f511890028");
    @Autowired
    private HistorySendTanDanDocService historySendTanDanDocService;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private RestTemplate restTemplate;

    private HashMap<String, String> getExtendHeaders(String token) {
        HashMap<String, String> exHeaders = new HashMap<String, String>();
        exHeaders.put("Authorization", "Bearer " + token);
        return exHeaders;
    }

    //@Scheduled(fixedRate = 2000)
    public void receivingDocument(){
        List<HistorySendTanDanDoc> units = historySendTanDanDocService.getUnits();
        for (HistorySendTanDanDoc unit:units){
            IntegratedConfigurationDto config = configurationService.getConfig(unit.getConfigId());

            String token = config.getParametersValue("access_token");
            String gateway = config.getParametersValue("gateway");
            HashMap<String, String> headers = getExtendHeaders(token);
            String endPoint = config.getParametersValue("getMsgDocument");
            String URL = gateway + endPoint + unit.getUnitCode();
            ListDataResponseDto data = MicroserviceExchange.get(this.restTemplate,URL, ListDataResponseDto.class);
            System.out.println();
        }
    }
}
