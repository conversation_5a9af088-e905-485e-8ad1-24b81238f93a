package vn.vnpt.digo.adapter.service;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import vn.vnpt.digo.adapter.dto.AffectedMessageDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.SidNameDto;
import vn.vnpt.digo.adapter.dto.ag_esb.businessstatus.BusinessDossierAGESBDto;
import vn.vnpt.digo.adapter.dto.ag_esb.businessstatus.BusinessDossierResDto;
import vn.vnpt.digo.adapter.dto.ag_esb.card.CheckCardIdRequest;
import vn.vnpt.digo.adapter.dto.ag_esb.card.CheckCardIdResponse;
import vn.vnpt.digo.adapter.dto.dbn.TokenResDto;
import vn.vnpt.digo.adapter.dto.event_log.PostEventLogDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.*;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
@Service
public class AGGDkkdAgesbService
{
    Logger logger = LoggerFactory.getLogger(AGGDkkdAgesbService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Value("${digo.upload-by-url-whitelist}")
    private String uploadWhitelist;

    IntegratedConfigurationDto config;

    @Autowired
    private EventLogService eventLogService;
    private final String SUCCESS_VAL = "1";
    private final String FAILURE_VAL = "0";
    private int STATUS_CODE = 200;
    private Boolean IS_CHECK_STATUS=false;
    private String RESPONSE_TEXT = "OK";

    public BusinessDossierResDto writeLogDKKD(HttpServletRequest request, BusinessDossierAGESBDto body, String code) {
        PostEventLogDto event = new PostEventLogDto();
        String inputXML=null;
        StringBuilder stringBuilder =new StringBuilder();
        event.setRequestAdapter(request, body, "ad");
        event.setServiceId(body.getConfigId());
        event.setKey(new SidNameDto("Code", body.getMaHoSo()));
        try {
            BusinessDossierResDto response;
            response = registerBusiness(body);
            inputXML=response.getInputXml();
            event.setRequestAdapter(request, response.getInputXml(), "ad");
            stringBuilder.append("Trạng thái liên thông đăng ký kinh doanh:(\n");
            stringBuilder.append("Trạng thái liên thông: ").append(response.getStatusDescription()).append(",\n");
            if(response.getStatus()==1)
            {
                stringBuilder.append("Mã trạng thái phản hồi: ").append(response.getStatus()).append(",\n");
                stringBuilder.append("Nội dung phản hồi: ").append(response.getValue()).append(")\n");
                event.setStatus(true);
            }
            else if(response.getStatus()==-1)
            {
                stringBuilder.append("Mã lỗi: ").append(response.getErrorCode()).append(",\n");
                stringBuilder.append("Mô tả lỗi: ").append(response.getErrorDescription()).append(")\n");
                event.setStatus(false);
            }
            event.setErrMsg(stringBuilder.toString());
            eventLogService.addNewAGG(event);
            logger.info("DIGO-Response: " + response);
            return response;
        } catch (Exception e) {
            event.setRequestAdapter(request, inputXML != null ? inputXML: body, "ad");
            event.setStatus(false);
            stringBuilder.append("Trạng thái liên thông đăng ký kinh doanh:(\n");
            stringBuilder.append("Trạng thái liên thông: ").append("Liên thông đăng ký kinh doanh thất bại!").append(",\n");
            stringBuilder.append("Mã lỗi: ").append(500).append(",\n");
            stringBuilder.append("Mô tả lỗi: ").append(e.getMessage()).append(")\n");
            event.setErrMsg(stringBuilder.toString());
            eventLogService.addNewAGG(event);
            Map<String, String> map = new HashMap<>();
            map.put("statusMessage", "Có lỗi khi gửi sang trục AG ESB!");
            map.put("statusCode", "-9999");
            updateDossierStatus(map,code);
            return new BusinessDossierResDto();
        }
    }

    public BusinessDossierResDto registerBusiness(BusinessDossierAGESBDto req) throws Exception
    {
        try {
            logger.info("---------------------------START_GET_REGISTER_BUSINESS_AG_ESB---------------------------");
            if (Objects.nonNull(req.getConfigId())) {
                config = configurationService.getConfig(req.getConfigId());
            }
            if (Objects.isNull(config)) {
                throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
            }
            String gatewayToken = config.getParametersValue("gateway-token");
            String consumerKey = config.getParametersValue("consumer-key");
            String consumerSecret = config.getParametersValue("consumer-secret");
            String registerCivilUrl  = config.getParametersValue("registerUrl");
            TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);
            return dangKyKinhDoanh (registerCivilUrl,  token.getAccessToken(), req);
        }
        catch (Exception e)
        {
            throw new Exception(e.getMessage());
        }
    }

    private BusinessDossierResDto dangKyKinhDoanh (String apiUrl, String access_token, BusinessDossierAGESBDto dangKyKinhDoanh ) throws Exception {
        try {
            BusinessDossierResDto objreturn = new BusinessDossierResDto();
            URL url = new URL(apiUrl);
            String auth = "Bearer " + access_token;
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            configureConnection(connection,auth);
            String xmlObject = buildXmlString(dangKyKinhDoanh );
            String formatXml=formatXml(xmlObject);
            objreturn.setInputXml(formatXml);
            sendRequest(connection, formatXml);
            String responsedContent = readResponse(connection);
            String formatXMLResonse=formatXml(responsedContent);
            processXmlResponse(formatXMLResonse, objreturn, dangKyKinhDoanh );
            return objreturn;
        }catch (Exception e)
        {
            throw new Exception(e.getMessage());
        }
    }
    public TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) throws Exception {
        String strConsumer = consumerKey + ":" + consumerSecret;
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(tokenUrl);
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();
        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(auth);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST, request, Object.class);
            logger.info("Http result:");
            System.out.println(result);
            TokenResDto token = GsonUtils.copyObject(result.getBody(), TokenResDto.class);
            return token;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    private void configureConnection(HttpURLConnection connection,String auth) throws ProtocolException {
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "text/xml");
        connection.setRequestProperty("Accept-Charset", "UTF-8");
        connection.setRequestProperty("SOAPAction", "http://angiang.gov.vn/operation/agesb/GuiHoSoMoi");
        connection.setRequestProperty("Authorization", auth);
        connection.setDoOutput(true);
        connection.setDoInput(true);
    }

    private String buildXmlString(BusinessDossierAGESBDto dangKyKinhDoanh ) throws Exception {
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("Asia/Ho_Chi_Minh"));
        long timestamp = now.toEpochSecond();  // Lấy timestamp dưới dạng seconds
        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
        String moduleValue = dangKyKinhDoanh .getModule();
        String maDonViGui = dangKyKinhDoanh .getMaDonViGui();
        String dataMappingEgency=null;
        if(dangKyKinhDoanh .getListOrganId().containsKey(maDonViGui))
        {
            dataMappingEgency=dangKyKinhDoanh .getListOrganId().get(maDonViGui);
        }
        Document doc = dBuilder.newDocument();
        Element envelope = doc.createElement("soapenv:Envelope");
        envelope.setAttribute("xmlns:soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
        envelope.setAttribute("xmlns:edx", "http://angiang.gov.vn/schemas/agesb/edXML");
        envelope.setAttribute("xmlns:ages", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        envelope.setAttribute("xmlns:oneg", "http://angiang.gov.vn/schemas/agesb/onegateXML");
        doc.appendChild(envelope);
        Element header = doc.createElement("soapenv:Header");
        envelope.appendChild(header);
        Element messageHeader = doc.createElement("ns3:MessageHeader");
        messageHeader.setAttribute("xmlns:ns3", "http://angiang.gov.vn/schemas/agesb/edXML");
        header.appendChild(messageHeader);
        Element from = doc.createElement("ns3:From");
        messageHeader.appendChild(from);
        Element organId = doc.createElement("ns3:OrganId");
        organId.appendChild(doc.createTextNode( getValueOrDefault(dataMappingEgency,"rỗng")));
        from.appendChild(organId);
        Element fieldCode = doc.createElement("ns2:FieldCode");
        fieldCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        fieldCode.appendChild(doc.createTextNode(dangKyKinhDoanh.getFiledcodeDKKD()));
        messageHeader.appendChild(fieldCode);
        if(dangKyKinhDoanh .getProcesscodeDKKD().containsKey(moduleValue))
        {
            Element processCode = doc.createElement("ns2:ProcessCode");
            processCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
            processCode.appendChild(doc.createTextNode(dangKyKinhDoanh .getProcesscodeDKKD().get(moduleValue)));
            messageHeader.appendChild(processCode);
        }
        Element body = doc.createElement("soapenv:Body");
        envelope.appendChild(body);
        Element messageBody = doc.createElement("edx:MessageBody");
        body.appendChild(messageBody);
        Element businessData = doc.createElement("ns2:BusinessData");
        businessData.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        businessData.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
        businessData.setAttribute("xsi:type", "ns2:BusinessData");
        messageBody.appendChild(businessData);
        Element thongTinHoSo = doc.createElement("ns1:ThongTinHoSo");
        thongTinHoSo.setAttribute("xmlns:ns1", "http://angiang.gov.vn/schemas/agesb/onegateXML");
        businessData.appendChild(thongTinHoSo);
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:MaBNHS", getValueOrDefault(dangKyKinhDoanh .getMaHoSo(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenToChuc", getValueOrDefault(dangKyKinhDoanh .getTenToChuc(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenNguoiNop", getValueOrDefault(dangKyKinhDoanh .getTenNguoiNop(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DiaChiNguoiNop", getValueOrDefault(dangKyKinhDoanh .getDiaChiNguoiNop(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:SoCMND", getValueOrDefault(dangKyKinhDoanh .getSoCMND(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DienThoai", getValueOrDefault(dangKyKinhDoanh .getDienThoai(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenDVCong", getValueOrDefault(dangKyKinhDoanh .getTenDichVuCong(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:NgayNhanHS", getValueOrDefaultDate(dangKyKinhDoanh .getNgayNhanHS())));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:NgayHenTraHS", getValueOrDefaultDate(dangKyKinhDoanh .getNgayHenTraHS())));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TinhTrangHS", getValueOrDefault(dangKyKinhDoanh .getTinhTrangHS(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TrangThaiXuLy", getValueOrDefaultInteger(dangKyKinhDoanh .getTrangThaiXuLy()).toString()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:PhongBanXuLy", getValueOrDefault(dangKyKinhDoanh .getPhongBanXuLy(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:CVXuLy", getValueOrDefault(dangKyKinhDoanh .getChuyenVienXuLy(), "rỗng")));
        ObjectMapper mapper = new ObjectMapper();
        dangKyKinhDoanh .getDataEform().put("idReceivedDec",dangKyKinhDoanh .getIdReceivedDec());
        dangKyKinhDoanh .getDataEform().put("dateReceivedDec",dangKyKinhDoanh .getDateReceivedDec());
        dangKyKinhDoanh .getDataEform().put("datePromissoryDec",dangKyKinhDoanh .getDatePromissoryDec());
        String jsonResult = mapper.writeValueAsString(dangKyKinhDoanh .getDataEform());
        // Thêm phần DuLieuChuyenNganh
        String duLieuChuyenNganhContent = jsonResult;
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DuLieuChuyenNganh", duLieuChuyenNganhContent));
        Element contentType = doc.createElement("ContentType");
        contentType.appendChild(doc.createTextNode("application/zip"));
        thongTinHoSo.appendChild(contentType);
        Element value = doc.createElement("Value");
        value.appendChild(doc.createTextNode(getFileAttachment(dangKyKinhDoanh.getListFileIds(), timestamp + "_" + dangKyKinhDoanh.getMaHoSo())));
        thongTinHoSo.appendChild(value);

        StringWriter stringWriter = new StringWriter();
        // Chuyển đổi DOM thành chuỗi
        javax.xml.transform.TransformerFactory.newInstance().newTransformer().transform(new javax.xml.transform.dom.DOMSource(doc), new javax.xml.transform.stream.StreamResult(stringWriter));
        return stringWriter.toString().replace("&gt;", ">").replace("&lt;", "<");
    }
    private String getFileAttachment(ArrayList<BusinessDossierAGESBDto.FileAttachment> listFiles, String zipFileName) throws IOException {
        // Nén dữ liệu vào file ZIP
        zipFileName = zipFileName.replaceAll("[^a-zA-Z0-9_.-]", "");
        try (FileOutputStream fos = new FileOutputStream(zipFileName);
             ZipOutputStream zipOut = new ZipOutputStream(fos)) {
            for (var file : listFiles) {
                // Thêm mỗi file vào trong file zip
                byte[] fileContent = MicroserviceExchange.getFile(restTemplate, microservice.filemanUri("file/" + file.getId()).toUriString());
                ZipEntry zipEntry = new ZipEntry(file.getFileName());
                zipOut.putNextEntry(zipEntry);
                zipOut.write(fileContent);
                zipOut.closeEntry();
            }
        }
        // Đọc nội dung file ZIP vào mảng byte
        byte[] zipFileBytes;
        try (FileInputStream fis = new FileInputStream(zipFileName);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            zipFileBytes = baos.toByteArray();
        }
        // Mã hóa mảng byte thành Base64
        String base64EncodedZip = Base64.getEncoder().encodeToString(zipFileBytes);
        // Xóa file ZIP
        File zipFile = new File(zipFileName);
        if (zipFile.exists()) {
            zipFile.delete();
        }
        return base64EncodedZip;
    }

    private String getValueOrDefault(String value, String defaultValue) {
        return (value != null) ? value : defaultValue;
    }
    private Integer getValueOrDefaultInteger(Integer value) {
        return (value != null) ? value : 0;
    }
    private static String getValueOrDefaultDate(Date value) {
        if (value != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return dateFormat.format(value);
        } else {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return dateFormat.format(new Date());
        }
    }
    private String formatXml(String unformattedXml) throws Exception {
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        StreamSource source = new StreamSource(new java.io.StringReader(unformattedXml));
        StringWriter resultWriter = new StringWriter();
        StreamResult result = new StreamResult(resultWriter);
        transformer.transform(source, result);
        return resultWriter.toString();
    }
    private Element createElementWithTextContent(Document doc, String elementName, String textContent) {
        Element element = doc.createElement(elementName);
        element.appendChild(doc.createTextNode(textContent));
        return element;
    }

    private void sendRequest(HttpURLConnection connection, String jsonObject) throws IOException {
        try (OutputStream outStream = connection.getOutputStream()) {
            outStream.write(jsonObject.getBytes());
        }
    }

    private String readResponse(HttpURLConnection connection) throws IOException {
        InputStream inputStream;
        try {
            inputStream = connection.getInputStream();
        } catch (IOException e) {
            inputStream = connection.getErrorStream();
        }
        if (inputStream != null) {
            try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                return bufferedReader.lines().collect(StringBuilder::new, StringBuilder::append, StringBuilder::append).toString();
            }
        } else {
            return "No response received";
        }
    }

    public AffectedMessageDto updateDossierStatus(Map response , String code) {
        String updateUrl = microservice.padmanUri("/agesb-dkkd/" + code + "/status-agesb-dkkd").toUriString();
        //String updateUrl = "http://localhost:8081/agesb-dkkd/"+code+"/status-agesb-dkkd";
        //String updateUrl = "http://localhost:8081/dossierAutoSync/"+code+"/status";
        logger.error("-----Update status dossier: " + updateUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(response, headers);
        AffectedMessageDto result = restTemplate.exchange(updateUrl, HttpMethod.PUT, request, AffectedMessageDto.class).getBody();
        return result;
    }
    private void processXmlResponse(String xmlResponse, BusinessDossierResDto objreturn, BusinessDossierAGESBDto dangKyHoTich) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        InputSource is = new InputSource(new StringReader(xmlResponse));
        Document document = builder.parse(is);
        if (document.getElementsByTagName("soapenv:Fault").getLength() > 0) {
            setXmlErrorResponseDetails(objreturn, xmlResponse, dangKyHoTich);
        } else {
            setXmlResponseDetails(objreturn, xmlResponse, dangKyHoTich);
        }
    }

    private void setXmlResponseDetails(BusinessDossierResDto objreturn, String xmlRespone, BusinessDossierAGESBDto dangKyKinhDoanh) {
        objreturn.setStatus(1);
        objreturn.setStatusDescription("Liên thông kinh doanh thành công!");
        objreturn.setValue(xmlRespone);
        Map<String, String> map = new HashMap<>();
        map.put("statusMessage", "Liên thông kinh doanh thành công!");
        map.put("statusCode", "1");
        map.put("statusMessageResponse","Đã tiếp nhận!");
        map.put("statusCodeResponse","1");
        updateDossierStatus(map,dangKyKinhDoanh.getMaHoSo());
    }

    private void setXmlErrorResponseDetails(BusinessDossierResDto objreturn, String xmlRespone, BusinessDossierAGESBDto dangKyKinhDoanh) {
        objreturn.setStatus(-1);
        objreturn.setStatusDescription("Liên thông kinh doanh thất bại!");
        objreturn.setErrorCode("500");
        objreturn.setErrorDescription(xmlRespone);
        Map<String, String> map = new HashMap<>();
        map.put("statusMessage", objreturn.getStatusDescription());
        map.put("statusCode","-1" );
        updateDossierStatus(map, dangKyKinhDoanh.getMaHoSo());
    }
    public CheckCardIdResponse checkCardId(CheckCardIdRequest checkCardIdRequest)  {
        List<String> whitelist = new ArrayList<>();

        try {
            whitelist = new ArrayList<String>(Arrays.asList(this.uploadWhitelist.split(";")));
            whitelist.removeAll(Arrays.asList("", null));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        if (whitelist.size() > 0) {
            URI uri = URI.create(checkCardIdRequest.getCheckCardIdUrl());
            String domain = uri.getHost();
            if (domain.toLowerCase().startsWith("www.")) {
                domain = domain.replaceFirst("^www\\.", "");
            }
            int count = 0;
            for (String url : whitelist) {
                if (domain.equalsIgnoreCase(url)) {
                    count++;
                }
            }
            if (count == 0) {
                throw new DigoHttpException(10448, HttpServletResponse.SC_BAD_REQUEST);
            }
        }
        CheckCardIdResponse checkCardIdResponse=new CheckCardIdResponse();
        if (checkCardIdRequest.getStrCardId() != null && !checkCardIdRequest.getStrCardId().isEmpty()) {
            try {
                CheckCardIdResponse response = isCitizenCardExisting(checkCardIdRequest);
                if (response != null) {
                    if (SUCCESS_VAL.equals(response.getResult())) {
                        STATUS_CODE = 200;
                        IS_CHECK_STATUS=true;
                        RESPONSE_TEXT = "Số CMND hợp lệ";

                    } else if (FAILURE_VAL.equals(response.getResult())) {
                        STATUS_CODE = 200;
                        IS_CHECK_STATUS=false;
                        RESPONSE_TEXT = "Số CMND đã tồn tại";
                    } else {
                        STATUS_CODE = 200;
                        IS_CHECK_STATUS=false;
                        RESPONSE_TEXT = "Không thể xác nhận thông tin";
                    }
                } else {
                    STATUS_CODE = 500;
                    IS_CHECK_STATUS=false;
                    RESPONSE_TEXT = "Không thể kiểm tra thông tin";
                }
            } catch (Exception ex) {
                STATUS_CODE = 500;
                IS_CHECK_STATUS=false;
                RESPONSE_TEXT = "Kiểm tra thông tin thất bại, Vui lòng thử lại sau!";
            }
        } else {
            STATUS_CODE = 500;
            IS_CHECK_STATUS=false;
            RESPONSE_TEXT = "Missing Information";
        }
        checkCardIdResponse.setResult(RESPONSE_TEXT);
        checkCardIdResponse.setStatusCode(STATUS_CODE);
        checkCardIdResponse.setIsCheckStatus(IS_CHECK_STATUS);
        System.out.print("Kết quả: "+STATUS_CODE+","+RESPONSE_TEXT);
        return checkCardIdResponse;
    }
    private CheckCardIdResponse isCitizenCardExisting(CheckCardIdRequest checkCardIdRequest)  {
        CheckCardIdResponse checkCardIdResponse = null;
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(checkCardIdRequest.getCheckCardIdUrl());
            String xmlRequest = createXmlRequest(checkCardIdRequest);
            StringEntity entity = new StringEntity(xmlRequest, "UTF-8");
            post.setHeader("Content-Type", "application/soap+xml; charset=utf-8");
            post.setEntity(entity);
            String response = EntityUtils.toString(client.execute(post).getEntity());
            checkCardIdResponse = parseXmlResponse(response);
            return checkCardIdResponse;
        } catch (Exception e) {

            return checkCardIdResponse;
        }
    }
    private static String createXmlRequest(CheckCardIdRequest checkCardIdRequest) throws ParserConfigurationException, TransformerException {
        DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder docBuilder = docFactory.newDocumentBuilder();
        Document doc = docBuilder.newDocument();
        Element envelope = doc.createElementNS("http://www.w3.org/2003/05/soap-envelope", "soap12:Envelope");
        envelope.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
        envelope.setAttribute("xmlns:xsd", "http://www.w3.org/2001/XMLSchema");
        doc.appendChild(envelope);
        Element body = doc.createElement("soap12:Body");
        envelope.appendChild(body);
        Element checkCardId = doc.createElementNS("http://tempuri.org/", "CheckCardId");
        body.appendChild(checkCardId);
        Element strCarId = doc.createElement("strCarId");
        strCarId.appendChild(doc.createTextNode(checkCardIdRequest.getStrCardId()));
        checkCardId.appendChild(strCarId);
        Element intHuyen = doc.createElement("intHuyen");
        intHuyen.appendChild(doc.createTextNode(String.valueOf(checkCardIdRequest.getIntDistrict())));
        checkCardId.appendChild(intHuyen);
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(doc), new StreamResult(writer));

        return writer.getBuffer().toString();
    }
    private CheckCardIdResponse parseXmlResponse(String xmlResponse) throws Exception {
        CheckCardIdResponse checkCardIdResponse=new CheckCardIdResponse();
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(new InputSource(new StringReader(xmlResponse)));
        NodeList nodeList = document.getElementsByTagName("CheckCardIdResult");
        if (nodeList.getLength() > 0) {
            String result = nodeList.item(0).getTextContent();
            if(result.equals("0"))
            {
               checkCardIdResponse.setResult("0");
            }
            else if(result.equals("1"))
            {
                checkCardIdResponse.setResult("1");
            }
            return  checkCardIdResponse;
        }
        return checkCardIdResponse;
    }


}
