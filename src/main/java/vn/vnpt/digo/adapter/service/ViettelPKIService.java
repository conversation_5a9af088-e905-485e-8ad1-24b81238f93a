/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.service;


import com.vnpt.vnptkyso.pdf.PdfHashSigner;
import com.vnpt.vnptkyso.pdf.PdfSignatureComment;
import com.vnpt.vnptkyso.pdf.PdfSignatureView;
import com.vnpt.vnptkyso.signer.*;
import com.vnpt.vnptkyso.utils.MessageDigestAlgorithm;
import org.apache.commons.codec.binary.Base64;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.ViettelPKIConfiguration;
import vn.vnpt.digo.adapter.dto.ViettelPKIDocumentDto;
import vn.vnpt.digo.adapter.dto.PostMultipleFileSignDto;
import vn.vnpt.digo.adapter.dto.PostHashFileResponseDto;
import vn.vnpt.digo.adapter.dto.IdNameFileDto;
import vn.vnpt.digo.adapter.dto.FileInfoDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.vnptsmartca.SignatureComment;
import vn.vnpt.digo.adapter.pojo.vnptsmartca.SignatureDocument;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.TimeZone;

/**
 *
 * <AUTHOR>
 */
@Service
public class ViettelPKIService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private Microservice microservice;
    
    @Autowired
    private Translator translator;
    
    @Autowired
    private IntegratedConfigurationService configurationService;

    
    private Logger logger = LoggerFactory.getLogger(ViettelPKIService.class);
    private final ObjectId serviceId = new ObjectId("62739fc4922aa6d0cda7d065"); // 
    private final ObjectId serviceIdQni = new ObjectId("62739fc4922aa6d0cda7d067");
    public PostMultipleFileSignDto signByFile(ObjectId fileId, ViettelPKIDocumentDto body)
            throws IOException, Exception{
        
        IntegratedConfigurationDto config;
        ObjectId userId = Context.getUserId();
        ObjectId deploymentId = Context.getDeploymentId();
        String fullname = Context.getUserFullname();
        if (Objects.nonNull(body.getConfigId())){
            config = configurationService.getConfig(body.getConfigId());
        } else{
            config = configurationService.getConfig(body.getAgencyId(),body.getSubsystemId(),this.serviceId);
        }
        

        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        String modeSync = config.getParameterValue("modeSync");
        String codeSign = config.getParameterValue("codeSign");
        String codeQueryCer = config.getParameterValue("codeQueryCer");
        String URL_WS = config.getParameterValue("urlWS");
        String privateKey = config.getParameterValue("privateKey");

        String appId = config.getParameterValue("appId").toString();
        String msspId = config.getParameterValue("msspId").toString();
        int forcePIN = config.getParameterValue("forcePin");
        int runImm = config.getParameterValue("runImm");
        int sigType = config.getParameterValue("signType");
        String hashType = config.getParameterValue("hashType");

        ViettelPKIConfiguration bodyData = new ViettelPKIConfiguration(modeSync, codeSign, codeQueryCer, URL_WS, privateKey,
                appId, msspId, forcePIN, runImm, sigType, hashType, body.getPhoneNumber(),
                body.getPositionX(), body.getPositionY(), body.getPage(), body. getName(),"");

        // T?o file sigh hash
         byte[] unsignedFile = new byte[0];
        String unsignedFilename = "";
        String unsignedFileContentType = MediaType.ALL_VALUE;
        
        try{
            String unsignedUrl = microservice.filemanUri("file/" + fileId.toHexString()).toUriString();
            ResponseEntity<byte[]> entity = MicroserviceExchange.getFileEntity(restTemplate, unsignedUrl);
            unsignedFile = entity.getBody();
            unsignedFilename = entity.getHeaders().getContentDisposition().getFilename();
            unsignedFileContentType = entity.getHeaders().getContentType().toString();
        } catch (Exception e){
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.phrase.file-to-sign")}, HttpServletResponse.SC_NOT_FOUND);
        }
//         // Get sign image
//        if(Objects.nonNull(body.getSignature())){
//            UserSignDto userSign = this.getUserSign(userId);
//            if(Objects.nonNull(userSign)){
//                for (var item: CollectionUtils.emptyIfNull(userSign.getSignature())){
//                    if(item.getId().equals(body.getSignature().getId())){
//                        fullname = item.getName();
//                        try{
//                            String signImageUrl = microservice.filemanUri("file/" + item.getImage().getId().toHexString()).toUriString();
//                            ResponseEntity<byte[]> entity = MicroserviceExchange.getFileEntity(restTemplate, signImageUrl);
//                            byte[] contentFile = entity.getBody();
//                            signImage = Base64.encodeBase64String(contentFile);
//                        } catch (HttpClientErrorException | NullPointerException e){
//                            logger.info("Can not get signature image");
//                            logger.info(e.getMessage());
//                        }
//                    }
//                }
//            } 
//        }
        String base64Cert = this.getViettelCertBase(bodyData);
        String fileType = "pdf";
          String _signedFilename;
        if(!unsignedFilename.endsWith("_signed.pdf")){
            _signedFilename = unsignedFilename.replaceAll(".pdf", "") + "_signed.pdf";
        }else{
            _signedFilename = unsignedFilename;
        }
        switch(unsignedFileContentType){
            case MediaType.APPLICATION_PDF_VALUE:
                fileType = "pdf";
            case MediaType.APPLICATION_XML_VALUE: 
                break;           
        }
        String hashValue = "";
         SignatureDocument document = new SignatureDocument();
        //document.setFontSize(signFontSize);
        //document.setFontName(signFontFamily);
        //document.setTextColor(signFontColor);
        //document.setFontStyle(signFontStyle);
        document.setFileContent(unsignedFile);
        document.setFileName(unsignedFilename);
        document.setFileType(fileType);
        //document.setSignImage(signImage);
        document.setHashAlgorithm(hashType);
        document.setFullName(fullname);
        document.setPage(body.getPage());
        document.setVisibleType("TEXT_ONLY");
       // document.setReason(body.getReason());
//       String postion =  body.getPositionX()+","
//               + body.getPositionY() +",60,30";
        document.setPosition(body.getPosition());
        
        IHashSginer signer = getHashSignerPdf(document, base64Cert);
        if(signer == null) {
            return null;
        }

        try {
            hashValue = signer.getSecondHashAsBase64();
        } catch (HashSignerException ex) {
            logger.error(ex.getMessage());
        }
        bodyData.setHashValue(hashValue);
        PostMultipleFileSignDto response = new PostMultipleFileSignDto();
        PostHashFileResponseDto responseVT = new PostHashFileResponseDto();
        byte[] signedData = null;  
        try{
            String url =  microservice.filemanUri("file/" + fileId + "/--viettel-pki-sign").toUriString();
            responseVT = MicroserviceExchange.postPki(restTemplate, url, bodyData, PostHashFileResponseDto.class);
            
            signedData = signer.sign(responseVT.getResponse());
        } catch (Exception e){
            throw new DigoHttpException(11003, new String[] { e.getMessage() }, HttpServletResponse.SC_NOT_FOUND);
        }
         if(signedData == null || signedData.length == 0) {
            throw new DigoHttpException(11005, new String[]{translator.toLocale("lang.sentence.check-signature-config")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        //Upload signed file to fileman
        final String signedFilename = _signedFilename;
        ByteArrayResource contentsAsResource = new ByteArrayResource(signedData) {
            @Override
            public String getFilename() {
                return signedFilename;
            }
        };
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add("file", contentsAsResource);
        String postFileUrl = microservice.filemanUri("file/"+fileId+"/--update-signed-file").toUriString();
        IdNameFileDto res = MicroserviceExchange.putMultipart(restTemplate, postFileUrl, map, IdNameFileDto.class);
        res.setFilename(_signedFilename);
        res.setSize(contentsAsResource.contentLength());
        
        response.setFilename(_signedFilename);
        response.setSize(contentsAsResource.contentLength());
        response.setId(res.getId());
        response.setFileServer("");
        return response;
    }
    
    public PostMultipleFileSignDto signByFileQni(ObjectId fileId, ViettelPKIDocumentDto body)
            throws IOException, Exception{

        IntegratedConfigurationDto config;
        if (Objects.nonNull(body.getConfigId())){
            config = configurationService.getConfig(body.getConfigId());
        } else{
            config = configurationService.getConfig(body.getAgencyId(),body.getSubsystemId(),this.serviceId);
        }
       

        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        String modeSync = config.getParameterValue("modeSync");
        String codeSign = config.getParameterValue("codeSign");
        String codeQueryCer = config.getParameterValue("codeQueryCer");
        String URL_WS = config.getParameterValue("urlWS");
       // String URL_WS = "https://mobilepki.viettel.vn/apws.asmx?wsdl";
        //String privateKey = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBALhrHRACD4HditIrALvjCifPfA8SXB5o1d7c/zDW3t4jzlCfvjWXz9EjEx6tr7Q35NUS630CjZlBhQhJyWQUMMZMQEUHYzMnJNsFtobgK31MBgd5hnz/Ip9mGW+86H/M7PLgbBABSKJxvxl5wNoA1YGTv+GwKY7GEq6BoVWY57cNAgMBAAECgYBPvqmjiXCRNG5Z8RRxKnzyuAqqLcM9txF8PE3xHYjjv9HwFRNX81x+z87tviczI9REutivhtIaCjXvXpDCQOTRrKviv0QAwHqlw/OCeVYQ3dQZwcuL0TjodoC5HTBc/Ue1KBTUpvL6t7lSDOyk2oP0ZjLF9NpwhttnRyeWapDYlQJBAPuCbX6wrmjJutFah0k0ZbMRdagVwnuLNcGopBcs/2T96LuZDI6OkRTsRX7p/1npk5lIp1THNWDEMn7WT+5CATMCQQC7tgiAzehse1rmEptD42yoYoUZlDvSSy6Rk7aglDs9Y4XCjuBT8/wJhoGS++lWy9o60+j+56A3MPY1/K7fF+a/AkEA3aAjCF58cc5BlzWkvPc1HsQl34W9KMyVBB+s6Nc3/jBYIudMz1oFsmA5JQrhfsN60rnY6bXY7Xo13NrQ+GylTQJBAKR1oU+QmCFrlkFbbfN17z48SxcVG7BstFlRjMF1/c0URB4o5fxIGLsTwLTRxyeHpSTDgzDKKUIRwLWeNGw72asCQQDwEkrGcefxW5JOqWbVCRStRTbL82MykrrH63voHt5LWSlwopIpqrBgqlsjvr03vEIe4T0zJ+5e5PlZA2fzCYRI";//
        String privateKey =config.getParameterValue("privateKey");

        String appId = config.getParameterValue("appId").toString();
        String msspId =config.getParameterValue("msspId").toString();//"Viettel"; 
        //config.getParameterValue("msspId").toString();
        int forcePIN = config.getParameterValue("forcePin");
        int runImm = config.getParameterValue("runImm");
        int sigType = config.getParameterValue("signType");
        String hashType =config.getParameterValue("hashType");

        ViettelPKIConfiguration bodyData = new ViettelPKIConfiguration(modeSync, codeSign, codeQueryCer, URL_WS, privateKey,
                appId, msspId, forcePIN, runImm, sigType, hashType, body.getPhoneNumber(),
                body.getPositionX(), body.getPositionY(), body.getPage(), body. getName(),"");

        PostMultipleFileSignDto response = new PostMultipleFileSignDto();
        try{
            String url = microservice.filemanUri("file/" + fileId + "/--viettel-pki-sign-qni").toUriString();
            response = MicroserviceExchange.postPki(restTemplate, url, bodyData, PostMultipleFileSignDto.class);
        } catch (Exception e){
            throw new DigoHttpException(11003, new String[] { e.getMessage() }, HttpServletResponse.SC_NOT_FOUND);
        }
        return response;
    }
      public ResponseEntity<byte[]> getFileContent(ObjectId id){
        try{
            String unsignedUrl = microservice.filemanUri("file/" + id.toString()).toUriString();
            ResponseEntity<byte[]> entity = MicroserviceExchange.getFileEntity(restTemplate, unsignedUrl);
            return entity;
        } catch (Exception e){
            return null;
        }
    }
    
    
    public String getFileInfo(ObjectId fileId){
        try{
            String url = microservice.filemanUri("file/" + fileId.toString() + "/filename+size").toUriString();
            FileInfoDto response = MicroserviceExchange.get(restTemplate, url, FileInfoDto.class);
            return response.getFilename();
        } catch (HttpClientErrorException | NullPointerException e){
            return null;
        }
    }
    
    public String getViettelCertBase(ViettelPKIConfiguration bodyData){
        try{
            String url = microservice.filemanUri("file/--viettel-ca-get-certbase").toUriString();
            PostHashFileResponseDto response = MicroserviceExchange.postPki(restTemplate, url,bodyData, PostHashFileResponseDto.class);
            return response.getResponse();
        } catch (HttpClientErrorException | NullPointerException e){
            return null;
        }
    }
    
     private IHashSginer getHashSignerPdf(SignatureDocument signDocument, String certBase64) {
        SignatureParameter param = new SignatureParameter(signDocument.getFileContent(), certBase64);
        IHashSginer signer = null;
        Base64 codec = new Base64();
        try {
            signer = HashSignerFactory.genenrateSigner(DocumentType.PDF, param);

            com.vnpt.vnptkyso.pdf.PdfHashSigner pdfSigner = (com.vnpt.vnptkyso.pdf.PdfHashSigner) signer;

            //Set Hash
            MessageDigestAlgorithm alg;
            switch(signDocument.getHashAlgorithm()) {
                case "SHA1":
                    alg  = MessageDigestAlgorithm.SHA1;
                    break;
                case "SHA256":
                    alg  = MessageDigestAlgorithm.SHA256;
                    break;
                case "SHA384":
                    alg  = MessageDigestAlgorithm.SHA384;
                    break;
                case "SHA512":
                    alg  = MessageDigestAlgorithm.SHA512;
                    break;
                default:
                    alg  = MessageDigestAlgorithm.SHA256;
                    break;
            }
            pdfSigner.setHashAlgorithm(alg);
            // [REQUIRED] Vị trí đặt chữ ký
            pdfSigner.addSignatureViews(new PdfSignatureView(signDocument.getPage(), signDocument.getPosition()));
            // [OPTIONAL] Hình ảnh chữ ký (default=VNPT logo)
            pdfSigner.setImage(codec.decode(signDocument.getSignImage()));
            pdfSigner.setReason(signDocument.getReason());
            // [OPTIONAL] Kiểu hiển thị (default=TEXT_WITH_BACKGROUND)
            
            PdfHashSigner.RenderMode mode = PdfHashSigner.RenderMode.valueOf(signDocument.getVisibleType());
            pdfSigner.setRenderMode(mode);
            if(!PdfHashSigner.RenderMode.LOGO_ONLY.equals(mode)) {
                pdfSigner.setSignatureText(signDocument.getFullName());
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
//                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Ho_Chi_Minh"));
                pdfSigner.setSignatureText(translator.toLocale("lang.phrase.signature.text", new String[]{signDocument.getFullName(),simpleDateFormat.format(calendar.getTime())}));
            }
            // [OPTIONAL] Màu chữ (default=000000)
            pdfSigner.setFontColor(signDocument.getTextColor());
            // [OPTIONAL] Font chữ (default=Times_New_Roman)
            pdfSigner.setFontName(PdfHashSigner.FontName.valueOf(signDocument.getFontName()));
            // [OPTIONAL] Cỡ chữ (default=13)
            pdfSigner.setFontSize(signDocument.getFontSize());
            // [OPTIONAL] Kiển chữ (default=Normal)
            pdfSigner.setFontStyle(PdfHashSigner.FontStyle.valueOf(signDocument.getFontStyle()));
            // [OPTIONAL] Nội dung hiển thị

            // [OPTIONAL] Thông tin TSA server
            // pdfSigner.setTsaClient("http://timestamp.entrust.net/TSS/RFC3161sha2TS",
            // null, null);
            // [OPTIONAL] Thêm đầy đủ certchain vào file pdf (yêu cầu cho phép kết nối đến
            // CA)
            pdfSigner.useCertChain();
            // [OPTIONAL] Thêm text comment
            for (SignatureComment comment : signDocument.getComments()) {
                String rect = comment.getPosition();
                int cmtPage = comment.getPage();
                String text = comment.getText();
                if (text == null || text.isEmpty() || rect == null || rect.isEmpty()) {
                    continue;
                }
                PdfSignatureComment com = new PdfSignatureComment(PdfSignatureComment.Types.TEXT, rect, text,
                        cmtPage, PdfHashSigner.FontName.valueOf(comment.getFontName()), PdfHashSigner.FontStyle.valueOf(comment.getFontStyle()),
                        comment.getFontSize(), comment.getTextColor(), "");
                pdfSigner.addSignatureComment(com);
            }
            
            
        } catch (HashSignerException e) {
            logger.error(e.getMessage());
        }
        return signer;
    }
}
