package vn.vnpt.digo.adapter.service.bdg;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Oauth2RestTemplate;

@Service
public class HumanService {
    @Autowired
    Microservice microservice;

    @Autowired
    Oauth2RestTemplate oauth2RestTemplate;

    public <T> T post(String absUrl, Object body, Class<T> returnType){
        String URL = microservice.humanUri(absUrl).toUriString();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(oauth2RestTemplate.getToken(false));
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        return restTemplate.exchange(URL, HttpMethod.POST, request, returnType).getBody();
    }

    public <T> T post(String absUrl, Object body, ParameterizedTypeReference<T> returnTypeList){
        String URL = microservice.humanUri(absUrl).toUriString();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(oauth2RestTemplate.getToken(false));
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        return restTemplate.exchange(URL, HttpMethod.POST, request, returnTypeList).getBody();
    }

    public <T> T get(String absUrl, Class<T> returnType){
        String URL = microservice.humanUri(absUrl).toUriString();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(oauth2RestTemplate.getToken(false));
        HttpEntity<?> request = new HttpEntity<>(headers);
        return restTemplate.exchange(URL, HttpMethod.GET, request, returnType).getBody();
    }

    public <T> T get(String absUrl, ParameterizedTypeReference<T> returnTypeRef){
        String URL = microservice.humanUri(absUrl).toUriString();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(oauth2RestTemplate.getToken(false));
        HttpEntity<?> request = new HttpEntity<>(headers);
        return restTemplate.exchange(URL, HttpMethod.GET, request, returnTypeRef).getBody();
    }
}
