package vn.vnpt.digo.adapter.service;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.dto.SendTelegramDto;

import java.io.IOException;
import java.net.*;
import java.net.Authenticator;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class TelegramBotService {

    @Autowired
    private RestTemplate restTemplate;

    private static final String TELEGRAM_API_URL = "https://api.telegram.org/bot";

    public CompletableFuture<Boolean> sendTelegram(SendTelegramDto sendObj) {
        return CompletableFuture.supplyAsync(() -> {
            // L<PERSON>u trữ authenticator hiện tại để khôi phục sau khi hoàn thành
            final Authenticator originalAuthenticator = Authenticator.getDefault();
            try {
                if (isProxyConfigured(sendObj)) {
                    // Chỉ thiết lập authenticator nếu có thông tin xác thực proxy
                    if (hasProxyCredentials(sendObj)) {
                        // Thiết lập authenticator mới chỉ cho request này
                        Authenticator proxyAuthenticator = createProxyAuthenticator(sendObj);
                        Authenticator.setDefault(proxyAuthenticator);
                    }
                    // Sử dụng OkHttp với SOCKS proxy
                    return sendWithOkHttpAndSocksProxy(sendObj);
                }
            } finally {
                // Khôi phục authenticator ban đầu sau khi hoàn thành
                Authenticator.setDefault(originalAuthenticator);
            }
            return true;
        });
    }

    private boolean isProxyConfigured(SendTelegramDto sendObj) {
        return sendObj.getProxyIp() != null && !sendObj.getProxyIp().isEmpty() &&
               sendObj.getProxyPort() != null && !sendObj.getProxyPort().isEmpty();
    }

    private boolean hasProxyCredentials(SendTelegramDto sendObj) {
        return sendObj.getProxyUser() != null && !sendObj.getProxyUser().isEmpty() &&
               sendObj.getProxyPwd() != null && !sendObj.getProxyPwd().isEmpty();
    }

    private Authenticator createProxyAuthenticator(SendTelegramDto sendObj) {
        final String proxyUser = sendObj.getProxyUser();
        final String proxyPassword = sendObj.getProxyPwd();
        final String proxyHost = sendObj.getProxyIp();
        final int proxyPort = Integer.parseInt(sendObj.getProxyPort());

        return new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                if (getRequestingHost().equalsIgnoreCase(proxyHost) &&
                    getRequestingPort() == proxyPort) {
                    log.debug("Providing authentication for proxy {}:{}", proxyHost, proxyPort);
                    return new PasswordAuthentication(proxyUser, proxyPassword.toCharArray());
                }
                return null;
            }
        };
    }

    private boolean sendWithOkHttpAndSocksProxy(SendTelegramDto sendObj) {
        try {
            // Tạo proxy
            SocketAddress addr = new InetSocketAddress(sendObj.getProxyIp(), Integer.parseInt(sendObj.getProxyPort()));
            Proxy proxy = new Proxy(Proxy.Type.SOCKS, addr);

            // Tạo OkHttpClient với proxy và timeout
            OkHttpClient client = new OkHttpClient.Builder()
                    .proxy(proxy)
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .build();

            boolean allSuccess = true;
            
            // Gửi tin nhắn đến group chat nếu có
            if (sendObj.getGroupChatId() != null && !sendObj.getGroupChatId().isEmpty()) {
                // Tạo request body
                FormBody.Builder formBuilder = new FormBody.Builder();
                formBuilder.add("chat_id", sendObj.getGroupChatId());
                formBuilder.add("text", sendObj.getMsg());

                // Thêm topic_id nếu có
                if (sendObj.getTopicId() != null && !sendObj.getTopicId().isEmpty()) {
                    formBuilder.add("message_thread_id", sendObj.getTopicId());
                }

                // Tạo request
                Request request = new Request.Builder()
                        .url(TELEGRAM_API_URL + sendObj.getBotToken() + "/sendMessage")
                        .post(formBuilder.build())
                        .build();

                // Thực hiện request
                try (Response response = client.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        log.info("Successfully sent Telegram message to group {}", sendObj.getGroupChatId());
                    } else {
                        log.error("Failed to send Telegram message to group. Status: {}, Body: {}",
                                response.code(), response.body() != null ? response.body().string() : "No body");
                        allSuccess = false;
                    }
                }
            }
            
            // Gửi tin nhắn đến các user nếu có
            if (sendObj.getChatUserId() != null && !sendObj.getChatUserId().isEmpty()) {
                for (String userId : sendObj.getChatUserId()) {
                    // Tạo request body cho mỗi user
                    FormBody.Builder userFormBuilder = new FormBody.Builder();
                    userFormBuilder.add("chat_id", userId);
                    userFormBuilder.add("text", sendObj.getMsg());
                    
                    // Tạo request
                    Request userRequest = new Request.Builder()
                            .url(TELEGRAM_API_URL + sendObj.getBotToken() + "/sendMessage")
                            .post(userFormBuilder.build())
                            .build();
                    
                    // Thực hiện request
                    try (Response response = client.newCall(userRequest).execute()) {
                        if (response.isSuccessful()) {
                            log.info("Successfully sent Telegram message to user {}", userId);
                        } else {
                            log.error("Failed to send Telegram message to user {}. Status: {}, Body: {}",
                                    userId, response.code(), response.body() != null ? response.body().string() : "No body");
                            allSuccess = false;
                        }
                    }
                }
            }
            
            return allSuccess;
        } catch (Exception e) {
            log.error("Error sending Telegram message with SOCKS proxy: {}", e.getMessage(), e);
            return false;
        }
    }
    }
    

    
