package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.math3.util.Pair;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.MappingData;
import vn.vnpt.digo.adapter.document.NBRSLog;
import vn.vnpt.digo.adapter.document.NBRSLog.ServiceLog;
import vn.vnpt.digo.adapter.document.NBRSLog.ServiceLog.ProcessLog;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.MappingDataListDto;
import vn.vnpt.digo.adapter.dto.PostDataLogToKafkaDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.ErrorMessage;
import vn.vnpt.digo.adapter.pojo.NBRSProcessIdList;
import vn.vnpt.digo.adapter.pojo.NBRSProcessIdList.NBRSProcessId;
import vn.vnpt.digo.adapter.stream.NBRSDossierHCMProducer;
import vn.vnpt.digo.adapter.thread.NBRSDossierHCMChangeProcess;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class NBRSService {
    Logger logger = LoggerFactory.getLogger(NBRSService.class);

    @Autowired
    private ServiceService serviceService;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private Microservice microservice;

    @Autowired
    private IntegratedLogsService integratedLogsService;

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    UtilService utilService;
    @Value(value = "${digo.thread.lgsphcm.dossier-sync-config-id}")
    private ObjectId lgspHCMDossierSyncConfigId;

    @Value(value = "${digo.oidc.client-id}")
    private String clientId;
    @Value(value= "${digo.nbrs.sync.dossier.hcm}")
    private Boolean nBRSDossierHCM;

    @Value(value = "${digo.oidc.client-secret}")
    private String clientSecret;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;

    @Value(value = "${nbrs.serviceid}")
    private ObjectId serviceId;

    @Value(value = "${nbrs.mapDossierStatus.serviceid}")
    private ObjectId mapStatusServiceId;

    @Value(value = "${nbrs.mapDossierAgency.serviceid}")
    private ObjectId mapAgencyServiceId;

    @Value("${vnpt.integration.nbrs.sync.enable}")
    private Boolean syncNbrsEnable;
    @Autowired
    private NBRSDossierHCMProducer nbrsDossierHCMProducer;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Translator translator;

    ObjectMapper mapper = new ObjectMapper().configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
    List<MappingDataListDto> statusMappingData;
    List<MappingDataListDto> agencyMappingData;

    @Scheduled(cron = "0 0 4 * * *", zone = "Asia/Ho_Chi_Minh")
    ////@SchedulerLock(name = "syncNbrsDaily", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void syncNbrsDaily() throws JsonProcessingException {
        if(this.syncNbrsEnable){
            System.out.println("Start job sync dossier KHDT!");

            String date = new SimpleDateFormat("yyyy-MM-dd").format(Calendar.getInstance().getTime());
            synchronizeNBRSDossier(getDateBefore(date), getDateAfter(date));

            List<NBRSLog> logs = mongoTemplate.find(new Query().addCriteria(Criteria.where("status").is(0)), NBRSLog.class);
            for (NBRSLog log : logs)
                synchronizeNBRSDossier(log.getDossierAcceptedDay(), log.getDossierAcceptedDay());

            System.out.println("End job sync dossier KHDT!");
        }
    }

    public HashMap<String, Object> synchronizeNBRSDossier(String fromDate, String toDate) throws JsonProcessingException {
        IntegratedConfigurationDto config = configurationService.searchByServiceId(this.serviceId);
        List<HashMap<String, Object>> arrayLogs = new ArrayList<>();
        HashMap<String, Object> inputLogs = new HashMap<>();

        if (Objects.isNull(config))
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);


        JSONArray valueNameJsons = new JSONObject(config.getParametersValue("value-name-param").toString()).getJSONArray("valueNameParam");
        restTemplate = MicroserviceExchange.getRestTemplate(Context.getJwtAuthenticationTokenValue());

        statusMappingData = mongoTemplate.find(new Query().addCriteria(Criteria.where("type.id").is(mapStatusServiceId)), MappingDataListDto.class);
        agencyMappingData = mongoTemplate.find(new Query().addCriteria(Criteria.where("type.id").is(mapAgencyServiceId)), MappingDataListDto.class);
        HashMap<String, Object> mapAgencyMappingDataList = new HashMap<>();
        mapAgencyMappingDataList.put("mapAgencyServiceId", mapAgencyServiceId.toString());
        mapAgencyMappingDataList.put("agencyMappingData", agencyMappingData);
        mapAgencyMappingDataList.put("valueNameJsons", valueNameJsons);
        mapAgencyMappingDataList.put("this.serviceId", this.serviceId.toString());
        mapAgencyMappingDataList.put("config", config);
        mapAgencyMappingDataList.put("fromDate.compareTo(toDate)", fromDate.compareTo(toDate));
        arrayLogs.add(mapAgencyMappingDataList);
        while (fromDate.compareTo(toDate) <= 0) {
            NBRSLog log = mongoTemplate.findOne(new Query().addCriteria(Criteria.where("dossierAcceptedDay").is(fromDate)), NBRSLog.class);
            boolean isExistLog = Objects.nonNull(log);
            HashMap<String, Object> isExistLogs = new HashMap<>();
            isExistLogs.put("isExistLog", isExistLog);
            arrayLogs.add(isExistLogs);
            if (isExistLog) {
                if (log.getStatus() == 1) {
                    fromDate = getDateAfter(fromDate);
                    continue;
                }
            } else
                log = new NBRSLog(new ObjectId(), 0, new Date(), fromDate, new HashMap<>());

            int logStatus = 1;
            try{
                for (int i = 0; i < valueNameJsons.length(); i++) {
                    int serviceLogStatus = 2;

                    var valueName = valueNameJsons.getJSONObject(i);
                    String serviceCode = valueName.getString("serviceCode");

                    ServiceLog serviceLog = isExistLog ? log.getServiceLogs().get(serviceCode) : new ServiceLog();

                    List<ProcessLog> processLogs = new ArrayList<>();

                    if (Objects.isNull(serviceLog) || serviceLog.getStatus() == 0) {
                        var getListIdRes = nbrsExchange("--dossiers", new HashMap<>(Map.of("fromDate", fromDate, "toDate", toDate, "serviceCode", serviceCode)));    /** Gọi API lấy danh sách processID*/
                        HashMap<String, Object> dossiersList = new HashMap<>();
                        dossiersList.put("--dossiers", "fromDate" + fromDate + "toDate"+ toDate);
                        dossiersList.put("serviceCode", getListIdRes);
                        arrayLogs.add(dossiersList);
                        if (Objects.isNull(getListIdRes)) {
                            serviceLog.setStatus(0);
                            log.getServiceLogs().put(serviceCode, serviceLog);
                            logStatus = 0;
                            continue;
                        }
                        List<ProcessLog> processLog1 =  getListProcessLog(mapper.convertValue(getListIdRes, NBRSProcessIdList.class));
                        HashMap<String, Object> processLog1List = new HashMap<>();
                        processLog1List.put("--processLog1List", processLog1);
                        arrayLogs.add(processLog1List);
                        serviceLog = new ServiceLog(1, processLog1);

                    } else if (serviceLog.getStatus() == 2) {
                        log.getServiceLogs().put(serviceCode, serviceLog);
                        continue;
                    }
                    HashMap<String, Object> requestErr = new HashMap<>();
                    requestErr.put("serviceLog", serviceLog);
                    if (Objects.nonNull(serviceLog)){
                        processLogs = serviceLog.getProcessLogs();
                    }
                    for (int j = 0; j < processLogs.size(); j++) {
                        var processLog = processLogs.get(j);
                        if (processLog.getStatus() == 0) {
                            var request = getFullDossierInfo(valueName, processLog.getId(), serviceCode);
                            requestErr.put("getFullDossierInfo", request);
                            if (Objects.isNull(request)) {
                                serviceLogStatus = 1;
                                continue;
                            }
                            if (Objects.nonNull(request.get("Error"))) {
                                serviceLogStatus = 1;
                                processLog.setStatus(0);
                                processLog.setErrMsg(request.get("Erorr").toString());
                                logger.info(request.toString(), "Error");
                            } else {
                                //String padmanUrl = "http://localhost:8081/dossier-nbrs/";
                                String padmanUrl = microservice.padmanUri("/dossier-nbrs/").toUriString();

                                request.put("statusMappingData", statusMappingData);

                                boolean isNullData = Objects.isNull(request.get("dossierProcess")) || Objects.isNull(request.get("businessInfo"));  //Chi tiết QTXL hoặc thông tin đk kinh doanh null
                                logger.info("isNullData 1: " + isNullData);
                                logger.info("request input: " + request);

                                requestErr.put("isNullData", isNullData);
                                requestErr.put("requestSend", request);
                                arrayLogs.add(requestErr);
                                if (Objects.isNull(request.get("mappedAgencyId"))) {
                                    serviceLogStatus = 1;
                                    processLog.setStatus(0);
                                    logger.info(request.toString(), "mappedAgencyId NULL");
                                    // + Cập nhật errMsg trong apiIntegrationLog
                                } else if (Objects.nonNull(request.get("empty"))) { //Hồ sơ trả về mảng rỗng
                                    processLog.setStatus(1);
                                    processLog.setErrMsg("Không có hồ sơ");
                                    logger.info(request.toString(), "Không có hồ sơ");
                                } else if (isNullData) {    // Lỗi lấy thông tin đăng ký kinh doanh hoặc quá trình xử lý
                                    serviceLogStatus = 1;
                                    processLog.setStatus(0);
                                    processLog.setErrMsg("Lỗi lấy thông tin đăng ký kinh doanh/quá trình xử lý hoặc hồ sơ chưa kết thúc xử lý");
                                    logger.info(request.toString(), "Lỗi lấy thông tin đăng ký kinh doanh/quá trình xử lý hoặc hồ sơ chưa kết thúc xử lý");
                                    if (!isExistLog)
                                        try{
                                            logger.info("padmanUrl: " + padmanUrl);
                                            logger.info("request : " + request);
                                            MicroserviceExchange.post(padmanUrl, request, ErrorMessage.class);
                                        }
                                        catch (Exception e){}
                                } else {
                                    logger.info("nBRSDossierHCM 1: " + nBRSDossierHCM);
                                    if(nBRSDossierHCM)
                                    {
                                        try{
                                            request.remove("statusMappingData");
                                            request.remove("dossierProcess");
                                            JSONObject valuename = new JSONObject(request.get("valueName").toString());
                                            var dossierInfoReq = request.get("dossierInfo");
                                            var dossierInfo = ((HashMap<String, ArrayList<LinkedHashMap>>) dossierInfoReq)
                                                    .get(valuename.getString("dossierInfo")).get(0);
                                            String codeBoKHDT = dossierInfo.get(valuename.getString("procedureCode")).toString();
                                            String[] listCodeKHDT = codeBoKHDT.strip().split(",");
                                            dossierInfo.put(valuename.getString("procedureCode"), listCodeKHDT[0]);
                                            ((HashMap<String, ArrayList<LinkedHashMap>>) request.get("dossierInfo"))
                                                    .get(valuename.getString("dossierInfo")).set(0, dossierInfo);
                                            if(NBRSDossierHCMChangeProcess.listNBRSdossier.offer(request))
                                            {
                                                logger.info("Queue length of NBRSHCM request data: " + NBRSDossierHCMChangeProcess.listNBRSdossier.stream().count());
                                            }else{
                                                logger.error("NBRSHCM-Dossier: Offer request failed: " + request.toString());
                                            }
                                            processLog.setStatus(1);
                                            processLog.setErrMsg("successfully offer data into queue ");
                                        }catch (Exception e){
                                            processLog.setStatus(0);
                                            processLog.setErrMsg("Lỗi" + e.getMessage());
                                            serviceLogStatus = 1;
                                        }

                                    }else {
                                        try{
                                            logger.info("padmanUrl 1: " + padmanUrl);
                                            logger.info("request 1: " + request);
                                            HashMap<String, Object> padmanUrlSend = new HashMap<>();
                                            padmanUrlSend.put("padmanUrl", padmanUrl);
                                            padmanUrlSend.put("padmanUrlSendDto", request);
                                            ErrorMessage errorMessage = MicroserviceExchange.post(padmanUrl, request, ErrorMessage.class);
                                            if (Objects.nonNull(request.get("dossierCode"))){
                                                padmanUrlSend.put("dossierCode", request.get("dossierCode"));
                                            }

                                            padmanUrlSend.put("responseErrorMessage ", errorMessage);
                                            arrayLogs.add(padmanUrlSend);
                                            if (errorMessage.getError_code().equals("1")){
                                                processLog.setStatus(1);
                                                processLog.setErrMsg("success");
                                            }
                                            else {
                                                processLog.setStatus(0);
                                                processLog.setErrMsg(errorMessage.getMessage());
                                                serviceLogStatus = 1;
                                            }
                                        }catch (Exception e){
                                            processLog.setStatus(0);
                                            processLog.setErrMsg("Lỗi lưu hồ sơ qua padman");
                                            serviceLogStatus = 1;
                                        }
                                    }
                                }
                            }
                            processLogs.set(j, processLog);
                        }
                    }
                    serviceLog.setStatus(serviceLogStatus);
                    if (serviceLogStatus != 2)
                        logStatus = 0;

                    log.getServiceLogs().put(serviceCode, serviceLog);
                }
                log.setStatus(logStatus);

                mongoTemplate.save(log);
            }catch (Exception e){
                if(!isExistLog){
                    log = new NBRSLog(new ObjectId(), 0, new Date(), fromDate, new HashMap<>());
                    mongoTemplate.save(log);
                }
            }
            fromDate = getDateAfter(fromDate);
        }
        inputLogs.put("arrayLogs", arrayLogs);
        return inputLogs;
    }
    public int synchronizeNBRSHCMDossier(String fromDate, String toDate) throws JsonProcessingException {
        int count_nbrs_ids = 0;
        IntegratedConfigurationDto config = configurationService.searchByServiceId(this.serviceId);
        if (Objects.isNull(config))
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);

        JSONArray valueNameJsons = new JSONObject(config.getParametersValue("value-name-param").toString()).getJSONArray("valueNameParam");
        restTemplate = MicroserviceExchange.getRestTemplate(Context.getJwtAuthenticationTokenValue());

        statusMappingData = mongoTemplate.find(new Query().addCriteria(Criteria.where("type.id").is(mapStatusServiceId)), MappingDataListDto.class);
        agencyMappingData = mongoTemplate.find(new Query().addCriteria(Criteria.where("type.id").is(mapAgencyServiceId)), MappingDataListDto.class);

        while (fromDate.compareTo(toDate) <= 0) {
            try{
                for (int i = 0; i < valueNameJsons.length(); i++) {
                    var valueName = valueNameJsons.getJSONObject(i);
                    String serviceCode = valueName.getString("serviceCode");
                    var getListIdRes = nbrsExchange("--dossiers", new HashMap<>(Map.of("fromDate", fromDate, "toDate", fromDate, "serviceCode", serviceCode)));    /** Gọi API lấy danh sách processID*/
                    if(Objects.isNull(getListIdRes)){
                        continue;
                    }
                    NBRSProcessIdList  nbrsProcessIdlist =   mapper.convertValue(getListIdRes, NBRSProcessIdList.class);
                    List<NBRSProcessId> processIdList= new ArrayList<>();
                    if(Objects.nonNull(nbrsProcessIdlist.getEnt_list())){
                        processIdList = nbrsProcessIdlist.getEnt_list();
                    }else if(Objects.nonNull(nbrsProcessIdlist.getCoop_doc_list())){
                        processIdList = nbrsProcessIdlist.getCoop_doc_list();
                    }else{
                        processIdList = nbrsProcessIdlist.getHh_list();
                    }
                    count_nbrs_ids = count_nbrs_ids + processIdList.size();
                    for(int j = 0 ; j < processIdList.size() ; j++)
                    {
                        if(NBRSDossierHCMChangeProcess.listIdNBRSdossier.offer(new Pair<>(processIdList.get(j).getProcess_id().toString(), new Pair<>(valueName, serviceCode))))
                        {
                            logger.info("Queue length of NBRSHCM Ids: " + NBRSDossierHCMChangeProcess.listIdNBRSdossier.stream().count());
                        }else{
                            logger.error("NBRSHCM-Dossier: Offer Id failed: " + processIdList.get(j).getProcess_id().toString());
                        }
                    }

                }

            }catch (Exception e){
                logger.info("Lỗi liên quan đến việc call lấy process Id của hồ sơ HCM từ KHDT" + e.getMessage());
            }
            fromDate = getDateAfter(fromDate);
        }
        return count_nbrs_ids;
    }
    public String syncDossierNBRSHCM(String processId, JSONObject valueName , String serviceCode) throws JsonProcessingException {
        try{
            IntegratedConfigurationDto config = configurationService.getConfig(lgspHCMDossierSyncConfigId);
            RestTemplate rest = MicroserviceExchange.getOAuth2RestTemplate(config);
            var request = getFullDossierHCMInfo(restTemplate, valueName, processId, serviceCode);
            if (Objects.isNull(request)) {
                return "Sync finish with Null";
            }
            if (Objects.nonNull(request.get("Error"))) {
                logger.info(request.toString() + "Error");
                return "Sync finish with Error";
            } else {
                if (Objects.isNull(request.get("mappedAgencyId"))) {
                    logger.info(request.toString() + "mappedAgencyId NULL");
                    return "Sync finish with mappedAgencyId NULL";
                } else if (Objects.nonNull(request.get("empty"))) { //Hồ sơ trả về mảng rỗng
                    logger.info(request.toString() + "Không có hồ sơ");
                    return "Sync finish with no document";
                } else
                {
                    try{
                        JSONObject valuename = new JSONObject(request.get("valueName").toString());
                        var dossierInfoReq = request.get("dossierInfo");
                        var dossierInfo = ((HashMap<String, ArrayList<LinkedHashMap>>) dossierInfoReq)
                                .get(valuename.getString("dossierInfo")).get(0);
                        String codeBoKHDT = dossierInfo.get(valuename.getString("procedureCode")).toString();
                        String[] listCodeKHDT = codeBoKHDT.strip().split(",");
                        dossierInfo.put(valuename.getString("procedureCode"), listCodeKHDT[0]);
                        ((HashMap<String, ArrayList<LinkedHashMap>>) request.get("dossierInfo"))
                                .get(valuename.getString("dossierInfo")).set(0, dossierInfo);
                        ErrorMessage errorMessage = utilService.syncNBRSDossierHCM(rest,request);
                        logger.info("Result sync NBRS DossierHCM into Padman: " + errorMessage.getError_code() + " " + errorMessage.getMessage());
                    }catch (Exception e){
                        logger.info("Lỗi chạy đồng bộ KHDT về IGATE: " + e.getMessage());
                    }
                }
            }
            return "Sync finish";
        }catch(Exception e)
        {
            return e.getMessage();
        }
    }
    private Map<String, Object> getFullDossierHCMInfo(RestTemplate rest , JSONObject valueName, String processId, String serviceCode) throws JsonProcessingException {
        Map<String, Object> fullDossierInfo = new HashMap<>();
        try {
            var dossierInfoRes = utilService.nbrsExchange(rest, new HashMap<>(Map.of("processID", processId, "serviceCode", serviceCode)));
            if (!Objects.nonNull(dossierInfoRes))
                return null;

            var temp = ((HashMap<String, ArrayList<LinkedHashMap>>) dossierInfoRes).get(valueName.getString("dossierInfo"));
            if (temp.size() == 0)
                return Map.of("empty", true);
            var dossierInfo = temp.get(0);
            String dossierStatus = dossierInfo.get("TrangThaiHoSo").toString();
            Object businessInfo = "";
            // HS đã có kết quả
            if (dossierStatus.equals("9") || dossierStatus.equals("10")) {
                String businessInfoCode = dossierInfo.get(valueName.getString("businessRegisterCode")).toString();
                businessInfo = nbrsExchange("--info", new HashMap<>(Map.of("taxCode", businessInfoCode, "serviceCode", serviceCode)));
                if(Objects.isNull(businessInfo)){
                    businessInfo = "";
                }
            }
            // HS không được tiếp nhận hoặc dừng xử lý
            if (dossierStatus.equals("3") || dossierStatus.equals("8"))
                businessInfo = "";


            fullDossierInfo.put("valueName", valueName.toString());
            fullDossierInfo.put("processId", processId);
            fullDossierInfo.put("dossierInfo", dossierInfoRes);
            fullDossierInfo.put("businessInfo", businessInfo);
            fullDossierInfo.put("mappedDossierStatus", getMappedDossierStatus(dossierInfo.get("TrangThaiHoSo").toString()));

            var agency = getMappedAgencyId(dossierInfo.get("DonViXuLy").toString());
            if(Objects.isNull(agency))
                fullDossierInfo.put("Error", "Không map được đơn vị xử lý");
            else
                fullDossierInfo.put("mappedAgencyId", agency);

            var procedureCode = dossierInfo.get(valueName.getString("procedureCode"));
            if(Objects.isNull(procedureCode))
                fullDossierInfo.put("Error", "Hồ sơ không có TTHC");

        } catch (Exception e) {
            fullDossierInfo.put("Error", e.getMessage());
        }
        return fullDossierInfo;
    }

    public List<NBRSLog> getSyncNbrsLog(String dossierAcceptedDay){
        return mongoTemplate.find(new Query().addCriteria(Criteria.where("dossierAcceptedDay").is(dossierAcceptedDay)), NBRSLog.class);
    }
    private Object nbrsExchange(String apiCode, HashMap<String, String> req) throws JsonProcessingException {
        try {
            String serviceCode = "investment-plan";
            return serviceService.exchange(serviceCode, apiCode, null,null, null, req, null,null,null,null,false).getBody();
        } catch (Exception e) {
            return null;
        }
    }

    private Map<String, Object> getFullDossierInfo(JSONObject valueName, String processId, String serviceCode) throws JsonProcessingException {
        Map<String, Object> fullDossierInfo = new HashMap<>();
        try {
            var dossierInfoRes = nbrsExchange("--dossier-info", new HashMap<>(Map.of("processID", processId, "serviceCode", serviceCode)));
            if (!Objects.nonNull(dossierInfoRes))
                return null;

            var temp = ((HashMap<String, ArrayList<LinkedHashMap>>) dossierInfoRes).get(valueName.getString("dossierInfo"));
            if (temp.size() == 0)
                return Map.of("empty", true);

            var dossierInfo = temp.get(0);

            String dossierCode = dossierInfo.get("MaHoSo").toString();
            String dossierStatus = dossierInfo.get("TrangThaiHoSo").toString();

            var dossierProcessRes = nbrsExchange("--dossier-process", new HashMap<>(Map.of("in_journal_no", dossierCode, "serviceCode", serviceCode)));
            Object businessInfo = "";
            fullDossierInfo.put("dossierCode", dossierCode);
            fullDossierInfo.put("dossierStatus", dossierStatus);
            // HS đã có kết quả
            if (dossierStatus.equals("9") || dossierStatus.equals("10")) {
                String masodoanhnghiep = valueName.getString("businessRegisterCode");
                String businessInfoCode = dossierInfo.get(masodoanhnghiep).toString();
                businessInfo = nbrsExchange("--info", new HashMap<>(Map.of("taxCode", businessInfoCode, "serviceCode", serviceCode)));
                if(Objects.isNull(businessInfo)){
                    businessInfo = "";
                }
            }
            // HS không được tiếp nhận hoặc dừng xử lý
            if (dossierStatus.equals("3") || dossierStatus.equals("8")){
                businessInfo = "";

            }

            fullDossierInfo.put("valueName", valueName.toString());
            fullDossierInfo.put("processId", processId);
            fullDossierInfo.put("dossierInfo", dossierInfoRes);
            fullDossierInfo.put("dossierProcess", dossierProcessRes);
            fullDossierInfo.put("businessInfo", businessInfo);
            fullDossierInfo.put("mappedDossierStatus", getMappedDossierStatus(dossierInfo.get("TrangThaiHoSo").toString()));

            var agency = getMappedAgencyId(dossierInfo.get("DonViXuLy").toString());
            if(Objects.isNull(agency))
                fullDossierInfo.put("Error", "Không map được đơn vị xử lý");
            else
                fullDossierInfo.put("mappedAgencyId", agency);

            var procedureCode = dossierInfo.get(valueName.getString("procedureCode"));
            if(Objects.isNull(procedureCode))
                fullDossierInfo.put("Error", "Hồ sơ không có TTHC");

        } catch (Exception e) {
            fullDossierInfo.put("Error", e.getMessage());
        }
        return fullDossierInfo;
    }

    private List<ProcessLog> getListProcessLog(NBRSProcessIdList nbrsProcessIdList) {
        List<ProcessLog> processLogs = new ArrayList<>();

        List<NBRSProcessId> processIdList;
        if (nbrsProcessIdList.getEnt_list() == null)
            if (nbrsProcessIdList.getCoop_doc_list() == null)
                processIdList = nbrsProcessIdList.getHh_list();
            else
                processIdList = nbrsProcessIdList.getCoop_doc_list();
        else
            processIdList = nbrsProcessIdList.getEnt_list();

        for (NBRSProcessId processId : processIdList)
            processLogs.add(new ProcessLog(processId.getProcess_id(), 0, ""));

        return processLogs;
    }

    private String getDateAfter(String date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(date, formatter).plusDays(1).format(formatter);
    }
    private String getDateBefore(String date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(date, formatter).minusDays(1).format(formatter);
    }

    private String getMappedAgencyId(String agencyName) {
        //agencyName = "Tỉnh KonTum";
        for (MappingDataListDto mappingData : agencyMappingData) {
            if (mappingData.getDest().getName().equals(agencyName))
                return mappingData.getSource().getId();
        }
        return null;
    }

    private String getMappedDossierStatus(String statusId) {
        for (MappingDataListDto mappingData : statusMappingData) {
            if (mappingData.getDest().getId().equals(statusId))
                return mappingData.getSource().getId();
        }
        return null;
    }

    public AffectedRowsDto delete(ObjectId id) {
        AffectedRowsDto affectedRows = new AffectedRowsDto();
        NBRSLog NBRSLogItem = mongoTemplate.findById(id, NBRSLog.class);
        if (NBRSLogItem != null) {
            mongoTemplate.remove(NBRSLogItem);
            affectedRows.setAffectedRows(1); // Giả sử affectedRows có phương thức setAffectedRows
        } else {
            affectedRows.setAffectedRows(0);
        }
        return affectedRows;
    }
}
