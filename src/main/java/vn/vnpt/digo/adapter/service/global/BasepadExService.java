package vn.vnpt.digo.adapter.service.global;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.dvclt.ProcedureDto;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;

@Service
public class BasepadExService {
    @Autowired
    private Microservice microservice;
    
    @Cacheable(value = "BasepadExService.getProcedureByNationCode", key = "{#code, #agencyId}")
    public ProcedureDto[] getProcedureByNationCode(RestTemplate restTemplate, String code, String agencyId){
        
        String procedureByNationCodeUrl = microservice.basepadUri("/procedure/--by-nation-code?nation-code=" + code + (agencyId == null ? "&common-use=true" : "&common-use=false&agency-id=" + agencyId)).toUriString();

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);

        ProcedureDto[] result = MicroserviceExchange.getJsonNoAuth(restTemplate, procedureByNationCodeUrl, ProcedureDto[].class);
        return result;
    }
    
    @Cacheable(value = "BasepadExService.getProcedureByDvcltProcedureCode", key = "{#dvcltProcedureCode, #agencyId}")
    public ProcedureDto[] getProcedureByDvcltProcedureCode(RestTemplate restTemplate, String dvcltProcedureCode, String agencyId){

        String procedureByNationCodeUrl = microservice.basepadUri("/procedure/--by-dvclt-procedure-code?dvclt-procedure-code=" + dvcltProcedureCode + (agencyId == null ? "&common-use=true" : "&common-use=false&agency-id=" + agencyId)).toUriString();

        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);

        ProcedureDto[] result = MicroserviceExchange.getJsonNoAuth(restTemplate, procedureByNationCodeUrl, ProcedureDto[].class);
        return result;
    }
}
