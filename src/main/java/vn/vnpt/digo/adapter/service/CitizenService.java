/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import org.bson.types.ObjectId;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.CitizenInforVerifyResDto;
import vn.vnpt.digo.adapter.dto.CitizenParamsDto;
import vn.vnpt.digo.adapter.dto.CitizenDataDto;
import vn.vnpt.digo.adapter.dto.ParamDto;
import vn.vnpt.digo.adapter.dto.csdldc.CitizenResponseDto;
import vn.vnpt.digo.adapter.dto.csdldc.ShareCitizenInfoRequestDto;
import vn.vnpt.digo.adapter.dto.csdldc.ShareCitizenOrginInfoResponseDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.Parameters;
import vn.vnpt.digo.adapter.util.Translator;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.ConnectService;
import vn.vnpt.digo.adapter.util.StringHelper;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.GsonUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
/**
 *
 * <AUTHOR>
 */
@Service
public class CitizenService {
    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private ConnectService connectService;
    
    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private ShareHouseHoldInfoService shareHouseHoldInfoService;

    @Autowired
    private CSDLDCService csdldcService;

    @Autowired
    private  ShareHouseHoldInfoService configInfService;


    Logger logger = LoggerFactory.getLogger(CitizenService.class);
    
    private ObjectId serviceId = new ObjectId("5f7c16069abb62f511890028");

    private final ObjectId subsystemId = new ObjectId("5f7c16069abb62f511880007");

    private ObjectId subSystemCSDLIdLIgate = new ObjectId("5f7c16069abb62f511880003");

    private ObjectId serviceIdIgate = new ObjectId("5f7c16069abb62f511891041");

    private ObjectId serviceCSDLIdIgate = new ObjectId("5f7c16069abb62f511890034");

    private ObjectId agencyIdIgate = new ObjectId("60c868a4289bad69c7cbffea");

    public CitizenInforVerifyResDto verifyCitizen(CitizenParamsDto params, Map<String, Object> body){
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String token = config.getParametersValue("access_token");
        String authHash = config.getParametersValue("authHash");
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);   
        headers.add("AuthHash", authHash);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<String> result = restTemplate.exchange(config.getParametersValue("gateway")
                + "apiCSDLDanCu/XacThucThongTinCongDan", HttpMethod.POST, request, String.class);
        logger.info("Response from apiCSDLDanCu/XacThucThongTinCongDan: " + result.toString());
        Gson g = new Gson();
        CitizenInforVerifyResDto response = g.fromJson(result.getBody(), CitizenInforVerifyResDto.class);
        return response;
    }

    /**
     * birthday: dd/MM/yyyy
     * @return
     */
    public CitizenDataDto getDataCitizens(String fullname, String birthday, String identityNumber, String checkCitizenType, String agencyCSDLId, String subSystemCSDLId, String serviceId, String username)  throws IOException {
        CitizenDataDto newCitizenDataDto = new CitizenDataDto();
        IntegratedConfigurationDto config = configurationService.getConfig(null, subsystemId, serviceIdIgate );
        switch (checkCitizenType){
            case "1" : {
                try {
                    List<Parameters> parameters = config.getParameters();
                    List<ParamDto> listParams = new ArrayList<>();
                    for(Parameters param:parameters){
                        ParamDto item = new ParamDto();
                        item.setKey(param.getKey());
                        item.setValue(param.getOriginValue());
                        listParams.add(item);
                    }
                    String system = this.getParam(listParams,"system");
                    if (system.equals("igate1")){
                        String igate1DomainApi = this.getParam(listParams,"igate1DomainApi");
                        String igate1AccessToken = this.getParam(listParams,"igate1AccessToken");
                        String igate1SecretKey = this.getParam(listParams,"igate1SecretKey");
                        String securityKey = StringHelper.toHmacSha256(igate1SecretKey,igate1AccessToken);
                        String igate1EndPoint = this.getParam(listParams,"igate1EndPointGetDossierDigitization");
                        String URL = igate1DomainApi + "/" + igate1EndPoint;
                        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
                        body.add("access-token",igate1AccessToken);
                        body.add("security_key",securityKey);
                        body.add("fullname",fullname);
                        body.add("birthday",configInfService.toYYYYMMDD(birthday));
                        body.add("identityNumber",identityNumber);
                        String jsonString = MicroserviceExchange.postMultipartNoAuth(this.restTemplate, URL, body, String.class);
                        ObjectMapper mapper = new ObjectMapper();
                        ShareCitizenOrginInfoResponseDto newObjectCitizen = mapper.readValue(jsonString, new TypeReference<ShareCitizenOrginInfoResponseDto>(){});
//                        newCitizenDataDto.setBirthday(newObjectCitizen.getNgayThangNamSinh().getNgayThangNam());
//                        newCitizenDataDto.setFullname(newObjectCitizen.getHoVaTen().getTen());
                        newCitizenDataDto.setIdentityNumber9(newObjectCitizen.getSoCMND());
                        newCitizenDataDto.setIdentityNumber12(newObjectCitizen.getSoDinhDanh());
                    }
                }catch (Exception e){
                   logger.info(" iagte 1 : " + e);
                }
                break;
            }
            case "2" : {
                //yyyyMMdd
                if (agencyCSDLId != null && !agencyCSDLId.isEmpty()){
                    agencyIdIgate = new ObjectId(agencyCSDLId);
                }
                if (subSystemCSDLId != null && !subSystemCSDLId.isEmpty()){
                    subSystemCSDLIdLIgate = new ObjectId(subSystemCSDLId);
                }
                if (serviceId != null && !serviceId.isEmpty()){
                    serviceCSDLIdIgate = new ObjectId(serviceId);
                }
                IntegratedConfigurationDto configCSDL = configurationService.getConfig(agencyIdIgate, subSystemCSDLIdLIgate, serviceCSDLIdIgate );
                ShareCitizenInfoRequestDto newShareCitizenInfoRequestDto = new ShareCitizenInfoRequestDto();
                newShareCitizenInfoRequestDto.setFullname(fullname);
                newShareCitizenInfoRequestDto.setAgencyId(agencyIdIgate);
                newShareCitizenInfoRequestDto.setSubsystemId(subSystemCSDLIdLIgate);
                newShareCitizenInfoRequestDto.setIndentityNumber(identityNumber);
                newShareCitizenInfoRequestDto.setBirthday(configInfService.toYYYYMMDD(birthday));
                CitizenResponseDto res = csdldcService.getCitizenInfo(newShareCitizenInfoRequestDto,configCSDL,username);
                if (res == null){
                    throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
                }
                ShareCitizenOrginInfoResponseDto newObjectCitizen = GsonUtils.copyObject(res.getInfo(), ShareCitizenOrginInfoResponseDto.class);
//                newCitizenDataDto.setBirthday(newObjectCitizen.getNgayThangNamSinh().getNgayThangNam());
//                newCitizenDataDto.setFullname(newObjectCitizen.getHoVaTen().getTen());
                newCitizenDataDto.setIdentityNumber9(newObjectCitizen.getSoCMND());
                newCitizenDataDto.setIdentityNumber12(newObjectCitizen.getSoDinhDanh());
                break;
            }
            case "3" : {
                //yyyyMMdd
                String agencyId = "?agency-id=012345678901234567890123";
                if (agencyCSDLId != null && !agencyCSDLId.isEmpty()){
                    agencyId = "?agency-id=" + agencyCSDLId;
                }
                String subSystemId = "&subsystem-id=5f7c16069abb62f511880003";
                if (subSystemCSDLId != null && !subSystemCSDLId.isEmpty()){
                    subSystemId = "&subsystem-id="+ subSystemId;
                }
                String URL = microservice.adapterUri("citizen/--info").toUriString();
                URL += agencyId;
                URL += subSystemId;
                URL += "&indentity-number=" + identityNumber;
                URL += "&fullname=" + fullname;
                URL += "&birthday=" + configInfService.toYYYYMMDD(birthday);
                logger.info("URL get citizen/--data truong hop 3 : " + URL);
                String tokenCheckCitizen = config.getParametersValue("checkCitizenToken");
                String decodeToken = StringHelper.decode(tokenCheckCitizen);
                String[] decodeTokens = decodeToken.split(",");
                String ssoURL = decodeTokens[0];
                String clientId = decodeTokens[1];
                String clientSecret = decodeTokens[2];
                String apiURL = decodeTokens[3];
                String key = decodeTokens[4];
                URL += "&key=" + key;
                CitizenResponseDto citizen = connectService.getJSon(URL,ssoURL,apiURL,clientId,clientSecret, CitizenResponseDto.class);
                if (citizen == null){
                    throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
                }
                ShareCitizenOrginInfoResponseDto newObjectCitizen = GsonUtils.copyObject(citizen.getInfo(), ShareCitizenOrginInfoResponseDto.class);
//                newCitizenDataDto.setBirthday(newObjectCitizen.getNgayThangNamSinh().getNgayThangNam());
//                newCitizenDataDto.setFullname(newObjectCitizen.getHoVaTen().getTen());
                newCitizenDataDto.setIdentityNumber9(newObjectCitizen.getSoCMND());
                newCitizenDataDto.setIdentityNumber12(newObjectCitizen.getSoDinhDanh());
                break;
            }
        }
        return newCitizenDataDto;
    }

    public String getParam(List<ParamDto> params, String key){
        try{
            List<ParamDto> _params = params.stream().filter(i->i.getKey().equals(key)).collect(Collectors.toList());
            return _params.get(0).getValue();
        }catch (Exception ex){
            return null;
        }
    }

    }
