package vn.vnpt.digo.adapter.service.bdg.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface TelegramAlert {

    String name() default "";

    int max() default 5;

    String groupChatId() default "-4058643510";
}
