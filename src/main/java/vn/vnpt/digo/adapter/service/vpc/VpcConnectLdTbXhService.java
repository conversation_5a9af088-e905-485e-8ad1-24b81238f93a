package vn.vnpt.digo.adapter.service.vpc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.KTM_Monre_Integration.Dossier;
import vn.vnpt.digo.adapter.dto.bdg.StatusDossierVDXP;
import vn.vnpt.digo.adapter.dto.dbn.DBNDossierApplyOnlineDto;
import vn.vnpt.digo.adapter.dto.dbn.ldtbxh.ExtentData;
import vn.vnpt.digo.adapter.dto.hbh.HistoryInputDto;
import vn.vnpt.digo.adapter.dto.hbh.btxh.*;
import vn.vnpt.digo.adapter.dto.hbh.log.LogParam;
import vn.vnpt.digo.adapter.dto.ktm_social_protection.KTMSocialProtectionGetDossierResultDTO;
import vn.vnpt.digo.adapter.dto.ktm_social_protection.KTMSocialProtectionParamsDto;
import vn.vnpt.digo.adapter.dto.ktm_social_protection.KTMSocialProtectionRequestDTO;
import vn.vnpt.digo.adapter.dto.ktm_social_protection.KTMSocialProtectionSyncStatusParamsDto;
import vn.vnpt.digo.adapter.dto.social_protection.SocialProtectionGetDossierDTO;
import vn.vnpt.digo.adapter.dto.social_protection.SocialProtectionGetDossierResultDTO;
import vn.vnpt.digo.adapter.dto.social_protection.SocialProtectionGetDossierResultDataDTO;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameDto;
import vn.vnpt.digo.adapter.dto.utils.SectorDetailDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.ParametersType;
import vn.vnpt.digo.adapter.pojo.ProcedureForm;
import vn.vnpt.digo.adapter.pojo.Translate;
import vn.vnpt.digo.adapter.service.IntegratedConfigurationService;
import vn.vnpt.digo.adapter.service.MappingDataService;
import vn.vnpt.digo.adapter.service.UtilService;
import vn.vnpt.digo.adapter.service.hbh.HbhEventLogService;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Oauth2RestTemplate;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.google.common.base.Strings.isNullOrEmpty;

@Service
@Slf4j
public class VpcConnectLdTbXhService {
    public final static ObjectId SERVICE_ID = new ObjectId("66cd5511a8a832c92a99ab32");
    @Autowired
    private Translator translator;
    @Autowired
    private IntegratedConfigurationService configurationService;
    @Autowired
    private Microservice microservice;
    @Autowired
    private MappingDataService mappingDataService;
    @Autowired
    private LgspSavisService lgspVpcService;
    @Autowired
    private Oauth2RestTemplate oauth2RestTemplate;
    @Autowired
    private HbhEventLogService eventLogService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private UtilService utilService;

    final SimpleDateFormat newSDF = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
    final SimpleDateFormat oldSDF = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    final SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy");
    final SimpleDateFormat vnDateSdf = new SimpleDateFormat("dd/MM/yyyy");
    final SimpleDateFormat sysDateTimeSdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");

    private static final String Key_MappingStatus = "MappingStatus";
    private static final String Key_MappingCommune = "MappingCommune";
    private static final String Key_MappingDistrict = "MappingDistrict";
    private static final String Key_MappingProvince = "MappingProvince";

    @Scheduled(cron = "${digo.schedule.vpc-ldtbxh.sync-ketQuaCuoiCung.cron:-}")
    public void syncKetQuaCuoiCungDossier() {
        IntegratedConfigurationDto config = configurationService.searchByServiceId(SERVICE_ID);
        if (Objects.isNull(config)) {
            log.info("syncHsDangKyDossier Config null. Stop");
            return;
        }
        log.info("syncKetQuaCuoiCungDossier Config "+config);
        Boolean enable = config.getParametersValue("EnableSync_LayKetQuaCuoiCung");
        if(enable == null || !enable) {
            log.info("syncKetQuaCuoiCungDossier Config not enable: "+enable);
            return;
        }
        String url = config.getParametersValue("URL_LayKetQuaCuoiCung");
        String acceptKey = config.getParametersValue("AcceptKey");
        String provinceCode = config.getParametersValue("MaTinh");
        String lastSync = config.getParametersValue("LastSync_LayKetQuaCuoiCung");

        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        Calendar from = Calendar.getInstance();
        try {
            Date fromDate = sdf.parse(lastSync);
            from.setTime(fromDate);
        } catch (Exception e) {
            log.error(e.getMessage());
            from = Calendar.getInstance();
            from.set(Calendar.HOUR, 0);
            from.set(Calendar.MINUTE, 0);
            from.set(Calendar.SECOND, 0);
        }
        String fromTime = sdf.format(from.getTime());

        Calendar to = Calendar.getInstance();
        to.setTime(from.getTime());
        to.add(Calendar.DATE, 1);
        String toTime = sdf.format(to.getTime());

        HashMap<String, Object> body = new HashMap<>();
        body.put("LoaiDuLieu", "100");
        body.put("MaTinh", provinceCode);
        body.put("ThoiGianBatDau", fromTime);
        body.put("ThoiGianKetThuc", toTime);
        body.put("AcceptKey", acceptKey);
        SocialProtectionGetDossierDTO response = null;
        //Log
        LogParam logParams = new LogParam();
        logParams.setRequestUrl(url);
        logParams.setRequestMethod("POST");
        logParams.setServiceName("VPC-LĐTBXH-LayKetQuaCuoiCung");
        logParams.setKey(provinceCode);
        try {
            response = lgspVpcService.postRequest(null, url, body, SocialProtectionGetDossierDTO.class);
            logResponse(response);
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, true, null);
        } catch (Exception e) {
            log.error("syncKetQuaCuoiCungDossier "+e.getMessage());
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, false, e.getMessage());
        }
        if(response == null) {
            log.info("syncKetQuaCuoiCungDossier: No data");
            return;
        }
        ArrayList<String> result = null;
        logParams = new LogParam();
        logParams.setKey(provinceCode);
        logParams.setServiceName("VPC-LĐTBXH-Sync-LayKetQuaCuoiCung");
        try {
            result = this.convertDossierFromBTXHToiGate(response.getResult(), config);
            updateLastSync("LastSync_LayKetQuaCuoiCung", from, to);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        eventLogService.writeLog(SERVICE_ID, logParams, response.getResult(), result, true, null);
    }

    @Scheduled(cron = "${digo.schedule.vpc-ldtbxh.sync-hoSoDangKy.cron:-}")
    public void syncHsDangKyDossier() {
        IntegratedConfigurationDto config = configurationService.searchByServiceId(SERVICE_ID);
        if (Objects.isNull(config)) {
            log.info("syncHsDangKyDossier Config null. Stop");
            return;
        }
        log.info("syncHsDangKyDossier Config "+config);
        Boolean enable = config.getParametersValue("EnableSync_LayHoSoDangKy");
        if(enable == null || !enable) {
            log.info("syncHsDangKyDossier Config not enable: "+enable);
            return;
        }
        String url = config.getParametersValue("URL_LayHoSoDangKy");
        String acceptKey = config.getParametersValue("AcceptKey");
        String provinceCode = config.getParametersValue("MaTinh");
        String lastSync = config.getParametersValue("LastSync_LayHoSoDangKy");

        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        Calendar from = Calendar.getInstance();
        try {
            Date fromDate = sdf.parse(lastSync);
            from.setTime(fromDate);
        } catch (Exception e) {
            log.error(e.getMessage());
            from = Calendar.getInstance();
            from.set(Calendar.HOUR, 0);
            from.set(Calendar.MINUTE, 0);
            from.set(Calendar.SECOND, 0);
        }
        String fromTime = sdf.format(from.getTime());

        Calendar to = Calendar.getInstance();
        to.setTime(from.getTime());
        to.add(Calendar.DATE, 1);
        String toTime = sdf.format(to.getTime());

        HashMap<String, Object> body = new HashMap<>();
        body.put("AcceptKey", acceptKey);
        body.put("LoaiDuLieu", "1");
        body.put("MaTinh", provinceCode);
        body.put("ThoiGianBatDau", fromTime);
        body.put("ThoiGianKetThuc", toTime);
        SocialProtectionGetDossierDTO response = null;
        //Log
        LogParam logParams = new LogParam();
        logParams.setRequestUrl(url);
        logParams.setRequestMethod("POST");
        logParams.setServiceName("VPC-LĐTBXH-LayHoSoDangKy");
        logParams.setKey(provinceCode);
        try {
            response = lgspVpcService.postRequest(null, url, body, SocialProtectionGetDossierDTO.class);
            logResponse(response);
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, true, null);
        } catch (Exception e) {
            log.error("syncHsDangKyDossier "+e.getMessage());
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, false, e.getMessage());
        }
        if(response == null) {
            log.info("syncHsDangKyDossier: No data");
            return;
        }
        ArrayList<String> result = null;
        logParams = new LogParam();
        logParams.setKey(provinceCode);
        logParams.setServiceName("VPC-LĐTBXH-Sync-LayHoSoDangKy");
        try {
            result = this.convertDossierFromBTXHToiGate(response.getResult(), config);
            updateLastSync("LastSync_LayHoSoDangKy", from, to);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("LastSync_LayHoSoDangKy" +e.getMessage());
        }
        eventLogService.writeLog(SERVICE_ID, logParams, response.getResult(), result, true, null);
    }

    public HashMap<String, ObjectId> getMappingId(IntegratedConfigurationDto config) {
        HashMap<String, ObjectId> mappingId = new HashMap<>();
        String mapStatusServiceId = config.getParametersValue("MappingStatusServiceId");
        String mapProvinceServiceId = config.getParametersValue("MappingProvinceServiceId");
        String mapDistrictServiceId = config.getParametersValue("MappingDistrictServiceId");
        String mappingCommuneServiceId = config.getParametersValue("MappingCommuneServiceId");
        try {
            mappingId.put(Key_MappingStatus, new ObjectId(mapStatusServiceId));
        } catch (Exception e) {
            log.error("getMappingId Key_MappingStatus: "+e.getMessage());
        }
        try {
            mappingId.put(Key_MappingProvince, new ObjectId(mapProvinceServiceId));
        } catch (Exception e) {
            log.error("getMappingId Key_MappingProvince: "+e.getMessage());
        }
        try {
            mappingId.put(Key_MappingDistrict, new ObjectId(mapDistrictServiceId));
        } catch (Exception e) {
            log.error("getMappingId Key_MappingDistrict: "+e.getMessage());
        }
        try {
            mappingId.put(Key_MappingCommune, new ObjectId(mappingCommuneServiceId));
        } catch (Exception e) {
            log.error("getMappingId Key_MappingCommune: "+e.getMessage());
        }
        return mappingId;
    }

    public ArrayList<String> convertDossierFromBTXHToiGate(SocialProtectionGetDossierResultDTO input, IntegratedConfigurationDto config) {
        HashMap<String, ObjectId> mappingId = getMappingId(config);
        ArrayList<String> message = new ArrayList<>();
        ArrayList<SocialProtectionGetDossierResultDataDTO> dataList = input.getData();
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        for (SocialProtectionGetDossierResultDataDTO dataDTO : dataList) {
            log.info("convertDossierFromBTXHToiGate code: " + dataDTO.getMaHoSo());

            DBNDossierApplyOnlineDto.ProcedureDto procedure = getProcedureFull(dataDTO, mappingId, DBNDossierApplyOnlineDto.ProcedureDto.class);
            log.info("getProcedureFull ProcedureDto" + procedure);
            if (procedure == null) {
                message.add("Hồ sơ " + dataDTO.getMaHoSo() + ": Không tìm thấy thủ tục " + dataDTO.getMaTTHC() + " của cơ quan ");
                continue;
            }
            PostProcedureDto procedureFull = getProcedureFull(dataDTO, mappingId, PostProcedureDto.class);
            log.info("getProcedureFull PostProcedureDto " + procedureFull);
            if (procedureFull == null) {
                message.add("Hồ sơ " + dataDTO.getMaHoSo() + ": Không tìm thấy thủ tục " + dataDTO.getMaTTHC());
                continue;
            }
            //Procedure level
            DBNDossierApplyOnlineDto.IdNameDto procedureLevel = null;
            try {
                procedureLevel = mapper.convertValue(procedureFull.getLevel(), DBNDossierApplyOnlineDto.IdNameDto.class);
            } catch (Exception e) {
                log.error("procedureLevel convertValue: "+e.getMessage());
            }
            // Quy trinh thu tuc
            DBNDossierApplyOnlineDto.ProcessDto process = getProcess(procedure.getId());
            if (process == null) {
                message.add("Hồ sơ " + dataDTO.getMaHoSo() + ": Không tìm thấy quy trình của thủ tục " + procedure.getId());
                continue;
            }
            // Agency
            AgencyFullyDto agencyFullyDto = getAgency(procedureFull.getAgency().get(0).getId().toHexString());
            if (agencyFullyDto == null) {
                message.add("Hồ sơ " + dataDTO.getMaHoSo() + ": Không tìm thấy đơn vị");
                continue;
            }
            DBNDossierApplyOnlineDto.AgencyDto agency = convertAgency(agencyFullyDto);
            // Nguoi nop, quy trinh
            DBNDossierApplyOnlineDto.ApplicantDto applicantDto = getApplicant(dataDTO);
            LinkedHashMap processDefinition = (LinkedHashMap) process.getProcessDefinition();
            try {
                String applicantEformId = ((LinkedHashMap) processDefinition.get("applicantEForm")).get("id").toString();
                applicantDto.setEformId(applicantEformId);
            } catch (Exception e){
                log.error("applicantEForm error: "+e.getMessage());
            }
            // Eform
            DBNDossierApplyOnlineDto.FormDto eFormDto = new DBNDossierApplyOnlineDto.FormDto();
            try {
                String eformId = ((LinkedHashMap) processDefinition.get("eForm")).get("id").toString();
                eFormDto.setId(eformId);
            } catch (Exception e){
                log.error("applicantEForm error: "+e.getMessage());
            }
            eFormDto.setData(getEformData(dataDTO));
            GetDossierOnlineDto existDossier = hasDossier(dataDTO.getMaHoSo());
            if (existDossier != null) {
                message.add("Hồ sơ " + dataDTO.getMaHoSo() + ": " + "Đã tồn taị: Cập nhật trạng thái");
                updateDossierStatus(existDossier.getId(), dataDTO, agency,
                        procedure, procedureLevel, process,
                        applicantDto,
                        eFormDto);
                continue;
            }
            DBNDossierApplyOnlineDto dossierApplyOnline = new DBNDossierApplyOnlineDto();
            dossierApplyOnline.setCode(dataDTO.getMaHoSo());
            if(!"1".equals(dataDTO.getStatusId())) {
                Date newAcceptDate = getDateFromBhxh(dataDTO.getNgayTiepNhan());
                dossierApplyOnline.setAcceptedDate(newAcceptDate);
                Date newAppointmentDate = getDateFromBhxh(dataDTO.getNgayHenTra());
                dossierApplyOnline.setAppointmentDate(newAppointmentDate);
            }
            dossierApplyOnline.setDossierReceivingKind(this.getDossierReceivingKind(dataDTO.getHinhThucTra(), config));

            dossierApplyOnline.setProcedure(procedure);
            dossierApplyOnline.setProcedureLevel(procedureLevel);
            dossierApplyOnline.setAgency(agency);
            dossierApplyOnline.setDossierConvert(new DBNDossierApplyOnlineDto.DossierConvert(2, "LĐXH"));
            dossierApplyOnline.setDossierTaskStatus(getDossierTaskStatus(dataDTO.getStatusId()));
            dossierApplyOnline.setDossierMenuTaskRemind(getDossierTaskReminder(dataDTO.getStatusId()));
            // Trang thai ho so
            dossierApplyOnline.setDossierStatus(getDossierStatusCode(dataDTO.getStatusId()));
            dossierApplyOnline.setProcedureProcessDefinition(process);
            // Thong tin nguoi nop, quy trinh
            dossierApplyOnline.setApplicant(applicantDto);
            dossierApplyOnline.setEForm(eFormDto);
            // ngày hoàn thành xử lý
            if(Objects.nonNull(dataDTO.getNgayKetThucXuLy_ThucTe()) && "10".equals(dataDTO.getStatusId()) ){
                dossierApplyOnline.setCompletedDate(getDateFromBhxh(dataDTO.getNgayKetThucXuLy_ThucTe()));
            }
            // File ket qua
            ExtentData extentData = this.xmlToObjectConverter(dataDTO.getCertificateExtentData());
            if(extentData.getDanhSachGiayToKetQua() != null) {
                dossierApplyOnline.setAttachment(this.getAttachmentResult(extentData.getDanhSachGiayToKetQua().getGiayToKetQuas()));
            }

            String newDossierUrl = microservice.padmanUri("/dossier/--apply-online").toUriString();
            IdDto dossier = null;
            try {
                dossier = MicroserviceExchange.postJsonNoAuth(this.getRestTemplate(), newDossierUrl, dossierApplyOnline, IdDto.class);
            } catch (Exception e) {
                log.error("Luu ho so loi: " + e.getMessage());
                message.add("Hồ sơ " + dataDTO.getMaHoSo() + ": Lưu thất bại " + e.getMessage());
            }
            if (dossier == null) {
                continue;
            }
            message.add("Hồ sơ " + dataDTO.getMaHoSo() + ": Lưu thành công");
            updateDossierFormFile(extentData, dossier.getId().toHexString());
        }
        return message;
    }

    public EFormDataDto getEformData(SocialProtectionGetDossierResultDataDTO data) {
        EFormDataDto eform = new EFormDataDto();
        //Hktt
        eform.setHKTT_MaTinh(getDiaDanhFormIO(1, data.getHktT_MaTinh()));
        eform.setHKTT_MaHuyen(getDiaDanhFormIO(2, data.getHktT_MaHuyen()));
        eform.setHKTT_MaXa(getDiaDanhFormIO(3, data.getHktT_MaXa()));
        eform.setHKTT_MaThon(data.getHktT_MaThon());
        eform.setHKTT_ChiTiet(data.getHktT_ChiTiet());
        //Noht
        eform.setNOHT_MaTinh(getDiaDanhFormIO(1, data.getNohT_MaTinh()));
        eform.setNOHT_MaHuyen(getDiaDanhFormIO(2, data.getNohT_MaHuyen()));
        eform.setNOHT_MaXa(getDiaDanhFormIO(3, data.getNohT_MaXa()));
        eform.setNOHT_MaThon(data.getNohT_MaThon());
        eform.setNOHT_ChiTiet(data.getNohT_ChiTiet());
        //LDD
        eform.setSoDinhDanh(data.getSoDinhDanh());
        eform.setLoaiDinhDanh(getLoaiDinhDanhFormIO(data.getLoaiDinhDanh()));
        eform.setLoaiDoiTuong(getLoaiDoiTuongFormIO(data.getLoaiDoiTuong()));
        return eform;
    }

    public static EFormDataDto.SelectFormIO getLoaiDinhDanhFormIO(String value) {
        EFormDataDto.SelectFormIO selectForm = new EFormDataDto.SelectFormIO();
        selectForm.setValue(value);
        if("1".equals(value)) {
            selectForm.setLabel("Căn cước công dân");
        } else if("2".equals(value)) {
            selectForm.setLabel("Chứng minh nhân dân");
        } else if("3".equals(value)) {
            selectForm.setLabel("Mã số thuế doanh nghiệp");
        }
        return selectForm;
    }

    public static EFormDataDto.SelectFormIO getLoaiDoiTuongFormIO(String value) {
        EFormDataDto.SelectFormIO selectForm = new EFormDataDto.SelectFormIO();
        selectForm.setValue(value);
        if("1".equals(value)) {
            selectForm.setLabel("Người dân");
        } else if("2".equals(value)) {
            selectForm.setLabel("Doanh nghiệp");
        } else if("3".equals(value)) {
            selectForm.setLabel("Cơ quan nhà nước");
        } else if("4".equals(value)) {
            selectForm.setLabel("Tổ chức khác");
        }
        return selectForm;
    }

    public EFormDataDto.SelectFormIO getDiaDanhFormIO(int type, String code) {
        EFormDataDto.SelectFormIO selectForm = new EFormDataDto.SelectFormIO();
        PlaceDetailDto place = null;
        try {
            if (type == 1) {
                place = utilService.getProvinceByCode(code);
            } else if (type == 2) {
                place = utilService.getDistrictByCode(code);
            } else if (type == 3) {
                place = utilService.getWardByCode(code);
            }
            if (place != null) {
                selectForm.setLabel(place.getName());
                selectForm.setValue(place.getId());
            }
        } catch (Exception e) {
            log.error("getDiaDanhFormIO "+e.getMessage());
        }
        return selectForm;
    }

    private RestTemplate getRestTemplate() {
        return oauth2RestTemplate.getOAuth2RestTemplate();
    }

    public DBNDossierApplyOnlineDto.RecvKindDto getDossierTaskRemind(String loaiDulieu) {
        DBNDossierApplyOnlineDto.RecvKindDto dossierTaskStatus = new DBNDossierApplyOnlineDto.RecvKindDto();

        List<DBNDossierApplyOnlineDto.NameDto> name1 = new ArrayList<>() {
            {
                add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Mới đăng ký"));
                add(new DBNDossierApplyOnlineDto.NameDto((short) 46, "Just signed up"));

            }
        };
        List<DBNDossierApplyOnlineDto.NameDto> name100 = new ArrayList<>() {
            {
                add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Đã trả kết quả"));
                add(new DBNDossierApplyOnlineDto.NameDto((short) 46, "Results returned"));

            }
        };
        if("100".equals(loaiDulieu)) {
            dossierTaskStatus.setId("60f52f0109cbf91d41f88839");
            dossierTaskStatus.setName(name100);
        } else if("1".equals(loaiDulieu)) {
            dossierTaskStatus.setId("60f52e0d09cbf91d41f88834");
            dossierTaskStatus.setName(name1);
        }
        return dossierTaskStatus;
    }

    private String formatDate(String input) {
        if (input == null || input.isEmpty()) {
            return null;
        }
        String newDate = null;
        try {
            Date oldDate = oldSDF.parse(input);
            newDate = newSDF.format(oldDate);
        } catch (Exception ignored) {
        }
        return newDate;
    }

    private Date getDateFromBhxh(String input) {
        if(input == null) {
            return null;
        }
        String _newAppliedDate = this.formatDate(input);
        try {
            return newSDF.parse(_newAppliedDate);
        } catch (Exception e) {
            log.error("getDateFromBhxh: "+e.getMessage());
        }
        return null;
    }

    private String convertFormatDate(String input, SimpleDateFormat fromSdf, SimpleDateFormat toSdf) {
        if (input == null || input.isEmpty()) {
            return "";
        }
        String newDate = "";
        try {
            Date oldDate = fromSdf.parse(input);
            newDate = toSdf.format(oldDate);
        } catch (Exception ignored) {
        }
        return newDate;
    }

    public DBNDossierApplyOnlineDto.RecvKindDto getDossierReceivingKind(String hinhThuc, IntegratedConfigurationDto config) {
        String nhanBcciId = config.getParametersValue("NhanQuaBuuChinhId");
        String nhanTrucTiepId = config.getParametersValue("NhanTrucTiepId");
        DBNDossierApplyOnlineDto.RecvKindDto dossierReceivingKind = new DBNDossierApplyOnlineDto.RecvKindDto();
        var transReceivingKind = new DBNDossierApplyOnlineDto.NameDto();
        transReceivingKind.setLanguageId((short) 228);
        if ("1".equals(hinhThuc)) {
            transReceivingKind.setName("Nhận Qua dịch vụ BCCI");
            dossierReceivingKind.setId(nhanBcciId);

        } else {//Trả kết quả tại bộ phận tiếp  nhận  và  trả  kết  quả
            dossierReceivingKind.setId(nhanTrucTiepId);
            transReceivingKind.setName("Nhận trực tiếp");
        }
        ArrayList<DBNDossierApplyOnlineDto.NameDto> translateNames = new ArrayList<>();
        translateNames.add(transReceivingKind);
        dossierReceivingKind.setName(translateNames);
        return dossierReceivingKind;
    }

    public DBNDossierApplyOnlineDto.ApplicantDto getApplicant(SocialProtectionGetDossierResultDataDTO dataDTO) {
        DBNDossierApplyOnlineDto.ApplicantDto applicant = new DBNDossierApplyOnlineDto.ApplicantDto();
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        var applicantData = new HashMap<>();
        applicantData.put("fullname", dataDTO.getHoVaTen());
        applicantData.put("identityNumber", dataDTO.getCmnd());
        applicantData.put("phoneNumber", dataDTO.getSoDienThoai());
        applicantData.put("email", dataDTO.getEmail());
        applicantData.put("ownerFullname", dataDTO.getHoVaTen());
        applicantData.put("isOwnerDossier", true);
        applicantData.put("ownerIdentityNumber", dataDTO.getCmnd());
        applicantData.put("ownerPhoneNumber", dataDTO.getSoDienThoai());
        applicantData.put("address", dataDTO.getHktT_ChiTiet());
        applicantData.put("ownerAddress", dataDTO.getHktT_ChiTiet());
        String newNgayThangNamSinh = this.convertFormatDate(dataDTO.getNgayThangNamSinh(), vnDateSdf, sysDateTimeSdf);
        applicantData.put("birthday", newNgayThangNamSinh);
        String newCmnD_NgayCap = this.convertFormatDate(dataDTO.getCmnD_NgayCap(), vnDateSdf, sysDateTimeSdf);
        applicantData.put("identityDate", newCmnD_NgayCap);
        applicantData.put("nation", new DBNDossierApplyOnlineDto.AddressDetailDto("Việt Nam", "5f39f4a95224cf235e134c5c"));
        if (!dataDTO.getGioiTinhId().isEmpty()) {
            applicantData.put("gender", dataDTO.getGioiTinhId().equals("1034") ? 1 : 2);//1034 : Nam, 1035: Nữ
        }
        AgencyFullyDto noiCap = getAgencyByCode(dataDTO.getCmnD_NoiCap());
        if(noiCap != null) {
            Dossier.IdName idName = new Dossier.IdName();
            idName.setId(noiCap.getId().toHexString());
            idName.setName(noiCap.getTransName((short)228));
            applicantData.put("identityAgency", idName);
        }
        //Tinh

        PlaceDetailDto placeProvince = utilService.getProvinceByCode(dataDTO.getHktT_MaTinh());
        if (placeProvince != null) {
            DBNDossierApplyOnlineDto.AddressDetailDto province = new DBNDossierApplyOnlineDto.AddressDetailDto();
            province.setLabel(placeProvince.getName());
            province.setValue(placeProvince.getId());
            applicantData.put("province", province);
        }
        PlaceDetailDto placeDistrict = utilService.getDistrictByCode(dataDTO.getHktT_MaHuyen());
        if (placeDistrict != null) {
            DBNDossierApplyOnlineDto.AddressDetailDto district = new DBNDossierApplyOnlineDto.AddressDetailDto();
            district.setLabel(placeDistrict.getName());
            district.setValue(placeDistrict.getId());
            applicantData.put("district", district);
        }
        PlaceDetailDto placeCommune = utilService.getWardByCode(dataDTO.getHktT_MaXa());
        if (placeCommune != null) {
            DBNDossierApplyOnlineDto.AddressDetailDto commune = new DBNDossierApplyOnlineDto.AddressDetailDto();
            commune.setLabel(placeCommune.getName());
            commune.setValue(placeCommune.getId());
            applicantData.put("village", commune);
        }
        applicant.setData(applicantData);
        return applicant;

    }

    public <T> T getProcedureFull(SocialProtectionGetDossierResultDataDTO dataDTO, HashMap<String, ObjectId> mappingId, Class<T> clazz) {
        MappingDataDto mappingData = null;
        if (!Strings.isNullOrEmpty(dataDTO.getMaXa())) {
            mappingData = mappingDataService.getByDestNoDeployment(mappingId.get(Key_MappingCommune), dataDTO.getMaXa());
        }
        if (mappingData == null && !Strings.isNullOrEmpty(dataDTO.getMaHuyen())) {
            mappingData = mappingDataService.getByDestNoDeployment(mappingId.get(Key_MappingDistrict), dataDTO.getMaHuyen());
        }
        if (mappingData == null && !Strings.isNullOrEmpty(dataDTO.getMaTinh())){
            mappingData = mappingDataService.getByDestNoDeployment(mappingId.get(Key_MappingProvince), dataDTO.getMaTinh());
        }
        log.info("getProcedureFull mappingData: " + mappingData);
        if (mappingData == null || mappingData.getSource().getId() == null) {
            return null;
        }
        String agencyId = mappingData.getSource().getId();
        String url = microservice.basepadUri("procedure/--get-by-BTXH-code" + "?code=" + dataDTO.getMaTTHC() + "&agency-id=" + agencyId).toUriString();
        log.info("getProcedureFull url: " + url);
        try {
            return MicroserviceExchange.getNoAuth(this.getRestTemplate(), url, clazz);
        } catch (Exception e) {
            log.error("getFullProcedure: " + e.getMessage());
        }
        return null;
    }

    public GetDossierOnlineDto hasDossier(String code) {
        log.info("Check ho so: " + code);
        GetDossierOnlineDto res = null;
        try {
            res = utilService.getDossierByCode(getRestTemplate(), code);
        } catch (Exception e) {
            log.error("Check ho so err: " + e.getMessage());
        }
        if(res != null) {
            return res;
        }
        log.info("Check ho so: " + code + " khong ton tai");
        return null;

    }

    private void logResponse(Object res) {
        if (res == null) {
            log.info("DIGO-Response: NULL");
            return;
        }
        log.info("DIGO-Response: " + res.toString());
    }

    public AffectedRowsDto updateLastSync(String key, Calendar fromDate, Calendar toDate) {
        Calendar toDay = Calendar.getInstance();
        Date lastSyncDate = (DateUtils.isSameDay(toDay, fromDate)) ? fromDate.getTime() : toDate.getTime();
        String lastDateUpdateSync = oldSDF.format(lastSyncDate);
        IntegratedConfigurationDto config = configurationService.searchByServiceId(SERVICE_ID);
        if (Objects.isNull(config)) {
            return new AffectedRowsDto(-1, "Config not found");
        }
        config.setParametersValue(key, lastDateUpdateSync, new ParametersType(1, "String"));

        IntegratedConfiguration configuration = mongoTemplate.findById(config.getId(), IntegratedConfiguration.class);
        configuration.setParameters(config.getParameters());
        mongoTemplate.save(configuration);
        return new AffectedRowsDto(1);
    }

    public DBNDossierApplyOnlineDto.ProcessDto getProcess(String procedureId) {
        String processUrl = microservice.basepadUri("/procedure-process-definition/--apply-online?procedure-id=" + procedureId).toUriString();
        log.info("getProcess "+processUrl);
        DBNDossierApplyOnlineDto.ProcessDto[] process = null;
        try {
            process = MicroserviceExchange.getNoAuth(this.getRestTemplate(), processUrl, DBNDossierApplyOnlineDto.ProcessDto[].class);
            log.info("getProcess "+process);
        } catch (Exception e) {
            log.error("getProcess "+e.getMessage());
        }

        if(process != null && process.length > 0) {
            return process[0];
        }
        return null;
    }

    public AffectedRowsDto pushSyncDossier(KTMSocialProtectionParamsDto params) throws ParseException {
        KTMSocialProtectionRequestDTO dossier = params.getDossier();
        var affectedRowsDto = new AffectedRowsDto(0);

        IntegratedConfigurationDto config = null;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.searchByServiceId(SERVICE_ID);
        }

        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        HashMap<String, ObjectId> mappingId = getMappingId(config);
        mappingData(dossier, mappingId);

        String url = "1".equals(dossier.getLoaiDuLieu()) ? config.getParametersValue("URL_DongBoHoSoDangKy") : config.getParametersValue("URL_CapNhatKetQuaCuoiCung").toString();
        HashMap<String, String> input = createBodySyncDossierToLDXH(dossier, config);
        if (config.getParametersValue("MaTinh").equals("11")) {
            String acceptKey = config.getParametersValue("AccessKey");
            input.put("AcceptKey", acceptKey);
        }
        KTMSocialProtectionGetDossierResultDTO result = null;
        // Log param
        LogParam logParams = new LogParam();
        logParams.setRequestUrl(url);
        logParams.setRequestMethod("POST");
        String serviceName = "1".equals(dossier.getLoaiDuLieu()) ? "LĐTBXH-DongBoHsTiepNhan" : "LĐTBXH-CapnhatKetQuaCuoiCung";
        logParams.setServiceName(serviceName);
        logParams.setKey(input.get("MaHoSo"));
        try {
            result = lgspVpcService.postRequest(null, url, input, KTMSocialProtectionGetDossierResultDTO.class);
            logResponse(result);
            affectedRowsDto.setMessage(result.getMessage());
            if ( result.getError_code() == 0) {
                eventLogService.writeLog(SERVICE_ID, logParams, input, result, true, null);
                affectedRowsDto.setAffectedRows(1);
            } else {
                eventLogService.writeLog(SERVICE_ID, logParams, input, result, false, null);
            }
        } catch (Exception e) {
            affectedRowsDto.setMessage(e.getMessage());
            log.info("Exception: " + e.getMessage());
            eventLogService.writeLog(SERVICE_ID, logParams, input, null, false, e.getMessage());
        }
        return affectedRowsDto;
    }

    public void mappingData(KTMSocialProtectionRequestDTO input, HashMap<String, ObjectId> mappingId) {
        String maTinh = input.getMaTinh();
        String maHuyen = input.getMaHuyen();
        String maXa = input.getMaXa();
        String maTinhMapping = null;
        String maHuyenMapping = null;
        String maXaMapping = null;
        try {
            if (!Strings.isNullOrEmpty(maTinh)) {
                maTinhMapping = mappingDataService.getBySource(mappingId.get(Key_MappingProvince), input.getMaTinh()).getDest().getId();
            }

            if (!Strings.isNullOrEmpty(maHuyen)) {
                maHuyenMapping = mappingDataService.getBySource(mappingId.get(Key_MappingDistrict), input.getMaHuyen()).getDest().getId();
            }

            if (!Strings.isNullOrEmpty(maXa)) {
                maXaMapping = mappingDataService.getBySource(mappingId.get(Key_MappingCommune), input.getMaXa()).getDest().getId();
            }
        } catch (Exception e) {
            log.error("Error mapping dossier: " + translator.toLocale("lang.phrase.mapping-data"));
        }
        input.setMaTinh(maTinhMapping);
        input.setMaHuyen(maHuyenMapping);
        input.setMaXa(maXaMapping);
    }

    public AffectedRowsDto pushSyncStatusDossier(KTMSocialProtectionParamsDto params) {
        KTMSocialProtectionSyncStatusParamsDto dossier = params.getStatusDossier();
        var affectedRowsDto = new AffectedRowsDto(0);

        if (dossier == null) {
            affectedRowsDto.setMessage(translator
                    .toLocale("digo.http.response.error.10000", new String[]{"HoSo"}));
            return affectedRowsDto;
        }
        IntegratedConfigurationDto config = null;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.searchByServiceId(SERVICE_ID);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String url = config.getParametersValue("URL_DongBoTienTrinhXuLy");

        HashMap<String, String> inputTask = createBodySyncTask(dossier);
        if (config.getParametersValue("MaTinh").equals("11")) {
            String acceptKey = config.getParametersValue("AccessKey");
            inputTask.put("AcceptKey", acceptKey);
        }
        KTMSocialProtectionGetDossierResultDTO result = null;
        // Log param
        LogParam logParams = new LogParam();
        logParams.setRequestUrl(url);
        logParams.setRequestMethod("POST");
        logParams.setServiceName("ĐTBXH-DongBoTienTrinhXuLy");
        logParams.setKey(dossier.getMaHoSo());
        try {
            result = lgspVpcService.postRequest(null, url, inputTask, KTMSocialProtectionGetDossierResultDTO.class);
            logResponse(result);

            if (result != null) {
                affectedRowsDto.setMessage(result.getMessage());
            }
            if (result != null && result.getError_code() == 0) {
                eventLogService.writeLog(SERVICE_ID, logParams, inputTask, result, true, null);
                affectedRowsDto.setAffectedRows(1);
            } else {
                eventLogService.writeLog(SERVICE_ID, logParams, inputTask, result, false, null);
            }
        } catch (Exception e) {
            affectedRowsDto.setMessage(e.getMessage());
            log.info("Exception: " + e.getMessage());
            eventLogService.writeLog(SERVICE_ID, logParams, dossier, null, false, e.getMessage());
        }
        return affectedRowsDto;
    }

    public HashMap<String, String> createBodySyncDossierToLDXH(KTMSocialProtectionRequestDTO params, IntegratedConfigurationDto config) throws ParseException {

        DossierDetailDto dossier = this.getDossierDetail(params.getMaHoSo()); //AcceptKey đc hiểu là dossierId sau sẽ set lại giá trị
        HashMap<String, String> input = new HashMap<>();
        input.put("LoaiDuLieu", params.getLoaiDuLieu());
        input.put("MaHoSo", dossier.getCode());
        //Lấy thông tin sector: Do 1 số hs k lấy đc lưu đc sector trong procedure
        String sectorCode = dossier.getProcedure().getSector().getCode();
        if (Objects.isNull(sectorCode)) {
            try {
                SectorDetailDto sector = utilService.getSector(this.getRestTemplate(), new ObjectId(dossier.getProcedure().getSector().getId()));
                sectorCode = sector.code;
            } catch (Exception ignored) {}
        }
        input.put("MaLinhVuc", sectorCode);
        input.put("TenLinhVuc", dossier.getProcedure().getSector().getTransName());
        input.put("KenhThucHien", params.getKenhThucHien());

        String HinhThucTra = Objects.nonNull(dossier.getDossierReceivingKind()) && config.getParametersValue("NhanTrucTiepId").equals(dossier.getDossierReceivingKind().getId().toString()) ? "0" : "1";
        input.put("HinhThucTra", HinhThucTra);

        input.put("MaTTHC", params.getMaTTHC());
        input.put("TenTTHC", dossier.getProcedure().getTransName());
        input.put("HoVaTen", dossier.getApplicant().getData().getFullname());
        input.put("NgayThangNamSinh", formatDDMMYYY(dossier.getApplicant().getData().getBirthday()));
        input.put("GioiTinhId", dossier.getApplicant().getData().getGender().equals("1") ? "1034" : "1035");
        input.put("DanTocId", "");
        input.put("CMND", dossier.getApplicant().getData().getIdentityNumber());
        input.put("CMND_NgayCap", formatDDMMYYY(dossier.getApplicant().getData().getIdentityDate()));
        input.put("CMND_NoiCap", "");
        input.put("MaTinh", params.getMaTinh());
        input.put("MaHuyen", params.getMaHuyen());
        input.put("MaXa", params.getMaXa());
        input.put("SoDienThoai", dossier.getApplicant().getData().getPhoneNumber());
        input.put("Email", dossier.getApplicant().getData().getEmail());

        String ngayTiepNhan = Objects.nonNull(dossier.getAcceptedDate()) ? oldSDF.format(dossier.getAcceptedDate()) : oldSDF.format(new Date());
        input.put("NgayTiepNhan", ngayTiepNhan);
        input.put("DonViXuLy", Objects.nonNull(dossier.getAgency().getParent()) ? dossier.getAgency().getParent().getTransName() : "");
        input.put("NoiNopHoSo", "2");

        getDossierFormFile(dossier, params);
        input.put("HoSoCoThanhPhanSoHoa", params.getHoSoCoThanhPhanSoHoa());
        input.put("TaiKhoanDuocXacThucVoiVNeID", "0");
        input.put("DuocThanhToanTrucTuyen", params.getDuocThanhToanTrucTuyen());

        input.put("HKTT_MaTinh", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getHKTT_MaTinh())
        && Objects.nonNull(dossier.getEForm().getData().getHKTT_MaTinh().getValue())) ?
                utilService.getProvinceCode(dossier.getEForm().getData().getHKTT_MaTinh().getValue()) : null);
        input.put("HKTT_MaHuyen", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getHKTT_MaHuyen())
        && Objects.nonNull(dossier.getEForm().getData().getHKTT_MaHuyen().getValue())) ?
                utilService.getDistrictCode(dossier.getEForm().getData().getHKTT_MaHuyen().getValue()) : null);
        input.put("HKTT_MaXa", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getHKTT_MaXa())
        && Objects.nonNull(dossier.getEForm().getData().getHKTT_MaXa().getValue())) ?
                utilService.getWardCode(dossier.getEForm().getData().getHKTT_MaXa().getValue()) : null);
        input.put("NOHT_MaTinh", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getHKTT_MaTinh())
        && Objects.nonNull(dossier.getEForm().getData().getHKTT_MaTinh().getValue())) ?
                utilService.getProvinceCode(dossier.getEForm().getData().getNOHT_MaTinh().getValue()) : null);
        input.put("NOHT_MaHuyen", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getNOHT_MaHuyen())
        && Objects.nonNull(dossier.getEForm().getData().getNOHT_MaHuyen().getValue())) ?
                utilService.getDistrictCode(dossier.getEForm().getData().getNOHT_MaHuyen().getValue()) : null);
        input.put("NOHT_MaXa", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getNOHT_MaXa())
        && Objects.nonNull(dossier.getEForm().getData().getNOHT_MaXa().getValue())) ?
                utilService.getWardCode(dossier.getEForm().getData().getNOHT_MaXa().getValue()) : null);
        input.put("NOHT_MaThon", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getNOHT_MaThon())) ? dossier.getEForm().getData().getNOHT_MaThon() : null);
        input.put("HKTT_MaThon", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getHKTT_MaThon())) ? dossier.getEForm().getData().getHKTT_MaThon() : null);
        input.put("HKTT_ChiTiet", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getHKTT_ChiTiet())) ? dossier.getEForm().getData().getHKTT_ChiTiet() : null);
        input.put("NOHT_ChiTiet", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getNOHT_ChiTiet())) ? dossier.getEForm().getData().getNOHT_ChiTiet() : null);

        String loaiDinhDanh = "1";
        String ownerIdentityNumber = Objects.nonNull(dossier.getApplicant().getData().getOwnerIdentityNumber()) ?
                dossier.getApplicant().getData().getOwnerIdentityNumber() : dossier.getApplicant().getData().getIdentityNumber();
        if (ownerIdentityNumber.length() == 12) {
            loaiDinhDanh = "1";
        } else if (ownerIdentityNumber.length() == 9) {
            loaiDinhDanh = "2";
        } else {
            loaiDinhDanh = "3";
        }
        input.put("LoaiDinhDanh", loaiDinhDanh);
        input.put("LoaiDoiTuong", (Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getLoaiDoiTuong())) ? dossier.getEForm().getData().getLoaiDoiTuong().getValue() : "1");
        String statusMapping = "1";
        try {
            IdCodeNameDto status = utilService.getTag(this.getRestTemplate(), dossier.getDossierTaskStatus().getId());
            if (Objects.nonNull(status) && !isNullOrEmpty(status.getIntegratedCode())) {
                statusMapping = status.getIntegratedCode();
            }
        } catch (Exception ignored){}
        input.put("StatusId", statusMapping);
        input.put("UrlDetail", params.getUrlDetail());
        input.put("SubmitedDate", params.getSubmitedDate());
        input.put("CertificateExtentData", params.getCertificateExtentData());
        input.put("MaCSDL", "1");
        input.put("SoDinhDanh", Objects.nonNull(dossier.getEForm()) && Objects.nonNull(dossier.getEForm().getData().getSoDinhDanh()) ? dossier.getEForm().getData().getSoDinhDanh() : "1");

        return input;
    }

    public HashMap<String, String> createBodySyncTask(KTMSocialProtectionSyncStatusParamsDto params) {
        HashMap<String, String> inputTask = new HashMap<>();
        inputTask.put("MaHoSo", params.getMaHoSo());
        inputTask.put("TaiKhoanXuLy", params.getTaiKhoanXuLy());
        inputTask.put("NguoiXuLy", params.getNguoiXuLy());
        inputTask.put("ChucDanh", params.getChucDanh());
        inputTask.put("ThoiDiemXuLy", params.getThoiDiemXuLy());
        inputTask.put("DonViXuLy", params.getDonViXuLy());
        inputTask.put("NoiDungXuLy", params.getNoiDungXuLy());
        inputTask.put("NgayBatDau", params.getNgayBatDau());
        inputTask.put("NgayKetThucTheoQuyDinh", params.getNgayKetThucTheoQuyDinh());
        inputTask.put("UseridCreated", params.getUseridCreated());
        inputTask.put("UseridEdited", params.getUseridEdited());
        inputTask.put("DateCreated", params.getDateCreated());
        inputTask.put("DateEdited", params.getDateEdited());
        String statusMapping = "";
        if (ObjectId.isValid(params.getStatusId())) {
            try {
                IdCodeNameDto status = utilService.getTag(this.getRestTemplate(), new ObjectId(params.getStatusId()));
                if (Objects.nonNull(status) && !isNullOrEmpty(status.getIntegratedCode())) {
                    statusMapping = status.getIntegratedCode();
                }
            } catch (Exception ignored){}
        } else {
           statusMapping = !isNullOrEmpty(params.getStatusId()) ? params.getStatusId() : "";
        }
        inputTask.put("StatusId", statusMapping);
        return inputTask;
    }
    public void getDossierFormFile(DossierDetailDto dossier, KTMSocialProtectionRequestDTO params) {
        List<DossierDetailDto.FormFile> dossierFormFile = dossier.getDossierFormFile();
        String checkHoSoCoThanhPhanSoHoa = "0";
        StringBuilder xmlResult = new StringBuilder("<?xml version='1.0' encoding='utf-8'?><ExtentData xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"><DanhsachTaiLieuNop>");

        if (!dossierFormFile.isEmpty()) {
            for (DossierDetailDto.FormFile formFile : dossierFormFile) {
                if (Objects.isNull(formFile.getFile()) || formFile.getFile().isEmpty()) {
                    continue;
                }
                for (DossierDetailDto.AttachmentDto file: formFile.getFile()) {
                    xmlResult.append("<TaiLieuNop>" + "<TepDinhKemId>").append(file.getId()).append("</TepDinhKemId>").append("<TenTepDinhKem>").append(file.getFilename()).append("</TenTepDinhKem>").append("<DuongDanTaiTepTin>").append(microservice.filemanUri("/wopi/files/" + file.getId() + "/contents").encode().build().toUriString()).append("</DuongDanTaiTepTin>").append("<MaThanhPhanHoSo />").append("<DuocSoHoa>0</DuocSoHoa>").append("<DuocTaiSuDung>0</DuocTaiSuDung>").append("<DuocLayTuKhoDMQG>0</DuocLayTuKhoDMQG>").append("<MaKetQuaThayThe>0</MaKetQuaThayThe></TaiLieuNop>");
                }
                checkHoSoCoThanhPhanSoHoa = "1";
            }
        }
        xmlResult.append("</DanhsachTaiLieuNop>");
        if ("1".equals(params.getLoaiDuLieu())) {
            xmlResult.append("<DanhSachGiayToKetQua />");
        } else if ("100".equals(params.getLoaiDuLieu())) {
            xmlResult.append("<DanhSachGiayToKetQua>");
            if (Objects.nonNull(dossier.getAttachment()) && !dossier.getAttachment().isEmpty()) {
                for (DossierDetailDto.AttachmentDto attach : dossier.getAttachment()) {
                    if (!attach.getGroup().toString().equals("5f9bd9692994dc687e68b5a6")) {
                        continue;
                    }
                    xmlResult.append("<GiayToKetQua>" + "<TenGiayTo>").append(attach.getFilename()).append("</TenGiayTo>").append("<MaThanhPhanHoSo>0</MaThanhPhanHoSo>").append("<GiayToId>").append(attach.getId()).append("</GiayToId>").append("<DuongDanTepTinKetQua>").append(microservice.filemanUri("/wopi/files/" + attach.getId() + "/contents").encode().build().toUriString()).append("</DuongDanTepTinKetQua>").append("</GiayToKetQua>");
                }
            }
            xmlResult.append("</DanhSachGiayToKetQua>");
        }
        xmlResult.append("</ExtentData>");
        params.setCertificateExtentData(xmlResult.toString());
        params.setHoSoCoThanhPhanSoHoa(checkHoSoCoThanhPhanSoHoa);
    }

    public DossierDetailDto getDossierDetail(String id){
        String endpoint = microservice.padmanUri("dossier/" + id + "/--online").encode().build().toUriString();
        return MicroserviceExchange.getNoAuth(this.getRestTemplate(), endpoint, DossierDetailDto.class);
    }

    String formatDDMMYYY(String inputDate) throws ParseException {
        if (isNullOrEmpty(inputDate)) {
            return "";
        }
        try {
            Date date = newSDF.parse(inputDate);
            return outputFormat.format(date);
        } catch (Exception e) {
            return "";
        }
    }


    @Scheduled(cron = "${digo.schedule.vpc-ldtbxh.sync-tienTrinhXuLy.cron:-}")
    public void syncTientrinhXuly() {
        IntegratedConfigurationDto config = configurationService.searchByServiceId(SERVICE_ID);
        if (Objects.isNull(config)) {
            log.info("syncHsDangKyDossier Config null. Stop");
            return;
        }
        log.info("syncTientrinhXuly Config "+config);
        Boolean enable = config.getParametersValue("EnableSync_LayTienTrinhXuLy");
        if(enable == null || !enable) {
            log.info("syncTientrinhXuly Config not enable: "+enable);
            return;
        }
        String url = config.getParametersValue("URL_LayTienTrinhXuLy");
        String acceptKey = config.getParametersValue("AcceptKey");
        String provinceCode = config.getParametersValue("MaTinh");
        String lastSync = config.getParametersValue("LastSync_LayTienTrinhXuLy");

        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        Calendar from = Calendar.getInstance();
        try {
            Date fromDate = sdf.parse(lastSync);
            from.setTime(fromDate);
        } catch (Exception e) {
            log.error("sdf.parse: "+e.getMessage());
            from = Calendar.getInstance();
            from.set(Calendar.HOUR, 0);
            from.set(Calendar.MINUTE, 0);
            from.set(Calendar.SECOND, 0);
        }
        String fromTime = sdf.format(from.getTime());

        Calendar to = Calendar.getInstance();
        to.setTime(from.getTime());
        to.add(Calendar.DATE, 1);
        String toTime = sdf.format(to.getTime());

        HashMap<String, Object> body = new HashMap<>();
        body.put("AcceptKey", acceptKey);
        body.put("MaTinh", provinceCode);
        body.put("ThoiGianBatDau", fromTime);
        body.put("ThoiGianKetThuc", toTime);
        LayDuLieuBtxhDto response = null;
        //Log
        LogParam logParams = new LogParam();
        logParams.setRequestUrl(url);
        logParams.setRequestMethod("POST");
        logParams.setServiceName("VPC-LĐTBXH-LayTienTrinhXuLy");
        logParams.setKey(provinceCode);
        try {
            response = lgspVpcService.postRequest(null, url, body, LayDuLieuBtxhDto.class);
            logResponse(response.getResult());
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, true, null);
        } catch (Exception e) {
            log.error("syncTientrinhXuly "+e.getMessage());
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, false, e.getMessage());
        }
        if(response == null) {
            log.info("syncTientrinhXuly: No data");
            return;
        }
        ArrayList<String> result = null;
        logParams = new LogParam();
        logParams.setServiceName("VPC-LĐTBXH-Sync-LayTienTrinhXuLy");
        logParams.setKey(provinceCode);
        try {
            result = convertStatusBtxhToIGate(response.getResult(), config);
            updateLastSync("LastSync_LayTienTrinhXuLy", from, to);
        } catch (Exception e) {
            log.error("LastSync_LayTienTrinhXuLy "+e.getMessage());
        }
        eventLogService.writeLog(SERVICE_ID, logParams, response.getResult(), result, true, null);
    }

    public ArrayList<String> convertStatusBtxhToIGate(LayDuLieuBtxhDto.Result input, IntegratedConfigurationDto config) {
        ArrayList<String> message = new ArrayList<>();

        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ArrayList<LayTienTrinhXuLyDto> dataList =  mapper.convertValue(input.getData(), new TypeReference<>() {});
        for (LayTienTrinhXuLyDto dataDTO : dataList) {
            GetDossierOnlineDto dto;
            try {
                log.info("Check ho so : "+dataDTO.getMaHoSo());
                dto = utilService.getDossierByCode(getRestTemplate(), dataDTO.getMaHoSo());
            } catch (Exception e) {
                log.error("Check ho so err: "+e.getMessage());
                message.add("Hồ sơ " + dataDTO.getMaHoSo() + " lỗi tìm kiếm");
                continue;
            }
            if (dto == null) {
                message.add("Hồ sơ " + dataDTO.getMaHoSo() + " không tồn taị");
                continue;
            }
            String res = writeHistory(dto.getId(), dataDTO);
            message.add("Hồ sơ " + dataDTO.getMaHoSo() + " thêm lịch sử: "+res);
        }
        return message;
    }

    public String writeHistory(ObjectId dossierId, LayTienTrinhXuLyDto dto){
        deleteHistory(dossierId);
        HistoryInputDto.User user = new HistoryInputDto.User();
        user.setName(dto.getNguoiXuLy());
        StatusDossierVDXP statusVdxp = null;
        try {
            statusVdxp = StatusDossierVDXP.getStatus(Integer.parseInt(dto.getStatusId()));
        } catch (Exception e) {
            log.error("writeHistory "+e.getMessage());
        }
        String statusMsg = statusVdxp == null? "":statusVdxp.getDes();

        List<HistoryInputDto.Action> actions = new ArrayList<>();
        actions.add(new HistoryInputDto.Action("Tài khoản xử lý", dto.getTaiKhoanXuLy()));
        actions.add(new HistoryInputDto.Action("Người xử lý", dto.getNguoiXuLy()));
        actions.add(new HistoryInputDto.Action("Chức danh", dto.getChucDanh()));
        actions.add(new HistoryInputDto.Action("Đơn vị xử lý", dto.getDonViXuLy()));
        actions.add(new HistoryInputDto.Action("Nội dung xử lý", dto.getNoiDungXuLy()));
        actions.add(new HistoryInputDto.Action("Phòng ban xử lý", dto.getPhongBanXuLy()));
        actions.add(new HistoryInputDto.Action("Trạng thái", dto.getTrangThai()+" "+statusMsg));
        actions.add(new HistoryInputDto.Action("Thời điểm xử lý", dto.getThoiDiemXuLy()));
        actions.add(new HistoryInputDto.Action("Ngày bắt đầu", dto.getNgayBatDau()));
        actions.add(new HistoryInputDto.Action("Ngày kết thúc theo quy định", dto.getNgayKetThucTheoQuyDinh()));
        actions.add(new HistoryInputDto.Action("Mã đơn vị", dto.getMaDonVi()));

        HistoryInputDto historyDto = new HistoryInputDto();
        historyDto.setUser(user);
        historyDto.setItemId(dossierId);
        historyDto.setAction(actions);
        try {
            Date date = oldSDF.parse(dto.getThoiDiemXuLy());
            historyDto.setCreatedDate(date);
        } catch (Exception e) {
            log.error("writeHistory "+e.getMessage());
        }
        String url = microservice.logmanUri("history").toUriString();
        String idHistory = "";
        try {
            idHistory = MicroserviceExchange.postJsonNoAuth(getRestTemplate(), url, historyDto, String.class);
            log.info("writeHistory success: "+idHistory);
        } catch (Exception e) {
            log.error("writeHistory "+e.getMessage());
        }
        return idHistory;
    }

    private void deleteHistory(ObjectId dossierId){
        String url = microservice.logmanUri("history/delete-by-group-and-item").toUriString()
                +"?group-id=1&item-id="+dossierId.toHexString();
        log.info("deleteHistory "+url);
        try {
            AffectedRowsDto affectedRowsDto = MicroserviceExchange.deleteNoAuth(getRestTemplate(), url, AffectedRowsDto.class);
            log.info("deleteHistory success");
        } catch (Exception e) {
            log.error("writeHistory "+e.getMessage());
        }
    }

    public ArrayList<PadPApplyDto.AttachmentDto> getListFile(ArrayList<ExtentData.TaiLieuNop> taiLieuNops) {
        ArrayList<PadPApplyDto.AttachmentDto> attachmentDtos = new ArrayList<>();
        if(taiLieuNops == null) {
            return attachmentDtos;
        }
        boolean checkHasFileTPHS = false;
        for (ExtentData.TaiLieuNop taiLieuNop : taiLieuNops) {
            PadPApplyDto.AttachmentDto infoFile = new PadPApplyDto.AttachmentDto();
            try {
                String encodedUri = URLEncoder.encode(taiLieuNop.getDuongDanTaiTepTin(), StandardCharsets.UTF_8);
                String endPoint = microservice.filemanUri("/file/--by-url-v1?file-url=").toUriString() + encodedUri;
                log.info("Upload file attachment: " + endPoint);
                infoFile = MicroserviceExchange.postNoBody(this.getRestTemplate(), endPoint, PadPApplyDto.AttachmentDto.class);
               // infoFile = MicroserviceExchange.postJsonNoAuth(this.getRestTemplate(), endPoint, null, PadPApplyDto.AttachmentDto.class);
                attachmentDtos.add(infoFile);
            } catch (Exception e) {
                infoFile.setFilename(taiLieuNop.getTenTepDinhKem());
                log.error("getListFile Lỗi upload file " + e.getMessage());
            }
        }
        return attachmentDtos;
    }

    public ArrayList<DBNDossierApplyOnlineDto.AttachmentDto> getAttachmentResult(ArrayList<ExtentData.GiayToKetQua> giayToKetQuas) {
        ArrayList<DBNDossierApplyOnlineDto.AttachmentDto> attachmentDtos = new ArrayList<>();
        if (giayToKetQuas != null && !giayToKetQuas.isEmpty()) {
            for (ExtentData.GiayToKetQua tmp : giayToKetQuas) {
                DBNDossierApplyOnlineDto.AttachmentDto attachment = new DBNDossierApplyOnlineDto.AttachmentDto();
                attachment.setFilename(tmp.getTenGiayTo());
                try {
                    String encodedUri = URLEncoder.encode(tmp.getDuongDanTepTinKetQua(), StandardCharsets.UTF_8);
                    String endPoint = microservice.filemanUri("/file/--by-url-v1?file-url=").toUriString() + encodedUri;
                    log.info("Upload file attachment result: " + endPoint);
                    attachment = MicroserviceExchange.postNoBody(this.getRestTemplate(), endPoint, DBNDossierApplyOnlineDto.AttachmentDto.class);
                    //attachment = MicroserviceExchange.postJsonNoAuth(this.getRestTemplate(), endPoint, null, DBNDossierApplyOnlineDto.AttachmentDto.class);
                    attachment.setFilename(tmp.getTenGiayTo());
                } catch (Exception e) {
                    log.error("getAttachmentResult Lỗi upload file: "+e.getMessage());
                }
                attachment.setGroup("5f9bd9692994dc687e68b5a6");
                attachmentDtos.add(attachment);
            }
        }
        return attachmentDtos;
    }

    public ArrayList<DossierFormFileDto> getDossierFormFile(ArrayList<PadPApplyDto.AttachmentDto> taiLieuNops, String dossierId) {
        ArrayList<DossierFormFileDto> dossierFormFiles = new ArrayList<>();
        for (PadPApplyDto.AttachmentDto taiLieuNop : taiLieuNops) {
            DossierFormFileDto dossierFormFile = new DossierFormFileDto();

            // set dossier
            dossierFormFile.setDossier(new DossierFormFileDto.Id(dossierId));
            // set procedureForm
            ProcedureForm procedureForm = new ProcedureForm();
            procedureForm.setId(new ObjectId().toHexString());
            procedureForm.setName(taiLieuNop.getFilename());
            dossierFormFile.setProcedureForm(procedureForm);
            // set case
            dossierFormFile.setCaze(new DossierFormFileDto.Id("5f3f3c044e1bd312a6f3addf"));

            dossierFormFile.setOrder(1);
            dossierFormFile.setQuantity(1);

            // set detail
            String textDetail = new String("B\u1ea3n sao".getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
            DossierFormFileDto.ProcedureFormDetail detail = new DossierFormFileDto.ProcedureFormDetail(
                    new DossierFormFileDto.ProcedureFormDetailType("623462c0f2e2ad4ed5787167", textDetail), 1
            );
            dossierFormFile.setDetail(detail);

            ArrayList<PadPApplyDto.AttachmentDto> fileDossier = new ArrayList<>();
            if (Objects.nonNull(taiLieuNop.getId())) {
                fileDossier.add(taiLieuNop);
            }
            dossierFormFile.setFile(fileDossier);
            dossierFormFiles.add(dossierFormFile);
        }
        return dossierFormFiles;
    }

    public ExtentData xmlToObjectConverter(String certificateExtentData) {
        ExtentData extentData = new ExtentData();
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(ExtentData.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            StringReader reader = new StringReader(certificateExtentData);

            extentData = (ExtentData) unmarshaller.unmarshal(reader);
        } catch (JAXBException e) {
            log.error("_________________" + e.getMessage());
        }
        return extentData;
    }

    public AgencyFullyDto getAgencyByCode(String code) {
        AgencyFullyDto agencyFullyDto = null;
        try{
            String endpoint = microservice.basedataUri("/agency/name+code+parent+ancestor/--fully-by-administrativeUnitCode?administrativeUnitCode=").toUriString()+code;
            log.info("getAgencyByCode "+endpoint);
            agencyFullyDto = MicroserviceExchange.getNoAuth(getRestTemplate(), endpoint, AgencyFullyDto.class);
        } catch (Exception e) {
            log.error("getAgencyByCode "+e.getMessage());
        }
        return agencyFullyDto;
    }

    public AgencyFullyDto getAgency(String id) {
        AgencyFullyDto agencyFullyDto = null;
        try{
            String endpoint = microservice.basedataUri("/agency/"+id+"/name+code+parent+ancestor/--fully").toUriString();
            log.info("getAgency url: "+endpoint);
            agencyFullyDto = MicroserviceExchange.getNoAuth(getRestTemplate(), endpoint, AgencyFullyDto.class);
            log.info("getAgency "+agencyFullyDto);
        } catch (Exception e) {
            log.error("getAgency: "+e.getMessage());
        }
        return agencyFullyDto;
    }

    private DBNDossierApplyOnlineDto.RecvKindDto getDossierTaskStatus(String status) {
        DBNDossierApplyOnlineDto.RecvKindDto dossierTaskStatus = new DBNDossierApplyOnlineDto.RecvKindDto();
        switch (status) {
            case "1":
                dossierTaskStatus.setId("60e409823dfc9609723e493c");
                List<DBNDossierApplyOnlineDto.NameDto> name0 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Mới đăng ký"));
                    }
                };
                dossierTaskStatus.setName(name0);
                break;
            case "2":
                dossierTaskStatus.setId("631e4a0967411b0b0d000001");
                List<DBNDossierApplyOnlineDto.NameDto> name2 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Tiếp nhận hồ sơ"));
                    }
                };
                dossierTaskStatus.setName(name2);
                break;
            case "3":
                dossierTaskStatus.setId("60ebf14b09cbf91d41f87f8d");
                List<DBNDossierApplyOnlineDto.NameDto> name3 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Không được tiếp nhận"));
                    }
                };
                dossierTaskStatus.setName(name3);
                break;
            case "4":
                dossierTaskStatus.setId("631e4a0967411b0b0d000002");
                List<DBNDossierApplyOnlineDto.NameDto> name4 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Đang xử lý"));
                    }
                };
                dossierTaskStatus.setName(name4);
                break;
            case "5":
                dossierTaskStatus.setId("60ebf03109cbf91d41f87f8b");
                List<DBNDossierApplyOnlineDto.NameDto> name5 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Yêu cầu bổ sung giấy tờ"));
                    }
                };
                dossierTaskStatus.setName(name5);
                break;
            case "6":
                dossierTaskStatus.setId("631e4a0967411b0b0d000003");
                List<DBNDossierApplyOnlineDto.NameDto> name6 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Thực hiện nghĩa vụ tài chính"));
                    }
                };
                dossierTaskStatus.setName(name6);
                break;
            case "7":
                dossierTaskStatus.setId("631e4a0967411b0b0d000004");
                List<DBNDossierApplyOnlineDto.NameDto> name7 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Công dân yêu cầu rút hồ sơ"));
                    }
                };
                dossierTaskStatus.setName(name7);
                break;
            case "8":
                dossierTaskStatus.setId("61ee30eada2d36b037e00005");
                List<DBNDossierApplyOnlineDto.NameDto> name8 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Dừng xử lý"));
                    }
                };
                dossierTaskStatus.setName(name8);
                break;
            case "9":
                dossierTaskStatus.setId("60ed1c3409cbf91d41f87fa1");
                List<DBNDossierApplyOnlineDto.NameDto> name9 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Đã có kết quả"));
                    }
                };
                dossierTaskStatus.setName(name9);
                break;
            case "10":
                dossierTaskStatus.setId("60ebf17309cbf91d41f87f8e");
                List<DBNDossierApplyOnlineDto.NameDto> name10 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Đã trả kết quả"));
                    }
                };
                dossierTaskStatus.setName(name10);
                break;
            case "11":
                dossierTaskStatus.setId("691e4a0967411b0b0d000011");
                List<DBNDossierApplyOnlineDto.NameDto> name11 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Từ chối từ chuyên ngành"));
                    }
                };
                dossierTaskStatus.setName(name11);
                break;
        }
        return dossierTaskStatus;
    }

    private UpdatePadPApplyDto.DossierStatus getDossierStatus(String status) {
        switch (status) {
            case "1":
                UpdatePadPApplyDto.DossierStatus status1 = new UpdatePadPApplyDto.DossierStatus(0, new ArrayList<>());
                status1.getName().add(new Translate((short)228, "Chờ tiếp nhận"));
                return status1;
            case "2":
                UpdatePadPApplyDto.DossierStatus status2 = new UpdatePadPApplyDto.DossierStatus(2, new ArrayList<>());
                status2.getName().add(new Translate((short)228, "Đang xử lý"));
                return status2;
            case "3":
                UpdatePadPApplyDto.DossierStatus status3 = new UpdatePadPApplyDto.DossierStatus(6, new ArrayList<>());
                status3.getName().add(new Translate((short)228, "Đã hủy"));
                return status3;
            case "4":
                UpdatePadPApplyDto.DossierStatus status4 = new UpdatePadPApplyDto.DossierStatus(2, new ArrayList<>());
                status4.getName().add(new Translate((short)228, "Đang xử lý"));
                return status4;
            case "6":
                UpdatePadPApplyDto.DossierStatus status6 = new UpdatePadPApplyDto.DossierStatus(14, new ArrayList<>());
                status6.getName().add(new Translate((short)228, "Yêu cầu thanh toán"));
                return status6;
            case "8":
                UpdatePadPApplyDto.DossierStatus status8 = new UpdatePadPApplyDto.DossierStatus(8, new ArrayList<>());
                status8.getName().add(new Translate((short)228, "Dừng xử lý"));
                return status8;
            case "9":
                UpdatePadPApplyDto.DossierStatus status9 = new UpdatePadPApplyDto.DossierStatus(4, new ArrayList<>());
                status9.getName().add(new Translate((short)228, "Có kết quả"));
                return status9;
            case "10":
                UpdatePadPApplyDto.DossierStatus status10 = new UpdatePadPApplyDto.DossierStatus(5, new ArrayList<>());
                status10.getName().add(new Translate((short)228, "Đã trả kết quả"));
                return status10;
        }
        UpdatePadPApplyDto.DossierStatus status0 = new UpdatePadPApplyDto.DossierStatus(0, new ArrayList<>());
        status0.getName().add(new Translate((short)228, "Chờ tiếp nhận"));
        return status0;
    }

    private int getDossierStatusCode(String status) {
        switch (status) {
            case "1":
                return 0;
            case "2":
            case "4":
                return 2;
            case "3":
                return 6;
            case "6":
                return 14;
            case "8":
                return 8;
            case "9":
                return 4;
            case "10":
                return 5;
        }
        return 0;
    }

    private void updateDossierStatus(ObjectId objectId, SocialProtectionGetDossierResultDataDTO dataDto,
                                     DBNDossierApplyOnlineDto.AgencyDto agency,
                                     DBNDossierApplyOnlineDto.ProcedureDto procedure,
                                     DBNDossierApplyOnlineDto.IdNameDto procedureLevel,
                                     DBNDossierApplyOnlineDto.ProcessDto process,
                                     DBNDossierApplyOnlineDto.ApplicantDto applicantDto,
                                     DBNDossierApplyOnlineDto.FormDto eFormDto
                                     ) {
        String statusId = dataDto.getStatusId();
        UpdatePadPApplyDto dossierOnline = new UpdatePadPApplyDto();
        dossierOnline.setDossierTaskStatus(getDossierTaskStatus(statusId));
        dossierOnline.setDossierMenuTaskRemind(getDossierTaskRemind(statusId));
        dossierOnline.setDossierStatus(getDossierStatus(statusId));
        dossierOnline.setAgency(agency);
        dossierOnline.setProcedure(procedure);
        dossierOnline.setProcedureLevel(procedureLevel);
        dossierOnline.setProcedureProcessDefinition(process);
        dossierOnline.setApplicant(applicantDto);
        dossierOnline.setEForm(eFormDto);
        if("1".equals(statusId)) {
            dossierOnline.setAcceptedDate(null);
        } else {
            Date newAcceptDate = getDateFromBhxh(dataDto.getNgayTiepNhan());
            dossierOnline.setAcceptedDate(newAcceptDate);
            Date newAppointmentDate = getDateFromBhxh(dataDto.getNgayHenTra());
            dossierOnline.setAppointmentDate(newAppointmentDate);
        }
        // File ket qua
        ExtentData extentData = this.xmlToObjectConverter(dataDto.getCertificateExtentData());
        if(extentData.getDanhSachGiayToKetQua() != null) {
            dossierOnline.setAttachment(this.getAttachmentResult(extentData.getDanhSachGiayToKetQua().getGiayToKetQuas()));
        }
        String updateDossierUrl = microservice.padmanUri("/dossier/"+objectId.toHexString()+"/--online").toUriString();
        AffectedRowsDto updateDossier = null;
        log.info("Cap nhat ho so id "+updateDossierUrl);
        log.info("Cap nhat ho so body "+dossierOnline);
        try {
            updateDossier = MicroserviceExchange.putJsonNoAuth(getRestTemplate(), updateDossierUrl, dossierOnline, AffectedRowsDto.class);
        } catch (Exception e) {
            log.error("Cap nhat ho so bi loi: "+e.getMessage());
            return;
        }
        log.info( "Cap nhat ho so id: "+updateDossier);
        removeDossierFormFile(objectId.toHexString());
        updateDossierFormFile(extentData, objectId.toHexString());
    }

    private DBNDossierApplyOnlineDto.RecvKindDto getDossierTaskReminder(String status) {
        DBNDossierApplyOnlineDto.RecvKindDto dossierTaskStatus = new DBNDossierApplyOnlineDto.RecvKindDto();
        switch (status) {
            case "1":
                dossierTaskStatus.setId("63da3830ee48c32f84775aaa");
                List<DBNDossierApplyOnlineDto.NameDto> name1 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Hồ sơ chờ tiếp nhận"));
                    }
                };
                dossierTaskStatus.setName(name1);
                break;
            case "2":
                dossierTaskStatus.setId("5ff8256594a7fa67df706ef0");
                List<DBNDossierApplyOnlineDto.NameDto> name2 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Được tiếp nhận"));
                    }
                };
                dossierTaskStatus.setName(name2);
                break;
            case "4":
                dossierTaskStatus.setId("63da3841ee48c32f84775aab");
                List<DBNDossierApplyOnlineDto.NameDto> name4 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Hồ sơ chờ xử lý"));
                    }
                };
                dossierTaskStatus.setName(name4);
                break;
            case "6":
                dossierTaskStatus.setId("60f6364e09cbf91d41f88857");
                List<DBNDossierApplyOnlineDto.NameDto> name6 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Yêu cầu chứng thực nghĩa vụ tài chính"));
                    }
                };
                dossierTaskStatus.setName(name6);
                break;
            case "9":
                dossierTaskStatus.setId("63db0597ee48c32f84775aae");
                List<DBNDossierApplyOnlineDto.NameDto> name9 = new ArrayList<>() {
                    {
                        add(new DBNDossierApplyOnlineDto.NameDto((short) 228, "Chờ trả kết quả"));
                    }
                };
                dossierTaskStatus.setName(name9);
                break;
        }
        return null;
    }

    private DBNDossierApplyOnlineDto.AgencyDto convertAgency(AgencyFullyDto agencyFullyDto) {
        DBNDossierApplyOnlineDto.AgencyDto agency = new DBNDossierApplyOnlineDto.AgencyDto();
        DBNDossierApplyOnlineDto.AgencyDto parentAgency = new DBNDossierApplyOnlineDto.AgencyDto();
        parentAgency.setId(agencyFullyDto.getId().toHexString());
        parentAgency.setCode(agencyFullyDto.getCode());
        List<DBNDossierApplyOnlineDto.NameDto> names = new ArrayList<>();
        if(agencyFullyDto.getName() != null) {
            for(AgencyFullyDto.Name item  :  agencyFullyDto.getName() ) {
                DBNDossierApplyOnlineDto.NameDto nameDto = new DBNDossierApplyOnlineDto.NameDto();
                nameDto.setName(item.getName());
                nameDto.setLanguageId(item.getLanguageId());
                names.add(nameDto);
            }
        }
        parentAgency.setName(names);
        agency.setParent(parentAgency);
        LinkedList<DBNDossierApplyOnlineDto.AgencyDto> listAncestor = new LinkedList<>();
        listAncestor.add(parentAgency);
        if(agencyFullyDto.getAncestors() != null) {
            for(AgencyFullyDto.Ancestor ancestor : agencyFullyDto.getAncestors()) {
                DBNDossierApplyOnlineDto.AgencyDto parent2Agency = new DBNDossierApplyOnlineDto.AgencyDto();
                parent2Agency.setId(ancestor.getId().toHexString());
                if(ancestor.getName() != null) {
                    List<DBNDossierApplyOnlineDto.NameDto> pNames = new ArrayList<>();
                    for(AgencyFullyDto.Name item  :  ancestor.getName() ) {
                        DBNDossierApplyOnlineDto.NameDto nameDto = new DBNDossierApplyOnlineDto.NameDto();
                        nameDto.setName(item.getName());
                        nameDto.setLanguageId(item.getLanguageId());
                        pNames.add(nameDto);
                    }
                    parent2Agency.setName(pNames);
                }
                listAncestor.add(parent2Agency);
            }
        }
        agency.setAncestors(listAncestor);
        log.info("agencyFullyDto agency "+agency);
        return agency;
    }

    private void updateDossierFormFile(ExtentData extentData, String dossierId) {
        if(extentData == null || extentData.getDanhsachTaiLieuNop() == null || extentData.getDanhsachTaiLieuNop().getTaiLieuNop() == null) {
            log.error("dossierFormFiles getTaiLieuNop empty");
            return;
        }
        ArrayList<PadPApplyDto.AttachmentDto> attachmentDtos = this.getListFile(extentData.getDanhsachTaiLieuNop().getTaiLieuNop());
        if(attachmentDtos == null) {
            log.error("dossierFormFiles attachmentDtos empty");
            return;
        }
        ArrayList<DossierFormFileDto> dossierFormFiles = this.getDossierFormFile(attachmentDtos, dossierId);
        if (dossierFormFiles == null || dossierFormFiles.isEmpty()) {
            log.error("dossierFormFiles empty");
            return;
        }
        String newDossierFormFileURL = microservice.padmanUri("/dossier-form-file").toUriString() + "/--by-dossier?dossier-id=" + dossierId;
        try {
            AffectedRowsDto formFile = MicroserviceExchange.putJsonNoAuth(this.getRestTemplate(), newDossierFormFileURL, dossierFormFiles, AffectedRowsDto.class);
            log.info("dossierFormFiles: "+formFile);
        } catch (Exception e) {
            log.error("dossierFormFiles error: "+e.getMessage());
        }
    }

    private void removeDossierFormFile(String dossierId) {
        String endpoint = microservice.padmanUri("/dossier-form-file").toUriString() + "/getBy?dossierId=" + dossierId;
        log.info("removeDossierFormFile get form file dosier: "+endpoint);
        IdDto[] list = null;
        try {
            list = MicroserviceExchange.getNoAuth(this.getRestTemplate(), endpoint, IdDto[].class);
        } catch (Exception e) {
            log.error("dossierFormFiles error: "+e.getMessage());
        }
        if(list == null || list.length == 0) {
            log.error("dossierFormFiles Empty "+dossierId);
            return;
        }
        for(IdDto id : list) {
            log.info("removeDossierFormFile form file id: "+id.getId().toHexString());
            endpoint = microservice.padmanUri("/dossier-form-file/"+id.getId().toHexString()+"?dossier-id="+dossierId).toUriString();
            log.info("removeDossierFormFile: "+endpoint);
            try {
                AffectedRowsDto affectedRowsDto = MicroserviceExchange.deleteNoAuth(this.getRestTemplate(), endpoint, AffectedRowsDto.class);
                log.info("removeDossierFormFile: "+affectedRowsDto);
            } catch (Exception e) {
                log.error("removeDossierFormFile error: "+e.getMessage());
            }
        }
    }
}
