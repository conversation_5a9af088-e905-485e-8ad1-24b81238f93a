/*

 */
package vn.vnpt.digo.adapter.service;

import javax.servlet.http.HttpServletResponse;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Base64;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.Hcm_KetQua_LLTPDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.tandan.judicialrecords.JRResDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.*;

/**
 *
 * <AUTHOR> 30/06/2022
 */

@Service
public class Hcm_LLTPService {

    //Logger logger = LoggerFactory.getLogger(IntegratedConfigurationService.class);
    @Autowired
    private IntegratedConfigurationService configurationService;
    @Value(value = "${digo.lgsphcm.configid}")
    private String configid;
//    @Value(value = "${digo.oidc.client-id}")
//    private String clientId;
//    @Value(value = "${digo.oidc.client-secret}")
//    private String clientSecret;
//    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
//    private String tokenUrl;
    @Autowired
    private RestTemplate restTemplate;

    private HashMap<String, String> getExtendHeaders(String lgspToken, String token) {
        HashMap<String, String> exHeaders = new HashMap<String, String>();
        exHeaders.put("lgspaccesstoken", lgspToken);
        exHeaders.put("Authorization", token);
        exHeaders.put("Content-Type", MediaType.valueOf("application/json").toString());
        return exHeaders;
    }

    private HashMap<String, String> getExtendHeadersToken(String lgspToken) {
        HashMap<String, String> exHeaders = new HashMap<String, String>();
        exHeaders.put("lgspaccesstoken", lgspToken);
        exHeaders.put("Content-Type", MediaType.valueOf("application/x-www-form-urlencoded").toString());
        return exHeaders;
    }

    public String getToken_From() {
        IntegratedConfigurationDto config;
        config = configurationService.getConfig(new ObjectId(configid));
        String result = "";
        try {
            //Lay bien deploymentId
            //ObjectId deploymentId = Context.getDeploymentId();
            //Bien lay adapter
          String uri = config.getParameterValue("adapter");
//            String uri = "https://hcmesb-test.tphcm.gov.vn/";
            //Cac bien khoi tao Authorization code
            String accessKey = config.getParametersValue("accessKey");
            String secretKey = config.getParametersValue("secretKey");
            String appName = config.getParametersValue("appName");
            String partnerCode = config.getParametersValue("partnerCode");
            String partnerCodeCus = config.getParametersValue("partnerCodeCus");

            //Khoi tao Authorization code
            String jsonString = new JSONObject()
                    .put("AccessKey", accessKey)
                    .put("SecretKey", secretKey)
                    .put("AppName", appName)
                    .put("PartnerCode", partnerCode)
                    .put("PartnerCodeCus", partnerCodeCus)
                    .toString().replaceAll(",", ",\n").replaceAll("\\{", "{\n").replaceAll("}", "\n}");
            String authorizationCode = Base64.getEncoder().encodeToString(jsonString.getBytes());
//            String authorizationCode = "ewoiQWNjZXNzS2V5IjoiNTc4OGFhYmNlNGIwODM2ZGVmMzY3YTI3IiwKIlNlY3JldEtleSI6IkE4OGpzRjFLMUZ2Z1djZjl2V2IzMGVDQno4NFVVaXdSVlNLZEppeUc3diIsCiJBcHBOYW1lIjogInZucHRoY20iLAoiUGFydG5lckNvZGUiOiAiMDAwLjAwLjE1LkgyOSIsCiJQYXJ0bmVyQ29kZUN1cyI6ICIwMDAuMDAuMTUuSDI5Igp9";
            String service = "lltp/token";
            String URL = uri + service;
            HashMap<String, String> headers = getExtendHeadersToken(authorizationCode);
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("grant_type", "client_credentials");
            String responseJSONString = MicroserviceExchange.postFormUrlEncodedLGSPHCM(this.restTemplate, URL, headers, body, String.class);
            JSONObject jsonObj = new JSONObject(responseJSONString);
            result = jsonObj.getString("token_type") + " " + jsonObj.getString("access_token");
        } catch (Exception ex) {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
        return result;
    }

    public List<Hcm_KetQua_LLTPDto> getDanhMuc_LLTP(String infoType) {
        IntegratedConfigurationDto config;
        config = configurationService.getConfig(new ObjectId(configid));

        try {
            //Lay bien deploymentId
            //ObjectId deploymentId = Context.getDeploymentId();
            //Bien lay adapter
            String uri = config.getParameterValue("adapter");
//            String uri = "https://hcmesb-test.tphcm.gov.vn/";//adapter Thu Nghiem
//            String uri = "https://hcmlgsp.tphcm.gov.vn/";//adapter chinh thuc
            //Cac bien khoi tao Authorization code
            String accessKey = config.getParametersValue("accessKey");
            String secretKey = config.getParametersValue("secretKey");
            String appName = config.getParametersValue("appName");
            String partnerCode = config.getParametersValue("partnerCode");
            String partnerCodeCus = config.getParametersValue("partnerCodeCus");

            //Khoi tao Authorization code
            String jsonString = new JSONObject()
                    .put("AccessKey", accessKey)
                    .put("SecretKey", secretKey)
                    .put("AppName", appName)
                    .put("PartnerCode", partnerCode)
                    .put("PartnerCodeCus", partnerCodeCus)
                    .toString().replaceAll(",", ",\n").replaceAll("\\{", "{\n").replaceAll("}", "\n}");
            String authorizationCode = Base64.getEncoder().encodeToString(jsonString.getBytes());
//            String authorizationCode = "ewoiQWNjZXNzS2V5IjoiNTc4OGFhYmNlNGIwODM2ZGVmMzY3YTI3IiwKIlNlY3JldEtleSI6IkE4OGpzRjFLMUZ2Z1djZjl2V2IzMGVDQno4NFVVaXdSVlNLZEppeUc3diIsCiJBcHBOYW1lIjogInZucHRoY20iLAoiUGFydG5lckNvZGUiOiAiMDAwLjAwLjE1LkgyOSIsCiJQYXJ0bmVyQ29kZUN1cyI6ICIwMDAuMDAuMTUuSDI5Igp9";
            String service = "lltp/apiLLTP/st/1.0/LLTP-API-traDanhMuc";
            String token_From = getToken_From();
            List<Hcm_KetQua_LLTPDto> lst_lltp = new ArrayList<>();
            String URL = uri + service;
            HashMap<String, String> headers = getExtendHeaders(authorizationCode, token_From);
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("infoType", infoType);
            String responseJSONString = MicroserviceExchange.postFormUrlEncodedLGSPHCM(this.restTemplate, URL, headers, body, String.class);
            JSONObject jsonObj = new JSONObject(responseJSONString);
            if (jsonObj.getString("status").equals("404")) {
                // "description": "Loai thong tin danh muc khong dung dinh dang"
                return lst_lltp;
            }
            Gson gson = new Gson();
            JRResDto temp_Json = gson.fromJson(responseJSONString, JRResDto.class);
            lst_lltp = gson.fromJson(temp_Json.getListContent(), new TypeToken<List<Hcm_KetQua_LLTPDto>>(){
            }.getType());
            return lst_lltp;
//            JSONObject jsonObj = new JSONObject(responseJSONString);
//            if (jsonObj.getString("Status").equals("SUCCESS")) {
//                JSONArray ResultObject = jsonObj.getJSONArray("ResultObject");
//                for (int i = 0; i < ResultObject.length(); i++) {
//                    JSONObject obj_LLTP = ResultObject.getJSONObject(i);
//                    String id = obj_LLTP.getString("id");
//                    String name = obj_LLTP.getString("name");
//                    Hcm_KetQua_LLTPDto hcm = new Hcm_KetQua_LLTPDto();
//                    hcm.setId(id);
//                    hcm.setName(name);
//                    lst_lltp.add(hcm);
//                }
//            } else {
//                return lst_lltp;
//            }
//         return responseJSONString;
        } catch (Exception ex) {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

}
