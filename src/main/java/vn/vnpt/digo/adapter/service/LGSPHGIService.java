package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.gtvt.GTVTTokenResponseDto;
import vn.vnpt.digo.adapter.dto.hgi.*;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.*;
import vn.vnpt.digo.adapter.pojo.hgi.*;
import vn.vnpt.digo.adapter.repository.HistorySendTanDanDossierRepository;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Type;
import java.util.*;


@RefreshScope
@Service
@Component
public class LGSPHGIService {

    private ObjectId serviceId = new ObjectId("6565b40b86515243100a3407");
    private ObjectId serviceIdHgi = new ObjectId("6790fbdbe6d2192a863812d9");
    Logger logger = LoggerFactory.getLogger(LGSPHGIService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IntegratedConfigurationService configurationService;

    IntegratedConfigurationDto config;


    public String getTokenHGI(IntegratedConfigurationDto config){
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        logger.info("Start get accesstoken getTokenHGI !!");
        String URL = config.getParametersValue("adapter") + "vbqppl/token";
//        String URL = "http://123.30.159.54:8280/vbqppl/token";
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", MediaType.valueOf("application/x-www-form-urlencoded").toString());
        headers.add("lgspaccesstoken", config.getParametersValue("lgspaccesstoken").toString());
//        headers.add("lgspaccesstoken", "ewoiQWNjZXNzS2V5IjoiYjc3Y2NhN2VlMDAxZmMyZTRlNjg5NGYyNjA1NzI4N2EiLAoiU2VjcmV0S2V5IjoiRm1ZekpsTkdVTnpJNE4yRmpZVGRsWlRBWWpjM1l6Um0yT0RreU5qQTEiLAoiQXBwTmFtZSI6ImhnX25nc3AiLAoiUGFydG5lckNvZGUiOiIwMDAuMDAuMDAuSDMwIiwKIlBhcnRuZXJDb2RlQ3VzIjoiMDAwLjAwLjAwLkgzMCIKfQ==");
        MultiValueMap<String, String> map= new LinkedMultiValueMap<String, String>();
        map.add("grant_type", "client_credentials");
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<MultiValueMap<String, String>>(map, headers);
        ResponseEntity<GTVTTokenResponseDto> result = restTemplate.exchange(URL, HttpMethod.POST, entity, GTVTTokenResponseDto.class);
        logger.info("Access Token getTokenHGI: " + result.getBody().getAccess_token());
        return result.getBody().getAccess_token();
    }

    public ResponseEntity<String> getLegalDocumentHgi(SearchDocumentFullHgi req) {
        logger.info("---------------------------getLegalDocument---------------------------");
        ResponseEntity<String> results = null;
        //Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        } else {
            config = configurationService.getConfig(req.getAgencyId(), req.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String tokencode = this.getTokenHGI(config);
        String lgspaccesstokencode =config.getParametersValue("lgspaccesstoken").toString();
        String adaptercode = config.getParametersValue("adapter").toString();
        String endPoint = config.getParametersValue("TimKiemVanbanFull").toString();
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + tokencode);
            headers.set("lgspaccesstoken", lgspaccesstokencode);
            headers.setContentType(MediaType.APPLICATION_JSON);
//            String t_gridRequest = "{\"sort\":[{\"field\":\"ID\",\"dir\":\"desc\"}]}";
//            req.setT_gridRequest(t_gridRequest);
            HttpEntity<?> request = new HttpEntity<>(req,headers);
            String URI = adaptercode + endPoint;
            results = restTemplate.exchange(URI, HttpMethod.POST, request, String.class);
            var data = results.getBody();
        }catch (Exception e){
            logger.error("Error calling http: ", e.getMessage());
            //throw e;
        }
        return results;
    }

    public ResponseEntity<String> getAllDocumentTypeHgi(SearchAllDocumentTypeHgi req) {
        logger.info("---------------------------getAllDocumentType---------------------------");
        ResponseEntity<String> results = null;
        //Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        } else {
            config = configurationService.getConfig(req.getAgencyId(), req.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        String tokencode = this.getTokenHGI(config);
        String lgspaccesstokencode =config.getParametersValue("lgspaccesstoken").toString();
        String adaptercode = config.getParametersValue("adapter").toString();
        String endPoint = config.getParametersValue("GetAllLoaiVanBan").toString();
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + tokencode);
            headers.set("lgspaccesstoken", lgspaccesstokencode);
            headers.setContentType(MediaType.APPLICATION_JSON);
            String t_gridRequest = "{\"sort\":[{\"field\":\"ID\",\"dir\":\"desc\"}]}";
            req.setT_gridRequest(t_gridRequest);
            HttpEntity<?> request = new HttpEntity<>(req,headers);
            String URI = adaptercode + endPoint;
            results = restTemplate.exchange(URI, HttpMethod.POST, request, String.class);
            var data = results.getBody();
        }catch (Exception e){
            logger.error("Error calling http: ", e.getMessage());
            //throw e;
        }
        return results;
    }

    public ResponseEntity<String> getAllEditorialAgencyHgi(SearchAllEditorialAgencyHgi req) {
        logger.info("---------------------------getAllEditorialAgency---------------------------");
        ResponseEntity<String> results = null;
        //Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        } else {
            config = configurationService.getConfig(req.getAgencyId(), req.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String tokencode = this.getTokenHGI(config);
        String lgspaccesstokencode =config.getParametersValue("lgspaccesstoken").toString();
        String adaptercode = config.getParametersValue("adapter").toString();
        String endPoint = config.getParametersValue("GetAllCoQuanBienTap").toString();

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + tokencode);
            headers.set("lgspaccesstoken", lgspaccesstokencode);
            headers.setContentType(MediaType.APPLICATION_JSON);
            String t_gridRequest = "{\"sort\":[{\"field\":\"ID\",\"dir\":\"desc\"}]}";
            Map<String, Object> _params = new HashMap<>();
            _params.put("t_gridRequest", t_gridRequest);
            HttpEntity<?> request = new HttpEntity<>(_params,headers);
            String URI = adaptercode + endPoint;
            results = restTemplate.exchange(URI, HttpMethod.POST, request, String.class);
            var data = results.getBody();
            JSONObject jsonObj;
            jsonObj = new JSONObject(data);

        }catch (Exception e){
            logger.error("Error calling http: ", e.getMessage());
            //throw e;
        }
        return results;
    }

    public ResponseEntity<String> getByIdHgi(GetDetailByIdHgi req) {
        logger.info("---------------------------getById---------------------------");
        ResponseEntity<String> results = null;
        //Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        } else {
            config = configurationService.getConfig(req.getAgencyId(), req.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String tokencode = this.getTokenHGI(config);
        String lgspaccesstokencode =config.getParametersValue("lgspaccesstoken").toString();
        String adaptercode = config.getParametersValue("adapter").toString();
        String endPoint = config.getParametersValue("GetById").toString();
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + tokencode);
            headers.set("lgspaccesstoken", lgspaccesstokencode);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<?> request = new HttpEntity<>(req,headers);
            String URI = adaptercode + endPoint;
            results = restTemplate.exchange(URI, HttpMethod.POST, request, String.class);
            var data = results.getBody();
        }catch (Exception e){
            logger.error("Error calling http: ", e.getMessage());
            //throw e;
        }
        return results;
    }

    public ResponseEntity<String> getListAttachHgi(GetListAttachUrlHgi req) {
        logger.info("---------------------------getListAttach---------------------------");
        ResponseEntity<String> results = null;
        //Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        } else {
            config = configurationService.getConfig(req.getAgencyId(), req.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String tokencode = this.getTokenHGI(config);
        String lgspaccesstokencode =config.getParametersValue("lgspaccesstoken").toString();
        String adaptercode = config.getParametersValue("adapter").toString();
        String endPoint = config.getParametersValue("VbqlGetListAttach").toString();
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + tokencode);
            headers.set("lgspaccesstoken", lgspaccesstokencode);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<?> request = new HttpEntity<>(req,headers);
            String URI = adaptercode + endPoint;
            results = restTemplate.exchange(URI, HttpMethod.POST, request, String.class);
//            var data = results.getBody();
        }catch (Exception e){
            logger.error("Error calling http: ", e.getMessage());
            //throw e;
        }
        return results;
    }

    private HashMap<String, String> getExtendHeaders(String token) {
        HashMap<String, String> exHeaders = new HashMap<String, String>();
        exHeaders.put("Authorization", "Bearer " + token);
        return exHeaders;
    }

    public IofficeResponseHgiDto getDanhSachVanBanIofficeHgi(IntegrationParamsDto params, IofficeRequestHgiDto body) throws Exception {
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceIdHgi);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String URL = config.getParametersValue("apidocument_url").toString();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Chuyển đổi định dạng ngày từ yyyy-MM-dd sang dd/MM/yyyy nếu cần
        String tuNgay = body.getTuNgay();
        if (tuNgay != null && !tuNgay.isEmpty()) {
            tuNgay = convertDateFormat(tuNgay);
        }

        String denNgay = body.getDenNgay();
        if (denNgay != null && !denNgay.isEmpty()) {
            denNgay = convertDateFormat(denNgay);
        }

        // JSON nội bộ cần serialize thành chuỗi
        Map<String, Object> jsonBody = new HashMap<>();
        jsonBody.put("trich_yeu", body.getTrichYeu());
        jsonBody.put("trich_yeu_org", "");
        jsonBody.put("ma_duthao", "");
        jsonBody.put("so_kyhieu", body.getSoKyHieu());
        jsonBody.put("so_kyhieu_org", body.getSoKyHieu());
        jsonBody.put("nguoixuly", "");
        jsonBody.put("nguoiky", "");
        jsonBody.put("donvixuly", "");
        jsonBody.put("coquan_banhanh", "");
        jsonBody.put("start_date_banhanh", tuNgay);
        jsonBody.put("end_date_banhanh", denNgay);
        jsonBody.put("loai_vanban", "");
        jsonBody.put("chk_search_toanvan", "0");
        jsonBody.put("chk_search_chinhxac", "0");
        jsonBody.put("search_menu_tracuu_2", "1");

        Gson gson = new Gson();
        String jsonBodyString = gson.toJson(jsonBody); // Serialize Map thành chuỗi JSON

        // Body chính cho API
        Map<String, Object> formData = new HashMap<>();
        formData.put("username", config.getParametersValue("username").toString());
        formData.put("password", config.getParametersValue("password").toString());
        if(config.getParametersValue("type_user").equals("true")) {
            formData.put("user", config.getParametersValue("user").toString());
        }else {
            formData.put("user", body.getUserAccount());
        }
        formData.put("pageNo", body.getPage());
        formData.put("pageRec", body.getSize());
        formData.put("json", jsonBodyString); // Gửi JSON dưới dạng chuỗi
        formData.put("ids", "");

        // Tạo request
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(formData, headers);

        // Gửi request API
        ResponseEntity<String> response = restTemplate.exchange(URL, HttpMethod.POST, request, String.class);

        // Parse kết quả trả về
        if (response.getStatusCode().is2xxSuccessful()) {
            IofficeResponseHgiDto res = gson.fromJson(response.getBody(), IofficeResponseHgiDto.class);
            return res;
        } else {
            throw new Exception("Lỗi khi gọi API: " + response.getStatusCode() + " - " + response.getBody());
        }
    }

    public TotalEofficeResponseHgiDto getTotalVanBanIofficeHgi(IntegrationParamsDto params, IofficeRequestHgiDto body) throws Exception {
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceIdHgi);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String URL = config.getParametersValue("apitotaldocument_url").toString();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Chuyển đổi định dạng ngày từ yyyy-MM-dd sang dd/MM/yyyy nếu cần
        String tuNgay = body.getTuNgay();
        if (tuNgay != null && !tuNgay.isEmpty()) {
            tuNgay = convertDateFormat(tuNgay);
        }

        String denNgay = body.getDenNgay();
        if (denNgay != null && !denNgay.isEmpty()) {
            denNgay = convertDateFormat(denNgay);
        }

        // JSON nội bộ cần serialize thành chuỗi
        Map<String, Object> jsonBody = new HashMap<>();
        jsonBody.put("trich_yeu", body.getTrichYeu());
        jsonBody.put("trich_yeu_org", "");
        jsonBody.put("ma_duthao", "");
        jsonBody.put("so_kyhieu", body.getSoKyHieu());
        jsonBody.put("so_kyhieu_org", body.getSoKyHieu());
        jsonBody.put("nguoixuly", "");
        jsonBody.put("nguoiky", "");
        jsonBody.put("donvixuly", "");
        jsonBody.put("coquan_banhanh", "");
        jsonBody.put("start_date_banhanh", tuNgay);
        jsonBody.put("end_date_banhanh", denNgay);
        jsonBody.put("loai_vanban", "");
        jsonBody.put("chk_search_toanvan", "0");
        jsonBody.put("chk_search_chinhxac", "0");
        jsonBody.put("search_menu_tracuu_2", "1");

        Gson gson = new Gson();
        String jsonBodyString = gson.toJson(jsonBody); // Serialize Map thành chuỗi JSON

        // Body chính cho API
        Map<String, Object> formData = new HashMap<>();
        formData.put("username", config.getParametersValue("username").toString());
        formData.put("password", config.getParametersValue("password").toString());
        if(config.getParametersValue("type_user").equals("true")) {
            formData.put("user", config.getParametersValue("user").toString());
        }else {
            formData.put("user", body.getUserAccount());
        }
        formData.put("pageNo", "-1");
        formData.put("pageRec", body.getSize());
        formData.put("json", jsonBodyString); // Gửi JSON dưới dạng chuỗi
        formData.put("ids", "");

        // Tạo request
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(formData, headers);

        // Gửi request API
        ResponseEntity<String> response = restTemplate.exchange(URL, HttpMethod.POST, request, String.class);

        // Parse kết quả trả về
        if (response.getStatusCode().is2xxSuccessful()) {
            TotalEofficeResponseHgiDto res = gson.fromJson(response.getBody(), TotalEofficeResponseHgiDto.class);
            return res;
        } else {
            throw new Exception("Lỗi khi gọi API: " + response.getStatusCode() + " - " + response.getBody());
        }
    }

    // Hàm hỗ trợ chuyển đổi định dạng ngày từ yyyy-MM-dd sang dd/MM/yyyy
    private String convertDateFormat(String date) {
        if (date == null || date.isEmpty()) return date;
        String[] arr = date.substring(0, 10).split("-");
        return arr[2] + "/" + arr[1] + "/" + arr[0];
    }

    public List<FileIofficeResponseHgiDto.ResponseFileDto> getDanhSachFileVanBanIofficeHgi(IntegrationParamsDto params, FileRequestHgiDto body) throws Exception {
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceIdHgi);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String URL = config.getParametersValue("apidocument_detail_url").toString();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Tạo body gửi đến API
        HashMap<String, Object> formData = new HashMap<>();
        formData.put("username", config.getParametersValue("username").toString());
        formData.put("password", config.getParametersValue("password").toString());
        if(config.getParametersValue("type_user").equals("true")) {
            formData.put("user", config.getParametersValue("user").toString());
        }else {
            formData.put("user", body.getUserAccount());
        }
        formData.put("id", body.getId_vb());

        // Gửi request đến API nội bộ
        HttpEntity<?> request = new HttpEntity<>(formData, headers);
        ResponseEntity<String> result = restTemplate.exchange(URL, HttpMethod.POST, request, String.class);

        // Parse JSON trả về
        // Parse JSON trả về
        Gson gson = new Gson();
        Type responseType = new TypeToken<FileIofficeResponseHgiDto.ApiResponse>() {}.getType();
        FileIofficeResponseHgiDto.ApiResponse apiResponse = gson.fromJson(result.getBody(), responseType);

        List<FileIofficeResponseHgiDto.ResponseFileDto> allFiles = new ArrayList<>();
        if (apiResponse.getData().getFilesKyso() != null) {
            allFiles.addAll(apiResponse.getData().getFilesKyso());
        }
        if (apiResponse.getData().getFilesVblq() != null) {
            allFiles.addAll(apiResponse.getData().getFilesVblq());
        }
        return allFiles;

    }
}
