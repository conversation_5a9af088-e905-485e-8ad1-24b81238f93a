package vn.vnpt.digo.adapter.service;

import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.codehaus.jackson.JsonParser;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.document.IntegratedLogs;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.istorage.*;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.Dossier;
import vn.vnpt.digo.adapter.pojo.IntegratedService;
import vn.vnpt.digo.adapter.pojo.Translate;
import vn.vnpt.digo.adapter.repository.IntegratedLogsRepository;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class UploadDossierIStorageService {
    @Autowired
    private Microservice microservice;
    @Autowired
    private IntegratedConfigurationService configurationService;
    @Autowired
    private Translator translator;
    @Autowired
    protected RestTemplate restTemplate;
    @Autowired
    private IntegratedLogsRepository integratedLogsRepository;

    public final static ObjectId SERVICE_ID = new ObjectId("664ab18e50014d764bad01fc");

    public AffectedRowsDto uploadDossier(String dossierId, String creatorDepartmentCode, String identityNumber) {
        IntegratedConfigurationDto config = configurationService.searchByServiceId(SERVICE_ID);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        PostDossierIStorageRequestDto body = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String json = config.getParametersValue("config");
            List<ConfigUploadIStorageDto> configUploadIStorages = objectMapper.readValue(json, new TypeReference<List<ConfigUploadIStorageDto>>() {});
            ConfigUploadIStorageDto configUploadIStorage = null;
            for (ConfigUploadIStorageDto c: configUploadIStorages) {
                if (c.getDepartmentCode().equals(creatorDepartmentCode)){
                    configUploadIStorage = c;
                    break;
                }
            }
            if(configUploadIStorage == null) {
                IntegratedLogs logData = new IntegratedLogs();
                IntegratedService integratedService = new IntegratedService();
                integratedService.setId(config.getService().getId());
                integratedService.setName(config.getService().getName());
                logData.setService(integratedService);
                IdNameDto idNameDto = new IdNameDto();
                idNameDto.setId(config.getId());
                idNameDto.setName(config.getName());
                logData.setConfig(idNameDto);
                IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
                idCodeNameSimpleDto.setId(new ObjectId(dossierId));
                logData.setItem(idCodeNameSimpleDto);
                logData.setStatus(0);
                logData.setData(creatorDepartmentCode);
                logData.setMessage("Hồ sơ không thuộc đơn vị cần liên thông lưu trữ hồ sơ");
                integratedLogsRepository.save(logData);
                return new AffectedRowsDto(0, "Hồ sơ không thuộc đơn vị cần liên thông lưu trữ hồ sơ");
            }
            Dossier dossier = getDossier(dossierId);
            if (dossier == null) {
                IntegratedLogs logData = new IntegratedLogs();
                IntegratedService integratedService = new IntegratedService();
                integratedService.setId(config.getService().getId());
                integratedService.setName(config.getService().getName());
                logData.setService(integratedService);
                IdNameDto idNameDto = new IdNameDto();
                idNameDto.setId(config.getId());
                idNameDto.setName(config.getName());
                logData.setConfig(idNameDto);
                IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
                idCodeNameSimpleDto.setId(new ObjectId(dossierId));
                logData.setItem(idCodeNameSimpleDto);
                logData.setStatus(0);
                logData.setData(creatorDepartmentCode);
                logData.setMessage("Không tìm thấy hồ sơ");
                integratedLogsRepository.save(logData);
                return new AffectedRowsDto(0, "Không tìm thấy hồ sơ");
            }
            IStorageGetTokenResponseDto dataToken = getToken(config);
            if (dataToken == null) {
                IntegratedLogs logData = new IntegratedLogs();
                IntegratedService integratedService = new IntegratedService();
                integratedService.setId(config.getService().getId());
                integratedService.setName(config.getService().getName());
                logData.setService(integratedService);
                IdNameDto idNameDto = new IdNameDto();
                idNameDto.setId(config.getId());
                idNameDto.setName(config.getName());
                logData.setConfig(idNameDto);
                IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
                idCodeNameSimpleDto.setId(new ObjectId(dossierId));
                logData.setItem(idCodeNameSimpleDto);
                logData.setStatus(0);
                logData.setData(creatorDepartmentCode);
                logData.setMessage("Lỗi tài khoản đăng nhập");
                integratedLogsRepository.save(logData);
                return new AffectedRowsDto(0, "Lỗi tài khoản đăng nhập");
            }
            /*
             * Lấy thông tin dossier để đẩy sang IStorage
             * */
            body = getInfoPostDossier(dossier,config);
            body.setDepartmentCode(configUploadIStorage.getDepartmentCode());
            body.setFolderId(configUploadIStorage.getFolderId());
            body.setStoragePeriodId(configUploadIStorage.getStoragePeriodId());
            body.setBackupPlan(configUploadIStorage.getBackupPlan());
            body.setModeOfUse(configUploadIStorage.getModeOfUse());
            body.setPhysicalState(configUploadIStorage.getPhysicalState());
            if(configUploadIStorage.getSender() == null || configUploadIStorage.getSender().isEmpty()){
                body.setSender(identityNumber);
            }else {
                body.setSender(configUploadIStorage.getSender());
            }
            ResponseEntity<PostDossierIStorageResponseDto> resultPostDossier = postDossier(body, config, dataToken);
            if (!resultPostDossier.getBody().getStatus().equals("200")) {
                IntegratedLogs logData = new IntegratedLogs();
                IntegratedService integratedService = new IntegratedService();
                integratedService.setId(config.getService().getId());
                integratedService.setName(config.getService().getName());
                logData.setService(integratedService);
                IdNameDto idNameDto = new IdNameDto();
                idNameDto.setId(config.getId());
                idNameDto.setName(config.getName());
                logData.setConfig(idNameDto);
                IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
                idCodeNameSimpleDto.setId(new ObjectId(dossierId));
                logData.setItem(idCodeNameSimpleDto);
                logData.setStatus(0);
                logData.setData(body.toString());
                logData.setMessage(resultPostDossier.toString());
                integratedLogsRepository.save(logData);
                return new AffectedRowsDto(0, resultPostDossier.toString());
            }
            // lấy thông tin thành phần hồ sơ
            List<DossierFormFileDto> formFileList = getDossierFiles(dossier);
            log.info("Thong tin thanh phan ho so");
            int priority = 0;
            for (DossierFormFileDto formFile : formFileList) {
                try {
                    AddDocumentIStorageRequestDto document = new AddDocumentIStorageRequestDto();
                    document.setFileId(resultPostDossier.getBody().getData().getId());
                    document.setName(formFile.getProcedureForm().getName());
                    if (formFile.getDetail().getType().getName() != null) {
                        document.setDocType(formFile.getDetail().getType().getName());
                    }
                    document.setType(config.getParametersValue("documentType"));
                    document.setNum(formFile.getDetail().getQuantity());
                    document.setPriority(priority);
                    document.setSummary(formFile.getProcedureForm().getName());
                    ResponseEntity<PostFileDossierDto> resultPostDocument = postDocument(document, dataToken, config);
                    if (resultPostDocument.getStatusCode() != HttpStatus.CREATED) {
                        IntegratedLogs logData = new IntegratedLogs();
                        IntegratedService integratedService = new IntegratedService();
                        integratedService.setId(config.getService().getId());
                        integratedService.setName(config.getService().getName());
                        logData.setService(integratedService);
                        IdNameDto idNameDto = new IdNameDto();
                        idNameDto.setId(config.getId());
                        idNameDto.setName(config.getName());
                        logData.setConfig(idNameDto);
                        IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
                        idCodeNameSimpleDto.setId(new ObjectId(dossierId));
                        logData.setItem(idCodeNameSimpleDto);
                        logData.setStatus(0);
                        logData.setData(document.toString());
                        logData.setMessage("Không thể thêm tài liệu - Response: " + resultPostDocument);
                        integratedLogsRepository.save(logData);
                        continue;
                    }
                    for (PadPApplyDto.AttachmentDto file : formFile.getFile()) {
                        try {
                            byte[] fileByte = getFile(file.getId());
                            ResponseEntity<PostFileDossierDto[]> resultPostFile = postFile(fileByte, file.getFilename(), dataToken, config);
                            if (resultPostFile.getStatusCode() != HttpStatus.OK) {
                                IntegratedLogs logData = new IntegratedLogs();
                                IntegratedService integratedService = new IntegratedService();
                                integratedService.setId(config.getService().getId());
                                integratedService.setName(config.getService().getName());
                                logData.setService(integratedService);
                                IdNameDto idNameDto = new IdNameDto();
                                idNameDto.setId(config.getId());
                                idNameDto.setName(config.getName());
                                logData.setConfig(idNameDto);
                                IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
                                idCodeNameSimpleDto.setId(new ObjectId(dossierId));
                                logData.setItem(idCodeNameSimpleDto);
                                logData.setStatus(0);
                                logData.setData(file.toString());
                                logData.setMessage("Không thể thêm file - Response: " + resultPostFile);
                                integratedLogsRepository.save(logData);
                                continue;
                            }
                            AddDocumentFileRequestDto bodyDocumentFile = new AddDocumentFileRequestDto();
                            bodyDocumentFile.setDocId(resultPostFile.getBody()[0].getId());
                            bodyDocumentFile.setDocumentId(resultPostDocument.getBody().getId());
                            bodyDocumentFile.setDocName(file.getFilename());
                            bodyDocumentFile.setDocSize(file.getSize());
                            ResponseEntity<PostDossierIStorageResponseDto> ResultPostDocumentFile = postDocumentFile(bodyDocumentFile, dataToken, config);
                            log.info("upload file success");
                        } catch (Exception ex) {
                            IntegratedLogs logData = new IntegratedLogs();
                            IntegratedService integratedService = new IntegratedService();
                            integratedService.setId(config.getService().getId());
                            integratedService.setName(config.getService().getName());
                            logData.setService(integratedService);
                            IdNameDto idNameDto = new IdNameDto();
                            idNameDto.setId(config.getId());
                            idNameDto.setName(config.getName());
                            logData.setConfig(idNameDto);
                            IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
                            idCodeNameSimpleDto.setId(new ObjectId(dossierId));
                            logData.setItem(idCodeNameSimpleDto);
                            logData.setStatus(0);
                            logData.setData(file.toString());
                            logData.setMessage("Không thể thêm file "+ ex.getMessage());
                            integratedLogsRepository.save(logData);
                        }
                    }
                    priority += 1;
                } catch (Exception e) {
                    IntegratedLogs logData = new IntegratedLogs();
                    IntegratedService integratedService = new IntegratedService();
                    integratedService.setId(config.getService().getId());
                    integratedService.setName(config.getService().getName());
                    logData.setService(integratedService);
                    IdNameDto idNameDto = new IdNameDto();
                    idNameDto.setId(config.getId());
                    idNameDto.setName(config.getName());
                    logData.setConfig(idNameDto);
                    IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
                    idCodeNameSimpleDto.setId(new ObjectId(dossierId));
                    logData.setItem(idCodeNameSimpleDto);
                    logData.setStatus(0);
                    logData.setData(formFile.toString());
                    logData.setMessage("Không thể thêm document: " + e.getMessage());
                    integratedLogsRepository.save(logData);
                }

            }
            if(dossier.getAttachment() != null && !dossier.getAttachment().isEmpty()) {
                AddDocumentIStorageRequestDto documentKQ = new AddDocumentIStorageRequestDto();
                documentKQ.setFileId(resultPostDossier.getBody().getData().getId());
                documentKQ.setName("Kết quả xử lý hồ sơ");
                documentKQ.setDocType("Kết quả");
                documentKQ.setType(config.getParametersValue("documentType"));
                documentKQ.setNum(dossier.getAttachment().size());
                documentKQ.setSummary("Kết quả xử lý hồ sơ");
                documentKQ.setPriority(priority);
                ResponseEntity<PostFileDossierDto> resultPostDocumentKQ = postDocument(documentKQ, dataToken, config);
                if(resultPostDocumentKQ.getStatusCode() != HttpStatus.CREATED) {
                    IntegratedLogs logData = new IntegratedLogs();
                    IntegratedService integratedService = new IntegratedService();
                    integratedService.setId(config.getService().getId());
                    integratedService.setName(config.getService().getName());
                    logData.setService(integratedService);
                    IdNameDto idNameDto = new IdNameDto();
                    idNameDto.setId(config.getId());
                    idNameDto.setName(config.getName());
                    logData.setConfig(idNameDto);
                    IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
                    idCodeNameSimpleDto.setId(new ObjectId(dossierId));
                    logData.setItem(idCodeNameSimpleDto);
                    logData.setStatus(0);
                    logData.setData(documentKQ.toString());
                    logData.setMessage("Không thể thêm file - Status code:" + resultPostDocumentKQ);
                    integratedLogsRepository.save(logData);
                }else {
                    for (Dossier.Attachment file : dossier.getAttachment()) {
                        try {
                            byte[] fileByte = getFile(file.getId());
                            ResponseEntity<PostFileDossierDto[]> resultPostFileKQ = postFile(fileByte, file.getFilename(), dataToken, config);
                            if (resultPostFileKQ.getStatusCode() != HttpStatus.OK) {
                                continue;
                            }
                            AddDocumentFileRequestDto bodyDocumentFile = new AddDocumentFileRequestDto();
                            bodyDocumentFile.setDocId(resultPostFileKQ.getBody()[0].getId());
                            bodyDocumentFile.setDocumentId(resultPostDocumentKQ.getBody().getId());
                            bodyDocumentFile.setDocName(file.getFilename());
                            bodyDocumentFile.setDocSize(file.getSize());
                            ResponseEntity<PostDossierIStorageResponseDto> ResultPostDocumentFile = postDocumentFile(bodyDocumentFile, dataToken, config);
                            log.info("upload file success");
                        } catch (Exception ex) {
                            IntegratedLogs logData = new IntegratedLogs();
                            IntegratedService integratedService = new IntegratedService();
                            integratedService.setId(config.getService().getId());
                            integratedService.setName(config.getService().getName());
                            logData.setService(integratedService);
                            IdNameDto idNameDto = new IdNameDto();
                            idNameDto.setId(config.getId());
                            idNameDto.setName(config.getName());
                            logData.setConfig(idNameDto);
                            IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
                            idCodeNameSimpleDto.setId(new ObjectId(dossierId));
                            logData.setItem(idCodeNameSimpleDto);
                            logData.setStatus(0);
                            logData.setData(file.toString());
                            logData.setMessage("Không thể thêm file: "+ ex.getMessage());
                            integratedLogsRepository.save(logData);
                        }
                    }
                }

            }
            IntegratedLogs logData = new IntegratedLogs();
            IntegratedService integratedService = new IntegratedService();
            integratedService.setId(config.getService().getId());
            integratedService.setName(config.getService().getName());
            logData.setService(integratedService);
            IdNameDto idNameDto = new IdNameDto();
            idNameDto.setId(config.getId());
            idNameDto.setName(config.getName());
            logData.setConfig(idNameDto);
            IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
            idCodeNameSimpleDto.setId(dossier.getId());
            idCodeNameSimpleDto.setCode(dossier.getCode());
            logData.setItem(idCodeNameSimpleDto);
            logData.setStatus(1);
            logData.setData(body.toString());
            logData.setMessage(resultPostDossier.getBody().toString());
            integratedLogsRepository.save(logData);
            return new AffectedRowsDto(1,"Đồng bộ hồ sơ lên IStorage thành công");
        } catch (Exception e) {
            IntegratedLogs logData = new IntegratedLogs();
            IntegratedService integratedService = new IntegratedService();
            integratedService.setId(config.getService().getId());
            integratedService.setName(config.getService().getName());
            logData.setService(integratedService);
            IdNameDto idNameDto = new IdNameDto();
            idNameDto.setId(config.getId());
            idNameDto.setName(config.getName());
            logData.setConfig(idNameDto);
            IdCodeNameSimpleDto idCodeNameSimpleDto = new IdCodeNameSimpleDto();
            idCodeNameSimpleDto.setId(new ObjectId(dossierId));
            logData.setItem(idCodeNameSimpleDto);
            logData.setStatus(0);
            logData.setMessage(e.toString());
            if(body != null) {
                logData.setData(body.toString());
            }else {
                logData.setData(creatorDepartmentCode);
            }
            integratedLogsRepository.save(logData);
            log.info("Exception: " + e.getMessage());
            return new AffectedRowsDto(0, e.toString());
        }
    }

    public ResponseEntity<PostDossierIStorageResponseDto> postDocumentFile(AddDocumentFileRequestDto body, IStorageGetTokenResponseDto dataToken, IntegratedConfigurationDto config) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(config.getParametersValue("urlAddDescriptionFile"));
        UriComponents uriComponents = uriBuilder.encode().build();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(dataToken.getAccess_token());
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<AddDocumentFileRequestDto> request = new HttpEntity<>(body, headers);
        ResponseEntity<PostDossierIStorageResponseDto> result = restTemplate.exchange(uriComponents.toUriString(), HttpMethod.POST, request, PostDossierIStorageResponseDto.class);
        return result;
    }

    public ResponseEntity<PostFileDossierDto[]> postFile(byte[] file, String fileName,IStorageGetTokenResponseDto dataToken, IntegratedConfigurationDto config) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(config.getParametersValue("urlUploadFile"));
        UriComponents uriComponents = uriBuilder.encode().build();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(dataToken.getAccess_token());
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        ByteArrayResource fileResource = new ByteArrayResource(file) {
            @Override
            public String getFilename() {
                return fileName;
            }
        };
        MultiValueMap<String, Object> bodyUploadFile = new LinkedMultiValueMap<>();
        bodyUploadFile.add("files", fileResource);
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(bodyUploadFile, headers);
        ResponseEntity<PostFileDossierDto[]> result = restTemplate.exchange(uriComponents.toUriString(), HttpMethod.POST, request, PostFileDossierDto[].class);
        return result;
    }

    public ResponseEntity<PostFileDossierDto> postDocument(AddDocumentIStorageRequestDto document, IStorageGetTokenResponseDto dataToken, IntegratedConfigurationDto config) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(config.getParametersValue("urlAddDocument"));
        UriComponents uriComponents = uriBuilder.encode().build();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(dataToken.getAccess_token());
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<AddDocumentIStorageRequestDto> request = new HttpEntity<>(document, headers);
        ResponseEntity<PostFileDossierDto> result = restTemplate.exchange(uriComponents.toUriString(), HttpMethod.POST, request, PostFileDossierDto.class);
        return result;
    }

    public ResponseEntity<PostDossierIStorageResponseDto> postDossier(PostDossierIStorageRequestDto body, IntegratedConfigurationDto config, IStorageGetTokenResponseDto dataToken) {
        try {
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(config.getParametersValue("urlCreateDossier"));
            UriComponents uriComponents = uriBuilder.encode().build();
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(dataToken.getAccess_token());
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<PostDossierIStorageRequestDto> request = new HttpEntity<>(body, headers);
            ResponseEntity<PostDossierIStorageResponseDto> result = restTemplate.exchange(uriComponents.toUriString(), HttpMethod.POST, request, PostDossierIStorageResponseDto.class);
            return result;
        } catch (Exception e) {
            log.error("Error calling http: ", e.getMessage());
            throw new DigoHttpException(11003, new String[]{"post dosier istorage:", e.getMessage()},
                    HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    public List<DossierFormFileDto> getDossierFiles(Dossier dossier) {
        String dossierFormFileUrl = microservice.padmanUri("/dossier-form-file/getBy?dossierId=" + dossier.getId().toHexString()).toUriString();
        DossierFormFileDto[] newDossierFormFile = MicroserviceExchange.get(new RestTemplate(), dossierFormFileUrl, DossierFormFileDto[].class);
        return List.of(newDossierFormFile);
    }

    public PostDossierIStorageRequestDto getInfoPostDossier(Dossier dossier,IntegratedConfigurationDto config) {
        PostDossierIStorageRequestDto data = new PostDossierIStorageRequestDto();
        data.setSrc("IGATE");
        // lấy năm của thời gian tạo hồ sơ
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dossier.getCreatedDate());
        int year = calendar.get(Calendar.YEAR);
        data.setYear(year);
        data.setCode(dossier.getCode());
        // lấy title
        String title = dossier.getProcedure().getTranslate().stream()
                .filter(name -> name.getLanguageId() == 228)
                .findFirst()
                .map(Translate::getName)
                .orElse("");
        data.setTitle(title);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        data.setFromDate(sdf.format(dossier.getCreatedDate()));
        data.setToDate(sdf.format(dossier.getAppointmentDate()));
        data.setType(config.getParametersValue("type"));
        data.setLanguage(config.getParametersValue("language"));
        return data;
    }

    public Dossier getDossier(String id) {
        String dossierUrl = microservice.padmanUri("/dossier/" + id + "/--online").toUriString();
        return MicroserviceExchange.get(new RestTemplate(), dossierUrl, Dossier.class);
    }

    public byte[] getFile(String id) {
        String fileUrl = microservice.filemanUri("file/" + id).toUriString();
        byte[] file = MicroserviceExchange.get(new RestTemplate(), fileUrl, byte[].class);
        return file;
    }

    public IStorageGetTokenResponseDto getToken(IntegratedConfigurationDto config) {
        boolean isGetTokenLGSP = config.getParametersValue("isGetTokenLGSP");
        if(!isGetTokenLGSP) {
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("grant_type", config.getParametersValue("grant_type"));
            body.add("client_id", config.getParametersValue("client_id"));
            body.add("client_secret", config.getParametersValue("client_secret"));
            body.add("scope", config.getParametersValue("scope"));
            body.add("username", config.getParametersValue("username"));
            body.add("password", config.getParametersValue("password"));
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(config.getParametersValue("urlGetToken"));
            UriComponents uriComponents = uriBuilder.encode().build();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
            ResponseEntity<IStorageGetTokenResponseDto> result = restTemplate.exchange(uriComponents.toUriString(), HttpMethod.POST, request, IStorageGetTokenResponseDto.class);
            return result.getBody();
        }
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(config.getParametersValue("urlGetToken"));
        UriComponents uriComponents = uriBuilder.encode().build();
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", config.getParametersValue("keyLGSP"));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body,headers);
        ResponseEntity<IStorageGetTokenResponseDto> result = restTemplate.exchange(uriComponents.toUriString(), HttpMethod.POST, request, IStorageGetTokenResponseDto.class);
        return result.getBody();
    }

}
