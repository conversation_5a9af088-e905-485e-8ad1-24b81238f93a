
package vn.vnpt.digo.adapter.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.VNPostStatus;
import vn.vnpt.digo.adapter.dto.VNPostResultDto;
import vn.vnpt.digo.adapter.repository.VNPostStatusRepository;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Translator;

@Service
public class VNPostStatusService {
    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private VNPostStatusRepository VNPostStatusRepository;
    @Autowired
    private MongoTemplate mongoTemplate;
    
    public VNPostResultDto postVNPostStatus(List<VNPostStatus> body)
    {
        VNPostResultDto result = new VNPostResultDto();
        for(VNPostStatus a : body)
        {
            VNPostStatusRepository.save(a);
        }
        result.setMessage("Success");
        result.setStatus("1");
        return result;
    }
    
    public List<VNPostStatus> search(String customerCode, String orderNumber, String deploymentId)
    {
        List<VNPostStatus> arrResult;
        arrResult = VNPostStatusRepository.searchVNPostStatus(customerCode,orderNumber,deploymentId);
        return arrResult;
    }
    
    public List<VNPostStatus> searchLast(String customerCode, String orderNumber, String deploymentId) {
        List<VNPostStatus> arrResult;
        arrResult = VNPostStatusRepository.searchVNPostStatus(customerCode, orderNumber, deploymentId);
//        System.out.println("arrResult");
//        System.out.println(arrResult);
        List<VNPostStatus> arrResultLast = new ArrayList<VNPostStatus>();
        if (arrResult.size() > 0) {
            VNPostStatus last = arrResult.get(0);
//            System.out.println("last");
//            System.out.println(last);
            arrResultLast.add(last);
        }
        return arrResultLast;
    }
    
    public VNPostResultDto postVNPostStatusItem(VNPostStatus body)
    {
        VNPostResultDto result = new VNPostResultDto();        
        VNPostStatusRepository.save(body);
        
        result.setMessage("Success");
        result.setStatus("1");
        return result;
    }

    public VNPostStatus getLastVnPostStatus(String customerCode, String orderNumber, String deploymentId)
    {
        Query query = new Query();
        if (Objects.nonNull(customerCode) && !customerCode.trim().isEmpty()) {
                query.addCriteria(Criteria.where("customerCode").is(customerCode));
        }
        if (Objects.nonNull(orderNumber) && !orderNumber.trim().isEmpty()) {
            query.addCriteria(Criteria.where("orderNumber").is(orderNumber));
        }
        if (Objects.nonNull(deploymentId) && !deploymentId.trim().isEmpty()) {
            query.addCriteria(Criteria.where("deploymentId").is(deploymentId));
        }
        query.with(Sort.by(Sort.Direction.DESC, "eventTime"));
        query.limit(1);
        return mongoTemplate.findOne(Query.of(query), VNPostStatus.class);
    }
}
