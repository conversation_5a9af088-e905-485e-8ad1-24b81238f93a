package vn.vnpt.digo.adapter.service;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.adapter.document.HistorySendTanDanDoc;
import vn.vnpt.digo.adapter.dto.DataResponseTanDanDto;
import vn.vnpt.digo.adapter.repository.HistorySendTanDanDocRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Date;
@Service
public class HistorySendTanDanDocService {

    Logger logger = LoggerFactory.getLogger(IntegratedLogsService.class);

    @Autowired
    private HistorySendTanDanDocRepository historySendTanDanDocRepository;

    public void save(String unitCode, String dossierId, ObjectId configId,
                     Map<String, Object> params, Map<String, Object> body, DataResponseTanDanDto response){
        try{
            HistorySendTanDanDoc log = new HistorySendTanDanDoc();
            log.setUnitCode(unitCode);
            log.setDossierId(dossierId);
            log.setConfigId(configId);
            log.setParams(params);
            log.setBody(body);
            log.setResponse(response);
            log.setCreateDate(new Date());
            historySendTanDanDocRepository.save(log);
        } catch (Exception e){
            logger.info("Save log send Tan Dan error: " + e.getMessage());
        }
    }

    public void remove(String unitCode, String dossierCode){
        HistorySendTanDanDoc item = historySendTanDanDocRepository.findByUnitCodeAndDossierCode(unitCode,dossierCode);
        if(Objects.nonNull(item)){
            historySendTanDanDocRepository.delete(item);
        }
    }

    public List<HistorySendTanDanDoc> getUnits(){
        List<HistorySendTanDanDoc> allItem = historySendTanDanDocRepository.findAll();
        List<HistorySendTanDanDoc> items = new ArrayList<>();
        List<String> unitCodes = new ArrayList<>();
        for(HistorySendTanDanDoc item:allItem){
            if(!unitCodes.contains(item.getUnitCode())){
                unitCodes.add(item.getUnitCode());
                items.add(item);
            }
        }
        return items;
    }
}
