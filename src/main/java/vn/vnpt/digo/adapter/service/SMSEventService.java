package vn.vnpt.digo.adapter.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.adapter.document.SMSEvent;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.v2.email.SendEmailWithTemplateDto;
import vn.vnpt.digo.adapter.dto.v2.smsbrandname.SendWithTemplateDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.repository.SMSEventRepository;
import vn.vnpt.digo.adapter.stream.NotifyProducerStream;
import vn.vnpt.digo.adapter.util.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;

@Service
public class SMSEventService {

    @Autowired
    private SMSEventRepository smsEventRepository;

    @Autowired
    private NotifyProducerStream notifyProducerStream;

    @Autowired
    private Translator translator;


    public List<IdDto> saveEvent(List<SendWithTemplateDto> request) throws ExecutionException, InterruptedException {
        List<IdDto> idDtoList = new ArrayList<>();
        for (SendWithTemplateDto batch: request){
            SMSEvent smsEvent = new SMSEvent();
            smsEvent.setCreatedDate(new Date());
            smsEvent.setRequest(batch);
            smsEvent.setUserId(Context.getUserId().toString());
            smsEvent.setFullname(Context.getUserFullname());
            smsEvent.setSendTo(batch.getPhoneNumber());
            Object dossierCode = batch.getParams().get("dossierCode");
            if (Objects.nonNull(dossierCode) && dossierCode instanceof String) {
                smsEvent.setDossierCode((String) dossierCode);
            }

            smsEvent = smsEventRepository.save(smsEvent);
            smsEvent.setStatus(1);
            smsEvent.setMessage("Send to kafka success");
            if(!this.notifyProducerStream.pushMessageNotifyMain(smsEvent).get()){
                smsEvent.setStatus(0);
                smsEvent.setMessage("Send to kafka fail");
                smsEventRepository.save(smsEvent);
                throw new DigoHttpException(10023, new String[]{translator.toLocale("lang.word.send-brandname")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }

            idDtoList.add(new IdDto(smsEvent.getId()));
        }

        return idDtoList;
    }

    public List<IdDto> saveEmailEvent(List<SendEmailWithTemplateDto> request) throws ExecutionException, InterruptedException {
        List<IdDto> idDtoList = new ArrayList<>();
        for (SendEmailWithTemplateDto batch: request){
            SMSEvent smsEvent = new SMSEvent();
            smsEvent.setCreatedDate(new Date());
            smsEvent.setRequest(batch);
            smsEvent.setType(2);
            smsEvent.setUserId(Context.getUserId().toString());
            smsEvent.setFullname(Context.getUserFullname());
            smsEvent.setSendTo(batch.getEmailAddress());
            Object dossierCode = batch.getParams().get("dossierCode");
            if (Objects.nonNull(dossierCode) && dossierCode instanceof String) {
                smsEvent.setDossierCode((String) dossierCode);
            }

            smsEvent = smsEventRepository.save(smsEvent);
            smsEvent.setStatus(1);
            smsEvent.setMessage("Send to kafka success");
            if(!this.notifyProducerStream.pushMessageNotifyMain(smsEvent).get()){
                smsEvent.setStatus(0);
                smsEvent.setMessage("Send to kafka fail");
                smsEventRepository.save(smsEvent);
                throw new DigoHttpException(10023, new String[]{translator.toLocale("lang.word.send-brandname")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
            idDtoList.add(new IdDto(smsEvent.getId()));
        }

        return idDtoList;
    }
}
