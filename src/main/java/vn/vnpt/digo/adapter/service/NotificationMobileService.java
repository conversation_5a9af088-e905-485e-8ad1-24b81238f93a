package vn.vnpt.digo.adapter.service;

import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.NotificationDto;
import vn.vnpt.digo.adapter.dto.cto.ContentFreemarkerDtoCTO;
import vn.vnpt.digo.adapter.dto.cto.FreemarkerParametersDtoCTO;
import vn.vnpt.digo.adapter.dto.cto.NotifiAppTTHCCtoDto;
import vn.vnpt.digo.adapter.dto.cto.UserMessageRequestCTODto;
import vn.vnpt.digo.adapter.dto.v2.freemarker.FreemarkerParametersDto;
import vn.vnpt.digo.adapter.dto.zalo.ZaloV2TokenResDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Objects;

@Service
public class NotificationMobileService {
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private Translator translator;

    @Autowired
    private IntegratedConfigurationService configurationService;

    private Logger logger = LoggerFactory.getLogger(NEACSignService.class);

    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511899110");
    public String getToken(ObjectId configIdParams, ObjectId agencyId, ObjectId subsystemId)
    {
        //Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(configIdParams)) {
            config = configurationService.getConfig(configIdParams);
        } else {
            config = configurationService.getConfig(agencyId, subsystemId, this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String configId = config.getParametersValue("configId").toString();
//        String fcmDataMap = config.getParametersValue("fcmDataMap").toString();
        String redirectType = config.getParametersValue("fcmDataMap-redirectType").toString();
        String redirectValue = config.getParametersValue("fcmDataMap-redirectValue").toString();
        String redirectCondition = config.getParametersValue("fcmDataMap-redirectCondition").toString();
        String redirectFeatureName = config.getParametersValue("fcmDataMap-redirectFeatureName").toString();
        return "jsonCert";
    }

    public NotificationDto callVnCitizen(Map<String, String> params, ObjectId configIdParams, ObjectId agencyId, ObjectId subsystemId) {
        try{
            IntegratedConfigurationDto config;
            if (Objects.nonNull(configIdParams)) {
                config = configurationService.getConfig(configIdParams);
            } else {
                config = configurationService.getConfig(agencyId, subsystemId, this.serviceId);
            }
            if (Objects.isNull(config)) {
                throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
            }
            String configId = config.getParametersValue("configId").toString();
            String tenant = config.getParametersValue("tenant").toString();
            String fcmDataMap = config.getParametersValue("fcmDataMap").toString();
            String redirectType = config.getParametersValue("fcmDataMap-redirectType").toString();
            String redirectValue = config.getParametersValue("fcmDataMap-redirectValue").toString();
            String redirectCondition = config.getParametersValue("fcmDataMap-redirectCondition").toString();
            String redirectFeatureName = config.getParametersValue("fcmDataMap-redirectFeatureName").toString();
            String fromSystemId = config.getParametersValue("from-system-id").toString();
            String fromSystemName = config.getParametersValue("from-system-name").toString();
            String title = config.getParametersValue("title").toString();
            NotificationDto body = new NotificationDto();
            ZaloV2TokenResDto token = getToken(config);
            body.setConfigId(configId);
            if(params.get("phoneNumber") != null){
                body.getTopic().add(params.get("phoneNumber"));
            }
            if(params.get("identityNumber") != null && body.getTopic().size() == 0){
                body.getTopic().add(params.get("identityNumber"));
            }
            if(params.get("userId") != null && !params.get("userId").isEmpty() && body.getTopic().size() == 0){
                body.getTopic().add(params.get("userId"));
            }
            if(params.get("province") != null && !params.get("province").isEmpty() && body.getTopic().size() == 0){
                body.getTopic().add(params.get("province"));
            }
            if(params.get("district") != null && !params.get("district").isEmpty() && body.getTopic().size() == 0){
                body.getTopic().add(params.get("district"));
            }
            if(params.get("village") != null && !params.get("village").isEmpty() && body.getTopic().size() == 0){
                body.getTopic().add(params.get("village"));
            }
            if(params.get("gender") != null && !params.get("gender").isEmpty() && body.getTopic().size() == 0){
                if(params.get("gender").equals("1")){
                    body.getTopic().add(tenant+"_male");
                }
                if(params.get("gender").equals("0")){
                    body.getTopic().add(tenant+"_female");
                }
            }
            if(body.getTopic().size() == 0){
                body.getTopic().add(tenant);
            }
            if(Objects.isNull(title) || title.isEmpty()){
                title = "Th\u00F4ng b\u00E1o tr\u1EA1ng th\u00E1i x\u1EED l\u00FD h\u1ED3 s\u01A1 d\u1ECBch v\u1EE5 c\u00F4ng\n";
            }
            if(fcmDataMap.equals("true")){
                body.setFcmDataMap(new NotificationDto.FcmDataMap(redirectType, redirectValue, redirectCondition, redirectFeatureName));
            }
            body.setTitle(title);
            body.setProvider("vnCitizens");
            body.setContent(params.get("title") +"!");
            body.setFromSystem(new NotificationDto.FromSystem(fromSystemId, fromSystemName));
            String endpoint = config.getParametersValue("url-notification").toString();
            IdDto idDto = MicroserviceExchange.postJsonBearAuth(restTemplate, endpoint, token.getAccess_token(), body, IdDto.class);
            return body;
        } catch (Exception e) {
            logger.info("callVnCitizen error: ");
            logger.info("callVnCitizen - Exception: " + e.getMessage());
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
        //Get config

    }


    public ZaloV2TokenResDto getToken(IntegratedConfigurationDto config) {
        try {
            String grantType = config.getParametersValue("grant_type").toString();
            String client_id = config.getParametersValue("client_id").toString();
            String client_secret = config.getParametersValue("client_secret").toString();
            String scope = config.getParametersValue("scope").toString();
            String endpoint = config.getParametersValue("url-get-token").toString();
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("grant_type", grantType);
            body.add("client_id", client_id);
            body.add("client_secret", client_secret);
            body.add("scope", scope);
            ZaloV2TokenResDto ret = MicroserviceExchange.getCitizenToken(restTemplate, endpoint, body);
            return ret;
        } catch (Exception e) {
            logger.info("get token error: ");
            logger.info("exchangeWithResponse - Exception: " + e.getMessage());
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public AffectedRowsDto sendNotificationAppTthcCTO(NotifiAppTTHCCtoDto request) throws Exception {
        try {
            //Get content
            String content = request.getTemplates().getStatusDetail();
            if(StringUtils.isEmpty(request.getTemplates().getStatusDetail()))
            {
                String uriRE = microservice.reporterUri("freemarker/--report?id=" + request.getTemplateId()).toUriString();
                FreemarkerParametersDtoCTO contentRequest = new FreemarkerParametersDtoCTO();

                if (contentRequest.getParameters() == null) {
                    contentRequest.setParameters(new ContentFreemarkerDtoCTO());
                }
                contentRequest.getParameters().setDossierCode(request.getTemplates().getDossierCode());
                contentRequest.getParameters().setDossierStatus(request.getTemplates().getStatusTitle());
                contentRequest.getParameters().setNextTask(request.getTemplates().getNextTask());

                content = MicroserviceExchange.callApi(restTemplate, HttpMethod.POST, uriRE, contentRequest, String.class);
            }

            String uriPA = microservice.padmanUri("dossier-notification/send").toUriString();
            UserMessageRequestCTODto userMessageRequestCTODto = new UserMessageRequestCTODto();
            userMessageRequestCTODto.setUserIds(request.getTemplates().getUserIds());
            userMessageRequestCTODto.setAgencyId(request.getTemplates().getAgencyId());
            userMessageRequestCTODto.setPositionId(request.getTemplates().getPositionId());
            userMessageRequestCTODto.setBodyMessage(content);
            String res = MicroserviceExchange.callApi(restTemplate, HttpMethod.POST, uriPA, userMessageRequestCTODto, String.class);
            return new AffectedRowsDto(1);

        } catch (Exception ex){
            var messageDefault = "Có hồ sơ mới " + request.getTemplates().getNextTask() + ", hãy xử lý hồ sơ";
            String uriPA = microservice.padmanUri("dossier-notification/send").toUriString();
            UserMessageRequestCTODto userMessageRequestCTODto = new UserMessageRequestCTODto();
            userMessageRequestCTODto.setUserIds(request.getTemplates().getUserIds());
            userMessageRequestCTODto.setAgencyId(request.getTemplates().getAgencyId());
            userMessageRequestCTODto.setPositionId(request.getTemplates().getPositionId());
            userMessageRequestCTODto.setBodyMessage(messageDefault);
            String res = MicroserviceExchange.callApi(restTemplate, HttpMethod.POST, uriPA, userMessageRequestCTODto, String.class);

            return new AffectedRowsDto(1);
        }
    }
}
