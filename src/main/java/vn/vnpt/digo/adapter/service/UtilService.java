package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.ByteArrayInputStream;
import java.util.*;
import javax.servlet.http.HttpServletResponse;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.IdCodeDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.GetDossierOnlineDto;
import vn.vnpt.digo.adapter.dto.Statistical468And761Dto;
import vn.vnpt.digo.adapter.dto.PadPApplyDto;
import vn.vnpt.digo.adapter.dto.AffectedMessageDto;
import vn.vnpt.digo.adapter.dto.MappingDataDto;
import vn.vnpt.digo.adapter.dto.PlaceDetailDto;
import vn.vnpt.digo.adapter.dto.cmu.CMULGSPDossierLLTPDto;
import vn.vnpt.digo.adapter.dto.cmu.LLTPDossierStatusCMU;
import vn.vnpt.digo.adapter.dto.industry_trade_bd.FileInfo;
import vn.vnpt.digo.adapter.dto.lgsphcm.*;

import vn.vnpt.digo.adapter.dto.dbn.DBNDossierApplyOnlineDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPDossierDetailDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPDossierDetailLLTPDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPDossierDetailHTTPDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPDossierDto1;
import vn.vnpt.digo.adapter.dto.lgsphcm.LLTPUpdateStatusDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.HTTPAgencyFullAddressDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMDossierDetailDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMDossierFeeDto;
import vn.vnpt.digo.adapter.dto.lgsphgi.HGILGSPDossierDetailLLTPDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDetailDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierFeeDto;
import vn.vnpt.digo.adapter.dto.qnm.ilis.ILisLogResDto;
import vn.vnpt.digo.adapter.dto.utils.*;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.ErrorMessage;
import vn.vnpt.digo.adapter.pojo.UpdateVNPostStatus;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.dto.lgsphcm.HTTPUpdateStatusDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LLTPDossierSendFailDto;
import vn.vnpt.digo.adapter.util.Oauth2RestTemplate;
import vn.vnpt.digo.adapter.util.StringHelper;

import java.text.Normalizer;

@Service
public class UtilService {
    
    Logger logger = LoggerFactory.getLogger(UtilService.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private Oauth2RestTemplate oauth2RestTemplate;

    @Cacheable(value = "UtilServiceGetTag", key = "#id")
    public IdCodeNameDto getTag(RestTemplate rest, ObjectId id){
        String endpoint = microservice.basecatUri("/tag/" + id.toHexString()).encode().build().toUriString();
        IdCodeNameDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, IdCodeNameDto.class);
        return ret;
    }

    public NpsDossierDetailDto getDossier(RestTemplate rest, ObjectId id){
        String endpoint = microservice.padmanUri("dossier/" +id.toHexString() + "/--online").encode().build().toUriString();
        NpsDossierDetailDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, NpsDossierDetailDto.class);
        return ret;
    }

    public CommentDossierDto getCommentDossier(RestTemplate rest, ObjectId id){
        String endpoint = microservice.messengerUri("comment/").encode().build().toUriString() + "?group-id=2&page=0&size=10&sort=createdDate,desc&item-id=" + id.toHexString();
        CommentDossierDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, CommentDossierDto.class);
        return ret;
    }
    public GetDossierOnlineDto getDossierByCode(RestTemplate rest, String code){
        String endpoint = microservice.padmanUri("dossier/" + code + "/--by-code").encode().build().toUriString();
        GetDossierOnlineDto ret = MicroserviceExchange.get(rest, endpoint, GetDossierOnlineDto.class);
        return ret;
    }

    public ComponentDossierDto getComponentDossier(RestTemplate rest, ObjectId id) {
        String endpoint =microservice.storageUri("igate/--components").encode().build().toUriString() + "?system=igate2&dossier-id=" + id.toHexString();
        ComponentDossierDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, ComponentDossierDto.class);
        return ret;
    }

    public GetDossierOnlineDto getDossierById(String dossierId){
        String endpoint = microservice.padmanUri("dossier/" + dossierId + "/--online").encode().build().toUriString();
        return MicroserviceExchange.get(oauth2RestTemplate.getOAuth2RestTemplate(), endpoint, GetDossierOnlineDto.class);
    }
    
    public LGSPHCMDossierDetailDto getDossierLGSPHCM(RestTemplate rest, ObjectId id){
//        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" +id.toHexString() + "/--online").encode().build().toUriString();
        LGSPHCMDossierDetailDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, LGSPHCMDossierDetailDto.class);
        return ret;
    }
    public AffectedRowsDto updateSyncExtend(RestTemplate rest, ObjectId id, PadPApplyDto.SyncExtendWithNoteDto SyncExtendDto) {
        try {
            //String endpoint = "http://localhost:8081/dossier/"  + id.toHexString() + "/--update-sync-extend";
            String endpoint = microservice.padmanUri("/dossier/" + id.toHexString() + "/--update-sync-extend").toUriString();
            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, SyncExtendDto, AffectedRowsDto.class);
            return ret;
        } catch (Exception e) {
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public List<Statistical468And761Dto.DigitizingResponDto> checkDigitizing(RestTemplate rest, String ids){
        try{
            String endpoint = microservice.filemanUri("file/--check-digitizing?file-ids=" + ids).encode().build().toUriString();
            String json = MicroserviceExchange.getNoAuth(rest, endpoint, String.class);
            ObjectMapper mapper = new ObjectMapper();
            List<Statistical468And761Dto.DigitizingResponDto> ret = mapper.readValue(json, new TypeReference<List<Statistical468And761Dto.DigitizingResponDto>>(){});
            return ret;
        }catch (Exception ex){
            return null;
        }
    }
    public Object nbrsExchange(RestTemplate rest, HashMap<String, String> req) throws  Exception{
        try{
            String endpoint = microservice.adapterUri("/service/investment-plan/--dossier-info/--hcm").encode().build().toString();
            //String endpoint = "http://10.200.20.154:30102/service/investment-plan/--dossier-info/--hcm";
            return MicroserviceExchange.postJsonNoAuth(rest, endpoint , req, Object.class);
        }catch(Exception e){
            return null;
        }
    }
    public ErrorMessage syncNBRSDossierHCM(RestTemplate rest , Map<String , Object> requestData) throws Exception{
        String endpoint = microservice.padmanUri("/dossier-nbrs/--hcm").encode().build().toUriString();
        ErrorMessage errorMessage = MicroserviceExchange.postJsonNoAuth(rest, endpoint, requestData, ErrorMessage.class);
        return errorMessage;
    }

    public AffectedRowsDto checkOnlinePayment(RestTemplate rest, ObjectId id){
        String endpoint = microservice.padmanUri("dossier-payment/--check-online-payment?dossier-id=" + id.toHexString()).encode().build().toUriString();
        AffectedRowsDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, AffectedRowsDto.class);
        return ret;
    }
    public AffectedRowsDto checkOnlinePaymentBDTC(RestTemplate rest, ObjectId id){
        String endpoint = microservice.padmanUri("dossier-payment/--check-online-payment-bdtc?dossier-id=" + id.toHexString()).encode().build().toUriString();
        //String endpoint = "http://localhost:8081/dossier-payment/--check-online-payment-bdtc?dossier-id=" + id.toHexString();
        AffectedRowsDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, AffectedRowsDto.class);
        return ret;
    }
    public AffectedRowsDto checkOnlinePaymentHGI(RestTemplate rest, ObjectId id){
        String endpoint = microservice.padmanUri("dossier-payment/--check-online-payment-hgi?dossier-id=" + id.toHexString()).encode().build().toUriString();
        //String endpoint = "http://localhost:8081/dossier-payment/--check-online-payment-bdtc?dossier-id=" + id.toHexString();
        AffectedRowsDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, AffectedRowsDto.class);
        return ret;
    }


    public IdCodeDto getFormDetail(IntegratedConfigurationDto config, ObjectId id){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.basepadUri("form/" + id.toHexString()).encode().build().toUriString();
        IdCodeDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, IdCodeDto.class);
        return ret;
    }
    
    public HCMLGSPDossierDetailDto getDossierHCMLGSP(RestTemplate rest, ObjectId id){
        String endpoint = microservice.padmanUri("dossier/" +id.toHexString() + "/--online").encode().build().toUriString();
        HCMLGSPDossierDetailDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, HCMLGSPDossierDetailDto.class);
        return ret;
    }

    public HCMLGSPDossierDetailLLTPDto getDossierLLTPHCMLGSP(IntegratedConfigurationDto config, ObjectId id){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" +id.toHexString() + "/--online").encode().build().toUriString();
        HCMLGSPDossierDetailLLTPDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, HCMLGSPDossierDetailLLTPDto.class);
        return ret;
    }
    public HGILGSPDossierDetailLLTPDto getDossierLLTPHGILGSP_v1(IntegratedConfigurationDto config, ObjectId id){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" +id.toHexString() + "/--online").encode().build().toUriString();
        HGILGSPDossierDetailLLTPDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, HGILGSPDossierDetailLLTPDto.class);
        return ret;
    }
    
    public HCMLGSPDossierDetailHTTPDto getDossierHTTPHCMLGSP(IntegratedConfigurationDto config, ObjectId id){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" +id.toHexString() + "/--online").encode().build().toUriString();
        HCMLGSPDossierDetailHTTPDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, HCMLGSPDossierDetailHTTPDto.class);
        return ret;
    }
    
    public NpsDossierFeeDto[] getDossierFees(RestTemplate rest, ObjectId id){
        String endpoint = microservice.padmanUri("dossier-fee?dossier-id=" +id.toHexString()).encode().build().toUriString();
        NpsDossierFeeDto[] ret = MicroserviceExchange.getNoAuth(rest, endpoint, NpsDossierFeeDto[].class);
        return ret;
    }
    
    public IdCodeProcostTypeDto[] getCodeDossierFees(RestTemplate rest, List<String> listId){
        String endpoint = microservice.basepadUri("procost-type/--get-code-by-list-id").encode().build().toUriString();
        IdCodeProcostTypeDto[] ret = MicroserviceExchange.postJsonNoAuth(rest, endpoint, listId, IdCodeProcostTypeDto[].class);
        return ret;
    }
    
    public LGSPHCMDossierFeeDto[] getDossierFeesLGSPHCM(RestTemplate rest, ObjectId id){
        String endpoint = microservice.padmanUri("dossier-fee?dossier-id=" +id.toHexString()).encode().build().toUriString();
        LGSPHCMDossierFeeDto[] ret = MicroserviceExchange.getNoAuth(rest, endpoint, LGSPHCMDossierFeeDto[].class);
        return ret;
    }

    public NpsDossierFeeDto[] getDossierFeesHCMLGSP(RestTemplate rest, ObjectId id){
        String endpoint = microservice.padmanUri("dossier-fee?dossier-id=" +id.toHexString()).encode().build().toUriString();
        NpsDossierFeeDto[] ret = MicroserviceExchange.getNoAuth(rest, endpoint, NpsDossierFeeDto[].class);
        return ret;
    }

    @Cacheable(value = "UtilServiceGetUser", key = "#id")
    public UserInfoDto getUser(RestTemplate rest, ObjectId id){
        if (id == null) {
            return null;
        }
        String endpoint = microservice.humanUri("user/" +id.toHexString()).encode().build().toUriString();
        UserInfoDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, UserInfoDto.class);
        return ret;
    }

    @Cacheable(value = "UtilServiceGetUser", key = "#id")
    public UserInfoDto getUser(IntegratedConfigurationDto config, ObjectId id){
        RestTemplate rest = getOAuth2RestTemplate(config);
        String endpoint = microservice.humanUri("user/" +id.toHexString()).encode().build().toUriString();
        UserInfoDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, UserInfoDto.class);
        return ret;
    }

    @Cacheable(value = "UtilServiceGetProcedure", key = "#id")
    public ProcedureDetailDto getProcedure(RestTemplate rest, ObjectId id){
        String endpoint = microservice.basepadUri("procedure/" +id.toHexString()).encode().build().toUriString();
        ProcedureDetailDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, ProcedureDetailDto.class);
        return ret;
    }
    
    @Cacheable(value = "UtilServiceGetProcedureParentNationCode", key = "#id")
    public ProcedureDetailDto getProcedureSearchParentNationCode(RestTemplate rest, ObjectId id){
        String endpoint = microservice.basepadUri("procedure/get-procedure-parent-nation-code?id=" +id.toHexString()).encode().build().toUriString();
//        String endpoint = "http://localhost:8069/procedure/get-procedure-parent-nation-code?id=" + id.toHexString();
        ProcedureDetailDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, ProcedureDetailDto.class);
        return ret;
    }

    @Cacheable(value = "UtilServiceGetSector", key = "#id")
    public SectorDetailDto getSector(RestTemplate rest, ObjectId id){
        String endpoint = microservice.basepadUri("sector/" +id.toHexString()).encode().build().toUriString();
        SectorDetailDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, SectorDetailDto.class);
        return ret;
    }


    @Cacheable(value = "UtilServiceGetAgencyNameCode", key = "#id")
    public IdCodeNameSimpleDto getAgencyNameCode(RestTemplate rest, ObjectId id){
        String endpoint = microservice.basedataUri("agency/" +id.toHexString() + "/name+code+ancetors").encode().build().toUriString();
        IdCodeNameSimpleDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, IdCodeNameSimpleDto.class);
        return ret;
    }

    public FileNameSizeDto getFileInfo(RestTemplate rest, String fileId){
        String endpoint = microservice.filemanUri("file/" + fileId + "/filename+size+type").encode().build().toUriString();
        FileNameSizeDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, FileNameSizeDto.class);
        return ret;
    }

    public ResponseEntity<Object> downloadAttachment(RestTemplate rest, String fileId) {

        try {
            String endpoint = microservice.filemanUri("file/download/" + fileId).encode().build().toUriString();
            byte[] bytes = MicroserviceExchange.getFileNoAuth(rest, endpoint);
            FileNameSizeDto filename = getFileInfo(rest, fileId);
            if (Objects.isNull(filename.getContentType())) {
                filename.setContentType("application/octet-stream"); // Default if not found
            }
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename.getFilename() + "\"")
                    .contentType(MediaType.parseMediaType(filename.getContentType()))
                    .body(bytes);
        } catch (Exception e) {
            throw new DigoHttpException(11001, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public ResponseEntity<Object> downloadAttachmentByUuid(RestTemplate rest, String fileId, String uuid) {

        try {
            String endpoint = microservice.filemanUri("file/download/" + fileId + "?uuid=" + uuid).encode().build().toUriString();
            byte[] bytes = MicroserviceExchange.getFileNoAuth(rest, endpoint);
            FileNameSizeDto filename = getFileInfo(rest, fileId);
            String fileName = filename.getFilename();
            if (Objects.isNull(filename.getContentType())) {
                filename.setContentType("application/octet-stream"); // Default if not found
            }
            try{
                fileName = Normalizer.normalize(filename.getFilename(), Normalizer.Form.NFD);
            }catch(Exception ex){
                throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
            }
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .contentType(MediaType.parseMediaType(filename.getContentType()))
                    .body(bytes);
        } catch (Exception e) {
            throw new DigoHttpException(11001, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public NpsDossierDetailDto searchDossierByCode(RestTemplate rest, String code){
        String endpoint = microservice.padmanUri("dossier/" + code + "/--by-code").encode().build().toUriString();
        NpsDossierDetailDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, NpsDossierDetailDto.class);
        return ret;
    }

    public AffectedRowsDto updateSync(IntegratedConfigurationDto config, ObjectId id, PadPApplyDto.SyncWithNoteDto SyncDto) {
        try {
            RestTemplate rest = getOAuth2RestTemplate(config);
            String endpoint = microservice.padmanUri("/dossier/" + id.toHexString() + "/--update-sync").toUriString();
            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, SyncDto, AffectedRowsDto.class);
            return ret;
        } catch (Exception e) {
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }


    private RestTemplate getOAuth2RestTemplate(IntegratedConfigurationDto config) {
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(config.getParametersValue("sso-url"));
        details.setClientId(config.getParametersValue("client-id"));
        details.setClientSecret(config.getParametersValue("client-secret"));
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }
    
    RestTemplate getOAuth2RestTemplateLGSP(IntegratedConfigurationDto config) {
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
         details.setAccessTokenUri(config.getParametersValue("sso-uri"));
        details.setClientId(config.getParametersValue("client_id"));
        details.setClientSecret(config.getParametersValue("client_secret"));
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }

    public AffectedRowsDto updateSync(RestTemplate rest, ObjectId id, PadPApplyDto.SyncWithNoteDto SyncDto) {
        try {
            String endpoint = microservice.padmanUri("/dossier/" + id.toHexString() + "/--update-sync").toUriString();
            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, SyncDto, AffectedRowsDto.class);
            return ret;
        } catch (Exception e) {
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public AffectedRowsDto updateSync(RestTemplate rest, ObjectId id, PadPApplyDto.SyncWithNoteLevelDto SyncDto) {
        try {
            String endpoint = microservice.padmanUri("/dossier/" + id.toHexString() + "/--update-sync").toUriString();
            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, SyncDto, AffectedRowsDto.class);
            return ret;
        } catch (Exception e) {
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public AffectedRowsDto updateSyncHPG(RestTemplate rest, ObjectId id, PadPApplyDto.SyncWithNoteHPGDto SyncDto) {
        try {
            String endpoint = microservice.padmanUri("/dossier/" + id.toHexString() + "/--update-sync-HPG").toUriString();
            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, SyncDto, AffectedRowsDto.class);
            return ret;
        } catch (Exception e) {
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    public DossierSyncLogDto getSyncLog(RestTemplate rest, ObjectId id)
    {
          try{
              String endpoint = microservice.padmanUri("/dossier/" + id.toHexString() + "/--get-sync-log-dossier").toUriString();
              //String endpoint = "http://localhost:8081/dossier/" + id.toHexString() + "/--get-sync-log-dossier";
              DossierSyncLogDto res = MicroserviceExchange.getNoAuth(rest, endpoint, DossierSyncLogDto.class);
              return res;
          }catch (Exception e){
                 throw  new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
          }
    }
    
    public AffectedRowsDto updateSyncHCM(RestTemplate rest, ObjectId id, PadPApplyDto.InputSyncWithNoteDto inputSyncDto) {
        try {
            String endpoint = microservice.padmanUri("/dossier/" + id.toHexString() + "/--update-sync-hcm").toUriString();
            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, inputSyncDto, AffectedRowsDto.class);
            return ret;
        } catch (Exception e) {
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    public AffectedRowsDto updateSyncLog(RestTemplate rest, ObjectId id, PutSyncDossierLogDto syncDto) {
        try {
            String endpoint = microservice.padmanUri("/sync-log-dossier/--update-log").toUriString();
            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, syncDto, AffectedRowsDto.class);
            return ret;
        } catch (Exception e) {
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    public AffectedRowsDto updateSyncHCMLGSP(RestTemplate rest, ObjectId id, PadPApplyDto.SyncWithNoteDto SyncDto) {
        try {
            logger.info("Start updateSyncHCMLGSP");
//            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("/dossier/" + id.toHexString() + "/--update-sync-lgsphcm").toUriString();
//            String endpoint = "http://localhost:8081/dossier/" + id.toHexString() + "/--update-sync-lgsphcm";
            logger.info("updateSyncHCMLGSP endpoint " + endpoint);
            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, SyncDto, AffectedRowsDto.class);
            return ret;
        } catch (Exception e) {
            logger.info("Exception: " + e.getMessage());
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    public DossierSyncLogDto getExtendSyncLog(RestTemplate rest, ObjectId id, Boolean isLGSPHCM)
    {
        try{
            String endpoint = microservice.padmanUri("/dossier/" + id.toHexString() + "/--get-extend-sync-log-dossier?isLGSPHCM="+ isLGSPHCM).toUriString();
            //String endpoint = "http://localhost:8081/dossier/" + id.toHexString() + "/--get-extend-sync-log-dossier?isLGSPHCM=" + isLGSPHCM;
            DossierSyncLogDto res = MicroserviceExchange.getNoAuth(rest, endpoint, DossierSyncLogDto.class);
            return res;
        }catch (Exception e){
            throw  new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }


    public HTTPAgencyFullAddressDto getAgencyFullAdressByCode(IntegratedConfigurationDto config, String code){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.basedataUri("agency/" + code + "/--full-address").encode().build().toUriString();
        HTTPAgencyFullAddressDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, HTTPAgencyFullAddressDto.class);
        return ret;
    }
    
    public HCMLGSPDossierDto1 getDossierByCodeHCM(IntegratedConfigurationDto config, String code){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" + code + "/--by-code").encode().build().toUriString();
        HCMLGSPDossierDto1 ret = MicroserviceExchange.getNoAuth(rest, endpoint, HCMLGSPDossierDto1.class);
        return ret;
    }
    
    public AffectedMessageDto updateStatusDossierLLTPHCM(IntegratedConfigurationDto config, String id, LLTPUpdateStatusDto inputDto) {
        try {
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/" + id + "/--lltp-lgsp-hcm-status").encode().build().toUriString();
            AffectedMessageDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, inputDto, AffectedMessageDto.class);
            return ret;
        } catch (Exception e) {
            logger.error("updateStatusDossierLLTPHCM error: " + e);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    public AffectedMessageDto updateStatusDossierLLTPHCMCode(IntegratedConfigurationDto config, String code, LLTPUpdateStatusDto inputDto) {
        try {
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/" + code + "/--lltp-lgsp-hcm-status-code").encode().build().toUriString();
            AffectedMessageDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, inputDto, AffectedMessageDto.class);
            return ret;
        } catch (Exception e) {
            logger.error("updateStatusDossierLLTPHCM error: " + e);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    public List<LLTPDossierSendFailDto> getListLttpSendFail(IntegratedConfigurationDto config, int count){
         try{
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/--lltp-lgsp-hcm-send-fail-list?count=" + count).encode().build().toUriString();

            String json = MicroserviceExchange.getNoAuth(rest, endpoint, String.class);
            ObjectMapper mapper = new ObjectMapper();
            List<LLTPDossierSendFailDto> ret = mapper.readValue(json, new TypeReference<List<LLTPDossierSendFailDto>>(){});
            return ret;
        }catch (Exception ex){
            return null;
        }
    }
    
    public String putVNPostStatusHCMLGSP(IntegratedConfigurationDto config, String dossierId, UpdateVNPostStatus inputDto){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" + dossierId + "/vnpost/--vnpost-status").encode().build().toUriString();
//        endpoint = "http://localhost:8081/dossier/655d9d5d8052461727d9963b/vnpost/--vnpost-status";
        String ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, inputDto, String.class);
        return ret;
    }
    
    public String postDossiersVNPostHCMLGSP(IntegratedConfigurationDto config, List<Object> inputDto){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/--list-dossier-vnpost").encode().build().toUriString();
        String ret = MicroserviceExchange.postJsonNoAuth(rest, endpoint, inputDto, String.class);
        return ret;
    }

    public String postDossiersVNPostHCMLGSPByCode(IntegratedConfigurationDto config, List<String> inputDto){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/--list-dossier-vnpost-by-code").encode().build().toUriString();
        //String endpoint = "http://localhost:8081/dossier/--list-dossier-vnpost-by-code";
        String ret = MicroserviceExchange.postJsonNoAuth(rest, endpoint, inputDto, String.class);
        return ret;
    }
    
    public String returnVNPostStatusHCMLGSP(IntegratedConfigurationDto config, String dossierId, UpdateVNPostStatus inputDto){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" + dossierId + "/return/--vnpost-status").encode().build().toUriString();
        String ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, inputDto, String.class);
        return ret;
    }
    
    public String newVNPostStatusHCMLGSP(IntegratedConfigurationDto config, String dossierId, UpdateVNPostStatus inputDto){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" + dossierId + "/new/--vnpost-status").encode().build().toUriString();
        String ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, inputDto, String.class);
        return ret;
    }


    public String getMappingIdAgency(IntegratedConfigurationDto config, String code){
//      String urlGetMaDonVi = "http://localhost:8080/mapping-data?data-type=5fc0707a62681a8bef000018&source-id=62174f6e378b3c2a75639ff4";
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint =  microservice.adapterUri("/mapping-data?data-type=5fc0707a62681a8bef000018&source-id="+ code).toUriString();
        logger.info("HCMLGSP-HTTP Get MappingIdAgency config" + rest + ", endpoint: " + endpoint);
        MappingDataDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, MappingDataDto.class);
        String mappingIdAgency  = ret.getDest().getId();
    
        return mappingIdAgency;
    }
    
    public AffectedRowsDto updateDossierStatusHTTP(IntegratedConfigurationDto config, String id,  HTTPUpdateStatusDto input) {
        try {
            logger.info("HCMLGSP-HTTP updateDossierStatusHTTP id:" + id + ", input: " + input);
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("/dossier/" + id + "/--http-lgsp-hcm-status").toUriString();
            logger.info("HCMLGSP-HTTP updateDossierStatusHTTP endpoint: " + endpoint );
            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, input, AffectedRowsDto.class);
            return ret;
        } catch (Exception e) {
            logger.info("HCMLGSP-HTTP updateDossierStatusHTTP endpoint erro: " + e);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public String getProvinceCode(String placeId){
        try {
            return placeId.substring(placeId.length() - 2);
        } catch (Exception e) {
            return null;
        }
    }

    public String getDistrictCode(String placeId){
        try {
            return placeId.substring(placeId.length() - 3);
        } catch (Exception e) {
            return null;
        }
    }

    public String getWardCode(String placeId){
        try {
            return placeId.substring(placeId.length() - 5);
        } catch (Exception e) {
            return null;
        }
    }

    public PlaceDetailDto getProvinceByCode(String code){
        String provinceId = "5def47c5f47614018c0000" + code;
        return getPlaceById(provinceId);
    }

    public PlaceDetailDto getDistrictByCode(String code){
        String districtId = "5def47c5f47614018c001" + code;
        return getPlaceById(districtId);
    }

    public PlaceDetailDto getWardByCode(String code){
        String wardId = "5def47c5f47614018c1" + code;
        return getPlaceById(wardId);
    }

    public PlaceDetailDto getPlaceById(String id){
        String placeUrl = microservice.basedataUri("/place/").toUriString() + id;
        return MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), placeUrl, PlaceDetailDto.class);
    }

    public DBNDossierApplyOnlineDto.AgencyDto getAgencyNameCodeByAdministrativeUnitCode(String administrativeUnitCode) {
        try {
//            String getAgencyFullyUri = "http://localhost:8080" + "/agency/name+code+parent+ancestor/--fully-by-administrativeUnitCode?administrativeUnitCode=" + administrativeUnitCode + "&province-code=" + provinceCode;
            String getAgencyFullyUri = microservice.basedataUri("/agency/name+code+parent+ancestor/--fully-by-administrativeUnitCode?administrativeUnitCode=" + administrativeUnitCode + "&province-code=")
                    .toUriString();
            return MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), getAgencyFullyUri, DBNDossierApplyOnlineDto.AgencyDto.class);
        } catch (Exception e) {
            logger.info("getAgencyNameCodeByAdministrativeUnitCode lỗi " + e.getMessage());
            return null;
        }
    }

    public DBNDossierApplyOnlineDto.ProcessDto getProcessByProcedureIdAndAgency(String procedureId, String agencyId) {
        String shortUriProcess = "procedure-process-definition/--by-procedure-and-applied-agency?procedure-id=" + procedureId + "&applied-agency-id=" + agencyId;
//        String uriBuilderProcess = "http://localhost:8069/" + shortUriProcess;
        String uriBuilderProcess = microservice.basepadUri(shortUriProcess).toUriString();
        
        DBNDossierApplyOnlineDto.ProcessDto processDefinition = null;
        try {
            DBNDossierApplyOnlineDto.ProcessDto[] processDtoList = MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), uriBuilderProcess, DBNDossierApplyOnlineDto.ProcessDto[].class);

            List<DBNDossierApplyOnlineDto.ProcessDto> processDefinitionArrayList = List.of(processDtoList);
            if (!processDefinitionArrayList.isEmpty()) {
                processDefinition = processDefinitionArrayList.get(0);
            }
        } catch (Exception e) {
            logger.info("DIGO-Info: " + e.getMessage());
        }
        return processDefinition;
    }

    public PlaceGsoDto getPlaceByAgencyId(String agencyId) {
        String placeUrl = microservice.basedataUri("/agency/").toUriString() + agencyId;
        AgencyAddressPlaceDto dto = null;
        try {
            dto = MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), placeUrl, AgencyAddressPlaceDto.class);
        } catch (Exception e) {
            logger.error("getPlaceByAgencyId: " + e.getMessage());
        }
        if(dto == null || dto.getAddress() == null || dto.getAddress().getPlace() == null) {
            return null;
        }
        AgencyAddressPlaceDto.Place place = dto.getAddress().getPlace();
        String placeId = dto.getAddress().getPlace().getId();
        int level = -1;
        if(dto.getLevel() != null && dto.getLevel().getId() != null) {
            String levelId = dto.getLevel().getId();
            switch (levelId) {
                case "5f39f4335224cf235e134c5b":
                    level = 1;
                    break;
                case "5f39f4155224cf235e134c59":
                    level = 2;
                    break;
                case "5febfe2295002b5c79f0fc9f":
                    level = 3;
                    break;
            }
        }
        List<String> ancestors = new ArrayList<>();
        if(place.getAncestor() != null) {
            for(AgencyAddressPlaceDto.Id id : place.getAncestor()) {
                ancestors.add(id.getId());
            }
        }
        for(String id : ancestors) {
            if(level == 1 && id.startsWith("5def47c5f47614018c0000")) {
                placeId = id;
                break;
            }
            if(level == 2 && id.startsWith("5def47c5f47614018c001")) {
                placeId = id;
                break;
            }
            if(level == 3 && id.startsWith("5def47c5f47614018c1")) {
                placeId = id;
                break;
            }
        }
        return new PlaceGsoDto(placeId, ancestors);
    }
    
    public AffectedRowsDto updateDossierStatusHTTPLTKH(IntegratedConfigurationDto config, String id,  HTTPUpdateStatusDto input) {
        try {
            logger.info("HCMLGSP-HTTP updateDossierStatusHTTPLTKH id:" + id + ", input: " + input);
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("/dossier/" + id + "/--http-lgsp-hcm-status-ltkh").toUriString();
            logger.info("HCMLGSP-HTTP updateDossierStatusHTTPLTKH endpoint: " + endpoint );
            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, input, AffectedRowsDto.class);
            logger.info("HCMLGSP-HTTP updateDossierStatusHTTPLTKH retCallPadman: " + ret );
            return ret;
        } catch (Exception e) {
           logger.info("HCMLGSP-HTTP updateDossierStatusHTTP endpoint erro: " + e);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    public List<HTTPDossierLTKH> getListDossierHTTPLTKHHCMLGSP (IntegratedConfigurationDto config) {
        try {
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("/dossier/--http-lgsp-hcm-list-ltkh").toUriString();
            logger.info("HCMLGSP-HTTP ListDossierHTTPLTKH endpoint: " + endpoint );
            String json = MicroserviceExchange.getNoAuth(rest, endpoint, String.class);
            logger.info("HCMLGSP-HTTP ListDossierHTTPLTKH jsonRetCallListHTTPLTKH: " + json );
            ObjectMapper mapper = new ObjectMapper();
            List<HTTPDossierLTKH> ret = mapper.readValue(json, new TypeReference<List<HTTPDossierLTKH>>(){});
            logger.info("HCMLGSP-HTTP ListDossierHTTPLTKH listRetCallListHTTPLTKH: " + ret );
            return ret;
        }catch (Exception ex) {
            logger.info("HCMLGSP-HTTP updateDossierStatusHTTP endpoint erro: " + ex);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    public ILisLogResDto getAgencyDossierByCode(RestTemplate rest, String code){
        String endpoint = microservice.padmanUri("dossier/" + code + "/--by-code").encode().build().toUriString();
        ILisLogResDto ret = MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), endpoint, ILisLogResDto.class);
        return ret;
    }
    
    public DossierSyncExtendResponseDto[] getDossierSyncExtend(IntegratedConfigurationDto config, Boolean isLGSPHCM){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/--get-dossier-sync-extend?is-lgsphcm="+ isLGSPHCM).encode().build().toUriString();
//        String endpoint = "http://localhost:8081/dossier/--get-dossier-sync-extend?is-lgsphcm=" + isLGSPHCM;
        DossierSyncExtendResponseDto[] ret = MicroserviceExchange.getNoAuth(rest, endpoint, DossierSyncExtendResponseDto[].class);
        return ret;
    }

    public HGILGSPDossierLLTPDto_v2 getDossierLLTPHGILGSP(IntegratedConfigurationDto config, ObjectId id){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" +id.toHexString() + "/--online").encode().build().toUriString();
        HGILGSPDossierLLTPDto_v2 ret = MicroserviceExchange.getNoAuth(rest, endpoint, HGILGSPDossierLLTPDto_v2.class);
        return ret;
    }

    // Lấy id hồ sơ theo mã hồ sơ
    public LLTPDossierSendFailDto getDossierIdByCode(IntegratedConfigurationDto config, String code){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" + code + "/--by-code").encode().build().toUriString();
        LLTPDossierSendFailDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, LLTPDossierSendFailDto.class);
        return ret;
    }

    // Lấy danh sách hồ sơ gửi thành công
    public List<LLTPDossierSendFailDto> getListDossierSuccess(IntegratedConfigurationDto config) {
        try {
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/--list-dossier-send-success").encode().build().toUriString();

            String json = MicroserviceExchange.getNoAuth(rest, endpoint, String.class);
            ObjectMapper mapper = new ObjectMapper();
            List<LLTPDossierSendFailDto> ret = mapper.readValue(json, new TypeReference<List<LLTPDossierSendFailDto>>(){});

            logger.info("Successfully retrieved {} items from API endpoint {}", ret.size(), endpoint);

            return ret;
        } catch (Exception ex) {
            logger.error("Error while fetching list of successful dossiers: {}", ex.getMessage(), ex);
            return null;
        }
    }

    public List<LLTPDossierStatusHGI> getListDossierLLTPHGI(IntegratedConfigurationDto config) {
        try {
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/--lltp-lgsp-hgi-list").encode().build().toUriString();
            String json = MicroserviceExchange.getNoAuth(rest, endpoint, String.class);
            ObjectMapper mapper = new ObjectMapper();
            List<LLTPDossierStatusHGI> ret = mapper.readValue(json, new TypeReference<List<LLTPDossierStatusHGI>>(){});
            return ret;
        } catch (Exception e) {
            logger.error("getListDossierLLTPHGI error: " + e);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    public LLTPResultSync updateStatusDossierListHGI(IntegratedConfigurationDto config, List<UpdateStatusDossierDto> inputDtoList) {
        try {
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/--lgsphgi-update-dossier-status-list").encode().build().toUriString();
            LLTPResultSync ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, inputDtoList, LLTPResultSync.class);
            return ret;
        } catch (Exception e) {
            logger.error("updateStatusDossierListHGI error: " + e);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public List<LLTPDossierSendFailDto> getListLttpSendFailHGI(IntegratedConfigurationDto config, int count){
        try{
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/--lltp-lgsp-hcm-send-fail-list-hgi?count=" + count).encode().build().toUriString();
            String json = MicroserviceExchange.getNoAuth(rest, endpoint, String.class);
            ObjectMapper mapper = new ObjectMapper();
            List<LLTPDossierSendFailDto> ret = mapper.readValue(json, new TypeReference<List<LLTPDossierSendFailDto>>(){});
            return ret;
        }catch (Exception ex){
            return null;
        }
    }

    public LLTPResultSync updateAttachmentListHGI(IntegratedConfigurationDto config, List<UpdateStatusDossierDto> inputDtoList) {
        try {
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/--online-list").encode().build().toUriString();
            LLTPResultSync ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, inputDtoList, LLTPResultSync.class);
            return ret;
        } catch (Exception e) {
            logger.error("updateAttachmentListHGI error: " + e);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public FileUResDto[] uploadFileHGI(IntegratedConfigurationDto config, ArrayList<FileInfo> file) {
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String uploadUrl = microservice.filemanUri("/file/--multiple?uuid=1").toUriString();
        MultiValueMap<String, Object> requestMap = new LinkedMultiValueMap<>();
        for (FileInfo item : file) {
            byte[] bytes = Base64.getDecoder().decode(item.getBase64());
            ByteArrayResource byteArrayResource = new ByteArrayResource(bytes) {
                @Override
                public String getFilename() {
                    return item.getTenTepDinhKem();
                }
            };

            requestMap.add("files", byteArrayResource);
        }
        FileUResDto[] lstFile = MicroserviceExchange.postMultipartNoAuth(rest, uploadUrl, requestMap, FileUResDto[].class);
        return lstFile;
    }
    
    public String getMappingIdAgencyMTTDC (IntegratedConfigurationDto config, String agencyId) {
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        if (!agencyId.equalsIgnoreCase("")) {
            String endpoint =  microservice.adapterUri("/mapping-data?data-type=66cd9e6d9b39e99355816ef5&source-id="+ agencyId).toUriString();
            MappingDataDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, MappingDataDto.class);
            String mappingMTTDC  = ret.getDest().getId();

            return mappingMTTDC;
        } else {
            return null;
        }
    }


    public CMULGSPDossierLLTPDto getDossierLLTPCMULGSP(IntegratedConfigurationDto config, ObjectId id){
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String endpoint = microservice.padmanUri("dossier/" +id.toHexString() + "/--online").encode().build().toUriString();
//        String endpoint = "http://localhost:8081/dossier/" +id.toHexString() + "/--online";
        CMULGSPDossierLLTPDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, CMULGSPDossierLLTPDto.class);
        return ret;
    }

    public List<LLTPDossierStatusCMU> getListDossierLLTPCMU(IntegratedConfigurationDto config) {
        try {
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/--lltp-lgsp-hgi-list").encode().build().toUriString();
            String json = MicroserviceExchange.getNoAuth(rest, endpoint, String.class);
            ObjectMapper mapper = new ObjectMapper();
            List<LLTPDossierStatusCMU> ret = mapper.readValue(json, new TypeReference<List<LLTPDossierStatusCMU>>(){});
            return ret;
        } catch (Exception e) {
            logger.error("getListDossierLLTPCMU error: " + e);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    public LLTPResultSync updateStatusDossierListCMU(IntegratedConfigurationDto config, List<UpdateStatusDossierDto> inputDtoList) {
        try {
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/--lgsphgi-update-dossier-status-list").encode().build().toUriString();
            LLTPResultSync ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, inputDtoList, LLTPResultSync.class);
            return ret;
        } catch (Exception e) {
            logger.error("updateStatusDossierListHGI error: " + e);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public List<LLTPDossierSendFailDto> getListLttpSendFailCMU(IntegratedConfigurationDto config, int count){
        try{
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/--lltp-lgsp-hcm-send-fail-list-hgi?count=" + count).encode().build().toUriString();
            String json = MicroserviceExchange.getNoAuth(rest, endpoint, String.class);
            ObjectMapper mapper = new ObjectMapper();
            List<LLTPDossierSendFailDto> ret = mapper.readValue(json, new TypeReference<List<LLTPDossierSendFailDto>>(){});
            return ret;
        }catch (Exception ex){
            return null;
        }
    }

    public LLTPResultSync updateAttachmentListCMU(IntegratedConfigurationDto config, List<UpdateStatusDossierDto> inputDtoList) {
        try {
            RestTemplate rest = getOAuth2RestTemplateLGSP(config);
            String endpoint = microservice.padmanUri("dossier/--online-list").encode().build().toUriString();
            LLTPResultSync ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, inputDtoList, LLTPResultSync.class);
            return ret;
        } catch (Exception e) {
            logger.error("updateAttachmentListCMU error: " + e);
            throw new DigoHttpException(10009, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public FileUResDto[] uploadFileCMU(IntegratedConfigurationDto config, ArrayList<FileInfo> file) {
        RestTemplate rest = getOAuth2RestTemplateLGSP(config);
        String uploadUrl = microservice.filemanUri("/file/--multiple?uuid=1").toUriString();
        MultiValueMap<String, Object> requestMap = new LinkedMultiValueMap<>();
        for (FileInfo item : file) {
            byte[] bytes = Base64.getDecoder().decode(item.getBase64());
            ByteArrayResource byteArrayResource = new ByteArrayResource(bytes) {
                @Override
                public String getFilename() {
                    return item.getTenTepDinhKem();
                }
            };

            requestMap.add("files", byteArrayResource);
        }
        FileUResDto[] lstFile = MicroserviceExchange.postMultipartNoAuth(rest, uploadUrl, requestMap, FileUResDto[].class);
        return lstFile;
    }

    public TechIdProviderDto getTechId(RestTemplate rest, String identityNumber, ObjectId userId){
        String endpoint = microservice.humanUri("user/--get-tech-id?identity-number=" + identityNumber + "&user-id=" + userId).encode().build().toUriString();
        TechIdProviderDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, TechIdProviderDto.class);
        return ret;
    }

    public Object updateTechId(RestTemplate rest, String identityNumber, ObjectId userId, String techId){
        try{
            if (!StringHelper.hasValue(identityNumber) && !StringHelper.hasValue(techId)) {
                return null;
            }
            String endpoint = microservice.humanUri("user/--update-tech-id?identity-number=" + identityNumber + "&user-id=" + userId + "&technical-id=" + techId).encode().build().toUriString();
            Object ret = MicroserviceExchange.putNoAuthNoBody(rest, endpoint, Object.class);
            logger.info("Hàm updateTechId, kết quả trả về: " + ret.toString());
            return ret;
        } catch(Exception ex){
            logger.info("Hàm updateTechId, lỗi: " + ex.getMessage());
            return null;
        }
    }

    public TechIdProviderDto getTaxCodeByUserId(RestTemplate rest, ObjectId userId){
        if (Objects.isNull(userId)) {
            return null;
        }
        String endpoint = microservice.humanUri("user/--get-tax-code?user-id=" + userId).encode().build().toUriString();
        TechIdProviderDto ret = MicroserviceExchange.getNoAuth(rest, endpoint, TechIdProviderDto.class);
        return ret;
    }
}
