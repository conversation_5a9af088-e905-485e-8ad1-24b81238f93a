/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.api.IpccVoiceController;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.GettingConfigurationParamsDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.IpccGetRecordParamsDto;
import vn.vnpt.digo.adapter.dto.UbmttqAddReqDto;
import vn.vnpt.digo.adapter.dto.UbmttqAddResDto;
import vn.vnpt.digo.adapter.dto.UbmttqErrDto;
import vn.vnpt.digo.adapter.dto.UbmttqResultReqDto;
import vn.vnpt.digo.adapter.dto.UbmttqTokenReqDto;
import vn.vnpt.digo.adapter.dto.UbmttqTokenResDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.Result;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

/**
 *
 * <AUTHOR>
 */
@Service
public class UbmttqService {

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private IntegratedConfigurationService configurationService;

    Logger logger = LoggerFactory.getLogger(UbmttqService.class);

    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890024");
    
    private final Gson gson = new Gson();

    private String getToken(IntegratedConfigurationDto config) {
        UbmttqTokenReqDto body = new UbmttqTokenReqDto(
                config.getParametersValue("username").toString(),
                config.getParametersValue("password").toString()
        );
        String endpoint = config.getParametersValue("gateway").toString() + config.getParametersValue("login-path").toString();
        UbmttqTokenResDto ret = MicroserviceExchange.postJsonNoAuth(restTemplate, endpoint, body, UbmttqTokenResDto.class);
        logger.info(ret.toString());
        return ret.getToken();
    }

    public AffectedRowsDto addTicket(UbmttqAddReqDto body) {
        //Get config
        AffectedRowsDto ret = new AffectedRowsDto();
        IntegratedConfigurationDto config;
        String back = "";
        if (Objects.nonNull(body.getConfigId())) {
            config = configurationService.getConfig(body.getConfigId());
        } else {
            config = configurationService.getConfig(body.getAgencyId(), body.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        try {
            String token = this.getToken(config);
            String endpoint = config.getParametersValue("gateway").toString() + config.getParametersValue("add-ticket-path").toString();
            back = MicroserviceExchange.postUbmttqNoAuth(restTemplate, token, endpoint, body, String.class);
            UbmttqAddResDto res = gson.fromJson(back, UbmttqAddResDto.class);
            if(Objects.nonNull(res)){
                logger.info(res.toString());
                if(res.getResult() == 0){
                    ret.setAffectedRows(1);
                }
            }
            return ret;
        } catch (Exception e) {
            UbmttqErrDto res = gson.fromJson(back, UbmttqErrDto.class);
            throw new DigoHttpException(11003, new String[]{ res.getErrorMessage() }, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    public AffectedRowsDto updateResult(GettingConfigurationParamsDto params, UbmttqResultReqDto body) {
        //Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        try {
            logger.info("ubmttq said: " + body.getStatus());
            Result req = new Result();
            if(Objects.equals(body.getStatus(), "S")){
                req.setContent(body.getNote());
            } else {
                req.setContent(body.getReason());
                req.setUbmttqResult(false);
            }
            req.setDone(true);
            String endpoint = microservice.petitionUri("/petition/" + body.getTransactionId() + "/--update-result").toUriString();
            AffectedRowsDto ret = MicroserviceExchange.putJson(restTemplate, endpoint, req, AffectedRowsDto.class);
            return ret;
        } catch (Exception e) {
            throw new DigoHttpException(10009, new String[]{translator.toLocale("lang.word.ticket")}, HttpServletResponse.SC_NOT_FOUND);
        }
    }
}
