package vn.vnpt.digo.adapter.service;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.IntegrationParamsDto;
import vn.vnpt.digo.adapter.dto.zalo.ZaloKtmSendMessageReqDto;
import vn.vnpt.digo.adapter.dto.zalo.ZaloTemplateKtmDto;
import vn.vnpt.digo.adapter.dto.zalo.ZaloV2SendMessageResDto;
import vn.vnpt.digo.adapter.dto.zalo.ZaloV2TokenResDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.ListType;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Objects;
import java.util.Date;
import java.util.Calendar;
import java.util.TimeZone;


@Service
public class ZaloKTMService {
    @Autowired
    private Translator translator;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private Microservice microservice;

    @Value(value = "${digo.oidc.client-id}")
    private String clientId;

    @Value(value = "${digo.oidc.client-secret}")
    private String clientSecret;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;

    private static RestTemplate restTemplate = new RestTemplate();

    private ObjectId serviceId = new ObjectId("5f7c16069abb62f511890027");

    private final String getSendMessageUrl = "oa/message";
    private final String getProfileUrl = "oa/getprofile?data={data}";

    Logger logger = LoggerFactory.getLogger(ZaloV2Service.class);
    public String postDossierByCode(String userId, String code, IntegrationParamsDto params) throws JSONException, ParseException {

        String dossierCode = code;

        restTemplate = this.getRestTemplate();

        //call dossier with code to padman
        //String getDossierUrl = "http://localhost:808/dossier/" + dossierCode.toString()+ "/--by-code";
        String getDossierUrl = "/dossier/" + dossierCode.toString()+ "/--by-code";
        UriComponentsBuilder uriBuilder = microservice.padmanUri(getDossierUrl);
        //String getDossierUrl = "dossier/" + dossierCode+ "/--public";
        String dossierJson = MicroserviceExchange.getPadmanKtm(restTemplate, uriBuilder.toUriString(), String.class);
        JSONObject dossierJsonObject = new JSONObject(dossierJson);


        //set data to dto
        JSONObject aplicant =  dossierJsonObject.getJSONObject("applicant");
        JSONObject data =  aplicant.getJSONObject("data");
        var applicantName = data.get("fullname");
        var dossierCodes = dossierJsonObject.get("code");

        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
        System.out.println("check log format dateTime");
        var acceptedDate = dossierJsonObject.get("acceptedDate");
        Date tempAccepted = df.parse(acceptedDate.toString());
        var formattedAcceptedDate = formatDateToString(tempAccepted, "HH:mm:ss dd/MM/yyyy", "Asia/Ho_Chi_Minh");
        System.out.println(tempAccepted);
        System.out.println("change timezone :"+ formattedAcceptedDate);

        Object appointmentDate;
        String formattedAppointmentDate = " ";
        if(dossierJsonObject.has("appointmentDate")){
            appointmentDate = dossierJsonObject.get("appointmentDate");
            String beforeConvertAppointment = appointmentDate.toString();
            Date tempAppointment = df.parse(beforeConvertAppointment);
            formattedAppointmentDate =  formatDateToString(tempAppointment, "HH:mm:ss dd/MM/yyyy", "Asia/Ho_Chi_Minh");;
        }


        JSONObject receivingPlace =  dossierJsonObject.getJSONObject("receivingPlace");
        Object receivingAddress;
        if(receivingPlace.has("fullAddress")){
            receivingAddress = receivingPlace.get("fullAddress");
        }else {
            receivingAddress = "Nhận Trực tiếp tại bộ phận Tiếp nhận và Trả kết quả";
        }

        JSONObject dossierStatus = dossierJsonObject.getJSONObject("dossierStatus");
        JSONArray dossierStatusArray = dossierStatus.getJSONArray("name");
        String status = dossierStatusArray.getJSONObject(0).get("name").toString();

        JSONObject procedure =  dossierJsonObject.getJSONObject("procedure");
        JSONArray translateArray = procedure.getJSONArray("translate");
        String procedureName = translateArray.getJSONObject(0).get("name").toString();


        JSONObject agency =  dossierJsonObject.getJSONObject("agency");
        JSONObject parent =  agency.getJSONObject("parent");
        JSONArray parentArray = parent.getJSONArray("name");
        String agencyName = parentArray.getJSONObject(0).get("name").toString();

        //set data to template
        ZaloTemplateKtmDto templateObj =  new ZaloTemplateKtmDto();
        templateObj.setCode(dossierCodes.toString());
        templateObj.setFullname(applicantName.toString());
        templateObj.setAcceptedDate(formattedAcceptedDate);
        templateObj.setReturnedDate(formattedAppointmentDate);
        templateObj.setDossierStatus(status);
        templateObj.setProcedureName(procedureName);
        templateObj.setAgencyName(agencyName);
        templateObj.setReceivingPlace(receivingAddress.toString());
        sendTemplate(userId,templateObj, params);
        return dossierJson;
    }

    //send Template to zalo
    public ZaloV2SendMessageResDto sendTemplate(String user_id, ZaloTemplateKtmDto body, IntegrationParamsDto params) {
        ZaloV2SendMessageResDto ret = null;
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String token = refreshToken(config);
        try {
            String endpoint = "https://openapi.zalo.me/v2.0/oa/message";
            // send message to zalo
            ZaloKtmSendMessageReqDto req = new ZaloKtmSendMessageReqDto(user_id, body);
            ret = MicroserviceExchange.postToZalo(restTemplate, endpoint, token, req, ZaloV2SendMessageResDto.class);
            return ret;
        } catch (Exception e) {
            logger.debug(e.getMessage());
            throw new DigoHttpException(11003, new String[] { e.getMessage() }, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private String refreshToken(IntegratedConfigurationDto config){
        String expiredDateString = config.getParametersValue("token-expire").toString();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
        Date current = new Date();
        Date expiredDate = current;
        if(!Objects.equals(expiredDateString, "")){
            try {
                expiredDate =  dateFormat.parse(expiredDateString);
            } catch (Exception e){
                logger.info("ZaloV2-refreshToken: failed ex " + e.getMessage());
            }
        } else {
            Calendar x = Calendar.getInstance();
            x.setTime(current);
            x.add(Calendar.HOUR, - 1);
            expiredDate = x.getTime();
        }
        if(current.after(expiredDate)){
            //refreshToken
            try{
                String endpoint = config.getParametersValue("token-api").toString();
                MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
                body.add("refresh_token", config.getParametersValue("refresh-token").toString());
                body.add("app_id", config.getParametersValue("app-id").toString());
                body.add("grant_type", "refresh_token");
                ZaloV2TokenResDto ret = MicroserviceExchange.getZaloToken(restTemplate, endpoint, config.getParametersValue("secret-key").toString(), body);
                IntegratedConfiguration rewConfig = configurationService.getById(config.getId());
                rewConfig.setParametersValue("refresh-token", ret.getRefresh_token(),  ListType.STRING);
                Calendar c = Calendar.getInstance();
                c.setTime(current);
                c.add(Calendar.HOUR, config.getParametersValue("token-expire-offset"));
                rewConfig.setParametersValue("token-expire", dateFormat.format(c.getTime()),  ListType.STRING);
                rewConfig.setParametersValue("token", ret.getAccess_token(),  ListType.STRING);
                configurationService.save(rewConfig);
                return ret.getAccess_token();
            } catch (Exception e){
                return config.getParametersValue("token").toString();
            }
        }
        return config.getParametersValue("token").toString();
    }

    /**
     * Utility function to convert java Date to TimeZone format
     *
     * @param date
     * @param format
     * @param timeZone
     * @return
     */
    public static String formatDateToString(
            Date date, String format,
            String timeZone
    ) {
        // null check
        if (date == null) return null;
        // create SimpleDateFormat object with input format
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        // default system timezone if passed null or empty
        if (timeZone == null || "".equalsIgnoreCase(timeZone.trim())) {
            timeZone = Calendar.getInstance().getTimeZone().getID();
        }
        // set timezone to SimpleDateFormat
        sdf.setTimeZone(TimeZone.getTimeZone(timeZone));
        // return Date in required format with timezone as String
        return sdf.format(date);
    }

    private RestTemplate getRestTemplate(){
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(this.tokenUrl + "/protocol/openid-connect/token");
        details.setClientId(this.clientId);
        details.setClientSecret(this.clientSecret);
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }

}
