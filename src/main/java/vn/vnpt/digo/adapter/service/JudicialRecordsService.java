/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.HashMap;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.event_log.PostEventLogDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LLTPTraTrangThaiHoSoOutputDto;
import vn.vnpt.digo.adapter.dto.lgspqni.StatusCriminalJudicalResponseDto;
import vn.vnpt.digo.adapter.dto.tandan.civil.CivilDossierStatusDto;
import vn.vnpt.digo.adapter.dto.tandan.judicialrecords.JRDossierDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.*;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import vn.vnpt.digo.adapter.document.MappingCategoryInterServices;
import vn.vnpt.digo.adapter.pojo.BodyCategoryMapLLTP;
import vn.vnpt.digo.adapter.pojo.ReponseCategoryMapLLTP;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.JudicialRecordsTokenDto;
import vn.vnpt.digo.adapter.dto.JudicialRecordsDto;
import vn.vnpt.digo.adapter.dto.JudicialRecordsParamsDto;
import vn.vnpt.digo.adapter.dto.JudicialRecordsGetCategoryDto;
import vn.vnpt.digo.adapter.dto.GetListCategoryMapLLTPHTTPDto;
import vn.vnpt.digo.adapter.dto.SidNameDto;
import vn.vnpt.digo.adapter.dto.AffectedMessageDto;
@Service
public class JudicialRecordsService {
    Logger logger = LoggerFactory.getLogger(JudicialRecordsService.class);
    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private EventLogService eventLogService;
    
    @Autowired
    private IntegratedConfigurationService configurationService;
    
    private ObjectId serviceId = new ObjectId("5f7c16069abb62f511890009");

    @Autowired
    private IntegratedLogsService logService;

    @Autowired
    private MappingCategoryInterService mappingCategoryInterService;

    @Autowired
    private MongoTemplate mongoTemplate;
    
    public String getToken(IntegratedConfigurationDto config) {
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(config.getParametersValue("consumer-key"), config.getParametersValue("consumer-secret"));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(headers);
        JudicialRecordsTokenDto result = restTemplate.exchange(config.getParametersValue("gateway-token") + "?grant_type=client_credentials",
                HttpMethod.POST, request, JudicialRecordsTokenDto.class).getBody();
        //Return body
        if (Objects.isNull(result.getAccess_token())) {
            return null;
        }
        return result.getAccess_token();
    }
    
    public JudicialRecordsGetCategoryDto getCategory(JudicialRecordsParamsDto params, BodyCategoryMapLLTP body) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(this.getToken(config));
        headers.add("service-code", "LyLichTuPhap_traDanhMuc");
        
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<String> result = restTemplate.exchange(config.getParametersValue("gateway").toString(), HttpMethod.POST, request, String.class);
        Gson g = new Gson();
        JudicialRecordsGetCategoryDto response = g.fromJson(result.getBody(), JudicialRecordsGetCategoryDto.class);
        if(params.getType() != null && params.getType() == "save")
        {
            MappingCategoryInterServices newS = new MappingCategoryInterServices();
            if(Objects.nonNull(params.getConfigId()))
            {
                newS.setConfigId(params.getConfigId().toString());
            }
            newS.setServiceId(this.serviceId.toString());
            ObjectId deploymentId = Context.getDeploymentId();
            newS.setDeploymentId(deploymentId.toString());
            newS.setListContent(response.getListContent());
            newS.setType(body.getInfoType().toString());
            mappingCategoryInterService.saveCategory(newS.getConfigId(),newS.getServiceId(),newS.getDeploymentId(),newS.getType(),newS.getListContent());
        }
        return response;
    }
    
    public JudicialRecordsDto sendCriminalRecord(JudicialRecordsParamsDto params, Map<String, Object> body, String code) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(this.getTokenLGSP(params).getAccess_token());
        headers.add("service-code", "LyLichTuPhap_nhanHoSoDangKy");
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        logger.info("CALL STATUS TO UPDATE");
        ResponseEntity<String> result = restTemplate.exchange(config.getParametersValue("gateway").toString(), HttpMethod.POST, request, String.class);
        Gson g = new Gson();
        JudicialRecordsDto response = g.fromJson(result.getBody(), JudicialRecordsDto.class);
        try{
            Map<String,String> map = new HashMap<>();
            map.put("statusCode",response.getStatus().toString());
            map.put("statusMessage",response.getDescription());
            logger.info("Status Update Info:" + "-----------------"+ map.toString());
            updateDossierLLTPStatus(map,code);
        }catch (Exception e){
            logger.info("EXEPTION:" + "-----------------"+ e.getMessage());
        }
        return response;
    }
    
    public ArrayList<GetListCategoryMapLLTPHTTPDto> getListCategory(String infoType,JudicialRecordsParamsDto params) {
        ArrayList<GetListCategoryMapLLTPHTTPDto> newArr = new ArrayList<GetListCategoryMapLLTPHTTPDto>();
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(this.getToken(config));
        headers.add("service-code", "LyLichTuPhap_traDanhMuc");
        BodyCategoryMapLLTP body = new BodyCategoryMapLLTP();
        body.setInfoType(Integer.parseInt(infoType));
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<String> result = restTemplate.exchange(config.getParametersValue("gateway").toString(), HttpMethod.POST, request, String.class);
        Gson g = new Gson();
        ReponseCategoryMapLLTP response = g.fromJson(result.getBody(), ReponseCategoryMapLLTP.class);
        if(response.getListContent() != null)
        {
            GetListCategoryMapLLTPHTTPDto[] list = g.fromJson(response.getListContent(), GetListCategoryMapLLTPHTTPDto[].class);            
            for(GetListCategoryMapLLTPHTTPDto newL: list)
            {
                GetListCategoryMapLLTPHTTPDto res = new GetListCategoryMapLLTPHTTPDto();
                res.setId(newL.getId());
                res.setName(newL.getName());
                newArr.add(res);
            }
        }
        return newArr;
    }
    
    public JudicialRecordsGetCategoryDto getCategoryGet(JudicialRecordsParamsDto params, BodyCategoryMapLLTP body) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(this.getToken(config));
//        headers.add("service-code", "LyLichTuPhap_traDanhMuc");
        
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<String> result = restTemplate.exchange(config.getParametersValue("gateway").toString() + "traDanhMuc", HttpMethod.POST, request, String.class);
        Gson g = new Gson();
        JudicialRecordsGetCategoryDto response = g.fromJson(result.getBody(), JudicialRecordsGetCategoryDto.class);
        if(params.getType() != null && params.getType() == "save")
        {
            MappingCategoryInterServices newS = new MappingCategoryInterServices();
            if(Objects.nonNull(params.getConfigId()))
            {
                newS.setConfigId(params.getConfigId().toString());
            }
            newS.setServiceId(this.serviceId.toString());
            ObjectId deploymentId = Context.getDeploymentId();
            newS.setDeploymentId(deploymentId.toString());
            newS.setListContent(response.getListContent());
            newS.setType(body.getInfoType().toString());
            mappingCategoryInterService.saveCategory(newS.getConfigId(),newS.getServiceId(),newS.getDeploymentId(),newS.getType(),newS.getListContent());
        }
        return response;
    }

    public JudicialRecordsDto sendCriminalRecordGetV2(JudicialRecordsParamsDto params, Map<String, Object> body,String code) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(this.getTokenLGSP(params).getAccess_token());
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        logger.info("---Call api get status-----");
        JudicialRecordsDto response = restTemplate.postForObject(config.getParametersValue("domain").toString() + config.getParametersValue("nhanHoSoDangKy").toString(), request, JudicialRecordsDto.class);
        try{
            Map<String,String> paramsGetStatus =  new HashMap<>();
            ObjectMapper mapper = new ObjectMapper(); // jackson's objectmapper
            JRDossierDto.DeclarationWsForm pojo = mapper.convertValue( body.get("declarationForm"), JRDossierDto.DeclarationWsForm.class);
//            JRDossierDto.DeclarationWsForm  form = (JRDossierDto.DeclarationWsForm) body.get("declarationForm");
            paramsGetStatus.put("idReceivedDec",body.get("idReceivedDec").toString());
            paramsGetStatus.put("identifyNo", pojo.getIdentifyNo());
            paramsGetStatus.put("infoType","2");
            paramsGetStatus.put("configId",params.getConfigId().toString());
            LLTPTraTrangThaiHoSoOutputDto statusRespone= traTrangThaiHoSo(paramsGetStatus);
            Map<String,String> map = new HashMap<>();
            map.put("statusMessage",statusRespone.getDecStatusName());
            map.put("statusCode",statusRespone.getDecStatusId());
            logger.info("---Call  padman update status-----");
            updateDossierLLTPStatus(map,code);
        }catch (Exception e){
            logger.info("EXEPTION:" + "-----------------"+ e.getMessage());
        }
        return response;
    }

    public JudicialRecordsDto sendCriminalRecordGet(JudicialRecordsParamsDto params, Map<String, Object> body,String code) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(this.getTokenLGSP(params).getAccess_token());
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        logger.info("---Call api get status-----");
        JudicialRecordsDto response = restTemplate.postForObject(config.getParametersValue("gateway").toString() + "nhanHoSoDangKy", request, JudicialRecordsDto.class);
        try{
            Map<String,String> paramsGetStatus =  new HashMap<>();
            ObjectMapper mapper = new ObjectMapper(); // jackson's objectmapper
            JRDossierDto.DeclarationWsForm pojo = mapper.convertValue( body.get("declarationForm"), JRDossierDto.DeclarationWsForm.class);
//            JRDossierDto.DeclarationWsForm  form = (JRDossierDto.DeclarationWsForm) body.get("declarationForm");
            paramsGetStatus.put("idReceivedDec",body.get("idReceivedDec").toString());
            paramsGetStatus.put("identifyNo", pojo.getIdentifyNo());
            paramsGetStatus.put("infoType","2");
            paramsGetStatus.put("configId",params.getConfigId().toString());
            LLTPTraTrangThaiHoSoOutputDto statusRespone= traTrangThaiHoSo(paramsGetStatus);
            Map<String,String> map = new HashMap<>();
            map.put("statusMessage",statusRespone.getDecStatusName());
            map.put("statusCode",statusRespone.getDecStatusId());
            logger.info("---Call  padman update status-----");
            updateDossierLLTPStatus(map,code);
        }catch (Exception e){
            logger.info("EXEPTION:" + "-----------------"+ e.getMessage());
        }
        return response;
    }

    public JudicialRecordsDto writeLogLLTPV2(HttpServletRequest request,JudicialRecordsParamsDto params, Map<String, Object> body, String code,ObjectId logID, int type ){
        //region event log init
        PostEventLogDto event = new PostEventLogDto();
        event.setRequestAdapter(request, body,"ad");
        //event.setRes(null);
        event.setPid(logID);
        event.setServiceId(this.serviceId);
        event.setKey( new SidNameDto("Code", code ));
        //endregion event log init
        try{
            JudicialRecordsDto response = new JudicialRecordsDto();
            if(type == 1 ){
                response = sendCriminalRecordGetV2(params, body, code);
            }
            if(type == 2 ){
                response = sendCriminalRecord(params, body, code);
            }
            //region event log success (status)
            event.setStatus(true);
            event.setErrMsg(response.toString());
            eventLogService.addNew(event);
            //endregion event log success (status, message)
            logger.info("DIGO-Response: " + response.toString());
            return response;
        }catch(Exception e){
            //region event log fail (status, message)
            event.setStatus(false);
            event.setErrMsg(e.getMessage());
            eventLogService.addNew(event);
            //endregion event log fail (status, message)
            Map<String,String> map = new HashMap<>();
            try{
                if( e.getMessage().contains("404")){
                    logger.info("DIGO-Info: status" + e.getMessage().replace("404 Not Found: ", ""));
                    String newStr = e.getMessage().replace("404 Not Found: ", "");
                    String newStr2 = newStr.substring(1,newStr.length()-1);
                    ObjectMapper mapper = new ObjectMapper();
                    mapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
                    Map statusErr = mapper.readValue(newStr2,  HashMap.class);
                    logger.info("DIGO-Info: status" + statusErr.get("status"));
                    logger.info("DIGO-Info: description" + statusErr.get("description"));
                    map.put("statusMessage",statusErr.get("description").toString());
                    map.put("statusCode", statusErr.get("status").toString());
                }
            }catch(Exception ex){
                map.put("statusMessage",ex.getMessage());
                map.put("statusCode","-999");
            }
            //endregion event log fail (status, message)
            updateDossierLLTPStatus(map,code);
            return new JudicialRecordsDto();
        }
    }

    public JudicialRecordsDto writeLogLLTP(HttpServletRequest request,JudicialRecordsParamsDto params, Map<String, Object> body, String code,ObjectId logID, int type ){
        try {
            body.put("code", code);
            mongoTemplate.save(body, "dossierSendBodyCriminalRecord");
        } catch (Exception e) {
            System.out.println("data insert log"+ e.getMessage());
        }
        //region event log init
        PostEventLogDto event = new PostEventLogDto();
        event.setRequestAdapter(request, body,"ad");
        //event.setRes(null);
        event.setPid(logID);
        event.setServiceId(this.serviceId);
        event.setKey( new SidNameDto("Code", code ));
        //endregion event log init
        try{
            JudicialRecordsDto response = new JudicialRecordsDto();
            if(type == 1 ){
                response = sendCriminalRecordGet(params, body, code);
            }
            if(type == 2 ){
                response = sendCriminalRecord(params, body, code);
            }
            //region event log success (status)
            event.setStatus(true);
            event.setErrMsg(response.toString());
            eventLogService.addNew(event);
            //endregion event log success (status, message)
            logger.info("DIGO-Response: " + response.toString());
            return response;
        }catch(Exception e){
            //region event log fail (status, message)
            event.setStatus(false);
            event.setErrMsg(e.getMessage());
            eventLogService.addNew(event);
            //endregion event log fail (status, message)
            Map<String,String> map = new HashMap<>();
            try{
                if( e.getMessage().contains("404")){
                    logger.info("DIGO-Info: status" + e.getMessage().replace("404 Not Found: ", ""));
                    String newStr = e.getMessage().replace("404 Not Found: ", "");
                    String newStr2 = newStr.substring(1,newStr.length()-1);
                    ObjectMapper mapper = new ObjectMapper();
                    mapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
                    Map statusErr = mapper.readValue(newStr2,  HashMap.class);
                    logger.info("DIGO-Info: status" + statusErr.get("status"));
                    logger.info("DIGO-Info: description" + statusErr.get("description"));
                    map.put("statusMessage",statusErr.get("description").toString());
                    map.put("statusCode", statusErr.get("status").toString());
                }
            }catch(Exception ex){
                map.put("statusMessage",ex.getMessage());
                map.put("statusCode","-999");
            }
            //endregion event log fail (status, message)
            updateDossierLLTPStatus(map,code);
            return new JudicialRecordsDto();
        }
    }

    public AffectedMessageDto updateDossierLLTPStatus(Map response , String code){
        String updateUrl = microservice.padmanUri("/dossierAutoSync/" + code + "/status-lltp").toUriString();
//        String updateUrl = "http://localhost:8080/dossierAutoSync/"+code+"/status-lltp";
        logger.info("DIGO-Response-URL: " + updateUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(response, headers);
        AffectedMessageDto result = restTemplate.exchange(updateUrl, HttpMethod.PUT, request, AffectedMessageDto.class).getBody();
        return result;
    }
    
    public JudicialRecordsTokenDto getTokenLGSP(JudicialRecordsParamsDto params) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(config.getParametersValue("consumer-key"), config.getParametersValue("consumer-secret"));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(headers);
        JudicialRecordsTokenDto result = restTemplate.exchange(config.getParametersValue("gateway-token") + "?grant_type=client_credentials",
                HttpMethod.POST, request, JudicialRecordsTokenDto.class).getBody();
        //Return body
        if (Objects.isNull(result.getAccess_token())) {
            return null;
        }
        return result;
    }
    private HttpHeaders getExtendHeadersSend(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);
        return headers;
    }

    public LLTPTraTrangThaiHoSoOutputDto traTrangThaiHoSo(Map body) {
        logger.info("--------------Start----------------------------");
        IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(body.get("configId").toString()));
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        try {
            String uri = config.getParametersValue("gateway").toString();
            JudicialRecordsParamsDto tokenDto = new JudicialRecordsParamsDto(new ObjectId(body.get("configId").toString()),new ObjectId(body.get("configId").toString()),new ObjectId(body.get("configId").toString()),null);
            JudicialRecordsTokenDto lgspToken = getTokenLGSP(tokenDto);
            String lltpToken = lgspToken.getAccess_token();
            HttpHeaders headers = getExtendHeadersSend(lltpToken);
            HttpEntity<?> req = new HttpEntity<>(body, headers);
            LLTPTraTrangThaiHoSoOutputDto result = restTemplate.postForObject(uri+"traTrangThaiHs", req, LLTPTraTrangThaiHoSoOutputDto.class);
            return result;
        } catch (Exception e) {
            logger.info("--------------Throw exception----------------------------");
            logService.save(config, new IdCodeNameSimpleDto(new ObjectId(body.get("idReceivedDec").toString()),"", ""), 0, e.getMessage(), body.toString());
            LLTPTraTrangThaiHoSoOutputDto error = new LLTPTraTrangThaiHoSoOutputDto();
            error.setDecStatusName("99999");
            error.setDescription(e.getMessage());
            return error;
        }
    }

    public List<StatusCriminalJudicalResponseDto> getListStatusFromLGSP(CivilDossierStatusDto body) {
        logger.info("--------------Start----------------------------");
        IntegratedConfigurationDto config = configurationService.getConfig(body.getConfigId());
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        List<StatusCriminalJudicalResponseDto> listStatusReponse = new ArrayList<>();
        try{
            String uri = config.getParametersValue("gateway").toString();
            JudicialRecordsParamsDto tokenDto = new JudicialRecordsParamsDto(body.getConfigId(),body.getConfigId(),body.getConfigId(),null);
            JudicialRecordsTokenDto lgspToken = getTokenLGSP(tokenDto);
            String lltpToken = lgspToken.getAccess_token();
            HttpHeaders headers = getExtendHeadersSend(lltpToken);
            body.getDossiers().forEach(status ->{
                try {
                    HttpEntity<?> req = new HttpEntity<>(status, headers);
                    StatusCriminalJudicalResponseDto result = restTemplate.postForObject(uri+"traTrangThaiHs", req, StatusCriminalJudicalResponseDto.class);
                    listStatusReponse.add(new StatusCriminalJudicalResponseDto(result, status.get("idReceivedDec").toString()));
                } catch (Exception ex) {
                    logger.info("--Throw exception--:" + ex.getMessage());
                    logService.save(config, new IdCodeNameSimpleDto(new ObjectId(status.get("idReceivedDec").toString()),"", ""), 0, ex.getMessage(), body.toString());
                    if (ex.getMessage().contains("404")) {
                        logger.info("DIGO-Info: status" + ex.getMessage().replace("404 Not Found: ", ""));
                        try{
                            String newStr = ex.getMessage().replace("404 Not Found: ", "");
                            String newStr2 = newStr.substring(1,newStr.length()-1);
                            ObjectMapper mapper = new ObjectMapper();
                            mapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
                            Map statusErr = mapper.readValue(newStr2,  HashMap.class);
                            logger.info("DIGO-Info: status" + statusErr.get("status"));
                            logger.info("DIGO-Info: description" + statusErr.get("description"));
                            listStatusReponse.add(new StatusCriminalJudicalResponseDto(null,null, statusErr.get("status").toString(), statusErr.get("description").toString(),null,null,status.get("idReceivedDec").toString()));
                        } catch (JsonProcessingException e) {
                            listStatusReponse.add(new StatusCriminalJudicalResponseDto(null,null, "-99999", "Co loi xay ra",null,null,status.get("idReceivedDec").toString()));
                            throw new RuntimeException(e);
                        }
                    }else{
                        listStatusReponse.add(new StatusCriminalJudicalResponseDto(null,null, "-99999", "Co loi xay ra",null,null,status.get("idReceivedDec").toString()));
                    }
                }
            });
        } catch (Exception e) {
            logger.info("--Throw exception--:" + e.getMessage());
            return new ArrayList();
        }
        return  listStatusReponse;
    }
}
