package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.FileUResDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.industry_trade_bd.*;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.bdg.DossierExtend;
import vn.vnpt.digo.adapter.properties.SCTProperties;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import java.util.Date;
import java.util.HashMap;
import java.util.Base64;
import java.util.stream.Collectors;

@Service
public class IndustryAndTradeBDService {
    Logger logger = LoggerFactory.getLogger(IndustryAndTradeBDService.class);
    @Autowired
    private Translator translator;
    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private Microservice microservice;

    @Autowired
    private MappingDataService mappingDataService;

    private IntegratedConfigurationDto config;
    @Autowired
    RestTemplate rest;
    @Autowired
    private IntegratedLogsService integratedLogsService;

    private DossierExtendsDto dossier;

    private final ObjectId SERVICE_ID = new ObjectId("62be69ce2a990f4998d0986b");

    private final ObjectId SUBSYSTEM_ID = new ObjectId("5f7c16069abb62f511880003");

    enum TypeStatus{
        TASK_STATUS, // Trạng thái công việc
        TASK_REMIND // Thông báo nhắc nhở
    }
    public SCTResponce1Dto getPlacesLv2(ObjectId configId, ObjectId deploymentId, BasicReqDto data) {

        SCTResponce1Dto res = new SCTResponce1Dto();

        this.existConfig(configId, deploymentId);
        this.authBasicMC(data.getKey(), data.getSecretkey());

        String placeUrl = microservice.basedataUri("/place/--search?nation-id=" + config.getParametersValue("nation-id") + "&parent-type-id=" + config.getParametersValue("district-type-id")).toUriString();
//        String placeUrl = "http://localhost:8080/place/--search?nation-id="+ config.getParametersValue("nation-id") + "&parent-type-id=" + config.getParametersValue("district-type-id");
        PlaceDto[] places = MicroserviceExchange.getNoAuth(rest, placeUrl, PlaceDto[].class);
        ArrayList<PlaceLv2ResDto>  placesRes = new ArrayList<>();
        for (PlaceDto place: places){
            PlaceLv2ResDto placeRes = new PlaceLv2ResDto();
            placeRes.setQuanID(place.getId());
            placeRes.setTenQuan(place.getName());
            String districtId = place.getId();
            if (districtId != null && districtId.length() == 24){
                String codeDistrict = districtId.substring(21);
                placeRes.setMaHanhChinh(codeDistrict);
            }
            String provinceId = place.getParent().getId();
            if (provinceId != null && provinceId.length() == 24){
                String codeProvince = provinceId.substring(22);

                placeRes.setMaTinhThanh(codeProvince);
            }
            placeRes.setTinhThanhID(provinceId);
            placeRes.setTenTinhThanh(place.getParent().getName());
            placesRes.add(placeRes);

        }
        res.setStatusCode(200);
        res.setResultObject(new Gson().toJson(placesRes));
        return res ;
    }

    public SCTResponce1Dto getPlacesLv3(ObjectId configId, ObjectId deploymentId, BasicReqDto data) {

        SCTResponce1Dto res = new SCTResponce1Dto();

        this.existConfig(configId, deploymentId);
        this.authBasicMC(data.getKey(), data.getSecretkey());

        String placeUrl = microservice.basedataUri("/place/--search?nation-id=" + config.getParametersValue("nation-id") + "&parent-type-id=" + config.getParametersValue("ward-type-id")).toUriString();
//        String placeUrl = "http://localhost:8080/place/--search?nation-id="+ config.getParametersValue("nation-id") + "&parent-type-id=" + config.getParametersValue("ward-type-id");
        PlaceDto[] places = MicroserviceExchange.getNoAuth(rest, placeUrl, PlaceDto[].class);

        ArrayList<PlaceLv3ResDto>  placesRes = new ArrayList<>();
        for (PlaceDto place: places){

            PlaceLv3ResDto placeRes = new PlaceLv3ResDto();
            placeRes.setPhuongID(place.getId());
            placeRes.setTenPhuong(place.getName());
            String wardId = place.getId();
            if (wardId != null && wardId.length() == 24){
                String codeWard = wardId.substring(19);
                placeRes.setMaHanhChinh(codeWard);
            }
            String districtId = place.getParent().getId();
            if (districtId != null && districtId.length() == 24){
                String codeDistrict = districtId.substring(21);
                placeRes.setMaQuan(codeDistrict);
            }
            placeRes.setQuanID(districtId);
            placeRes.setTenQuan(place.getParent().getName());
            placesRes.add(placeRes);

        }
        res.setStatusCode(200);
        res.setResultObject(new Gson().toJson(placesRes));
        return res ;
    }

    public SCTResponce1Dto getSectors(ObjectId configId, ObjectId deploymentId, SectorReqDto data){

        SCTResponce1Dto res = new SCTResponce1Dto();

        this.existConfig(configId, deploymentId);
        this.authBasicMC(data.getKey(), data.getSecretkey());

        String agency_id = this.mappingDataService.getByDest(new ObjectId(config.getParametersValue("agency-map-type").toString()), data.getDonViID().toString(), deploymentId).getSourceId();
//        System.out.println(mapping_data);
        String sectorUrl = microservice.basepadUri("/sector/--all?agency-id=" + agency_id + "&only-agency-id=" + config.getParametersValue("sector-only-agency-id")).toUriString();
        SectorDto[] sectors = MicroserviceExchange.getNoAuth(rest, sectorUrl, SectorDto[].class);

        ArrayList<SectorResDto>  sectorsRes = new ArrayList<>();
        for (SectorDto sector: sectors){

            SectorResDto sectorRes = new SectorResDto();
            sectorRes.setLinhVucID(sector.getId());
            sectorRes.setMaLinhVuc(sector.getCode());
            sectorRes.setTenLinhVuc(sector.getName());
            sectorsRes.add(sectorRes);

        }
        res.setStatusCode(200);
        res.setResultObject(new Gson().toJson(sectorsRes));
        return res ;
    }


    public SCTResponce1Dto getProcedures(ObjectId configId, ObjectId deploymentId, SectorReqDto data){

        SCTResponce1Dto res = new SCTResponce1Dto();

        this.existConfig(configId, deploymentId);
        this.authBasicSCT(data.getKey(), data.getSecretkey());

        String agency_id = this.mappingDataService.getByDest(new ObjectId(config.getParametersValue("agency-map-type").toString()), data.getDonViID().toString(), deploymentId).getSourceId();

        String procedureUrl = microservice.basepadUri("/procedure/--agency-id?agency-id=" + agency_id).toUriString();
//        String procedureUrl = "http://localhost:8069/procedure/--agency-id?agency-id=" + agency_id;

        ProcedureDto[] procedures = MicroserviceExchange.getNoAuth(rest, procedureUrl, ProcedureDto[].class);

        ArrayList<ProcedureResDto>  proceduresRes = new ArrayList<>();
        String[] listTimeUnit = config.getParametersValue("listProcessingTimeUnitID").toString().split(";");

        for (ProcedureDto procedure: procedures){
            ProcedureResDto procedureRes = new ProcedureResDto();
            procedureRes.setThuTucMotCuaID(procedure.getId());
            if (Objects.nonNull(procedure.getExtendBDG())){
                procedureRes.setThuTucID(procedure.getExtendBDG().getFpt1GateProcedureId());
                procedureRes.setMaThuTuc(procedure.getExtendBDG().getFpt1GateProcedureCode());
                procedureRes.setLinhVucID(procedure.getExtendBDG().getFpt1GateSectorId());
            }
            procedureRes.setMaThuTucMotCua(procedure.getCode());
            procedureRes.setTenThuTuc(TranslateName.getLanguageVN(procedure.getTranslate()));
            procedureRes.setLinhVucMotCuaID(procedure.getSectorId());
            if (procedure.getProcessingTimeUnit() != null){
                //Minutes
                if( procedure.getProcessingTimeUnit().equals(listTimeUnit[0])){
                    procedureRes.setSoNgayGiaiQuyet(1);
                }
                //Hours
                else if(procedure.getProcessingTimeUnit().equals(listTimeUnit[1])){
                    Double hours = procedure.getProcessingTime();
                    procedureRes.setSoNgayGiaiQuyet((int)Math.ceil(hours/8));
                }else {
                    procedureRes.setSoNgayGiaiQuyet((int) Math.ceil(procedure.getProcessingTime()));
                }
            }else{
                procedureRes.setSoNgayGiaiQuyet(null);
            }
            proceduresRes.add(procedureRes);
        }
        res.setStatusCode(200);
        res.setResultObject(new Gson().toJson(proceduresRes));
        return res ;
    }

    public SCTResponce1Dto getProcedueForm(ObjectId configId, ObjectId deploymentId, ProcedureFormReqDto data){

        SCTResponce1Dto res = new SCTResponce1Dto();

        this.existConfig(configId, deploymentId);
        this.authBasicSCT(data.getKey(), data.getSecretkey());

//        String procedureFormUrl = microservice.basepadUri("/bdg-procedure-form/getBy?fpt1GateProcedureId=" + data.getThuTucHanhChinhID()).toUriString();
        try{
            String procedureFormUrl = microservice.basepadUri("/bdg-procedure-form/getBy?fpt1GateProcedureId=" + data.getThuTucHanhChinhID()).toUriString();
//            String procedureFormUrl = "http://localhost:8069/bdg-procedure-form/getBy?fpt1GateProcedureId=" + data.getThuTucHanhChinhID();
            ProcedureFormDto[] procedureForms = MicroserviceExchange.getNoAuth(rest, procedureFormUrl, ProcedureFormDto[].class);

            ArrayList<ProcedureFormResDto>  proceduresFormRes = new ArrayList<>();

            for (ProcedureFormDto procedureForm : procedureForms){
                if (procedureForm.getStatus() == 1){
                    ProcedureFormResDto procedureFormRes = new ProcedureFormResDto();
                    procedureFormRes.setMaLoaiHoSoDinhKem(procedureForm.getForm().getCode());
                    procedureFormRes.setTenLoaiHoSoDinhKem(TranslateName.getLanguageVN(procedureForm.getForm().getName()));
                    proceduresFormRes.add(procedureFormRes);
                }
            }

            res.setStatusCode(200);
            res.setResultObject(new Gson().toJson(proceduresFormRes));
            return res ;
        }catch (Exception e){
            logger.info("SCT BDG: " + e.getMessage());
            res.setStatusCode(500);
            res.setDescription("Lỗi trong khi xử lý");
            res.setResultObject("[]");
            return res ;
        }

    }

    /**
     * Đồng bộ hồ sơ trong khoảng thời gian
     * @param configId
     * @param deploymentId
     * @param data
     * @return
     */
    public SCTResponce1Dto getDossierByPeriod(ObjectId configId, ObjectId deploymentId, DossierReqDto data){

        SCTResponce1Dto res = new SCTResponce1Dto();

        this.existConfig(configId, deploymentId);
        this.authBasicSCT(data.getKey(), data.getSecretkey());

        String agency_id = config.getParametersValue("agency_id");
        //        String agency_id = this.mappingDataService.getByDest(new ObjectId(config.getParametersValue("key-agency-map-type").toString()), data.getKey(), deploymentId).getSourceId();
        String timezone = config.getParametersValue(SCTProperties.TIMEZONE);
//        String dossierUrl = "http://localhost:8080/dossier-sctbd/--parent-agency?parent-agency-id=" + agency_id + "&begin-time=" +data.getTuNgay() +"&end-time=" + data.getDenNgay() + "&timezone=" + config.getParametersValue(SCTProperties.TIMEZONE);
        String dossierUrl = microservice.padmanUri("/dossier-sctbd/--parent-agency?parent-agency-id=" + agency_id + "&begin-time=" + data.getTuNgay() + "&end-time=" + data.getDenNgay() + "&timezone=" + timezone).toUriString();
        logger.info(dossierUrl);
        DossierDto[] dossiers = MicroserviceExchange.getNoAuth(rest, dossierUrl, DossierDto[].class);
        logger.info("Count dossier: " + dossiers.length);
        ArrayList<DossierResDto>  dossiersRes = new ArrayList<>();
        if (dossiers.length > 0 ){
            for (DossierDto dossier: dossiers){
                dossiersRes.add(setDossierResDto(dossier));
            }
            res.setResultObject(new Gson().toJson(dossiersRes));
        }else {
            res.setResultObject("[]");
        }

        res.setStatusCode(200);

        return res ;
    }

    public SCTResponce1Dto getDossierFile(ObjectId configId, ObjectId deploymentId, DossierFileReqDto data){

        SCTResponce1Dto res = new SCTResponce1Dto();
        this.existConfig(configId, deploymentId);
        this.authBasicSCT(data.getKey(), data.getSecretkey());

        try{
            //        String dossierUrl = "http://localhost:8080/dossier-sctbd/files?dossier-id=" + data.getHoSoID() ;
            String dossierUrl = microservice.padmanUri("/dossier-sctbd/files?dossier-id=" + data.getHoSoID()).toUriString();
            DossierFileDto[] dossierFiles = MicroserviceExchange.getNoAuth(rest, dossierUrl, DossierFileDto[].class);
            logger.info("Lay thanh cong dossierFiles");
            ArrayList<DossierFileResDto>  dossiersFileRes = new ArrayList<>();
            for (DossierFileDto dossierFile: dossierFiles){
                DossierFileResDto dossierFileRes = new DossierFileResDto();
                if (dossierFile.getType().getName().toLowerCase().contains("chính")){
                    dossierFileRes.setSoBanChinh(dossierFile.getQuantity().toString());
                }
                if (dossierFile.getType().getName().toLowerCase().contains("sao")){
                    dossierFileRes.setSoBanSao(dossierFile.getQuantity().toString());
                }
                dossierFileRes.setTenHoSoKemTheo(dossierFile.getProcedureForm());
                for (DossierFileDto.File file: dossierFile.getFile()){
                    String url = microservice.filemanUri("file/" + file.getId()).toUriString();
                    byte[] fileContent = MicroserviceExchange.getFileNoAuth(rest, url);
//                byte[] fileContent = entity.getBody();
                    String sourceFile = Base64.getEncoder().encodeToString(fileContent);

                    dossierFileRes.setFileBase64String(sourceFile);
                    dossierFileRes.setFileName(file.getFilename());

                    dossiersFileRes.add(dossierFileRes);
                }
            }
            res.setStatusCode(200);
            res.setResultObject(new Gson().toJson(dossiersFileRes));
            return res ;
        }catch (Exception e){
            logger.info("SCT BDG: " + e.getMessage());
            res.setStatusCode(500);
            res.setDescription("Lỗi trong khi xử lý");
            res.setResultObject("[]");
            return res ;
        }

    }

    public DossierResDto setDossierResDto(DossierDto dossier) {

        DossierDto.dateFormat = config.getParametersValue(SCTProperties.DATETIME_FORMAT);

        DossierResDto dossierRes = new DossierResDto();
        dossierRes.setHoSoID(dossier.getId());
        dossierRes.setMaHoSo(dossier.getCode());
        dossierRes.setLinhVucID(dossier.getProcedure().getSector().getId());
        dossierRes.setMaLinhVuc(dossier.getProcedure().getSector().getCode());
        dossierRes.setLinhVuc(dossier.getProcedure().getSector().getName());
        dossierRes.setThuTucID(dossier.getProcedure().getId());
        dossierRes.setMaThuTucHanhChinh(dossier.getProcedure().getCode());
        dossierRes.setThuTucHanhChinh(TranslateName.getLanguageVN(dossier.getProcedure().getTranslate()));
        dossierRes.setSoBienNhan(dossier.getSoBienNhan());
        dossierRes.setTenNguoiNop(dossier.getApplicant().getFullname());
        dossierRes.setTenToChuc(dossier.getApplicant().getOrganization());
        dossierRes.setDiaChi(dossier.getFullAddress());
        dossierRes.setEmail(dossier.getApplicant().getEmail());
        dossierRes.setSoDienThoai(dossier.getApplicant().getPhoneNumber());
        dossierRes.setNgayNhan(dossier.getAcceptedDate());
        dossierRes.setNgayHenTra(dossier.getAppointmentDate());
        dossierRes.setNgayHoanThanh(dossier.getCompletedDate());
        dossierRes.setHinhThucNop(dossier.getApplyMethod());
        if (!Objects.isNull(dossier.getAccepter())){
            dossierRes.setTenNguoiNhan(dossier.getAccepter().getFullname());
            dossierRes.setIDNguoiNhan(dossier.getAccepter().getId());
        }else {
            dossierRes.setTenNguoiNhan("");
            dossierRes.setIDNguoiNhan("");
        }


        dossierRes.setTinhTrangThanhToan(dossier.getStatusPayment());
        if (Objects.nonNull(dossier.getTask()) && dossier.getTask().size() > 0){
            ArrayList<DossierDto.DossierTask> tasks = dossier.getTask();
            ArrayList<DossierResDto.Officer> officers = new ArrayList<>();
            for (DossierDto.DossierTask task: tasks){
                    DossierDto.CandidateUser assignee = task.getAssignee();
                    if (Objects.nonNull(assignee)){
                        //Check nhan vien khong bi trung
                        if (!officers.stream().anyMatch(a -> a.getIDNhanVien().equals(assignee.getId()))){
                            officers.add(new DossierResDto.Officer(assignee.getId(), assignee.getFullname()));
                        }
                    }
            }
            dossierRes.setNguoiThucHien(officers);
        }
        return dossierRes;
    }

    /**
     * Trả trạng thái đã tiếp nhận khi SCT nhận hồ sơ
     * @param configId
     * @param deploymentId
     * @param data
     * @return
     */
    public StatusResDto receiveStatus(ObjectId configId, ObjectId deploymentId, ReceiveStatusReq data){
        StatusResDto res = new StatusResDto();
        try{
            this.existConfig(configId, deploymentId);
            this.authBasicSCT(data.getKey(), data.getSecretkey());
            logger.info("Lấy cấu hình và xác thực thành công");

            String dossierUrl = microservice.padmanUri("/dossier/" + data.getMaHoSo()+ "/--by-code").toUriString() ;
//            String dossierUrl = "http://localhost:8080/dossier/" + data.getMaHoSo()+ "/--by-code" ;
            dossier = MicroserviceExchange.getNoAuth(rest, dossierUrl, DossierExtendsDto.class);

            if (!this.checkDossierBelongsAgency(dossier)){
                res.setCode(-1);
                res.setMessage("Mã hồ sơ không trực thuộc sở CT");
                logger.info("Mã hồ sơ không trực thuộc sở CT");
                return res;
            }
            if (dossier.getTask().size() > 1){
                res.setCode(-1);
                res.setMessage("Hồ sơ đã qua bước tiếp nhận, không thể nhận");
                logger.info("Dossier passed receive node. Don't get");
                return res;
            }
            logger.info("Lấy thành công hồ sơ");
            String message = null;
            if (data.getTrangThai().equals(config.getParametersValue(SCTProperties.CODE_RECEIVED_SUCCESS)))
            {
                //Tạo bộ trạng thái hồ sơ
                HashMap<String, Enum> lsParamConfig = new HashMap<>();
                lsParamConfig.put(SCTProperties.TASK_STATUS_RECEIVED, TypeStatus.TASK_STATUS);
                lsParamConfig.put(SCTProperties.TASK_REMIND_RECEIVED, TypeStatus.TASK_REMIND);
                StatusDossierDto statusDossierDto =  this.putStatusDossier(lsParamConfig);
                DossierExtend extendBDG = new DossierExtend();
                extendBDG.setStatusSyncConnect(1);
                extendBDG.setReceiveConnectDate(new Date());
                extendBDG.setReceivedConnect(true);
                statusDossierDto.setExtendBDG(extendBDG);

                this.updateStatus(statusDossierDto);

                if (data.getMoTa() == "" || data.getMoTa() == null){
                    message = "Đã tiếp nhận thành công";
                }else {
                    message = data.getMoTa();
                }
            }else {
                if (data.getMoTa() == "" || data.getMoTa() == null){
                    message = "Lỗi tiếp nhận hồ sơ từ SCT";
                }else {
                    message = data.getMoTa();
                }
            }

            this.comment(message);
            res.setCode(1);
            res.setMessage("Cập nhật thành công");
            logger.info("Cập nhật thành công trạng thái hồ sơ tiếp nhận");
//            IntegratedConfigurationDto configToLog = new IntegratedConfigurationDto();
//            IntegratedService serviceToLog = new IntegratedService(this.SERVICE_ID, "SCT Bình Dương");
//            configToLog.setService(serviceToLog);
//            configToLog.setId(configId);
//            integratedLogsService.save(configToLog, new IdCodeNameSimpleDto(new ObjectId(), data.getMaHoSo(), "TiepNhanHSThanhCong"), 0, "", GsonUtils.getJson(res.toString()));
            return res;
        }catch (Exception e){
            res.setCode(-1);
            res.setMessage("Lỗi khi trả trạng thái đã tiếp nhận");
            logger.info("Lỗi khi trả trạng thái đã tiếp nhận");
            return res;
        }
    }

    /**
     * Đồng bộ trạng thái hồ sơ SCT về igate
     * @param configId
     * @param deploymentId
     * @param data
     * @return
     */
    public StatusResDto syncStatusDossier(ObjectId configId, ObjectId deploymentId, SyncStatusDossierReqDto data){
        StatusResDto res = new StatusResDto();
        try{
            this.existConfig(configId, deploymentId);
            this.authBasicSCT(data.getKey(), data.getSecretkey());
            logger.info("Lấy cấu hình và xác thực thành công");

            String dossierUrl = microservice.padmanUri("/dossier/" + data.getMaHoSo()+ "/--by-code").toUriString() ;
            dossier = MicroserviceExchange.getNoAuth(rest, dossierUrl, DossierExtendsDto.class);
            if (!this.checkDossierBelongsAgency(dossier)){
                res.setCode(-1);
                res.setMessage("Mã hồ sơ không trực thuộc sở CT");
                logger.info("Mã hồ sơ không trực thuộc sở CT");
                return res;
            }
            logger.info("Get successfully dossier");

            //Set extend BDG
            DossierExtend extendBDG = new DossierExtend();
            switch (data.getTrangThai()){
                case "DOC_ASSIGN":
                case "DOC_HANDLE":
                case "DOC_CHECK":
                case "DOC_APPROVE":
                    extendBDG.setStatusSyncConnect(2); //Dang xu ly
                    break;

                case "DOC_RETURN":
                    extendBDG.setStatusSyncConnect(5); //Tra ket qua
                    extendBDG.setReturnedConnect(true);;
                    break;
                default:
                    res.setCode(-1);
                    res.setMessage("Lỗi đồng bộ hồ sơ");
                    logger.info("Sai trạng thái");
                    return res;
            }

            //Lay tag trang thai ho so
            Map<String, String> paramsStatus = new HashMap<>();
            paramsStatus.put("integratedCode", data.getTrangThai());
            paramsStatus.put("categoryId", config.getParametersValue("TrangThaiHoSo"));
            paramsStatus.put("status", "1");
            String paramStatus = paramsStatus.entrySet().stream().map(Object::toString).collect(Collectors.joining("&"));

            String tagByUrl = microservice.basecatUri("/tag/getBy/--no-page?" + paramStatus).toUriString();
//            String tagByUrl = "http://localhost:8083/tag/getBy/--no-page?" + paramStatus ;
            TagDto[] tagsStatus = MicroserviceExchange.getNoAuth(rest, tagByUrl, TagDto[].class);
            if (tagsStatus.length < 1){
                res.setCode(-1);
                res.setMessage("Lỗi đồng bộ hồ sơ");
                logger.info("Not found tag");
                return res;
            }
            TagDto tagStatus = tagsStatus[0];

            //Lay cong viec ho so
            Map<String, String> paramsTask = new HashMap<>();
            paramsTask.put("integratedCode", data.getTrangThai());
            paramsTask.put("categoryId", config.getParametersValue("CongViecHoSo"));
            paramsTask.put("status", "1");
            String paramTask = paramsTask.entrySet().stream().map(Object::toString).collect(Collectors.joining("&"));
            String tagByUrl1 = microservice.basecatUri("/tag/getBy/--no-page?" + paramTask).toUriString();
//            String tagByUrl1 = "http://localhost:8083/tag/getBy/--no-page?" + paramTask ;
            TagDto[] tagsTask = MicroserviceExchange.getNoAuth(rest, tagByUrl1, TagDto[].class);
            if (tagsTask.length < 1){
                res.setCode(-1);
                res.setMessage("Lỗi đồng bộ hồ sơ");
                logger.info("Not found tag");
                return res;
            }
            TagDto tagTask = tagsTask[0];
            logger.info("Set status dossier");
            StatusDossierDto statusDossierDto = new StatusDossierDto();
            statusDossierDto.setCode(dossier.getCode());
            statusDossierDto.setId(dossier.getId());
            statusDossierDto.setDossierTaskStatus(new DossierTaskStatusSimple(tagStatus.getId(), tagStatus.getTrans()));
            statusDossierDto.setDossierMenuTaskRemind(new DossierTaskStatusSimple(tagTask.getId(), tagTask.getTrans()));
            statusDossierDto.setExtendBDG(extendBDG);

            logger.info("update status dossier");
            this.updateStatus(statusDossierDto);
            String message = "Nhận thành công trạng thái hồ sơ tiếp nhận. Trạng thái đồng bộ: " + TranslateName.getLanguageVN(tagStatus.getTrans()) ;

            this.comment(message);
            res.setCode(1);
            res.setMessage("Nhận thành công");
            logger.info("Done");
            return res;
        }
        catch (Exception e){
            res.setCode(-1);
            res.setMessage("Lỗi đồng bộ hồ sơ");
            logger.info("Lỗi đồng bộ hồ sơ");
            return res;
        }
    }

    /**
     * Đính kèm văn bản kết quả xử lý theo trạng thái
     * @param configId
     * @param deploymentId
     * @param input
     * @return
     */
    public StatusResDto attachEdocDossier(ObjectId configId, ObjectId deploymentId, AttachmentEDocDossierReqDto input){
        StatusResDto res = new StatusResDto();
        try{
            this.existConfig(configId, deploymentId);
            this.authBasicSCT(input.getKey(), input.getSecretkey());
            logger.info("Lấy cấu hình và xác thực thành công");

            String dossierUrl = microservice.padmanUri("/dossier/" + input.getMaHoSo()+ "/--by-code").toUriString() ;
//            String dossierUrl = "http://localhost:8080/dossier/" + input.getMaHoSo()+ "/--by-code" ;
            dossier = MicroserviceExchange.getNoAuth(rest, dossierUrl, DossierExtendsDto.class);
            logger.info("Lấy thành công hồ sơ");

            //Kiểm tra đúng trạng thái để đính kèm
            if (!Objects.isNull(dossier.getExtendBDG()) && !dossier.getExtendBDG().getReturnedConnect()){
                res.setCode(-1);
                res.setMessage("Cần đồng bộ trạng thái 'trả kết quả' trước");
                logger.info("Cần đồng bộ trạng thái 'trả kết quả' trước");
                return res;
            }

            if (input.getTaiLieuGui().size() == 0 || input.getTaiLieuGui() == null){
                res.setCode(-1);
                res.setMessage("Không có văn bản đính kèm");
                return res;
            }

            FileUResDto[] files = this.uploadFile(input.getTaiLieuGui());
            logger.info("upload file success");
            ArrayList<AttachmentSCTDto> lsAtt;
            if (dossier.getAttachment() == null){
                lsAtt = new ArrayList<>();
            }else {
                lsAtt = dossier.getAttachment();
            }

            ArrayList<HistorySCTDto.Action> actions = new ArrayList<>(); //Dung de gan gia tri vào history
            for (FileUResDto file: files){
                AttachmentSCTDto att = new AttachmentSCTDto();
                att.setId(new ObjectId(file.getId()));
                att.setFilename(file.getFilename());
                att.setSize(file.getSize());
                lsAtt.add(att);

                HistorySCTDto.FileInfo fileInfo = new HistorySCTDto.FileInfo();
                fileInfo.setId(new ObjectId(file.getId()));
                fileInfo.setName(file.getFilename());

                HistorySCTDto.Action action = new HistorySCTDto.Action();
                action.setFieldNameRbk("lang.word.result");

                action.setNewValue(new ObjectMapper().writeValueAsString(fileInfo));
                action.setOriginalValue("");

                actions.add(action);
            }

            String attachmentUrl = microservice.padmanUri("/dossier-sctbd/" + dossier.getId() + "/update-attachment").toUriString();
//            logger.info(attachmentUrl);
//            String attachmentUrl = "http://localhost:8080/dossier-sctbd/"+ dossier.getId() + "/update-attachment";

            AffectedRowsDto result = MicroserviceExchange.putJsonNoAuth(rest, attachmentUrl, new PutDossierAttachmentSCT(lsAtt), AffectedRowsDto.class);
            logger.info("Cập nhật file đính kèm thành công");
            String message = "Đã gửi " + input.getTaiLieuGui().size() + " văn bản : " + files[0].getFilename() + ",... Ghi chú: " ;
            if (input.getYKien() == "" || input.getYKien() == null){
                message += "Không";
            }else {
                message += input.getYKien();
            }

            this.comment(message);
            this.writeHistory(actions);

            res.setCode(1);
            res.setMessage("Nhận thành công");
            logger.info("Nhận thành công văn bản kết quả xử lý");
            return res;
        }catch (Exception e){
            res.setCode(-1);
            res.setMessage("Lỗi gửi văn bản kết quả xử lý");
            logger.info("Lỗi gửi văn bản kết quả xử lý");
            return res;
        }
    }

    /**
     * Tạo các trạng thái hồ sơ
     * @param lsParams
     * @return StatusDossierDto
     */
    public StatusDossierDto createStatusDossier(HashMap<String,Enum> lsParams){
        StatusDossierDto statusDossierDto = new StatusDossierDto();
        for (String key :
                lsParams.keySet()) {
            String tagId = this.config.getParametersValue(key);
            if (tagId == null && tagId == ""){
                throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
            }
            String url = microservice.basecatUri("/tag/" + tagId).toUriString() ;
            logger.info(url);
            TagDto tag = MicroserviceExchange.getNoAuth(rest, url, TagDto.class);
            if (lsParams.get(key) == TypeStatus.TASK_STATUS){
                statusDossierDto.setDossierTaskStatus(new DossierTaskStatusSimple(tag.getId(), tag.getTrans()));
            }
            if (lsParams.get(key) == TypeStatus.TASK_REMIND){
                statusDossierDto.setDossierMenuTaskRemind(new DossierTaskStatusSimple(tag.getId(), tag.getTrans()));
            }
        }
        return statusDossierDto;
    }

    public void existConfig(ObjectId configId, ObjectId deploymentId){
        if (Objects.nonNull(configId) && Objects.nonNull(deploymentId)) {
            config = configurationService.getConfig(configId, deploymentId);
        }else {
            config = configurationService.getConfig(null, this.SUBSYSTEM_ID, this.SERVICE_ID);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        rest = MicroserviceExchange.getOAuth2RestTemplate(config);
    }

    public void authBasicSCT(String key, String secret){
        if (!config.getParametersValue(SCTProperties.KEY).equals(key) || !config.getParametersValue(SCTProperties.SECRET).equals(secret)){
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public void authSCTWithAgencyId(String key, String secret, String id){
        if (!config.getParametersValue(SCTProperties.KEY).equals(key)
                || !config.getParametersValue(SCTProperties.SECRET).equals(secret)
                || !config.getParametersValue(SCTProperties.AGENCY_ID).equals(id)){
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    public void authBasicMC(String key, String secret){
        if (!config.getParametersValue(SCTProperties.MC_KEY).equals(key) || !config.getParametersValue(SCTProperties.MC_SECRET).equals(secret)){
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public FileUResDto[] uploadFile(ArrayList<FileInfo> file){
        String uploadUrl = microservice.filemanUri("/file/--multiple").toUriString();
        MultiValueMap<String, Object> requestMap = new LinkedMultiValueMap<>();
        for (FileInfo item : file) {
            //create body update
            byte[] bytes = Base64.getDecoder().decode(item.getBase64());
            ByteArrayResource byteArrayResource = new ByteArrayResource(bytes) {
                @Override
                public String getFilename() {
                    return item.getTenTepDinhKem();
                }
            };

            requestMap.add("files", byteArrayResource);
        }
        FileUResDto[] lstFile = MicroserviceExchange.postMultipartNoAuth(rest, uploadUrl, requestMap, FileUResDto[].class);
        return lstFile;
    }

    /**
     * Ý kiến xử lý
     * @param comment
     */
    @Async
    public void comment(String comment){

        String idUser = config.getParametersValue(SCTProperties.AGENCY_ID);
        String fullname = config.getParametersValue(SCTProperties.AGENCY_NAME);

        CommentSCTDto commentSCTDto = new CommentSCTDto();
        commentSCTDto.setItemId(dossier.getId());

        commentSCTDto.setUser( new UserInfo(new ObjectId(idUser), fullname));
        commentSCTDto.setContent(comment);

        String URL = microservice.messengerUri("comment").toUriString();
        String idComment = MicroserviceExchange.postJsonNoToken(rest, URL,commentSCTDto, String.class);
        logger.info("Saved comment");
    }

    /**
     * Log lịch sử
     */
    public void writeHistory(ArrayList<HistorySCTDto.Action> action){

        String idUser = config.getParametersValue(SCTProperties.AGENCY_ID);
        String fullname = config.getParametersValue(SCTProperties.AGENCY_NAME);

        HistorySCTDto historySCTDto = new HistorySCTDto();
        historySCTDto.setItemId(dossier.getId());
        historySCTDto.setUser(new UserInfo(new ObjectId(idUser), fullname));
        historySCTDto.setAction(action);
        String url = microservice.logmanUri("history").toUriString();
        String idHistory = MicroserviceExchange.postJsonNoAuth(rest, url, historySCTDto, String.class);
    }

    /**
     * Cập nhật trạng thái công việc hồ sơ
     * @param lsParamConfig
     * @return
     */
    public StatusDossierDto putStatusDossier(HashMap<String, Enum> lsParamConfig){

        StatusDossierDto statusDossierDto = this.createStatusDossier(lsParamConfig);
        statusDossierDto.setCode(dossier.getCode());
        statusDossierDto.setId(dossier.getId());
//        String urlStatusDossier = "http://localhost:8080/dossier-sctbd/status";
        return statusDossierDto;
    }

    public AffectedRowsDto updateStatus(StatusDossierDto statusDossierDto){
        String urlStatusDossier = microservice.padmanUri("/dossier-sctbd/status").toUriString();
//        String urlStatusDossier = "http://localhost:8080/dossier-sctbd/status" ;
        AffectedRowsDto row = MicroserviceExchange.putJsonNoAuth(rest, urlStatusDossier, statusDossierDto, AffectedRowsDto.class);
        return row;
    }
    /**
     * Lấy danh sách người dùng theo đơn vị
     * @param configId
     * @param deploymentId
     */
    public UserResDto getUsersByAgency(ObjectId configId, ObjectId deploymentId, UserInfoReqDto data){
        UserResDto res = new UserResDto();
        this.existConfig(configId, deploymentId);

        try{
            String urlAgency = microservice.basedataUri("/agency/name+code+parent+ancestor/--fully-by-code?code=" + data.getMaDonVi()).toUriString();
            logger.info(urlAgency);
            AgencyDto agency = MicroserviceExchange.getNoAuth(rest, urlAgency, AgencyDto.class);
            logger.info("Lấy thành công dữ liệu đơn vị");

            this.authSCTWithAgencyId(data.getKey(), data.getSecretkey(),agency.getId().toString());
            logger.info("Xác thực đúng mã định danh");

            String urlUser = microservice.humanUri("/user/search-not-pagination?agency-id=" + agency.getId()).toUriString();
//            String urlUser = "http://localhost:8080/user/search-not-pagination?agency-id=" + agency.getId();
            logger.info(urlUser);
            UserDto[] users = MicroserviceExchange.getNoAuth(rest, urlUser, UserDto[].class);
            logger.info("Lấy thành công user");
            ArrayList<UserTransformDto> lsUserTransform = new ArrayList<>();
            for (UserDto user : users ){
                UserTransformDto userTransformDto = new UserTransformDto();
                userTransformDto.setId(user.getId());
                userTransformDto.setHoTen(user.getFullname());
                userTransformDto.setGioiTinh(user.getGender());
                userTransformDto.setNgaySinh(user.getBirthday());
                if (user.getPhoneNumber() != null){
                    userTransformDto.setSoDienThoai(user.getPhoneNumber());
                }
                if (user.getEmail() != null) {
                    userTransformDto.setEmail(user.getEmail());
                }
                lsUserTransform.add(userTransformDto);
            }
            res.setCode(1);
            res.setMessage("Lấy dữ liệu thành công");
            res.setData(lsUserTransform);
        }catch (Exception e){
            res.setCode(-1);
            res.setMessage("lỗi lấy dữ liệu");
        }

        return res;
    }

    public boolean checkDossierBelongsAgency(DossierExtendsDto dossier){
        if (config.getParametersValue(SCTProperties.AGENCY_ID).equals(dossier.getAgency().getId().toString()))
            return true;
        if (config.getParametersValue(SCTProperties.AGENCY_ID).equals(dossier.getAgency().getParent().getId().toString()))
            return true;
        return false;
    }
}


