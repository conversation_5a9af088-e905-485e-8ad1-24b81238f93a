package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.dto.AffectedMessageDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.IntegrationParamsDto;
import vn.vnpt.digo.adapter.dto.SidNameDto;
import vn.vnpt.digo.adapter.dto.event_log.PostEventLogDto;
import vn.vnpt.digo.adapter.dto.minhtue.TokenResDto;
import vn.vnpt.digo.adapter.dto.minhtue.criminalrecords.*;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.IntegratedService;
import vn.vnpt.digo.adapter.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Date;
import java.util.HashMap;
import java.util.Base64;

@Service
@Component
public class LGSPMinhTueCriminalRecordsService {
    private ObjectId serviceId = new ObjectId("5f7c16069abb62f511891037");
    Logger logger = LoggerFactory.getLogger(LGSPMinhTueCriminalRecordsService.class);

    @Autowired
    private IntegratedConfigurationService configurationService;
    
    @Autowired
    private IntegratedLogsService integratedLogsService;

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;
    
    //lgsp minh tue 
    @Value(value = "${lgspminhtue.kgg.configId}")
    private String lgspKGGConfigId;

    @Autowired
    private EventLogService eventLogService;
    
    
    IntegratedConfigurationDto config;

//    private TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) throws Exception {
//        URL url = new URL(tokenUrl);
//        HttpURLConnection conn = (HttpURLConnection)url.openConnection();
//        String strConsumer = consumerKey + ":" + consumerSecret;
//        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
//        String strBase64Consumer = new String(base64Consumer);
//        String params = "grant_type=client_credentials";
//        String auth = "Basic " + strBase64Consumer;
//        conn.setRequestMethod("POST");
//        conn.setRequestProperty("ContentType", "application/x-www-form-urlencoded");
//        conn.setRequestProperty("Authorization", auth);
//        conn.setDoOutput(true);
//        DataOutputStream outStream = new DataOutputStream(conn.getOutputStream());
//        outStream.writeBytes(params);
//        outStream.flush();
//        outStream.close();
//        if (conn.getResponseCode() != 200) {
//            throw new Exception("Error " + conn.getResponseCode() + ": Failed to connect to Adapter");
//        } else {
//            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
//            StringBuffer responsedContent = new StringBuffer();
//
//            String bufferedLine;
//            while((bufferedLine = bufferedReader.readLine()) != null) {
//                responsedContent.append(bufferedLine);
//            }
//
//            bufferedReader.close();
//            conn.disconnect();
//            Gson gson = new Gson();
//            TokenResDto token = gson.fromJson(responsedContent.toString(), TokenResDto.class);
//            return token;
//        }
//    }

    private TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) {
        String strConsumer = consumerKey + ":" + consumerSecret;
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(tokenUrl);
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();

        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(auth);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST, request, Object.class);
            logger.info("Http result:");
            System.out.println(result);
            TokenResDto token = GsonUtils.copyObject(result.getBody(), TokenResDto.class);
            return token;
        } catch (Exception e) {
            logger.info("Error calling http: ", e.getMessage());
            throw new DigoHttpException(11003, new String[]{"get token LGSP Minh Tue:", e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    public CRSendResDto sendRecord(IntegrationParamsDto params, Map<String, Object> body, String code) throws Exception {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        logger.info("getConfig: ");
        System.out.println(config);
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
        ResponseEntity<String> result = null;
        
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<?> request = new HttpEntity<>(body, headers);
            result = restTemplate.exchange(
                    config.getParametersValue("lltpNhanHsDangKyUrl").toString(),
                    HttpMethod.POST, request, String.class);
            logger.info("Http result:");
            System.out.println(result);
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
            throw e;
        }
        
        try {
            //bo sung luu logs
            if(config.getId().toHexString().equals(lgspKGGConfigId)) {
                //luu log 

                IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                IntegratedService serviceNew = new IntegratedService(this.serviceId, "LGSP Minh Tue");
                configNew.setService(serviceNew);
                //save log
                integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(), "lltpNhanHsDangKyUrl", "Ly Lich Tu Phap"), 0, result == null ? "" : result.getBody(), new Gson().toJson(body));

            }
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
        }
        
        Gson gson = new Gson();
        CRSendResDto res = gson.fromJson(result.getBody(), CRSendResDto.class);
        //  set up response success
        Map<String, String> map = new HashMap<>();
        map.put("statusMessage", res.getDescription());
        map.put("statusCode", res.getStatus().toString());
        updateDossierLLTPStatus(map,code);
        //
        return res;
    }

    public List<CRDossierDto> receiveRecord(IntegrationParamsDto params) throws Exception {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        // get procedure info
        Map<String, Object> procedureParrams = new HashMap<>();
        procedureParrams.put("id", config.getParametersValue("crReveiveProcedure").toString());
        String procedureUrl = microservice.basepadUri("/procedure/").toUriString() + "{id}/--full";
        CRDossierDto.Procedure procedureInfo = MicroserviceExchange.get(this.restTemplate, procedureUrl,
                CRDossierDto.Procedure.class, procedureParrams);
        // get lltp
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        List<CRDossierDto> lstDossier = new ArrayList<>();
        // get lltp with type 1
        HashMap<String, Object> body = new HashMap<>();
        body.put("infoType", 1);
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<String> result = restTemplate.exchange(
                config.getParametersValue("lltpTraHsUrl").toString(), HttpMethod.POST,
                request, String.class);
        Gson gson = new Gson();
        CRResDto res = gson.fromJson(result.getBody(), CRResDto.class);
        if(!Objects.equals(res.getListContent(), "")){
            List<CRDossierRawDto> lstDossierType1;
            lstDossierType1 = gson.fromJson(res.getListContent(), new TypeToken<List<CRDossierRawDto>>() {}.getType());
            // fecth lltp dossier with type 1
            lstDossierType1.forEach(item -> {
                try{
                    CRDossierDto dossier = convertDossier(
                            config,
                            item,
                            procedureInfo,
                            new ObjectId(config.getParametersValue("cReveiveApplicantEform").toString()),
                            new ObjectId(config.getParametersValue("crReveiveEform").toString()),
                            "3"
                    );
                    lstDossier.add(dossier);
                } catch (Exception e){
                    logger.info(e.getMessage());
                }
            });
        }

        // get lltp with type 2
        HashMap<String, Object> body2 = new HashMap<>();
        body2.put("infoType", 2);
        HttpEntity<?> request2 = new HttpEntity<>(body2, headers);
        ResponseEntity<String> result2 = restTemplate.exchange(
                config.getParametersValue("lltpTraHsUrl").toString(), HttpMethod.POST,
                request2, String.class);
        CRResDto res2 = gson.fromJson(result2.getBody(), CRResDto.class);
        if(!Objects.equals(res2.getListContent(), "")){
            List<CRDossierRawDto> lstDossierType2;
            lstDossierType2 = gson.fromJson(res2.getListContent(), new TypeToken<List<CRDossierRawDto>>() {
            }.getType());
            // fecth lltp dossier with type 2
            lstDossierType2.forEach(item -> {
                try{
                    CRDossierDto dossier = convertDossier(
                            config,
                            item,
                            procedureInfo,
                            new ObjectId(config.getParametersValue("crReveiveApplicantEform").toString()),
                            new ObjectId(config.getParametersValue("crReveiveEform").toString()),
                            "1"
                    );
                    lstDossier.add(dossier);
                } catch (Exception e){
                    logger.info(e.getMessage());
                }
            });
        }
        return lstDossier;
    }

    private CRDossierDto convertDossier(IntegratedConfigurationDto config, CRDossierRawDto dossier, CRDossierDto.Procedure procedure,
                                        ObjectId applicantEformId, ObjectId eformId, String type) {
        CRDossierDto ret = new CRDossierDto();
        ret.setProcedure(procedure);
        ret.setDossierReceivingKind(new CRDossierDto.Kind(dossier.getDelivery()));
        if (Objects.nonNull(dossier.getDeclarationId())) {
            if (!Objects.equals(dossier.getDeclarationId(), "")) {
                ret.setSync(new CRDossierDto.Sync(dossier.getDeclarationId(), 101));
            }
        }
        if (Objects.nonNull(dossier.getDeclarationPortalId())) {
            if (!Objects.equals(dossier.getDeclarationPortalId(), "")) {
                ret.setSync(new CRDossierDto.Sync(dossier.getDeclarationPortalId(), 101));
            }
        }
        SimpleDateFormat formatDate = new SimpleDateFormat("dd/MM/yyyy");
        Date birthDate = new Date();
        try {
            birthDate = formatDate.parse(dossier.getBirthDateStr());
        } catch (Exception e) {
            logger.info("birthDate " + e.getMessage());
        }
        Date identityDate = new Date();
        try {
            identityDate = formatDate.parse(dossier.getBirthDateStr());
        } catch (Exception e) {
            logger.info("identityDate " + e.getMessage());
        }

        CRDossierDto.ApplicantEform applicantEform = new CRDossierDto.ApplicantEform(
                dossier.getBirthPlace(),
                dossier.getFullName(),
                dossier.getPhone(),
                dossier.getEmail(),
                dossier.getIdentifyNo(),
                birthDate,
                identityDate,
                Objects.equals(dossier.getGenderId(), "1") ? 1 : 0);
        CRDossierDto.Applicant applicant = new CRDossierDto.Applicant(applicantEformId, applicantEform);
        ret.setApplicant(applicant);
        if (Objects.equals(dossier.getDelivery(), "1")) {
            CRDossierDto.Place receivingPlace = new CRDossierDto.Place(
                    dossier.getDeliveryAddress());
            ret.setReceivingPlace(receivingPlace);
        }
        ret.setDossierTaskStatus(this.getStatus(config, dossier.getDecStatusId()));
        ret.setEForm(this.convertEform(dossier, eformId));
        ret.setInfoType(type);
        return ret;
    }

    private CRDossierDto.TaskStatus getStatus(IntegratedConfigurationDto config, String status) {
        CRDossierDto.TaskStatus ret = new CRDossierDto.TaskStatus();
        switch (status) {
            case "3": {
                ret = getTaskStatus(config, config.getParametersValue("crReveiveAcceptStatus").toString());
                break;
            }
            case "4": {
                ret = getTaskStatus(config, config.getParametersValue("crReveiveProcessingStatus").toString());
                break;
            }
            case "5": {
                ret = getTaskStatus(config, config.getParametersValue("crReveiveCompleteStatus").toString());
                break;
            }
            default: {
                ret = getTaskStatus(config, config.getParametersValue("crReveiveCompleteStatus").toString());
                break;
            }
        }
        return ret;
    }

    private CRDossierDto.TaskStatus getTaskStatus(IntegratedConfigurationDto config, String id) {
        CRDossierDto.TaskStatus ret = new CRDossierDto.TaskStatus();
        ret.setId(new ObjectId(id));

        String procedureUrl = microservice.basecatUri("/tag/").toUriString() + id;
        CRDossierDto.RawTag tag = MicroserviceExchange.get(this.restTemplate, procedureUrl, CRDossierDto.RawTag.class);

        ret.setName(tag.getTrans());
        return ret;
    }

    private CRDossierDto.Eform convertEform(CRDossierRawDto dossier, ObjectId efromId) {
        CRDossierDto.Eform ret = new CRDossierDto.Eform();
        ret.setId(efromId);

        CRDossierDto.DeclarationWsForm declarationForm = new CRDossierDto.DeclarationWsForm();
        declarationForm.setFullName(dossier.getFullName());
        declarationForm.setBirthDateStr(dossier.getBirthDateStr());
        declarationForm.setOtherName(dossier.getOtherName());
        declarationForm.setGenderId(dossier.getGenderId());
        declarationForm.setBirthPlace(dossier.getBirthPlace());
        declarationForm.setNationalityId(dossier.getNationalityId());
        declarationForm.setResidence(dossier.getResidence());
        declarationForm.setResidenceTemporary(dossier.getResidenceTemporary());
        declarationForm.setIdTypeId(dossier.getIdTypeId());
        declarationForm.setIdIssueDate(dossier.getIdIssueDate());
        declarationForm.setPhone(dossier.getPhone());
        declarationForm.setEthnicId(dossier.getEthnicId());
        declarationForm.setReRegionId(dossier.getReRegionId());
        declarationForm.setRtRegionId(dossier.getRtRegionId());
        declarationForm.setIdentifyNo(dossier.getIdentifyNo());
        declarationForm.setIdIssuePlace(dossier.getIdIssuePlace());
        declarationForm.setEmail(dossier.getEmail());
        declarationForm.setDadName(dossier.getDadName());
        declarationForm.setMomName(dossier.getMomName());
        declarationForm.setPartnerName(dossier.getPartnerName());
        declarationForm.setDadDob(dossier.getDadDob());
        declarationForm.setMomDob(dossier.getMomDob());
        declarationForm.setPartnerDob(dossier.getPartnerDob());
        declarationForm.setDeclareTypeId(dossier.getDeclareTypeId());
        declarationForm.setPurpose(dossier.getPurpose());
        declarationForm.setRequestQty(dossier.getRequestQty());
        declarationForm.setAgencyRequestId(dossier.getAgencyRequestId());
        declarationForm.setNote(dossier.getNote());
        declarationForm.setObjectRequestId(dossier.getObjectRequestId());
        declarationForm.setFormType(dossier.getFormType());
        declarationForm.setRequestQtyAdd(dossier.getRequestQtyAdd());
        declarationForm.setRegionRequestId(dossier.getRegionRequestId());
        declarationForm.setIsBanPosition(dossier.getIsBanPosition());
        declarationForm.setDelivery(dossier.getDelivery());
        declarationForm.setDeliveryAddress(dossier.getDeliveryAddress());
        declarationForm.setDeliveryDistrict(dossier.getDeliveryDistrict());

        List<CRDossierDto.ResidenceWSForm> residenceForm = new ArrayList<>();
        dossier.getLstResidence().forEach(item -> {
            CRDossierDto.ResidenceWSForm residence = new CRDossierDto.ResidenceWSForm();
            residence.setFromDateStr(item.getFromDateStr());
            residence.setToDateStr(item.getToDateStr());
            residence.setResidencePlace(item.getResidencePlace());
            residence.setJobName(item.getJobName());
            residence.setWorkPlace(item.getWorkPlace());
            residenceForm.add(residence);
        });

        CRDossierDto.MandatorWSForm mandatorForm = new CRDossierDto.MandatorWSForm();
        mandatorForm.setFullName(dossier.getMandator().getFullName());
        mandatorForm.setBirthDateStr(dossier.getMandator().getBirthDateStr());
        mandatorForm.setResidence(dossier.getMandator().getResidence());
        mandatorForm.setIdTypeId(dossier.getMandator().getIdTypeId());
        mandatorForm.setIdIssueDate(dossier.getMandator().getIdIssueDate());
        mandatorForm.setMandatorRelation(dossier.getMandator().getMandatorRelation());
        mandatorForm.setGenderId(dossier.getMandator().getGenderId());
        mandatorForm.setBirthPlaceId(dossier.getMandator().getBirthPlaceId());
        mandatorForm.setRegionId(dossier.getMandator().getRegionId());
        mandatorForm.setIdentifyNo(dossier.getMandator().getIdentifyNo());
        mandatorForm.setIdIssuePlace(dossier.getMandator().getIdIssuePlace());
        mandatorForm.setMandatorDate(dossier.getMandator().getMandatorDate());

        CRDossierDto.EformData data = new CRDossierDto.EformData(declarationForm, residenceForm, mandatorForm);
        ret.setData(data);
        return ret;
    }

    public List<CRStatusDto> receiveStatus(IntegrationParamsDto params) throws Exception {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        Gson gson = new Gson();
        List<CRStatusDto> lstStatus = new ArrayList<>();
        //get lltp status type 1
        HashMap<String, Object> body = new HashMap<>();
        body.put("infoType", 1);
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<String> result = restTemplate.exchange(
                config.getParametersValue("lltpTraDsTtHsUrl").toString(),
                HttpMethod.POST, request, String.class);
        CRResDto res = gson.fromJson(result.getBody(), CRResDto.class);
        if(!Objects.equals(res.getListContent(), "")){
            List<CRStatusDto> lstStatus1 = new ArrayList<>();
            lstStatus1 = gson.fromJson(res.getListContent(), new TypeToken<List<CRStatusDto>>() {}.getType());
            if(lstStatus1.size() > 0){
                lstStatus.addAll(lstStatus1);
            }
        }
        //get lltp status type 2
        HashMap<String, Object> body2 = new HashMap<>();
        body2.put("infoType", 2);
        HttpEntity<?> request2 = new HttpEntity<>(body2, headers);
        ResponseEntity<String> result2 = restTemplate.exchange(
                config.getParametersValue("lltpTraDsTtHsUrl").toString(),
                HttpMethod.POST, request2, String.class);
        CRResDto res2 = gson.fromJson(result2.getBody(), CRResDto.class);
        if(!Objects.equals(res2.getListContent(), "")){
            List<CRStatusDto> lstStatus2 = new ArrayList<>();
            lstStatus2 = gson.fromJson(res2.getListContent(), new TypeToken<List<CRStatusDto>>() {}.getType());
            if(lstStatus2.size() > 0){
                lstStatus.addAll(lstStatus2);
            }
        }
        lstStatus.forEach(item -> {
            try {
                CRDossierDto.TaskStatus status = this.getStatus(config, item.getDecStatusId());
                item.setStatus(status);
            } catch(Exception e){
                logger.info(e.getMessage());
            }
        });
        return lstStatus;
    }

    public CRSendResDto markAsDone(IntegrationParamsDto params, CRMarkAsDoneDto body) throws Exception {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<String> result = restTemplate.exchange(
                config.getParametersValue("lltpDanhDauHsThanhCongUrl"),
                HttpMethod.POST, request, String.class);
        Gson gson = new Gson();
        CRSendResDto res = gson.fromJson(result.getBody(), CRSendResDto.class);
        return res;
    }

    public CRSendResDto writeLogHTTP(HttpServletRequest request, IntegrationParamsDto params, Map<String, Object> body, String code, ObjectId logID) {
        //region event log init
        PostEventLogDto event = new PostEventLogDto();
        event.setRequestAdapter(request, body, "ad");
        //event.setRes(null);
        event.setPid(logID);
        event.setServiceId(this.serviceId);
        event.setKey(new SidNameDto("Code", code));
        //endregion event log init
        try {
            CRSendResDto response = new CRSendResDto();
            response = sendRecord(params, body, code);
            //region event log success (status)
            event.setStatus(true);
            event.setErrMsg(response.toString());
            eventLogService.addNew(event);
            //endregion event log success (status, message)
            logger.info("DIGO-Response: " + response.toString());
            return response;
        } catch (Exception e) {
            //region event log fail (status, message)
            event.setStatus(false);
            event.setErrMsg(e.getMessage());
            eventLogService.addNew(event);
            //endregion event log fail (status, message)
            // set up response error
            Map<String, String> map = new HashMap<>();
            map.put("statusMessage", translator.toLocale("lang.word.callLGSP"));
            map.put("statusCode", "-9999");
            updateDossierLLTPStatus(map,code);
            return new CRSendResDto(-1, e.getMessage(), "");
        }
    }

    public AffectedMessageDto updateDossierLLTPStatus(Map response , String code){
        String updateUrl = microservice.padmanUri("/dossierAutoSync/" + code + "/status-lltp").toUriString();
//        String updateUrl = "http://localhost:8080/dossierAutoSync/"+code+"/status-lltp";
        logger.info("DIGO-Response-URL: " + updateUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(response, headers);
        AffectedMessageDto result = restTemplate.exchange(updateUrl, HttpMethod.PUT, request, AffectedMessageDto.class).getBody();
        return result;
    }

    public CRResDto getCategory(IntegrationParamsDto params,String infoType) throws Exception {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HashMap<String, Object> body = new HashMap<>();
        body.put("infoType", infoType);
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<String> result = restTemplate.exchange(
                config.getParametersValue("traDanhMuc").toString(),
                HttpMethod.POST, request, String.class);
        Gson gson = new Gson();
        CRResDto res = gson.fromJson(result.getBody(), CRResDto.class); 
        return res;
    }
}
