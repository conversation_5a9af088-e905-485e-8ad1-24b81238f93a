
package vn.vnpt.digo.adapter.service;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import org.xml.sax.InputSource;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.event_log.EventLogRequest;
import vn.vnpt.digo.adapter.dto.event_log.PostEventLogDto;
import vn.vnpt.digo.adapter.dto.minhtue.TokenResDto;
import vn.vnpt.digo.adapter.dto.so_tnmt_agg.*;

import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Translator;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.XMLConstants;
import javax.xml.bind.*;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import javax.xml.parsers.SAXParserFactory;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.sax.SAXSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.*;

import java.lang.reflect.Type;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


import java.io.IOException;

import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import java.time.ZonedDateTime;
import java.time.ZoneId;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
public class SoTNMTAGGService {
    Logger logger = LoggerFactory.getLogger(SoTNMTAGGService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private EventLogService eventLogService;

    @Value(value = "${agesb.tnmt.configid}")
    private String configId;

    public AffectedRowsDto sendDossierToSoTNMT(HttpServletRequest request, SoTNMTDossierDto body) {
        AffectedRowsDto affectedRowsDto = new AffectedRowsDto();

        var config = configurationService.getConfig(new ObjectId(configId));
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String registerSoTNMTUrl = config.getParametersValue("url");


        TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);
        String auth = "Bearer " + token.getAccessToken();

        String xmlObject = buildXmlString(body);
        String formatXml = formatXml(xmlObject);

        // Create event log
        PostEventLogDto eventLogAGESB = createEventLog(new ObjectId(configId), body, registerSoTNMTUrl, auth, xmlObject);

        // Send request and process response
        ResponseStructureToAGESBDto response = sendRequestAndProcessResponse(registerSoTNMTUrl, formatXml, auth, eventLogAGESB);

        // Handle successful response
        if (response.getBody().getMessageResponse().getReceivedDate() != null) {
            updateStatus(body, new ObjectId(configId), affectedRowsDto);
        }

        return affectedRowsDto;
    }

    private TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) {
        String strConsumer = consumerKey + ":" + consumerSecret;
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(tokenUrl);
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();

        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(auth);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST, request, Object.class);
            //logger.info("Http result:");
            //System.out.println(result);
            return GsonUtils.copyObject(result.getBody(), TokenResDto.class);
        } catch (Exception e) {
            throw new DigoHttpException(10017, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    private String buildXmlString(SoTNMTDossierDto soTNMTDossierDto) {
        try{
            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
            Document doc = dBuilder.newDocument();

            ObjectMapper objectMapper = new ObjectMapper();

            // Lấy thời gian hiện tại theo múi giờ Việt Nam (Asia/Ho_Chi_Minh)
            ZonedDateTime now = ZonedDateTime.now(ZoneId.of("Asia/Ho_Chi_Minh"));
            long timestamp = now.toEpochSecond();  // Lấy timestamp dưới dạng seconds

            // Tạo các namespace
            Element envelope = doc.createElement("soapenv:Envelope");
            envelope.setAttribute("xmlns:soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
            envelope.setAttribute("xmlns:edx", "http://angiang.gov.vn/schemas/agesb/edXML");
            envelope.setAttribute("xmlns:ages", "http://angiang.gov.vn/schemas/agesb/agesbXML");
            envelope.setAttribute("xmlns:oneg", "http://angiang.gov.vn/schemas/agesb/onegateXML");
            doc.appendChild(envelope);

            Element header = doc.createElement("soapenv:Header");
            envelope.appendChild(header);

            Element messageHeader = doc.createElement("ns3:MessageHeader");
            messageHeader.setAttribute("xmlns:ns3", "http://angiang.gov.vn/schemas/agesb/edXML");
            header.appendChild(messageHeader);

            Element from = doc.createElement("ns3:From");
            messageHeader.appendChild(from);

            Element organId = doc.createElement("ns3:OrganId");
            organId.appendChild(doc.createTextNode(soTNMTDossierDto.getOrganId()));
            from.appendChild(organId);

            Element fieldCode = doc.createElement("ns2:FieldCode");
            fieldCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
            fieldCode.appendChild(doc.createTextNode(soTNMTDossierDto.getFieldCode()));
            messageHeader.appendChild(fieldCode);

            Element processCode = doc.createElement("ns2:ProcessCode");
            processCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
            processCode.appendChild(doc.createTextNode(soTNMTDossierDto.getProcessCode()));
            messageHeader.appendChild(processCode);

            // Tạo phần Body của SOAP message
            Element body = doc.createElement("soapenv:Body");
            envelope.appendChild(body);

            Element messageBody = doc.createElement("edx:MessageBody");
            body.appendChild(messageBody);

            Element businessData = doc.createElement("ns2:BusinessData");
            businessData.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
            businessData.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
            businessData.setAttribute("xsi:type", "ns2:BusinessData");
            messageBody.appendChild(businessData);

            Element thongTinHoSo = doc.createElement("ns1:ThongTinHoSo");
            thongTinHoSo.setAttribute("xmlns:ns1", "http://angiang.gov.vn/schemas/agesb/onegateXML");
            businessData.appendChild(thongTinHoSo);

            // Thêm các thành phần của XML tương ứng với thông tin hồ sơ
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:CVXuLy", soTNMTDossierDto.getThongTinHoSo().getCVXuLy()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DiaChiNguoiNop", soTNMTDossierDto.getThongTinHoSo().getDiaChiNguoiNop()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DienThoai", soTNMTDossierDto.getThongTinHoSo().getDienThoai()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:MaBNHS", soTNMTDossierDto.getThongTinHoSo().getMaBNHS()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:NgayHenTraHS", formatDateAGESB(soTNMTDossierDto.getThongTinHoSo().getNgayHenTraHS())));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:NgayNhanHS", formatDateAGESB(soTNMTDossierDto.getThongTinHoSo().getNgayNhanHS())));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:PhongBanXuLy", soTNMTDossierDto.getThongTinHoSo().getPhongBanXuLy()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:SoCMND", soTNMTDossierDto.getThongTinHoSo().getSoCMND()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenDVCong", soTNMTDossierDto.getThongTinHoSo().getTenDVCong()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenNguoiNop", soTNMTDossierDto.getThongTinHoSo().getTenNguoiNop()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenToChuc", soTNMTDossierDto.getThongTinHoSo().getTenToChuc()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TinhTrangHS", soTNMTDossierDto.getThongTinHoSo().getTinhTrangHS()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TrangThaiXuLy", soTNMTDossierDto.getThongTinHoSo().getTrangThaiXuLy()));
            thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DuLieuChuyenNganh", objectMapper.writeValueAsString(soTNMTDossierDto.getDuLieuChuyenNganh())));

            if(!soTNMTDossierDto.getListFileIds().isEmpty()){
                Element manifest = doc.createElement("ns2:Manifest");
                manifest.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
                manifest.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
                manifest.setAttribute("xsi:type", "ns2:Manifest");
                messageBody.appendChild(manifest);

                Element reference = doc.createElement("Reference");
                manifest.appendChild(reference);

                Element description = doc.createElement("Description");
                description.appendChild(doc.createTextNode(timestamp + "_" + soTNMTDossierDto.getThongTinHoSo().getMaBNHS()));
                reference.appendChild(description);

                Element name = doc.createElement("Name");
                name.appendChild(doc.createTextNode(timestamp + "_" + soTNMTDossierDto.getThongTinHoSo().getMaBNHS()));
                reference.appendChild(name);

                Element contentType = doc.createElement("ContentType");
                contentType.appendChild(doc.createTextNode("application/zip"));
                reference.appendChild(contentType);

                Element value = doc.createElement("Value");
                value.appendChild(doc.createTextNode(getFileAttachment(soTNMTDossierDto.getListFileIds(), timestamp + "_" + soTNMTDossierDto.getThongTinHoSo().getMaBNHS())));
                reference.appendChild(value);
            }

            StringWriter stringWriter = new StringWriter();
            // Chuyển đổi DOM thành chuỗi
            javax.xml.transform.TransformerFactory.newInstance().newTransformer().transform(new javax.xml.transform.dom.DOMSource(doc), new javax.xml.transform.stream.StreamResult(stringWriter));
            return stringWriter.toString().replace("&gt;", ">").replace("&lt;", "<");
        }catch (Exception ex){
            throw new DigoHttpException(10018, HttpServletResponse.SC_BAD_REQUEST);
        }
    }

    private Element createElementWithTextContent(Document doc, String elementName, String textContent) {
        Element element = doc.createElement(elementName);
        element.appendChild(doc.createTextNode(textContent));
        return element;
    }

    private String formatDateAGESB(String date) {
        try {
            TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
            DateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
            DateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            inputFormat.setTimeZone(timezone);
            outputFormat.setTimeZone(timezone);

            Date parsedDate = inputFormat.parse(date);
            return outputFormat.format(parsedDate);
        } catch (ParseException e) {
            throw new DigoHttpException(10015, HttpServletResponse.SC_BAD_REQUEST);
        }
    }

    private String getFileAttachment(ArrayList<SoTNMTDossierDto.FileAttachment> listFiles, String zipFileName) throws IOException {
        // Nén dữ liệu vào file ZIP
        zipFileName = zipFileName.replaceAll("[^a-zA-Z0-9_.-]", "");
        try (FileOutputStream fos = new FileOutputStream(zipFileName);
             ZipOutputStream zipOut = new ZipOutputStream(fos)) {
            for (var file : listFiles) {
                // Thêm mỗi file vào trong file zip
                byte[] fileContent = MicroserviceExchange.getFile(restTemplate, microservice.filemanUri("file/" + file.getId()).toUriString());
                ZipEntry zipEntry = new ZipEntry(file.getFileName());
                zipOut.putNextEntry(zipEntry);
                zipOut.write(fileContent);
                zipOut.closeEntry();
            }
        }

        // Đọc nội dung file ZIP vào mảng byte
        byte[] zipFileBytes;
        try (FileInputStream fis = new FileInputStream(zipFileName);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            zipFileBytes = baos.toByteArray();
        }

        // Mã hóa mảng byte thành Base64
        String base64EncodedZip = Base64.getEncoder().encodeToString(zipFileBytes);

        // Xóa file ZIP
        File zipFile = new File(zipFileName);
        if (zipFile.exists()) {
            var result = zipFile.delete();
            logger.info("Result delete: " + result);
        }
        return base64EncodedZip;
    }

    private String formatXml(String unformattedXml){
        try{
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            // Sử dụng Stream để chuyển đổi giữa các nguồn và đích
            StreamSource source = new StreamSource(new java.io.StringReader(unformattedXml));
            StringWriter resultWriter = new StringWriter();
            StreamResult result = new StreamResult(resultWriter);
            transformer.transform(source, result);
            return resultWriter.toString();
        }catch (Exception ex){
            throw new DigoHttpException(10018, HttpServletResponse.SC_BAD_REQUEST);
        }
    }

    private PostEventLogDto createEventLog(ObjectId configId, SoTNMTDossierDto body, String url, String auth, String xmlObject) {
        PostEventLogDto eventLog = new PostEventLogDto();
        eventLog.setServiceId(configId);
        eventLog.setKey(new SidNameDto("Code", body.getThongTinHoSo().getMaBNHS()));

        EventLogRequest request = new EventLogRequest();
        request.setUri(url);
        request.setMethod("POST");

        List<NameValueDto> headers = List.of(
                new NameValueDto("Content-Type", "text/xml"),
                new NameValueDto("Accept-Charset", "UTF-8"),
                new NameValueDto("SOAPAction", "http://angiang.gov.vn/operation/agesb/GuiHoSoMoi"),
                new NameValueDto("Authorization", auth)
        );

        request.setHeader(headers);
        request.setBody(xmlObject);
        eventLog.setReq(request);

        return eventLog;
    }

    private ResponseStructureToAGESBDto sendRequestAndProcessResponse(String url, String formattedXml, String auth, PostEventLogDto eventLog) {
        ResponseStructureToAGESBDto response;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "text/xml");
            headers.add("Accept-Charset", "UTF-8");
            headers.add("Authorization", auth);
            headers.add("SOAPAction", "http://angiang.gov.vn/operation/agesb/GuiHoSoMoi");

            HttpEntity<String> request = new HttpEntity<>(formattedXml, headers);
            ResponseEntity<String> result = restTemplate.exchange(url, HttpMethod.POST, request, String.class);

            eventLog.setStatus(true);
            eventLog.setErrMsg(result.getBody());
            eventLogService.addNewSoTNMTAGG(eventLog);

            JAXBContext jaxbContext = JAXBContext.newInstance(ResponseStructureToAGESBDto.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            StringReader reader = new StringReader(Objects.requireNonNull(result.getBody()));
            response = (ResponseStructureToAGESBDto) unmarshaller.unmarshal(reader);

            logger.info(response.toString());
        } catch (Exception ex) {
            eventLog.setStatus(false);
            eventLog.setErrMsg(ex.getMessage());
            eventLogService.addNewSoTNMTAGG(eventLog);
            throw new DigoHttpException(10020, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

        return response;
    }

    private void updateStatus(SoTNMTDossierDto body, ObjectId configId, AffectedRowsDto affectedRowsDto) {
        Map<String, String> map = Map.of(
                "maHoSo", body.getThongTinHoSo().getMaBNHS(),
                "tinhTrangHS", body.getThongTinHoSo().getTinhTrangHS(),
                "trangThaiXuLy", body.getThongTinHoSo().getTrangThaiXuLy(),
                "cvXuLy", body.getThongTinHoSo().getCVXuLy(),
                "phongBanXuLy", body.getThongTinHoSo().getPhongBanXuLy()
        );

        String updateUrl = microservice.padmanUri("/so-tnmt-agg/status").toUriString();
        //        String updateUrl = "http://localhost:8081" + "/so-tnmt-agg/status";
        PostEventLogDto logPadman = new PostEventLogDto();
        logPadman.setServiceId(configId);
        logPadman.setKey(new SidNameDto("Code", body.getThongTinHoSo().getMaBNHS()));

        try {
            HttpHeaders headers = initHeader();

            EventLogRequest eventLogRequestPadman = new EventLogRequest();
            eventLogRequestPadman.setUri(updateUrl);
            eventLogRequestPadman.setMethod("PUT");

            List<NameValueDto> headerPadman = List.of(
                    new NameValueDto("Content-Type", "application/json"),
                    new NameValueDto("Authorization", "Bearer " + Context.getJwtAuthenticationTokenValue())
            );

            eventLogRequestPadman.setHeader(headerPadman);
            eventLogRequestPadman.setBody(map);

            logPadman.setReq(eventLogRequestPadman);

            HttpEntity<?> request = new HttpEntity<>(map, headers);
            AffectedMessageDto result = restTemplate.exchange(updateUrl, HttpMethod.PUT, request, AffectedMessageDto.class).getBody();

            if (result != null && result.getAffectedRows() == 1) {
                logPadman.setStatus(true);
                logPadman.setErrMsg(result.toString());
                eventLogService.addNewSoTNMTAGG(logPadman);
                affectedRowsDto.setAffectedRows(1);
                affectedRowsDto.setMessage("Liên thông Sở Tài nguyên và Môi trường thành công");
            } else {
                throw new DigoHttpException(10021, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            logPadman.setStatus(false);
            logPadman.setErrMsg(e.getMessage());
            eventLogService.addNewSoTNMTAGG(logPadman);
            throw new DigoHttpException(10021, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    private HttpHeaders initHeader(){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        return headers;
    }


    public String updateStatusFromAGESB(HttpServletRequest request, String xml) throws Exception {
        ResponseUpdateStatusDto responseUpdateStatusDto = new ResponseUpdateStatusDto();
        ResponseUpdateStatusDto.Body body = new ResponseUpdateStatusDto.Body();
        ResponseUpdateStatusDto.Body.MessageResponse messageResponse = new ResponseUpdateStatusDto.Body.MessageResponse();

        RequestUpdateStatusDto reqUpdateStatus = parseXmlToObject(xml);

        PostEventLogDto eventLogAGESB = initLogReqAGESB(request, reqUpdateStatus, xml);

        Map<String, String> map = mapStatusFromConfig(reqUpdateStatus);
        String updateUrl = microservice.padmanUri("/so-tnmt-agg/status").toUriString();
//        String updateUrl = "http://localhost:8081" + "/so-tnmt-agg/status";
        PostEventLogDto logPadman = initLogReqPadman(updateUrl, reqUpdateStatus, map);
        try {
            HttpHeaders headers = initHeader();
            //Create request with body
            HttpEntity<?> request_pd = new HttpEntity<>(map, headers);
            AffectedMessageDto result = restTemplate.exchange(updateUrl, HttpMethod.PUT, request_pd, AffectedMessageDto.class).getBody();
            if (result != null && result.getAffectedRows() == 1) {
                logPadman.setStatus(true);
                logPadman.setErrMsg(result.toString());
                var resultLog = eventLogService.addNewSoTNMTAGG(logPadman);

                messageResponse.setMessageCode("urn:uuid:" + resultLog.getId());
                messageResponse.setReceivedDate(getCurrentDate());
                body.setMessageResponse(messageResponse);
                responseUpdateStatusDto.setBody(body);

                String xmlResponseESB =  convertObjectToStringXML(responseUpdateStatusDto);
                eventLogAGESB.setRes(xmlResponseESB);
                eventLogAGESB.setStatus(true);
                eventLogService.addNewSoTNMTAGG(eventLogAGESB);
                return xmlResponseESB;
            } else {
                messageResponse.setFaultcode("Update status dossier failed: ");
                throw new DigoHttpException(10021, new String[] {convertObjectToStringXML(responseUpdateStatusDto)}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            //Start Save log AGESB
            eventLogAGESB.setStatus(false);
            eventLogAGESB.setErrMsg(e.getMessage());
            eventLogService.addNewSoTNMTAGG(eventLogAGESB);
            //End Save log AGESB

            //Start Save log Padman
            logPadman.setStatus(false);
            logPadman.setErrMsg(e.getMessage());
            eventLogService.addNewSoTNMTAGG(logPadman);
            //End Save log Padman
            throw new DigoHttpException(10021, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }



    private PostEventLogDto initLogReqPadman(String updateUrl, RequestUpdateStatusDto reqUpdateStatus, Map<String, String> map){
        PostEventLogDto logPadman = new PostEventLogDto();
        logPadman.setServiceId(new ObjectId(configId));
        logPadman.setKey(new SidNameDto("Code", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getMaBNHS()));

        EventLogRequest eventLogRequestPadman = new EventLogRequest();
        eventLogRequestPadman.setUri(updateUrl);
        eventLogRequestPadman.setMethod("PUT");

        var headerPadman = new ArrayList<NameValueDto>();
        headerPadman.add(new NameValueDto("Content-Type", "application/json"));
        headerPadman.add(new NameValueDto("Authorization", "Bearer " + Context.getJwtAuthenticationTokenValue()));

        eventLogRequestPadman.setHeader(headerPadman);
        eventLogRequestPadman.setBody(map);

        logPadman.setReq(eventLogRequestPadman);

        return logPadman;
    }

    private PostEventLogDto initLogReqAGESB(HttpServletRequest request, RequestUpdateStatusDto reqUpdateStatus, String xml){
        //Create log when call api agesb
        PostEventLogDto eventLogAGESB = new PostEventLogDto();
        eventLogAGESB.setServiceId(new ObjectId(configId));

        EventLogRequest eventLogRequest = new EventLogRequest();
        eventLogRequest.setUri(request.getRequestURI());
        eventLogRequest.setMethod(request.getMethod());
        eventLogRequest.setBody(xml);
        eventLogAGESB.setReq(eventLogRequest);
        eventLogAGESB.setKey(new SidNameDto("Code", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getMaBNHS()));
        return eventLogAGESB;
    }


    private Map<String, String> mapStatusFromConfig(RequestUpdateStatusDto reqUpdateStatus) {
        var config = configurationService.getConfig(new ObjectId(configId));
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        Map<String, String> map = new HashMap<>();
        String trangThaiXuLy = reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getTrangThaiXuLy();

        // Mapping statuses based on configuration
        map.put("maHoSo", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getMaBNHS());
        map.put("tinhTrangHS", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getTinhTrangHS());
        map.put("cvXuLy", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getCvXuLy());
        map.put("phongBanXuLy", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getPhongBanXuLy());
        map.put("duLieuChuyenNganh", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getDuLieuChuyenNganh());

        // Chuyển đổi chuỗi JSON thành danh sách đối tượng
        Gson gson = new Gson();
        String json = config.getParametersValue("agesb-trangthai");
        Type trangThaiAGESBListType = new TypeToken<List<TrangThaiAGESBDto>>() {}.getType();
        List<TrangThaiAGESBDto> dsTrangThaiAGESB = gson.fromJson(json, trangThaiAGESBListType);
        map.put("trangThaiXuLy", findElementByGiaTri(dsTrangThaiAGESB, trangThaiXuLy));
        return map;
    }

    private static String findElementByGiaTri(List<TrangThaiAGESBDto> list, String giaTri) {
        for (TrangThaiAGESBDto element : list) {
            if (element.getGiaTri().equals(giaTri)) {
                return element.getTenTrangThai();
            }
        }
        return null; // Trả về null nếu không tìm thấy
    }

    private RequestUpdateStatusDto parseXmlToObject(String xml) throws Exception {
        SAXParserFactory spf = SAXParserFactory.newInstance();
        spf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        spf.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
        spf.setXIncludeAware(false);
        Source xmlSource = new SAXSource(spf.newSAXParser().getXMLReader(), new InputSource(new StringReader(xml)));
        JAXBContext jaxbContext = JAXBContext.newInstance(RequestUpdateStatusDto.class);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        return (RequestUpdateStatusDto) unmarshaller.unmarshal(xmlSource);
    }

    private String getCurrentDate() {
        Date date = new Date();
        TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dfCurrentDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dfCurrentDate.setTimeZone(timezone);
        return dfCurrentDate.format(date);
    }

    private String convertObjectToStringXML(ResponseUpdateStatusDto res){
        try{
            JAXBContext context = JAXBContext.newInstance(ResponseUpdateStatusDto.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);

            StringWriter sw = new StringWriter();
            marshaller.marshal(res, sw);
            return sw.toString();
        }catch (JAXBException e) {
            throw new RuntimeException(e);
        }
    }


}
