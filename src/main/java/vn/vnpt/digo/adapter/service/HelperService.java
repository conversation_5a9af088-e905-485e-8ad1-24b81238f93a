package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.*;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.ReplaceOptions;
import org.apache.kafka.common.protocol.types.Field;
import org.apache.poi.ss.formula.functions.T;
import org.bson.Document;
import org.bson.LazyBSONList;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.EformSyncLog;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.Integration.Connect;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.StringHelper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import javax.servlet.http.HttpServletResponse;
import java.security.MessageDigest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class HelperService {
    private static final String CURRENT_DB_NAME = "Adapter";
    public static final String FORMIO = "Formio";
    @Value("${digo.deploy-helper.enable}")
    private Boolean enable;

    @Value("${digo.deploy-helper.secret-key}")
    private String secretKey;

    @Value("${spring.data.mongodb.uri}")
    private String mongodbUri;

    @Autowired
    private AuthService authService;

    Logger logger = LoggerFactory.getLogger(HelperService.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Value("${spring.data.mongodb.uri}")
    private String mongoURI;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Value("${digo.microservice.gateway-url}")
    private String gatewayUrl;

    @Value("${digo.type.id.district1}")
    private String district1;
    @Value("${digo.type.id.district2}")
    private String district2;
    @Value("${db.eform-db}")
    private String eformDbName;

    private boolean replaceValues(Object obj, String oldText, String newText) {
        boolean isModified = false;

        if (obj instanceof BasicDBObject) {
            BasicDBObject dbObject = (BasicDBObject) obj;
            for (Map.Entry<String, Object> entry : dbObject.entrySet()) {
                Object value = entry.getValue();

                if (value instanceof String && value.equals(oldText)) {
                    dbObject.put(entry.getKey(), newText); // Thay thế giá trị
                    isModified = true;
                } else {
                    isModified |= replaceValues(value,oldText,newText); // Gọi đệ quy cho các giá trị lồng nhau
                }
            }
        } else if (obj instanceof BasicDBList) {
            BasicDBList dbList = (BasicDBList) obj;
            for (int i = 0; i < dbList.size(); i++) {
                Object value = dbList.get(i);

                if (value instanceof String && value.equals(oldText)) {
                    dbList.set(i, newText); // Thay thế giá trị trong danh sách
                    isModified = true;
                } else {
                    isModified |= replaceValues(value,oldText,newText); // Gọi đệ quy cho các giá trị lồng nhau
                }
            }
        }
        return isModified;
    }

    public Object setDeploymentId (String key, ObjectId newId, String testPrefix) {
        if (!enable) {
            logger.error("Chức năng đang đóng!");
            return "Chức năng đang đóng!";
        }
        logger.info("=== setDeploymentId ===");
        HashMap<String, Object> result = new HashMap<>();
        DBObject queryDBObject = new BasicDBObject("deploymentId", new BasicDBObject("$exists", true));
        DBObject updateDBObject = new BasicDBObject("$set", new BasicDBObject("deploymentId", newId));
        if (key.trim().equals(secretKey)) {
            logger.info("--- mongodbUri: " + mongodbUri);
            MongoClientURI uri = new MongoClientURI(mongodbUri);
            MongoClient mongoClient;
            try {
                mongoClient = new MongoClient(uri);
            } catch (Exception e) {
                return "Kết nối database thất bại!";
            }
            //Lấy dang sách database name
            List<String> databases = mongoClient.getDatabaseNames();
            logger.info("--- Databases size: " + databases.size());
            //Lấy danh sách collection
            for (int i=0; i<databases.size(); i++) {
                if (!testPrefix.isBlank() && !databases.get(i).contains(testPrefix)) continue;
                logger.info("\t--- Get database: " + databases.get(i));
                HashMap<String, Object> dbr = new HashMap<>();
                DB db = mongoClient.getDB(databases.get(i));
                var collections = db.getCollectionNames();
                logger.info("\t\t--- " + databases.get(i) + "'s collection size: " + collections.size());
                collections.forEach(collection -> {
                    try {
                        DBCollection dbCollection = db.getCollection(collection);
                        WriteResult writeResult = dbCollection.updateMulti(queryDBObject, updateDBObject);
                        logger.info("\t\t\t*** writeResult: " + writeResult);
                        dbr.put(collection, writeResult);
                    } catch (Exception e) {
                        dbr.put(collection, e.getMessage());
                    }
                });
                result.put(databases.get(i), dbr);
            }
            mongoClient.close();
        } else {
            logger.error("Key không hợp lệ!");
            return "Key không hợp lệ!";
        }

        return result;
    }

    public String replaceValues(String oldText, String newText){
        String message = null;
        if(!oldText.equals(newText)){
            MongoClientURI uri = new MongoClientURI(mongodbUri);
            MongoClient mongoClient = new MongoClient(uri);
            if(Objects.isNull(message)){
                List<String> dbNames = mongoClient.getDatabaseNames();
                //dbNames = dbNames.stream().filter(i->i.equals("svcAdapterDat")).collect(Collectors.toList());//hardcode
                Integer countDB = 0;
                Integer totalDB = dbNames.size();
                //dbNames = dbNames.stream().filter(i->List.of("svcHuman","svcSysman").contains(i)).collect(Collectors.toList());
                for (String dbName:dbNames){
                    DB db = mongoClient.getDB(dbName);
                    MongoDatabase database = mongoClient.getDatabase(dbName);
                    Set<String> colNames = db.getCollectionNames();
                    Integer countCol = 0;
                    Integer totalCol = colNames.size();
                    for (String colName:colNames){
                        MongoCollection<Document> collection = database.getCollection(colName);
                        FindIterable<Document> documents = collection.find();
                        Integer countDoc = 0;
                        for (Document document : documents) {
                            try {
                                String jsonString = document.toJson();

                                //Đổi domain api từ cũ sang mới
                                String updatedDocumentString = jsonString.replaceAll(oldText, newText);

                                //Chuyển đổi chuỗi thành Document để lưu ngược lại mongodb
                                Document updatedDocument = Document.parse(updatedDocumentString);

                                //Update dựa vào trường id
                                Document filter = new Document("_id", updatedDocument.get("_id"));

                                //Thực hiện lưu lại trường dữ liệu
                                collection.replaceOne(filter, updatedDocument);
                                String msg = "Đang quét database "+ dbName + "(" + countDB + "/"  + totalDB +"), collection " + colName + "(" + countCol + "/" + totalCol + "), phần tử thứ:" + countDoc + " thành công";
                                logger.info(msg);
                            } catch (Exception ex){
                                String msg = "Đang quét database "+ dbName + "(" + countDB + "/"  + totalDB +"), collection " + colName + "(" + countCol + "/" + totalCol + "), phần tử thứ:" + countDoc + " thất bại";
                                continue;
                            }
                            countDoc++;
                        }
                        countCol++;
                    }
                    countDB++;
                }
            }
        }
        return message;
    }

    private boolean isAgencyCode(String code, String sChar){
        String[] codes = code.split(sChar);
        if(codes.length!= 2){
            return false;
        }
        codes = code.split("\\.");
        for (String icode:codes){
            try{
                Integer.valueOf(icode.replaceAll(sChar,""));
            }catch (Exception ex){
                return false;
            }
        }
        return true;
    }

    private boolean isAgencyCode(String code){
        List<String> sChars = Arrays.asList("H","G");
        for (String sChar:sChars){
            if(isAgencyCode(code,sChar)){
                return true;
            }
        }
        return false;
    }

    private boolean isLengthValid(String str){
        boolean isValid = str.length() > 1 && str.length() <= 253;
        return isValid;
    }

    private List<String> extractIpDomains(String input){
        List<String> items = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\d{1,3}(?:\\.\\d{1,3}){3}(?::\\d{1,5})?|[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)+");
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String item = matcher.group();
            items.add(item);
        }
        items = items.stream().filter(i->!i.startsWith("vn.vnpt.digo.") && !isAgencyCode(i)).collect(Collectors.toList());
        return items;
    }

    @Cacheable(value = "getIpDomainInfo")
    public IpDomainInfo getIpDomainInfo(List<String> refixs, List<String> cmds){
        IpDomainInfo data = this.getNewIpDomainInfo(refixs, cmds);
        return data;
    }

    private List<String> getDatabasesOrCollections(List<String> cmds, Integer idx){
        List<String> items = new ArrayList<>();
        for (String cmd:cmds){
            try{
                String item = cmd.split("\\.")[idx];
                if(!items.contains(item)){
                    items.add(item);
                }
            }catch (Exception ex){}
        }
        return items;
    }

    private boolean contains(List<String> realDbNames, String dbName){
        for (String realDbName:realDbNames){
            if(dbName.endsWith(realDbName)){
                return true;
            }
        }
        return false;
    }

    private List<String> getRealDbNamesByRefix(List<String> refixs, List<String> realdbNames){
        List<String> items = new ArrayList<>();
        for (String irefix:refixs){
            for (String realdbName:realdbNames){
                String item = irefix + realdbName;
                items.add(item);
            }
        }
        return items;
    }

    public IpDomainInfo getNewIpDomainInfo(List<String> refixs, List<String> cmds){
        IpDomainInfo data = new IpDomainInfo();
        List<String> ipDomains = new ArrayList<>();
        List<DomainIpAddress> domainIpAddresses = new ArrayList<>();
        MongoClientURI uri = new MongoClientURI(mongodbUri);
        MongoClient mongoClient = new MongoClient(uri);
        List<String> dbNames = mongoClient.getDatabaseNames();
        if(Objects.nonNull(cmds) && cmds.size() > 0){
            List<String> realdbNames = this.getDatabasesOrCollections(cmds,0);
            if(Objects.isNull(refixs)||refixs.size()==0){
                dbNames = dbNames.stream().filter(i->this.contains(realdbNames,i)).collect(Collectors.toList());
            }else {
                List<String> fRealdbNames = this.getRealDbNamesByRefix(refixs,realdbNames);
                dbNames = dbNames.stream().filter(i->fRealdbNames.contains(i)).collect(Collectors.toList());
            }
        }

        for (String dbName:dbNames) {
            DB db = mongoClient.getDB(dbName);
            MongoDatabase database = mongoClient.getDatabase(dbName);
            Set<String> sColNames = db.getCollectionNames();
            List<String> colNames = new ArrayList<>(sColNames);
            if(Objects.nonNull(cmds) && cmds.size() > 0){
                List<String> realCols = this.getDatabasesOrCollections(cmds,1);
                colNames = colNames.stream().filter(i->realCols.contains(i)).collect(Collectors.toList());
            }
            for (String colName:colNames) {
                MongoCollection<Document> collection = database.getCollection(colName);
                FindIterable<Document> documents = collection.find();
                for (Document document : documents) {
                    Object objectId = document.get("_id");
                    String docid = objectId.toString();
                    String docs = document.toJson();
                    List<String> subIpDomains = this.extractIpDomains(docs);
                    if(subIpDomains.size() > 0){
                        DomainIpAddress domainIpAddress = new DomainIpAddress();
                        domainIpAddress.setDbname(dbName);
                        domainIpAddress.setColname(colName);
                        domainIpAddress.setDocid(docid);
                        domainIpAddress.setDomainIps(subIpDomains);
                        domainIpAddresses.add(domainIpAddress);
                        ipDomains.addAll(subIpDomains);
                    }
                }
            }
        }
        ipDomains = new ArrayList<>(new HashSet<>(ipDomains));
        List<FormTo> items = ipDomains.stream().map(i->new FormTo(i)).collect(Collectors.toList());
        items = items.stream().filter(i->isLengthValid(i.getFrom())).collect(Collectors.toList());
        data.setTrans(items);
        data.setInfo(domainIpAddresses);
        return data;
    }

    public IpDomainInfo getIpDomainInfo(boolean refresh, List<String> refixs, List<String> cmds){
        IpDomainInfo data = new IpDomainInfo();
        if(refresh){
            data = this.getNewIpDomainInfo(refixs, cmds);
        }else {
            data = this.getIpDomainInfo(refixs,cmds);
        }
        return data;
    }

    private FindIterable<Document>  getDocuments(MongoCollection<Document> collection,
                                                 List<String> cmds,
                                                 String colName){
        FindIterable<Document> documents = null;
        try{
            String regexColname = "." + colName + ".";
            String cmd = cmds.stream().filter(i->i.contains(regexColname)).collect(Collectors.toList()).get(0);
            String[] arr = cmd.split("\\.");
            if(arr.length==4 && colName.equals( arr[1])){
                ObjectId id = StringHelper.tryToBojectId(arr[3]);
                if(Objects.isNull(id)){
                    documents = collection.find(Filters.eq(arr[2], arr[3]));
                }else {
                    documents = collection.find(Filters.eq(arr[2], id));
                }
            }
        }catch (Exception ex){}
        if(Objects.isNull(documents)){
            documents = collection.find();
        }
        return documents;
    }

    public void replaceDomainOrIps(ReplaceDomainDto obj){
        MongoClientURI uri = new MongoClientURI(mongodbUri);
        List<String> cmds = obj.getCmds();
        List<FormTo> items = obj.getItems();
        List<String> refixs = obj.getRefixs();
        List<String> realdbNames = this.getDatabasesOrCollections(cmds,0);
        List<String> dbNames = this.getRealDbNamesByRefix(refixs,realdbNames);
        MongoClient mongoClient = new MongoClient(uri);
        for (String dbName:dbNames) {
            MongoDatabase database = mongoClient.getDatabase(dbName);
            List<String> colNames = this.getDatabasesOrCollections(cmds,1);
            for (String colName : colNames) {
                MongoCollection<Document> collection = database.getCollection(colName);
                FindIterable<Document> documents = this.getDocuments(collection,cmds,colName);
                for (Document document : documents) {
                    try{
                        Object objectId = document.get("_id");
                        String docs = document.toJson();
                        for (FormTo item:items){
                            if(docs.contains(item.getFrom())){
                                docs = docs.replaceAll(item.getFrom(),item.getTo());
                            }
                        }
                        Document ndocs = Document.parse(docs);
                        Document filter = new Document("_id", objectId);
                        collection.replaceOne(filter, ndocs);
                    }catch (Exception ex){}
                }
            }
        }
    }

    public void replaceDomainOrIps(ObjectId deploymentId,
                                   ReplaceDomainDto obj){
        Connect conn = this.authService.getConnect(deploymentId);
        String token = this.authService.getToken(conn.getDomainSSO(),conn.getClientID(),conn.getClientSecret());
        String URL = conn.getDomainAPI() + "/ad/helper/--replace-values?deployment-id=" + deploymentId;
        MicroserviceExchange.postByToken(restTemplate,URL,token,Object.class);
    }

    public Object getIpDomainInfo(ObjectId deploymentId, boolean refresh, List<String> refixs, List<String> cmds){
        Connect conn = this.authService.getConnect(deploymentId);
        String token = this.authService.getToken(conn.getDomainSSO(),conn.getClientID(),conn.getClientSecret());
        String URL = conn.getDomainAPI() + "/ad/helper/--find-domain-or-ips?deployment-id=" + deploymentId;
        URL += "&refresh=" + refresh + "&refixs="+ String.join(",",refixs);
        Object data = MicroserviceExchange.getByToken(restTemplate,URL,token,Object.class);
        return data;
    }

    public Object cloneEforms(HashMap<String, String> body) throws JsonProcessingException {
        AffectedRowsDto saveResults = new AffectedRowsDto();
        Integer savedCount = 0;

        //Bước 1: Gọi API lấy list thủ tục DVCQG
        String basepadURL = microservice.basepadUri("procedure/--get-procedure-codes").toUriString();
        List<String> procedureCodes = MicroserviceExchange.get(restTemplate, basepadURL, List.class);

        //Bước 2: Lấy token
        String sso = body.get("sso");
        String id = body.get("id");
        String sr = body.get("sr");
        String token = this.authService.getToken(sso, id, sr);

        //Bước 2: Gọi API lấy thông tin id eform theo thủ tục
        String api = body.get("api");
        List<DossierEformInfo> eformData = new ArrayList<>();
        for (String procedureCode : procedureCodes){
            String padmanURL = api + "/pa/dossier/--eform-info";
            List<String> bodyRequest = new ArrayList<>();
            bodyRequest.add(procedureCode);
            var eformInfoResult = MicroserviceExchange.postJsonBearAuth(restTemplate, padmanURL, token, bodyRequest, List.class);
            ObjectMapper objectMapper = new ObjectMapper();
            List<DossierEformInfo> eformInfoMappedList = objectMapper.convertValue(eformInfoResult, new TypeReference<List<DossierEformInfo>>() {});
            DossierEformInfo dossierEformInfo = eformInfoMappedList.get(0);
            eformData.add(dossierEformInfo);
        }

        //Bước 3: Lặp qua id eform trên để lấy dữ liệu eform
        String edb = body.get("eformdb");
        List<GetFullEformDto> result = new ArrayList<>();
        for (DossierEformInfo eform : eformData){
            if(Objects.isNull(eform.getCommonEformId()) && Objects.isNull(eform.getDetailEformId())){
                continue;
            }

            //Lấy dữ liệu eform
            String adapterURL = api + "/integration/review-eform/find-fully";
            List<String> bodyRequest = new ArrayList<>();

            if(Objects.nonNull(eform.getCommonEformId())){
                bodyRequest.add(eform.getCommonEformId().toHexString());
            }

            if(Objects.nonNull(eform.getDetailEformId())){
                bodyRequest.add(eform.getDetailEformId().toHexString());
            }

            var eformDataResult = MicroserviceExchange.postJsonBearAuth(restTemplate, adapterURL, token, bodyRequest, Object.class);
            ObjectMapper objectMapper = new ObjectMapper();
            List<GetFullEformDto> eformDataMappedList = objectMapper.convertValue(eformDataResult, new TypeReference<List<GetFullEformDto>>() {});

            //Bước 3.1: Lưu eform đã clone
            AffectedRowsDto saveEformResult = saveEform(eformDataMappedList, edb, api);
            savedCount = savedCount + saveEformResult.getAffectedRows();

            //Bước 3.2: Ghi log sau khi đã clone eform về
            EformSyncLog eformSyncLog = new EformSyncLog();
            eformSyncLog.setProceduceCode(eform.getProcedureCode());
            eformSyncLog.setCommonEformId(eform.getCommonEformId());
            eformSyncLog.setDetailEformId(eform.getDetailEformId());

            mongoTemplate.save(eformSyncLog);
        }

        saveResults.setAffectedRows(savedCount);
        saveResults.setMessage("Đã đồng bộ " + savedCount + " dữ liệu eform");

        return saveResults;
    }

    public AffectedRowsDto saveEform(List<GetFullEformDto> data,
                                     String edb,
                                     String api){
        AffectedRowsDto affectedRowsDto = new AffectedRowsDto();
        Integer inserted = 0;

        //Khởi tạo kết nối database
        MongoClientURI uri = new MongoClientURI(mongoURI);
        MongoClient mongoClient = new MongoClient(uri);

        //Kết nối với database và collection
        MongoDatabase database = mongoClient.getDatabase(edb);
        MongoCollection<Document> collection = database.getCollection("forms");

        for (GetFullEformDto eformData : data){
            try{
                String newMachineName = eformData.getMachineName();

                //Lấy ngày hiện tại
                LocalDateTime now = LocalDateTime.now();

                //Điều chỉnh định dạng
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ddMMyyyyHHmmss");

                //Chuyển ngày đã định dạng thành chuỗi
                String formatted = now.format(formatter);

                newMachineName = newMachineName + "_clone_" + formatted;
                eformData.setMachineName(newMachineName);

                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> map = mapper.convertValue(eformData, Map.class);
                Document doc = new Document(map);

                String jsonString = doc.toJson();

                //Đổi domain api từ cũ sang mới
                String updatedDocumentString = jsonString.replaceAll(api, gatewayUrl);

                //Chuyển đổi chuỗi thành Document để lưu ngược lại mongodb
                Document updatedDocument = Document.parse(updatedDocumentString);

                collection.insertOne(updatedDocument);

                inserted++;
            } catch (Exception ex){
                logger.info(ex.getMessage());
                continue;
            }
        }

        affectedRowsDto.setAffectedRows(inserted);
        affectedRowsDto.setMessage("Đã insert " + inserted + " dữ liệu");
        return affectedRowsDto;
    }

    public CloneEformDto checkAndCloneEformContainsDistrict(String id){
        CloneEformDto res = new CloneEformDto();
        if(id==null){
              throw new DigoHttpException(11010, new String[]{"Không tồn tại dbName"},  HttpServletResponse.SC_BAD_REQUEST);
        }
        /*String dbName = null;
        if(StringHelper.hasValue(dbNameForm.toString())){
            dbName = dbNameForm.toString();
        } else {
            dbName = eformDbName;
        }*/

        try {
            res.setId(new ObjectId(id));
            res.setCheck(0);
            ObjectId newId = null;
            String newMachineName = null;
            String newPath = null;
            String newName = null;
            String targetDbName = "";

            //Khởi tạo kết nối database
            MongoClientURI uri = new MongoClientURI(mongoURI);
            MongoClient mongoClient = new MongoClient(uri);
            if(StringHelper.hasValue(eformDbName.toString())){
              targetDbName = eformDbName.toString();
             } else {
              targetDbName = mongoTemplate.getDb().getName().replace(CURRENT_DB_NAME, FORMIO);
            }
            MongoDatabase database = mongoClient.getDatabase(targetDbName);


            //Kết nối với database và collection
           /* MongoDatabase database = mongoClient.getDatabase(dbName);*/
            MongoCollection<Document> collection = database.getCollection("forms");

            // Tìm document theo _id
            newId = compressToId(id,"genEformId");
            Document check = collection.find(Filters.eq("_id", newId)).first();
             // kiểm tra đã clone hay chưa
            if(check!=null){
                // Đã clone
                 res.setId(new ObjectId(check.get("_id").toString()));
                 res.setMachineName(check.get("machineName").toString());
                 res.setName(check.get("title").toString());
                 res.setCheck(2);
              // kiểm tra thằng con này
                 String componentsCheck = check.containsKey("components") && check.get("components") !=null ? check.get("components").toString().toUpperCase(Locale.ROOT) : "";
                if(((componentsCheck.contains("5ee304423167922ac55bea02")
                        || (district1!=null && componentsCheck.contains(district1))
                        || (district2!=null && componentsCheck.contains(district2)))
                        && componentsCheck.contains("ba/place/--search"))
                        || componentsCheck.contains("QUẬN") || componentsCheck.contains("HUYỆN")
                        || componentsCheck.contains("QUAN") || componentsCheck.contains("HUYEN")
                ){
                    // con vẫn còn quận huyện
                    res.setCheck(1);
                }
                 return res;
            }
            Document found = collection.find(Filters.eq("_id", new ObjectId(id))).first();
            if (found != null) {
               /* ObjectId oldId = found.getObjectId("_id");
                found.put("_id", oldId.toHexString());

                ObjectMapper mapper = new ObjectMapper();
                EFormCloneDto dto = mapper.convertValue(found, EFormCloneDto.class);
                System.out.println("Found document: " + dto.get_id() + ", " + dto.getComponents());

                // Thực hiện clone neu document la cap huyen
                String components = dto.getComponents().toString().toUpperCase(Locale.ROOT); */
                boolean checkClone = true;
                // eform con -> Không thực hiện clone -> vẫn check quận huyện
                if(found.containsKey("cloneFrom") && found.get("cloneFrom")!=null && found.get("cloneFrom")!=""){
                      checkClone = false;
                      res.setId(new ObjectId(found.get("_id").toString()));
                      res.setMachineName(null);
               }
                String components = found.containsKey("components") && found.get("components") !=null ? found.get("components").toString().toUpperCase(Locale.ROOT) : "";
                if(((components.contains("5ee304423167922ac55bea02")
                        || (district1!=null && components.contains(district1))
                        || (district2!=null && components.contains(district2)))
                        && components.contains("ba/place/--search"))
                        || components.contains("QUẬN") || components.contains("HUYỆN")
                        || components.contains("QUAN") || components.contains("HUYEN")
                ){
                    // có quận huyện
                     res.setCheck(1);
                    /*Map<String, Object> map = mapper.convertValue(dto, Map.class);
                    newId = compressToId(id,"genEformId");
                    newMachineName = map.get("machineName") + "IgnoreDistrict";
                    String newTitle = map.get("title") + " (Hai cấp)";
                    Document newDocument = collection.find(Filters.eq("_id", newId)).first();
                    map.put("_id", newId);
                    map.put("machineName", newMachineName);
                    map.put("title", newTitle);
                    map.put("cloneFrom", id);
                    Document updatedDocument = new Document(map);
                    collection.replaceOne(Filters.eq("_id", newId), updatedDocument, new ReplaceOptions().upsert(true));*/
                    if(checkClone==true){
                     Document documentChild = new Document(found);
                     Document documentParent = new Document(found);
                     // Xử lý clone
                     newMachineName = found.get("machineName") + "IgnoreDistrict";
                     newName = found.get("name") + "ignoredistrict";
                     newPath = found.get("path") + "ignoredistrict";
                     String newTitle = found.get("title") + " (Hai cấp)";
                     documentChild.put("_id", newId);
                     documentChild.put("machineName", newMachineName);
                     documentChild.put("name", newName);
                     documentChild.put("path", newPath);
                     documentChild.put("title", newTitle);
                     documentChild.put("cloneFrom", id);
                     collection.replaceOne(Filters.eq("_id", newId), documentChild, new ReplaceOptions().upsert(true));
                     res.setId(newId);
                     res.setMachineName(newMachineName);
                     res.setName(newTitle);
                    // Xử lý parent
                    documentParent.put("cloneTo", newId.toString());
                    collection.replaceOne(Filters.eq("_id", found.get("_id")), documentParent, new ReplaceOptions().upsert(true));
                    return res;
                    }
                }else{
                    // không có quận huyện
                    res.setCheck(2);
                }
            } else {
                throw new DigoHttpException(11010, new String[]{"id or database name does not exists"},  HttpServletResponse.SC_BAD_REQUEST);
            }
        }
        catch (DigoHttpException e){
            throw new DigoHttpException(11010, new String[]{Arrays.toString(e.getArguments())},  HttpServletResponse.SC_BAD_REQUEST);
        }
        catch (Exception ex){
            logger.info(ex.getMessage());
            throw new DigoHttpException(11010, new String[]{ex.getMessage()},  HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
        return res;
    }


    private static String extractDistributedCharacters(String input, int n) {
        if (input == null || input.length() < n) {
            throw new IllegalArgumentException("Chuỗi đầu vào không đủ dài");
        }

        int step = input.length() / n; // Khoảng cách bước nhảy
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < n; i++) {
            int index = (i * step + i) % input.length(); // Tính toán vị trí phân tán
            result.append(input.charAt(index));
        }
        String response = result.toString();
        return response;
    }

    private static long hashToTimestamp(String input) {
        long timestamp = 0;
        try {
            // Tạo đối tượng MessageDigest sử dụng thuật toán SHA-256
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // Tính toán giá trị hash
            byte[] hashBytes = digest.digest(input.getBytes());

            // Lấy 8 byte đầu tiên từ hash để chuyển thành số nguyên long
            long _timestamp = 0;
            for (int i = 0; i < 8; i++) {
                _timestamp = (_timestamp << 8) | (hashBytes[i] & 0xff);
            }

            // Chuyển giá trị về dương bằng cách lấy giá trị tuyệt đối
            _timestamp = Math.abs(_timestamp);

            // Giới hạn giá trị timestamp trong phạm vi hợp lệ
            long epoch = 1609459200000L; // Ngày 1/1/2021 00:00:00 GMT
            long maxRange = epoch + (365L * 10 * 24 * 60 * 60 * 1000); // 10 năm sau đó
            long ltimestamp = epoch + (_timestamp % (maxRange - epoch));

            timestamp = ltimestamp;

        } catch (Exception e) {
            long fixedTimestamp = java.time.Instant.parse("2025-01-01T00:00:00Z").getEpochSecond();
            timestamp = fixedTimestamp;
        }
        return timestamp;
    }

    public static ObjectId compressToId(String input, String key) {
        key = Objects.nonNull(key)?key:"";
        ObjectId oid = new ObjectId();
        try {
            // 4 byte đầu tiên: Timestamp
            long ltimestamp = hashToTimestamp(input);
            int timestamp = (int)ltimestamp;
            timestamp = Math.abs(timestamp);
            StringBuilder objectId = new StringBuilder(Integer.toHexString(timestamp));

            /// 5 byte tiếp theo: Hash từ input (dùng SHA-256)
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            String salt = "VNPT-IGATE-4.0@25-12-2025@14-00-PM@pvgiang";
            byte[] hashBytes = digest.digest((salt + input + key).getBytes());
            for (byte b : hashBytes) {
                objectId.append(String.format("%02x", b));
            }

            // Đảm bảo độ dài 24 ký tự
            //String id = objectId.substring(0, 24);
            String id = extractDistributedCharacters(objectId.toString(),24);
            oid = new ObjectId(id);
        } catch (Exception e) {}
        return oid;
    }
}
