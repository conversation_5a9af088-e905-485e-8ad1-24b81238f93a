package vn.vnpt.digo.adapter.service;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.sun.xml.bind.marshaller.CharacterEscapeHandler;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;
import vn.vnpt.digo.adapter.dto.AffectedMessageDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.NameValueDto;
import vn.vnpt.digo.adapter.dto.SidNameDto;
import vn.vnpt.digo.adapter.dto.event_log.EventLogRequest;
import vn.vnpt.digo.adapter.dto.event_log.PostEventLogDto;
import vn.vnpt.digo.adapter.dto.minhtue.TokenResDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Translator;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.*;
import java.lang.reflect.Type;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import vn.vnpt.digo.adapter.dto.powaco.*;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import vn.vnpt.digo.adapter.dto.PadPApplyDto.AttachmentDto;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;

@Service
public class PowacoService {

    @Value(value = "${agg.integratedpowaco.serviceid}")
    private ObjectId serviceId;

    @Value(value = "${agg.powaco.listStatus}")
    private String listStatus;

    Logger logger = LoggerFactory.getLogger(PowacoService.class);

    @Autowired
    private IntegratedLogsService integratedLogsService;

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private IntegratedConfigurationService configurationService;

    IntegratedConfigurationDto config;

    @Autowired
    private EventLogService eventLogService;

    @Value(value = "${agg.integratedpowaco.serviceid}")
    private String configId;


    public PowacoDossierResDto registerPowaco(PowacoDossierDto req) throws Exception
    {
        logger.info("---------------------------START_GET_REGISTER_Powaco_AG_ESB---------------------------");

        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String registerPowacoUrl  = config.getParametersValue("url");
        String token  = config.getParametersValue("access_token");

        return powacoDto(registerPowacoUrl, token, req);
    }

    private PowacoDossierResDto sendRequest(String url, String formattedXml, String auth) {
        PowacoDossierResDto objreturn = new PowacoDossierResDto();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "text/xml");
            headers.add("Accept-Charset", "UTF-8");
            headers.add("Authorization", auth);
            headers.add("SOAPAction", "http://angiang.gov.vn/operation/agesb/GuiHoSoMoi");

            HttpEntity<String> request = new HttpEntity<>(formattedXml, headers);
            ResponseEntity<Object> result = restTemplate.exchange(url, HttpMethod.POST, request, Object.class);
            if (result.getStatusCodeValue() == 1){
                objreturn.setStatus(result.getStatusCodeValue());
                String errorCode = "0";
                objreturn.setErrorCode(errorCode);
                objreturn.setStatusDescription(result.getBody().toString());
                objreturn.setErrorDescription("");
            } else {
                objreturn.setStatus(result.getStatusCodeValue());
                String errorCode = "500";
                objreturn.setErrorCode(errorCode);
                objreturn.setStatusDescription("Gửi hồ sơ liên thông thất bại");
                objreturn.setErrorDescription(result.getBody().toString());
            }
        } catch (HttpServerErrorException ex) {
            Integer indexTemp = 0;

            objreturn.setStatus(ex.getRawStatusCode());
            objreturn.setErrorCode(ex.getStatusCode().toString());
            indexTemp = ex.getMessage().indexOf(":");
            String messageTemp = ex.getMessage().substring(indexTemp + 3, ex.getMessage().length() - 1);
            objreturn.setStatusDescription(messageTemp);
            objreturn.setErrorDescription(ex.getMessage());
        }
        return objreturn;
    }

    private PowacoDossierResDto powacoDto(String apiUrl, String access_token, PowacoDossierDto powacoDto) throws IOException {
        PowacoDossierResDto objreturn = new PowacoDossierResDto();
        URL url = new URL(apiUrl);
        String auth = "Bearer " + access_token;
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        try {
            String xmlObject = buildXmlString(powacoDto);
            String formatXml=formatXml(xmlObject);
            objreturn = sendRequest(apiUrl, formatXml, auth);
            if (objreturn.getStatus() == 1){
                objreturn.setStatusDescription("Liên thông powaco thành công!");
                setXmlResponseDetails(objreturn,powacoDto);
            } else {
                objreturn.setStatusDescription("Liên thông powaco thất bại!");
                setXmlResponseDetails(objreturn,powacoDto);
            }

        } catch (Exception e) {
            return objreturn;
        }

        return objreturn;
    }
    public TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) {
        String strConsumer = consumerKey + ":" + consumerSecret;
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(tokenUrl);
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();

        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(auth);

            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST, request, Object.class);
            logger.info("Http result:");
            System.out.println(result);
            TokenResDto token = GsonUtils.copyObject(result.getBody(), TokenResDto.class);
            return token;
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
            throw new DigoHttpException(11003, new String[]{"get token AG ESB:", e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    private void configureConnection(HttpURLConnection connection,String auth) throws ProtocolException {
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "text/xml");
        connection.setRequestProperty("Accept-Charset", "UTF-8");
        connection.setRequestProperty("SOAPAction", "http://angiang.gov.vn/operation/agesb/GuiHoSoMoi");
        connection.setRequestProperty("Authorization", auth);
        connection.setDoOutput(true);
        connection.setDoInput(true);
    }

    private String buildXmlString(PowacoDossierDto powacoDto) throws Exception {
        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
        Document doc = dBuilder.newDocument();

        // Tạo các namespace
        Element envelope = doc.createElement("soapenv:Envelope");
        envelope.setAttribute("xmlns:soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
        envelope.setAttribute("xmlns:edx", "http://angiang.gov.vn/schemas/agesb/edXML");
        envelope.setAttribute("xmlns:ages", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        envelope.setAttribute("xmlns:oneg", "http://angiang.gov.vn/schemas/agesb/onegateXML");
        doc.appendChild(envelope);

        Element header = doc.createElement("soapenv:Header");
        envelope.appendChild(header);

        Element messageHeader = doc.createElement("ns3:MessageHeader");
        messageHeader.setAttribute("xmlns:ns3", "http://angiang.gov.vn/schemas/agesb/edXML");
        header.appendChild(messageHeader);

        Element from = doc.createElement("ns3:From");
        messageHeader.appendChild(from);

        Element organId = doc.createElement("ns3:OrganId");
        organId.appendChild(doc.createTextNode("28"));
        from.appendChild(organId);

        Element typeCode = doc.createElement("ns2:TypeCode");
        typeCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        typeCode.appendChild(doc.createTextNode("01"));
        messageHeader.appendChild(typeCode);

        Element fieldCode = doc.createElement("ns2:FieldCode");
        fieldCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        fieldCode.appendChild(doc.createTextNode("62"));
        messageHeader.appendChild(fieldCode);

        Element processCode = doc.createElement("ns2:ProcessCode");
        processCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        processCode.appendChild(doc.createTextNode("97"));
        messageHeader.appendChild(processCode);

        // Tạo phần Body của SOAP message
        Element body = doc.createElement("soapenv:Body");
        envelope.appendChild(body);

        Element messageBody = doc.createElement("edx:MessageBody");
        body.appendChild(messageBody);

        Element businessData = doc.createElement("ns2:BusinessData");
        businessData.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        businessData.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
        businessData.setAttribute("xsi:type", "ns2:BusinessData");
        messageBody.appendChild(businessData);

        Element thongTinHoSo = doc.createElement("ns1:ThongTinHoSo");
        thongTinHoSo.setAttribute("xmlns:ns1", "http://angiang.gov.vn/schemas/agesb/onegateXML");
        businessData.appendChild(thongTinHoSo);

        // Thêm các thành phần của XML tương ứng với thông tin hồ sơ
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:MaBNHS", powacoDto.getMaHoSo()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenToChuc", ""));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenNguoiNop", powacoDto.getTenNguoiNop()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DiaChiNguoiNop", powacoDto.getDiaChiNguoiNop()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:SoCMND", powacoDto.getSoCMND()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DienThoai", powacoDto.getDienThoai()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenDVCong", powacoDto.getTenDichVuCong()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:NgayNhanHS", convertDateFormat(powacoDto.getNgayNhanHS())));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:NgayHenTraHS", convertDateFormat(powacoDto.getNgayHenTraHS())));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TinhTrangHS", powacoDto.getTinhTrangHS()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TrangThaiXuLy", powacoDto.getTrangThaiXuLy()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:PhongBanXuLy", powacoDto.getPhongBanXuLy()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:CVXuLy", powacoDto.getCanBoXuLy()));
        String lstFileChuyenNganh = thongTinChuyenNganh(powacoDto);
        String dataTemp = "";
        // Thêm phần DuLieuChuyenNganh
        String duLieuChuyenNganhContent = "{\n" +
                "\"soBienNhan\": \"" + powacoDto.getMaHoSo() + "\",\n" +
                "\"ngayNhanHS\": \"" + convertDateFormat(powacoDto.getNgayNhanHS()) + "\",\n" +
                "\"ngayHenTra\": \"" + convertDateFormat(powacoDto.getNgayHenTraHS()) + "\",\n" +
                "\"maThuTuc\": \"" + powacoDto.getMaThuTuc() + "\",\n" +
                "\"thongTinNguoiNopDon\": {\n" +
                "\"hoTen\": \"" + powacoDto.getTenNguoiNop() + "\",\n" +
                "\"gioiTinh\": \"" + powacoDto.getGioiTinhNguoiNop() + "\",\n" +
                "\"ngaySinh\": \"" + convertDateFormat(powacoDto.getNgaySinhNguoiNop()) + "\",\n" +
                "\"nationalID\": \"" + powacoDto.getCmndNguoiNop() + "\",\n" +
                "\"ngayCap\": \"" + convertDateFormat(powacoDto.getNgayCapCMNDNguoiNop()) + "\",\n" +
                "\"noiCap\": \"" + powacoDto.getNoiCapCMNDNguoiNop() + "\",\n" +
                "\"sdt\": \"" + powacoDto.getSdtNguoiNop() + "\",\n" +
                "\"tinhId\": \"" + powacoDto.getTinhIdNguoiNop() + "\",\n" +
                "\"huyenId\": \"" + powacoDto.getHuyenIdNguoiNop() + "\",\n" +
                "\"xaId\": \"" + powacoDto.getXaIdNguoiNop() + "\",\n" +
                "\"soNha\": \"" + powacoDto.getSoNhaNguoiNop() + "\",\n" +
                "\"duongPho\": \"" + powacoDto.getDuongPhoNguoiNop() + "\"},\n" +
                "\"thongTinDiaChiLapDat\": {\n" +
                "\"tinhIdLD\": \"" + powacoDto.getTinhIdLapDat() + "\",\n" +
                "\"huyenIdLD\": \"" + powacoDto.getHuyenIdLapDat() + "\",\n" +
                "\"xaIdLD\": \"" + powacoDto.getXaIdLapDat() + "\",\n" +
                "\"soNhaLD\": \"" + powacoDto.getSoNhaLapDat() + "\",\n" +
                "\"duongPhoLD\": \"" + powacoDto.getDuongPhoLapDat() + "\"},\n" +
                "\"thongTinChuyenNganh\": [" +
                lstFileChuyenNganh +
                "]," +
                "\"maMDSD\": \"" + powacoDto.getMaMDSD() + "\",\n" +
                "\"moTaMDSD\": \"" + powacoDto.getMoTaMDSD() + "\" \n"+
                "}";

        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DuLieuChuyenNganh", duLieuChuyenNganhContent));
        StringWriter stringWriter = new StringWriter();
        // Chuyển đổi DOM thành chuỗi
        javax.xml.transform.TransformerFactory.newInstance().newTransformer().transform(new javax.xml.transform.dom.DOMSource(doc), new javax.xml.transform.stream.StreamResult(stringWriter));
        return stringWriter.toString().replace("&gt;", ">").replace("&lt;", "<");
    }

    private static String convertDateFormat(Date value) {
        if (value != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return dateFormat.format(value);
        } else {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return dateFormat.format(new Date());
        }
    }

    private String thongTinChuyenNganh(PowacoDossierDto dto){
        StringBuilder stringResult = new StringBuilder();
        dto.getDanhSachGiayTo().forEach(item ->{
            List<AttachmentDto> file = item.getFile();
            byte[] fileContent = MicroserviceExchange.getFile(restTemplate, microservice.filemanUri("file/" + file.get(0).getId()).toUriString());
            String jsonTemp = "";
            jsonTemp = "{\n" +
                    "\"tenGiayTo\": \"" + item.getProcedureForm().getName() + "\",\n" +
                    "\"dsGiayToDinhKem\": {\n" +
                    "\"name\": \"" + file.get(0).getFilename() + "\",\n" +
                    "\"data\": \"" + Base64.getEncoder().encodeToString(fileContent) + "\"}\n" +
                    "}";
            if (stringResult.length() > 0) {
                stringResult.append(",\n");
            }
            stringResult.append(jsonTemp);
        });
        return stringResult.toString();
    }

    private String formatXml(String unformattedXml) throws Exception {
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        // Sử dụng Stream để chuyển đổi giữa các nguồn và đích
        StreamSource source = new StreamSource(new java.io.StringReader(unformattedXml));
        StringWriter resultWriter = new StringWriter();
        StreamResult result = new StreamResult(resultWriter);

        transformer.transform(source, result);

        return resultWriter.toString();
    }
    private Element createElementWithTextContent(Document doc, String elementName, String textContent) {
        Element element = doc.createElement(elementName);
        element.appendChild(doc.createTextNode(textContent));
        return element;
    }

    private void setResponseDetails(PowacoDossierResDto objreturn, JsonObject jsonObj2) {
        objreturn.setStatus(jsonObj2.getAsJsonPrimitive("status").getAsInt());
        objreturn.setStatusDescription(jsonObj2.getAsJsonPrimitive("statusDescription").getAsString());
        objreturn.setErrorCode(jsonObj2.getAsJsonPrimitive("errorCode").getAsString());
        objreturn.setErrorDescription(jsonObj2.getAsJsonPrimitive("errorDescription").getAsString());
    }

    private void setErrorResponseDetails(PowacoDossierResDto objreturn, JsonObject jsonObj2, PowacoDossierDto powacoDto) {
        objreturn.setStatus(jsonObj2.getAsJsonPrimitive("status").getAsInt());
        objreturn.setValue("");
        objreturn.setStatusDescription(jsonObj2.getAsJsonPrimitive("statusDescription").getAsString());
        objreturn.setErrorCode("");
        objreturn.setErrorDescription("");

        // Set response success
        Map<String, String> map = Map.of("statusMessage", jsonObj2.getAsJsonPrimitive("statusDescription").toString(),
                "statusCode", String.valueOf(jsonObj2.getAsJsonPrimitive("status").getAsInt()));
        updateDossierStatus(map, powacoDto.getMaHoSo());
    }

    private void handleException(Exception exception, PowacoDossierResDto objreturn, PowacoDossierDto powacoDto) throws IOException {
        logger.error(exception.getMessage());
        objreturn.setStatus(-1);
        objreturn.setValue(powacoDto.getData());
        objreturn.setErrorDescription(exception.getMessage());
        throw new IOException(exception);
    }

    public PowacoDossierResDto writeLogPowaco(HttpServletRequest request, PowacoDossierDto body, String code, ObjectId logID, String configId)
    {
        PostEventLogDto event = new PostEventLogDto();
        event.setRequestAdapter(request, body, "ad");
        event.setPid(logID);
        event.setServiceId(this.serviceId);
        event.setKey(new SidNameDto("Code", code));
        try {
            PowacoDossierResDto response = new PowacoDossierResDto();
            response = registerPowaco(body);
            event.setErrMsg(response.getErrorDescription());
            eventLogService.addNewSoTNMTAGG(event);
            return response;
        } catch (Exception e) {
            Map<String, String> map = new HashMap<>();
            map.put("statusMessage", "Có lỗi khi gửi sang trục AG ESB!");
            map.put("statusCode", "-9999");
            updateDossierStatus(map,code);
            PowacoDossierResDto response = new PowacoDossierResDto();
            response.setStatus(500);
            response.setErrorDescription("Liên thông powaco thất bại!");
            return response;
        }
    }

    public AffectedMessageDto updateDossierStatus(Map response , String code){
        String updateUrl = microservice.padmanUri("/powaco/" + code + "/status").toUriString();
//        String updateUrl = "http://localhost:8081/powaco/" + code + "/status";
        logger.error("-----Update status dossier: " + updateUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(response, headers);
        AffectedMessageDto result = restTemplate.exchange(updateUrl, HttpMethod.PUT, request, AffectedMessageDto.class).getBody();
        return result;
    }
    private void processXmlResponse(String xmlResponse, PowacoDossierResDto objreturn, PowacoDossierDto powacoDto) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            InputSource is = new InputSource(new StringReader(xmlResponse));
            Document document = builder.parse(is);
            boolean hasFault = document.getElementsByTagName("soapenv:Fault").getLength() > 0;
            Integer sts;
            Element messageResponse = (Element) document.getElementsByTagName("m0:MessageResponse").item(0);
            sts = hasFault ? 0 : 1;
            if (objreturn.getErrorCode() != "0"){
                sts = 0;
            }
            if(sts==0)
            {
                setXmlErrorResponseDetails(objreturn,messageResponse,powacoDto);
            } else if (sts==1)
            {
                setXmlResponseDetails(objreturn,powacoDto);
            }

        } catch (Exception e) {
            // Handle XML parsing exceptions
            e.printStackTrace();
        }
    }

    private String getElementTextContent(Element parentElement, String tagName) {
        return parentElement.getElementsByTagName(tagName).item(0).getTextContent();
    }

    private void setXmlResponseDetails(PowacoDossierResDto objreturn, PowacoDossierDto powacoDto) {
        Map<String, String> map = new HashMap<>();
        map.put("statusMessage", objreturn.getStatusDescription());
        Integer status = objreturn.getStatus();
        map.put("statusCode", status.toString());
        updateDossierStatus(map,powacoDto.getMaHoSo());
        // Add other elements as needed
    }

    private void setXmlErrorResponseDetails(PowacoDossierResDto objreturn, Element messageResponse, PowacoDossierDto powacoDto) {
        objreturn.setStatus(objreturn.getStatus());
        objreturn.setStatusDescription("Liên thông Powaco thất bại");
        objreturn.setErrorCode(getElementTextContent(messageResponse, "faultcode"));
        objreturn.setErrorDescription("");

        // Set response success
        Map<String, String> map = Map.of("statusMessage", getElementTextContent(messageResponse, "m:ReceivedDate"),
                "statusCode", String.valueOf(Integer.parseInt(getElementTextContent(messageResponse, "m:MessageCode"))));
        updateDossierStatus(map, powacoDto.getMaHoSo());
    }

    private HttpHeaders initHeader(){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        return headers;
    }

    public String updateStatusFromAGESB(HttpServletRequest request, String xml) throws JAXBException {
        ResponseUpdateStatusDto responseUpdateStatusDto = new ResponseUpdateStatusDto();
        ResponseUpdateStatusDto.Body body = new ResponseUpdateStatusDto.Body();
        ResponseUpdateStatusDto.Body.MessageResponse messageResponse = new ResponseUpdateStatusDto.Body.MessageResponse();

        RequestUpdateStatusDto reqUpdateStatus = parseXmlToObject(xml);

        PostEventLogDto eventLogAGESB = initLogReqAGESB(request, reqUpdateStatus, xml);

        Map<String, String> map = mapStatusFromConfig(reqUpdateStatus);
        String updateUrl = microservice.padmanUri("/powaco/status").toUriString();
        PostEventLogDto logPadman = initLogReqPadman(updateUrl, reqUpdateStatus, map);
        try {
            HttpHeaders headers = initHeader();
            //Create request with body
            HttpEntity<?> request_pd = new HttpEntity<>(map, headers);
            AffectedMessageDto result = restTemplate.exchange(updateUrl, HttpMethod.PUT, request_pd, AffectedMessageDto.class).getBody();
            if (result != null && result.getAffectedRows() == 1) {
                logPadman.setStatus(true);
                logPadman.setErrMsg(result.toString());
                var resultLog = eventLogService.addNewSoTNMTAGG(logPadman);

                messageResponse.setMessageCode("urn:uuid:" + resultLog.getId());
                messageResponse.setReceivedDate(getCurrentDate());
                body.setMessageResponse(messageResponse);
                responseUpdateStatusDto.setBody(body);

                String xmlResponseESB =  convertObjectToStringXML(responseUpdateStatusDto);
                eventLogAGESB.setRes(xmlResponseESB);
                eventLogAGESB.setStatus(true);
                eventLogService.addNewSoTNMTAGG(eventLogAGESB);
                return xmlResponseESB;
            } else {
                messageResponse.setFaultcode("Update status dossier failed: ");
                return result.getMessage();
//                throw new DigoHttpException(10021, new String[] {convertObjectToStringXML(responseUpdateStatusDto)}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            //Start Save log AGESB
            eventLogAGESB.setStatus(false);
            eventLogAGESB.setErrMsg(e.getMessage());
            eventLogService.addNewSoTNMTAGG(eventLogAGESB);
            //End Save log AGESB

            //Start Save log Padman
            logPadman.setStatus(false);
            logPadman.setErrMsg(e.getMessage());
            eventLogService.addNewSoTNMTAGG(logPadman);
            //End Save log Padman
            throw new DigoHttpException(10021, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    private PostEventLogDto initLogReqPadman(String updateUrl, RequestUpdateStatusDto reqUpdateStatus, Map<String, String> map){
        PostEventLogDto logPadman = new PostEventLogDto();
        logPadman.setServiceId(new ObjectId(configId));
        logPadman.setKey(new SidNameDto("Code", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getMaBNHS()));

        EventLogRequest eventLogRequestPadman = new EventLogRequest();
        eventLogRequestPadman.setUri(updateUrl);
        eventLogRequestPadman.setMethod("PUT");

        var headerPadman = new ArrayList<NameValueDto>();
        headerPadman.add(new NameValueDto("Content-Type", "application/json"));
        headerPadman.add(new NameValueDto("Authorization", "Bearer " + Context.getJwtAuthenticationTokenValue()));

        eventLogRequestPadman.setHeader(headerPadman);
        eventLogRequestPadman.setBody(map);

        logPadman.setReq(eventLogRequestPadman);

        return logPadman;
    }

    private PostEventLogDto initLogReqAGESB(HttpServletRequest request, RequestUpdateStatusDto reqUpdateStatus, String xml){
        //Create log when call api agesb
        PostEventLogDto eventLogAGESB = new PostEventLogDto();
        eventLogAGESB.setServiceId(new ObjectId(configId));

        EventLogRequest eventLogRequest = new EventLogRequest();
        eventLogRequest.setUri(request.getRequestURI());
        eventLogRequest.setMethod(request.getMethod());
        eventLogRequest.setBody(xml);
        eventLogAGESB.setReq(eventLogRequest);
        eventLogAGESB.setKey(new SidNameDto("Code", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getMaBNHS()));
        return eventLogAGESB;
    }


    private Map<String, String> mapStatusFromConfig(RequestUpdateStatusDto reqUpdateStatus) {
        var config = configurationService.getConfig(new ObjectId(configId));
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        Map<String, String> map = new HashMap<>();
        String trangThaiXuLy = reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getTrangThaiXuLy();

        // Mapping statuses based on configuration
        map.put("maHoSo", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getMaBNHS());
        map.put("tinhTrangHS", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getTinhTrangHS());
        map.put("cvXuLy", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getCvXuLy());
        map.put("phongBanXuLy", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getPhongBanXuLy());
        map.put("duLieuChuyenNganh", reqUpdateStatus.getBody().getEsbMessageRequest().getMessageBody().getThongTinHoSo().getDuLieuChuyenNganh());

        // Chuyển đổi chuỗi JSON thành danh sách đối tượng
        Gson gson = new Gson();
        String json = listStatus.replace("'", "\"");;
        Type trangThaiAGESBListType = new TypeToken<List<TrangThaiAGESBDto>>() {}.getType();
        List<TrangThaiAGESBDto> dsTrangThaiAGESB = gson.fromJson(json, trangThaiAGESBListType);
        map.put("trangThaiXuLy", findElementByGiaTri(dsTrangThaiAGESB, trangThaiXuLy));
        return map;
    }

    private static String findElementByGiaTri(List<TrangThaiAGESBDto> list, String giaTri) {
        for (TrangThaiAGESBDto element : list) {
            if (element.getGiaTri().equals(giaTri)) {
                if (element.getGiaTri().equals("0") || element.getGiaTri().equals("2") || element.getGiaTri().equals("4")){
                    return element.getTenTrangThai();
                } else {
                    return "chuyenbuochoso";
                }
            }
        }
        return null; // Trả về null nếu không tìm thấy
    }

    private RequestUpdateStatusDto parseXmlToObject(String xml) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(RequestUpdateStatusDto.class);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        StringReader reader = new StringReader(Objects.requireNonNull(xml));
        return (RequestUpdateStatusDto) unmarshaller.unmarshal(reader);
    }

    private String getCurrentDate() {
        Date date = new Date();
        TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dfCurrentDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dfCurrentDate.setTimeZone(timezone);
        return dfCurrentDate.format(date);
    }

    private String convertObjectToStringXML(ResponseUpdateStatusDto res){
        try{
            JAXBContext context = JAXBContext.newInstance(ResponseUpdateStatusDto.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);

            StringWriter sw = new StringWriter();
            marshaller.marshal(res, sw);
            return sw.toString();
        }catch (JAXBException e) {
            throw new RuntimeException(e);
        }
    }
}