package vn.vnpt.digo.adapter.service;


import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.zalo.ZaloV2SendMessageResDto;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;

@Service
public class QD766Service {

    private static RestTemplate restTemplate = new RestTemplate();

    public Object callApi(String body){
        String endpoint = "https://dichvucong.gov.vn/jsp/rest.jsp";
        return MicroserviceExchange.postJsonQD766NoAuth(restTemplate, endpoint, body, String.class);
    }

}
