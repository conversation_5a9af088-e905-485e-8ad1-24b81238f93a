package vn.vnpt.digo.adapter.service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.document.IntegratedReCallEvent;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.GetDossierDetailDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.ReCallStatusDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.nps.NpsAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDetailDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierStatusDto;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.repository.IntegratedEventRepository;
import vn.vnpt.digo.adapter.repository.IntegratedReCallEventRepository;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;

@Service
public class IntegratedReCallEventService {
    Logger logger = LoggerFactory.getLogger(IntegratedReCallEventService.class);

    @Autowired
    private IntegratedReCallEventRepository repository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private NpsDossierSyncService service;
    
    @Autowired
    private Microservice microservice;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private HCMLGSPDossierSyncService serviceLGSPHCM;
    
    @Value(value = "${digo.schedule.nps-dossier-integrated-re-call-event.enable}")
    private Boolean enableScheduleNPSDossierReCallEvent;  
    
    @Scheduled(fixedDelay = 100)
//    ////@SchedulerLock(name = "npsDossierEventReCall", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    // ////@SchedulerLock(name = "npsDossierEventReCall", lockAtLeastFor = "PT0.5S", lockAtMostFor = "PT20S")
    protected void npsDossierEvent(){
        if (enableScheduleNPSDossierReCallEvent) {
            if(EventProperties.DOSSIER_RECALL_EVENT_AVAILABLE) {
                logger.info("start nps sync recall");
                //Query event
                try{
                    IntegratedReCallEvent event = this.getEvent();
                    if(Objects.nonNull(event)){
                        NpsAsyncReceiveDto eventObject = (NpsAsyncReceiveDto) event.getData();
                        switch(eventObject.getType()) {
                            case 1: {
                                logger.info("NPS-Sync: Event recall dossier with" + GsonUtils.getJson(eventObject));
                                if(Objects.nonNull(eventObject.getDossier().getNationCode()) && eventObject.getDossier().getNationCode().indexOf("G22") == 0){
                                    service.syncPromotionDossier(eventObject, 1, event.getCreatedDate());
                                }
                                service.syncDossier(eventObject);
                                break;
                            }
                            case 2: {
                                logger.info("NPS-Sync: Event recall dossier status with" + GsonUtils.getJson(eventObject));
                                if(Objects.nonNull(eventObject.getDossier().getNationCode()) && eventObject.getDossier().getNationCode().indexOf("G22") == 0){
                                    service.syncPromotionDossier(eventObject, 2, event.getCreatedDate());
                                }
                                service.syncDossierStatus(eventObject);
                                break;
                            }
                        }
                        event.setStatus(2);
                        event.setMessage("success");
                        this.repository.save(event);
                    } else {
                        logger.info("can not find event re call!!!");
                        EventProperties.DOSSIER_RECALL_EVENT_AVAILABLE = false;
                    }
                } catch (Exception e){
                    logger.info("event failed ex " + e.getMessage());
                }
            } else {
                // logger.info("no message to sync!!!");
            }
        }
        
    }
    
    public AffectedRowsDto updateEventKM() throws ParseException {
        Query query = new Query();
        query.addCriteria(Criteria.where("data.dossier.code").regex("G22", "i"));
        String sDate1="2022/07/21";  
        Date date1=new SimpleDateFormat("yyyy/MM/dd").parse(sDate1); 
        query.addCriteria(Criteria.where("createdDate").lt(date1));
        query.addCriteria(Criteria.where("data.dossier.updated").is(true));
//        long accountAppDtos = mongoTemplate.count(query, IntegratedEvent.class);
        List<IntegratedReCallEvent> listEvent = mongoTemplate.find(query, IntegratedReCallEvent.class);
        for(IntegratedReCallEvent oneEvent: listEvent){
            oneEvent.setStatus(0);
            repository.save(oneEvent);
        }
        return new AffectedRowsDto(listEvent.size());
    }
    
    public AffectedRowsDto updateEventListCode(List<String> listCode) throws ParseException {
        for(String code: listCode){
            Query query = new Query();
            query.addCriteria(Criteria.where("data.dossier.code").is(code));
            query.addCriteria(Criteria.where("data.dossier.updated").is(true));
            query.addCriteria(Criteria.where("data.type").is(1));
            List<IntegratedReCallEvent> listEvent = mongoTemplate.find(query, IntegratedReCallEvent.class);
            for(IntegratedReCallEvent oneEvent: listEvent){
                oneEvent.setStatus(0);
                repository.save(oneEvent);
            }
        }
        
        return new AffectedRowsDto(listCode.size());
    }
    
    public AffectedRowsDto updateEventListCodeStatus(List<String> listCode) throws ParseException {
        IntegratedReCallEvent event1 = repository.findById(new ObjectId("62add8443424cf2bcc10dd34")).get();
        IntegratedReCallEvent event2 = repository.findById(new ObjectId("62add8453424cf2bcc10dd35")).get();
        IntegratedReCallEvent event4 = repository.findById(new ObjectId("62b02ba8cd49f7227ed96f71")).get();
        IntegratedReCallEvent event5 = repository.findById(new ObjectId("62add8453424cf2bcc10dd35")).get();
        IntegratedReCallEvent event6 = repository.findById(new ObjectId("62afd290cd49f7227ed96a76")).get();
        for (String code: listCode){
            NpsDossierDetailDto dossier = null;
            try{
                String url = microservice.padmanUri("dossier/" + code + "/--by-code").toUriString();
                logger.info("getDossierByCode: URL=" + url);
                dossier = MicroserviceExchange.get(restTemplate, url,  NpsDossierDetailDto.class);
            } catch (Exception e){
                logger.info("event failed ex " + e.getMessage());
            }
            if(dossier != null){
                IntegratedReCallEvent newEvent5 = GsonUtils.copyObject(event5);
                newEvent5.setId(null);
                newEvent5.setStatus(0);
                NpsAsyncReceiveDto newData5 = GsonUtils.copyObject(newEvent5.getData(), NpsAsyncReceiveDto.class);
                newData5.getDossier().setId(dossier.getId());
                newData5.getDossier().setCode(dossier.getCode());
                newEvent5.setData(newData5);
                newEvent5.setCreatedDate(dossier.getAppliedDate());
                newEvent5.setUpdatedDate(dossier.getAppliedDate());
                repository.save(newEvent5);
            }
            else{
                IntegratedReCallEvent newEvent6 = GsonUtils.copyObject(event6);
                newEvent6.setId(null);
                newEvent6.setStatus(0);
                String strDate = "20" + code.substring(14, 20) + " 08:00:00";
                Date date1=new SimpleDateFormat("yyyyMMdd HH:mm:ss").parse(strDate); 
                newEvent6.setCreatedDate(date1);
                newEvent6.setUpdatedDate(date1);
                NpsAsyncReceiveDto newData6 = GsonUtils.copyObject(newEvent6.getData(), NpsAsyncReceiveDto.class);
//                newData6.getDossier().setId(oneDossier.getId());
                newData6.getDossier().setCode(code);
                NpsDossierStatusDto newNpsDossierStatus = newData6.getStatus();
//                if(Objects.nonNull(oneDossier.getAccepter()) && Objects.nonNull(oneDossier.getAccepter().getFullname())){
//                    newNpsDossierStatus.setAssignee(oneDossier.getAccepter().getFullname());
//                }
//                if(Objects.nonNull(oneDossier.getAgency())){
//                    newNpsDossierStatus.setAgency(GsonUtils.copyObject(oneDossier.getAgency(), vn.vnpt.digo.adapter.dto.nps.NpsDossierStatusDto.AgencyDto.class));
//                }
                newNpsDossierStatus.setAssignee("Can bo");
                Date newDate = new Date(date1.getTime() + 2*3600*1000);
                newNpsDossierStatus.setStartDate(newDate);
                Date newDateUpdate = new Date(date1.getTime() + 3*3600*1000);
                newNpsDossierStatus.setUpdatedDate(newDateUpdate);

                Date newDate2 = new Date(date1.getTime() + 4*3600*1000);
                newNpsDossierStatus.setEndDate(newDate2);
                
                newData6.setStatus(newNpsDossierStatus);
                newEvent6.setData(newData6);
                repository.save(newEvent6);
            }
                
        }
        return new AffectedRowsDto(listCode.size());
    }

    public AffectedRowsDto updateDossierListCancel(ReCallStatusDto reCallStatusDto) throws ParseException {
        IntegratedReCallEvent event6 = new IntegratedReCallEvent();
        for (String code: reCallStatusDto.getListCode()){
            NpsDossierDetailDto dossier = null;
                IntegratedReCallEvent newEvent6 = GsonUtils.copyObject(event6);
                String datefromCode = code.split("-")[1];
                String strDate = "20" + datefromCode + " 08:00:00";
                if(datefromCode.length() == 8) {
                    strDate = datefromCode + " 08:00:00";
                }
                Date date1=new SimpleDateFormat("yyyyMMdd HH:mm:ss").parse(strDate);
                newEvent6.setCreatedDate(date1);
                newEvent6.setUpdatedDate(date1);
                // set data
                NpsAsyncReceiveDto newData6 = new NpsAsyncReceiveDto();
                newData6.setConfigId(reCallStatusDto.getConfigId());
                newData6.setAgencyId(reCallStatusDto.getAgencyId());
                newData6.setSubsystemId(reCallStatusDto.getSubsystemId());
                newData6.setType(2);
                // dossier
                NpsDossierDto dossierValue = new NpsDossierDto();
                dossierValue.setCode(code);
                dossierValue.setUpdated(true);
                newData6.setDossier(dossierValue);
                //
                // set status
                NpsDossierStatusDto newNpsDossierStatus = new NpsDossierStatusDto();
                newNpsDossierStatus.setAssignee("Can bo");
                newNpsDossierStatus.setPosition("Can bo");
                newNpsDossierStatus.setContent(reCallStatusDto.getStatusName());
                IdDto idStatus = new IdDto(); idStatus.setId(reCallStatusDto.getStatusId());
                newNpsDossierStatus.setStatus(idStatus);
                Date newDate = new Date(date1.getTime() + 2*3600*1000);
                newNpsDossierStatus.setStartDate(newDate);
                Date newDateUpdate = new Date(date1.getTime() + 3*3600*1000);
                newNpsDossierStatus.setUpdatedDate(newDateUpdate);

                Date newDate2 = new Date(date1.getTime() + 4*3600*1000);
                newNpsDossierStatus.setEndDate(newDate2);

                newData6.setStatus(newNpsDossierStatus);
                newEvent6.setData(newData6);
                repository.save(newEvent6);

        }
        if(reCallStatusDto.getListCode().size() > 0){
            EventProperties.DOSSIER_RECALL_EVENT_AVAILABLE = true;
        }
        return new AffectedRowsDto(reCallStatusDto.getListCode().size());
    }
    
    public AffectedRowsDto reAddEventDossier(List<GetDossierDetailDto> listDossier){
        IntegratedReCallEvent event1 = repository.findById(new ObjectId("62cfe4769d961e2e1f8022ab")).get();
        IntegratedReCallEvent event2 = repository.findById(new ObjectId("62d0dfd69d961e2e1f80ea3e")).get();
        IntegratedReCallEvent event4 = repository.findById(new ObjectId("62d677649d961e2e1f8557d5")).get();
        IntegratedReCallEvent event5 = repository.findById(new ObjectId("62da645e7574df7014875d01")).get();
        for (GetDossierDetailDto oneDossier: listDossier){
            // created hs
            IntegratedReCallEvent newEvent1 = GsonUtils.copyObject(event1);
            newEvent1.setId(null);
            newEvent1.setStatus(0);
            NpsAsyncReceiveDto newData1 = GsonUtils.copyObject(newEvent1.getData(), NpsAsyncReceiveDto.class);
            newData1.getDossier().setId(oneDossier.getId());
            newData1.getDossier().setCode(oneDossier.getCode());
            newEvent1.setData(newData1);
            newEvent1.setCreatedDate(oneDossier.getAppliedDate());
            newEvent1.setUpdatedDate(oneDossier.getAppliedDate());
            repository.save(newEvent1);
            
            if(oneDossier.getDossierStatus().getId() != 0){
                IntegratedReCallEvent newEvent2 = GsonUtils.copyObject(event2);
                newEvent2.setId(null);
                newEvent2.setStatus(0);
                NpsAsyncReceiveDto newData2 = GsonUtils.copyObject(newEvent2.getData(), NpsAsyncReceiveDto.class);
                newData2.getDossier().setId(oneDossier.getId());
                newData2.getDossier().setCode(oneDossier.getCode());
                newEvent2.setData(newData2);
                newEvent2.setCreatedDate(oneDossier.getAppliedDate());
                newEvent2.setUpdatedDate(oneDossier.getAppliedDate());
                repository.save(newEvent2);
            }
            if(oneDossier.getDossierStatus().getId() == 4 || oneDossier.getDossierStatus().getId() == 5){
                IntegratedReCallEvent newEvent4 = GsonUtils.copyObject(event4);
                newEvent4.setId(null);
                newEvent4.setStatus(0);
                NpsAsyncReceiveDto newData4 = GsonUtils.copyObject(newEvent4.getData(), NpsAsyncReceiveDto.class);
                newData4.getDossier().setId(oneDossier.getId());
                newData4.getDossier().setCode(oneDossier.getCode());
                NpsDossierStatusDto newNpsDossierStatus = newData4.getStatus();
                if(Objects.nonNull(oneDossier.getAccepter()) && Objects.nonNull(oneDossier.getAccepter().getFullname())){
                    newNpsDossierStatus.setAssignee(oneDossier.getAccepter().getFullname());
                }
                if(Objects.nonNull(oneDossier.getAgency())){
                    newNpsDossierStatus.setAgency(GsonUtils.copyObject(oneDossier.getAgency(), vn.vnpt.digo.adapter.dto.nps.NpsDossierStatusDto.AgencyDto.class));
                }
                newData4.setStatus(newNpsDossierStatus);
                newEvent4.setData(newData4);
                newEvent4.setCreatedDate(oneDossier.getAppliedDate());
                newEvent4.setUpdatedDate(oneDossier.getAppliedDate());
                repository.save(newEvent4);
            
            
                if(oneDossier.getDossierStatus().getId() == 5){
                    IntegratedReCallEvent newEvent5 = GsonUtils.copyObject(event5);
                    newEvent5.setId(null);
                    newEvent5.setStatus(0);
                    NpsAsyncReceiveDto newData5 = GsonUtils.copyObject(newEvent5.getData(), NpsAsyncReceiveDto.class);
                    newData5.getDossier().setId(oneDossier.getId());
                    newData5.getDossier().setCode(oneDossier.getCode());
                    newEvent5.setData(newData5);
                    newEvent5.setCreatedDate(oneDossier.getAppliedDate());
                    newEvent5.setUpdatedDate(oneDossier.getAppliedDate());
                    repository.save(newEvent5);
                }
            }
        }
        return new AffectedRowsDto(listDossier.size());
    }
    
    public AffectedRowsDto callRunEvent(List<NpsAsyncReceiveDto> listEvent){
        for(NpsAsyncReceiveDto event: listEvent){
            IntegratedReCallEvent value = new IntegratedReCallEvent();
            value.setData(event);
            Integer check = this.pushEvent(value);
        }
        return new AffectedRowsDto(listEvent.size());
    }
    
    public AffectedRowsDto callRunEventAll(){
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(0));
        query.addCriteria(Criteria.where("type").is(1));
        query.with(Sort.by(Sort.Direction.ASC, "_id"));
        List<IntegratedReCallEvent> listEvent = this.mongoTemplate.find(query, IntegratedReCallEvent.class);
        for(IntegratedReCallEvent event: listEvent){
            Integer check = this.pushEvent(event);
        }
        return new AffectedRowsDto(listEvent.size());
    }
    
    public Integer checkcallDosssier(ObjectId dossierid){
        NpsDossierDetailDto dossier = null;
        String url = microservice.padmanUri("dossier/" + dossierid.toHexString() + "/--online").toUriString();
        logger.info("getDossierByCode: URL=" + url);
        dossier = MicroserviceExchange.get(restTemplate, url,  NpsDossierDetailDto.class);
        if(Objects.nonNull(dossier)){
            return 1;
        }
        return 0;
    }
    
    private Integer pushEvent(IntegratedReCallEvent event){
            logger.info("start nps sync");
            //Query event
            try{
                    NpsAsyncReceiveDto eventObject = (NpsAsyncReceiveDto) event.getData();
                    switch(eventObject.getType()) {
                        case 1: {
                            logger.info("NPS-Sync: Event dossier with" + GsonUtils.getJson(eventObject));
                            if(Objects.nonNull(eventObject.getDossier().getNationCode()) && eventObject.getDossier().getNationCode().indexOf("G22") == 0){
                                service.syncPromotionDossier(eventObject, 1, event.getCreatedDate());
                            }
                            service.syncDossier(eventObject);
                            break;
                        }
                        case 2: {
                            logger.info("NPS-Sync: Event dossier status with" + GsonUtils.getJson(eventObject));
                            if(Objects.nonNull(eventObject.getDossier().getNationCode()) && eventObject.getDossier().getNationCode().indexOf("G22") == 0){
                                service.syncPromotionDossier(eventObject, 2, event.getCreatedDate());
                            }
                            service.syncDossierStatus(eventObject);
                            break;
                        }
                    }
                    event.setStatus(2);
                    event.setMessage("success");
                    this.repository.save(event);
                    return 1;
            } catch (Exception e){
                logger.info("event failed ex " + e.getMessage());
                return 0;
            }
    }
    
    IntegratedReCallEvent getEvent(){
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(0));
        query.addCriteria(Criteria.where("type").is(1));
        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("isLGSPHCM").exists(false),
                Criteria.where("isLGSPHCM").is(false));
        query.addCriteria(criteria);
        query.with(Sort.by(Sort.Direction.ASC, "_id"));
        IntegratedReCallEvent ret = this.mongoTemplate.findOne(query, IntegratedReCallEvent.class);
        return ret;
    }
    
    IntegratedEvent getEventLGSPHCM(){
        Query queryEventSyncDossier = new Query();
        queryEventSyncDossier.addCriteria(Criteria.where("isLGSPHCM").is(true));
        queryEventSyncDossier.addCriteria(Criteria.where("status").is(0));
        queryEventSyncDossier.addCriteria(Criteria.where("type").is(1));
        queryEventSyncDossier.addCriteria(Criteria.where("data.type").is(1));
        queryEventSyncDossier.with(Sort.by(Sort.Direction.ASC, "_id"));
        IntegratedEvent eventSyncDossier = this.mongoTemplate.findOne(queryEventSyncDossier, IntegratedEvent.class);
        if(Objects.isNull(eventSyncDossier)) {
            Query queryEventSyncDossierStatus = new Query();
            queryEventSyncDossierStatus.addCriteria(Criteria.where("isLGSPHCM").is(true));
            queryEventSyncDossierStatus.addCriteria(Criteria.where("status").is(0));
            queryEventSyncDossierStatus.addCriteria(Criteria.where("type").is(1));
            queryEventSyncDossierStatus.with(Sort.by(Sort.Direction.ASC, "_id"));
            IntegratedEvent eventSyncDossierStatus = this.mongoTemplate.findOne(queryEventSyncDossierStatus, IntegratedEvent.class);
            return eventSyncDossierStatus;
        }
        return eventSyncDossier;
    }
    
    public Number getCountEventOpen(){
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(0));
        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("isLGSPHCM").exists(false),
                Criteria.where("isLGSPHCM").is(false));
        query.addCriteria(criteria);
        Number ret = this.mongoTemplate.count(query, IntegratedReCallEvent.class);
        return ret;
    }
    
    public IntegratedReCallEvent getEventOpening(Integer isLast){
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(0));
        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("isLGSPHCM").exists(false),
                Criteria.where("isLGSPHCM").is(false));
        query.addCriteria(criteria);
        if(isLast == 0){
        query.with(Sort.by(Sort.Direction.ASC, "_id"));
        }
        else{
            query.with(Sort.by(Sort.Direction.DESC, "_id"));
        }
        IntegratedReCallEvent ret = this.mongoTemplate.findOne(query, IntegratedReCallEvent.class);
        return ret;
    }
}
