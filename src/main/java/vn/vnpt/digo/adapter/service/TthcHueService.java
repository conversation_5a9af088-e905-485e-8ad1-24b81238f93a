/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.AgencyFullyDto;
import vn.vnpt.digo.adapter.dto.GettingConfigurationParamsDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.PostAgencyDto;
import vn.vnpt.digo.adapter.dto.PostProcedureDto;
import vn.vnpt.digo.adapter.dto.PostTagDto;
import vn.vnpt.digo.adapter.dto.ProcedureFormFormPostDto;
import vn.vnpt.digo.adapter.dto.ProcedureFormPostByOriginDto;
import vn.vnpt.digo.adapter.dto.ProcedureLockDto;
import vn.vnpt.digo.adapter.dto.TagDto;
import vn.vnpt.digo.adapter.dto.TthcHuePostSectorDto;
import vn.vnpt.digo.adapter.dto.TthcHueResDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.ProcedureFormForm;
import vn.vnpt.digo.adapter.pojo.Translate;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.StringUtil;
import vn.vnpt.digo.adapter.util.Translator;

/**
 *
 * <AUTHOR>
 */
@Service
public class TthcHueService {
    
    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private IntegratedConfigurationService configurationService;

    Logger logger = LoggerFactory.getLogger(TthcHueService.class);

    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890025");
    
    private List<TagDto> procedureFormTypeTagList = new ArrayList<>();
    
    private List<ProcedureFormForm> formList = new ArrayList<>();
    
    public List<TthcHueResDto.Donvi> getAgency(GettingConfigurationParamsDto params) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String body = createRequest(this.getAgencyReq());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("application/soap+xml; charset=utf-8"));
        HttpEntity<String> request = new HttpEntity<>(body, headers);
        //Request endpoint
        String endpoint = config.getParametersValue("gateway") + "?op=TTHC_DanhSachDonVi";
        //Send request
        ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, String.class);
        logger.info(result.getBody());
        TthcHueResDto res = this.getResponse(result.getBody());
        return res.getDonvi();
    }
    
    public List<TthcHueResDto.Thutuc> getTTHC(GettingConfigurationParamsDto params) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String body = createRequest(this.getTTHCReq());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("application/soap+xml; charset=utf-8"));
        HttpEntity<String> request = new HttpEntity<>(body, headers);
        //Request endpoint
        String endpoint = config.getParametersValue("gateway") + "?op=TTHC_DanhSachTTHC";
        //Send request
        ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, String.class);
        TthcHueResDto res = this.getResponse(result.getBody());
        return res.getTTHC();
    }
    
    public List<Object> getTTHCByAgency(GettingConfigurationParamsDto params, Integer agencyId) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String body = createRequest(this.getTTHCByAgencyReq(agencyId));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("application/soap+xml; charset=utf-8"));
        HttpEntity<String> request = new HttpEntity<>(body, headers);
        //Request endpoint
        String endpoint = config.getParametersValue("gateway") + "?op=TTHC_DanhSachTTHCTheoDonVi";
        //Send request
        ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, String.class);
        logger.info(result.getBody());
        TthcHueResDto res = this.getResponse(result.getBody());
        return res.getTTHCByAgency();
    }
    
    public TthcHueResDto.ChitietThutuc getTTHCById(GettingConfigurationParamsDto params, Integer procedureId) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String body = createRequest(this.getTTHCByIdReq(procedureId));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("application/soap+xml; charset=utf-8"));
        HttpEntity<String> request = new HttpEntity<>(body, headers);
        //Request endpoint
        String endpoint = config.getParametersValue("gateway") + "?op=TTHC_ThongTinChiTietThuTuc_IDThuTuc";
        //Send request
        ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, String.class);
        TthcHueResDto res = this.getResponse(result.getBody());
        logger.info("getTTHCById details returned!");
        return res.getTTHCById();
    }
    
    public List<TthcHueResDto.Linhvuc> getSector(GettingConfigurationParamsDto params) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String body = createRequest(this.getSectorReq());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("application/soap+xml; charset=utf-8"));
        HttpEntity<String> request = new HttpEntity<>(body, headers);
        //Request endpoint
        String endpoint = config.getParametersValue("gateway") + "?op=TTHC_DanhSachLinhVuc";
        //Send request
        ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, String.class);
        TthcHueResDto res = this.getResponse(result.getBody());
        return res.getSector();
    }
    
    public AffectedRowsDto syncSector(GettingConfigurationParamsDto params) {
        AffectedRowsDto ret = new AffectedRowsDto();
        List<TthcHueResDto.Linhvuc> lstSector = this.getSector(params);
        for (TthcHueResDto.Linhvuc item : lstSector) {
            PostProcedureDto.Sector res = this.postSector(item);
            if(Objects.nonNull(res)){
                ret.setAffectedRows(ret.getAffectedRows() + 1);
            }
        }
        return ret;
    }
    
    public PostProcedureDto.Sector postSector(TthcHueResDto.Linhvuc sector) {
        try{
            TthcHuePostSectorDto req = new TthcHuePostSectorDto();
            req.setCode(sector.getIdlinhvuc().toString());
            Translate vi = new Translate((short)228, sector.getTenlinhvuc());
            List<Translate> lstTrans = new ArrayList<>() {{
                add(vi);
            }};
            req.setName(lstTrans);
            req.setOriginId(sector.getIdlinhvuc().toString());
            String endpoint = microservice.basepadUri("/sector").toUriString();
            IdDto ret = MicroserviceExchange.postJson(restTemplate, endpoint, req, IdDto.class);
            
            return new PostProcedureDto.Sector(
                ret.getId(),
                req.getCode(),
                null,
                req.getName(),
                req.getStatus(),
                req.getOriginId()
            );
        } catch (Exception e) {
            logger.info("Failed to post sector with code: '" + sector.getIdlinhvuc() + "'. Reason: " + e.getMessage());
            return null;
        }
    }
    
    public AffectedRowsDto syncProcedure(GettingConfigurationParamsDto params) {
        AffectedRowsDto ret = new AffectedRowsDto();
        // Check lock
        boolean lock = this.checkLock();
        if (!lock) {
            try {
                this.lockSync(true);
                List<TthcHueResDto.Thutuc> procedureList = this.getTTHC(params);
                this.getProcedureFormTypeTagList();
                logger.info("Procedure total: " + procedureList.size());
                procedureList.forEach(p -> {
                    logger.info(p.getId().toString());
                });
                int i = 1;
                for (TthcHueResDto.Thutuc thuTuc : procedureList) {
                    // Check procedure existance
                    int originId = thuTuc.getId();
                    TthcHueResDto.ChitietThutuc details = this.getTTHCById(params, originId);
                    logger.info("Procedure number " + i + ": " + originId);
                    logger.info("Fetched procedure details successfully: " + Objects.nonNull(details));
                    Boolean existed = this.checkProcedureExistance(originId);
                    logger.info("Procedure exists in Basepad: " + existed);
                    if (Objects.nonNull(details)) {
                        if (!existed) {
                            IdDto res = this.postProcedure(details);
                            if (Objects.nonNull(res)) ret.setAffectedRows(ret.getAffectedRows() + 1);
                        }
                        if (Objects.nonNull(details.getHoSoKemTheo())) {
                            // delete existing procedureForms
                            deleteAllProcedureFormsByProcedureOriginId(originId);
                            // post new procedureForms
                            logger.info("Creating procedure's procedureForms. ProcedureForm total: " + details.getHoSoKemTheo().size());
                            addProcedureForms(originId, details.getHoSoKemTheo());
                        }
                    }
                    i++;
                }
                this.lockSync(false);
            } catch (Exception e) {
                this.lockSync(false);
            }
            return ret;
        } else {
            throw new DigoHttpException(11004, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    public boolean checkLock() {
        try{
            String endpoint = microservice.basepadUri("/procedure/--check-lock").toUriString();
            AffectedRowsDto ret = MicroserviceExchange.get(restTemplate, endpoint, AffectedRowsDto.class);
            if(Objects.equals(ret.getAffectedRows(), 1)){
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }
    
    public Boolean checkProcedureExistance(int originId) {
        try {
            String endpoint = microservice.basepadUri("/procedure/--get-id-by-origin?origin-id=" + originId).toUriString();
            String res = MicroserviceExchange.get(restTemplate, endpoint, String.class);
            logger.info("Procedure existance checking returned, res.length: " + res.length() + " - " + res);
//            if (res.length() > 2) {
//                return true;
//            }
            // huu minh fix
            if (res.length() >= 1) {
                return true;
            }
            return false;
        } catch (HttpClientErrorException e) {
            return false;
        }
    }
    
    public void lockSync(boolean lock) {
        ProcedureLockDto req = new ProcedureLockDto(lock);
        String endpoint = microservice.basepadUri("/procedure/--lock-sync").toUriString();
        AffectedRowsDto ret = MicroserviceExchange.putJson(restTemplate, endpoint, req, AffectedRowsDto.class);
        logger.info("lock sync: " + ret.toString());
    }
    
    public IdDto postProcedure(TthcHueResDto.ChitietThutuc p) {
        PostProcedureDto req = new PostProcedureDto();
        try {
            //set code
            req.setCode(p.getId().toString());
            //set level
            req.setLevel(this.getLevel(p.getMucDo()));
            //set agency level
            req.setAgencyLevel(this.getAgencyLevel(p.getDonViBanHanh()));
            //getSector
            PostProcedureDto.Sector sector = this.getSector(p.getIdLinhVuc());
            if (Objects.nonNull(sector)) {
                req.setSector(sector);
            } else {
                sector = this.postSector(new TthcHueResDto.Linhvuc(
                    p.getIdDonVi(),
                    p.getLinhVuc(),
                    p.getIdLinhVuc(),
                    null,
                    null
                ));
                if (sector != null) {
                    req.setSector(sector);
                } else {
                    logger.info("Creating procedure failed because of the null sector");
                    return null;
                }
            }
            //getAgency
            AgencyFullyDto agency = this.getAgency(p.getIdDonVi());
            List<AgencyFullyDto> lstAgency = new ArrayList<>();
            if (Objects.nonNull(agency)) {
                req.setAgencyIssued(agency.getTransName(translator.getCurrentLocaleId()));
                lstAgency.add(agency);
                req.setAgency(lstAgency);
            } else {
                AgencyFullyDto newAgency = this.postAgency(
                    new TthcHueResDto.Donvi(
                        p.getIdDonVi(),
                        p.getDonViBanHanh(),
                        p.getMaDinhDanhDonVi(),
                        null,
                        null,
                        null,
                        p.getVitri()
                    )
                );
                if (newAgency != null) {
                    req.setAgencyIssued(newAgency.getTransName(translator.getCurrentLocaleId()));
                    lstAgency.add(newAgency);
                    req.setAgency(lstAgency);
                } else {
                    logger.info("Creating procedure failed because of the null agency");
                    return null;
                }
            }
            //new P Translate
            PostProcedureDto.PTranslate vi = new PostProcedureDto.PTranslate();
            //set Translate
            vi.setLanguageId((short)228);
            vi.setName(p.getTenThuTuc());
            vi.setAgencyIssued(p.getDonViBanHanh());
            vi.setProcessingTime(p.getThoiGianGiaiQuyet());
            vi.setResults("");
            vi.setRequirement(p.getYeuCau());
            //get LegalGrounds
            String legalGrounds = "";
            if (Objects.nonNull(p.getCanCuPhapLy())) {
                for (TthcHueResDto.CancuPhaply item : p.getCanCuPhapLy()) {
                    legalGrounds = legalGrounds + item.getVanBan() + " <br> ";
                }
            }
            vi.setLegalGrounds(legalGrounds);
            vi.setSteps(p.getTrinhTuThucHien());
            //get DossierComponent
            String dossierComponent = "";
            if (Objects.nonNull(p.getHoSoKemTheo())) {
                for (TthcHueResDto.HosoKemtheo item : p.getHoSoKemTheo()) {
                    dossierComponent = dossierComponent + item.getTenHoSo();
                    if(Objects.nonNull(item.getLoaiHoSo())) {
                        dossierComponent = dossierComponent + " (" + item.getLoaiHoSo() + ")";
                    }
                    dossierComponent = dossierComponent + " <br> ";
                }
            }
            vi.setDossierComponent(dossierComponent);
            vi.setFee(p.getILePhi() != null ? (new Integer(p.getILePhi().intValue())).toString() : ".");
            vi.setServiceFee(p.getLePhi() != null ? p.getLePhi() : ".");
            vi.setAgencyAccept(".");
            vi.setAgencyCombination("");
            vi.setImplementer("");
            vi.setImplementationMethod(p.getCachThucThucHien() != null ? p.getCachThucThucHien() : ".");
            List<PostProcedureDto.PTranslate> lstTrans = new ArrayList<>() {
                {
                    add(vi);
                }
            };
            req.setTranslate(lstTrans);
            //set originId
            req.setOriginId(p.getId().toString());
            //set imlementer
            req.setImplementer(this.getImplementer(2));
            if (p.getSoLuongHoSo() != null) {
                try {
                    int quantity = Integer.parseInt(p.getSoLuongHoSo());
                    req.setDossierQuantity(quantity);
                } catch (NumberFormatException e){
                    logger.info("Can not set dossierQuantity because p.getSoLuongHoSo() is not integer.");
                }
            }
        } catch (NullPointerException npe) {
            logger.info("Posting procedure NullPointerException in ChitietThutuc");
            return null;
        }

        String endpoint = microservice.basepadUri("/procedure").toUriString();
        try {
            IdDto ret = MicroserviceExchange.postJson(restTemplate, endpoint, req, IdDto.class);
            return ret;
        } catch (Exception e) {
            logger.info("Procedure posting error: " + e.getMessage());
            return null;
        }
    }
    
    public PostProcedureDto.Tag getLevel(Integer level){
        PostProcedureDto.Tag ret = new PostProcedureDto.Tag();
        String levelId;
        String levelName;
        switch(level) {
            case 1:{
                levelId = "61f25eb372eb62396f2fed69";
                levelName = "Mức độ 1";
                break;
            }
            case 2:{
                levelId = "5f5b2c2b4e1bd312a6f3ae23";
                levelName = "Mức độ 2";
                break;
            }
            case 3:{
                levelId = "5f5b2c4b4e1bd312a6f3ae24";
                levelName = "Mức độ 3";
                break;
            }
            case 4:{
                levelId = "5f5b2c564e1bd312a6f3ae25";
                levelName = "Mức độ 4";
                break;
            }
            default: {
                levelId = "5f5b2c2b4e1bd312a6f3ae23";
                levelName = "Mức độ 2";
                break;
            }
        }
        ret.setId(new ObjectId(levelId));
        List<Translate> lstTrans = new ArrayList<>() {{
            add(new Translate((short)228, levelName));
        }};
        ret.setName(lstTrans);
        return ret;
    }
        
    public List<PostProcedureDto.Tag> getAgencyLevel(String aLevel) {
        List<PostProcedureDto.Tag> res = new ArrayList<>();
        PostProcedureDto.Tag level = new PostProcedureDto.Tag();
        String levelId;
        String levelName;
        String lev = getAgencyLevelFromAgencyName(aLevel);
        switch(lev) {
            case "cấp 1": {
                levelId = "60a70e584aba560b6069e646";
                levelName = "Đơn vị cấp 1";
                break;
            }
            case "cấp 2": {
                levelId = "60a70e644aba560b6069e647";
                levelName = "Đơn vị cấp 2";
                break;
            }
            case "cấp 3": {
                levelId = "60a70e584aba560b6069e648";
                levelName = "Đơn vị cấp 3";
                break;
            }
            case "cấp 4": {
                levelId = "60a70ef04aba560b6069e649";
                levelName = "Đơn vị cấp 4";
                break;
            }
            case "bộ": {
                levelId = "5ff6b1a706d0e31c6bf13e09";
                levelName = "Cấp Bộ";
                break;
            }
            case "quận": {
                levelId = "5f39f4155224cf235e134c59";
                levelName = "Quận/ Huyện";
                break;
            }
            case "huyện": {
                levelId = "5f39f4155224cf235e134c59";
                levelName = "Quận/ Huyện";
                break;
            }
            case "sở": {
                levelId = "5f39f42d5224cf235e134c5a";
                levelName = "Sở";
                break;
            }
            case "tỉnh": {
                levelId = "5f39f4335224cf235e134c5b";
                levelName = "Tỉnh/ Thành phố";
                break;
            }
            case "thành phố": {
                levelId = "5f39f4335224cf235e134c5b";
                levelName = "Tỉnh/ Thành phố";
                break;
            }
            case "phường": {
                levelId = "5febfe2295002b5c79f0fc9f";
                levelName = "Phường/ Xã";
                break;
            }
            case "xã": {
                levelId = "5febfe2295002b5c79f0fc9f";
                levelName = "Phường/ Xã";
                break;
            }
            default: {
                levelId = "5f7fc588b80e603d5300dce1";
                levelName = "Không xác định";
                break;
            }
        }
        
        level.setId(new ObjectId(levelId));
        List<Translate> lstTrans = new ArrayList<>() {{
            add(new Translate((short)228, levelName));
        }};
        level.setName(lstTrans);
        res.add(level);
        
        return res;
    }
    
    private String getAgencyLevelFromAgencyName(String agencyName) {
        if (agencyName != null) {
            String name = agencyName.trim().toLowerCase();
            
            if (name.contains("cấp 1")) return "cấp 1";
            if (name.contains("cấp 2")) return "cấp 2";
            if (name.contains("cấp 3")) return "cấp 3";
            if (name.contains("cấp 4")) return "cấp 4";
            if (name.contains("bộ")) return "bộ";
            if (name.contains("quận")) return "quận";
            if (name.contains("huyện")) return "huyện";
            if (name.contains("sở")) return "sở";
            if (name.contains("tỉnh")) return "tỉnh";
            if (name.contains("thành phố")) return "thành phố";
            if (name.contains("phường")) return "phường";
            if (name.contains("xã")) return "xã";
        }
        
        return agencyName;
    }
    
    public List<PostProcedureDto.Tag> getImplementer(Integer aLevel){
        List<PostProcedureDto.Tag> ret = new ArrayList<>();
        switch(aLevel) {
            case 2:{
                PostProcedureDto.Tag level = new PostProcedureDto.Tag();
                level.setId(new ObjectId("5f7fc6b0b80e603d5300dce3"));
                List<Translate> lstTrans = new ArrayList<>() {{
                    add(new Translate((short)228, "Công dân Việt Nam"));
                }};
                level.setName(lstTrans);
                ret.add(level);
                break;
            }
            default: {
                PostProcedureDto.Tag level = new PostProcedureDto.Tag();
                level.setId(new ObjectId("5f7fc6b0b80e603d5300dce3"));
                List<Translate> lstTrans = new ArrayList<>() {{
                    add(new Translate((short)228, "Công dân Việt Nam"));
                }};
                level.setName(lstTrans);
                ret.add(level);
                break;
            }
        }
        return ret;
    }
    
    public PostProcedureDto.Sector getSector(Integer id) {
        try{
            String endpoint = microservice.basepadUri("sector/--by-origin?origin-id=" + id).toUriString();
            PostProcedureDto.Sector ret = MicroserviceExchange.get(restTemplate, endpoint, PostProcedureDto.Sector.class);
            return ret;
        } catch (Exception e) {
            logger.info(e.getMessage());
            return null;
        }
    }
    
    public AffectedRowsDto syncAgency(GettingConfigurationParamsDto params) {
        AffectedRowsDto ret = new AffectedRowsDto();
        List<TthcHueResDto.Donvi> lstAgency = this.getAgency(params);
        List<TthcHueResDto.Donvi> lstTemp = new ArrayList<>();
        while(!Objects.equals(lstAgency.size(), lstTemp.size())){
            lstTemp = new ArrayList<>(); 
            for (TthcHueResDto.Donvi item : lstAgency) {
                Integer res = this.postSyncAgency(item, true);
                if(res == 1){
                    ret.setAffectedRows(ret.getAffectedRows() + 1);
                } else if (res == 2) {
                    lstTemp.add(item);
                }
            }
            lstAgency = lstTemp;
        }
        if(lstTemp.size() > 0){
            for (TthcHueResDto.Donvi item : lstAgency) {
                Integer res = this.postSyncAgency(item, false);
                if(res == 1){
                    ret.setAffectedRows(ret.getAffectedRows() + 1);
                }
            }
        }
        return ret;
    }
    
    public Integer postSyncAgency(TthcHueResDto.Donvi agency, boolean setParrent) {
        try{
            PostAgencyDto req = new PostAgencyDto();
            req.setCode(agency.getMadonvi());
            AgencyFullyDto.Name vi = new AgencyFullyDto.Name((short)228, agency.getTendonvi());
            List<AgencyFullyDto.Name> lstTrans = new ArrayList<>() {{ add(vi); }};
            req.setName(lstTrans);
            req.setOriginId(agency.getId());
            if(!Objects.equals(agency.getIdParent(), 0) && setParrent){
                AgencyFullyDto parent = this.getAgency(agency.getIdParent());
                if(Objects.isNull(parent)){
                    return 2;
                } else {
                    req.setParent(parent.getId());
                }
            }
            String endpoint = microservice.basedataUri("/agency").toUriString();
            IdDto ret = MicroserviceExchange.postJson(restTemplate, endpoint, req, IdDto.class);
            return 1;
        } catch (Exception e) {
            logger.info("Posting agency failed. Reason: " + e.getMessage());
            return 0;
        }
    }
    
    public AgencyFullyDto postAgency(TthcHueResDto.Donvi agency) {
        try{
            PostAgencyDto req = new PostAgencyDto();
            req.setCode(agency.getMadonvi());
            AgencyFullyDto.Name vi = new AgencyFullyDto.Name((short)228, agency.getTendonvi());
            List<AgencyFullyDto.Name> lstTrans = new ArrayList<>() {{
                add(vi);
            }};
            req.setName(lstTrans);
            req.setOriginId(agency.getId());
            String endpoint = microservice.basedataUri("/agency").toUriString();
            IdDto ret = MicroserviceExchange.postJson(restTemplate, endpoint, req, IdDto.class);
            return new AgencyFullyDto(
                ret.getId(),
                req.getName(),
                req.getCode(),
                null,
                null
            );
        } catch (Exception e) {
            logger.info("Posting agency failed. Reason: " + e.getMessage());
            return null;
        }
    }
    
    public AgencyFullyDto getAgency(Integer id) {
        try{
            String endpoint = microservice.basedataUri("/agency/name+code+parent+ancestor/--fully-by-origin?origin=3&origin-id=" + id).toUriString();
            AgencyFullyDto ret = MicroserviceExchange.get(restTemplate, endpoint, AgencyFullyDto.class);
            return ret;
        } catch (Exception e) {
            logger.info(e.getMessage());
            return null;
        }
    }
    
    private String getSectorReq() {
        StringBuilder sb = new StringBuilder();
        //Start xml request
        sb.append("<TTHC_DanhSachLinhVuc xmlns=\"http://tempuri.org/\" />");
        return sb.toString();
    }
    
    private String getTTHCByIdReq(Integer agencyId) {
        StringBuilder sb = new StringBuilder();
        //Start xml request
        sb.append("<TTHC_ThongTinChiTietThuTuc_IDThuTuc xmlns=\"http://tempuri.org/\">");
        sb.append("<id_thutuc>").append(agencyId).append("</id_thutuc>");
        sb.append("</TTHC_ThongTinChiTietThuTuc_IDThuTuc>");
        return sb.toString();
    }
    
    private String getTTHCByAgencyReq(Integer agencyId) {
        StringBuilder sb = new StringBuilder();
        //Start xml request
        sb.append("<TTHC_DanhSachTTHCTheoDonVi xmlns=\"http://tempuri.org/\">");
        sb.append("<madonvi>").append(agencyId).append("</madonvi>");
        sb.append("</TTHC_DanhSachTTHCTheoDonVi>");
        return sb.toString();
    }
    
    private String getTTHCReq() {
        StringBuilder sb = new StringBuilder();
        //Start xml request
        sb.append("<TTHC_DanhSachTTHC xmlns=\"http://tempuri.org/\" />");
        return sb.toString();
    }
    
    private String getAgencyReq() {
        StringBuilder sb = new StringBuilder();
        //Start xml request
        sb.append("<TTHC_DanhSachDonVi xmlns=\"http://tempuri.org/\" />");
        return sb.toString();
    }
    
    private String createRequest(String content) {
        StringBuilder sb = new StringBuilder();
        //Start xml request
        sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        sb.append("<soap12:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">");
        sb.append("<soap12:Body>");
        sb.append(content);
        sb.append("</soap12:Body>");
        sb.append("</soap12:Envelope>");
        return sb.toString();
    }
    
    public TthcHueResDto getResponse(String input) {
        try {
            input = input.replace("<?xml version=\"1.0\" encoding=\"utf-8\"?>", "");
            XmlMapper xmlMapper = new XmlMapper();
            TthcHueResDto value = xmlMapper.readValue(input, TthcHueResDto.class);
            return value;
        } catch (Exception e){
            return new TthcHueResDto();
        }
    }
    
    private void addProcedureForms(int procedureOriginId, List<TthcHueResDto.HosoKemtheo> procedureFormList) {
        List<ProcedureFormPostByOriginDto.ProcedureFormByOrigin> postedPFs = new ArrayList<>();
        for (TthcHueResDto.HosoKemtheo pF : procedureFormList) {
            logger.info("Checking procedureForm: " + GsonUtils.getJson(pF));
            String code = pF.getIdHoSo().toString();
            List<Translate> name = new ArrayList<>();
            name.add(new Translate((short) 228, pF.getTenHoSo()));
            ProcedureFormForm form = new ProcedureFormForm(null, code, name);
            // check if loaiHoSo exists in procedure form type list
            ProcedureFormPostByOriginDto.ProcedureFormDetailTypePostDto type = getProcedureFormTypeFromList(pF.getLoaiHoSo());
            // check if form exists
            int index = formIndexInList(code);
            if (index == -1) {
                try {
                    String endpoint = microservice.basepadUri("/form/--by-code").queryParam("code", code).build().toUriString();
                    form = MicroserviceExchange.get(restTemplate, endpoint, ProcedureFormForm.class);
                    logger.info("Form existed: " + form);
                    this.formList.add(form);
                } catch (HttpClientErrorException e) {
                    // form does not exist -> post a new one
                    ProcedureFormFormPostDto newForm = new ProcedureFormFormPostDto(code, name, 1, 0.0);
                    try {
                        String endpoint = microservice.basepadUri("/form").toUriString();
                        IdDto res = MicroserviceExchange.postJson(restTemplate, endpoint, newForm, IdDto.class);
                        form.setId(res.getId());
                        this.formList.add(form);
                        logger.info("Form not existed. Created new: " + form);
                    } catch (Exception e1) {
                        logger.info("Form posting error: " + e1.getMessage());
                    }
                }
            } else {
                form = this.formList.get(index);
            }
            // post procedureForm
            if (form.getId() != null) {
                ProcedureFormPostByOriginDto.ProcedureFormByOrigin procedureForm = new ProcedureFormPostByOriginDto.ProcedureFormByOrigin();
                procedureForm.setRequirement(pF.getBatBuoc());
                procedureForm.setForm(form);
                ProcedureFormPostByOriginDto.ProcedureFormDetailPostDto detail = new ProcedureFormPostByOriginDto.ProcedureFormDetailPostDto(
                    type,
                    1
                );
                
                List<ProcedureFormPostByOriginDto.ProcedureFormDetailPostDto> details = new ArrayList<>();
                details.add(detail);
                procedureForm.setDetail(details);
                if(Objects.nonNull(pF.getFileLink())) {
                    String urlLink ="";
                     if(!pF.getFileLink().toString().startsWith("https://"))
                         urlLink = "https://" + pF.getFileLink();
                    procedureForm.setFileLink(urlLink);
                    procedureForm.setFileLinkName(pF.getIdHoSo() + "_" + pF.getMaHoSo());
                }
                postedPFs.add(procedureForm);
            }
        }
        
        try {
            ProcedureFormPostByOriginDto reqBody = new ProcedureFormPostByOriginDto(procedureOriginId, postedPFs);
            String endpoint = microservice.basepadUri("/procedure-form/--by-procedure-origin-id").toUriString();
            AffectedRowsDto res = MicroserviceExchange.postJson(restTemplate, endpoint, reqBody, AffectedRowsDto.class);
            logger.info("Successfully posted " + res.getAffectedRows() + " procedureForms");
        } catch (HttpClientErrorException e) {
            logger.info("ProcedureForm posting HttpClientErrorException: " + e.getMessage());
        } catch (Exception e) {
            logger.info("ProcedureForm posting error: " + e.getMessage());
        }
    }
    
    private int formIndexInList(String code) {
        for (int i = 0; i < this.formList.size(); i++) {
            if (code.equals(this.formList.get(i).getCode().toString())) return i;
        }
        return -1;
    }
    
    private void getProcedureFormTypeTagList() {
        try {
            String endpoint = microservice.basecatUri("/tag/--by-category-id")
                .queryParam("category-id", "5f3a491c4e1bd312a6f00002")
                .queryParam("status", "1")
                .queryParam("size", "200")
                .queryParam("sort", "order,asc")
                .toUriString();
            String res = MicroserviceExchange.get(restTemplate, endpoint, String.class);
            List<String> arr = StringUtil.arrayStringToStringArray(res);
            for (String strTag : arr) {
                ObjectMapper mapper = new ObjectMapper();
                try {
                    Map<String, String> map = mapper.readValue(strTag, Map.class);
                    TagDto tag = new TagDto();
                    tag.setId(new ObjectId(map.get("id")));
                    tag.setName(map.get("name"));
                    this.procedureFormTypeTagList.add(tag);
                } catch (JsonProcessingException jsonEx) {
                    logger.info("Procedure type parse JsonProcessingException: " + jsonEx.getMessage());
                }
            }
            logger.info("Original procedure form type tag list:");
            logger.info(GsonUtils.getJson(this.procedureFormTypeTagList));
        } catch (Exception e) {
            logger.info("Procedure form type tags getting error: " + e.getMessage());
        }
    }
    
    private ProcedureFormPostByOriginDto.ProcedureFormDetailTypePostDto getProcedureFormTypeFromList(String strType) {
        ProcedureFormPostByOriginDto.ProcedureFormDetailTypePostDto type = null;
        
        if (Objects.nonNull(strType)) {
            String str = strType.trim().toLowerCase();
            TagDto tag = null;
            List<TagDto> tags = this.procedureFormTypeTagList.stream().filter(t -> t.getName().trim().toLowerCase().contains(str)).collect(Collectors.toList());
            try {
                tag = Collections.min(tags, Comparator.comparing(t -> Math.abs(t.getName().length() - str.length())));
            } catch (NoSuchElementException ex) {
                // procedure form type tag does not exist, post new tag
                PostTagDto newTag = new PostTagDto();
                newTag.setCategory(new ArrayList<>() {{ add(new ObjectId("5f3a491c4e1bd312a6f00002")); }});
                newTag.setTrans(new ArrayList<>() {{ add(new Translate((short) 228, strType)); }});
                
                String endpoint = microservice.basecatUri("/tag").toUriString();
                try {
                    IdDto res = MicroserviceExchange.postJson(restTemplate, endpoint, newTag, IdDto.class);
                    tag = new TagDto();
                    tag.setId(res.getId());
                    tag.setName(strType);
                    this.procedureFormTypeTagList.add(tag);
                    logger.info("Added new tag into procedureFormTypeTagList");
                    logger.info(GsonUtils.getJson(this.procedureFormTypeTagList));
                } catch (Exception e) {
                    logger.info("Procedure form type tag posting error: " + e.getMessage());
                }
            }

            if (Objects.nonNull(tag)) {
                List<Translate> detailName = new ArrayList<>();
                detailName.add(new Translate((short) 228, tag.getName()));
                type = new ProcedureFormPostByOriginDto.ProcedureFormDetailTypePostDto(tag.getId().toString(), detailName);
            }
        }
        
        return type;
    }
    
    private void deleteAllProcedureFormsByProcedureOriginId(int procedureOriginId) {
        String endpoint = microservice.basepadUri("/procedure-form/--by-procedure-origin-id")
            .queryParam("procedure-origin-id", procedureOriginId)
            .toUriString();
        try {
            AffectedRowsDto res = MicroserviceExchange.delete(restTemplate, endpoint, AffectedRowsDto.class);
            logger.info("Successfully deleted " + res.getAffectedRows() + " procedureForms of procedure with originId " + procedureOriginId);
        } catch (Exception e) {
            logger.info("Procedure forms deleting error: " + e.getMessage());
        }
    }
}
