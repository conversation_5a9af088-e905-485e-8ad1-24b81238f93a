package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import org.apache.http.HttpException;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.dto.BoTNMTPsDossierDto;
import vn.vnpt.digo.adapter.dto.BoTNMTPsGetListReqDto;
import vn.vnpt.digo.adapter.dto.BoTNMTPsGetListResDto;
import vn.vnpt.digo.adapter.dto.BoTNMTPsDossierRawDto;
import vn.vnpt.digo.adapter.dto.TaiLieuNop;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.cmu.UpdateTNMTApiParams;
import vn.vnpt.digo.adapter.dto.cmu.UpdateTNMTApiResponse;
import vn.vnpt.digo.adapter.dto.minhtue.TokenResDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Date;
import java.util.HashMap;
import java.util.TimeZone;
import java.util.Arrays;
import java.util.Base64;
import java.util.stream.Collectors;

@Service
public class BoTNMTPadserviceService {
    @Autowired
    private IntegratedConfigurationService configurationService;
    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890021");
    @Autowired
    private Translator translator;
    private final Gson gson = new Gson();
    @Autowired
    private MappingDataService mappingDataService;
    private final ObjectId procedureTypeId = new ObjectId("5fc0707a62681a8bef000013");
    @Autowired
    private Microservice microservice;
    @Autowired
    private RestTemplate restTemplate;

    private final ObjectId integratedConfigID = new ObjectId("63fd68b00bea1754bee166d6");


    public BoTNMTPsGetListResDto getDossier(BoTNMTPsGetListReqDto params) {
        BoTNMTPsGetListResDto res = new BoTNMTPsGetListResDto();
        //Get config
        IntegratedConfigurationDto config = null;
        if (Objects.nonNull(params.getConfigId())) {
            //config = configurationService.getConfig(new ObjectId("63fd68b00bea1754bee166d6"));
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        try {
            List<BoTNMTPsDossierDto> dossier = new ArrayList<>();
            //For and get data from list config procedure
            //Get data from FPT
            BoTNMTPsDossierRawDto[] lstFptDossier = this.getDataFromBoTNMT(config, params);
            if (lstFptDossier.length > 0) {
                //Get procedure data
                Map<String, Object> procedureParrams = new HashMap<>();
                //Fetch and mapping data to response
                for (BoTNMTPsDossierRawDto item : lstFptDossier) {

                    BoTNMTPsDossierDto newDossier = this.convertDossier(config, params, item);
                    if (Objects.nonNull(newDossier)) {
                        dossier.add(newDossier);
                    }
                }
            }
            res.setDosser(dossier);
            res.setTotalRows(res.getDosser().size());
            return res;
        } catch (DigoHttpException digoE) {
            digoE.printStackTrace();
            throw digoE;
        } catch (Exception e) {
            e.printStackTrace();
            throw new DigoHttpException(11002, new String[]{e.getMessage()}, HttpServletResponse.SC_BAD_REQUEST);
        }
    }
    public TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) {
        String strConsumer = consumerKey + ":" + consumerSecret;
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(tokenUrl);
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();

        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(auth);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST, request, Object.class);
            System.out.println(result);
            TokenResDto token = GsonUtils.copyObject(result.getBody(), TokenResDto.class);
            return token;
        } catch (Exception e) {
            e.printStackTrace();
            throw new DigoHttpException(11003, new String[]{e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    private BoTNMTPsDossierRawDto[] getDataFromBoTNMT(IntegratedConfigurationDto config, BoTNMTPsGetListReqDto params) {
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        Object ssoUrl = config.getParametersValue("sso-url");
        Object clientID = config.getParametersValue("client-id");
        Object clientSecret = config.getParametersValue("client-secret");
        Object gateway = config.getParametersValue("gateway");
        Object listProcedure = config.getParametersValue("list-procedure");
        List<String> myListProcedure = new ArrayList<String>(Arrays.asList(listProcedure.toString().substring(1,listProcedure.toString().length() - 1)
                .split(",")))
                .stream().map(el->el.strip()).collect(Collectors.toList());
        System.out.println(ssoUrl.toString());
        System.out.println(clientID.toString());
        System.out.println(clientSecret.toString());
        String token = getToken(ssoUrl.toString(), clientID.toString(), clientSecret.toString()).getAccessToken();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json;charset=UTF-8");
        headers.setBearerAuth(token);
        HashMap<String, Object> body = new HashMap<>();
        body.put("tungay", df.format(Date.from(params.getFromDate())));
        body.put("denngay", df.format(Date.from(params.getToDate())));
        body.put("matinh", params.getMaTinh());
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        JSONObject json = new JSONObject(body);
//        UriComponents builder = UriComponentsBuilder.fromHttpUrl("https://api.monre.gov.vn/dvcthietyeu/demo/1.0/hoso/danhsachhoso")
//                .queryParam("tungay", df.format(params.getFromDate()))
//                .queryParam("denngay", df.format(params.getToDate()))
//                .queryParam("matinh", params.getMaTinh())
//                .build();
        //ResponseEntity<BoTNMTPsDossierRawDto[]> result = restTemplate.exchange("https://api.monre.gov.vn/dvcthietyeu/demo/1.0/hoso/danhsachhoso", HttpMethod.POST, request, BoTNMTPsDossierRawDto[].class, params);
        ResponseEntity<BoTNMTPsDossierRawDto[]> result = restTemplate.exchange(gateway.toString()+"/hoso/danhsachhoso", HttpMethod.POST, request, BoTNMTPsDossierRawDto[].class, params);
        System.out.println("result"+ result);
        //String ret2 = MicroserviceExchange.getFpt(restTemplate, builder.toUriString(), String.class);
        //return MicroserviceExchange.getFptEncode(restTemplate, builder.toUriString(), BoTNMTPsDossierRawDto[].class);
        List<BoTNMTPsDossierRawDto> boTNMTPsDossierRawDtoStream = Arrays.stream(result.getBody()).filter(el -> {
            if (myListProcedure.contains(el.getMaTTHC()+ el.getMaHuyen()) && (el.getTrangThaiHoSo().equals("1")|| el.getTrangThaiHoSo().equals(("7")))) {
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.toList());
        return boTNMTPsDossierRawDtoStream.toArray(new BoTNMTPsDossierRawDto[boTNMTPsDossierRawDtoStream.size()]);
    }
    private BoTNMTPsDossierDto convertDossier(IntegratedConfigurationDto config, BoTNMTPsGetListReqDto params, BoTNMTPsDossierRawDto dossier) throws ClassNotFoundException, ParseException {
        BoTNMTPsDossierDto ret = new BoTNMTPsDossierDto();
        //Do something to convert code
        // Mapping applicant
        BoTNMTPsDossierDto.Applicant applicant = new BoTNMTPsDossierDto.Applicant();
        if (Objects.nonNull(dossier.getChuHoSo().getTen()) && !Objects.equals(dossier.getChuHoSo().getTen(), "")) {
            applicant.setFullname(dossier.getChuHoSo().getTen());
        } else {
            applicant.setFullname(dossier.getChuHoSo().getTen());
        }
        applicant.setEmail(dossier.getChuHoSo().getEmail());
        applicant.setPhoneNumber(dossier.getChuHoSo().getSoDienThoai());
        // Applicant identity
        applicant.setIdentityNumber(dossier.getChuHoSo().getSoCMND());
        // Applicant address
        // Mapping address place
        applicant.setAddress(dossier.getChuHoSo().getDiaChi());

        ret.setApplicant(applicant);

        BoTNMTPsDossierDto.Eform eform = new BoTNMTPsDossierDto.Eform();
        eform.setData(dossier.getDonDangKy());
        ret.setEform(eform);
        // Mapping attachment
        List<BoTNMTPsDossierDto.Attachment> lstAttachment = new ArrayList<>();
        List<TaiLieuNop> taiLieuNopListAttachment = dossier.getTaiLieuNop();
        if (Objects.nonNull(taiLieuNopListAttachment)) {
            for (TaiLieuNop item : taiLieuNopListAttachment) {
                BoTNMTPsDossierDto.Attachment attachment = new BoTNMTPsDossierDto.Attachment();
                attachment.setFilename(item.getTenTepDinhKem());
                attachment.setGroup("1");
                //attachment.setLink(item.getDuongDanTaiTepTin());
                attachment.setLink(item.getDuongDanTaiTepTin());
                attachment.setTenThanhPhanHoSo(item.getTenThanhPhanHoSo());
                attachment.setSize(1);
                attachment.setId(new ObjectId());
                lstAttachment.add(attachment);
            }

            ret.setAttachment(lstAttachment);
        }
        // Mapping receiving place
        BoTNMTPsDossierDto.Place receivingPlace = new BoTNMTPsDossierDto.Place();
        receivingPlace.setFullAddress(dossier.getChuHoSo().getDiaChi());
        receivingPlace.setNation("Việt Nam");
        ret.setReceivingPlace(receivingPlace);

        // Mapping procedure
        //https://apitest.vnptigate.vn/bd/procedure/6392bf28d6423414624f3498
        //lay danh sach param config list procedure
        Object listProcedure = config.getParametersValue("list-procedure");
        List<String> myListProcedure = new ArrayList<String>(Arrays.asList(listProcedure.toString().substring(1,listProcedure.toString().length() - 1)
                .split(",")))
                .stream().map(el->el.strip()).collect(Collectors.toList());
        System.out.println(myListProcedure);
        //lay danh sachs param config tham so list-mapped-procedure
        Object listMappedProcedure = config.getParametersValue("list-mapped-procedure");
        List<String> myListMappedProcedure = Arrays.stream(listMappedProcedure.toString().substring(1,
                listMappedProcedure.toString().length() - 1).split(",")).map(String::strip).collect(Collectors.toList());
        //if (config.getParametersValue("list-procedure"))
        int index = myListProcedure.indexOf(dossier.getMaTTHC()+dossier.getMaHuyen());
        String procedureUrl;
        if( index!= -1 ){
            String idProcedure = myListMappedProcedure.get(index);
            System.out.println(idProcedure);
            procedureUrl = microservice.basepadUri("/procedure/").toUriString() + idProcedure;
        }else {
            throw new ClassNotFoundException("Not found procedure");
        }
        //Map<String, Object> procedureParrams = new HashMap<>();
        //procedureParrams.put("id", dossier.getMaTTHC());
        BoTNMTPsDossierDto.Procedure procedureInfo1 = MicroserviceExchange.get(restTemplate,procedureUrl,BoTNMTPsDossierDto.Procedure.class);
        //FptPsDossierDto.Procedure procedureInfo = MicroserviceExchange.getNoAuth(this.getOAuth2RestTemplate(config), procedureUrl, FptPsDossierDto.Procedure.class, procedureParrams);
        ret.setProcedure(procedureInfo1);
        ret.setCode(dossier.getMaHoSo());
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        df.setTimeZone(TimeZone.getTimeZone("Asia/Ho_Chi_Minh"));
        ret.setCreatedDate(df.parse(dossier.getNgayNopHoSo()).toInstant());
        ret.setTrangThaiHoSo(dossier.getTrangThaiHoSo());
        ret.setLoaiDoiTuong(dossier.getLoaiDoiTuong());
        return ret;
    }

    private OAuth2RestTemplate getOAuth2RestTemplate(IntegratedConfigurationDto config) {

        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(config.getParametersValue("sso-url"));
        details.setClientId(config.getParametersValue("client-id"));
        details.setClientSecret(config.getParametersValue("client-secret"));
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }

    public AffectedRowsDto syncDossierToTNMT(UpdateTNMTApiParams params){
        IntegratedConfigurationDto config = configurationService.getConfig(this.integratedConfigID);
        AffectedRowsDto affectedRowsDto = new AffectedRowsDto(0);
        OAuth2RestTemplate oAuth2RestTemplate = getOAuth2RestTemplate(config);

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json;charset=UTF-8");
            headers.setBearerAuth(oAuth2RestTemplate.getAccessToken().getValue());

            HttpEntity<UpdateTNMTApiParams> updateTNMTApiParamsHttpEntity = new HttpEntity<>(params,
                    headers);
            ResponseEntity<UpdateTNMTApiResponse> result = oAuth2RestTemplate.exchange( config.getParametersValue("gateway")+"/hoso/capnhathoso", HttpMethod.POST,updateTNMTApiParamsHttpEntity, UpdateTNMTApiResponse.class);
            if(result.getStatusCode().isError()){
                throw new HttpException();
            }
            affectedRowsDto.setAffectedRows(result.getBody().getDataCount());

            return affectedRowsDto;

        } catch (Exception e){
            return affectedRowsDto;
        }
    }
}
