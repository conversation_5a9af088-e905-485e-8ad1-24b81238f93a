package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import java.util.HashMap;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.config.ZaloConfig;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.SendZaloBatchDto;
import vn.vnpt.digo.adapter.dto.SendZaloResponseDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.Translator;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import net.minidev.json.JSONObject;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.DetailDossierDto;
import vn.vnpt.digo.adapter.dto.FindInfoDossierDto;
import vn.vnpt.digo.adapter.dto.SendZaloBodyDto;
import vn.vnpt.digo.adapter.repository.ZaloRepository;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;

/**
 * <AUTHOR>
 */
@Service
public class ZaloService {
    @Autowired
    private Translator translator;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private IntegratedConfigurationService configurationService;
    
    @Autowired
    private ZaloRepository zaloRepository;
    
    @Autowired
    private Microservice microservice;
       
    private ObjectId zaloId = new ObjectId("5f96885432c7c6aed84b7a7b");
    
    private String token = "";
    
    protected static final int CONNECTION_TIMEOUT = 2000;
    protected static final int READ_TIMEOUT = 15000;
    protected ZaloConfig config = null;
    
    public AffectedRowsDto sendBatch(SendZaloBatchDto batch, SendZaloBodyDto bd) throws Exception {
        //Get zalo by Id
        IntegratedConfigurationDto config;
        if (java.util.Objects.nonNull(batch.getConfigId())) {
            config = configurationService.getConfig(batch.getConfigId());
        } else {
            config = configurationService.getConfig(batch.getAgencyId(), batch.getSubsystemId(), this.zaloId);
        }
        if (java.util.Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        this.token = config.getParametersValue("access-token").toString();
        JSONObject body = createRequest(config, bd);

        if (bd.getListPhoneNumber() == null){
            if(!bd.getPhoneNumber().isBlank() && this.isValidPhone(bd.getPhoneNumber())){
                String userId = this.checkUserId(bd.getPhoneNumber());
                JSONObject obj = new JSONObject();
                obj.put("user_id", userId);
                JSONObject data = new JSONObject();
                data.put("recipient", obj);
                data.put("message", body);
                //Create HttpEntity request
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);        
                HttpEntity<String> request = new HttpEntity<>(data.toJSONString(), headers);
                //Request endpoint
                String endpoint = zaloRepository.OA_SEND_MESSAGE;
                Map<String, Object> params = new HashMap<>();
                params.put("access_token", token);
                endpoint = endpoint + "?access_token={access_token}";
                //Send request
                ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, String.class, params);
                Gson g = new Gson();
                SendZaloResponseDto response = g.fromJson(result.getBody(), SendZaloResponseDto.class);
                if(!response.getError().equals(0)){
                    throw new Exception("ERROR: " + response.getError() + " ERROR_MESS: " + response.getMessage() + " DATA: " + response.getData());
                }
                return new AffectedRowsDto(1);
            }else
                return new AffectedRowsDto(0);
        }else{
            for(String phone: bd.getListPhoneNumber()){   
                if(this.isValidPhone(bd.getPhoneNumber())){
                    String userId = this.checkUserId(phone);
                    JSONObject obj = new JSONObject();
                    obj.put("user_id", userId);
                    JSONObject data = new JSONObject();
                    data.put("recipient", obj);
                    data.put("message", body);

                    //Create HttpEntity request
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);        
                    HttpEntity<String> request = new HttpEntity<>(data.toJSONString(), headers);
                    //Request endpoint
                    String endpoint = zaloRepository.OA_SEND_MESSAGE;
                    Map<String, Object> params = new HashMap<>();
                    params.put("access_token", token);
                    endpoint = endpoint + "?access_token={access_token}";
                    //Send request
                    ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, String.class, params);
                    Gson g = new Gson();
                    SendZaloResponseDto response = g.fromJson(result.getBody(), SendZaloResponseDto.class);
                    if(!response.getError().equals(0)){
                        throw new Exception("ERROR: " + response.getError() + " ERROR_MESS: " + response.getMessage() + " DATA: " + response.getData());
                    }
                }
            }
        }
        return new AffectedRowsDto(1);
    }
    
    private JSONObject createRequest(IntegratedConfigurationDto zalo, SendZaloBodyDto content){
        JSONObject obj = new JSONObject();
        JSONObject obj1;
        String typeTemplate = "";
        ArrayList elements = new ArrayList();
        try{
            if(translator.getCurrentLocaleId() == 228){
                obj.put("title", zalo.getParametersValue("name-title-vi").toString());  
            }else{
                obj.put("title", zalo.getParametersValue("name-title-en").toString());
            }
        }catch (NullPointerException e) {
            obj.put("title", "Thông tin hồ sơ");            
        }
        
        try{
            obj.put("template_id", zalo.getParametersValue("application-template-id").toString());
            obj.put("template_data", content.getContent());
            obj.put("payload", "callback_data");
            typeTemplate = "business";
            elements.add(obj);
        }catch (NullPointerException e) {
            //template old
            obj.put("image_url", zalo.getParametersValue("message-thumbnail-link").toString());
            obj.put("subtitle", content.getContent().get("data"));
            obj1 = new JSONObject();
            obj1.put("type", "oa.open.url");
            obj1.put("url", zalo.getParametersValue("action-link").toString());
            obj.put("default_action", obj1);
            elements.add(obj);  
            typeTemplate = "list";
        }        
        
        //add file tiep nhan
        try {
            if(!content.getFile().isEmpty()){
            obj = new JSONObject();
            if(translator.getCurrentLocaleId() == 228){
                obj.put("title", zalo.getParametersValue("name-download-file-vi").toString());  
            }else{
                obj.put("title", zalo.getParametersValue("name-download-file-en").toString());
            }
            
            obj.put("image_url", zalo.getParametersValue("link-icon-download").toString());
            obj1 = new JSONObject();
            obj1.put("type", "oa.open.url");
            obj1.put("url", zalo.getParametersValue("action-link").toString() + content.getFile());
            obj.put("default_action", obj1);
            elements.add(obj);
            }
        } catch (NullPointerException e) {
            System.out.println("Link phieu tiep nhan");
        }        
        //tro ve trang chu
        try {
            if(!zalo.getParametersValue("xem-them").toString().isEmpty()){
            obj = new JSONObject();
            if(translator.getCurrentLocaleId() == 228){
                obj.put("title", zalo.getParametersValue("name-icon-igate-vi").toString());  
            }else{
                obj.put("title", zalo.getParametersValue("name-icon-igate-en").toString());
            }
            
            obj.put("image_url", zalo.getParametersValue("link-icon-igate").toString());
            obj1 = new JSONObject();
            obj1.put("type", "oa.open.url");
            obj1.put("url", zalo.getParametersValue("action-link").toString());
            obj.put("default_action", obj1);
            elements.add(obj);
            }
        } catch (NullPointerException e) {
            System.out.println("Link icon xem thêm");
        }
        //lien he
        try {
            obj = new JSONObject();
            if(translator.getCurrentLocaleId() == 228){
                obj.put("title", zalo.getParametersValue("name-qr-zalo-vi").toString());   
            }else{
                obj.put("title", zalo.getParametersValue("name-qr-zalo-en").toString());
            }
            obj.put("image_url", zalo.getParametersValue("link-qr-zalo").toString());
            obj1 = new JSONObject();
            obj1.put("type", "oa.open.url");
            obj1.put("url", zalo.getParametersValue("action-link").toString());
            obj.put("default_action", obj1);
            elements.add(obj);
        } catch (NullPointerException e) {
            System.out.println("Chưa cấu hình QR code");
        }
        
        obj = new JSONObject();
        obj.put("template_type", typeTemplate);
        obj.put("elements", elements);
        obj1 = new JSONObject();
        obj1.put("type", "template");
        obj1.put("payload", obj);
        
        obj = new JSONObject();
        obj.put("attachment", obj1);
        return obj;
    }
        
    private String checkUserId(String phone) throws Exception{
        String endpoint = zaloRepository.OA_GET_USER_PROFILE;
        if(this.isValidPhone(phone)){
            HashMap<String, String> data = new HashMap<>();
            data.put("phone", phone);
            Gson gson = new Gson();
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", token);
            params.put("data", gson.toJson(data));

            endpoint = endpoint + "?access_token={access_token}&data={data}";
            SendZaloResponseDto results = MicroserviceExchange.getZalo(restTemplate, endpoint, SendZaloResponseDto.class, params);
            if(results.getError().equals(0)){
                return results.getData().get("user_id").toString();
            }
            else{
                throw new Exception("ERROR: " + results.getError() + " ERROR_MESS: " + results.getMessage() + " DATA: " + results.getData());
            }
        }else{
            throw new Exception("ERROR: phone " + phone + " is invalid" );
        }
    }

    public SendZaloResponseDto checkUserId(String phone, SendZaloBatchDto batch) throws Exception{
        IntegratedConfigurationDto config;
        if (java.util.Objects.nonNull(batch.getConfigId())) {
            config = configurationService.getConfig(batch.getConfigId());
        } else {
            config = configurationService.getConfig(batch.getAgencyId(), batch.getSubsystemId(), this.zaloId);
        }
        if (java.util.Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String endpoint = zaloRepository.OA_GET_USER_PROFILE;
        HashMap<String, String> data = new HashMap<>();
        if(this.isValidPhone(phone)){
            data.put("phone", phone);
            Gson gson = new Gson();
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", config.getParametersValue("access-token").toString());
            params.put("data", gson.toJson(data));

            endpoint = endpoint + "?access_token={access_token}&data={data}";
            SendZaloResponseDto results = MicroserviceExchange.getZalo(restTemplate, endpoint, SendZaloResponseDto.class, params);
            if(results.getError().equals(0)){
                return results;
            }
            else{
                throw new Exception("ERROR: " + results.getError() + " ERROR_MESS: " + results.getMessage() + " DATA: " + results.getData());
            }
        }else{
            throw new Exception("ERROR: phone " + phone + " is invalid" );
        }
    }
    
    public AffectedRowsDto findInfoDossier(FindInfoDossierDto info, ObjectId deploymentId, Short languageId){
        IntegratedConfigurationDto config;
        config = configurationService.getConfigZalo(this.zaloId, info.getApp_id(), "app-id", deploymentId);
        if (java.util.Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        try{
            //Create HttpEntity request
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);        
            HttpEntity<String> request = new HttpEntity<>(null, headers);
            //Request endpoint
            String endpoint = microservice.padmanUri("dossier/--search-detail").toUriString();
            Map<String, Object> params = new HashMap<>();
            params.put("code", info.getMessage().get("text"));
            params.put("deployment-id", config.getDeploymentId());
            if (config.getParametersValue("application-template-id").toString() != null && !config.getParametersValue("application-template-id").toString().isBlank() && !config.getParametersValue("application-template-id").toString().isEmpty())
                params.put("template", "new");
            else
                params.put("template", "old");
            params.put("language-id", languageId);
            endpoint = endpoint + "?code={code}&deployment-id={deployment-id}&language-id={language-id}&template={template}";
            //Send request
            ResponseEntity<DetailDossierDto> result = restTemplate.exchange(endpoint, HttpMethod.GET, request, DetailDossierDto.class, params);
            JSONObject body = result.getBody().getContent();
            return sendMessage(body, config, info.getUser_id_by_app());
        }
        catch(Exception e){
            throw new DigoHttpException(11000, new String[]{ e.toString() });
        }
    }
    
    public AffectedRowsDto sendMessage(JSONObject content, IntegratedConfigurationDto config, String userId) throws Exception {
        this.token = config.getParametersValue("access-token").toString();
        JSONObject body = createRequest(config, content);

        if (body != null){
            JSONObject obj = new JSONObject();
            obj.put("user_id", userId);
            JSONObject data = new JSONObject();
            data.put("recipient", obj);
            data.put("message", body);
            //Create HttpEntity request
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);        
            HttpEntity<String> request = new HttpEntity<>(data.toJSONString(), headers);
            //Request endpoint
            String endpoint = zaloRepository.OA_SEND_MESSAGE;
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", token);
            endpoint = endpoint + "?access_token={access_token}";
            //Send request
            ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, String.class, params);
            Gson g = new Gson();
            SendZaloResponseDto response = g.fromJson(result.getBody(), SendZaloResponseDto.class);
            if(!response.getError().equals(0)){
                throw new Exception("ERROR: " + response.getError() + " ERROR_MESS: " + response.getMessage() + " DATA: " + response.getData());
            }
            return new AffectedRowsDto(1);
        }
        return new AffectedRowsDto(0);
    }
    
    private JSONObject createRequest(IntegratedConfigurationDto zalo, JSONObject content){
        JSONObject obj = new JSONObject();
        JSONObject obj1;
        String typeTemplate = "";
        
        ArrayList elements = new ArrayList();
        try{
            if(translator.getCurrentLocaleId() == 228){
                obj.put("title", zalo.getParametersValue("name-title-vi").toString());  
            }else{
                obj.put("title", zalo.getParametersValue("name-title-en").toString());
            }
        }catch (NullPointerException e) {
            obj.put("title", "Thông tin hồ sơ");            
        }
        
        try{
            if (!zalo.getParametersValue("application-template-id").toString().isBlank() && !zalo.getParametersValue("application-template-id").toString().isEmpty()){
                obj.put("template_id", zalo.getParametersValue("application-template-id").toString());
                obj.put("payload", "callback_data");
                typeTemplate = "business";
                obj.put("template_data", content);
                elements.add(obj);
            }else{
                obj.put("image_url", zalo.getParametersValue("message-thumbnail-link").toString());
                obj.put("subtitle", content.get("data"));
                obj1 = new JSONObject();
                obj1.put("type", "oa.open.url");
                obj1.put("url", zalo.getParametersValue("action-link").toString());
                obj.put("default_action", obj1);
                elements.add(obj);  
                typeTemplate = "list";
            }
            
        }catch (NullPointerException e) {
            //template old
            obj.put("image_url", zalo.getParametersValue("message-thumbnail-link").toString());
            obj.put("subtitle", content.get("data"));
            
            obj1 = new JSONObject();
            obj1.put("type", "oa.open.url");
            obj1.put("url", zalo.getParametersValue("action-link").toString());
            obj.put("default_action", obj1);
            elements.add(obj);  
            typeTemplate = "list";
        }        
        
        //tro ve trang chu
        try {
            obj = new JSONObject();
            if(translator.getCurrentLocaleId() == 228){
                obj.put("title", zalo.getParametersValue("name-icon-igate-vi").toString());  
            }else{
                obj.put("title", zalo.getParametersValue("name-icon-igate-en").toString());
            }
            
            obj.put("image_url", zalo.getParametersValue("link-icon-igate").toString());
            obj1 = new JSONObject();
            obj1.put("type", "oa.open.url");
            obj1.put("url", zalo.getParametersValue("action-link").toString());
            obj.put("default_action", obj1);
            elements.add(obj);
        } catch (NullPointerException e) {
            System.out.println("Link icon xem thêm");
        }
        //lien he
        try {
            obj = new JSONObject();
            if(translator.getCurrentLocaleId() == 228){
                obj.put("title", zalo.getParametersValue("name-qr-zalo-vi").toString());   
            }else{
                obj.put("title", zalo.getParametersValue("name-qr-zalo-en").toString());
            }
            obj.put("image_url", zalo.getParametersValue("link-qr-zalo").toString());
            obj1 = new JSONObject();
            obj1.put("type", "oa.open.url");
            obj1.put("url", zalo.getParametersValue("action-link").toString());
            obj.put("default_action", obj1);
            elements.add(obj);
        } catch (NullPointerException e) {
            System.out.println("Chưa cấu hình QR code");
        }
        
        obj = new JSONObject();
        obj.put("template_type", typeTemplate);
        obj.put("elements", elements);
        obj1 = new JSONObject();
        obj1.put("type", "template");
        obj1.put("payload", obj);
        
        obj = new JSONObject();
        obj.put("attachment", obj1);
        return obj;
    }
    
    private String convertDate(String date){
        if(date.length() > 19){
            date = date.substring(0,19);
            String dateConvert = "";
            
            String[] parts = date.split("T");
            String[] strdate = parts[0].split("-");
            dateConvert = strdate[2] + "/" + strdate[1] + "/" + strdate[0] + " " + parts[1];
            return dateConvert;
        }
        return null;
    }
   
//  tạm thời tắt invite vì hiện tại chỉ dùng được cho sdt của OA, các khách hàng khác thì phải đăng kí quota
//    public void sendInvite(IntegratedConfigurationDto config, String phone, String name) throws Exception {
//        this.token = config.getParametersValue("access-token").toString();
//        if (phone != null){
//            JSONObject body = new JSONObject();
//            JSONObject obj = new JSONObject();
//            JSONObject data = new JSONObject();
//            
//
//            data.put("phone", phone);
//            body.put("recipient", data);
//            
//            data = new JSONObject();
//            data.put("username", name);
//            obj.put("template_data", data);
//            obj.put("template_id", config.getParametersValue("follow-template-id").toString());
//            obj.put("payload", "callback_data");
//            
//            ArrayList elements = new ArrayList();
//            elements.add(obj);
//            data = new JSONObject();
//            data.put("template_type", "invite");
//            data.put("elements", elements);
//            
//            obj = new JSONObject();
//            obj.put("type", "template");
//            obj.put("payload", data);
//            
//            data = new JSONObject();
//            data.put("attachment", obj);
//            
//            body.put("message", data);
//            
//            //Create HttpEntity request
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);        
//            HttpEntity<String> request = new HttpEntity<>(body.toJSONString(), headers);
//            //Request endpoint
//            String endpoint = zaloRepository.OA_SEND_MESSAGE;
//            Map<String, Object> params = new HashMap<>();
//            params.put("access_token", token);
//            endpoint = endpoint + "?access_token={access_token}";
//            //Send request
//            ResponseEntity<String> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, String.class, params);
//            Gson g = new Gson();
//            SendZaloResponseDto response = g.fromJson(result.getBody(), SendZaloResponseDto.class);
//            if(!response.getError().equals(0)){
//                throw new Exception("ERROR: " + response.getError() + " ERROR_MESS: " + response.getMessage() + " DATA: " + response.getData());
//            }
//        }
//    }
    
    public boolean isValidPhone(String s) 
    { 
        if(s.startsWith("0") == true){
            Pattern p = Pattern.compile("(0)?[0-9]{10}"); 
            Matcher m = p.matcher(s); 
            return (m.find() && m.group().equals(s)); 
        }
        return false;
    } 
}