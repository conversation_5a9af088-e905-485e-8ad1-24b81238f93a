package vn.vnpt.digo.adapter.service;

import java.util.LinkedHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.token.JWTTokenRequestDto;
import java.lang.reflect.Field;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.HttpStatusCodeException;
import vn.vnpt.digo.adapter.api.TokenController;

@RefreshScope
@Service
public class TokenService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrlConfig;

    private Logger logger = LoggerFactory.getLogger(TokenService.class);
    
    public ResponseEntity<?> getToken(String urlStr, JWTTokenRequestDto jwtToken, String authorizationHeader) {
        String tokenUrl = tokenUrlConfig.replaceAll("auth/realms/digo", "") + urlStr;
        // Create request
        // Create header
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        if (authorizationHeader != null && !authorizationHeader.isEmpty()) {
            headers.set("Authorization", authorizationHeader);
        }
        //Create request with body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        
        for (Field field : jwtToken.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                requestBody.set(field.getName(), field.get(jwtToken).toString());
            } catch (Exception e) {
            }
        }
        HttpEntity<?> request = new HttpEntity<>(requestBody, headers);
        restTemplate.getMessageConverters().add(new FormHttpMessageConverter());
        ResponseEntity<?> result;
        try {
            result = restTemplate.exchange(tokenUrl, HttpMethod.POST, request, Object.class);
        } catch(HttpStatusCodeException e) {
            result = ResponseEntity.status(e.getRawStatusCode()).headers(e.getResponseHeaders())
                    .body(e.getResponseBodyAsString());
        }
        // Return result
        return result;
    }
}
