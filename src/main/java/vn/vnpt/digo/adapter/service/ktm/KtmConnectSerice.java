package vn.vnpt.digo.adapter.service.ktm;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.ktm.SchemaIoficeDto;
import vn.vnpt.digo.adapter.dto.ktm.requestGetVbIofficeDto;
import vn.vnpt.digo.adapter.dto.ktm.requestTokenIofficeDto;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Objects;

@Service
public class KtmConnectSerice {

    private static RestTemplate restTemplate = new RestTemplate();

    public requestTokenIofficeDto getTokens(requestTokenIofficeDto requestBody ){

        requestTokenIofficeDto res = MicroserviceExchange.post("https://kontum.vnptioffice.vn/qlvb_ktm/api/login/v3/", requestBody, requestTokenIofficeDto.class);
        return res;
    }

    public String getToken(requestTokenIofficeDto requestBody) {

            String url = "https://kontum.vnptioffice.vn/qlvb_ktm/api/login/v3/";

            String result = MicroserviceExchange.postJsonNoAuth(restTemplate, url, requestBody, String.class );

            return result;
    }

    private String convertObjectToJson(Object object) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error converting object to JSON");
        }
    }

    public String getVB(requestGetVbIofficeDto request){
        String token = request.getToken();
        String endpoint = "https://kontum.vnptioffice.vn/qlvb_ktm/api/document/getlistlookupbyparam/";
        String result = MicroserviceExchange.postJsonBearAuthIoffice(restTemplate, endpoint, token, request, String.class );

        return result;
    }

    public String getFile(requestGetVbIofficeDto request){
        String token = request.getToken();
        String param = request.getParam();
        String endpoint = "https://kontum.vnptioffice.vn/qlvb_ktm/api/file/getfileattach/" + param + "/";
        String result = MicroserviceExchange.getIoffice(restTemplate, endpoint, token, String.class );

        return result;
    }

    public String downloadBase64file(requestGetVbIofficeDto request){
        String token = request.getToken();
        String param = request.getParam();
        String endpoint = "https://kontum.vnptioffice.vn/qlvb_ktm/api/file/downloaddocument/" + param + "/";
        String result = MicroserviceExchange.getIoffice(restTemplate, endpoint, token, String.class );

        return result;
    }

}
