package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.FileUResDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.ereceipt.PostDossierReceiptDto;
import vn.vnpt.digo.adapter.dto.hcm_vietinfo_ereceipt.*;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.properties.bdg.IntegratedConfigurationProperties;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Oauth2RestTemplate;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.text.Normalizer;
import java.util.Base64;
import java.util.Date;
import java.util.Objects;
import java.util.regex.Pattern;

@Service
public class HCMVietInfoEReceiptService {

    @Autowired
    private IntegratedConfigurationService configurationService;
    private IntegratedConfigurationDto config;

    public static final String APP_NAME = "appName";
    public static final String PARTNER_CODE = "partnerCode";

    Logger logger = LoggerFactory.getLogger(HCMVietInfoEReceiptService.class);

    @Autowired
    private Microservice microservice;
    @Value("${digo.rest.connection.create.hcm}")
    private Boolean enableCreateRestHCM;
    @Autowired
    private Oauth2RestTemplate oauth2RestTemplate;

    @Autowired
    Translator translator;
    private String getAuthorizationCode() {
        String accessKey = config.getParametersValue("accessKey");
        String secretKey = config.getParametersValue("secretKey");
        String appName = config.getParametersValue("appName");
        String partnerCode = config.getParametersValue("partnerCode");
        String partnerCodeCus = config.getParametersValue("partnerCodeCus");

        //Khởi tạo Authorization code
        String jsonString = new JSONObject()
                .put("AccessKey", accessKey)
                .put("SecretKey", secretKey)
                .put("AppName", appName)
                .put("PartnerCode", partnerCode)
                .put("PartnerCodeCus", partnerCodeCus)
                .toString().replaceAll(",", ",\n").replaceAll("\\{", "{\n").replaceAll("}", "\n}");

        return Base64.getEncoder().encodeToString(jsonString.getBytes());
    }

    public EReceiptVietInfoDto previewEReceipt(IssueEReceiptBodyDto body) throws NoSuchAlgorithmException, KeyManagementException {
        config = configurationService.getConfig(body.getConfigId());

        IssueEReceiptBodyDto.VietInfoReceipt vietInfoReceiptBody = body.getVietInfoReceipt();
        vietInfoReceiptBody.setAppName(config.getParametersValue(APP_NAME));
        vietInfoReceiptBody.setPartnerCode(config.getParametersValue(PARTNER_CODE));

        String accessToken = getAuthorizationCode();
        String url = config.getParametersValue("XemTruocBienLai");
        RestTemplate restTemplate;
        if(this.enableCreateRestHCM)
        {
            restTemplate = this.oauth2RestTemplate.setCustomTimeoutsV2((Integer) config.getParameterValue("connect-timeout"), (Integer) config.getParameterValue("read-timeout"));
        }else{
            restTemplate = new RestTemplate();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("lgspaccesstoken", accessToken);
        HttpEntity<?> request = new HttpEntity<>(vietInfoReceiptBody, headers);
        try{
            String result = restTemplate.exchange(url, HttpMethod.POST, request, String.class).getBody();
            JSONObject res =  new JSONObject(result);
            Boolean statusResult =  res.getBoolean("Result");
            if (!statusResult){
                throw new DigoHttpException(11010, new String[]{res.getString("ErrorMessage")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
            ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            EReceiptVietInfoDto eReceiptVietInfo = objectMapper.readValue(res.getJSONObject("DataResult").toString(), EReceiptVietInfoDto.class);
            String fileName = toFileName(eReceiptVietInfo.getReceiptName());
            fileName = fileName + ".pdf";
            eReceiptVietInfo.setFileName(fileName);

            return eReceiptVietInfo;
        }catch (Exception e){
            throw new DigoHttpException(11010, new String[]{translator.toLocale("lang.word.hcm.vietinfo.ereceipt.failed")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    public AffectedRowsDto getListEReceipt(){

        return null;
    }

    public EReceiptVietInfoDto issueEReceipt(IssueEReceiptBodyDto body) throws NoSuchAlgorithmException, KeyManagementException {
        config = configurationService.getConfig(body.getConfigId());

        IssueEReceiptBodyDto.VietInfoReceipt vietInfoReceiptBody = body.getVietInfoReceipt();
        vietInfoReceiptBody.setAppName(config.getParametersValue(APP_NAME));
        vietInfoReceiptBody.setPartnerCode(config.getParametersValue(PARTNER_CODE));

        String accessToken = getAuthorizationCode();
        String url = config.getParametersValue("LapBienLaiThuPhi");
        RestTemplate restTemplate;
        if(this.enableCreateRestHCM)
        {
            restTemplate = this.oauth2RestTemplate.setCustomTimeoutsV2((Integer) config.getParameterValue("connect-timeout"), (Integer) config.getParameterValue("read-timeout"));
        }else {
              restTemplate = new RestTemplate();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("lgspaccesstoken", accessToken);
        HttpEntity<?> request = new HttpEntity<>(vietInfoReceiptBody, headers);
        try{
            String result = restTemplate.exchange(url, HttpMethod.POST, request, String.class).getBody();
            JSONObject res =  new JSONObject(result);
            Boolean statusResult =  res.getBoolean("Result");
            if (!statusResult){
                logger.error("DIGO-Error: Error VietInfo statusResult : " + res);
                logger.error("DIGO-Error: Error VietInfo Ma Ho So Loi : " + vietInfoReceiptBody.getDossierCode());
                throw new DigoHttpException(11010, new String[]{res.getString("ErrorMessage")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
            ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            EReceiptVietInfoDto eReceiptVietInfo = objectMapper.readValue(res.getJSONObject("DataResult").toString(), EReceiptVietInfoDto.class);
            String fileName = toFileName(eReceiptVietInfo.getReceiptName());
            fileName = fileName + ".pdf";
            eReceiptVietInfo.setFileName(fileName);

            //Upload file
            PostDossierReceiptDto.ReceiptData.File file = new PostDossierReceiptDto.ReceiptData.File();
            PostDossierReceiptDto dossierReceipt = buildDossierReceipt(body);
            dossierReceipt.getData().getVietInfoData().setTicket(eReceiptVietInfo.getTicket());
            try {
                String uploadUrl = microservice.filemanUri("/file/--multiple").toUriString();
                byte[] bytes = Base64.getDecoder().decode(eReceiptVietInfo.getBase64());
                String finalFileName = fileName;
                LinkedMultiValueMap<String, Object> requestMap = new LinkedMultiValueMap<>();
                ByteArrayResource byteArrayResource = new ByteArrayResource(bytes) {
                    @Override
                    public String getFilename() {
                        return finalFileName;
                    }
                };
                requestMap.add("files", byteArrayResource);
                FileUResDto[] lstFile = MicroserviceExchange.postMultipart(restTemplate, uploadUrl, requestMap, FileUResDto[].class);            
                if (lstFile.length > 0){
                    file.setId(lstFile[0].getId());
                    file.setName(finalFileName);
                }            
                dossierReceipt.getData().setFile(file);
            } catch (Exception e){
                logger.error("DIGO-Error: Error VietInfo Receipt Upload file : " + e);
                logger.error("DIGO-Error: Error VietInfo Ma Ho So Loi : " + vietInfoReceiptBody.getDossierCode());
            }
            try {
                String urlDossierReceipt = microservice.padmanUri("/dossier-receipt").toUriString();
                String token = MicroserviceExchange.getToken();
                String responseFromPadman = MicroserviceExchange.postJsonBearAuth(restTemplate, urlDossierReceipt, token, dossierReceipt, String.class);
                if (Objects.isNull(responseFromPadman)) {
                    logger.error("DIGO-Error: Error VietInfo Call Api DossierReceipt with body : " + vietInfoReceiptBody);
                }
            } catch (Exception e){
                logger.error("DIGO-Error: Error VietInfo MicroserviceExchange : " + e);
                logger.error("DIGO-Error: Error VietInfo Ma Ho So Loi : " + vietInfoReceiptBody.getDossierCode());
            }
            return eReceiptVietInfo;
        }catch (Exception e){
            logger.error("DIGO-Error: Error VietInfo Cannot create receipt : " + e);
            logger.error("DIGO-Error: Error VietInfo Cannot create receipt for dossier code : " + vietInfoReceiptBody.getDossierCode());
            throw new DigoHttpException(11010, new String[]{translator.toLocale("lang.word.hcm.vietinfo.ereceipt.failed")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    public EReceiptVietInfoDto viewEReceipt(ViewEReceiptBodyDto body){
        config = configurationService.getConfig(body.getConfigId());

        ViewEReceiptBodyDto.VietInfoReceipt vietInfoReceiptBody = body.getVietInfoReceipt();
        vietInfoReceiptBody.setAppName(config.getParametersValue(APP_NAME));
        vietInfoReceiptBody.setPartnerCode(config.getParametersValue(PARTNER_CODE));

        String accessToken = getAuthorizationCode();
        String url = config.getParametersValue("XemLaiBienLai");
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("lgspaccesstoken", accessToken);
        HttpEntity<?> request = new HttpEntity<>(vietInfoReceiptBody, headers);
        try{
            String result = restTemplate.exchange(url, HttpMethod.POST, request, String.class).getBody();
            JSONObject res =  new JSONObject(result);
            Boolean statusResult =  res.getBoolean("Result");
            if (!statusResult){
                throw new DigoHttpException(11010, new String[]{res.getString("ErrorMessage")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
            ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            EReceiptVietInfoDto eReceiptVietInfo = objectMapper.readValue(res.getJSONObject("DataResult").toString(), EReceiptVietInfoDto.class);
            String fileName = toFileName(eReceiptVietInfo.getReceiptName());
            fileName = fileName + ".pdf";
            eReceiptVietInfo.setFileName(fileName);

            return eReceiptVietInfo;
        }catch (Exception e){
            throw new DigoHttpException(11010, new String[]{translator.toLocale("lang.word.hcm.vietinfo.ereceipt.failed")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    public AffectedRowsDto cancelEReceipt(CanelEReceiptBodyDto body){
        config = configurationService.getConfig(body.getConfigId());

        CanelEReceiptBodyDto.VietInfoReceipt vietInfoReceiptBody = body.getVietInfoReceipt();
        vietInfoReceiptBody.setAppName(config.getParametersValue(APP_NAME));
        vietInfoReceiptBody.setPartnerCode(config.getParametersValue(PARTNER_CODE));

        String accessToken = getAuthorizationCode();
        String url = config.getParametersValue("HuyBienLaiThuPhi");
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("lgspaccesstoken", accessToken);
        HttpEntity<?> request = new HttpEntity<>(vietInfoReceiptBody, headers);
        try{
            String result = restTemplate.exchange(url, HttpMethod.POST, request, String.class).getBody();
            JSONObject res =  new JSONObject(result);
            Boolean statusResult =  res.getBoolean("Result");
            if (!statusResult){
                throw new DigoHttpException(11010, new String[]{res.getString("ErrorMessage")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
            return new AffectedRowsDto(1, res.getString("ErrorMessage"));
        }catch (Exception e){
            throw new DigoHttpException(11010, new String[]{translator.toLocale("lang.word.hcm.vietinfo.ereceipt.failed")}, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    public static String toFileName(String text) {
        String normalizedText = Normalizer.normalize(text, Normalizer.Form.NFD);
        Pattern pattern = Pattern.compile("\\p{InCombiningDiacriticalMarks}+");
        return StringUtils.stripAccents(pattern.matcher(normalizedText).replaceAll("").replaceAll("[.,]", "").replaceAll("[đĐ]", "d").toLowerCase());

    }

    public PostDossierReceiptDto buildDossierReceipt(IssueEReceiptBodyDto body){

        PostDossierReceiptDto dossierReceipt = body.getDossierReceipt();
        dossierReceipt.setStatus(1); //0 là đã hủy biên lai, 1 là đã phát hành (chưa thanh toán), 2 là đã phát hành và đã thanh toán
        PostDossierReceiptDto.ReceiptData receiptData =  dossierReceipt.getData();
        receiptData.setArisingDate(new Date());
        receiptData.setSubsystemId(IntegratedConfigurationProperties.VNPT_IGATE_SUBSYSTEM_ID);
        PostDossierReceiptDto.ReceiptData.VietInfoData vietInfoData = new PostDossierReceiptDto.ReceiptData.VietInfoData();
        vietInfoData.setReceiptCode(body.getVietInfoReceipt().getReceiptCode());
        receiptData.setVietInfoData(vietInfoData);
        dossierReceipt.setData(receiptData);
        if(Objects.nonNull(body.getExtendHCM())){
            dossierReceipt.setExtendHCM(body.getExtendHCM());
        }
        return dossierReceipt;
    }
}
