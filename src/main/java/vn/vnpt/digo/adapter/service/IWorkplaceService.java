package vn.vnpt.digo.adapter.service;


import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.bson.types.ObjectId;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.AffectedMessageDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.iworkplace.*;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.Parameters;
import vn.vnpt.digo.adapter.repository.IntegratedConfigurationRepository;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Type;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class IWorkplaceService {

    @Value("${digo.schedule.iworkplace-enable}")
    private Boolean iWorkplaceScheduleEnable;

    @Value("${digo.integration.iworkplace-enable}")
    private Boolean iWorkplaceEnable;

    @Autowired
    private IntegratedConfigurationRepository integratedConfigurationRepository;

    @Autowired
    private RestTemplate restTemplate;

    @Value(value = "https://gateway-onegov.vnpt.vn")
    private String URL;

    Logger logger = LoggerFactory.getLogger(IWorkplaceService.class);

    @Scheduled(fixedDelay = 1800000)
    ////@SchedulerLock(name = "syncUsers", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void syncUsers(){
        if(iWorkplaceScheduleEnable){
            logger.info("Start sync user and department from iWorkplace");
            String token = getToken();
            String createdDate = Instant.now().atOffset(ZoneOffset.UTC).minusMinutes(30).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            logger.info("Time start: "+createdDate);
            // thên giá trị createdDate vào các API bên dưới để lấy các tài khoản được tạo bởi 30 phút
            try{
                String url = this.URL+"/admin/api/users?page=0&size=10&activated.equals=true&sort=login,asc&createdDate.greaterThanOrEqual=";
                String usersJson = MicroserviceExchange.getWithTokenAuth(restTemplate,url,token,String.class);
                if (usersJson != null && !usersJson.isEmpty()) {
                    Gson gson = new Gson();
                    Type listType = new TypeToken<List<UserResponseDto>>() {}.getType();
                    List<UserResponseDto> userList = gson.fromJson(usersJson, listType);
                    for (UserResponseDto user : userList) {
                        // Bạn có thể truy cập các trường của mỗi đối tượng UserResponseDto ở đây
                        System.out.println("ID người dùng: " + user.getId());
                        System.out.println("Tên người dùng: " + user.getName());
                    }
                }
            }catch(Exception ex){
                logger.info("Error sync user");
                logger.info(ex.toString());
            }
            try{
                // Cần viết thêm dto lấy thông tin đơn vị
                String url = this.URL+"/admin/api/departments?page=0&size=50&activated.equals=true&createdDate.greaterThanOrEqual=";
                String departmentsJson = MicroserviceExchange.getWithTokenAuth(restTemplate,url,token,String.class);
                if (departmentsJson != null && !departmentsJson.isEmpty()) {
                    Gson gson = new Gson();
                    Type listType = new TypeToken<List<DepartmentResponseDto>>() {}.getType();
                    List<DepartmentResponseDto> departmentsList = gson.fromJson(departmentsJson, listType);
                    for (DepartmentResponseDto department : departmentsList) {
                        // Bạn có thể truy cập các trường của mỗi đối tượng DepartmentResponseDto ở đây
                        System.out.println("ID cơ quan: " + department.getId());
                        System.out.println("Tên cơ quan: " + department.getName());
                    }
                }
            }catch(Exception ex){
                logger.info("Error sysn department");
                logger.info(ex.toString());
            }
            logger.info("End sync user and department from iWorkplace");
        }else{
            logger.info("Digo.schedule.iworkplace-enable configuration parameter must be enabled.");
        }
    }

    public String getToken(){
        if(iWorkplaceEnable){
            IntegratedConfigurationDto integratedConfiguration = integratedConfigurationRepository.getByServiceId(new ObjectId("5f7c16069abb62f511890041"));
            if(Objects.nonNull(integratedConfiguration)){
                try {
                    MultiValueMap<String, Object> paramsQuery = new LinkedMultiValueMap<>();
                    for (Parameters parameters : integratedConfiguration.getParameters()) {
                        switch (parameters.getKey()) {
                            case "grant_type":
                                paramsQuery.add("grant_type", parameters.getOriginValue());
                                break;
                            case "username":
                                paramsQuery.add("username", parameters.getOriginValue());
                                break;
                            case "password":
                                paramsQuery.add("password", parameters.getOriginValue());
                                break;
                            case "client_id":
                                paramsQuery.add("client_id", parameters.getOriginValue());
                                break;
                            case "client_secret":
                                paramsQuery.add("client_secret", parameters.getOriginValue());
                                break;
                            case "scope":
                                paramsQuery.add("scope", parameters.getOriginValue());
                                break;
                            case "url":
                                this.URL = parameters.getOriginValue();
                                break;
                        }
                    }
                    String url = this.URL + "/oauth2/token";
                    TokenResponseDto token = MicroserviceExchange.postFormUrlEncoded(restTemplate, url, paramsQuery, TokenResponseDto.class);
                    return token.getAccess_token();
                }catch(Exception ex){
                    throw new DigoHttpException(10446, HttpServletResponse.SC_NOT_FOUND);
                }
            }else{
                throw new DigoHttpException(10446, HttpServletResponse.SC_NOT_FOUND);
            }
        }else{
            throw new DigoHttpException(10446, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public AffectedMessageDto postIWorkplace(IWorkplaceRequestDto iWorkplaceRequestDto){
        AffectedMessageDto affectedMessageDto = new AffectedMessageDto();
        try {
            String url = this.URL + "/integrate/api/posts";
            String rs = MicroserviceExchange.postJsonBearAuthStr(restTemplate, url, getToken(), iWorkplaceRequestDto, String.class);
            ObjectMapper objectMapper = new ObjectMapper();
            StatusResponseDto statusResponseDto = objectMapper.readValue(rs, StatusResponseDto.class);
            if(statusResponseDto.getStatus().equals("SUCCESS") || statusResponseDto.getStatus().equals("OK")){
                affectedMessageDto.setAffectedRows(1);
            }else{
                affectedMessageDto.setAffectedRows(0);
            }
            affectedMessageDto.setMessage(statusResponseDto.getMessage());
        }catch(Exception ex){
            System.out.println(ex);
        }
        return affectedMessageDto;
    }
}
