/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.adapter.document.MappingType;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.MappingTypeDto;
import vn.vnpt.digo.adapter.dto.MappingTypeInputDto;
import vn.vnpt.digo.adapter.dto.MappingTypeListDto;
import vn.vnpt.digo.adapter.dto.MappingTypeUpdateDto;
import vn.vnpt.digo.adapter.dto.PostDataLogToKafkaDto;
import vn.vnpt.digo.adapter.dto.PostDataLogToKafkaDto.Action;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.LogGroup;
import vn.vnpt.digo.adapter.pojo.LogType;
import vn.vnpt.digo.adapter.pojo.MappingTypeTrans;
import vn.vnpt.digo.adapter.repository.MappingTypeRepository;
import vn.vnpt.digo.adapter.stream.DataLoggerStream;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.KafkaExchange;
import vn.vnpt.digo.adapter.util.Translator;

/**
 *
 * <AUTHOR>
 */
@Service
public class MappingTypeService {

    @Autowired
    private Translator translator;

    @Autowired
    private MappingTypeRepository mappingTypeRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private DataLoggerStream dataLoggerStream;

    Logger logger = LoggerFactory.getLogger(IntegrationServiceService.class);

    public Page<MappingTypeListDto> getAll(String keyword, Integer status, Pageable pageable) {
        // Get deployment id
        ObjectId deploymentId = Context.getDeploymentId();
        // Create query
        Query query = new Query();
        if (Objects.nonNull(keyword) && !Objects.equals(keyword, "")) {
            query.addCriteria(Criteria.where("trans.languageId").is(translator.getCurrentLocaleId()));
            query.addCriteria(Criteria.where("trans.name").regex(keyword, "i"));
        }
        if (Objects.nonNull(status)) {
            query.addCriteria(Criteria.where("status").is(status));
        }
        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("ignoreDeployment").is(true),
                Criteria.where("deploymentId").is(deploymentId)
        );
        query.addCriteria(criteria);
        query.with(pageable);
        query.with(Sort.by(Sort.Order.desc("_id")));
        long offset = this.getOffset(pageable);
        query.skip(offset);
        query.limit(pageable.getPageSize());
        List<MappingTypeListDto> mappingTypes = mongoTemplate.find(query, MappingTypeListDto.class);
        Page<MappingTypeListDto> page = PageableExecutionUtils.getPage(mappingTypes, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), MappingTypeListDto.class));
        // Set number value
        int num = (page.getNumber() * page.getSize()) + 1;
        for (MappingTypeListDto item : page) {
            item.setNum(num++);
            item.setTransName(translator.getCurrentLocaleId());
        }

        return page;
    }

    public IdDto create(MappingTypeInputDto input) {
        MappingType newMappingType = new MappingType();
        if (Objects.nonNull(input.getId())) {
            newMappingType.setId(input.getId());
        }
        List<MappingTypeTrans> newTrans = new ArrayList<>();
        newTrans.add(new MappingTypeTrans(translator.getCurrentLocaleId(), input.getName(), input.getDescription()));
        newMappingType.setTrans(newTrans);
        newMappingType.setSourceDataAccess(input.getSourceDataAccess());
        newMappingType.setDestDataAccess(input.getDestDataAccess());
        newMappingType.setStatus(input.getStatus());
        newMappingType.setDeploymentId(Context.getDeploymentId());
        MappingType mappingType = mappingTypeRepository.save(newMappingType);
        KafkaExchange.pushLog(dataLoggerStream, mappingType.getId(), null, LogType.NEW_MAPPING_TYPE);

        return new IdDto(mappingType.getId());
    }

    public AffectedRowsDto update(ObjectId id, MappingTypeUpdateDto input) {
        AffectedRowsDto affectedRows = new AffectedRowsDto();
        List<Action> listAction = new ArrayList<>();
        try {
            MappingType oldMappingType = mappingTypeRepository.findById(id).get();
            if (!input.getName().isEmpty()) {
                MappingTypeTrans newTrans = new MappingTypeTrans(translator.getCurrentLocaleId(), input.getName(), input.getDescription());
                List<MappingTypeTrans> listTrans = oldMappingType.getTrans();
                //Update field MappingTypeTrans for Mapping Type
                if (!listTrans.contains(newTrans)) {//Update if not contains
                    boolean duplicate = listTrans.stream().anyMatch((tran) -> {
                        //Update if contains locale code
                        if (Objects.equals(tran.getLanguageId(), translator.getCurrentLocaleId())) {
                            if (!Objects.equals(tran.getName(), newTrans.getName())) {
                                //Get originalName
                                String originalName = tran.getName();
                                tran.setName(newTrans.getName());
                                //Set acction
                                Action actionName = new Action("lang.word.name", originalName, newTrans.getName());
                                //Add action to listAction
                                listAction.add(actionName);
                                affectedRows.setAffectedRows(1);
                            }
                            if (!Objects.equals(tran.getDescription(), newTrans.getDescription())) {
                                //Get originalDescription
                                String originalDescription = tran.getDescription();
                                tran.setDescription(newTrans.getDescription());
                                //Set acction
                                Action actionDescription = new Action("lang.word.description", originalDescription, newTrans.getDescription());
                                //Add action to listAction
                                listAction.add(actionDescription);
                                affectedRows.setAffectedRows(1);
                            }
                        }
                        return Objects.equals(tran.getLanguageId(), translator.getCurrentLocaleId());
                    });
                    //Add new if not contains
                    if (!duplicate) {
                        listTrans.add(newTrans);
                        //Set acction
                        Action actionName = new Action("lang.word.title", null, newTrans.getName());
                        //Add action to listAction
                        listAction.add(actionName);
                        //Set acction
                        Action actionDescription = new Action("lang.word.content", null, newTrans.getDescription());
                        //Add action to listAction
                        listAction.add(actionDescription);
                        affectedRows.setAffectedRows(1);
                    }
                }
            }
            if (!Objects.equals(oldMappingType.getSourceDataAccess(), input.getSourceDataAccess())) {
                Action actionSource;
                if(Objects.nonNull(oldMappingType.getSourceDataAccess())){
                    actionSource = new Action("lang.word.source", oldMappingType.getSourceDataAccess().toString(), input.getSourceDataAccess().toString());
                } else {
                    actionSource = new Action("lang.word.source", "", input.getSourceDataAccess().toString());
                }
                listAction.add(actionSource);
                oldMappingType.setSourceDataAccess(input.getSourceDataAccess());
                affectedRows.setAffectedRows(1);
            }
            if (!Objects.equals(oldMappingType.getDestDataAccess(), input.getDestDataAccess())) {
                Action actionDest;
                if(Objects.nonNull(oldMappingType.getDestDataAccess())){
                    actionDest = new Action("lang.word.content", oldMappingType.getDestDataAccess().toString(), input.getDestDataAccess().toString());
                } else {
                    actionDest = new Action("lang.word.content", "", input.getDestDataAccess().toString());
                }
                listAction.add(actionDest);
                oldMappingType.setDestDataAccess(input.getDestDataAccess());
                affectedRows.setAffectedRows(1);
            }
            if (Objects.nonNull(oldMappingType.getStatus()) && !oldMappingType.getStatus().equals(input.getStatus())) {
                Action actionStatus = new Action("lang.word.content", oldMappingType.getStatus().toString(), input.getStatus().toString());
                listAction.add(actionStatus);
                oldMappingType.setStatus(input.getStatus());
                affectedRows.setAffectedRows(1);
            }
            if (affectedRows.getAffectedRows() == 1) {
                Date updatedDate = new Date();
                PostDataLogToKafkaDto.Action actionUpdatedDate = new PostDataLogToKafkaDto.Action("lang.phrase.updated-date", oldMappingType.getUpdatedDate().toString(), updatedDate.toString());
                listAction.add(actionUpdatedDate);
                oldMappingType.setUpdatedDate(updatedDate);
                mappingTypeRepository.save(oldMappingType);
                if (!listAction.isEmpty()) {
                    KafkaExchange.pushLog(dataLoggerStream, oldMappingType.getId(), listAction, LogType.UPDATE_MAPPING_TYPE);
                }
            }
        } catch (Exception e) {
            logger.info(e.getMessage());
            e.printStackTrace();
            throw new DigoHttpException(10005, HttpServletResponse.SC_BAD_REQUEST);
        }

        return affectedRows;
    }

    public AffectedRowsDto delete(ObjectId id) {
        AffectedRowsDto affectedRows = new AffectedRowsDto();
        if (mappingTypeRepository.deleteMappingTypeById(id) != 0) {
            affectedRows.setAffectedRows(1);
            KafkaExchange.pushLog(dataLoggerStream, id, null, LogType.DELETE_MAPPING_TYPE);
        }

        return affectedRows;
    }

    public MappingTypeDto getById(ObjectId id) {
        ObjectId deploymentId = Context.getDeploymentId();
        MappingTypeDto mappingType = mappingTypeRepository.getByIdAndDeploymentId(id, deploymentId);
        if (Objects.nonNull(mappingType)) {
            return mappingType;
        } else {
            throw new DigoHttpException(11001, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private long getOffset(Pageable page) {
        return page.getPageNumber() * page.getPageSize();
    }
}
