package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.gson.Gson;
import com.google.gson.JsonParser;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.tbnohtttl_qni.GuiThongTinHoSoDto;
import vn.vnpt.digo.adapter.dto.tbnohtttl_qni.GuiThongTinXuLyHoSoDto;
import vn.vnpt.digo.adapter.dto.tbnohtttl_qni.TbnohtttlReqDto;
import vn.vnpt.digo.adapter.dto.tbnohtttl_qni.TbnohtttlResDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Service
public class TBNOHTTTLQNIService {

    Logger logger = LoggerFactory.getLogger(PaymentPlatformService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private RestTemplate restTemplate;

    private Gson gson = new Gson();

    private final ObjectId serviceId = new ObjectId("5f7c1606969692f511890038");

    @Autowired
    private Microservice microservice;

    @Autowired
    private MongoTemplate mongoTemplate;

    private String getToken(String tokenUrl, String consumerKey, String consumerSecret, String tokenValue) {
        String tokenStr = "";

        if (tokenValue != null && !tokenValue.isEmpty()) {
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.set("Authorization", tokenValue);

                MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
                map.add("grant_type", "client_credentials");

                HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<MultiValueMap<String, String>>(map,
                        headers);

                JSONObject response = new JSONObject(
                        restTemplate.exchange(tokenUrl, HttpMethod.POST, entity, String.class).getBody());
                logger.info("Response from " + tokenUrl + ": " + response);
                tokenStr = response.getString("access_token");
                logger.info("DIGO-Info: getToken value = " + tokenStr);
            } catch (Exception e) {
                logger.info("DIGO-Info: getToken Exception " + e.getMessage());
            }
        } else {
            try {
                ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
                details.setAccessTokenUri(tokenUrl);
                details.setClientId(consumerKey);
                details.setClientSecret(consumerSecret);
                details.setGrantType("client_credentials");
                tokenStr = new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext()).getAccessToken()
                        .getValue();
                logger.info("DIGO-Info: getToken value = " + tokenStr);
            } catch (Exception e) {
                logger.info("DIGO-Info: getToken Exception " + e.getMessage());
            }

        }

        return tokenStr;
    }

    public TbnohtttlResDto.SendInfoDossierResponse sendInfoDossier(List<GuiThongTinHoSoDto> params) {
        logger.info("DIGO-Info: begin sendInfoDossier with MaHoSoMotCua = " + params.get(0).getMaHoSo());

        //Get config
        IntegratedConfigurationDto config;
        var result = new TbnohtttlResDto.SendInfoDossierResponse();
        result.setStatus(false);
        result.setMessage("");

        if (Objects.nonNull(params.get(0).getConfigId())) {
            config = configurationService.getConfig(params.get(0).getConfigId());
        } else {
            config = configurationService.getConfig(params.get(0).getAgencyId(), params.get(0).getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("gui-thong-tin-ho-so-url").toString();
            String consumerKey = config.getParametersValue("consumer-key");
            String consumerSecret = config.getParametersValue("consumer-secret");
            String tokenValue = config.getParametersValue("token-value");

            String token = getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);

            //setTimeout(restTemplate, 60000);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), TbnohtttlResDto.SendInfoDossierResponse.class);
                if (response.getStatus() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    result.setMessage(jsonModelInput);
                    result.setStatus(false);
                } else {
                    result = response;
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                result.setMessage(jsonModelInput);
                result.setStatus(false);
            }
        } catch (Exception ex) {
            result.setMessage(ex.getMessage());
            result.setStatus(false);
        }

        return result;
    }

    public TbnohtttlResDto.GetInfoDossierResponse getInfoDossier(String fromDate, String toDate, String configId, String maDonVi) {
        logger.info(String.format("getInfoDossier fromdate: %s toDate: %s", fromDate, toDate));

        //Get config
        IntegratedConfigurationDto config;
        var result = new TbnohtttlResDto.GetInfoDossierResponse();
        result.setStatus(false);
        result.setMessage("");

        config = configurationService.getConfig(new ObjectId(configId));

        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("lay-thong-tin-ho-so-url").toString();
            String consumerKey = config.getParametersValue("consumer-key");
            String consumerSecret = config.getParametersValue("consumer-secret");
            String tokenValue = config.getParametersValue("token-value");
            String procedureId = config.getParametersValue("procedure-id").toString();
            String procedureFormId = config.getParametersValue("procedure-id-form").toString();

            String token = getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);

            //setTimeout(restTemplate, 60000);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            Map<String, Object> params = new HashMap<>();
            params.put("tuNgay", fromDate);
            params.put("denNgay", toDate);
            params.put("maDonVi", maDonVi);

            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.GET, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), TbnohtttlResDto.GetInfoDossierResponse.class);
                if (response.getStatus() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    result.setMessage(jsonModelInput);
                    result.setStatus(false);
                } else if (response.getStatus()) {

                    // sync dossier to padman
                    var getDossierRequest = new TbnohtttlReqDto.GetInfoDossierRequest();
                    getDossierRequest.setRequest(response.getData());

                    var urlStringFormat = String.format("/tbnohtttlqni/--get-info-dossier?procedure-id=%s&procedure-form-id=%s", procedureId, procedureFormId);
                    String urlPadman = microservice.padmanUri(urlStringFormat).toUriString();
                    var responseFromPadmanString = MicroserviceExchange.postJson(restTemplate, urlPadman, getDossierRequest, String.class);

                    if (responseFromPadmanString != null) {
                        var responseFromPadmanObject = new JsonParser().parse(responseFromPadmanString).getAsJsonObject();
                        var affectedRow = responseFromPadmanObject.get("affectedRows").getAsInt();
                        var message = responseFromPadmanObject.get("message").getAsString();
                        result.setMessage(message);

                        if (affectedRow == 1)
                            result.setStatus(true);
                    }
                } else {
                    result = response;
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                result.setMessage(jsonModelInput);
                result.setStatus(false);
            }
        } catch (Exception ex) {
            result.setMessage(ex.getMessage());
            result.setStatus(false);
        }

        return result;
    }

    public TbnohtttlResDto.SendInfoProcessDossierResponse sendInfoProcessDossier(List<GuiThongTinXuLyHoSoDto> params) {
        logger.info("DIGO-Info: begin SendInfoProcessDossierResponse with MaHoSoMotCua = " + params.get(0).getMaHoSo());

        //Get config
        IntegratedConfigurationDto config;
        var result = new TbnohtttlResDto.SendInfoProcessDossierResponse();
        result.setStatus(false);
        result.setMessage("");

        if (Objects.nonNull(params.get(0).getConfigId())) {
            config = configurationService.getConfig(params.get(0).getConfigId());
        } else {
            config = configurationService.getConfig(params.get(0).getAgencyId(), params.get(0).getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("gui-thong-tin-xu-ly-ho-so-url").toString();
            String consumerKey = config.getParametersValue("consumer-key");
            String consumerSecret = config.getParametersValue("consumer-secret");
            String tokenValue = config.getParametersValue("token-value");

            String token = getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);

            //setTimeout(restTemplate, 60000);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), TbnohtttlResDto.SendInfoProcessDossierResponse.class);
                if (response.getStatus() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    result.setMessage(jsonModelInput);
                    result.setStatus(false);
                } else {
                    result = response;
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                result.setMessage(jsonModelInput);
                result.setStatus(false);
            }
        } catch (Exception ex) {
            result.setMessage(ex.getMessage());
            result.setStatus(false);
        }

        return result;
    }

}
