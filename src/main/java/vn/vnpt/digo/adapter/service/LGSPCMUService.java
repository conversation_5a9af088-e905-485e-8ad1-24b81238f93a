package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.cmu.IofficeRequestDto;
import vn.vnpt.digo.adapter.dto.cmu.IofficeResponseDto;
import vn.vnpt.digo.adapter.dto.dbn.DBNAgencyDto;
import vn.vnpt.digo.adapter.dto.event_log.PostEventLogDto;
import vn.vnpt.digo.adapter.dto.minhtue.TokenResDto;
import vn.vnpt.digo.adapter.dto.minhtue.criminalrecords.*;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.CoopDossierDetailDto;
import vn.vnpt.digo.adapter.pojo.HouseDossierDetailDto;
import vn.vnpt.digo.adapter.pojo.IntegratedService;
import vn.vnpt.digo.adapter.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Component
public class LGSPCMUService {
    private ObjectId serviceId = new ObjectId("65f125b0cf42927c807fada5");
    Logger logger = LoggerFactory.getLogger(LGSPCMUService.class);

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private IntegratedLogsService integratedLogsService;

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    //lgsp cà mau
    @Value(value = "${lgspminhtue.kgg.configId}")
    private String lgspCMUConfigId;

    @Value("${vnpt.permission.check-user}")
    private String scope;

    @Autowired
    private EventLogService eventLogService;

    IntegratedConfigurationDto config;

    private String getToken(IntegratedConfigurationDto config) {
        String tokenUrl = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String strConsumer = consumerKey + ":" + consumerSecret;
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(tokenUrl);
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();

        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(auth);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST, request, Object.class);
            logger.info("Http result:");
            System.out.println(result);
            TokenResDto token = GsonUtils.copyObject(result.getBody(), TokenResDto.class);
            return token.getAccessToken();
        } catch (Exception e) {
            logger.info("Error calling http: ", e.getMessage());
            throw new DigoHttpException(11003, new String[]{"get token LGSP Ca Mau:", e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    public DBNAgencyDto getAgency(String id){
        String agencyUrl = microservice.basedataUri("/agency/" + id).toUriString();
        return MicroserviceExchange.get(new RestTemplate(), agencyUrl, DBNAgencyDto.class);
    }

    public IofficeResponseDto getDanhSachVanBanIoffice(IntegrationParamsDto params, IofficeRequestDto body) throws Exception {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }

        DBNAgencyDto agencyFully = getAgency(params.getAgencyId().toHexString());
        String agencyCode = "";
        if (Objects.nonNull(agencyFully.getExtendBDG()) && Objects.nonNull(agencyFully.getExtendBDG().getCodeNationalPublicBDG())){
            agencyCode = agencyFully.getExtendBDG().getCodeNationalPublicBDG();
        } else {
            agencyCode = agencyFully.getCode();
        }

        String token = getToken(config);
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        String tuNgay = body.getTuNgay();
        if(tuNgay != null && !tuNgay.isEmpty()){
            tuNgay = tuNgay.substring(0, 10);
            String[] arr = tuNgay.split("-");
            tuNgay = arr[2] + "/" + arr[1] + "/" + arr[0];
        }

        String denNgay = body.getDenNgay();
        if(denNgay != null && !denNgay.isEmpty()){
            denNgay = denNgay.substring(0, 10);
            String[] arr = denNgay.split("-");
            denNgay = arr[2] + "/" + arr[1] + "/" + arr[0];
        }

        Gson gson = new Gson();
        HashMap<String, Object> formData = new HashMap<>();
        formData.put("page", body.getPage());
        formData.put("size", body.getSize());
        formData.put("so_ky_hieu", body.getSoKyHieu());
        formData.put("trich_yeu", body.getTrichYeu());
        formData.put("ten_co_quan_ban_hanh", "");
        formData.put("ma_dinh_danh_dv", agencyCode);
        formData.put("nam", "0");
        formData.put("ma_ctcb_van_thu", "");
        formData.put("ma_don_vi_quan_tri", "");
        formData.put("tu_ngay", tuNgay);
        formData.put("den_ngay", denNgay);

        HttpEntity<?> request = new HttpEntity<>(formData, headers);
        ResponseEntity<String> result = restTemplate.exchange(
                config.getParametersValue("iOfficeLayDanhSachVanBan").toString(),
                HttpMethod.POST, request, String.class);
        IofficeResponseDto res = gson.fromJson(result.getBody(), IofficeResponseDto.class);
        return res;
    }

    public List<IofficeResponseDto.ResponseFileDto> getDanhSachFileVanBanIoffice(IntegrationParamsDto params, String maVanBanDi) throws Exception {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }

        String token = getToken(config);
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        Gson gson = new Gson();
        HashMap<String, Object> formData = new HashMap<>();
        formData.put("chuoi_ma_vbdi", maVanBanDi);
        HttpEntity<?> request = new HttpEntity<>(formData, headers);
        ResponseEntity<String> result = restTemplate.exchange(
                config.getParametersValue("iOfficeLayFileVanBan").toString(),
                HttpMethod.POST, request, String.class);
        Type listType = new TypeToken<List<IofficeResponseDto.ResponseFileDto>>() {}.getType();
        List<IofficeResponseDto.ResponseFileDto> res = gson.fromJson(result.getBody(), listType);
        return res;
    }

    public EntInfoDetailDto getEntInfoCmuDto(BusinessRegistrationParamsCMUDto params) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new EntInfoDetailDto();
        }
        String gateway = config.getParametersValue("gateway").toString();
        String token = getToken(config);
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
//        String url = gateway + "/apiDKKD/1.0/DoanhNghiep/chiTietDoanhNghiep";
        String url = config.getParametersValue("chiTietDoanhNghiep").toString();
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?msdn=" + params.getMsdn(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            JsonObject json = jsonObject.get("GetEntCert").getAsJsonArray().get(0).getAsJsonObject();
            EntInfoDetailDto result = g.fromJson(json, EntInfoDetailDto.class);
            return result;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public CoopInfoDetailDto getCoopInfoDetailDto(BusinessRegistrationParamsCMUDto params) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new CoopInfoDetailDto();
        }
        String gateway = config.getParametersValue("gateway").toString();
        String token = getToken(config);
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
//        String url = gateway + "/apiDKKD/1.0/HopTacXa/chiTietHopTacXa";
        String url = config.getParametersValue("chiTietHopTacXa").toString();
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?mst=" + params.getMst(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            JsonObject json = jsonObject.get("CoopCert").getAsJsonArray().get(0).getAsJsonObject();
            CoopInfoDetailDto result = g.fromJson(json, CoopInfoDetailDto.class);
            return result;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public HouseInfoDetailDto getHouseInfoDetailDto(BusinessRegistrationParamsCMUDto params) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new HouseInfoDetailDto();
        }
        String gateway = config.getParametersValue("gateway").toString();
        String token = getToken(config);
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
//        String url = gateway + "/apiDKKD/1.0/HoKinhDoanh/chiTietHoKinhDoanh";
        String url = config.getParametersValue("chiTietHoKinhDoanh").toString();
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?mst=" + params.getMst(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            JsonObject json = jsonObject.get("HHCert").getAsJsonArray().get(0).getAsJsonObject();
            HouseInfoDetailDto result = g.fromJson(json, HouseInfoDetailDto.class);
            return result;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    // get detail dossier
    public EntDossierDetailDto getEntDosserDetailCmuDto(String processId, String token, String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
//        String url = gateway + "/apiDKKD/1.0/DoanhNghiep/thongTinHoSo";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?processID=" + processId, HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            if (jsonObject != null) {
                if (jsonObject.get("DongBoHoSoMC_DP") != null && jsonObject.get("DongBoHoSoMC_DP").getAsJsonArray().size()>0) {
                    JsonObject json = jsonObject.get("DongBoHoSoMC_DP").getAsJsonArray().get(0).getAsJsonObject();
                    EntDossierDetailDto result = g.fromJson(json, EntDossierDetailDto.class);
                    return result;
                }
            }
            return null;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public CoopDossierDetailDto getCoopDosserDetailCmuDto(String processId, String token, String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
//        String url = gateway + "/apiDKKD/1.0/HopTacXa/thongTinHoSo";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?processID=" + processId, HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            if (jsonObject != null) {
                if (jsonObject.get("GetDocCoop") != null && jsonObject.get("GetDocCoop").getAsJsonArray().size()>0) {
                    JsonObject json = jsonObject.get("GetDocCoop").getAsJsonArray().get(0).getAsJsonObject();
                    CoopDossierDetailDto result = g.fromJson(json, CoopDossierDetailDto.class);
                    return result;
                }
            }
            return null;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public HouseDossierDetailDto getHouseDosserDetailCmuDto(String processId, String token, String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
//        String url = gateway + "/apiDKKD/1.0/HoKinhDoanh/thongTinHoSo";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?processID=" + processId, HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            if (jsonObject != null) {
                if (jsonObject.get("DongBoHoSoMC_DP") != null && jsonObject.get("DongBoHoSoMC_DP").getAsJsonArray().size()>0) {
                    JsonObject json = jsonObject.get("DongBoHoSoMC_DP").getAsJsonArray().get(0).getAsJsonObject();
                    HouseDossierDetailDto result = g.fromJson(json, HouseDossierDetailDto.class);
                    return result;
                }
            }
            return null;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    // get list dossier
    public Page<EntDossierDetailDto> getEntDossiersCmuDto(BusinessRegistrationParamsCMUDto params, Pageable pageable) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }

        List<EntDossierDetailDto> entDossierDetailDtos = new ArrayList<EntDossierDetailDto>();
        String gateway = config.getParametersValue("gateway").toString();
        String token = getToken(config);
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
//        String url = gateway + "/apiDKKD/1.0/DoanhNghiep/danhSachHoSo";
        String url = config.getParametersValue("danhSachHoSoDoanhNghiep").toString();
        String urlCT = config.getParametersValue("chiTietHoSoDoanhNghiep").toString();
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?from_date=" + params.getFromDate() + "&to_date=" + params.getToDate(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            int total = 0;
            if (response.getBody() != null) {
                JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
                JsonArray json = jsonObject.get("Ent_list").getAsJsonArray();
                Gson g = new Gson();
                List<Map<String, Object>> result = g.fromJson(json, ArrayList.class);

                total = result.size();
                int start = Math.toIntExact(pageable.getOffset());
                int end = (start + pageable.getPageSize()) > total ? total : (start + pageable.getPageSize());
                result = new PageImpl<>(result.subList(start, end), pageable, total).getContent();

                for (Map<String, Object> obj : result) {
                    String process_id = obj.get("process_id").toString();
                    EntDossierDetailDto entDossierDetailDto = this.getEntDosserDetailCmuDto(process_id, token, urlCT);
                    if (entDossierDetailDto != null) {
                        entDossierDetailDtos.add(entDossierDetailDto);
                    }
                }
            }
            return new PageImpl<>(entDossierDetailDtos, pageable, total);
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public Page<CoopDossierDetailDto> getCoopDossiersCmuDto(BusinessRegistrationParamsCMUDto params, Pageable pageable) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
        List<CoopDossierDetailDto> coopDossierDetailDtos = new ArrayList<CoopDossierDetailDto>();
        String gateway = config.getParametersValue("gateway").toString();
        String token = getToken(config);
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
//        String url = gateway + "/apiDKKD/1.0/HopTacXa/danhSachHoSo";
        String url = config.getParametersValue("danhSachHoSoHopTacXa").toString();
        String urlCT = config.getParametersValue("chiTietHoSoHopTacXa").toString();
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?from_date=" + params.getFromDate() + "&to_date=" + params.getToDate(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            int total = 0;
            if (response.getBody() != null) {
                JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
                JsonArray json = jsonObject.get("Coop_doc_list").getAsJsonArray();
                Gson g = new Gson();
                List<Map<String, Object>> result = g.fromJson(json, ArrayList.class);

                total = result.size();
                int start = Math.toIntExact(pageable.getOffset());
                int end = (start + pageable.getPageSize()) > total ? total : (start + pageable.getPageSize());

                result = new PageImpl<>(result.subList(start, end), pageable, total).getContent();

                for (Map<String, Object> obj : result) {
                    String process_id = obj.get("process_id").toString();
                    CoopDossierDetailDto coopDossierDetailDto = this.getCoopDosserDetailCmuDto(process_id, token, urlCT);
                    if (coopDossierDetailDto != null) {
                        coopDossierDetailDtos.add(coopDossierDetailDto);
                    }
                }
            }
            return new PageImpl<>(coopDossierDetailDtos, pageable, total);
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public Page<HouseDossierDetailDto> getHouseDossiersCmuDto(BusinessRegistrationParamsCMUDto params, Pageable pageable) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
        List<HouseDossierDetailDto> houseDossierDetailDtos = new ArrayList<HouseDossierDetailDto>();

        String gateway = config.getParametersValue("gateway").toString();
        String token = getToken(config);
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
//        String url = gateway + "/apiDKKD/1.0/HoKinhDoanh/danhSachHoSo";
        String url = config.getParametersValue("danhSachHoSoHoKinhDoanh").toString();
        String urlCT = config.getParametersValue("chiTietHoSoHoKinhDoanh").toString();
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?from_date=" + params.getFromDate() + "&to_date=" + params.getToDate(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            int total = 0;
            if (response.getBody() != null) {
                JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
                JsonArray json = jsonObject.get("hh_list").getAsJsonArray();
                Gson g = new Gson();
                List<Map<String, Object>> result = g.fromJson(json, ArrayList.class);

                total = result.size();
                int start = Math.toIntExact(pageable.getOffset());
                int end = (start + pageable.getPageSize()) > total ? total : (start + pageable.getPageSize());
                result = new PageImpl<>(result.subList(start, end), pageable, total).getContent();

                for (Map<String, Object> obj : result) {
                    String process_id = obj.get("process_id").toString();
                    HouseDossierDetailDto houseDossierDetailDto = this.getHouseDosserDetailCmuDto(process_id, token, urlCT);
                    if (houseDossierDetailDto != null) {
                        houseDossierDetailDtos.add(houseDossierDetailDto);
                    }
                }
            }
            return new PageImpl<>(houseDossierDetailDtos, pageable, total);
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }
}
