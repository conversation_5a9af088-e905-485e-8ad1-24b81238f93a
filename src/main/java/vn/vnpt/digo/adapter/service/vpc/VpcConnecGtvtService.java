package vn.vnpt.digo.adapter.service.vpc;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.MinistryTransportDossierDto;
import vn.vnpt.digo.adapter.dto.StatisticTransportDossierDto;
import vn.vnpt.digo.adapter.dto.gtvt.DossierResponseDto;
import vn.vnpt.digo.adapter.dto.gtvt.GTVTGetDossierListResponseDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.service.IntegratedConfigurationService;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.util.*;


@Service
@Slf4j
public class VpcConnecGtvtService {
    @Autowired
    private Translator translator;
    @Autowired
    private IntegratedConfigurationService configurationService;
    @Autowired
    private LgspSavisService lgspVpcService;

    private static final ObjectId GTVT_SERVICE_ID = new ObjectId("66d918cad850957c9bd74d3f");

    private String tracuuHosoApi = "";
    private String thongkeHosoApi = "";
    private String danhsachHosoApi = "";
    private String maDonVi="SGTVT211";
    private final ObjectMapper objectMapper = new ObjectMapper();

    public void requestConfig(IntegratedConfigurationDto config) {
        this.tracuuHosoApi = config.getParametersValue("traCuuHoSo").toString();
        this.danhsachHosoApi = config.getParametersValue("danhSachHoSo").toString();
        this.thongkeHosoApi = config.getParametersValue("thongKeHoSo").toString();
        this.maDonVi= config.getParametersValue("gtvtMaDonVi").toString();
    }

    public MinistryTransportDossierDto getDetailTranSportDossier(String id) {
        IntegratedConfigurationDto config = configurationService.searchByServiceId(GTVT_SERVICE_ID);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        this.requestConfig(config);
        MinistryTransportDossierDto ministryTransportData = new MinistryTransportDossierDto();
        Map<String, Object> params = new HashMap<>();
        params.put("MaHoSo", id);
        String response = null;
        try {
            response = lgspVpcService.getRequest(null, this.tracuuHosoApi, params, String.class);
        } catch (Exception e) {
            log.error("tracuuHosoApi"+e.getMessage());
        }
        if(response == null || response.isBlank()) {
            return ministryTransportData;
        }
        Map<String, String> result = null;
        TypeReference<HashMap<String, String>> typeRef = new TypeReference<>() {};
        try {
            result = objectMapper.readValue(response, typeRef);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        if(result == null) {
            return ministryTransportData;
        }

        String kenhThucHien = result.get("KenhThucHien");
        if (kenhThucHien != null) {
            try {
                ministryTransportData.setKenhThucHien(Integer.parseInt(kenhThucHien));
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        String maHoSo = result.get("MaHoSo");
        String maTTHC = result.get("MaTTHC");
        String tenTTHC = result.get("TenTTHC");
        String tenLinhVuc = result.get("TenLinhVuc");
        String trichYeuHoSo = result.get("TrichYeuHoSo");
        String donViXuLy = result.get("DonViXuLy");
        String maDoiTuong = result.get("MaDoiTuong");
        String chuHoSo = result.get("ChuHoSo");
        String soDienThoai = result.get("SoDienThoai");
        String email = result.get("Email");
        String diaChi = result.get("DiaChi");
        String maTinh = result.get("MaTinh");
        String tenTinh = result.get("TenTinh");
        String tenHuyen = result.get("TenHuyen");
        String maHuyen = result.get("MaHuyen");
        String maXa = result.get("MaXa");
        String tenXa = result.get("TenXa");
        String ngayGuiHoSo = result.get("NgayGuiHoSo");
        String ngayTiepNhan = result.get("NgayTiepNhan");
        String ngayHenTra = result.get("NgayHenTra");
        String ngayTra = result.get("NgayTra");
        String trangThaiHoSo = result.get("TrangThaiHoSo");
        String tenTrangThaiHoSo = result.get("TenTrangThaiHoSo");
        String nguoiNop = result.get("NguoiNop");
        String maNguoiNop = result.get("MaNguoiNop");
        String dienThoaiNguoiNop = result.get("DienThoaiNguoiNop");
        String emaiNguoiNop = result.get("EmaiNguoiNop");
        String ngayCapNhat = result.get("NgayCapNhat");
        String ngayHuyHoSo = result.get("NgayHuyHoSo");
        String maDonViXuLy = result.get("MaDonViXuLy");
        String ngayKetThucXuLy = result.get("NgayKetThucXuLy");

        ministryTransportData.setMaSoHoSo(maHoSo);
        ministryTransportData.setMaTTHC(maTTHC);
        ministryTransportData.setTenTTHC(tenTTHC);
        ministryTransportData.setTenLinhVuc(tenLinhVuc);
        ministryTransportData.setTrichYeuHoSo(trichYeuHoSo);
        ministryTransportData.setDonViXuLy(donViXuLy);
        ministryTransportData.setMaDoiTuong(maDoiTuong);
        ministryTransportData.setChuHoSo(chuHoSo);
        ministryTransportData.setSoDienThoai(soDienThoai);
        ministryTransportData.setEmail(email);
        ministryTransportData.setDiaChi(diaChi);
        ministryTransportData.setMaTinh(maTinh);
        ministryTransportData.setTenTinh(tenTinh);
        ministryTransportData.setTenHuyen(tenHuyen);
        ministryTransportData.setMaHuyen(maHuyen);
        ministryTransportData.setMaXa(maXa);
        ministryTransportData.setTenXa(tenXa);
        ministryTransportData.setNgayGuiHoSo(ngayGuiHoSo);
        ministryTransportData.setNgayTiepNhan(ngayTiepNhan);
        ministryTransportData.setNgayHenTra(ngayHenTra);
        ministryTransportData.setNgayTra(ngayTra);
        ministryTransportData.setTrangThaiHoSo(trangThaiHoSo);
        ministryTransportData.setTenTrangThaiHoSo(tenTrangThaiHoSo);
        ministryTransportData.setNguoiNop(nguoiNop);
        ministryTransportData.setMaNguoiNop(maNguoiNop);
        ministryTransportData.setDienThoaiNguoiNop(dienThoaiNguoiNop);
        ministryTransportData.setEmaiNguoiNop(emaiNguoiNop);
        ministryTransportData.setNgayCapNhat(ngayCapNhat);
        ministryTransportData.setNgayHuyHoSo(ngayHuyHoSo);
        ministryTransportData.setMaDonViXuLy(maDonViXuLy);
        ministryTransportData.setNgayKetThucXuLy(ngayKetThucXuLy);
        return ministryTransportData;
    }

    public Slice<MinistryTransportDossierDto> getDetailListDossier(String fromDate, String toDate, String trangthai, String mathutuc, Pageable pageable) {
        IntegratedConfigurationDto config = configurationService.searchByServiceId(GTVT_SERVICE_ID);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        this.requestConfig(config);
        Map<String, Object> params = new HashMap<>();
        params.put("ReceiveFromDate", fromDate);
        params.put("ReceiveToDate", toDate);
        params.put("Status", trangthai);
        params.put("MaThuTuc", mathutuc);
        GTVTGetDossierListResponseDto response = null;
        try {
            response = lgspVpcService.getRequest(null, this.danhsachHosoApi, params, GTVTGetDossierListResponseDto.class);
        } catch (Exception e) {
            log.error("danhSachHoSoApi"+e.getMessage());
        }
        List<MinistryTransportDossierDto> ministryTransportDataList = new ArrayList<>();
        if(response == null || response.getData() == null) {
            return new PageImpl<>(ministryTransportDataList, pageable,0);
        }
        for (DossierResponseDto item: response.getData()) {
                MinistryTransportDossierDto ministryTransportData = new MinistryTransportDossierDto();
                ministryTransportData.setMaSoHoSo(item.getMaHoSo());
                ministryTransportData.setMaTTHC(item.getMaTTHC());
                ministryTransportData.setTenTTHC(item.getTenTTHC());
                ministryTransportData.setTenLinhVuc(item.getTenLinhVuc());
                ministryTransportData.setTrichYeuHoSo(item.getTrichYeuHoSo());
                ministryTransportData.setDonViXuLy(item.getDonViXuLy());
                ministryTransportData.setMaDoiTuong(item.getMaDoiTuong());
                ministryTransportData.setChuHoSo(item.getChuHoSo());
                ministryTransportData.setSoDienThoai(item.getSoDienThoai());
                ministryTransportData.setEmail(item.getEmail());
                ministryTransportData.setDiaChi(item.getDiaChi());
                ministryTransportData.setMaTinh(item.getMaTinh());
                ministryTransportData.setTenTinh(item.getTenTinh());
                ministryTransportData.setTenHuyen(item.getTenHuyen());
                ministryTransportData.setMaHuyen(item.getMaHuyen());
                ministryTransportData.setMaXa(item.getMaXa());
                ministryTransportData.setTenXa(item.getTenXa());
                ministryTransportData.setNgayGuiHoSo(item.getNgayGuiHoSo());
                ministryTransportData.setNgayTiepNhan(item.getNgayTiepNhan());
                ministryTransportData.setNgayHenTra(item.getNgayHenTra());
                ministryTransportData.setNgayTra(item.getNgayTra());
                ministryTransportData.setTrangThaiHoSo(item.getTrangThaiHoSo());
                ministryTransportData.setTenTrangThaiHoSo(item.getTenTrangThaiHoSo());
                ministryTransportData.setNguoiNop(item.getNguoiNop());
                ministryTransportData.setMaNguoiNop(item.getMaNguoiNop());
                ministryTransportData.setDienThoaiNguoiNop(item.getDienThoaiNguoiNop());
                ministryTransportData.setEmaiNguoiNop(item.getEmaiNguoiNop());
                ministryTransportData.setNgayHuyHoSo(item.getNgayHuyHoSo());
                ministryTransportData.setMaDonViXuLy(item.getMaDonViXuLy());
                ministryTransportData.setKenhThucHien(item.getKenhThucHien());
                ministryTransportData.setNgayKetThucXuLy(item.getNgayKetThucXuLy());
                ministryTransportDataList.add(ministryTransportData);
                
        }
        int numberOfElements = ministryTransportDataList.size();
        int pageSize = pageable.getPageSize();
        int currentPage = pageable.getPageNumber();
        int startItem = currentPage * pageSize;
        List<MinistryTransportDossierDto> pageList;

        if (ministryTransportDataList.size() < startItem) {
            pageList = Collections.emptyList();
        } else {
            int toIndex = Math.min(startItem + pageSize, ministryTransportDataList.size());
            pageList = ministryTransportDataList.subList(startItem, toIndex);
        }

        return new PageImpl<MinistryTransportDossierDto>(
                pageList, pageable, numberOfElements
        );
    }
    public StatisticTransportDossierDto getStatisticDossierTransport(String month, String year) {
        IntegratedConfigurationDto config = configurationService.searchByServiceId(GTVT_SERVICE_ID);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        this.requestConfig(config);
        StatisticTransportDossierDto statisticTransportDossierDto = new StatisticTransportDossierDto();
        Map<String, Object> params = new HashMap<>();
        params.put("Nam", year);
        params.put("Thang", month);
        params.put("MaDonVi", this.maDonVi);
        StatisticTransportDossierDto[] myObjects = null;
        try {
            myObjects = lgspVpcService.getRequest(null, this.thongkeHosoApi, params, StatisticTransportDossierDto[].class);
        } catch (Exception e) {
            log.error("thongkeHosoApi"+e.getMessage());
        }
        if(myObjects == null) {
            return statisticTransportDossierDto;
        }
        int abc = 0;
        int phanTramChuaXuLyTreHan=0;
        int soTonKyTruoc=0;
        int tongChuaXuLyTrongHan=0;
        int phanTramXuLyTreHan=0;
        int tongXuLyTreHan=0;
        int phanTramXuLyDungHan=0;
        int tongChuaXuLyTreHan=0;
        int soNhanTrongKy=0;
        int tongChuaXuLy=0;
        int thang=0;
        int nam=0;
        int phanTramChuaXuLyTrongHan=0;
        int tongDaXuLy=0;
        int tongXuLyDungHan=0;
        for (StatisticTransportDossierDto obj : myObjects) {
            abc = obj.getTongSoXuLy();
            phanTramChuaXuLyTreHan=obj.getPhanTramChuaXuLyTreHan();
            soTonKyTruoc=obj.getSoTonKyTruoc();
            tongChuaXuLyTrongHan=obj.getTongChuaXuLyTrongHan();
            phanTramXuLyTreHan=obj.getPhanTramXuLyTreHan();
            tongXuLyTreHan=obj.getTongXuLyTreHan();
            phanTramXuLyDungHan=obj.getPhanTramXuLyDungHan();
            tongChuaXuLyTreHan=obj.getTongChuaXuLyTreHan();
            soNhanTrongKy=obj.getSoNhanTrongKy();
            tongChuaXuLy=obj.getTongChuaXuLy();
            thang=obj.getThang();
            nam=obj.getNam();
            phanTramChuaXuLyTrongHan=obj.getPhanTramChuaXuLyTrongHan();
            tongDaXuLy=obj.getTongDaXuLy();
            tongXuLyDungHan=obj.getTongXuLyDungHan();
        };
        statisticTransportDossierDto.setPhanTramChuaXuLyTreHan(phanTramChuaXuLyTreHan);
        statisticTransportDossierDto.setTongSoXuLy(abc);
        statisticTransportDossierDto.setSoTonKyTruoc(soTonKyTruoc);
        statisticTransportDossierDto.setTongChuaXuLyTrongHan(tongChuaXuLyTrongHan);
        statisticTransportDossierDto.setPhanTramXuLyTreHan(phanTramXuLyTreHan);
        statisticTransportDossierDto.setTongXuLyTreHan(tongXuLyTreHan);
        statisticTransportDossierDto.setPhanTramXuLyDungHan(phanTramXuLyDungHan);
        statisticTransportDossierDto.setTongChuaXuLyTreHan(tongChuaXuLyTreHan);
        statisticTransportDossierDto.setSoNhanTrongKy(soNhanTrongKy);
        statisticTransportDossierDto.setTongChuaXuLy(tongChuaXuLy);
        statisticTransportDossierDto.setThang(thang);
        statisticTransportDossierDto.setNam(nam);
        statisticTransportDossierDto.setPhanTramChuaXuLyTrongHan(phanTramChuaXuLyTrongHan);
        statisticTransportDossierDto.setTongDaXuLy(tongDaXuLy);
        statisticTransportDossierDto.setTongXuLyDungHan(tongXuLyDungHan);
        return statisticTransportDossierDto;
    }

}


