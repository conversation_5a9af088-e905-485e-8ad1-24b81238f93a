/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.service;

import java.util.Objects;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationByIdDto;
import vn.vnpt.digo.adapter.repository.ChatbotRepository;
import vn.vnpt.digo.adapter.repository.IntegratedConfigurationRepository;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Translator;

@Service
public class ChatbotService {
    @Autowired
    private Translator translator;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private Microservice microservice;
    
    @Autowired
    private IntegratedConfigurationService configurationService;
    
    @Autowired
    private IntegratedConfigurationRepository configurationRepository;
    
    @Autowired
    private ChatbotRepository chatbotRepository;
    
    private ObjectId serviceId = new ObjectId("5f7c16069abb62f511890016");
    
    @Cacheable(value = "ChatbotService::findById", key = "{#agencyId, #subSystemId}")
    public IntegratedConfigurationByIdDto findById(ObjectId agencyId,ObjectId subSystemId ) {
        ObjectId deploymentId = Context.getDeploymentId();
        IntegratedConfigurationByIdDto integratedConfiguration = chatbotRepository.getIntegratedConfigurationById(agencyId, subSystemId, serviceId,deploymentId);
        if (Objects.nonNull(integratedConfiguration)) {
            integratedConfiguration.setTransSubsystem(translator.getCurrentLocaleId());
            integratedConfiguration.setTransParameters();
            integratedConfiguration.setTransApplyAgencies(translator.getCurrentLocaleId());
            integratedConfiguration.setTransExceptAgencies(translator.getCurrentLocaleId());
            integratedConfiguration.setTransTag(translator.getCurrentLocaleId());
            return integratedConfiguration;
        } else {
            return null;
        }
    }
}
