package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.document.IntegratedGetBillEvent;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.GetDossierDetailDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.PPGetBillAndQueryDto;
import vn.vnpt.digo.adapter.dto.PPGetBillResDto;
import vn.vnpt.digo.adapter.dto.PutBillPaymentDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.nps.NpsAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDetailDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierStatusDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.repository.IntegratedEventRepository;
import vn.vnpt.digo.adapter.repository.IntegratedGetBillEventRepository;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

@Service
public class IntegratedGetBillEventService {
    Logger logger = LoggerFactory.getLogger(IntegratedGetBillEventService.class);

    @Autowired
    private IntegratedEventRepository repository;
    
    @Autowired
    private IntegratedGetBillEventRepository getbillRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private NpsDossierSyncService service;
    
    @Autowired
    private PaymentPlatformService  paymentPlatformservice;
    
    @Autowired
    private Microservice microservice;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private IntegratedConfigurationService configurationService;
    
    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890013");
    
    @Autowired
    private Translator translator;    
    
    @Value(value = "${digo.schedule.dossier-get-bill-event.enable}")
    private Boolean enableScheduleNPSGetBillDossierEvent;   
    
    @Scheduled(fixedDelay = 1000)
    ////@SchedulerLock(name = "npsGetBillDossierEvent", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void npsGetBillDossierEvent(){
        if (enableScheduleNPSGetBillDossierEvent) {
            if(EventProperties.DOSSIER_GET_BILL_EVENT_AVAILABLE){
                logger.info("start nps sync get bill");
                //Query event
                try{
                    IntegratedGetBillEvent event = this.getEvent();
                    if(Objects.nonNull(event)){
                        PPGetBillAndQueryDto getBillDto = new PPGetBillAndQueryDto();
                        getBillDto.setAgencyId(event.getAgencyId());
                        getBillDto.setConfigId(event.getConfigId());
                        getBillDto.setSubsystemId(event.getSubsystemId());
                        getBillDto.setMaThamChieu(event.getMaThamChieu());
                        getBillDto.setThoiGianGD(event.getThoiGianGD());

                        try{
                            Gson gson = new Gson();
                            Object data = paymentPlatformservice.getBill(getBillDto);
                            PPGetBillResDto billRest = GsonUtils.copyObject(data, PPGetBillResDto.class);
                            if(Objects.nonNull(billRest.getIdFile())){
                                IntegratedConfigurationDto config = this.getConfigPayment(getBillDto);
                                RestTemplate rest = this.getOAuth2RestTemplateBill(config);
                                String endpoint = microservice.padmanUri("dossier-payment/" +getBillDto.getMaThamChieu() + "/--update-bill").encode().build().toUriString();
                                PutBillPaymentDto billId = new PutBillPaymentDto();
                                billId.setBillFileId(billRest.getIdFile());
                                AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, billId, AffectedRowsDto.class);
                            }
                            event.setMessage("success");
                        } catch (Exception e){
                            logger.info("event failed ex " + e.getMessage());
                        }
                        event.setStatus(2);

                        this.getbillRepository.save(event);
                    } else {
                        logger.info("can not find event get bill!!!");
                        EventProperties.DOSSIER_GET_BILL_EVENT_AVAILABLE = false;
                    }
                } catch (Exception e){
                    logger.info("event failed ex " + e.getMessage());
                }
            } else {
                // logger.info("no message to sync!!!");
            }
        }
        
    }
    
    
    public AffectedRowsDto saveGetBillEvent(IntegratedGetBillEvent billEvent){       
        getbillRepository.save(billEvent);
        EventProperties.DOSSIER_GET_BILL_EVENT_AVAILABLE = true;
        return new AffectedRowsDto(1);
    }
    
    public AffectedRowsDto callRunEvent(List<IntegratedGetBillEvent> listEvent){
        for(IntegratedGetBillEvent event: listEvent){
            Integer check = this.pushEvent(event);
        }
        return new AffectedRowsDto(listEvent.size());
    }
    
    public AffectedRowsDto callRunEventAll(){
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(0));
//        query.addCriteria(Criteria.where("type").is(1));
        query.with(Sort.by(Sort.Direction.ASC, "_id"));
        List<IntegratedGetBillEvent> listEvent = this.mongoTemplate.find(query, IntegratedGetBillEvent.class);
        for(IntegratedGetBillEvent event: listEvent){
            Integer check = this.pushEvent(event);
        }
        return new AffectedRowsDto(listEvent.size());
    }
    
    private Integer pushEvent(IntegratedGetBillEvent event){
            logger.info("start nps sync");
            //Query event
            try{
                if(Objects.nonNull(event)){
                    PPGetBillAndQueryDto getBillDto = new PPGetBillAndQueryDto();
                    getBillDto.setAgencyId(event.getAgencyId());
                    getBillDto.setConfigId(event.getConfigId());
                    getBillDto.setSubsystemId(event.getSubsystemId());
                    getBillDto.setMaThamChieu(event.getMaThamChieu());
                    getBillDto.setThoiGianGD(event.getThoiGianGD());
                    
                    try{
                        Gson gson = new Gson();
                        Object data = paymentPlatformservice.getBill(getBillDto);
                        PPGetBillResDto billRest = GsonUtils.copyObject(data, PPGetBillResDto.class);
                        if(Objects.nonNull(billRest.getIdFile())){
                            IntegratedConfigurationDto config = this.getConfigPayment(getBillDto);
                            RestTemplate rest = this.getOAuth2RestTemplateBill(config);
                            String endpoint = microservice.padmanUri("dossier-payment/" +getBillDto.getMaThamChieu() + "/--update-bill").encode().build().toUriString();
                            PutBillPaymentDto billId = new PutBillPaymentDto();
                            billId.setBillFileId(billRest.getIdFile());
                            AffectedRowsDto ret = MicroserviceExchange.putJsonNoAuth(rest, endpoint, billId, AffectedRowsDto.class);
                        }
                        event.setMessage("success");
                    } catch (Exception e){
                        logger.info("event failed ex " + e.getMessage());
                    }
                    event.setStatus(2);
                    
                    this.getbillRepository.save(event);
                } else {
                    logger.info("can not find event!!!");
                    EventProperties.DOSSIER_GET_BILL_EVENT_AVAILABLE = false;
                }
            } catch (Exception e){
                logger.info("event failed ex " + e.getMessage());
            }
            return 1;
    }
    
    IntegratedGetBillEvent getEvent(){
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(0));
        query.with(Sort.by(Sort.Direction.ASC, "_id"));
        IntegratedGetBillEvent ret = this.mongoTemplate.findOne(query, IntegratedGetBillEvent.class);
        return ret;
    }
    
    public static RestTemplate getOAuth2RestTemplateBill(IntegratedConfigurationDto config) {
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(config.getParametersValue("pad-sso-url"));
        details.setClientId(config.getParametersValue("pad-client-id"));
        details.setClientSecret(config.getParametersValue("pad-client-secret"));
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }
    
    IntegratedConfigurationDto getConfigPayment(PPGetBillAndQueryDto body){
        IntegratedConfigurationDto config;
        if (Objects.nonNull(body.getConfigId())) {
            config = configurationService.getConfig(body.getConfigId());
        } else {
            config = configurationService.getConfig(body.getAgencyId(), body.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        return config;
    }
    
}
