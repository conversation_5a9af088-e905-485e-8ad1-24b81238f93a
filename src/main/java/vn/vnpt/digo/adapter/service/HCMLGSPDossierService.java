/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.HCMLGSPDossierSyncReqDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMXacNhanGoiTinTienDoXuLyDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSHCMAsyncResponseDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMAsyncReceiveDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.stream.LGSPHCMDossierTestStream;
import vn.vnpt.digo.adapter.stream.payload.NpsAsyncTest;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;
import vn.vnpt.digo.adapter.dto.DossierSyncExtendResponseDto;
import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.document.IntegratedEventCompleted;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.nps.NpsAsyncReceiveDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierStatusDto;
import vn.vnpt.digo.adapter.repository.IntegratedEventRepository;

/**
 *
 * <AUTHOR>
 */
@Service
@RefreshScope
public class HCMLGSPDossierService {
    Logger logger = LoggerFactory.getLogger(HCMLGSPDossierService.class);

    Gson gson = new Gson();

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private UtilService utilService;

    @Autowired
    private HCMLGSPDossierSyncService service;

    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890030");

    @Autowired
    private IntegratedConfigurationService configurationService;
    
    @Value("${lgsp.hcm.token}")
    private String lgspToken;
    
    @Value(value = "${digo.lgsphcm.configid}")
    private String configid;
    
    @Autowired
    private HCMLGSPDossierSyncService serviceLGSP;
    
    @Autowired
    private LGSPHCMDossierTestStream LGSPHCMDossierStream;
    
    @Autowired
    private MongoTemplate mongoTemplate;
    
    @Autowired
    private IntegratedEventRepository eventRepository;
    
    @Value(value = "${digo.thread.lgsphcm.dossier-sync-config-id}")
    private ObjectId lgspHCMDossierSyncConfigId;
    
    public String synchronizeDossier(IntegratedConfigurationDto config, HCMLGSPDossierSyncReqDto body) {
        try {
            
            String service = config.getParametersValue("serviceDongBoHoSoChuyenNganh").toString();
            String uri = config.getParametersValue("adapter").toString();        
            String authorizationCode = lgspToken;
//            String uri = "https://hcmesb-test.tphcm.gov.vn/";
//            String service = "dongbohosochuyennganh";


            String URL = uri + service;

            // set header lgspaccesstoken
            HashMap<String, String> headers = getExtendHeadersSend(authorizationCode);
            
            ObjectMapper mapper = new ObjectMapper();
            String jsonBody = mapper.writeValueAsString(body);
            // synchronize dossier
            String res = MicroserviceExchange.postJson(this.restTemplate, URL, jsonBody, headers, String.class);
            logger.info("HCMLGSP-synchronizeDossier: Response sync " + gson.toJson(res));
            return res;
        } catch (Exception e) {
            logger.info("HCMLGSP-synchronizeDossier: Synchronize dossier fail");
            return e.getMessage();
        }
    }
    
    private HashMap<String, String> getExtendHeadersSend(String token) {
        HashMap<String, String> exHeaders = new HashMap<String, String>();
        exHeaders.put("lgspaccesstoken", token);
        return exHeaders;
    }
    
    public ResponseEntity<Object> sync(HCMLGSPAsyncReceiveDto input){
        LGSHCMAsyncResponseDto ex = new LGSHCMAsyncResponseDto();
        try{
            logger.info("HCMLGSP-Sync: Kafka receiving dossier with" + gson.toJson(input));
            LGSHCMAsyncResponseDto ret = serviceLGSP.syncDossier(input);
            return ResponseEntity.ok(ret);
        } catch (Exception e){
            logger.info("HCMLGSP-Exception: Kafka receiving failed with" + gson.toJson(input));
            ex.setError_code("-1");
            ex.setMessage("Exception" + e.getMessage());
            ex.setStatus("FAIL");
            return ResponseEntity.ok(ex);
        }
    }
    
    public String syncDVCQG(LGSPHCMAsyncReceiveDto input){
        String ret = null;
        try{
            switch(input.getType()) {
                case 1: {
                    logger.info("NPS-Sync: Kafka receiving dossier with" + gson.toJson(input));
                    ret = service.syncDossierDVCQG(input);
                    break;
                }
                case 2: {
                    logger.info("NPS-Sync: Kafka receiving dossier with" + gson.toJson(input));
                    ret = service.syncDossierStatusDVCQG(input);
                    break;
                }
            }
            return ret;
        } catch (Exception e){
            logger.info("NPS-Exception: Kafka receiving failed with" + gson.toJson(input));
            return ret;
        }
    }

    public String kafka(NpsAsyncTest input){
        this.LGSPHCMDossierStream.push(input);
        return "Send";
    }
    
    public String xacNhanGoiTinLGSP(LGSPHCMXacNhanGoiTinTienDoXuLyDto body){
        try {
            IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(configid));
                    
            String uri = config.getParametersValue("adapter").toString();
            String service = config.getParametersValue("urlXacNhanGoiTinLGSP").toString();
            
//            String uri = "https://hcmesb-test.tphcm.gov.vn/";
//            String service = "dvctp/XacNhanGoiTinTienDoXuLyHoSoChuyenNganh";

            String authorizationCode = lgspToken;

            String URL = uri + service;

            // set header lgspaccesstoken
            HashMap<String, String> headers = getExtendHeadersSend(authorizationCode);
            
            ObjectMapper mapper = new ObjectMapper();
            String jsonBody = mapper.writeValueAsString(body);
            // synchronize dossier
                    logger.info("---------------body " + gson.toJson(body));
            String res = MicroserviceExchange.postJson(this.restTemplate, URL, jsonBody, headers, String.class);
            logger.info("XacNhanGoiTinLGSP: Response sync " + gson.toJson(res));

            return res;
        } catch (Exception e) {
            logger.info("XacNhanGoiTinLGSP fail " + e);
            return e.getMessage();
        }
    }
    
    public DossierSyncExtendResponseDto[] syncDossierFail(Boolean isLGSPHCM){
        IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(configid));
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        DossierSyncExtendResponseDto[] dossierList = utilService.getDossierSyncExtend(config, isLGSPHCM);
        for(int i = 0; i < dossierList.length; i++){
            Query query = new Query();
            int countEvent = 0;
            int countEventCompleted = 0;
            if(!isLGSPHCM){
                query.addCriteria(Criteria.where("").andOperator(
                    Criteria.where("data.dossier.code").is(dossierList[i].getCode()),
                    Criteria.where("data.status").exists(true),
                    Criteria.where("isLGSPHCM").is(false),
                    Criteria.where("").orOperator(
                        Criteria.where("data.status.status._id").is(dossierList[i].getDossierTaskStatusId()),
                        Criteria.where("data.status.taskId").is(dossierList[i].getCurrentTaskId())
                    )
                ));
            }else{
                query.addCriteria(Criteria.where("").andOperator(
                    Criteria.where("data.dossier.code").is(dossierList[i].getCode()),
                    Criteria.where("data.status").exists(true),
                    Criteria.where("isLGSPHCM").is(true),
                    Criteria.where("").orOperator(
                        Criteria.where("data.status.status._id").is(dossierList[i].getDossierTaskStatusId()),
                        Criteria.where("data.status.taskId").is(dossierList[i].getCurrentTaskId())
                    )
                ));
            }
            countEvent = (int) mongoTemplate.count(query, IntegratedEvent.class);
            countEventCompleted = (int) mongoTemplate.count(query, IntegratedEventCompleted.class);
            if(countEvent + countEventCompleted == 0){
                //cteate event sync status
                NpsAsyncReceiveDto inputObj = new NpsAsyncReceiveDto();
//                inputObj.setAgencyId(dossierList[i].getAgency().getId());
                if(isLGSPHCM){
                    inputObj.setConfigId(lgspHCMDossierSyncConfigId);
                }else{
                    inputObj.setAgencyId(dossierList[i].getAgency().getId());
                    inputObj.setSubsystemId(dossierList[i].getSubsystemId());
                }
                
                
                NpsDossierDto dossierInput = new NpsDossierDto();
                dossierInput.setId(dossierList[i].getId());
                dossierInput.setCode(dossierList[i].getCode());
                dossierInput.setNationCode(dossierList[i].getNationCode());
                dossierInput.setUpdated(true);
                inputObj.setDossier(dossierInput);
                
                NpsDossierStatusDto status = new NpsDossierStatusDto();
                status.setAgency(dossierList[i].getAgency());
                status.setAssignee("Can Bo C");
                status.setContent(dossierList[i].getDossierMenuTaskRemindName());
                status.setTaskId(dossierList[i].getCurrentTaskId());
                
                status.setStatus(new IdDto(dossierList[i].getDossierTaskStatusId()));
                status.setStartDate(dossierList[i].getStartDate());
                status.setUpdatedDate(dossierList[i].getStartDate());
                status.setEndDate(dossierList[i].getEndDate());
                inputObj.setStatus(status);
                IntegratedEvent event = new IntegratedEvent();
                event.setIsLGSPHCM(isLGSPHCM);
                event.setData(inputObj);
                IntegratedEvent saved = eventRepository.save(event);
            }
        }
        
        
        return dossierList;
    }
}
