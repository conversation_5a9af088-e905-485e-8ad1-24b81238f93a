package vn.vnpt.digo.adapter.service.hbh;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.bson.types.ObjectId;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.KTM_Monre_Integration.Dossier;
import vn.vnpt.digo.adapter.dto.hbh.tnmt.UpdateMonreDossierDTO;
import vn.vnpt.digo.adapter.dto.bdg.SelectFormIO;
import vn.vnpt.digo.adapter.dto.dbn.DBNDossierApplyOnlineDto;
import vn.vnpt.digo.adapter.dto.hbh.log.LogParam;
import vn.vnpt.digo.adapter.dto.minhtue.tntm.ChuSuDung;
import vn.vnpt.digo.adapter.dto.hbh.tnmt.DonDangKy;
import vn.vnpt.digo.adapter.dto.hbh.tnmt.TNMTListDossierDto;
import vn.vnpt.digo.adapter.dto.minhtue.tntm.TaiLieuNop;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.ProcedureForm;
import vn.vnpt.digo.adapter.service.DbnSystemService;
import vn.vnpt.digo.adapter.service.IntegratedConfigurationService;
import vn.vnpt.digo.adapter.service.UtilService;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Oauth2RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import static com.google.common.base.Strings.isNullOrEmpty;

@Service
@Slf4j
@Getter
public class TNMTService extends LgspService{
    private final ObjectId SERVICE_ID = new ObjectId("6531dcf55ea1ea72127263d5");

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
    private final SimpleDateFormat newFormat = new SimpleDateFormat("dd-MM-yyyy");;

    @Value(value = "${lgsp.tnmt.hbh.configid}")
    private String configId;

    @Autowired
    protected IntegratedConfigurationService configurationService;

    @Autowired
    private UtilService utilService;

    @Autowired
    private DbnSystemService dbnSystemService;
    
    @Autowired
    private Microservice microservice;

    @Autowired
    private HbhEventLogService hbhEventLogService;

    @Autowired
    private Oauth2RestTemplate oauth2RestTemplate;

    
    public IntegratedConfigurationDto getInfoConfig(ObjectId configId) {

        IntegratedConfigurationDto config;
        if (Objects.nonNull(configId)) {
            config = configurationService.getConfig(configId);
        } else {
            config = configurationService.searchByServiceId(this.SERVICE_ID);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        return config;
    }

    public List<TNMTListDossierDto> LayHosoTNMT() {
        IntegratedConfigurationDto config = this.getInfoConfig(new ObjectId(this.configId));

        List<TNMTListDossierDto> dossierDtos = new ArrayList<>();
        String strFromDate = config.getParametersValue("ThoiGianBatLayHsDongBo");
        String strToDate = config.getParametersValue("ThoiGianKetThucLayHsDongBo");
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        LocalDateTime now = LocalDateTime.now();

        if (isNullOrEmpty(strFromDate)){
            LocalDateTime timeFrom = now.withHour(0).withMinute(0).withSecond(0);
            strFromDate = dtf.format(timeFrom);
        }
        if (isNullOrEmpty(strToDate)) {
            LocalDateTime timeTo = now.withHour(23).withMinute(59).withSecond(59);
            strToDate = dtf.format(timeTo);
        }

        Map<String, Object> params = new HashMap<>();
        params.put("TuNgay", strFromDate);
        params.put("DenNgay", strToDate);
        params.put("MaTinh", config.getParametersValue("MaTinh"));
        String urlLayDanhSachHoSo = config.getParametersValue("LayDanhSachHoSo");
        LogParam logParam = new LogParam();
        logParam.setServiceName("TNMT");
        logParam.setRequestMethod("POST");
        logParam.setRequestUrl(urlLayDanhSachHoSo);
        try {
            String strResponse = this.postRequest(null, urlLayDanhSachHoSo, params, String.class);
            Gson gson = new Gson();
            dossierDtos = gson.fromJson(strResponse, new TypeToken<ArrayList<TNMTListDossierDto>>(){}.getType()); 
            hbhEventLogService.writeLog(SERVICE_ID, logParam, params, dossierDtos, true, null);
        } catch (Exception e) {
            hbhEventLogService.writeLog(SERVICE_ID, logParam, params, null, false, e.getMessage());
        }
     
        return dossierDtos;
    }
    @Scheduled(cron = "${lgsp.tnmt.hbh.schedule.get-dossier.cron}")
    ////@SchedulerLock(name = "syncDossierHbhTNMT", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void syncDossierHbhTNMT() throws ParseException, JsonProcessingException {
        IntegratedConfigurationDto config = this.getInfoConfig(new ObjectId(this.configId));
        if (config.getParametersValue("enableJob").equals("0")) {
            return;
        }
        List<TNMTListDossierDto> dossierDtos = this.LayHosoTNMT();
        if (dossierDtos.isEmpty()) {
            return;
        }
        for (TNMTListDossierDto dto : dossierDtos) {
            if (!dto.getTrangThaiHoSo().equalsIgnoreCase("1")) {
                continue;
            }
            ObjectId dossierId = insertDossier(dto, config);
            if (Objects.isNull(dossierId)) {
                continue;
            }
        }
    }

    public ObjectId insertDossier(TNMTListDossierDto dossierData, IntegratedConfigurationDto config) throws JsonProcessingException, ParseException {
        
        String applyDossier = microservice.padmanUri("/dossier/--apply-online").toUriString();
        ObjectId dossierId = null;

        LogParam logParam = new LogParam();
        logParam.setKey(dossierData.getMaHoSo());
        logParam.setServiceName("TNMT");
        logParam.setRequestMethod("POST");
        logParam.setRequestUrl(applyDossier);

        boolean isExists = this.findDossierByCode(dossierData.getMaHoSo());
        log.info("Dossier exists: " + isExists);
        if (isExists) {
            return null;
        }
        DBNDossierApplyOnlineDto postDossierOnlineDto = new DBNDossierApplyOnlineDto();

        DBNDossierApplyOnlineDto.AgencyDto agency = utilService.getAgencyNameCodeByAdministrativeUnitCode(dossierData.getMaHuyen());
        if (Objects.isNull(agency)) {
            hbhEventLogService.writeLog(SERVICE_ID, logParam, null, null, false, "Không tìm thấy cơ quan thực hiện với mã mapping " + dossierData.getMaHuyen());
            return null;
        }
        postDossierOnlineDto.setAgency(agency);

        DBNDossierApplyOnlineDto.ProcedureDtoTemp procedureDtoTemp = this.getProcedureByCodeTNMT(dossierData.getMaTTHC(), agency.getId());
        if (Objects.isNull(procedureDtoTemp)) {
            hbhEventLogService.writeLog(SERVICE_ID, logParam, null, null, false,  "Không tìm thấy thủ tục TNMT " + dossierData.getMaTTHC()  + " của cơ quan mapping(" + dossierData.getMaHuyen() + ") tương ứng");
            return null;
        }
        DBNDossierApplyOnlineDto.ProcedureDto procedure = new DBNDossierApplyOnlineDto.ProcedureDto(procedureDtoTemp.getId(), procedureDtoTemp.getCode(), procedureDtoTemp.getTranslate(), procedureDtoTemp.getSector());
        postDossierOnlineDto.setProcedure(procedure);
        postDossierOnlineDto.setProcedureLevel(procedureDtoTemp.getLevel());

        postDossierOnlineDto.setApplyMethod(this.getApplyMethod(dossierData.getKenhThucHien()));
        postDossierOnlineDto.setCode(dossierData.getMaHoSo());
        postDossierOnlineDto.setDossierReceivingKind(this.getDossierReceivingKind(dossierData.getHinhThuc()));
        postDossierOnlineDto.setAppliedDate(dateFormat.parse(dossierData.getNgayNopHoSo()));

        DBNDossierApplyOnlineDto.ProcessDto processDefinition = utilService.getProcessByProcedureIdAndAgency(procedure.getId(), agency.getId());
        if (Objects.isNull(processDefinition)) {
            hbhEventLogService.writeLog(SERVICE_ID, logParam, null, null, false, "Không tìm thấy quy trình của thủ tục " + procedure.getId());
            return null;
        }
        postDossierOnlineDto.setProcedureProcessDefinition(processDefinition);

        String eformApplicantDefault = config.getParametersValue("eFormApplication");

        JSONObject json = new org.json.JSONObject(GsonUtils.getJson(processDefinition.getProcessDefinition()));
        if(json.has("applicantEForm") && json.getJSONObject("applicantEForm") != null && json.getJSONObject("applicantEForm").getString("id") != null) {
            eformApplicantDefault = json.getJSONObject("applicantEForm").getString("id");
        }
        if (isNullOrEmpty(eformApplicantDefault)) {
            hbhEventLogService.writeLog(SERVICE_ID, logParam, postDossierOnlineDto, null, false, "Không tìm thấy eform mặc định");
        }

        String eFormIdDefault = config.getParametersValue("eformID");
        if(json.has("eForm") && json.getJSONObject("eForm") != null && json.getJSONObject("eForm").getString("id") != null) {
            eFormIdDefault = json.getJSONObject("eForm").getString("id");
        }
        if (!eFormIdDefault.isEmpty()) {
            DBNDossierApplyOnlineDto.FormDto eform = new DBNDossierApplyOnlineDto.FormDto();
            eform.setId(eFormIdDefault);
            eform.setData(createDataEformDetail(dossierData));
            postDossierOnlineDto.setEForm(eform);
        }

        if (!dossierData.getNgayTiepNhan().isBlank()) {
            postDossierOnlineDto.setAcceptedDate(dateFormat.parse(dossierData.getNgayTiepNhan()));
        }
        if (!dossierData.getNgayHenTra().isBlank()) {
            postDossierOnlineDto.setAppointmentDate(dateFormat.parse(dossierData.getNgayHenTra()));
        }
        postDossierOnlineDto.setDossierConvert(new DBNDossierApplyOnlineDto.DossierConvert(3, "TNMT"));
        postDossierOnlineDto.setApplicant(this.getApplicant(dossierData, eformApplicantDefault));
        
        ArrayList<PadPApplyDto.AttachmentDto> attachmentDtos = this.saveFileTPHS(dossierData.getTaiLieuNop(), postDossierOnlineDto);
        try {
            IdDto dossier = MicroserviceExchange.postJsonNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), applyDossier, postDossierOnlineDto, IdDto.class);
            dossierId = dossier.getId();
            hbhEventLogService.writeLog(SERVICE_ID, logParam, postDossierOnlineDto, "Lưu hồ sơ thành công " + dossier.getId(), true, null);
        } catch (Exception e) {
            hbhEventLogService.writeLog(SERVICE_ID, logParam, postDossierOnlineDto, null, false, e.getMessage());
            return null;
        }
        ArrayList<DossierFormFileDto> dossierFormFileDtos = new ArrayList<>();
        if (!attachmentDtos.isEmpty()) {
            dossierFormFileDtos = this.getDossierFormFile(attachmentDtos, dossierId.toString());
        }
        if (!dossierData.getDanhSachTepDinhKemKhac().isEmpty()) {
            for (TNMTListDossierDto.DanhSachTepDinhKem item: dossierData.getDanhSachTepDinhKemKhac()) {
                PadPApplyDto.AttachmentDto att = new PadPApplyDto.AttachmentDto();
                try {
                    String postFile = microservice.filemanUri("/file/--by-url-v1?file-url=").toUriString() + URLEncoder.encode(item.getDuongDanTaiTepTin(), StandardCharsets.UTF_8);
                    att = MicroserviceExchange.postNoBody(oauth2RestTemplate.getOAuth2RestTemplate(), postFile, PadPApplyDto.AttachmentDto.class);
                } catch (Exception e) {
                    att.setFilename(item.getTenGiayTo());
                }
                saveTPHS(dossierFormFileDtos, att, dossierId.toString());
            }
        }
        String addDossierFormFile = microservice.padmanUri("/dossier-form-file").toUriString() + "/--by-dossier?dossier-id=" + dossierId.toHexString();
        try {
            MicroserviceExchange.putJsonNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), addDossierFormFile, dossierFormFileDtos, AffectedRowsDto.class);
        } catch (Exception exception) {
            logParam.setRequestUrl(addDossierFormFile);
            hbhEventLogService.writeLog(SERVICE_ID, logParam, addDossierFormFile, null, false, exception.getMessage());
        }
        return dossierId;
    }

    public DBNDossierApplyOnlineDto.ApplyMethod getApplyMethod(Integer kenhThucHien) {
        var applyMethod = new DBNDossierApplyOnlineDto.ApplyMethod();
        applyMethod.setId(0);
        if (kenhThucHien.equals(1)) {
            applyMethod.setId(1);
        } else if (kenhThucHien.equals(2)) {
            applyMethod.setId(0);
        } else if (kenhThucHien.equals(3)) {
            applyMethod.setId(2);
        }
        return applyMethod;
    }

    public DBNDossierApplyOnlineDto.RecvKindDto getDossierReceivingKind(String hinhThuc) {
        IntegratedConfigurationDto config = this.getInfoConfig(new ObjectId(this.configId));
        DBNDossierApplyOnlineDto.RecvKindDto dossierReceivingKind = new DBNDossierApplyOnlineDto.RecvKindDto();
        var transReceivingKind = new DBNDossierApplyOnlineDto.NameDto();
        transReceivingKind.setLanguageId((short) 228);
        if (hinhThuc.equals("0")) {
            transReceivingKind.setName("Nhận Qua dịch vụ BCCI");
            dossierReceivingKind.setId(config.getParametersValue("receiving-vnpost-id"));

        } else {//Trả kết quả tại bộ phận tiếp  nhận  và  trả  kết  quả
            dossierReceivingKind.setId(config.getParametersValue("receiving-directly-id"));
            transReceivingKind.setName("Nhận trực tiếp");
        }
        ArrayList<DBNDossierApplyOnlineDto.NameDto> translateNames = new ArrayList<>();
        translateNames.add(transReceivingKind);
        dossierReceivingKind.setName(translateNames);
        return dossierReceivingKind;
    }

    public DBNDossierApplyOnlineDto.ApplicantDto getApplicant(TNMTListDossierDto data, String eformId) throws JsonProcessingException {
        DBNDossierApplyOnlineDto.ApplicantDto applicant = new DBNDossierApplyOnlineDto.ApplicantDto();
        var applicantData = new HashMap<>();
        applicantData.put("fullname", data.getChuHoSo().getTen());
        applicantData.put("identityNumber", data.getChuHoSo().getSoCMND());
        applicantData.put("phoneNumber", data.getChuHoSo().getSoDienThoai());
        applicantData.put("email", data.getChuHoSo().getEmail());
        applicantData.put("address", data.getChuHoSo().getDiaChi());
        applicantData.put("ownerFullname", data.getChuHoSo().getTen());
        applicantData.put("ownerIdentityNumber", data.getChuHoSo().getSoCMND());
        applicantData.put("ownerPhoneNumber", data.getChuHoSo().getSoDienThoai());
        applicantData.put("ownerAddress", data.getChuHoSo().getDiaChi());

        applicant.setEformId(eformId);
        applicant.setData(applicantData);
        return applicant;
    }
    public ArrayList<PadPApplyDto.AttachmentDto> saveFileTPHS(List<TaiLieuNop> taiLieuNops, DBNDossierApplyOnlineDto dossierApplyOnlineDto) {
        ArrayList<PadPApplyDto.AttachmentDto> attachmentDtos = new ArrayList<>();
        boolean checkHasFileTPHS = false;
        
        for (TaiLieuNop file : taiLieuNops) {
            String encodedUri = URLEncoder.encode(file.getDuongDanTaiTepTin(), StandardCharsets.UTF_8);
            String postFile = microservice.filemanUri("/file/--by-url-v1?file-url=").toUriString() + encodedUri;
            PadPApplyDto.AttachmentDto att = new PadPApplyDto.AttachmentDto();
            try {
                att = MicroserviceExchange.postNoBody(oauth2RestTemplate.getOAuth2RestTemplate(), postFile, PadPApplyDto.AttachmentDto.class);
                checkHasFileTPHS = true;
            } catch (Exception e) {
               log.info("Lỗi upload file TPHS " + e.getMessage());
            }
            att.setFilename(file.getTenTepDinhKem());
            att.setGroup(file.getTenThanhPhanHoSo());
            attachmentDtos.add(att);
        }
        if (dossierApplyOnlineDto != null) {
            dossierApplyOnlineDto.setCheckHasFileTPHS(checkHasFileTPHS);
        }
        return attachmentDtos;
    }
    public ArrayList<DossierFormFileDto> getDossierFormFile(ArrayList<PadPApplyDto.AttachmentDto> taiLieuNops, String dossierId) {
        ArrayList<DossierFormFileDto> dossierFormFiles = new ArrayList<>();
        for (PadPApplyDto.AttachmentDto file : taiLieuNops) {
            saveTPHS(dossierFormFiles, file, dossierId);
        }
        return dossierFormFiles;
    }

    public void saveTPHS(ArrayList<DossierFormFileDto> dossierFormFiles, PadPApplyDto.AttachmentDto file, String dossierId) {
        ArrayList<PadPApplyDto.AttachmentDto> fileDossier = new ArrayList<>();

        DossierFormFileDto dossierFormFile = new DossierFormFileDto();
        DossierFormFileDto.Id dossier = new DossierFormFileDto.Id();
        dossier.setId(dossierId);
        dossierFormFile.setDossier(dossier);
        // set procedureForm
        ProcedureForm procedureForm = new ProcedureForm();
        ObjectId newOB = new ObjectId();
        procedureForm.setId(newOB.toHexString());
        procedureForm.setName(file.getGroup()); // đang để Tên TPHS vào group sau khi lấy ra -> update group về null
        dossierFormFile.setProcedureForm(procedureForm);
        // set case
        DossierFormFileDto.Id caze = new DossierFormFileDto.Id();
        caze.setId("5f3f3c044e1bd312a6f3addf");
        dossierFormFile.setCaze(caze);

        dossierFormFile.setOrder(1);
        dossierFormFile.setQuantity(1);

        // set detail
        String textDetail = new String("B\u1ea3n sao".getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
        DossierFormFileDto.ProcedureFormDetail detail = new DossierFormFileDto.ProcedureFormDetail(
                new DossierFormFileDto.ProcedureFormDetailType("623462c0f2e2ad4ed5787167", textDetail), 1
        );
        file.setGroup(null);
        if (Objects.nonNull(file.getId())) {
            fileDossier.add(file);
        }
        dossierFormFile.setDetail(detail);
        dossierFormFile.setFile(fileDossier);
        dossierFormFiles.add(dossierFormFile);
    }
    
    public Object capNhatThongTinHoSo(IntegrationParamsDto integrationParamsDto, Map<String, Object> body)
    {
        IntegratedConfigurationDto config = this.getInfoConfig(integrationParamsDto.getConfigId());
        Map<String, Object> params = new HashMap<>();
        params.put("MaTinh", config.getParametersValue("MaTinh"));
        
        UpdateMonreDossierDTO dossier = this.getDataDossier(body.get("dossierId").toString(), config);
        if (dossier == null) {
           return null;
        }
        ArrayList<UpdateMonreDossierDTO> data = new ArrayList<>();
        data.add(dossier);
        params.put("Data", data);
        
        String urlCapNhatHoSo = config.getParametersValue("CapNhatThongTinHoSo");
        LogParam logParam = new LogParam();
        logParam.setServiceName("TNMT");
        logParam.setKey(dossier.getMaHoSo());
        logParam.setRequestUrl(urlCapNhatHoSo);
        try {
            Object res = this.postRequest(null, urlCapNhatHoSo, params, Object.class);
            hbhEventLogService.writeLog(SERVICE_ID, logParam, params, res, true, null);
            return res;
        } catch (Exception e) {
            hbhEventLogService.writeLog(SERVICE_ID, logParam, params, null, false, e.getMessage());
            return null;
        }
    }
    

    public UpdateMonreDossierDTO getDataDossier(String id, IntegratedConfigurationDto config) {
        GetDossierOnlineDto dossier = utilService.getDossierById(id);
        String urlCapNhatHoSo = config.getParametersValue("CapNhatThongTinHoSo");
        if (Objects.isNull(dossier) || Objects.isNull(dossier.getConvert()) || dossier.getConvert().getType() != 3) {
            return null;
        }
        UpdateMonreDossierDTO dossierTNMT = new UpdateMonreDossierDTO();
        dossierTNMT.setMaHoSo(dossier.getCode());
        dossierTNMT.setNgayTiepNhan(this.convertDate(dossier.getAcceptedDate()));
        dossierTNMT.setNgayHenTra(this.convertDate(dossier.getAppointmentDate()));
        dossierTNMT.setNgayTra(this.convertDate(dossier.getReturnedDate()));
        dossierTNMT.setNgayKetThucXuLy(this.convertDate(dossier.getCompletedDate()));
        dossierTNMT.setThongTinTra("");
        dossierTNMT.setGhiChu("");
        TagDto dossierTaskStatus = getDossierTaskStatus(dossier.getDossierTaskStatus().getId().toHexString());
        if (Objects.isNull(dossierTaskStatus) || isNullOrEmpty(dossierTaskStatus.getIntegratedCode())) {
            LogParam logParam = createLogParams("CapNhatHoSo", dossier.getCode(), urlCapNhatHoSo, "GET");
            hbhEventLogService.writeLog(SERVICE_ID, logParam, null, null, false, "Không tìm thấy thông tin mapping của trạng thái " + dossier.getDossierTaskStatus().getId().toHexString());
        }
        String statusID = dossierTaskStatus.getIntegratedCode();
        dossierTNMT.setTrangThaiHoSo(statusID);

        ArrayList<UpdateMonreDossierDTO.DanhSachGiayToKetQua> dsFileKQ = new ArrayList<>();
        ArrayList<UpdateMonreDossierDTO.DanhSachHoSoBoSung> danhSachHoSoBoSungs = new ArrayList<>();
        if(Objects.nonNull(dossier.getAttachment())){
            List<Dossier.AttachmentDto> attachments = dossier.getAttachment();
            if(!attachments.isEmpty()){
                for (Dossier.AttachmentDto attachment: attachments) {
                    if(attachment.getGroup().equals("5f9bd9692994dc687e68b5a6")){
                        UpdateMonreDossierDTO.DanhSachGiayToKetQua fileKQ = new UpdateMonreDossierDTO.DanhSachGiayToKetQua();
                        fileKQ.setTenGiayTo(attachment.getFilename());
                        fileKQ.setDuongDanTepTinKetQua(microservice.filemanUri("/wopi/files/" + attachment.getId() + "/contents").encode().build().toUriString());
                        dsFileKQ.add(fileKQ);
                    }
                }
                
            }
        }
        dossierTNMT.setDanhSachGiayToKetQua(dsFileKQ);
        if (dossier.getDossierReceivingKind().getId().equalsIgnoreCase("5f8968888fffa53e4c073ded")) {
            dossierTNMT.setHinhThuc("0");
        } else {
            dossierTNMT.setHinhThuc("1");
        }
        dossierTNMT.setDonViXuLy(dossier.getAgency().getName());
        dossierTNMT.setDanhSachHoSoBoSung(danhSachHoSoBoSungs);
        dossierTNMT.setQuaTrinhXuLy(this.getQTXL(dossier.getTask(), dossier.getCode()));

        return dossierTNMT;
    }

    public ArrayList<UpdateMonreDossierDTO.QuaTrinhXuLy> getQTXL(ArrayList<GetDossierOnlineDto.DossierTask> dossierTasks, String dossierCode) {
        ArrayList<UpdateMonreDossierDTO.QuaTrinhXuLy> quaTrinhXuLy = new ArrayList<>();
        if (Objects.isNull(dossierTasks) || dossierTasks.isEmpty()) {
            return quaTrinhXuLy;
        }

        for (GetDossierOnlineDto.DossierTask task: dossierTasks){
            if (task == null) {
                continue;
            }
            UpdateMonreDossierDTO.QuaTrinhXuLy xuLy = new UpdateMonreDossierDTO.QuaTrinhXuLy();
            xuLy.setMaHoSo(dossierCode);
            String nguoiXuLy = !Strings.isNullOrEmpty(task.getAssignee().getFullname()) ? task.getAssignee().getFullname() : "";
            xuLy.setNguoiXuLy(nguoiXuLy);
            String chucDanh = "";
            if (task.getCandidatePosition() != null && task.getCandidatePosition().getName() != null) {
                for (int m = 0; m < task.getCandidatePosition().getName().size(); m++) {
                    if (task.getCandidatePosition().getName().get(m) != null && task.getCandidatePosition().getName().get(m).getLanguageId() == 228) {
                        chucDanh = !Strings.isNullOrEmpty(task.getCandidatePosition().getName().get(m).getName()) ? task.getCandidatePosition().getName().get(m).getName() : "";
                        break;
                    }
                }
            }
            xuLy.setChucDanh(chucDanh);
            xuLy.setThoiDiemXuLy(this.convertDate(task.getCompletedDate()));

            String phongBanXuly = "";
            if (task.getCandidateGroup() != null && !task.getCandidateGroup().isEmpty()) {
                for (int m = 0; m < task.getCandidateGroup().get(0).getName().size(); m++) {
                    if (task.getCandidateGroup().get(0).getName().get(m) != null && task.getCandidateGroup().get(0).getName().get(m).getLanguageId() == 228) {
                        phongBanXuly = !Strings.isNullOrEmpty(task.getCandidateGroup().get(0).getName().get(m).getName()) ?
                                task.getCandidateGroup().get(0).getName().get(m).getName() : "";
                    }
                }
            }
            xuLy.setPhongBanXuLy(phongBanXuly);
            xuLy.setNoiDungXuLy(task.getBpmProcessDefinitionTask().getName().getName());
            if (task.getCompletedDate() != null) {
                xuLy.setTrangThai("1");
            } else {
                xuLy.setTrangThai("0");
            }
            xuLy.setNgayBatDau(this.convertDate(task.getCreatedDate()));
            xuLy.setNgayKetThucTheoQuyDinh(this.convertDate(task.getDueDate()));
            quaTrinhXuLy.add(xuLy);
        }
        return quaTrinhXuLy;
    }

    public ObjectId getServiceId() {
        return SERVICE_ID;
    }
    
    public boolean findDossierByCode(String code) {
        String findDossierUrl = microservice.padmanUri("/dossier/search?code=" + code).toUriString();
        boolean isExists = false;
        try {
            String dossierResponse = MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), findDossierUrl, String.class);
            JSONObject dossier = new JSONObject(dossierResponse);
            if (Integer.parseInt(dossier.get("numberOfElements").toString()) > 0) {
                isExists = true;
            }
        } catch (Exception ignored) {}
        return isExists;
    }

    public String convertDate(Date source) {
        if (source == null) {
            return "";
        }
        return dateFormat.format(source);
    }
    public String convertStringToDate(String dateString) {
        if (isNullOrEmpty(dateString)) {
            return "";
        }
        try {
            Date date = dateFormat.parse(dateString);
            return newFormat.format(date);
        } catch (ParseException e) {
            return "";
        }
    }

    public TagDto getDossierTaskStatus(String id){
        try {
            String dossierTaskStatusUrl = microservice.basecatUri("/tag/" + id).toUriString();
            return MicroserviceExchange.get(oauth2RestTemplate.getOAuth2RestTemplate(), dossierTaskStatusUrl, TagDto.class);
        } catch (Exception e) {
            return null;
        }
    }
    
    public DonDangKy createDataEformDetail(TNMTListDossierDto dossierData) {
        vn.vnpt.digo.adapter.dto.minhtue.tntm.DonDangKy donDangKy = dossierData.getDonDangKy();
        DonDangKy donDangKyHBH = new DonDangKy();
        ArrayList<DonDangKy.ChuSuDung> chuSuDungs = new ArrayList<>();
        if (donDangKy.getChuSuDung() != null && !donDangKy.getChuSuDung().isEmpty()) {
            for (ChuSuDung item: donDangKy.getChuSuDung()) {
                DonDangKy.ChuSuDung chuSuDung = new DonDangKy.ChuSuDung();
                chuSuDung.setNgaySinh(convertStringToDate(item.getNgaySinh()));
                chuSuDung.setHoVaTen(item.getHoVaTen());
                chuSuDung.setSoCMND(item.getSoCMND());
                chuSuDung.setDiaChi(item.getDiaChi());
                chuSuDungs.add(chuSuDung);
            }
        }
        donDangKyHBH.setChuSuDung(chuSuDungs);
        donDangKyHBH.setSoPhatHanh(donDangKy.getSoPhatHanh());
        donDangKyHBH.setSoVaoSo(donDangKy.getSoVaoSo());
        donDangKyHBH.setNgayCapGCN(convertStringToDate(donDangKy.getNgayCapGCN()));
        DonDangKy.DiaChiThuaDat diaChiThuaDat = new DonDangKy.DiaChiThuaDat();
        if (Objects.nonNull(donDangKy.getDiaChiThuaDat()) && !isNullOrEmpty(donDangKy.getDiaChiThuaDat().getMaTinh())) {
            SelectFormIO tinhThanh = new SelectFormIO(donDangKy.getDiaChiThuaDat().getMaTinh(), donDangKy.getDiaChiThuaDat().getTenTinh());
            diaChiThuaDat.setMaTinh(tinhThanh);
        }
        
        if (Objects.nonNull(donDangKy.getDiaChiThuaDat()) && !isNullOrEmpty(donDangKy.getDiaChiThuaDat().getMaHuyen())) {
            SelectFormIO quanHuyen = new SelectFormIO(donDangKy.getDiaChiThuaDat().getMaHuyen(), donDangKy.getDiaChiThuaDat().getTenHuyen());
            diaChiThuaDat.setMaHuyen(quanHuyen);
        }
        if (Objects.nonNull(donDangKy.getDiaChiThuaDat()) && !isNullOrEmpty(donDangKy.getDiaChiThuaDat().getMaXa())) {
            SelectFormIO phuongXa = new SelectFormIO(donDangKy.getDiaChiThuaDat().getMaXa(), donDangKy.getDiaChiThuaDat().getTenXa());
            diaChiThuaDat.setMaXa(phuongXa);
        }
        if (Objects.nonNull(donDangKy.getDiaChiThuaDat()) && Objects.nonNull(donDangKy.getDiaChiThuaDat().getDiaChiChiTiet())) {
            diaChiThuaDat.setDiaChiChiTiet(donDangKy.getDiaChiThuaDat().getDiaChiChiTiet());
        }
        donDangKyHBH.setNoiDungBienDong(donDangKy.getNoiDungBienDong());
        donDangKyHBH.setLyDoBienDong(donDangKy.getLyDoBienDong());
        donDangKyHBH.setNhuCauCapMoi(donDangKy.getNhuCauCapMoi());
        donDangKyHBH.setDiaChiThuaDat(diaChiThuaDat);
        return donDangKyHBH;
    }

    public DBNDossierApplyOnlineDto.ProcedureDtoTemp getProcedureByCodeTNMT(String codeBoTNMT, String agencyId) {
        String procedureUrl = microservice.basepadUri("/procedure/by-code-tnmt?codeBoTNMT=" + codeBoTNMT + "&agency-id=" + agencyId).toUriString();
        try {
            return MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), procedureUrl, DBNDossierApplyOnlineDto.ProcedureDtoTemp.class);
        } catch (Exception e) {
            return null;
        }
    }
    
    public LogParam createLogParams(String serviceName, String key, String url, String method) {
        LogParam logParam = new LogParam();
        logParam.setServiceName(serviceName);
        logParam.setKey(key);
        logParam.setRequestUrl(url);
        logParam.setRequestMethod(Objects.isNull(method) ? "POST" : "GET");
        return logParam;
    }
}
