package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.health.HealthComponent;
import org.springframework.boot.actuate.health.HealthEndpoint;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.pojo.Permission;
import vn.vnpt.digo.adapter.util.Context;

import java.net.URI;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Objects;

@Service

public class HealthService {

    Logger logger = LoggerFactory.getLogger(HealthService.class);

    @Autowired
    private HealthEndpoint healthEndpoint;

    @Value("${telegram.bot.token}")
    private String botToken;

    @Value("${telegram.chat.id}")
    private String defaultChatId;

    @Value("${telegram.chat.topic}")
    private String defaultTopic;

    @Value("${vnpt.permission.check-user}")
    private String scope;

    public void sendNotification(String message, String chatId, String topic) {
        logger.info("=== sendNotification ===");
        if (Objects.isNull(chatId) || chatId.isBlank()) chatId = defaultChatId;
        if (Objects.isNull(topic) || topic.isBlank()) topic = defaultTopic;
        String text = String.format("``` \n --- svc-adapter --- \n %s \n %s \n  %s ```",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss dd/MM/yyyy")),
                "-".repeat(19),
                message);
        URI uri = UriComponentsBuilder.fromHttpUrl("https://api.telegram.org/bot" + botToken + "/sendMessage")
                .queryParam("message_thread_id",  topic)
                .queryParam("chat_id", chatId + "_" + topic)
                .queryParam("parse_mode", "markdown")
                .queryParam("text", text)
                .build()
                .toUri();

        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> entity = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.exchange(uri, HttpMethod.POST, entity, String.class);
    }

    public Object getAndLogHealth(String chatId, String topic) {
        logger.info("=== getAndLogHealth ===");
        HealthComponent health = healthEndpoint.health();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        try {
            String healthJson = objectMapper.writeValueAsString(health);
            try {
                sendNotification(healthJson, chatId, topic);
                logger.info("--- Send telegram notify: successfully ---");
            } catch (Exception e) {
                logger.info("--- Send telegram notify: failed ---");
            }
            if (scope != null && !scope.isEmpty()) {
                Permission permission = Context.getPermission(scope);
                if (permission == null) {
                    HashMap<String, Object> er = new HashMap<>();
                    er.put("status",  health.getStatus().getCode());
                    return er;
                }
            }
            return health;
        } catch (Exception e) {
            HashMap<String, Object> er = new HashMap<>();
            er.put("status", "UP");
            logger.info("--- Can not check detail health ---");
            return er;
        }
    }
}
