package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.gson.Gson;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.asxh.AsxhAddRequestDto;
import vn.vnpt.digo.adapter.dto.asxh.AsxhAddResponseDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.StringHelper;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Service
public class AsxhService {

    Logger logger = LoggerFactory.getLogger(PaymentPlatformService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private RestTemplate restTemplate;

    private Gson gson = new Gson();

    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890040");

    @Autowired
    private Microservice microservice;

    @Autowired
    private MongoTemplate mongoTemplate;
    

    private String getToken(String tokenUrl,String type, String username, String password, String usernameAuth, String passwordAuth) {
        String tokenStr = "";
        try {
            HttpHeaders headers = new HttpHeaders();
            String base64Token = StringHelper.toBase64Encode(usernameAuth + ":" + passwordAuth);
            headers.set("Authorization", "Basic " + base64Token);
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("grant_type", type);
            map.add("username", username);
            map.add("password", password);
    
            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<MultiValueMap<String, String>>(map,
                    headers);
    
            JSONObject response = new JSONObject(
                    restTemplate.exchange(tokenUrl, HttpMethod.POST, entity, String.class).getBody());
            logger.info("Response from " + tokenUrl + ": " + response);
            tokenStr = response.getString("access_token");
            logger.info("DIGO-Info: getToken value = " + tokenStr);
        } catch (Exception e) {
            logger.info("DIGO-Info: getToken Exception " + e.getMessage());
        }

        return tokenStr;
    }

    public AsxhAddResponseDto addDossier(AsxhAddRequestDto params) {
        logger.info("DIGO-Info: begin asxh addDossier with MaHoSoMotCua = " + params.getMaHoSo());

        //Get config
        IntegratedConfigurationDto config;
        var result = new AsxhAddResponseDto();
        result.setStatusCode(0);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("them-moi-ho-so-url").toString();
            String type = config.getParametersValue("grant_type").toString();
            String username = config.getParametersValue("username");
            String password = config.getParametersValue("password");
            String usernameAuth = config.getParametersValue("username-auth");
            String passwordAuth = config.getParametersValue("password-auth");

            String token = getToken(tokenUrl, type, username, password, usernameAuth, passwordAuth);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);
            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), AsxhAddResponseDto.class);
                if (response == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    result.setMessage(jsonModelInput);
                } else {
                    result = response;
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                result.setMessage(jsonModelInput);
            }
        } catch (Exception ex) {
            result.setMessage(ex.getMessage());
        }

        return result;
    }

    

}
