package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.Gson;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.VNPostEvent;
import vn.vnpt.digo.adapter.document.VNPostStatus;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.LgspVNPostPostageVasDto;
import vn.vnpt.digo.adapter.dto.LgspVNPostContactDto;
import vn.vnpt.digo.adapter.dto.VNPostResultDto;
import vn.vnpt.digo.adapter.dto.LgspVNPostMStatusDto;
import vn.vnpt.digo.adapter.dto.LgspVNPostPriceResponseDto;
import vn.vnpt.digo.adapter.dto.VNPostStatusDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.ApiResultDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMLogInputDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.ListDossierUpdateVnpostStatusDto;
import vn.vnpt.digo.adapter.pojo.UpdateVNPostStatus;
import vn.vnpt.digo.adapter.pojo.UpdateVNPostStatus.DataPost;
import vn.vnpt.digo.adapter.repository.VNPostEventRepository;
import vn.vnpt.digo.adapter.stream.NpsDossierProducerStream;
import vn.vnpt.digo.adapter.stream.VNPostProducerStream;
import vn.vnpt.digo.adapter.util.*;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.industry_trade_bd.DossierExtendsDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDetailDto;

import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@RefreshScope
@Service
public class LgspHcmVnPostService {

    Logger logger = LoggerFactory.getLogger(LgspHcmVnPostService.class);
    Gson gson = new Gson();

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private RestTemplate restTemplate;
    
     @Autowired
    private LGSPHCMLogService LGSPHCMLogService;

    @Autowired
    NpsDossierProducerStream npsDossierProducerStream;
    
    @Autowired
    private VNPostStatusService VNPostStatusService;
    
    @Autowired
    private Microservice microservice;

    @Autowired
    private Translator translator;

    @Value(value = "${digo.oidc.client-id}")
    private String clientId;

    @Value(value = "${digo.oidc.client-secret}")
    private String clientSecret;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;

    @Value(value = "${digo.lgsphcm.configid}")
    private String configid;
    
    @Value("${lgsp.hcm.token}")
    private String lgspToken;
    
    @Value(value = "${digo.lgsphcm.configid}")
    private String vnPostOverLgspconfigid;

    @Value(value = "${digo.hcm.transmitDossierCodeViaVNPost}")
    private int transmitDossierCodeViaVNPost;
    
    @Autowired
    private IntegratedLogsService IntegratedLogsService;    
    
    private IntegratedConfigurationDto config;
    @Autowired
    RestTemplate rest;
    
    @Autowired
    private UtilService utilService;
    
    @Value(value = "${digo.schedule.lgsp-hcm-tracking-vnpost.enable}")
    private Boolean enableScheduleTrackingVNPost;

    @Autowired
    private VNPostEventRepository vnPostEventRepository;

    @Autowired
    private VNPostProducerStream vnPostProducerStream;

    private static final String SUCCESS_STATUS = "100";
    private static final String ERROR_STATUS = "101";
    private static final String NULL_RESPONSE_STATUS = "102";
    
    private HttpHeaders getExtendHeadersToken(String lgspToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("lgspaccesstoken", lgspToken);
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        return headers;
    }

    private HttpHeaders getExtendHeadersSend(String token, String lgspaccesstoken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("lgspaccesstoken", lgspaccesstoken);
//        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);
        return headers;
    }
    
     private HashMap<String, String> getExtendHeaders(String token) {
        HashMap<String, String> exHeaders = new HashMap<String, String>();
        exHeaders.put("Authorization", "Bearer " + token);
        return exHeaders;
    }

    public String getTokenLGSP() {
        IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(configid));
        String uri = config.getParameterValue("adapter");
        String accessKey = config.getParametersValue("accessKey");
        String secretKey = config.getParametersValue("secretKey");
        String appName = config.getParametersValue("appName");
        String partnerCode = config.getParametersValue("partnerCode");
        String partnerCodeCus = config.getParametersValue("partnerCodeCus");

       String jsonString = new JSONObject()
                .put("AccessKey", accessKey)
                .put("SecretKey", secretKey)
                .put("AppName", appName)
                .put("PartnerCode", partnerCode)
                .put("PartnerCodeCus", partnerCodeCus)
                .toString().replaceAll(",", ",\n").replaceAll("\\{", "{\n").replaceAll("}", "\n}");

        logger.info("LgspHcmVnpostService: getTokenLGSP: " + Base64.getEncoder().encodeToString(jsonString.getBytes()));
         
        return Base64.getEncoder().encodeToString(jsonString.getBytes());
    }
    
    public String getTokenServices() {
        IntegratedConfigurationDto config;
        config = configurationService.getConfig(new ObjectId(configid));
        String token = "";
        try {
            String lgspToken = getTokenLGSP();

            String uri = config.getParameterValue("adapter");
            String service = config.getParameterValue("urlGetTokenVNPOST");
            String url = uri + service;
            
            HttpHeaders headers = getExtendHeadersToken(lgspToken);
            
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("grant_type", "client_credentials");
            
            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<MultiValueMap<String, String>>(map, headers);

            JSONObject response = new JSONObject(restTemplate.exchange(url, HttpMethod.POST, entity, String.class).getBody());
            logger.info("Response from " + url + ": " + response);
            token = response.getString("access_token");
        } catch (Exception ex) {
            logger.info("getTokenServices: " + ex);
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
        return token;
    }
    
    /**
     * Name: getPrice
     * Author: phucnh.it2
     * Date: 20220712
     */
     public ApiResultDto getPriceHCC(){
        ApiResultDto returnDto = new ApiResultDto();
        String retData = "";
         try {
            
            IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(vnPostOverLgspconfigid));
                    
            String uri = config.getParametersValue("adapter").toString(); 
            String service = config.getParametersValue("urlGetPriceHCC").toString();
            
            String URL = uri + service;             
//            String URL = "https://hcmesb-test.tphcm.gov.vn/apiVNPostNGSP/st/p1.0/info/PriceHCC";

            // set header lgspaccesstoken            
            String lgspToken = getTokenLGSP();
            String vnpostToken = getTokenServices();
            
            HttpHeaders headers = getExtendHeadersSend(vnpostToken, lgspToken);   
            HttpEntity<?> request = new HttpEntity<>( headers);
            ResponseEntity<String> res = restTemplate.exchange(URL, HttpMethod.GET, request, String.class);                       

            Gson g = new Gson();
            retData = g.fromJson(res.getBody(), String.class);
            
            logger.info("getPriceHCC: Response get " + gson.toJson(res));            
            
            returnDto.setMessage("Success");
            returnDto.setError_code("1");
            returnDto.setData(res);
            returnDto.setStatus("OK");
            
            //Thuc hien luu log call API
            logger.info("DIGO-Info 1.2.1: saveApiLog: getPriceHCC: Start...");  
            LGSPHCMLogInputDto bodyLog = new LGSPHCMLogInputDto();
            bodyLog.setRequestID("");
            bodyLog.setAPIFunction(URL);                        
            bodyLog.setDossierCode("");  
            
            // khong co thong tin ma don vi
            bodyLog.setAgencyCode("");
            
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            String toDay = df.format(new Date());
            bodyLog.setCallTime(toDay);
            
            bodyLog.setResult(Boolean.TRUE);            
            bodyLog.setResponseBody(retData);
            bodyLog.setRequestBody("");   
                //SaveLog : ApiLogTypes
                //LLTP = "100"; //ly lich tu phap
                //HHTP = "200"; // ho tich tu phap 
                //VNPOST = "300"; // vnpost
                //CMNS = "400"; //cap ma ngan sach
                //DMDC = "500"; //Danh muc dung chung
                //BHXH = "600"; //Tra cuu thong tin bhxh
                //CSDLDKDN = "700"; //CSDL dang ky doanh nghiep
                //VBQPPL = "800";   //van ban quy pham phap luat
            bodyLog.setApiType(ApiLogTypes.VNPOST);

            IdDto retLog = LGSPHCMLogService.addLog(bodyLog);
            logger.info("DIGO-Info 1.2.2: retLog: " + retLog);

        } catch (Exception e) {
            logger.info("getPriceHCC fail " + e);            
            returnDto.setMessage(e.getMessage());
            returnDto.setError_code("10004");
            returnDto.setData(null);
            returnDto.setStatus("Fail");    
        }
        
         return returnDto;
    }
    
    /**
     * Name: getVnPostInfomation
     * Author: phucnh.it2
     * Date: 20220701
     */
     public ApiResultDto getVnPostInfomation(Integer provinceCode, Integer districtCode, String communeCode){
        
        ApiResultDto returnDto = new ApiResultDto();
        LgspVNPostPostageVasDto ret = null;
         try {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            String toDay = df.format(new Date());
            
            IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(vnPostOverLgspconfigid));
            String uri = config.getParametersValue("adapter").toString(); 
            String service = config.getParametersValue("urlGetInfomationPost").toString();            
                        
            String param = "?ProvinceCode=" + provinceCode + "&DistrictCode=" + districtCode + "&CommuneCode=" + communeCode;
//            String service = "apiVNPostNGSP/p1.0/info/GetInfomationPost";
            String URL = uri + service + param;
            
            String lgspToken = getTokenLGSP();
            String vnpostToken = getTokenServices();            
            HttpHeaders headers = getExtendHeadersSend(vnpostToken, lgspToken);           
           
            HttpEntity<?> request = new HttpEntity<>( headers);
            ResponseEntity<String> result = restTemplate.exchange(URL, HttpMethod.GET, request, String.class);

            Gson g = new Gson();
            ret = g.fromJson(result.getBody(), LgspVNPostPostageVasDto.class);
            
            logger.info("getVnPostInfomation: Response get " + gson.toJson(ret));            
                        
            returnDto.setMessage("Success");
            returnDto.setError_code("1");
            returnDto.setData(ret);
            returnDto.setStatus("OK");                    
                    
            //Thuc hien luu log call API
            logger.info("DIGO-Info 1.2.1: saveApiLog: getVnPostInfomation: Start...");  
            LGSPHCMLogInputDto bodyLog = new LGSPHCMLogInputDto();
            bodyLog.setRequestID("");
            bodyLog.setAPIFunction(URL);                        
            bodyLog.setDossierCode("");  
            
            // khong co thong tin ma don vi
            bodyLog.setAgencyCode("");            
            
            bodyLog.setCallTime(toDay);            
            bodyLog.setResult(Boolean.TRUE);            
            bodyLog.setResponseBody(result.getBody());
            bodyLog.setRequestBody("");   
            bodyLog.setApiType(ApiLogTypes.VNPOST);
                //SaveLog : ApiLogTypes
                //LLTP = "100"; //ly lich tu phap
                //HHTP = "200"; // ho tich tu phap 
                //VNPOST = "300"; // vnpost
                //CMNS = "400"; //cap ma ngan sach
                //DMDC = "500"; //Danh muc dung chung
                //BHXH = "600"; //Tra cuu thong tin bhxh
                //CSDLDKDN = "700"; //CSDL dang ky doanh nghiep
                //VBQPPL = "800";   //van ban quy pham phap luat

            IdDto retLog = LGSPHCMLogService.addLog(bodyLog);
            logger.info("DIGO-Info 1.2.2: retLog: " + retLog);

        } catch (Exception e) {
            logger.info("getVnPostInfomation fail " + e);
            returnDto.setMessage(e.getMessage());
            returnDto.setError_code("10004");
            returnDto.setData(null);
            returnDto.setStatus("Fail");  
        }
         return returnDto;
    }
     
     /**
     * Name: getInfoCollectionFee: Lay thong tin duoc thu ho le phi HCC
     * Author: phucnh.it2
     * Date: 20220712
     */
     
     public ApiResultDto getInfoCollectionFee(Integer profileFee){
        
         ApiResultDto returnDto = new ApiResultDto();
         try {
            
            LgspVNPostContactDto ret = null;            
            IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(vnPostOverLgspconfigid));                    
            String uri = config.getParametersValue("adapter").toString();
            String service = config.getParametersValue("urlGetPostageVas").toString();
            String param = "?ProfileFee=" + profileFee;                      
//            String URL = "https://api.ngsp.gov.vn/apiVNPostNGSP/p1.0/info/GetPostageVas" + param;
            String URL = uri + service + param;

            String lgspToken = getTokenLGSP();
            String vnpostToken = getTokenServices();            
            HttpHeaders headers = getExtendHeadersSend(vnpostToken, lgspToken);     
           
            HttpEntity<?> request = new HttpEntity<>( headers);
            ResponseEntity<String> result = restTemplate.exchange(URL, HttpMethod.GET, request, String.class);

            Gson g = new Gson();
            ret = g.fromJson(result.getBody(), LgspVNPostContactDto.class);
            logger.info("getInfoCollectionFee: Response get " + gson.toJson(ret));
                        
            returnDto.setMessage("Success");
            returnDto.setError_code("1");
            returnDto.setData(ret);
            returnDto.setStatus("OK");      
            
            //Thuc hien luu log call API
            logger.info("DIGO-Info 1.2.1: saveApiLog: getInfoCollectionFee: Start...");  
            LGSPHCMLogInputDto bodyLog = new LGSPHCMLogInputDto();
            bodyLog.setRequestID("");
            bodyLog.setAPIFunction(URL);                        
            bodyLog.setDossierCode("");  
            bodyLog.setAgencyCode("");
            
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            String toDay = df.format(new Date());
            bodyLog.setCallTime(toDay);
            
            bodyLog.setResult(Boolean.TRUE);            
            bodyLog.setResponseBody(result.getBody());
            bodyLog.setRequestBody("");   
                //SaveLog : ApiLogTypes
                //LLTP = "100"; //ly lich tu phap
                //HHTP = "200"; // ho tich tu phap 
                //VNPOST = "300"; // vnpost
                //CMNS = "400"; //cap ma ngan sach
                //DMDC = "500"; //Danh muc dung chung
                //BHXH = "600"; //Tra cuu thong tin bhxh
                //CSDLDKDN = "700"; //CSDL dang ky doanh nghiep
                //VBQPPL = "800";   //van ban quy pham phap luat
            bodyLog.setApiType(ApiLogTypes.VNPOST);

            IdDto retLog = LGSPHCMLogService.addLog(bodyLog);
            logger.info("DIGO-Info 1.2.2: retLog: " + retLog);
            
        } catch (Exception e) {
            logger.info("getInfoCollectionFee fail " + e);   
            returnDto.setMessage(e.getMessage());
            returnDto.setError_code("10004");
            returnDto.setData(null);
            returnDto.setStatus("Fail");  
        }
        
        return returnDto;
    }
     
     /**
     * Name: getShippingInfo: Lay thong tin van chuyen
     * Author: phucnh.it2
     * Date: 20220712
     */
     
     public ApiResultDto getShippingInfo(String pagesize, String lastId){
        
         ApiResultDto returnDto = new ApiResultDto();   
         VNPostResultDto res = null;
         try {
            
            IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(vnPostOverLgspconfigid));                    
            String uri = config.getParametersValue("adapter").toString(); 
            String service = config.getParametersValue("urlTrackingVNPOST").toString();  
            String param = "?pagesize=" + pagesize + "&lastId" + lastId;                      
//            String URL = "https://api.ngsp.gov.vn/apiVNPostNGSP/p1.0/order/tracking" + param;
            String URL = uri + service + param;

            // set header lgspaccesstoken            
//            String token = this.getTokenLGSP();             
//            HttpHeaders headers = getExtendHeadersSend(token, lgspToken); 
            String lgspToken = getTokenLGSP();
            String vnpostToken = getTokenServices();            
            HttpHeaders headers = getExtendHeadersSend(vnpostToken, lgspToken);     
           
            HttpEntity<?> request = new HttpEntity<>( headers);
            ResponseEntity<String> result = restTemplate.exchange(URL, HttpMethod.GET, request, String.class);
            
            Gson g = new Gson();
            res = g.fromJson(result.getBody(), VNPostResultDto.class);
            
            logger.info("getShippingInfo: Response get " + gson.toJson(res));
            
            returnDto.setMessage("Success");
            returnDto.setError_code("1");
            returnDto.setData(res);
            returnDto.setStatus("OK"); 
                        
            //Thuc hien luu log call API
            logger.info("DIGO-Info 1.2.1: saveApiLog: getShippingInfo: Start...");  
            LGSPHCMLogInputDto bodyLog = new LGSPHCMLogInputDto();
            bodyLog.setRequestID("");
            bodyLog.setAPIFunction(URL);                        
            bodyLog.setDossierCode("");  
            bodyLog.setAgencyCode("");
            
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            String toDay = df.format(new Date());
            bodyLog.setCallTime(toDay);
            
            bodyLog.setResult(Boolean.TRUE);            
            bodyLog.setResponseBody(result.getBody());
            bodyLog.setRequestBody("");   
                //SaveLog : ApiLogTypes
                //LLTP = "100"; //ly lich tu phap
                //HHTP = "200"; // ho tich tu phap 
                //VNPOST = "300"; // vnpost
                //CMNS = "400"; //cap ma ngan sach
                //DMDC = "500"; //Danh muc dung chung
                //BHXH = "600"; //Tra cuu thong tin bhxh
                //CSDLDKDN = "700"; //CSDL dang ky doanh nghiep
                //VBQPPL = "800";   //van ban quy pham phap luat
            bodyLog.setApiType(ApiLogTypes.VNPOST);

            IdDto retLog = LGSPHCMLogService.addLog(bodyLog);
            logger.info("DIGO-Info 1.2.2: retLog: " + retLog);

            
        } catch (Exception e) {
            logger.info("getShippingInfo fail " + e);
            returnDto.setMessage(e.getMessage());
            returnDto.setError_code("10004");
            returnDto.setData(null);
            returnDto.setStatus("Fail");  
        }
         
         return returnDto;
    }
     
      /**
     * Name: getCancelBilling: Huy van don
     * Author: phucnh.it2
     * Date: 20220712
     */
    
     public String getCancelBilling(String customerCode, String orderNumber){
        try {
            
            IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(vnPostOverLgspconfigid));                    
            String uri = config.getParametersValue("adapter").toString(); 
            String service = config.getParametersValue("urlCancelOrderVNPOST").toString();  
            String authorizationCode = lgspToken;            
            String param = "?CustomerCode=" + customerCode + "&OrderNumber" + orderNumber;                      
//            String URL = "https://api.ngsp.gov.vn/apiVNPostNGSP/p1.0/order/cancel" + param;
            String URL = uri + service + param;

            // set header lgspaccesstoken
            String token = this.getTokenLGSP(); 
            HashMap<String, String> headers = getExtendHeaders(token);
            String res = MicroserviceExchange.getJson(this.restTemplate, URL, headers, String.class);
            logger.info("getCancelBilling: Response get " + gson.toJson(res));
                        
            //Thuc hien luu log call API
            logger.info("DIGO-Info 1.2.1: saveApiLog: getCancelBilling: Start...");  
            LGSPHCMLogInputDto bodyLog = new LGSPHCMLogInputDto();
            bodyLog.setRequestID("");
            bodyLog.setAPIFunction(URL);                        
            bodyLog.setDossierCode("");  
            bodyLog.setAgencyCode("");
            
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            String toDay = df.format(new Date());
            bodyLog.setCallTime(toDay);
            
            bodyLog.setResult(Boolean.TRUE);            
            bodyLog.setResponseBody(res);
            bodyLog.setRequestBody("");   
                //SaveLog : ApiLogTypes
                //LLTP = "100"; //ly lich tu phap
                //HHTP = "200"; // ho tich tu phap 
                //VNPOST = "300"; // vnpost
                //CMNS = "400"; //cap ma ngan sach
                //DMDC = "500"; //Danh muc dung chung
                //BHXH = "600"; //Tra cuu thong tin bhxh
                //CSDLDKDN = "700"; //CSDL dang ky doanh nghiep
                //VBQPPL = "800";   //van ban quy pham phap luat
            bodyLog.setApiType(ApiLogTypes.VNPOST);

            IdDto retLog = LGSPHCMLogService.addLog(bodyLog);
            logger.info("DIGO-Info 1.2.2: retLog: " + retLog);

            return res;
        } catch (Exception e) {
            logger.info("getCancelBilling fail " + e);
            return e.getMessage();
        }
    }

    //3.3 nhan thong tin van don
    public VNPostResultDto getOrder(Map<String, Object> body) {
        VNPostResultDto response = new VNPostResultDto();
        LGSPHCMLogInputDto bodyLog = new LGSPHCMLogInputDto();
        bodyLog.setApiType(ApiLogTypes.VNPOST);
        IdCodeNameSimpleDto item = new IdCodeNameSimpleDto();
        item.setName("VNPost");
        String url = null;
        Map<String, Object> reqBody = null;
        IntegratedConfigurationDto config = null;
        try {
            config = configurationService.getConfig(new ObjectId(vnPostOverLgspconfigid));
            url = buildUrl(config);

            String dossierId = validateBodyAndGetDossierId(config, body, item);
            body.remove("DossierId");

            reqBody = normalizeBodyFields(body);
            reqBody.put("ItemCode", "");

            HttpHeaders headers = getExtendHeadersSend(getTokenServices(), getTokenLGSP());
            HttpEntity<?> request = new HttpEntity<>(reqBody, headers);

            ResponseEntity<String> result = restTemplate.exchange(url, HttpMethod.POST, request, String.class);

            if(result.getBody() == null) saveLog(config, item, NULL_RESPONSE_STATUS, result.toString(), reqBody);

            saveLog(config, item, SUCCESS_STATUS, result.getBody(), reqBody);

            response = gson.fromJson(result.getBody(), VNPostResultDto.class);

            updateVNPostStatus(config, dossierId, reqBody, response);

            saveApiLog(bodyLog, url, result.getBody(), reqBody, true);

        } catch (IllegalArgumentException e) {
            logger.error("Invalid input: {}", e.getMessage());
            response.setStatus("101");
            response.setMessage(e.getMessage());
            saveLog(config,item,ERROR_STATUS,e.getMessage(), body);
            saveApiLog(bodyLog, url, e.getMessage(), body, false);
        } catch (Exception e) {
            logger.error("Error processing VNPost order: {}", e.getMessage(), e);
            response.setStatus("101");
            response.setMessage(e.getMessage());
            saveLog(config,item,ERROR_STATUS, e.getMessage(), body);
            saveApiLog(bodyLog, url, e.getMessage(), body, false);
        }

        logger.info("getOrder response: {}", response);
        return response;
    }

    private String validateBodyAndGetDossierId(IntegratedConfigurationDto config, Map<String, Object> body, IdCodeNameSimpleDto item) {
        if (!body.containsKey("DossierId") || body.get("DossierId") == null) {
            throw new IllegalArgumentException("DossierId is required");
        }
        if(!body.containsKey("OrderNumber") || body.get("OrderNumber") == null) {
            throw new IllegalArgumentException("OrderNumber is required");
        }

        item.setId(new ObjectId(body.get("DossierId").toString()));
        item.setCode(body.get("OrderNumber").toString());
        saveLog(config, item, SUCCESS_STATUS, "vnpost", body);
        return body.get("DossierId").toString();
    }

    private String buildUrl(IntegratedConfigurationDto config) {
        String uri = config.getParametersValue("adapter").toString();
        String service = config.getParametersValue("urlOrderPostVNPOST").toString();
        return uri + service;
    }

    private void updateVNPostStatus(IntegratedConfigurationDto config, String dossierId,
                                    Map<String, Object> normalizedBody, VNPostResultDto response) {
        UpdateVNPostStatus updateBody = new UpdateVNPostStatus();
        updateBody.setCustomerCode(normalizedBody.get("CustomerCode").toString());
        updateBody.setStatusCode(Integer.parseInt(response.getStatus()));
        updateBody.setStatusMessage(response.getMessage());
        updateBody.setItemCode(response.getItemCode());
        updateBody.setSoDonHang(response.getSoDonHang());
        updateBody.setData(buildDataPost(normalizedBody));

        String updateResponse = utilService.putVNPostStatusHCMLGSP(config, dossierId, updateBody);
        logger.debug("DIGO-Info: Update VNPost status response: {}", updateResponse);
    }

    private DataPost buildDataPost(Map<String, Object> normalizedBody) {
        DataPost dataPost = new DataPost();
        String[] fields = {"CustomerCode", "OrderNumber", "CODAmount", "SenderProvince", "SenderDistrict",
                "SenderAddress", "SenderName", "SenderEmail", "SenderTel", "SenderDesc",
                "Description", "ReceiverName", "ReceiverAddress", "ReceiverTel",
                "ReceiverProvince", "ReceiverDistrict", "ReceiverEmail"};
        for (String field : fields) {
            setDataPostField(dataPost, field, normalizedBody.get(field).toString());
        }
        return dataPost;
    }

    private void setDataPostField(DataPost dataPost, String field, String value) {
        switch (field) {
            case "CustomerCode": dataPost.setCustomerCode(value); break;
            case "OrderNumber": dataPost.setOrderNumber(value); break;
            case "CODAmount": dataPost.setCODAmount(value); break;
            case "SenderProvince": dataPost.setSenderProvince(value); break;
            case "SenderDistrict": dataPost.setSenderDistrict(value); break;
            case "SenderAddress": dataPost.setSenderAddress(value); break;
            case "SenderName": dataPost.setSenderName(value); break;
            case "SenderEmail": dataPost.setSenderEmail(value); break;
            case "SenderTel": dataPost.setSenderTel(value); break;
            case "SenderDesc": dataPost.setSenderDesc(value); break;
            case "Description": dataPost.setDescription(value); break;
            case "ReceiverName": dataPost.setReceiverName(value); break;
            case "ReceiverAddress": dataPost.setReceiverAddress(value); break;
            case "ReceiverTel": dataPost.setReceiverTel(value); break;
            case "ReceiverProvince": dataPost.setReceiverProvince(value); break;
            case "ReceiverDistrict": dataPost.setReceiverDistrict(value); break;
            case "ReceiverEmail": dataPost.setReceiverEmail(value); break;
        }
    }

    private void saveIntegratedLog(IntegratedConfigurationDto config, IdCodeNameSimpleDto item, VNPostResultDto response, Map<String, Object> reqBody) {
        IntegratedLogsService.save(config, item, Integer.parseInt(response.getStatus()),
                response.getMessage(), reqBody.toString());
    }

    private void saveLog(IntegratedConfigurationDto config, IdCodeNameSimpleDto item, Object status, String response, Map<String, Object> reqBody) {
        int statusCode = status instanceof String ? Integer.parseInt((String) status) : (Integer) status;
        IntegratedLogsService.save(config, item, statusCode, response != null ? response : "Null", reqBody != null ? reqBody.toString() : "{}");
    }

    private void saveApiLog(LGSPHCMLogInputDto bodyLog, String url, String responseBody,Map<String, Object> reqBody, boolean status) {
        bodyLog.setAPIFunction(url);
        bodyLog.setCallTime(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        bodyLog.setResult(status);
        bodyLog.setResponseBody(responseBody != null ? responseBody : "");
        bodyLog.setRequestBody(reqBody.toString());
        try {
            IdDto retLog = LGSPHCMLogService.addLog(bodyLog);
            logger.info("DIGO-Info: API log saved: {}", retLog);
        } catch (Exception e) {
            logger.error("Failed to save API log: {}", e.getMessage(), e);
        }
    }

    public VNPostResultDto saveOrder(Map<String, Object> body)  {
        VNPostResultDto response = new VNPostResultDto();

        VNPostEvent vnPostEvent = new VNPostEvent();
        vnPostEvent.setCreatedDate(new Date());
        vnPostEvent.setRequest(body);
        vnPostEvent.setUserId(Context.getUserId().toString());
        vnPostEvent.setFullname(Context.getUserFullname());
        vnPostEvent.setOrderNumber(body.get("OrderNumber").toString());
        vnPostEvent.setDossierId(body.get("DossierId").toString());
        vnPostEvent = vnPostEventRepository.save(vnPostEvent);
        vnPostEvent.setStatus(1);
        vnPostEvent.setMessage("Send to kafka success");
        try{
            if(!this.vnPostProducerStream.pushVNPostMessageProcedureDetailMain(vnPostEvent).get()){
                vnPostEvent.setStatus(0);
                vnPostEvent.setMessage("Send to kafka fail");
                response.setStatus(new String("500"));
                response.setMessage(new String("Lỗi gửi VNPOST"));
                vnPostEventRepository.save(vnPostEvent);
            } else {
                response.setStatus(new String("200"));
                response.setMessage(new String("Gửi hồ sơ đến VNPost thành công!"));
            }
        } catch (Exception digoEx) {
            logger.info("getOrder: " + digoEx);

            if (digoEx.getCause() instanceof DigoHttpException) {
                DigoHttpException ex = (DigoHttpException) digoEx.getCause();
                // Extract code and message if DigoHttpException has these methods
                String errorCode = String.valueOf(ex.getStatusCode()); // Example of getCode() usage
                String errorMessage = Arrays.stream(ex.getArguments()).collect(Collectors.toList()).toString();

                response.setStatus(errorCode);
                response.setMessage(errorMessage);
                logger.info("getOrder - DigoHttpException: Code=" + errorCode + ", Message=" + errorMessage);
            }

        }
//        catch (Exception e){
//                logger.info("getOrder: " + e);
//        }

            return response;
        }
       
    
    //3.6 lay thong tin goi cuoc van chuyen
    public LgspVNPostPriceResponseDto getPrice(Map<String, Object> body) {
        LgspVNPostPriceResponseDto response = null;
        LGSPHCMLogInputDto bodyLog = new LGSPHCMLogInputDto();
        
        try {
            IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(vnPostOverLgspconfigid));                    
            String uri = config.getParametersValue("adapter").toString();
            String service = config.getParametersValue("urlGetPriceVNPOST").toString();  
//            String service = "apiVNPostNGSP/st/p1.0/info/getPrice";

            String URL = uri + service;

            bodyLog.setAPIFunction(URL);
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            String toDay = df.format(new Date());
            bodyLog.setCallTime(toDay);

            String lgspToken = getTokenLGSP();
            String vnpostToken = getTokenServices();
            HttpHeaders headers = getExtendHeadersSend(vnpostToken, lgspToken);

            HttpEntity<?> request = new HttpEntity<>(body, headers);
            ResponseEntity<String> result = restTemplate.exchange(URL, HttpMethod.POST, request, String.class);
            Gson g = new Gson();
            response = g.fromJson(result.getBody(), LgspVNPostPriceResponseDto.class);

            //Thực hiện luu IntegratedLogs
            IdCodeNameSimpleDto item = new IdCodeNameSimpleDto();                 
            IntegratedLogsService.save(config, item, Integer.parseInt(response.getContent().getCode()), response.getContent().getDesc(), response.toString());
            
            //Thuc hien luu log call API
            logger.info("DIGO-Info 1.2.1: saveApiLog: postOrder: Start...");

            bodyLog.setResult(Boolean.TRUE);
            bodyLog.setResponseBody(result.getBody());
            bodyLog.setRequestBody("");
            bodyLog.setApiType(ApiLogTypes.VNPOST);

            IdDto retLog = LGSPHCMLogService.addLog(bodyLog);
            logger.info("DIGO-Info 1.2.2: retLog: " + retLog);
        } catch (Exception e) {
            logger.info("postOrder getPrice: " + e);
            bodyLog.setResult(Boolean.FALSE);
            bodyLog.setResponseBody(e.toString());
            bodyLog.setRequestBody("");
            bodyLog.setApiType(ApiLogTypes.VNPOST);

            try {
                LGSPHCMLogService.addLog(bodyLog);
            } catch (Exception ex) {
                logger.info("postOrder fail to save log: " + ex);
            }
        }
        return response;
    }
    
    //tu dong cap nhat trang thai
    //@Scheduled(cron = "0 */5 * * * *") //05 phut chay 1 lan tai giay thu 0
    @Scheduled(cron = "0 */5 * * * *")
    ////@SchedulerLock(name = "getTrackingScheduleHCM", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void getTrackingSchedule() throws java.text.ParseException, JsonProcessingException {
        if (enableScheduleTrackingVNPost) {
            logger.info("Star JOB update VNPost HCM Status");
            String c_lastId = "000000000000000000000000";
            String c_pageSize = "1000";
            List<VNPostStatus> arrR = VNPostStatusService.searchLast(null, null, vnPostOverLgspconfigid.toString());
            if (arrR.size() == 0) {
                c_lastId = "000000000000000000000000";
            } else {
                for (VNPostStatus a : arrR) {
                    c_lastId = a.getId().toString();
                    break;
                }
            }
      
    try {
            IntegratedConfigurationDto config = configurationService.getConfig(new ObjectId(vnPostOverLgspconfigid));                    
            String uri = config.getParametersValue("adapter").toString(); 
            String service = config.getParametersValue("urlSTOrderTrackingVNPOST").toString();  
    //      String service = "apiVNPostNGSP/st/p1.0/order/tracking";

            String vnpostUrl = uri + service;  
            logger.info("vnpostUrl: ", vnpostUrl); 

            String lgspToken = getTokenLGSP();
            logger.info("lgspToken: ", lgspToken); 

            String vnpostToken = getTokenServices();        
            logger.info("Token VNPost: ", vnpostToken);   

            HttpHeaders headers = getExtendHeadersSend(vnpostToken, lgspToken);         
            HttpEntity<?> request = new HttpEntity<>(headers);        
            ResponseEntity<String> result = restTemplate.exchange(vnpostUrl
                    + "?pagesize=" + c_pageSize + "&lastId=" + c_lastId, HttpMethod.GET, request, String.class);
            
    if (Objects.nonNull(result)){
        logger.info("Response API first:result ", result);

                String text = result.getBody();
                text = text.replace("rh:doc", "rhdoc");
                text = text.replace("$oid", "oid");
                Gson g = new Gson();
                LgspVNPostMStatusDto response = g.fromJson(text, LgspVNPostMStatusDto.class);
                logger.info("Response API first: ", response.get_size());
            //Luu vào vnpostStatus
            if(transmitDossierCodeViaVNPost == 1){
                this.TrackingVnpostStatusByCode(response, c_pageSize, vnpostUrl, request);
            }else{
                this.TrackingVnpostStatusById(response,c_pageSize, vnpostUrl, request);
            }
            //end of response
            }else {
                logger.info("Response is null: ", vnpostUrl);
            }
        } catch (Exception ex) {
               logger.info("try catch getTrackingSchedule(): " + ex);
               throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        } 
    }        
}
    
    
    private String callAPIPostListObject(String url, List<Object> body) {
        //Create header
        HttpHeaders headers = new HttpHeaders();
//       headers.setBearerAuth(this.getOAuth2AccessToken().toString());
//        headers.setBasicAuth(config.getParametersValue("client-id").toString(), config.getParametersValue("client-secret").toString());
        headers.setContentType(MediaType.APPLICATION_JSON);
        //Create request with body

        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<String> result = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
        //Return body
        return result.getBody();
    }
    
      private String callAPIPut(String url, Object body) {
        //Create header
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(this.getOAuth2AccessToken().toString());
//        headers.setBasicAuth(config.getParametersValue("client-id").toString(), config.getParametersValue("client-secret").toString());
        headers.setContentType(MediaType.APPLICATION_JSON);
        //Create request with body

        HttpEntity<?> request = new HttpEntity<>(body, headers);
        ResponseEntity<String> result = restTemplate.exchange(url, HttpMethod.PUT, request, String.class);
        //Return body
        return result.getBody();
    }
      
      private OAuth2AccessToken getOAuth2AccessToken() {
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(this.tokenUrl + "/protocol/openid-connect/token");
        details.setClientId(this.clientId);
        details.setClientSecret(this.clientSecret);
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext()).getAccessToken();
    }

    private Map<String, Object> normalizeBodyFields(Map<String, Object> body) {

        List<String> correctKeys = Arrays.asList(
                "CustomerCode", "OrderNumber", "CODAmount", "SenderProvince", "SenderDistrict", "SenderAddress",
                "SenderName", "SenderEmail", "SenderTel", "SenderDesc", "Description",
                "ReceiverName", "ReceiverAddress", "ReceiverTel", "ReceiverProvince", "ReceiverDistrict", "ReceiverEmail"
        );

        Map<String, Object> normalizedBody = new HashMap<>();

        for (Map.Entry<String, Object> entry : body.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            String matchedKey = correctKeys.stream()
                    .filter(k -> k.equalsIgnoreCase(key))
                    .findFirst()
                    .orElse(key);

            normalizedBody.put(matchedKey, value);
        }

        return normalizedBody;
    }

    private void TrackingVnpostStatusByCode(LgspVNPostMStatusDto response,String c_pageSize,String vnpostUrl, HttpEntity<?> request){
        Gson g = new Gson();
        logger.info("TrackingVnpostStatusByCode");
        try {
            if(response.get_size() != 0)
            {
                List<String> arrOI = new ArrayList<String>();
                List<VNPostStatus> newArray = new ArrayList<VNPostStatus>();
                for(LgspVNPostMStatusDto.MrhDoc  r: response.get_embedded().getRhdoc())
                {
                    VNPostStatus newA = new VNPostStatus();

                    newA.setCustomerCode(r.getCustomerCode());
                    newA.setStatusCode(r.getStatusCode());
                    newA.setStatusMessage(r.getStatusMessage());
                    newA.setOrderNumber(r.getOrderNumber());
                    newA.setEventTime(r.getEventTime());
                    newA.setNotes(r.getNotes());
                    //newA.setItemCode(r.getItemCode().toString());
                    //newA.setPostID(r.getPostID());
                    newArray.add(newA);
                    arrOI.add(newA.getOrderNumber());
                }
                VNPostStatusService.postVNPostStatus(newArray);
                String arrDossier = utilService.postDossiersVNPostHCMLGSPByCode(config, arrOI);//lấy danh sach dossier có vnpostStatus vs vnpostStatusReturn

                logger.info("arrDossier = this.callAPIPostListObject(urlListDossier, arrOI) : ", arrDossier);
                //cập nhật trang thái vnpostStatus vào dossier
                ListDossierUpdateVnpostStatusDto[] arrSectorAgency = g.fromJson(arrDossier, ListDossierUpdateVnpostStatusDto[].class);
                if (Objects.nonNull(arrSectorAgency) && arrSectorAgency.length > 0) {
                    for (ListDossierUpdateVnpostStatusDto sOld : arrSectorAgency) {
                        for (VNPostStatus sNew : newArray) {
                            if (sNew.getOrderNumber() != null) {
                                if (sOld.getCode().equals(sNew.getOrderNumber())) {
                                    if (sNew.getOrderNumber().toLowerCase().contains("tkq")) {
                                        UpdateVNPostStatus newBody = new UpdateVNPostStatus();
                                        newBody.setCustomerCode(sNew.getCustomerCode());
                                        newBody.setId(sNew.getId().toString());
                                        newBody.setStatusCode(sNew.getStatusCode());
                                        newBody.setStatusMessage(sNew.getStatusMessage());
                                        String arrPut = utilService.returnVNPostStatusHCMLGSP(config, sOld.getId(), newBody);
                                    } else {
                                        UpdateVNPostStatus newBody = new UpdateVNPostStatus();
                                        newBody.setCustomerCode(sNew.getCustomerCode());
                                        newBody.setId(sNew.getId().toString());
                                        newBody.setStatusCode(sNew.getStatusCode());
                                        newBody.setStatusMessage(sNew.getStatusMessage());
                                        String arrPut = utilService.newVNPostStatusHCMLGSP(config, sOld.getId(), newBody);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            logger.info("Response API get_total_pages: ", response.get_total_pages());
            //search danh sach vnpostStatus trong Adapter
            if (response.get_total_pages() > 0) {
                for (int i = 0; i < response.get_total_pages(); i++) {
                    String lastIdFor = null;
                    List<VNPostStatus> arrRFor = VNPostStatusService.search(null, null, vnPostOverLgspconfigid.toString());
                    //public List<VNPostStatus> search(String customerCode, String orderNumber, String deploymentId)
                    if (arrRFor.size() == 0) {
                        lastIdFor = "000000000000000000000000";
                    } else {
                        //sort ngược, lấy giá trị cuối cùng
                        for (VNPostStatus b : arrRFor) {
                            lastIdFor = b.getId().toString();
                            break;
                        }
                    }
                    ResponseEntity<String> resultFor = restTemplate.exchange(vnpostUrl
                            + "?pagesize=" + c_pageSize + "&lastId=" + lastIdFor, HttpMethod.GET, request, String.class);
                    String textFor = resultFor.getBody();
                    textFor = textFor.replace("rh:doc", "rhdoc");
                    textFor = textFor.replace("$oid", "oid");
                    VNPostStatusDto responseFor = g.fromJson(textFor, VNPostStatusDto.class);
                    logger.info("Response API second: ", responseFor.get_size());
                    if (responseFor.get_size() != 0) {
                        List<String> arrOI = new ArrayList<String>();
                        List<VNPostStatus> newArrayFor = new ArrayList<VNPostStatus>();
                        for (VNPostStatusDto.RhDoc r : responseFor.get_embedded().getRhdoc()) {
                            VNPostStatus newA = new VNPostStatus();
                            newA.setId(new ObjectId(r.get_id().getOid()));
                            newA.setCustomerCode(r.getCustomerCode());
                            //newA.setDeploymentId(deploymentId.toString());
                            newA.setDeploymentId(vnPostOverLgspconfigid.toString());
                            newA.setEventTime(r.getEventTime());
                            newA.setItemCode(r.getItemCode());
                            newA.setNotes(r.getNotes());
                            newA.setOrderNumber(r.getOrderNumber());
                            newA.setPostID(r.getPOSID());
                            newA.setStatusCode(r.getStatusCode());
                            newA.setStatusMessage(r.getStatusMessage());
                            newArrayFor.add(newA);
                            arrOI.add(newA.getCustomerCode());

                        }
                        VNPostStatusService.postVNPostStatus(newArrayFor); //luu vào vnpostStatus trên Adapter
                        String arrDossier = utilService.postDossiersVNPostHCMLGSPByCode(config, arrOI);   //lấy danh sách bên Padman, nếu có danh sách thì thực hiện update

                        ListDossierUpdateVnpostStatusDto[] arrSectorAgency = g.fromJson(arrDossier, ListDossierUpdateVnpostStatusDto[].class);
                        if(Objects.nonNull(arrSectorAgency) && arrSectorAgency.length > 0)
                        {
                            for(ListDossierUpdateVnpostStatusDto sOld: arrSectorAgency)
                            {
                                for(VNPostStatus sNew: newArrayFor)
                                {
                                    if (sNew.getOrderNumber() != null) {
                                        if (sOld.getCode().equals(sNew.getOrderNumber())) {
                                            if (sNew.getOrderNumber().toLowerCase().contains("tkq")) {
                                                UpdateVNPostStatus newBody = new UpdateVNPostStatus();
                                                newBody.setCustomerCode(sNew.getCustomerCode());
                                                newBody.setId(sNew.getId().toString());
                                                newBody.setStatusCode(sNew.getStatusCode());
                                                newBody.setStatusMessage(sNew.getStatusMessage());
                                                String arrPut = utilService.returnVNPostStatusHCMLGSP(config, sOld.getId(), newBody);
                                            } else {
                                                UpdateVNPostStatus newBody = new UpdateVNPostStatus();
                                                newBody.setCustomerCode(sNew.getCustomerCode());
                                                newBody.setId(sNew.getId().toString());
                                                newBody.setStatusCode(sNew.getStatusCode());
                                                newBody.setStatusMessage(sNew.getStatusMessage());
                                                String arrPut = utilService.newVNPostStatusHCMLGSP(config, sOld.getId(), newBody);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.info("try catch TrackingVnpostStatusByCode(): " + e);
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private void TrackingVnpostStatusById(LgspVNPostMStatusDto response,String c_pageSize, String vnpostUrl, HttpEntity<?> request){
        Gson g = new Gson();
        logger.info("TrackingVnpostStatusById");
        try{
            if(response.get_size() != 0)
            {
                List<Object> arrOI = new ArrayList<Object>();
                List<VNPostStatus> newArray = new ArrayList<VNPostStatus>();
                for(LgspVNPostMStatusDto.MrhDoc  r: response.get_embedded().getRhdoc())
                {
                    VNPostStatus newA = new VNPostStatus();

                    newA.setCustomerCode(r.getCustomerCode());
                    newA.setStatusCode(r.getStatusCode());
                    newA.setStatusMessage(r.getStatusMessage());
                    newA.setOrderNumber(r.getOrderNumber());
                    newA.setEventTime(r.getEventTime());
                    newA.setNotes(r.getNotes());
                    //newA.setItemCode(r.getItemCode().toString());
                    //newA.setPostID(r.getPostID());
                    newArray.add(newA);

                    if (newA.getOrderNumber().length() > 23) {
                        String idHoSo = newA.getOrderNumber().substring(0, 24);
                        if (ObjectId.isValid(idHoSo)) {
                            arrOI.add(idHoSo);
                        }
                    }
                }
                VNPostStatusService.postVNPostStatus(newArray);
                String arrDossier = utilService.postDossiersVNPostHCMLGSP(config, arrOI);//lấy danh sach dossier có vnpostStatus vs vnpostStatusReturn

                logger.info("arrDossier = this.callAPIPostListObject(urlListDossier, arrOI) : ", arrDossier);
                //cập nhật trang thái vnpostStatus vào dossier
                ListDossierUpdateVnpostStatusDto[] arrSectorAgency = g.fromJson(arrDossier, ListDossierUpdateVnpostStatusDto[].class);
                if (Objects.nonNull(arrSectorAgency) && arrSectorAgency.length > 0) {
                    for (ListDossierUpdateVnpostStatusDto sOld : arrSectorAgency) {
                        for (VNPostStatus sNew : newArray) {
                            if (sNew.getOrderNumber().length() > 23) {
                                if (sOld.getId().equals(sNew.getOrderNumber().substring(0, 24))) {
                                    if (sNew.getOrderNumber().length() > 24) {
                                        if (sNew.getOrderNumber().substring(24).equals("tkq")) {
                                            //Nếu mã hs của hs mới = mã hs của hs cũ và có chuỗi cuối = tkq
                                            UpdateVNPostStatus newBody = new UpdateVNPostStatus();
                                            newBody.setCustomerCode(sNew.getCustomerCode());
                                            newBody.setId(sNew.getId().toString());
                                            newBody.setStatusCode(sNew.getStatusCode());
                                            newBody.setStatusMessage(sNew.getStatusMessage());
                                            String idHoSo = sNew.getOrderNumber().substring(0, 24);
                                            String arrPut = utilService.returnVNPostStatusHCMLGSP(config, sOld.getId(), newBody);
                                        }
                                    } else {
                                        UpdateVNPostStatus newBody = new UpdateVNPostStatus();
                                        newBody.setCustomerCode(sNew.getCustomerCode());
                                        newBody.setId(sNew.getId().toString());
                                        newBody.setStatusCode(sNew.getStatusCode());
                                        newBody.setStatusMessage(sNew.getStatusMessage());
                                        String arrPut = utilService.newVNPostStatusHCMLGSP(config, sOld.getId(), newBody);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            logger.info("Response API get_total_pages: ", response.get_total_pages());
            //search danh sach vnpostStatus trong Adapter
            if (response.get_total_pages() > 0) {
                for (int i = 0; i < response.get_total_pages(); i++) {
                    String lastIdFor = null;
                    List<VNPostStatus> arrRFor = VNPostStatusService.search(null, null, vnPostOverLgspconfigid.toString());
                    //public List<VNPostStatus> search(String customerCode, String orderNumber, String deploymentId)
                    if (arrRFor.size() == 0) {
                        lastIdFor = "000000000000000000000000";
                    } else {
                        //sort ngược, lấy giá trị cuối cùng
                        for (VNPostStatus b : arrRFor) {
                            lastIdFor = b.getId().toString();
                            break;
                        }
                    }
                    ResponseEntity<String> resultFor = restTemplate.exchange(vnpostUrl
                            + "?pagesize=" + c_pageSize + "&lastId=" + lastIdFor, HttpMethod.GET, request, String.class);
                    String textFor = resultFor.getBody();
                    textFor = textFor.replace("rh:doc", "rhdoc");
                    textFor = textFor.replace("$oid", "oid");
                    VNPostStatusDto responseFor = g.fromJson(textFor, VNPostStatusDto.class);
                    logger.info("Response API second: ", responseFor.get_size());
                    if (responseFor.get_size() != 0) {
                        List<Object> arrOI = new ArrayList<Object>();
                        List<VNPostStatus> newArrayFor = new ArrayList<VNPostStatus>();
                        for (VNPostStatusDto.RhDoc r : responseFor.get_embedded().getRhdoc()) {
                            VNPostStatus newA = new VNPostStatus();
                            newA.setId(new ObjectId(r.get_id().getOid()));
                            newA.setCustomerCode(r.getCustomerCode());
                            //newA.setDeploymentId(deploymentId.toString());
                            newA.setDeploymentId(vnPostOverLgspconfigid.toString());
                            newA.setEventTime(r.getEventTime());
                            newA.setItemCode(r.getItemCode());
                            newA.setNotes(r.getNotes());
                            newA.setOrderNumber(r.getOrderNumber());
                            newA.setPostID(r.getPOSID());
                            newA.setStatusCode(r.getStatusCode());
                            newA.setStatusMessage(r.getStatusMessage());
                            newArrayFor.add(newA);
                            if (newA.getOrderNumber().length() > 23) {
                                String idHoSo = newA.getOrderNumber().substring(0, 24);
                                if (ObjectId.isValid(idHoSo)) {
                                    arrOI.add(idHoSo);
                                }
                            }
                        }
                        VNPostStatusService.postVNPostStatus(newArrayFor); //luu vào vnpostStatus trên Adapter
                        String arrDossier = utilService.postDossiersVNPostHCMLGSP(config, arrOI);   //lấy danh sách bên Padman, nếu có danh sách thì thực hiện update

                        ListDossierUpdateVnpostStatusDto[] arrSectorAgency = g.fromJson(arrDossier, ListDossierUpdateVnpostStatusDto[].class);
                        if(Objects.nonNull(arrSectorAgency) && arrSectorAgency.length > 0)
                        {
                            for(ListDossierUpdateVnpostStatusDto sOld: arrSectorAgency)
                            {
                                for(VNPostStatus sNew: newArrayFor)
                                {
                                    if (sNew.getOrderNumber().length() > 23) {
                                        if (sOld.getId().equals(sNew.getOrderNumber().substring(0, 24))) {
                                            if (sNew.getOrderNumber().length() > 24) {
                                                if (sNew.getOrderNumber().substring(24).equals("tkq")) {
                                                    UpdateVNPostStatus newBody = new UpdateVNPostStatus();
                                                    newBody.setCustomerCode(sNew.getCustomerCode());
                                                    newBody.setId(sNew.getId().toString());
                                                    newBody.setStatusCode(sNew.getStatusCode());
                                                    newBody.setStatusMessage(sNew.getStatusMessage());
                                                    String idHoSo = sNew.getOrderNumber().substring(0, 24);
                                                    String arrPut = utilService.returnVNPostStatusHCMLGSP(config, sOld.getId(), newBody);
                                                }
                                            } else {
                                                UpdateVNPostStatus newBody = new UpdateVNPostStatus();
                                                newBody.setCustomerCode(sNew.getCustomerCode());
                                                newBody.setId(sNew.getId().toString());
                                                newBody.setStatusCode(sNew.getStatusCode());
                                                newBody.setStatusMessage(sNew.getStatusMessage());
                                                String arrPut = utilService.newVNPostStatusHCMLGSP(config, sOld.getId(), newBody);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.info("try catch TrackingVnpostStatusById(): " + e);
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }
}
