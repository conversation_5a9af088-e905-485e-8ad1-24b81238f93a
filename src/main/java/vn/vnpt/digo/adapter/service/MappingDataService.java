/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.TextCriteria;
import org.springframework.data.mongodb.core.query.TextQuery;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import vn.vnpt.digo.adapter.document.MappingData;
import vn.vnpt.digo.adapter.document.MappingType;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.MappingDataByIdDto;
import vn.vnpt.digo.adapter.dto.MappingDataDataTypeDto;
import vn.vnpt.digo.adapter.dto.MappingDataDto;
import vn.vnpt.digo.adapter.dto.MappingDataInputDto;
import vn.vnpt.digo.adapter.dto.MappingDataListDto;
import vn.vnpt.digo.adapter.dto.MappingDataUpdateDto;
import vn.vnpt.digo.adapter.dto.PostDataLogToKafkaDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.LogType;
import vn.vnpt.digo.adapter.repository.MappingDataRepository;
import vn.vnpt.digo.adapter.repository.MappingTypeRepository;
import vn.vnpt.digo.adapter.stream.DataLoggerStream;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.KafkaExchange;
import vn.vnpt.digo.adapter.util.Translator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import vn.vnpt.digo.adapter.pojo.MappingDataValue;

/**
 *
 * <AUTHOR>
 */
@Service
public class MappingDataService {

    @Autowired
    private MappingDataRepository mappingDataRepository;

    @Autowired
    private MappingTypeRepository mappingTypeRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private Translator translator;

    @Autowired
    private DataLoggerStream dataLoggerStream;

    Logger logger = LoggerFactory.getLogger(IntegrationServiceService.class);

    public MappingDataDto getBySource(ObjectId type, String sourceId, String destId) {
        ObjectId deploymentId = Context.getDeploymentId();
        MappingDataDto res = mappingDataRepository.getBySource(type, sourceId, deploymentId);
        return res;
    }

    public MappingDataDto getBySource(ObjectId type, String sourceId) {
        ObjectId deploymentId = Context.getDeploymentId();
        MappingDataDto res = mappingDataRepository.getBySource(type, sourceId, deploymentId);
        return res;
    }

    public MappingDataDto getByMappingdata(String typeName) {
        ObjectId deploymentId = Context.getDeploymentId();
        // Create query
        Query query;
        if (Objects.nonNull(typeName) && !Objects.equals(typeName, "")) {
            TextCriteria criteria = TextCriteria.forDefaultLanguage().matchingAny(typeName).caseSensitive(false).diacriticSensitive(true);
            TextCriteria.forDefaultLanguage().getCriteriaObject();
            query = TextQuery.queryText(criteria);
        } else {
            query = new Query();
        }
        query.addCriteria(Criteria.where("status").is(1));
        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("ignoreDeployment").is(true),
                Criteria.where("deploymentId").is(deploymentId)
        );
        query.addCriteria(criteria);
        List<MappingDataListDto> mappingDatas = mongoTemplate.find(query, MappingDataListDto.class);
        Gson g = new Gson();
        if (!mappingDatas.isEmpty()) { // Kiểm tra danh sách không rỗng
            Gson gson = new Gson();
            String json = gson.toJson(mappingDatas.get(0)); // Chuyển object thành JSON string
            MappingDataDto response = gson.fromJson(json, MappingDataDto.class); // Chuyển JSON thành object

            System.out.println(response);
            return response;
        }
        return null;
    }

    public MappingDataDto getBySource(ObjectId type, String sourceId, ObjectId deploymentId) {
        MappingDataDto res = mappingDataRepository.getBySource(type, sourceId, deploymentId);
        return res;
    }

    public MappingDataDto getByDest(ObjectId type, String sourceId) {
        ObjectId deploymentId = Context.getDeploymentId();
        MappingDataDto res = mappingDataRepository.getByDest(type, sourceId, deploymentId);
        return res;
    }

    public MappingDataDto getByDest(ObjectId type, String sourceId, ObjectId deploymentId) {
        MappingDataDto res = mappingDataRepository.getByDest(type, sourceId, deploymentId);
        return res;
    }

    public Page<MappingDataListDto> search(String keyword, Integer phrase, Integer status, Boolean deleted, Pageable pageable) {
        // Get deployment id
        ObjectId deploymentId = Context.getDeploymentId();
        // Create query
        Query query;
        if (Objects.nonNull(keyword) && !Objects.equals(keyword, "")) {
            TextCriteria criteria;
            if (phrase == 1) {
                criteria = TextCriteria.forDefaultLanguage().matchingPhrase(keyword).caseSensitive(false).diacriticSensitive(true);
            } else {
                criteria = TextCriteria.forDefaultLanguage().matchingAny(keyword).caseSensitive(false).diacriticSensitive(true);
            }
            TextCriteria.forDefaultLanguage().getCriteriaObject();
            query = TextQuery.queryText(criteria);
        } else {
            query = new Query();
        }
        if (Objects.nonNull(status)) {
            query.addCriteria(Criteria.where("status").is(status));
        }
        if (Objects.nonNull(deleted)) {
            query.addCriteria(Criteria.where("deleted").is(deleted));
        }

        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("ignoreDeployment").is(true),
                Criteria.where("deploymentId").is(deploymentId)
        );
        query.addCriteria(criteria);

        query.with(pageable);
        query.with(Sort.by(Sort.Order.desc("_id")));
        long offset = this.getOffset(pageable);
        query.skip(offset);
        query.limit(pageable.getPageSize());
        List<MappingDataListDto> mappingDatas = mongoTemplate.find(query, MappingDataListDto.class);
        Page<MappingDataListDto> page = PageableExecutionUtils.getPage(mappingDatas, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), MappingDataListDto.class));
        // Set number value
        int num = (page.getNumber() * page.getSize()) + 1;
        for (MappingDataListDto item : page) {
            item.setNum(num++);
            item.setTransType(translator.getCurrentLocaleId());
        }

        return page;
    }

    public List<MappingDataListDto>  getListMapping(String keyword,ObjectId typeId, Integer phrase, Integer status, Boolean deleted, Pageable pageable) {
        // Get deployment id
        ObjectId deploymentId = Context.getDeploymentId();
        // Create query
        Query query;
        if (Objects.nonNull(keyword) && !Objects.equals(keyword, "")) {
            TextCriteria criteria;
            if (phrase == 1) {
                criteria = TextCriteria.forDefaultLanguage().matchingPhrase(keyword).caseSensitive(false).diacriticSensitive(true);
            } else {
                criteria = TextCriteria.forDefaultLanguage().matchingAny(keyword).caseSensitive(false).diacriticSensitive(true);
            }
            TextCriteria.forDefaultLanguage().getCriteriaObject();
            query = TextQuery.queryText(criteria);
        } else {
            query = new Query();
        }
        if (Objects.nonNull(status)) {
            query.addCriteria(Criteria.where("status").is(status));
        }
        if (Objects.nonNull(deleted)) {
            query.addCriteria(Criteria.where("deleted").is(deleted));
        }
        if (Objects.nonNull(typeId)) {
            query.addCriteria(Criteria.where("type.id").is(typeId));
        }

        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("ignoreDeployment").is(true),
                Criteria.where("deploymentId").is(deploymentId)
        );
        query.addCriteria(criteria);

        query.with(pageable);
        query.with(Sort.by(Sort.Order.desc("_id")));
        List<MappingDataListDto> mappingDatas = mongoTemplate.find(query, MappingDataListDto.class);
        return mappingDatas;
    }

    public List<MappingDataListDto>  getListMappingDLK(String keyword,ObjectId typeId, Integer phrase, Integer status, Boolean deleted) {
        // Get deployment id
        ObjectId deploymentId = Context.getDeploymentId();
        // Create query
        Query query;
        if (Objects.nonNull(keyword) && !Objects.equals(keyword, "")) {
            TextCriteria criteria;
            if (phrase == 1) {
                criteria = TextCriteria.forDefaultLanguage().matchingPhrase(keyword).caseSensitive(false).diacriticSensitive(true);
            } else {
                criteria = TextCriteria.forDefaultLanguage().matchingAny(keyword).caseSensitive(false).diacriticSensitive(true);
            }
            TextCriteria.forDefaultLanguage().getCriteriaObject();
            query = TextQuery.queryText(criteria);
        } else {
            query = new Query();
        }
        if (Objects.nonNull(status)) {
            query.addCriteria(Criteria.where("status").is(status));
        }
        if (Objects.nonNull(deleted)) {
            query.addCriteria(Criteria.where("deleted").is(deleted));
        }
        if (Objects.nonNull(typeId)) {
            query.addCriteria(Criteria.where("type.id").is(typeId));
        }

        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("ignoreDeployment").is(true),
                Criteria.where("deploymentId").is(deploymentId)
        );
        query.addCriteria(criteria);
        query.with(Sort.by(Sort.Order.desc("_id")));
        List<MappingDataListDto> mappingDatas = mongoTemplate.find(query, MappingDataListDto.class);
        return mappingDatas;
    }

    public IdDto create(MappingDataInputDto input) {
        try {
            MappingData newMappingData = new MappingData();
            if (Objects.nonNull(input.getId())) {
                newMappingData.setId(input.getId());
            }
            // Set type
            MappingType type = mappingTypeRepository.findById(input.getType().getId()).get();
            newMappingData.setTransType(type);
            newMappingData.setSource(input.getSource());
            newMappingData.setDest(input.getDest());
            if (Objects.nonNull(input.getStatus())) {
                newMappingData.setStatus(input.getStatus());
            }
            if (Objects.nonNull(input.getIgnoreDeployment())) {
                newMappingData.setIgnoreDeployment(input.getIgnoreDeployment());
            }
            newMappingData.setDeploymentId(Context.getDeploymentId());
            MappingData mappingData = mappingDataRepository.save(newMappingData);
            KafkaExchange.pushLog(dataLoggerStream, mappingData.getId(), null, LogType.NEW_MAPPING_DATA);

            return new IdDto(mappingData.getId());
        } catch (Exception ex) {
            throw new DigoHttpException(10008, new String[]{translator.toLocale("lang.phrase.mapping-data")}, HttpServletResponse.SC_BAD_REQUEST);
        }
    }

    public AffectedRowsDto update(ObjectId id, MappingDataUpdateDto input) {

        AffectedRowsDto affectedRows = new AffectedRowsDto();
        try {
            MappingData oldMappingData = mappingDataRepository.findById(id).get();
            List<PostDataLogToKafkaDto.Action> actions = new ArrayList<>();
            if (Objects.nonNull(input.getType()) && !Objects.equals(oldMappingData.getType().getId(), input.getType().getId())) {
                MappingType type = mappingTypeRepository.findById(input.getType().getId()).get();
                PostDataLogToKafkaDto.Action actionType = new PostDataLogToKafkaDto.Action("lang.word.type", oldMappingData.getType().toString(), type.toString());
                actions.add(actionType);
                oldMappingData.setTransType(type);
                affectedRows.setAffectedRows(1);
            }
            if (Objects.nonNull(input.getSource()) && !Objects.equals(oldMappingData.getSource(), input.getSource())) {
                PostDataLogToKafkaDto.Action actionSource = new PostDataLogToKafkaDto.Action("lang.word.source", oldMappingData.getSource().toString(), input.getSource().toString());
                actions.add(actionSource);
                oldMappingData.setSource(input.getSource());
                affectedRows.setAffectedRows(1);
            }
            if (Objects.nonNull(input.getDest()) && !Objects.equals(oldMappingData.getDest(), input.getDest())) {
                PostDataLogToKafkaDto.Action actionDest = new PostDataLogToKafkaDto.Action("lang.word.name", oldMappingData.getDest().toString(), input.getDest().toString());
                actions.add(actionDest);
                oldMappingData.setDest(input.getDest());
                affectedRows.setAffectedRows(1);
            }
            if (Objects.nonNull(input.getStatus()) && !Objects.equals(oldMappingData.getStatus(), input.getStatus())) {
                PostDataLogToKafkaDto.Action actionStatus = new PostDataLogToKafkaDto.Action("lang.word.status", oldMappingData.getStatus().toString(), input.getStatus().toString());
                actions.add(actionStatus);
                oldMappingData.setStatus(input.getStatus());
                affectedRows.setAffectedRows(1);
            }
            if (Objects.nonNull(input.getIgnoreDeployment()) && !Objects.equals(oldMappingData.getIgnoreDeployment(), input.getIgnoreDeployment())) {
                PostDataLogToKafkaDto.Action actionIgnoreDeployment = new PostDataLogToKafkaDto.Action("lang.phrase.ignore-deployment", oldMappingData.getIgnoreDeployment().toString(), input.getIgnoreDeployment().toString());
                actions.add(actionIgnoreDeployment);
                oldMappingData.setIgnoreDeployment(input.getIgnoreDeployment());
                affectedRows.setAffectedRows(1);
            }
            if (affectedRows.getAffectedRows() == 1) {
                Date updatedDate = new Date();
                PostDataLogToKafkaDto.Action actionUpdatedDate = new PostDataLogToKafkaDto.Action("lang.phrase.updated-date", oldMappingData.getUpdatedDate().toString(), updatedDate.toString());
                actions.add(actionUpdatedDate);
                oldMappingData.setUpdatedDate(updatedDate);
                mappingDataRepository.save(oldMappingData);
                if (!actions.isEmpty()) {
                    KafkaExchange.pushLog(dataLoggerStream, oldMappingData.getId(), actions, LogType.UPDATE_MAPPING_DATA);
                }
            }
        } catch (Exception e) {
            throw new DigoHttpException(10005, HttpServletResponse.SC_BAD_REQUEST);
        }

        return affectedRows;
    }

    public AffectedRowsDto delete(ObjectId id) {
        AffectedRowsDto affectedRows = new AffectedRowsDto();
        List<PostDataLogToKafkaDto.Action> actions = new ArrayList<>();
        MappingData data = mappingDataRepository.findById(id).get();
        ObjectMapper objectMapper = new ObjectMapper();
        String dataString = null;
        try {
             dataString = objectMapper.writeValueAsString(data);
        }catch (Exception ex){
            
        }
        PostDataLogToKafkaDto.Action actionDelete = new PostDataLogToKafkaDto.Action("data", dataString, null);
        actions.add(actionDelete);
        if (mappingDataRepository.deleteMappingDataById(id) != 0) {
            affectedRows.setAffectedRows(1);
            KafkaExchange.pushLog(dataLoggerStream, id, actions, LogType.DELETE_MAPPING_DATA);
        }

        return affectedRows;
    }

    public MappingDataByIdDto getById(ObjectId id) {
        ObjectId deploymentId = Context.getDeploymentId();
        MappingDataByIdDto mappingData = mappingDataRepository.getByIdAndDeploymentId(id, deploymentId);
        if (Objects.nonNull(mappingData)) {
            return mappingData;
        } else {
            throw new DigoHttpException(11001, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private long getOffset(Pageable page) {
        return page.getPageNumber() * page.getPageSize();
    }

    public AffectedRowsDto importFromFile(MultipartFile file) {
        AffectedRowsDto resultDto = new AffectedRowsDto();
        List<MappingData> mappingDataList = new ArrayList<>();
        List<String> resultErr = new ArrayList<>();
        try {
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            // Get the first sheet
            Sheet sheet = workbook.getSheetAt(0);
            // Set row iterator
            Iterator<Row> rowIterator = sheet.rowIterator();
            // Skip the header
            rowIterator.next();
            // Reading
            int num = 0;
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                String numString = String.valueOf(row.getRowNum());
                num = Integer.parseInt(numString) + 1;
                logger.info("num " + num);
                // Colunm 2
                String typeId = row.getCell(1).getStringCellValue();
                // Colunm 3
                String sourceId = row.getCell(2).getStringCellValue();
                // Colunm 4
                String sourceName = row.getCell(3).getStringCellValue();
                // Colunm 5
                String destId = null;
                Integer destIds = null;
                try {
                    destId = row.getCell(4).getStringCellValue();
                } catch (Exception e) {
                    destIds = (int) row.getCell(4).getNumericCellValue();
                }
                if (destIds != null) {
                    destId = destIds.toString();
                }
                
                // Colunm 6
                String destName = row.getCell(5).getStringCellValue();
                // Colunm 7
                Integer status = (int) row.getCell(6).getNumericCellValue();
                // Colunm 8
                Boolean ignoreDeployment = row.getCell(7).getBooleanCellValue();
                //
                MappingData newMappingData = new MappingData();
                // Set type
                MappingType type = mappingTypeRepository.findById(new ObjectId(typeId)).get();
                newMappingData.setTransType(type);
                newMappingData.setSource(new MappingDataValue(sourceId, sourceName));
                newMappingData.setDest(new MappingDataValue(destId, destName));
                if (Objects.nonNull(status)) {
                    newMappingData.setStatus(status);
                }
                if (Objects.nonNull(ignoreDeployment)) {
                    newMappingData.setIgnoreDeployment(ignoreDeployment);
                }
                // Add to list for saving if all the column cells are valid
                newMappingData.setDeploymentId(Context.getDeploymentId());
                mappingDataList.add(newMappingData);
            }
        } catch (Exception e) {
            throw new DigoHttpException(10008, new String[]{translator.toLocale("lang.phrase.mapping-data")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        int nums = 0;
        logger.info("mappingDataList: " + mappingDataList.size());
        for (MappingData mappingData : mappingDataList) {
            nums = nums + 1;
            try {
                mappingDataRepository.save(mappingData);
            } catch (Exception e) {
                String meagger = "duplicate Rows" + nums;
                resultErr.add(meagger);
            }
            KafkaExchange.pushLog(dataLoggerStream, mappingData.getId(), null, LogType.NEW_MAPPING_DATA);
            resultDto.increase();
        }
        String errorContenPhoneNumberErr = StringUtils.join(resultErr, ", ");
        logger.info("errorContenPhoneNumberErr: " + errorContenPhoneNumberErr);
        return resultDto;
    }
    
    public MappingDataDataTypeDto getByDestId(ObjectId type, String destId) {
        ObjectId deploymentId = Context.getDeploymentId();
        MappingDataDataTypeDto res = mappingDataRepository.getByDestId(type, destId, deploymentId);
        return res;
    }

    public MappingDataDto getByDestNoDeployment(ObjectId type, String sourceId) {
        MappingDataDto res = mappingDataRepository.getByDest(type, sourceId, null);
        return res;
    }

    public MappingDataDto getBySourceNoDeployment(ObjectId type, String sourceId) {
        MappingDataDto res = mappingDataRepository.getBySource(type, sourceId, null);
        return res;
    }
    public MappingDataDataTypeDto getByDestName(ObjectId type, String destName) {
        ObjectId deploymentId = Context.getDeploymentId();
        return mappingDataRepository.getByDestName(type, destName,deploymentId);
    }

    public MappingDataDataTypeDto getByDestId(ObjectId deploymentId, ObjectId type, String destId) {
        MappingDataDataTypeDto res = mappingDataRepository.getByDestId(type, destId, deploymentId);
        return res;
    }
}
