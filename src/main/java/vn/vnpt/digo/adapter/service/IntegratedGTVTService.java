package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import java.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.BusinessRegistrationTokenDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.SIParamDto;
import vn.vnpt.digo.adapter.dto.gtvt.getBusinessGtvtDTO;
import vn.vnpt.digo.adapter.dto.lgspbogtvt.DossierLGSPGplxDto;
import vn.vnpt.digo.adapter.dto.lgspbogtvt.DossierLGSPGplxResponseDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Pageable;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 */
@Service
public class IntegratedGTVTService {

    Logger logger = LoggerFactory.getLogger(IntegratedConfigurationService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private RestTemplate restTemplate;

    @Value(value = "${digo.gplxgtvt.configid}")
    private String configid;

    @Value(value = "${digo.lgsphcm.configid}")
    private String configHCMid;

    private final ObjectId SERVICE_ID = new ObjectId("8fd6cee2346b5a0917467196");

    public String getTokenLGSPBoGTVTGplx() {
        SIParamDto params = new SIParamDto();
        params.setConfigId(new ObjectId(configid));
        IntegratedConfigurationDto config = null;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        logger.info("getConfig: ");
        System.out.println(config);

        String gatewayToken = config.getParametersValue("gateway-token");

        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
//            headers.setBasicAuth(auth);
            //Set body
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(gatewayToken,
                    HttpMethod.GET, request, Object.class);
            logger.info("Http result:");

            BusinessRegistrationTokenDto token = GsonUtils.copyObject(result.getBody(), BusinessRegistrationTokenDto.class);
            String lgspAccessToken = token.getAccess_token();
            return lgspAccessToken;
        } catch (Exception e) {
            logger.info("Error calling http: ", e.getMessage());
            throw new DigoHttpException(11003, new String[]{"get token So GTVT GPLX:", e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    public List<DossierLGSPGplxResponseDto> getListDosier(String cmnd) {
        //Get config
        SIParamDto params = new SIParamDto();
        params.setConfigId(new ObjectId(configid));
        IntegratedConfigurationDto config = null;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")},
                    HttpServletResponse.SC_NOT_FOUND);
        }
        logger.info("getConfig: ");
        System.out.println(config);
        String listDossierUrl = config.getParametersValue("listDossierUrl");
        try {
            String token = getTokenLGSPBoGTVTGplx();

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<?> request = new HttpEntity<>(headers);
//        String url = config.getParametersValue("gateway").toString()+config.getParametersValue("getListFileMsqhns").toString();
            ResponseEntity<String> result = restTemplate.exchange(listDossierUrl + "?key=" + SERVICE_ID
                            + "&cmnd=" + cmnd
                    , HttpMethod.GET, request, String.class);

            List<Map<String, Object>> listMap;
            Map<String, Object> map = new HashMap<>();
            map.put("data", result.getBody());

            Gson g = new Gson();
            DossierLGSPGplxDto[] response = g.fromJson(result.getBody(), DossierLGSPGplxDto[].class);
            List<DossierLGSPGplxResponseDto> list = new ArrayList<>();
            for (DossierLGSPGplxDto dto : response) {
                DossierLGSPGplxResponseDto dos = new DossierLGSPGplxResponseDto();
                dos.setName(dto.getHoVaTen());
                dos.setNationality(dto.getMaQuocTich());
                dos.setBirthDay(formatStringToDate(dto.getNgaySinh()));
                dos.setBirthPlace(dto.getNoiCT_MaDVHC());
                dos.setIndentify(dto.getSoCMT());
                dos.setGender(dto.getGioiTinh().equalsIgnoreCase("Nam") ? 1 : 0);
                dos.setRegisNo(dto.getMaDK());
                dos.setReceivingPlace(dto.getMaDVNhanHSo());
                dos.setReceivingDate(convertLongToStringDate(dto.getNgayNhanHSo()));
                dos.setAppointmentDate(convertLongToStringDate(dto.getNgayHenTra()));
                dos.setCreateDate(convertLongToStringDate(dto.getNgayTao()));
                dos.setUpdateDate(convertLongToStringDate(dto.getNgaySua()));
                dos.setDrivingLicense(dto.getHangGPLX());
                dos.setTypeCode(dto.getMaLoaiHs());
                dos.setTypeName(dto.getTenLoaiHs());
                dos.setStatusNo(dto.getTT_XuLy());
                dos.setStatus(dto.getTrangThaiXuLy());
                dos.setSysNo(dto.getMaHTCap());
                list.add(dos);
            }
            return list;
        } catch (Exception e) {
            throw new DigoHttpException(11003, new String[]{"get list integrate So GTVT GPLX:", e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }

    }

    public List<getBusinessGtvtDTO> getBusinessGTVT(String name, String size, String page){

        IntegratedConfigurationDto configHCM;
        configHCM = configurationService.getConfig(new ObjectId(configHCMid));
        String encodedString = getTokenGTVT(configHCM);
        String url = configHCM.getParameterValue("getUrlBusinessGTVT");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Authorization",encodedString);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("AppName","VNPT");

        if(page != null && !page.isEmpty()){
            map.add("PageNum", page);
        }

        if(size != null && !size.isEmpty()){
            map.add("PageSize",size);
        }

        if(name != null && !name.isEmpty()){
            String trimmedName = name.trim();
            map.add("TenDoanhNghiep", trimmedName);
        }


        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);
        JSONObject response = new JSONObject(restTemplate.exchange(url, HttpMethod.POST, entity, String.class).getBody());

        List<getBusinessGtvtDTO> resultList = new ArrayList<>();

        if (response.has("DataResult") && response.get("DataResult") instanceof JSONArray) {
            JSONArray dataArray = (JSONArray) response.get("DataResult");
            for (int i = 0; i < dataArray.length(); i++) {
                JSONObject businessObject = dataArray.getJSONObject(i);
                resultList.add(getBusinessGtvtDTO.mapJsonToGetBusinessGtvtDTO(businessObject));
            }
        }

        return resultList;
    }

    public List<getBusinessGtvtDTO> getInvestorsGTVT(String name, String size, String page){

        IntegratedConfigurationDto configHCM;
        configHCM = configurationService.getConfig(new ObjectId(configHCMid));
        String encodedString = getTokenGTVT(configHCM);
        String url = configHCM.getParameterValue("getUrlInvestorsGTVT");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Authorization",encodedString);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("AppName","VNPT");

        if(page != null && !page.isEmpty()){
            map.add("PageNum", page);
        }

        if(size != null && !size.isEmpty()){
            map.add("PageSize",size);
        }

        if(name != null && !name.isEmpty()){
            String trimmedName = name.trim();
            map.add("TenDoanhNghiep", trimmedName);
        }


        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);
        JSONObject response = new JSONObject(restTemplate.exchange(url, HttpMethod.POST, entity, String.class).getBody());

        List<getBusinessGtvtDTO> resultList = new ArrayList<>();

        if (response.has("DataResult") && response.get("DataResult") instanceof JSONArray) {
            JSONArray dataArray = (JSONArray) response.get("DataResult");
            for (int i = 0; i < dataArray.length(); i++) {
                JSONObject businessObject = dataArray.getJSONObject(i);
                resultList.add(getBusinessGtvtDTO.mapJsonToGetBusinessGtvtDTO(businessObject));
            }
        }
        return resultList;
    }

    public List<getBusinessGtvtDTO> getDonViThiCongGTVT(String name, String size, String page){

        IntegratedConfigurationDto configHCM;
        configHCM = configurationService.getConfig(new ObjectId(configHCMid));
        String encodedString = getTokenGTVT(configHCM);
        String url = configHCM.getParameterValue("getUrlDonViThiCongGTVT");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Authorization",encodedString);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("AppName","VNPT");

        if(page != null && !page.isEmpty()){
            map.add("PageNum", page);
        }

        if(size != null && !size.isEmpty()){
            map.add("PageSize",size);
        }

        if(name != null && !name.isEmpty()){
            String trimmedName = name.trim();
            map.add("TenDoanhNghiep", trimmedName);
        }


        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);
        JSONObject response = new JSONObject(restTemplate.exchange(url, HttpMethod.POST, entity, String.class).getBody());

        List<getBusinessGtvtDTO> resultList = new ArrayList<>();

        if (response.has("DataResult") && response.get("DataResult") instanceof JSONArray) {
            JSONArray dataArray = (JSONArray) response.get("DataResult");
            for (int i = 0; i < dataArray.length(); i++) {
                JSONObject businessObject = dataArray.getJSONObject(i);
                resultList.add(getBusinessGtvtDTO.mapJsonToGetBusinessGtvtDTO(businessObject));
            }
        }
        return resultList;
    }

    public List<getBusinessGtvtDTO> getDonViGiamSatGTVT(String name, String size, String page){

        IntegratedConfigurationDto configHCM;
        configHCM = configurationService.getConfig(new ObjectId(configHCMid));
        String encodedString = getTokenGTVT(configHCM);
        String url = configHCM.getParameterValue("getUrlDonViGiamSatGTVT");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Authorization",encodedString);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("AppName","VNPT");

        if(page != null && !page.isEmpty()){
            map.add("PageNum", page);
        }

        if(size != null && !size.isEmpty()){
            map.add("PageSize",size);
        }

        if(name != null && !name.isEmpty()){
            String trimmedName = name.trim();
            map.add("TenDoanhNghiep", trimmedName);
        }


        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);
        JSONObject response = new JSONObject(restTemplate.exchange(url, HttpMethod.POST, entity, String.class).getBody());

        List<getBusinessGtvtDTO> resultList = new ArrayList<>();

        if (response.has("DataResult") && response.get("DataResult") instanceof JSONArray) {
            JSONArray dataArray = (JSONArray) response.get("DataResult");
            for (int i = 0; i < dataArray.length(); i++) {
                JSONObject businessObject = dataArray.getJSONObject(i);
                resultList.add(getBusinessGtvtDTO.mapJsonToGetBusinessGtvtDTO(businessObject));
            }
        }
        return resultList;
    }

    public List<getBusinessGtvtDTO> getDonViTaiLapGTVT(String name, String size, String page){

        IntegratedConfigurationDto configHCM;
        configHCM = configurationService.getConfig(new ObjectId(configHCMid));
        String encodedString = getTokenGTVT(configHCM);
        String url = configHCM.getParameterValue("getUrlDonViTaiLapGTVT");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Authorization",encodedString);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("AppName","VNPT");

        if(page != null && !page.isEmpty()){
            map.add("PageNum", page);
        }

        if(size != null && !size.isEmpty()){
            map.add("PageSize",size);
        }

        if(name != null && !name.isEmpty()){
            String trimmedName = name.trim();
            map.add("TenDoanhNghiep", trimmedName);
        }


        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);
        JSONObject response = new JSONObject(restTemplate.exchange(url, HttpMethod.POST, entity, String.class).getBody());

        List<getBusinessGtvtDTO> resultList = new ArrayList<>();

        if (response.has("DataResult") && response.get("DataResult") instanceof JSONArray) {
            JSONArray dataArray = (JSONArray) response.get("DataResult");
            for (int i = 0; i < dataArray.length(); i++) {
                JSONObject businessObject = dataArray.getJSONObject(i);
                resultList.add(getBusinessGtvtDTO.mapJsonToGetBusinessGtvtDTO(businessObject));
            }
        }
        return resultList;
    }

    private String getTokenGTVT(IntegratedConfigurationDto configHCM){

        String AppName = configHCM.getParameterValue("AppNameGTVT");
        String PartnerCode = configHCM.getParameterValue("PartnerCodeGTVT");
        String SecretKey = configHCM.getParameterValue("SecretKeyGTVT");

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("AppName",AppName);
        jsonObject.put("PartnerCode",PartnerCode);
        jsonObject.put("SecretKey",SecretKey);

        String jsonString = jsonObject.toString().replaceAll(",", ",\n").replaceAll("\\{", "{\n").replaceAll("}", "\n}");

        return Base64.getEncoder().encodeToString(jsonString.getBytes());
    }

    private String convertLongToStringDate(String text) {
        String[] output = text.split("[()]");
        Long l = Long.valueOf(output[1]);
        Date date=new Date(l);
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
        String dateText = df2.format(date);
        System.out.println(dateText);
        return dateText;
    }
    private Date formatStringToDate(String text) {
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ", Locale.ENGLISH);
//        Date date = null;
//        try {
//            date = formatter.parse(text);
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        System.out.println(date.toString());
//        return date.toString();
        DateFormat sourceFormat = new SimpleDateFormat("dd/MM/yyyy");
        Date date = null;
        try {
            date = sourceFormat.parse(text);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }
}
