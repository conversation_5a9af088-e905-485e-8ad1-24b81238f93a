package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.document.ChungThucToken;
import vn.vnpt.digo.adapter.document.TokenPartner;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.dto.vnptctdt.*;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.repository.ChungThucTokenRepository;
import vn.vnpt.digo.adapter.repository.TokenPartnerRepository;
import vn.vnpt.digo.adapter.stream.AuthenticateDossierHCMProducerStream;
import vn.vnpt.digo.adapter.util.*;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.HttpServletResponse;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.sql.Timestamp;
import java.util.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import vn.vnpt.digo.adapter.document.IntegratedLogs;

/**
 *
 * <AUTHOR>
 */

@RefreshScope
@Service
public class ChungThucDienTuService {
    Logger logger = LoggerFactory.getLogger(ChungThucDienTuService.class);

    private RestTemplate restTemplate;

    @Autowired
    public ChungThucDienTuService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private IntegratedLogsService logService;

    @Autowired
    private Translator translator;

    @Autowired
    private Microservice microservice;

    @Autowired
    private Oauth2RestTemplate oauth2RestTemplate;

    @Autowired
    private ChungThucTokenRepository chungThucTokenRepository;

    @Autowired
    private TokenPartnerRepository tokenPartnerRepository;
    @Autowired
    AuthenticateDossierHCMProducerStream authenticateDossierHCMProducer;

    private final ObjectId SERVICE_ID = new ObjectId("5f7c16069abb62f511891708");

    private final Gson gson = new Gson();
    
    @Value(value = "${digo.chungthuc.skip-file-url-sign}")
    private Boolean skipFileUrlSign;
    
    @Value(value = "${digo.chungthuc.allow-next-step-if-upload-file-fail}")
    private Boolean allowNextStepIfUploadFileFail;

    @Value(value = "${digo.rest.connection.module.set-timeout}")
    private Boolean enableSetTimeOut;

    @Value(value = "${digo.chungthuc.changeCallKafkaDossierAuthen}")
    private Boolean changeCallKafkaDossierAuthen;

    @Autowired
    private MongoTemplate mongoTemplate;

    private Boolean refresh = false;

    public void setCustomTimeouts(int connectTimeout, int readTimeout) throws NoSuchAlgorithmException, KeyManagementException {
        this.restTemplate = restTemplateCustom(connectTimeout, readTimeout);
    }

    public RestTemplate restTemplateCustom(Integer restConnectTimeout, Integer restReadTimeout) throws NoSuchAlgorithmException, KeyManagementException {
        RestTemplateBuilder builder = new RestTemplateBuilder();
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }

                    @Override
                    public void checkClientTrusted(
                            java.security.cert.X509Certificate[] certs, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(
                            java.security.cert.X509Certificate[] certs, String authType) {
                    }
                }
        };
        SSLContext sslContext = SSLContext.getInstance("SSL");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLContext(sslContext)
                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                .build();
        HttpComponentsClientHttpRequestFactory customRequestFactory = new HttpComponentsClientHttpRequestFactory();
        customRequestFactory.setHttpClient(httpClient);
        //set timeout
        logger.info("change time out ChungThucDienTuService: restConnectTimeout=" + restConnectTimeout + " ,restReadTimeout=" + restReadTimeout);
        customRequestFactory.setConnectTimeout(restConnectTimeout);
        customRequestFactory.setConnectionRequestTimeout(restConnectTimeout);
        customRequestFactory.setReadTimeout(restReadTimeout);

        return builder.requestFactory(() -> customRequestFactory).build();
        //return builder.build();
    }

    private String getTokenCTDT(IntegratedConfigurationDto config) {
        String token = "";
        TokenPartner tokenPartner = this.getValidToken(new Date());
        if(Objects.nonNull(tokenPartner) && this.refresh == false){
            token = tokenPartner.getToken();
        }else{
            try {
                // Get config
                String endpoint = config.getParametersValue("api_get_token").toString();
                String username = config.getParametersValue("account_username");
                String password = config.getParametersValue("account_password");

                TokenCTDTRequestDto body = new TokenCTDTRequestDto(username, password);

                // Call
                TokenCTDTResponseDto ret = MicroserviceExchange.postJsonNoAuth(restTemplate, endpoint, body, TokenCTDTResponseDto.class);
                logger.info(ret.toString());
                token = ret.getAccess_token();
                if(token != null && token != ""){
                    Date currentDate = new Date();

                    tokenPartner = new TokenPartner();
                    tokenPartner.setType(1, "initToCTDT", endpoint);
                    tokenPartner.setAccountId(Context.getAccountId());
                    tokenPartner.setUsername(Context.getUserFullname());
                    tokenPartner.setUserId(Context.getUserId());
                    tokenPartner.setCreatedDate(currentDate);
                    tokenPartner.setUpdatedDate(currentDate);
                    tokenPartner.setExpiryDate(new Date(currentDate.getTime() + (ret.getExp()-120) * 1000L));
                    tokenPartner.setToken(token);
                    tokenPartnerRepository.save(tokenPartner);
                    this.refresh = false;
                }
            } catch (Exception e) {
                logger.info(e.toString());
            }
        }

        return token;
    }

    private TokenPartner getValidToken(Date curentDate){
        Query query = new Query();
        query.addCriteria(Criteria.where("expiryDate").gte(curentDate));
        query.addCriteria(Criteria.where("type.id").is(1));
        query.with(Sort.by(Sort.Direction.DESC, "expiryDate"));

        TokenPartner tokenPartner = mongoTemplate.findOne(query, TokenPartner.class);

        if(Objects.isNull(tokenPartner)){
            return null;
        }

        return tokenPartner;
    }

    private ResponseEntity<String> callApi(IntegratedConfigurationDto config, HttpEntity<?> request, String keyParams) {
        return restTemplate.exchange(
                config.getParametersValue(keyParams).toString(),
                HttpMethod.POST, request, String.class);
    }

    private CTDTResponseDto handleApiResponse(ResponseEntity<String> result, IntegratedConfigurationDto config, InitRequestDto body) {
        CTDTResponseDto res = gson.fromJson(result.getBody(), CTDTResponseDto.class);
        logService.save(config, new IdCodeNameSimpleDto(body.getIdHoSo(), body.getMaHoSo(), body.getMaTTHC()), 1, res.toString(), GsonUtils.getJson(body));
        return res;
    }

    private CTDTResponseDto handleApiError(String responseBody, IntegratedConfigurationDto config, InitRequestDto body){
        CTDTResponseDto errorResponse = gson.fromJson(responseBody, CTDTResponseDto.class);
        logService.save(config, new IdCodeNameSimpleDto(body.getIdHoSo(),body.getMaHoSo(),body.getMaTTHC()), 0, errorResponse.toString(), GsonUtils.getJson(body));
        return errorResponse;
    }

    private CTDTResponseDto handleApiUpdateFeeError(String responseBody, IntegratedConfigurationDto config, UpdateFeeRequestDto body){
        CTDTResponseDto errorResponse = gson.fromJson(responseBody, CTDTResponseDto.class);
        logService.save(config, new IdCodeNameSimpleDto(body.getIdHoSo(),body.getMaHoSo(),body.getMaTTHC()), 0, errorResponse.toString(), GsonUtils.getJson(body));
        return errorResponse;
    }

    private void updateFeePadman(UpdateFeeRequestDto body){
        try {
            String URL = microservice.padmanUri("dossier-auth-result").toUriString() + "/--update-is-paid";
//                    String URL = "http://localhost:8081/dossier-auth-result" + "/--update-is-paid";
            UpdateIsPaidDto updateIsPaidDto = new UpdateIsPaidDto(body.getId(), true);
            MicroserviceExchange.putJson(this.restTemplate, URL, updateIsPaidDto, AffectedRowsDto.class);
        } catch (Exception e) {
            logger.info("Save fileSignInfoDto: " + e.toString());
        }
    }

    public CTDTResponseDto initToCTDT(IntegrationParamsDto params, InitRequestDto body) {
        // Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.SERVICE_ID);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        if(body.getChungThucDienTu().isEmpty() && body.getChungThucGiay().isEmpty()){
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NO_CONTENT);
        }

        try{
            if(enableSetTimeOut && Objects.nonNull(config.getParametersValue("enable-timeout")) && (Boolean) config.getParameterValue("enable-timeout") == true){
                this.setCustomTimeouts((Integer) config.getParameterValue("connect-timeout"), (Integer) config.getParameterValue("read-timeout"));
            }
        }catch (Exception e){
        }

        String token = this.getTokenCTDT(config);
        if (Objects.isNull(token)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.token") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        // Create header
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        try {
            // Call
            ResponseEntity<String> result = callApi(config, request, "api_init");
            return handleApiResponse(result, config, body);
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            if(ex.getStatusCode() == HttpStatus.UNAUTHORIZED){
                this.refresh = true;
                token = this.getTokenCTDT(config);
                headers.set("Authorization", token);
                request = new HttpEntity<>(body, headers);

                try{
                    ResponseEntity<String> result = callApi(config, request, "api_init");
                    return handleApiResponse(result, config, body);
                }catch (HttpClientErrorException | HttpServerErrorException e) {
                    String responseBody = e.getResponseBodyAsString();
                    return handleApiError(responseBody, config, body);
                }
            }else{
                String responseBody = ex.getResponseBodyAsString();
                return handleApiError(responseBody, config, body);
            }
        } catch (Exception e) {
            logService.save(config, new IdCodeNameSimpleDto(body.getIdHoSo(),body.getMaHoSo(),body.getMaTTHC()), 0, e.getMessage(), GsonUtils.getJson(body));
            return new CTDTResponseDto("-1", e.getMessage());
        }
    }

    public CTDTResponseDto updateFeeToCTDT(IntegrationParamsDto params, UpdateFeeRequestDto body) {
        // Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.SERVICE_ID);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String token = this.getTokenCTDT(config);
        if (Objects.isNull(token)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.token") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        // Create header
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        try {
            // Call
            ResponseEntity<String> result = callApi(config, request, "api_update_fee");
            CTDTResponseDto res = gson.fromJson(result.getBody(), CTDTResponseDto.class);

            if ("0".equals(res.getErr_code())){
                this.updateFeePadman(body);
            }
            return res;
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            if(ex.getStatusCode() == HttpStatus.UNAUTHORIZED){
                this.refresh = true;
                token = this.getTokenCTDT(config);
                headers.set("Authorization", token);
                request = new HttpEntity<>(body, headers);

                try{
                    ResponseEntity<String> result = callApi(config, request, "api_update_fee");
                    CTDTResponseDto res = gson.fromJson(result.getBody(), CTDTResponseDto.class);
                    if ("0".equals(res.getErr_code())){
                        this.updateFeePadman(body);
                    }
                    return res;
                }catch (HttpClientErrorException | HttpServerErrorException e) {
                    String responseBody = e.getResponseBodyAsString();
                    return handleApiUpdateFeeError(responseBody, config, body);
                }
            }else{
                String responseBody = ex.getResponseBodyAsString();
                return handleApiUpdateFeeError(responseBody, config, body);
            }
        } catch (Exception e) {
            logService.save(config, new IdCodeNameSimpleDto(body.getIdHoSo(),body.getMaHoSo(),body.getMaTTHC()), 0, e.getMessage(), GsonUtils.getJson(body));
            return new CTDTResponseDto("-1", e.getMessage());
        }
    }

    public ResponseEntity<byte[]> downloadFileToCTDT(IntegrationParamsDto params, String url, String resultId){
        // Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.SERVICE_ID);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String token = this.getTokenCTDT(config);
        if (Objects.isNull(token)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.token") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String api_download_file = config.getParametersValue("api_download_file").toString();
        // Create header
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> request = new HttpEntity<>(headers);
        // get file from ctdt
        List<ResponseEntity<byte[]>> listByteFileCTDT = new ArrayList<>();
        if(Objects.nonNull(url) && Objects.nonNull(api_download_file)){
            try {
                // Call
                ResponseEntity<byte[]> result = restTemplate.exchange(
                        api_download_file + "?url=" + url,
                        HttpMethod.GET, request, byte[].class);
                // save to fileman
                listByteFileCTDT.add(result);
                List<FileSignInfoDto> fileSignInfoDto = this.uploadToFileman(listByteFileCTDT);
                // Update fileSignInfoDto
                if (fileSignInfoDto.size() > 0) {
                    try {
                        String URL = microservice.padmanUri("dossier-auth-result").toUriString() + "/--update-file-sign/" + resultId + "/" + url;
                        //String URL = "http://localhost:8081/dossier-auth-result" + "/--update-file-sign/" + resultId + "/" + url;
                        MicroserviceExchange.putJson(this.restTemplate, URL, fileSignInfoDto.get(0), AffectedRowsDto.class);
                        return result;
                    } catch (Exception e) {
                        logger.info("Save fileSignInfoDto: " + e.toString());
                    }
                }
                return result;
            } catch (Exception e) {
                logger.info(e.toString());
                throw new DigoHttpException(11001, HttpServletResponse.SC_NOT_FOUND);
            }
        }
        return null;
    }

    public ResponseEntity<byte[]> previewFileToCTDT(IntegrationParamsDto params, String url){
        // Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.SERVICE_ID);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String token = this.getTokenCTDT(config);
        if (Objects.isNull(token)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.token") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        // Create header
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> request = new HttpEntity<>(headers);
        try {
            // Call
            ResponseEntity<byte[]> result = restTemplate.exchange(
                    config.getParametersValue("api_preview_file").toString() + "?url=" + url,
                    HttpMethod.GET, request, byte[].class);
            return result;
        } catch (Exception e) {
            logger.info(e.toString());
            throw new DigoHttpException(11001, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public TokenIgateResponseDto getTokenIgate(String authorization) {
        // Get config
        IntegratedConfigurationDto config = configurationService.searchByServiceId(this.SERVICE_ID);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String endpoint = config.getParametersValue("igate_url_get_token").toString();
        String grant_type = config.getParametersValue("igate_grant_type").toString();
        String client_id = config.getParametersValue("igate_client_id").toString();
        String client_secret = config.getParametersValue("igate_client_secret").toString();
        String username = "";
        String password = "";

        // Authorization:  Basic base64_encode(username:password)
        authorization = authorization.replaceAll("Basic ", "");
        byte[] decodedBytes = Base64.getDecoder().decode(authorization);
        String decodedString = new String(decodedBytes);
        String[] parts = decodedString.split(":");
        if (parts.length == 2) {
            username = parts[0]; // Phần tử đầu tiên là username
            password = parts[1]; // Phần tử thứ hai là password
        } else {
            throw new DigoHttpException(10000, new String[]{translator.toLocale("lang.word.username")}, HttpServletResponse.SC_NOT_FOUND);
        }

        // Create header
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // Create request with body
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        requestBody.set("grant_type", grant_type);
        requestBody.set("client_id", client_id);
        requestBody.set("client_secret", client_secret);
        requestBody.set("username", username);
        requestBody.set("password", password);

        // Call
        HttpEntity<?> request = new HttpEntity<>(requestBody, headers);
        ResponseEntity<?> response;
        TokenIgateResponseDto result = new TokenIgateResponseDto();
        try {
            response = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                // Get reponse body and map data
                Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
                result.setToken((String)responseBody.get("access_token"));
                result.setExp((int)responseBody.get("expires_in"));
            }
        } catch(Exception e) {
            logger.info(e.toString());
        }

        return result;
    }

    public CTDTResponseDto verify(VerifyRequestDto body) {
        // validate
        String username = body.getUsername();
        String ig_session = body.getIg_session();
        if (Objects.isNull(username) || Objects.isNull(ig_session)){
            return new CTDTResponseDto("-1", "Yêu cầu nhập đầy đủ dữ liệu");
        }

        // get token chung thuc
        ChungThucToken chungThucToken;
        try {
            UUID uuid = UUID.fromString(ig_session);
            Optional<ChungThucToken> search = Optional.ofNullable(chungThucTokenRepository.findByUuid(uuid));

            if (search.isEmpty()){
                return new CTDTResponseDto("-1", "ig_session không chính xác");
            } else {
                chungThucToken = search.get();
            }
        } catch (Exception e){
            logger.info(e.getMessage());
            return new CTDTResponseDto("-1", "ig_session không đúng định dạng");
        }

        // Get config
        IntegratedConfigurationDto config = configurationService.searchByServiceId(this.SERVICE_ID);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String endpoint = config.getParametersValue("igate_url_verify").toString();

        // Create header
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(chungThucToken.getToken());

        HttpEntity<?> request = new HttpEntity<>(null, headers);
        ResponseEntity<?> response;
        try {
            response = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // Get reponse body and map data
            Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
            String preferred_username = (String)responseBody.get("preferred_username");
            // return result
            if (!preferred_username.equals(username)) {
                return new CTDTResponseDto("-1", "Tài khoản không chính xác");
            } else {
                return new CTDTResponseDto("1", "Tài khoản đang đăng nhập");
            }
        } catch (HttpStatusCodeException e) {
            logger.info(e.getMessage());
            return new CTDTResponseDto("0", "Tài khoản đã đăng xuất");
        } catch(Exception e) {
            logger.info(e.toString());
            return new CTDTResponseDto("-1", "Đã có lỗi xảy ra");
        }
    }

    public CTDTResponseDto result(ResultRequestDto body) {
        if(changeCallKafkaDossierAuthen){
            try {
                boolean result = authenticateDossierHCMProducer.push_from_authensystem_hcm(body);
                if(result) {
                    return new CTDTResponseDto("0", "Thành công");
                }else{
                    return new CTDTResponseDto("-1", "Thất bại push message vào kafka");
                }
            }catch (Exception e){
                logger.error("[Kafka-Authen-Dossier-Result]: ERROR {}", e.getStackTrace(), e);
                return new CTDTResponseDto("-1", e.toString());
            }
        }else {
            ResultRequestDto bodyOrigin = new ResultRequestDto();
            bodyOrigin = body;
            IntegratedConfigurationDto config = null;
            if (!skipFileUrlSign) {
                config = configurationService.searchByServiceId(this.SERVICE_ID);
                if (Objects.isNull(config)) {
                    throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")},
                            HttpServletResponse.SC_NOT_FOUND);
                }
            }
            try {
                if (!skipFileUrlSign) {
                    // Save file
                    body = this.saveUrlSign(body);
                }
                if (body == null) {
                    if (allowNextStepIfUploadFileFail) {
                        String URL = microservice.padmanUri("dossier-auth-result").toUriString();
                        PostResponseDto res = MicroserviceExchange.postJson(this.restTemplate, URL, bodyOrigin, PostResponseDto.class);
                        if (Objects.nonNull(res.getId())) {
                            return new CTDTResponseDto("0", "Thành công");
                        } else {
                            logService.save(config, new IdCodeNameSimpleDto(null, body.getMaHoSo(), body.getMaTTHC()), -1, "Thất bại", GsonUtils.getJson(body));
                            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")},
                                    HttpServletResponse.SC_ACCEPTED);
                        }
                    } else {
                        return new CTDTResponseDto("-1", "Thất bại. Đã có lỗi xảy ra!");
                    }
                }
                // Get URL
                String URL = microservice.padmanUri("dossier-auth-result").toUriString();
                //String URL = "http://localhost:8081/dossier-auth-result";
                // Call
                PostResponseDto res = MicroserviceExchange.postJson(this.restTemplate, URL, body, PostResponseDto.class);
                // return result
                if (Objects.nonNull(res.getId())) {
                    try {
                        logService.save(config, new IdCodeNameSimpleDto(null, body.getMaHoSo(), body.getMaTTHC()), 1, "Thành công", GsonUtils.getJson(body));
                    } catch (Exception e) {

                    }
                    return new CTDTResponseDto("0", "Thành công");
                } else {
                    if (!skipFileUrlSign) {
                        logService.save(config, new IdCodeNameSimpleDto(null, body.getMaHoSo(), body.getMaTTHC()), -1, "Thất bại", GsonUtils.getJson(body));
                        throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")},
                                HttpServletResponse.SC_ACCEPTED);
                    }
                    return new CTDTResponseDto("-1", "Thất bại");
                }
            } catch (Exception ex) {
                if (!skipFileUrlSign) {
                    logService.save(config, new IdCodeNameSimpleDto(null, body.getMaHoSo(), body.getMaTTHC()), -1, ex.getMessage(), GsonUtils.getJson(body));
                    throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")},
                            HttpServletResponse.SC_NOT_ACCEPTABLE);
                }
                logger.info(ex.toString());
                return new CTDTResponseDto("-1", ex.toString());
            }
        }
    }

    public ResultRequestDto saveUrlSign(ResultRequestDto body) throws JsonProcessingException {
        // Get config
        IntegratedConfigurationDto config = configurationService.searchByServiceId(this.SERVICE_ID);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String token = this.getTokenCTDT(config);
        if (Objects.isNull(token)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.token") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String api_download_file = config.getParametersValue("api_download_file").toString();
        // Create header
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> request = new HttpEntity<>(headers);
        // get file from ctdt
        List<ResponseEntity<byte[]>> listByteFileCTDT = new ArrayList<>();
        // chung thuc dien tu
        for (var giayTo : body.getChungThucDienTu()){
            try {
                // Call
                if (Objects.nonNull(giayTo.getUrlSign())) {
                    ResponseEntity<byte[]> fileByte = restTemplate.exchange(
                            api_download_file + "?url=" + giayTo.getUrlSign().toString(),
                            HttpMethod.GET, request, byte[].class);
                    listByteFileCTDT.add(fileByte);
                    giayTo.setStatusGetFileSign(true);
                }
            } catch (Exception e) {
                logger.info(e.toString());
                giayTo.setStatusGetFileSign(false);
                listByteFileCTDT.add(null);
            }
        }
        // chung thuc giay
        for (var giayTo : body.getChungThucGiay()){
            try {
                // Call
                if (Objects.nonNull(giayTo.getUrlSign())) {
                    ResponseEntity<byte[]> fileByte = restTemplate.exchange(
                            api_download_file + "?url=" + giayTo.getUrlSign().toString(),
                            HttpMethod.GET, request, byte[].class);
                    listByteFileCTDT.add(fileByte);
                    giayTo.setStatusGetFileSign(true);
                }
            } catch (Exception e) {
                logger.info(e.toString());
                giayTo.setStatusGetFileSign(false);
                listByteFileCTDT.add(null);
            }
        }
        List<FileSignInfoDto> uploadToFilemanResult = new ArrayList<>();
        // upload file to Fileman
//        if(listByteFileCTDT.size() <= 10){
//             uploadToFilemanResult = this.uploadToFileman(listByteFileCTDT);
//            if(uploadToFilemanResult == null){
//                return null;
//            }
//        }else{
            for(ResponseEntity<byte[]> byteFileCTDT:listByteFileCTDT){
                List<ResponseEntity<byte[]>> listByteFileCTDTOneFile = new ArrayList<>();
                listByteFileCTDTOneFile.add(byteFileCTDT);
                List<FileSignInfoDto> uploadToFilemanResultOneFile = this.uploadToFileman(listByteFileCTDTOneFile);
                if(uploadToFilemanResultOneFile == null){
                    return null;
                }
                uploadToFilemanResult.addAll(uploadToFilemanResultOneFile);
            }
//        }
        // set fileSignInfo
        int index = 0;
        for (var giayTo : body.getChungThucDienTu()){
            if (Objects.nonNull(giayTo.getUrlSign()) && giayTo.getStatusGetFileSign() == true) {
                giayTo.setFileSignInfo(uploadToFilemanResult.get(index));
                index++;
            }
        }
        for (var giayTo : body.getChungThucGiay()){
            if (Objects.nonNull(giayTo.getUrlSign()) && giayTo.getStatusGetFileSign() == true) {
                giayTo.setFileSignInfo(uploadToFilemanResult.get(index));
                index++;
            }
        }
        // return
        return body;
    }

    private List<FileSignInfoDto> uploadToFileman(List<ResponseEntity<byte[]>> files) throws JsonProcessingException {
        String URL = microservice.filemanUri("file/--multiple").toUriString();
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        for (var file : files){
            if (file != null) {
                ByteArrayResource byteArrayResource = new ByteArrayResource(file.getBody()){
                    @Override
                    public String getFilename() {
                        return file.getHeaders().getContentDisposition().getFilename();
                    }
                };
                body.add("files", byteArrayResource);
            }
        }
        try{
            RestTemplate rest = oauth2RestTemplate.getOAuth2RestTemplate();
            FileSignInfoDto[] fileSignInfoDtos = MicroserviceExchange.postMultipartNoAuth(rest, URL, body, FileSignInfoDto[].class);
            if(Objects.isNull(fileSignInfoDtos)){
                logger.info("ERROR function uploadToFileman (ChungThucDienTuService padman): Không thể upload file");
                return null;
            }
            //ObjectMapper mapper = new ObjectMapper();
            List<FileSignInfoDto> data = Arrays.asList(fileSignInfoDtos);
            return data;
        }catch(Exception e){
            logger.info("ERROR function uploadToFileman (ChungThucDienTuService padman):");
            logger.info(e.toString());
            return null;
        }
    }

    public FileBase64ResponseDto getFile(String file) {
        try{
            // Get URL
            String URL = microservice.filemanUri("file/--base64").toUriString();
            URL += "?ids=" + file;
            // Call
            String jsonString = MicroserviceExchange.get(this.restTemplate, URL, String.class);
            // map data
            ObjectMapper mapper = new ObjectMapper();
            List<Base64Dto> data = mapper.readValue(jsonString, new TypeReference<List<Base64Dto>>(){});
            // return result
            if (data.size() > 0) {
                return new FileBase64ResponseDto(1, data.get(0).getBase64());
            } else {
                return new FileBase64ResponseDto(0, "");
            }
        } catch (Exception ex){
            logger.info(ex.toString());
            return new FileBase64ResponseDto(-1, ex.toString());
        }
    }

    public ResponseEntity<Object> viewFile(String fileId) {
        try {
            // Get URL
            UriComponentsBuilder fileBuilder = microservice.filemanUri("file/" + fileId);
            UriComponents fileBuilderType = fileBuilder.encode().build();
            // Call
            byte[] bytes = MicroserviceExchange.getFile(restTemplate, fileBuilderType.toUriString());
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            String filename = "" + timestamp.getTime() + ".pdf";
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                    .body(bytes);
        } catch (Exception e) {
            logger.info(e.toString());
            throw new DigoHttpException(11001, HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    public CTDTResponseDto resyncGetResultAuthen(String dossierId, String dossierCode, int status){
        try{
            if(dossierId.length() > 20 || dossierCode.length() > 8){
                ObjectId id = null;
                if(Objects.nonNull(dossierId) && dossierId.length() > 20){
                    System.out.println("vô đây dossierId: "+dossierId);
                    id = new ObjectId(dossierId);
                }                
                if(Objects.isNull(status)){
                    status = -1;
                }
                List<IntegratedLogs> logs = logService.getLogsAuthen(this.SERVICE_ID,null,id,dossierCode,status, null);
                String data = logs.get(logs.size()-1).getData();
                if(Objects.nonNull(data)){
                    try{
                        ResultRequestDto body = GsonUtils.getObjectMapper().readValue(data, ResultRequestDto.class);
                        CTDTResponseDto result = this.result(body);
                        return result;
                    }catch(Exception ex){
                        return new CTDTResponseDto("0", ex.toString());
                    }
                }
                return new CTDTResponseDto("0","Không tìm thấy logs hoặc data 0 null!");
            }else{
                return new CTDTResponseDto("0","Không có thông tin hồ sơ!");
            }
        }catch(Exception e){
            return new CTDTResponseDto("0",e.toString());
        }
    }
}
