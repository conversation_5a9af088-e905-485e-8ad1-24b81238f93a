package vn.vnpt.digo.adapter.service;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;
import vn.vnpt.digo.adapter.dto.AffectedMessageDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.SidNameDto;
import vn.vnpt.digo.adapter.dto.ag_esb.civilstatus.*;
import vn.vnpt.digo.adapter.dto.event_log.PostEventLogDto;
import vn.vnpt.digo.adapter.dto.minhtue.TokenResDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Translator;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
@Service
public class AGGHttpAgesbService {
    Logger logger = LoggerFactory.getLogger(AGGHttpAgesbService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private IntegratedConfigurationService configurationService;

    IntegratedConfigurationDto config;

    @Autowired
    private EventLogService eventLogService;

    public CivilDossierResDto writeLogHTTP(HttpServletRequest request, CivilDossierAGESBDto body, String code)  {
        PostEventLogDto event = new PostEventLogDto();
        String inputXML=null;
        StringBuilder stringBuilder =new StringBuilder();
        event.setRequestAdapter(request, body, "ad");
        event.setServiceId(body.getConfigId());
        event.setKey(new SidNameDto("Code", body.getMaHoSo()));
        try {
            CivilDossierResDto response;
            response = registerCivil(body);
            inputXML=response.getInputXml();
            event.setRequestAdapter(request, response.getInputXml(), "ad");
            stringBuilder.append("Trạng thái liên thông hộ tịch tư pháp:(\n");
            stringBuilder.append("Trạng thái liên thông: ").append(response.getStatusDescription()).append(",\n");
            if(response.getStatus()==1)
            {
                stringBuilder.append("Mã trạng thái phản hồi: ").append(response.getStatus()).append(",\n");
                stringBuilder.append("Nội dung phản hồi: ").append(response.getValue()).append(")\n");
                event.setStatus(true);
            }
            else if(response.getStatus()==-1)
            {
                stringBuilder.append("Mã lỗi: ").append(response.getErrorCode()).append(",\n");
                stringBuilder.append("Mô tả lỗi: ").append(response.getErrorDescription()).append(")\n");
                event.setStatus(false);
            }
            event.setErrMsg(stringBuilder.toString());
            eventLogService.addNewAGG(event);
            logger.info("DIGO-Response: " + response);
            return response;
        } catch (Exception e) {
            event.setRequestAdapter(request, inputXML != null ? inputXML: body, "ad");
            event.setStatus(false);
            stringBuilder.append("Trạng thái liên thông hộ tịch tư pháp:(\n");
            stringBuilder.append("Trạng thái liên thông: ").append("Liên thông hộ tịch thất bại!").append(",\n");
            stringBuilder.append("Mã lỗi: ").append(500).append(",\n");
            stringBuilder.append("Mô tả lỗi: ").append(e.getMessage()).append(")\n");
            event.setErrMsg(stringBuilder.toString());
            eventLogService.addNewAGG(event);
            Map<String, String> map = new HashMap<>();
            map.put("statusMessage", "Có lỗi khi gửi sang trục AG ESB!");
            map.put("statusCode", "-9999");
            updateDossierStatus(map,code);
            return new CivilDossierResDto();
        }
    }


    public CivilDossierResDto registerCivil(CivilDossierAGESBDto req) throws Exception
    {
        logger.info("---------------------------START_GET_REGISTER_CIVIL_AG_ESB---------------------------");
        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String registerCivilUrl  = config.getParametersValue("registerUrl");
        TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);
        return dangKyHoTich(registerCivilUrl,  token.getAccessToken(), req);
    }

    private CivilDossierResDto dangKyHoTich(String apiUrl, String access_token, CivilDossierAGESBDto dangKyHoTich) throws Exception {
            CivilDossierResDto objreturn = new CivilDossierResDto();
            URL url = new URL(apiUrl);
            String auth = "Bearer " + access_token;
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            configureConnection(connection,auth);
            String xmlObject = buildXmlString(dangKyHoTich);
            String formatXml=formatXml(xmlObject);
            objreturn.setInputXml(formatXml);
            sendRequest(connection, formatXml);
            String responsedContent = readResponse(connection);
            String formatXMLResonse=formatXml(responsedContent);
            processXmlResponse(formatXMLResonse, objreturn, dangKyHoTich);
        return objreturn;
    }
    public TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) throws Exception {
        String strConsumer = consumerKey + ":" + consumerSecret;
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(tokenUrl);
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();
        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(auth);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST, request, Object.class);
            logger.info("Http result:");
            System.out.println(result);
            TokenResDto token = GsonUtils.copyObject(result.getBody(), TokenResDto.class);
            return token;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }


    private void configureConnection(HttpURLConnection connection,String auth) throws ProtocolException {
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "text/xml");
        connection.setRequestProperty("Accept-Charset", "UTF-8");
        connection.setRequestProperty("SOAPAction", "http://angiang.gov.vn/operation/agesb/GuiHoSoMoi");
        connection.setRequestProperty("Authorization", auth);
        connection.setDoOutput(true);
        connection.setDoInput(true);
    }

    private String buildXmlString(CivilDossierAGESBDto dangKyHoTich) throws Exception {
        String moduleValue = dangKyHoTich.getModule();
        String maDonViGui = dangKyHoTich.getMaDonViGui();
        String dataMappingEgency=null;
        if(dangKyHoTich.getListOrganId().containsKey(maDonViGui))
        {
            dataMappingEgency=dangKyHoTich.getListOrganId().get(maDonViGui);
        }
        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
        Document doc = dBuilder.newDocument();
        Element envelope = doc.createElement("soapenv:Envelope");
        envelope.setAttribute("xmlns:soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
        envelope.setAttribute("xmlns:edx", "http://angiang.gov.vn/schemas/agesb/edXML");
        envelope.setAttribute("xmlns:ages", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        envelope.setAttribute("xmlns:oneg", "http://angiang.gov.vn/schemas/agesb/onegateXML");
        doc.appendChild(envelope);
        Element header = doc.createElement("soapenv:Header");
        envelope.appendChild(header);
        Element messageHeader = doc.createElement("ns3:MessageHeader");
        messageHeader.setAttribute("xmlns:ns3", "http://angiang.gov.vn/schemas/agesb/edXML");
        header.appendChild(messageHeader);
        Element from = doc.createElement("ns3:From");
        messageHeader.appendChild(from);
        Element organId = doc.createElement("ns3:OrganId");
        organId.appendChild(doc.createTextNode( getValueOrDefault(dataMappingEgency,"rỗng")));
        from.appendChild(organId);
        Element fieldCode = doc.createElement("ns2:FieldCode");
        fieldCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        fieldCode.appendChild(doc.createTextNode(dangKyHoTich.getFiledcodeHTTP()));
        messageHeader.appendChild(fieldCode);
        if(dangKyHoTich.getProcesscodeHTTP().containsKey(moduleValue))
        {
            Element processCode = doc.createElement("ns2:ProcessCode");
            processCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
            processCode.appendChild(doc.createTextNode(dangKyHoTich.getProcesscodeHTTP().get(moduleValue)));
            messageHeader.appendChild(processCode);
        }
        Element body = doc.createElement("soapenv:Body");
        envelope.appendChild(body);
        Element messageBody = doc.createElement("edx:MessageBody");
        body.appendChild(messageBody);
        Element businessData = doc.createElement("ns2:BusinessData");
        businessData.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        businessData.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
        businessData.setAttribute("xsi:type", "ns2:BusinessData");
        messageBody.appendChild(businessData);
        Element thongTinHoSo = doc.createElement("ns1:ThongTinHoSo");
        thongTinHoSo.setAttribute("xmlns:ns1", "http://angiang.gov.vn/schemas/agesb/onegateXML");
        businessData.appendChild(thongTinHoSo);
        // Thêm các thành phần của XML tương ứng với thông tin hồ sơ
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:MaBNHS", getValueOrDefault(dangKyHoTich.getMaHoSo(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenToChuc", getValueOrDefault(dangKyHoTich.getTenToChuc(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenNguoiNop", getValueOrDefault(dangKyHoTich.getTenNguoiNop(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DiaChiNguoiNop", getValueOrDefault(dangKyHoTich.getDiaChiNguoiNop(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:SoCMND", getValueOrDefault(dangKyHoTich.getSoCMND(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DienThoai", getValueOrDefault(dangKyHoTich.getDienThoai(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TenDVCong", getValueOrDefault(dangKyHoTich.getTenDichVuCong(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:NgayNhanHS", getValueOrDefaultDate(dangKyHoTich.getNgayNhanHS())));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:NgayHenTraHS", getValueOrDefaultDate(dangKyHoTich.getNgayHenTraHS())));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TinhTrangHS", getValueOrDefault(dangKyHoTich.getTinhTrangHS(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:TrangThaiXuLy", getValueOrDefaultInteger(dangKyHoTich.getTrangThaiXuLy()).toString()));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:PhongBanXuLy", getValueOrDefault(dangKyHoTich.getPhongBanXuLy(), "rỗng")));
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:CVXuLy", getValueOrDefault(dangKyHoTich.getChuyenVienXuLy(), "rỗng")));
        ObjectMapper mapper = new ObjectMapper();
        String jsonResult = mapper.writeValueAsString(dangKyHoTich.getDataEform());
        // Thêm phần DuLieuChuyenNganh
        String duLieuChuyenNganhContent = "{\n" +
                "\"maDonVi\" : \""+dataMappingEgency+"\",\n" +
                "\"module\" : \""+dangKyHoTich.getModule()+"\",\n" +
                "\"maHoSo\" : \""+dangKyHoTich.getMaHoSo()+"\",\n" +
                "\"ngayTiepNhan\" : \""+getValueOrDefaultDate(dangKyHoTich.getNgayTiepNhan())+"\",\n" +
                "\"data\" :  \"" + dangKyHoTich.getDataEform() + "\"\n" +
                "}";
        thongTinHoSo.appendChild(createElementWithTextContent(doc, "ns1:DuLieuChuyenNganh", duLieuChuyenNganhContent));
        StringWriter stringWriter = new StringWriter();
        // Chuyển đổi DOM thành chuỗi
        TransformerFactory.newInstance().newTransformer().transform(new javax.xml.transform.dom.DOMSource(doc), new StreamResult(stringWriter));
        return stringWriter.toString().replace("&gt;", ">").replace("&lt;", "<");
    }

    private String getValueOrDefault(String value, String defaultValue) {
        return (value != null) ? value : defaultValue;
    }
    private Integer getValueOrDefaultInteger(Integer value) {
        return (value != null) ? value : 0;
    }
    private static String getValueOrDefaultDate(Date value) {
        if (value != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return dateFormat.format(value);
        } else {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return dateFormat.format(new Date());
        }
    }
    private String formatXml(String unformattedXml) throws Exception {
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        StreamSource source = new StreamSource(new StringReader(unformattedXml));
        StringWriter resultWriter = new StringWriter();
        StreamResult result = new StreamResult(resultWriter);
        transformer.transform(source, result);
        return resultWriter.toString();
    }
    private Element createElementWithTextContent(Document doc, String elementName, String textContent) {
        Element element = doc.createElement(elementName);
        element.appendChild(doc.createTextNode(textContent));
        return element;
    }

    private void sendRequest(HttpURLConnection connection, String jsonObject) throws IOException {
        try (OutputStream outStream = connection.getOutputStream()) {
            outStream.write(jsonObject.getBytes());
        }
    }

    private String readResponse(HttpURLConnection connection) throws IOException {
        InputStream inputStream;
        try {
            inputStream = connection.getInputStream();
        } catch (IOException e) {
            inputStream = connection.getErrorStream();
        }
        if (inputStream != null) {
            try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                return bufferedReader.lines().collect(StringBuilder::new, StringBuilder::append, StringBuilder::append).toString();
            }
        } else {
            return "No response received";
        }
    }

    public AffectedMessageDto updateDossierStatus(Map response , String code){
        String updateUrl = microservice.padmanUri("/agesb-http/" + code + "/status-agesb-http").toUriString();
       // String updateUrl = "http://localhost:8081/agesb-http/"+code+"/status-agesb-http";
        //String updateUrl = "http://localhost:8081/dossierAutoSync/"+code+"/status";
        logger.error("-----Update status dossier: " + updateUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(response, headers);
        AffectedMessageDto result = restTemplate.exchange(updateUrl, HttpMethod.PUT, request, AffectedMessageDto.class).getBody();
        return result;
    }
    private void processXmlResponse(String xmlResponse, CivilDossierResDto objreturn, CivilDossierAGESBDto dangKyHoTich) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        InputSource is = new InputSource(new StringReader(xmlResponse));
        Document document = builder.parse(is);
        if (document.getElementsByTagName("soapenv:Fault").getLength() > 0) {
            setXmlErrorResponseDetails(objreturn, xmlResponse, dangKyHoTich);
        } else {
            setXmlResponseDetails(objreturn, xmlResponse, dangKyHoTich);
        }
    }

    private void setXmlResponseDetails(CivilDossierResDto objreturn, String xmlRespone, CivilDossierAGESBDto dangKyHoTich) {
        objreturn.setStatus(1);
        objreturn.setStatusDescription("Liên thông hộ tịch thành công!");
        objreturn.setValue(xmlRespone);
        Map<String, String> map = new HashMap<>();
        map.put("statusMessage", "Liên thông hộ tịch thành công!");
        map.put("statusCode", "1");
        map.put("statusMessageResponse","Đã tiếp nhận!");
        map.put("statusCodeResponse","1");
        updateDossierStatus(map,dangKyHoTich.getMaHoSo());
    }

    private void setXmlErrorResponseDetails(CivilDossierResDto objreturn, String xmlRespone, CivilDossierAGESBDto dangKyHoTich) {
        objreturn.setStatus(-1);
        objreturn.setStatusDescription("Liên thông hộ tịch thất bại!");
        objreturn.setErrorCode("500");
        objreturn.setErrorDescription(xmlRespone);
        Map<String, String> map = new HashMap<>();
        map.put("statusMessage", objreturn.getStatusDescription());
        map.put("statusCode","-1" );
        updateDossierStatus(map, dangKyHoTich.getMaHoSo());
    }
}
