package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.bson.types.ObjectId;
import org.codehaus.jackson.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.Citizen;
import vn.vnpt.digo.adapter.document.MappingData;
import vn.vnpt.digo.adapter.document.errorLogs.CSDLDCLog;
import vn.vnpt.digo.adapter.dto.BusinessRegistrationTokenDto;
import vn.vnpt.digo.adapter.dto.GetProcedureDetailDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.csdldc.*;
import vn.vnpt.digo.adapter.dto.eform.CaterogyDto;
import vn.vnpt.digo.adapter.dto.eform.Column;
import vn.vnpt.digo.adapter.dto.eform.Component;
import vn.vnpt.digo.adapter.dto.eform.ItemComponent;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.repository.errorLogs.CSDLDCLogRepository;
import vn.vnpt.digo.adapter.util.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.text.DateFormat;
import java.text.Normalizer;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.*;

import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.json.JSONObject;
import org.json.XML;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
@RefreshScope
@Service
public class CSDLDCService {

    private final String PREFIX_PROVINCE_ID = "5def47c5f47614018c0000";//5def47c5f47614018c000087 => 2
    private final String PREFIX_DISTRICT_ID = "5def47c5f47614018c001";//5def47c5f47614018c001875 => 3
    private final String PREFIX_VILLAGE_ID = "5def47c5f47614018c1";//5def47c5f47614018c130196 => 5
    private final String PREFIX_ETHNIC_ID = "60a136a4575f3c1dd8c000";//60a136a4575f3c1dd8c00001 => 2
    private final String PREFIX_RELIGION_ID = "60a13156c7dc345c18f000";//60a13156c7dc345c18f00001 => 2
    public static final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890034");
    private static final String SOAP_PREFIX = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\">";
    public static int PRETTY_PRINT_INDENT_FACTOR = 5;
    private final List<String> CATEGORY_KEY_WORDS = Arrays.asList("ThuongTru.MaTinhThanh", "ThuongTru.MaPhuongXa", "ThuongTru.MaQuanHuyen",
            "NoiDangKyKhaiSinh.MaTinhThanh", "NoiDangKyKhaiSinh.MaPhuongXa", "NoiDangKyKhaiSinh.MaQuanHuyen",
            "NoiOHienTai.MaTinhThanh", "NoiOHienTai.MaPhuongXa", "NoiOHienTai.MaQuanHuyen",
            "QueQuan.MaTinhThanh", "QueQuan.MaPhuongXa", "QueQuan.MaQuanHuyen"
    );

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;
    
    @Autowired
    private ConnectService connectService;

    @Value("${digo.cache.security-key:Vnpt2021}")
    private String securityKey;

    @Value("${vnpt.human.updateCmndCccd}")
    private Boolean updateCmndCccd;

    @Value(value = "${digo.oidc.client-id}")
    private String clientId;

    @Value(value = "${digo.oidc.client-secret}")
    private String clientSecret;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;

    @Value(value = "${vnpt.connect-csdldc}")
    private Boolean isConnectCSDLDCQBH;

    @Value(value = "${digo.csdldc-search.check.hcm}")
    private Boolean isCheckCSDLDCSearch;

    @Value(value =  "${digo.list-csdldc.hcm}")
    private String[] listCSDLDCHCM;

    private CheckDisTrictOff checkDisTrictOff;

    @Autowired
    private CSDLDCLogRepository csdldcLogRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    Logger logger = LoggerFactory.getLogger(CSDLDCService.class);

    private HashMap<String, String> getExtendHeaders(String svURL,
                                                     String username,
                                                     String password) {
        HashMap<String, String> exHeaders = new HashMap<String, String>();
        Date date = new Date();
        long unixTime = date.getTime();
        String timestamp = Long.toString(unixTime);
        String token = StringHelper.toHex64(svURL + username + timestamp + password);
        String base64Token = StringHelper.toBase64Encode(username + ":" + token);
        exHeaders.put("Authorization", "Basic " + base64Token);
        exHeaders.put("timestamp", timestamp);
        return exHeaders;
    }

    private HashMap<String, String> getExtendHeaders(IntegratedConfigurationDto config) {
        HashMap<String, String> exHeaders = new HashMap<String, String>();
        String svURL = config.getParametersValue("shareCSDLDCEndpoint");
        String username = config.getParametersValue("username");
        String password = config.getParametersValue("password");
        String connectorType = config.getParametersValue("connectorType");
        Date date = new Date();
        long unixTime = date.getTime();
        String timestamp = Long.toString(unixTime);
        String token = StringHelper.toHex64(svURL + username + timestamp + password);
        String base64Token = StringHelper.toBase64Encode(username + ":" + token);
        exHeaders.put("Authorization", "Basic " + base64Token);
        exHeaders.put("timestamp", timestamp);
        if(connectorType != null && connectorType.equals("lgspHCM")){
            String lgspaccesstoken = this.getLGSPAccessToken(config);
            exHeaders.put("lgspaccesstoken", lgspaccesstoken);
        }
        return exHeaders;
    }


    public static String covertEngString(String value) {
        try {
            String temp = Normalizer.normalize(value, Normalizer.Form.NFD);
            Pattern pattern = Pattern.compile(temp).compile("\\p{InCombiningDiacriticalMarks}+");
            return pattern.matcher(temp).replaceAll("");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    private String createBody(String data) {
        StringBuilder sb = new StringBuilder();
        sb.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:dan=\"http://dancuquocgia.bca\">");
        sb.append("<soapenv:Header/>");
        sb.append("<soapenv:Body>");
        sb.append(data);
        sb.append("</soapenv:Body>");
        sb.append("</soapenv:Envelope>");
        return sb.toString();
    }

    private String createBodyLGSPHCM(String data) {
        StringBuilder sb = new StringBuilder();
        sb.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:qldc=\"http://dancuquocgia.bca\">");
        sb.append("<soapenv:Header/>");
        sb.append("<soapenv:Body>");
        sb.append(data);
        sb.append("</soapenv:Body>");
        sb.append("</soapenv:Envelope>");
        return sb.toString();
    }

    private String createShareCitizenInfoBody(ShareCitizenInfoRequestDto req, IntegratedConfigurationDto config) {
        String MaDVC = config.getParametersValue("MaDVC");
        String MaTichHop = config.getParametersValue("MaTichHop");
        String MaCanBo = req.getMaCanBo() != null ? req.getMaCanBo() : config.getParametersValue("MaCanBo");
        String indetity = req.getIndentityNumber();
        String birthday = req.getBirthday();
        String fullname = req.getFullname();
        fullname = covertEngString(fullname).toUpperCase();
        fullname = fullname.replaceAll("\u0110","\u0044");
        String identityNumberToken = Context.getJwtParameterValue("identity_number");
        String dstProvinceCode = config.getParametersValue("dstProvinceCode");
        Boolean enable346 = config.getParametersValue("346/TCTTKĐA06")!= null && "true".equals(config.getParametersValue("346/TCTTKĐA06").toString())?true:false;

        StringBuilder sb = new StringBuilder();
        sb.append("<dan:TraCuuThongTinCongDan>");
        sb.append("<dan:MaYeuCau>").append(UUID.randomUUID()).append("</dan:MaYeuCau>");
        sb.append("<dan:MaDVC>").append(MaDVC).append("</dan:MaDVC>");
        sb.append("<dan:MaTichHop>").append(MaTichHop).append("</dan:MaTichHop>");
        if(enable346){
            if(Objects.isNull(identityNumberToken) || identityNumberToken.isEmpty()){
                throw new DigoHttpException(10407, HttpServletResponse.SC_BAD_REQUEST);
            }
            String typeToken = Context.getJwtParameterValue("type");
            String clientId = Context.getJwtParameterValue("azp");
            String phoneNumber = Objects.nonNull(clientId) && "web-onegate".equals(clientId) ? getPhoneNumber() : "0";
            sb.append("<dan:MaCanBo>").append(identityNumberToken).append("</dan:MaCanBo>");
            sb.append("<dan:MaDonVi>").append(dstProvinceCode).append("</dan:MaDonVi>");
            sb.append("<dan:TaiKhoan>").append(MaCanBo).append("</dan:TaiKhoan>");
            sb.append("<dan:SoDienThoai>").append(phoneNumber).append("</dan:SoDienThoai>");
        }else {
        sb.append("<dan:MaCanBo>").append(MaCanBo).append("</dan:MaCanBo>");
        }

        if (indetity.length() == 12) {
            sb.append("<dan:SoDinhDanh>").append(indetity).append("</dan:SoDinhDanh>");
        } else {
            sb.append("<dan:SoCMND>").append(indetity).append("</dan:SoCMND>");
        }

        sb.append("<dan:HoVaTen>").append(fullname).append("</dan:HoVaTen>");

        if(birthday.length()==8){
            sb.append("<dan:NgayThangNamSinh>");
            sb.append("<dan:NgayThangNam>").append(birthday).append("</dan:NgayThangNam>");
            sb.append("</dan:NgayThangNamSinh>");
        }else {
            sb.append("<dan:Nam>");
            sb.append("<dan:Nam>").append(birthday).append("</dan:Nam>");
            sb.append("</dan:Nam>");
        }

        sb.append("</dan:TraCuuThongTinCongDan>");
        return sb.toString();
    }

    /**
     * value: dd/mm/yyyy
     **/
    private Date toGMT7Date(String value, String format){
        try{
            String yyyy = value.substring(0, 4);
            String mm = value.substring(4, 6);
            String dd = value.substring(6, 8);
            String tempDate = format;
            tempDate = tempDate.toLowerCase();
            tempDate = tempDate.replaceAll("dd",dd);
            tempDate = tempDate.replaceAll("mm",mm);
            tempDate = tempDate.replaceAll("yyyy",yyyy);
            DateFormat df = new SimpleDateFormat(format);
            df.setTimeZone(TimeZone.getTimeZone("GMT+7"));
            Date rsDate = df.parse(tempDate);
            return rsDate;
        }catch (Exception ex){
            return null;
        }

    }

    public void pushEformInfo(List<ItemComponent> list, List<Component> coms, String mapKey) {
        for (Component com : coms) {
            if (com.isInput() && !com.getType().equals("button")) {
                ItemComponent icom = com.getItemComponent(mapKey);
                if (Objects.nonNull(icom)) {
                    list.add(icom);
                }
            } else {
                List<Column> columns = com.getColumns();
                if (Objects.nonNull(columns)) {
                    if (columns.size() > 0) {
                        for (Column col : columns) {
                            if (Objects.nonNull(col.getComponents())) {
                                if (col.getComponents().size() > 0) {
                                    pushEformInfo(list, col.getComponents(), mapKey);
                                }
                            }
                        }
                    }
                }

                if (Objects.nonNull(com.getComponents())) {
                    if (com.getComponents().size() > 0) {
                        pushEformInfo(list, com.getComponents(), mapKey);
                    }
                }
            }
        }
    }

    public List<ItemComponent> getEformInfoById(ObjectId id, String mapKey) {
        List<ItemComponent> list = new ArrayList<>();
        try {
            String URL = microservice.eformUri("form/" + id).toUriString();
            String json = MicroserviceExchange.get(restTemplate, URL, String.class);
            TypeReference<HashMap<String, Object>> typeRef = new TypeReference<>() {
            };
            Map<String, Object> mapping = new ObjectMapper().readValue(json, typeRef);
            Object coms = mapping.get("components");
            ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
            String jsonComs = ow.writeValueAsString(coms);
            ObjectMapper mapper = new ObjectMapper();
            List<Component> components = mapper.readValue(jsonComs, new TypeReference<List<Component>>() {
            });

            //Collections.sort(list, new GenericComparator(orderBy));
            pushEformInfo(list, components, mapKey);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }

        return list;
    }

    private String getCaterogyId(String key, String value) {
        if ((key.contains("QuocTich") || key.endsWith(".QuocGia")) && value.equals("VN")) {
            return "5f39f4a95224cf235e134c5c";
        }

        String PREFIX_ID = "";
        if (key.equals("DanToc")) {
            PREFIX_ID = this.PREFIX_ETHNIC_ID;
        } else if (key.equals("TonGiao")) {
            PREFIX_ID = this.PREFIX_RELIGION_ID;
        } else if (key.endsWith(".MaTinhThanh")) {
            PREFIX_ID = this.PREFIX_PROVINCE_ID;
        } else if (key.endsWith(".MaQuanHuyen")) {
            if(checkDisTrictOff.checkOff()){
                PREFIX_ID = this.PREFIX_VILLAGE_ID;
            }else{
                PREFIX_ID = this.PREFIX_DISTRICT_ID;
            }
        } else if (key.endsWith(".MaPhuongXa")) {
            PREFIX_ID = this.PREFIX_VILLAGE_ID;
        }

        if (StringHelper.hasValue(PREFIX_ID)) {
            Integer defect = 24 - PREFIX_ID.length() - value.length();
            while (defect > 0) {
                value = "0" + value;
                defect--;
            }
            value = PREFIX_ID + value;
            return value;
        }

        return null;
    }

    private List<CitizenCaterogyResponseDto> getCaterogyEform(List<CitizenCaterogyRequestDto> reqs) {
        try {
            String URL = microservice.basedataUri("citizen/name").toUriString();
            String json = MicroserviceExchange.postJson(this.restTemplate, URL, reqs, String.class);
            ObjectMapper mapper = new ObjectMapper();
            List<CitizenCaterogyResponseDto> items = mapper.readValue(json, new TypeReference<List<CitizenCaterogyResponseDto>>() {
            });
            return items;
        } catch (Exception ex) {
        }
        return null;
    }

    private HashMap<String, Object> generateObjWithCaterogies(HashMap<String, Object> obj, List<CitizenCaterogyResponseDto> caterogies) {
        obj.entrySet().forEach(item -> {
            try {
                CaterogyDto caterogyDto = (CaterogyDto) item.getValue();
                String id = caterogyDto.getValue();
                CitizenCaterogyResponseDto category = caterogies.stream().filter(i -> i.getId().equals(id)).collect(Collectors.toList()).get(0);
                caterogyDto.setLabel(category.getName());
                obj.put(item.getKey(), caterogyDto);
                System.out.println();
            } catch (Exception ex) {
            }

        });
        return obj;
    }

    private String findLabelById(String id, List<CitizenCaterogyResponseDto> caterogies){
        CitizenCaterogyResponseDto category = caterogies.stream().filter(i -> i.getId().equals(id)).collect(Collectors.toList()).get(0);
        if(category == null) return null;
        return category.getName();
    }
    private void setLable(DiaChiDetailDto info, List<CitizenCaterogyResponseDto> caterogies){
        try {

            try{
                // phuong, xa
                String lablePhuongXa = findLabelById(info.getPhuongXa().getId(), caterogies);
                if (lablePhuongXa != null)
                    info.getPhuongXa().setLabel(lablePhuongXa);
            }catch (Exception e){

            }

            try{
                // quan, huyen
                String lableQuanHuyen = findLabelById(info.getQuanHuyen().getId(), caterogies);
                if (lableQuanHuyen != null)
                    info.getQuanHuyen().setLabel(lableQuanHuyen);
            }catch (Exception e){

            }

            try{
                // tinh thanh
                String lableTinh = findLabelById(info.getTinhThanh().getId(), caterogies);
                if (lableTinh != null)
                    info.getTinhThanh().setLabel(lableTinh);
            }catch (Exception e){

            }

            try{
                // quoc gia
                CaterogyCitizenDto QG = GsonUtils.copyObject(info.getQuocGia(), CaterogyCitizenDto.class);
                String lableQG = findLabelById(QG.getId(), caterogies);
                if (lableQG != null)
                    QG.setLabel(lableQG);
                info.setQuocGia(QG);
            }catch (Exception e){

            }




        }catch (Exception e){}
    }

    private void remapValueOriginInfo(ShareCitizenOrginInfoResponseDto originInfo, List<CitizenCaterogyResponseDto> caterogies) {

        setLable(originInfo.getThuongTru(), caterogies);
        setLable(originInfo.getQueQuan(), caterogies);
        setLable(originInfo.getNoiOHienTai(), caterogies);
        setLable(originInfo.getNoiDangKyKhaiSinh(), caterogies);
        try {
            // dan toc
            CaterogyCitizenDto DanToc = GsonUtils.copyObject(originInfo.getDanToc(), CaterogyCitizenDto.class);
            String lableDanToc = findLabelById(DanToc.getId(), caterogies);
            if (lableDanToc != null) {
                DanToc.setLabel(lableDanToc);
            }
            originInfo.setDanToc(DanToc);
        }catch (Exception e){}
        // ton giao
        try {
            CaterogyCitizenDto TonGiao = GsonUtils.copyObject(originInfo.getTonGiao(), CaterogyCitizenDto.class);
            String lableTonGiao = findLabelById(TonGiao.getId(), caterogies);
            if (lableTonGiao != null) {
                TonGiao.setLabel(lableTonGiao);
            }
            originInfo.setTonGiao(TonGiao);
        }catch (Exception e){}
        // quoc tich
        try {
            CaterogyCitizenDto QuocTich = GsonUtils.copyObject(originInfo.getQuocTich(), CaterogyCitizenDto.class);
            String lableQuocTich = findLabelById(QuocTich.getId(), caterogies);
            if (lableQuocTich != null) {
                QuocTich.setLabel(lableQuocTich);
            }
            originInfo.setQuocTich(QuocTich);
        }catch (Exception e){}

        // quoc tich cha
        try {
            CaterogyCitizenDto QuocTich = GsonUtils.copyObject(originInfo.getCha().getQuocTich(), CaterogyCitizenDto.class);
            String lableQuocTich = findLabelById(QuocTich.getId(), caterogies);
            if (lableQuocTich != null) {
                QuocTich.setLabel(lableQuocTich);
            }
            originInfo.getCha().setQuocTich(QuocTich);
        }catch (Exception e){}
        // quoc tich me
        try {
            CaterogyCitizenDto QuocTich = GsonUtils.copyObject(originInfo.getMe().getQuocTich(), CaterogyCitizenDto.class);
            String lableQuocTich = findLabelById(QuocTich.getId(), caterogies);
            if (lableQuocTich != null) {
                QuocTich.setLabel(lableQuocTich);
            }
            originInfo.getMe().setQuocTich(QuocTich);
        }catch (Exception e){}

        // quoc tich vo chong
        try {
            CaterogyCitizenDto QuocTich = GsonUtils.copyObject(originInfo.getVoChong().getQuocTich(), CaterogyCitizenDto.class);
            String lableQuocTich = findLabelById(QuocTich.getId(), caterogies);
            if (lableQuocTich != null) {
                QuocTich.setLabel(lableQuocTich);
            }
            originInfo.getVoChong().setQuocTich(QuocTich);
        }catch (Exception e){}

        // quoc tich vo chong
        try {
            CaterogyCitizenDto QuocTich = GsonUtils.copyObject(originInfo.getNguoiDaiDien().getQuocTich(), CaterogyCitizenDto.class);
            String lableQuocTichNguoiDaiDien = findLabelById(QuocTich.getId(), caterogies);
            if (lableQuocTichNguoiDaiDien != null) {
                QuocTich.setLabel(lableQuocTichNguoiDaiDien);
            }
            originInfo.getNguoiDaiDien().setQuocTich(QuocTich);
        }catch (Exception e){}
    }

    private String formatDate(String value, String format){
        try{
            String yyyy = value.substring(0, 4);
            String mm = value.substring(4, 6);
            String dd = value.substring(6, 8);
            format = format.toLowerCase();
            format = format.replaceAll("dd",dd);
            format = format.replaceAll("mm",mm);
            format = format.replaceAll("yyyy",yyyy);
            return format;
        }catch (Exception ex){
            return null;
        }
    }

    private HashMap<String, Object> getMapData(ShareCitizenInfoResponseDto citizen, ObjectId eformId) {
        HashMap<String, Object> obj = new HashMap<>();
        List<ItemComponent> components = getEformInfoById(eformId, "congdan");
        List<CitizenCaterogyRequestDto> reqs = new ArrayList<>();
        for (ItemComponent com : components) {
            try {
                String mapValue = com.getMap().getValue();
                String mapKey = com.getKey();
                Object value = null;
                switch (mapValue) {
                     // **********************Field 1: SoDinhDanh ************************
                    case "SoDinhDanh":
                        value = citizen.getSoDinhDanh();
                        break;
                    // **********************Field 2: SoCMND ************************
                    case "SoCMND":
                        value = citizen.getSoCMND();
                        break;
                    // **********************Field 3: HoVaTen ************************
                    case "HoVaTen.Ho":
                        value = citizen.getHoVaTen().getHo();
                        break;
                    case "HoVaTen.ChuDem":
                        value = citizen.getHoVaTen().getChuDem();
                        break;
                    case "HoVaTen.Ten":
                        value = citizen.getHoVaTen().getTen();
                        break;
                    // **********************Field 4: QuocTich ************************
                    case "QuocTich": {
                        String id = citizen.getQuocTich();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("nation");
                        reqs.add(req);
                        break;
                    }

                    // **********************Field 5: GioiTinh ************************
                    case "GioiTinh":
                        value = citizen.getGioiTinh();
                        break;
                    // **********************Field 6: DanToc ************************
                    case "DanToc": {
                        String id = citizen.getDanToc();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("ethnic");
                        reqs.add(req);
                        break;
                    }
                    // **********************Field 7: TonGiao ************************
                    case "TonGiao": {
                        String id = citizen.getTonGiao();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("religion");
                        reqs.add(req);
                        break;
                    }
                    // **********************Field 8: TinhTrangHonNhan ************************
                    case "TinhTrangHonNhan":
                        value = citizen.getTinhTrangHonNhan();
                        break;

                    // **********************Field 9: NhomMau ************************
                    case "NhomMau":
                        value = citizen.getNhomMau();
                        break;

                    // **********************Field 10: NgayThangNamSinh ************************
                    case "NgayThangNamSinh": {
                        String birthday = citizen.getNgayThangNamSinh().getNgayThangNam();
                        value = this.toGMT7Date(birthday, com.getFormat());
                        break;
                    }
                    case "NgayThangNamSinh.NgayThangNam": {
                        String birthday = citizen.getNgayThangNamSinh().getNgayThangNam();
                        value = this.toGMT7Date(birthday, com.getFormat());
                        break;
                    }

                    case "NgayThangNamSinh.Nam": {
                        value =citizen.getNgayThangNamSinh().getNam();
                        break;
                    }

                    case "NamSinh":
                        value = citizen.getNgayThangNamSinh().getNam();
                        break;

                    // **********************Field 11: NgayThangNamSinh ************************
                    case "SoSoHoKhau":
                        value = citizen.getSoSoHoKhau();
                        break;

                    // **********************Field 12: VoChong ************************
                    case "VoChong.SoDinhDanh":
                        value = citizen.getVoChong().getSoDinhDanh();
                        break;
                    case "VoChong.SoCMND":
                        value = citizen.getVoChong().getSoCMND();
                        break;
                    case "VoChong.HoVaTen":
                        value = citizen.getVoChong().getHoVaTen();
                        break;

                    case "VoChong.QuocTich": {
                        String id = citizen.getVoChong().getQuocTich();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("nation");
                        reqs.add(req);
                        break;
                    }

                    // **********************Field 13: NguoiDaiDien ************************
                    case "NguoiDaiDien.SoDinhDanh": {
                        value = citizen.getNguoiDaiDien().getSoDinhDanh();
                        break;
                    }
                    case "NguoiDaiDien.SoCMND": {
                        value = citizen.getNguoiDaiDien().getSoCMND();
                        break;
                    }
                    case "NguoiDaiDien.HoVaTen": {
                        value = citizen.getNguoiDaiDien().getHoVaTen();
                        break;
                    }
                    case "NguoiDaiDien.QuocTich": {
                        String id = citizen.getNguoiDaiDien().getQuocTich();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("nation");
                        reqs.add(req);
                        break;
                    }

                    // **********************Field 14: Cha ************************
                    case "Cha.SoDinhDanh": {
                        value = citizen.getCha().getSoDinhDanh();
                        break;
                    }
                    case "Cha.SoCMND": {
                        value = citizen.getCha().getSoCMND();
                        break;
                    }
                    case "Cha.HoVaTen": {
                        value = citizen.getCha().getHoVaTen();
                        break;
                    }
                    case "Cha.QuocTich": {
                        String id = citizen.getCha().getQuocTich();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("nation");
                        reqs.add(req);
                        break;
                    }

                    // **********************Field 15: Me ************************
                    case "Me.SoDinhDanh": {
                        value = citizen.getMe().getSoDinhDanh();
                        break;
                    }
                    case "Me.SoCMND": {
                        value = citizen.getMe().getSoCMND();
                        break;
                    }
                    case "Me.HoVaTen": {
                        value = citizen.getMe().getHoVaTen();
                        break;
                    }
                    case "Me.QuocTich": {
                        String id = citizen.getMe().getQuocTich();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("nation");
                        reqs.add(req);
                        break;
                    }

                    // **********************Field 16: ChuHo ************************
                    case "ChuHo.SoDinhDanh": {
                        value = citizen.getChuHo().getSoDinhDanh();
                        break;
                    }
                    case "ChuHo.SoCMND": {
                        value = citizen.getChuHo().getSoCMND();
                        break;
                    }
                    case "ChuHo.HoVaTen": {
                        value = citizen.getChuHo().getHoVaTen();
                        break;
                    }
                    case "ChuHo.QuanHe": {
                        value = citizen.getChuHo().getQuanHe();
                        break;
                    }

                    // **********************Field 17: QueQuan" ************************
                    /**
                     * **********************QueQuan************************
                     */
                    case "QueQuan.QuocGia": {
                        String id = citizen.getQueQuan().getQuocGia();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "QueQuan.MaTinhThanh": {
                        String id = citizen.getQueQuan().getMaTinhThanh();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "QueQuan.MaQuanHuyen": {
                        String id = citizen.getQueQuan().getMaQuanHuyen();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "QueQuan.MaPhuongXa": {
                        String id = citizen.getQueQuan().getMaPhuongXa();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "QueQuan.ChiTiet":
                        value = citizen.getQueQuan().getChiTiet();
                        break;

                    // **********************Field 18: NoiOHienTai" ************************
                    /**
                     * **********************NoiOHienTai************************
                     */
                    case "NoiOHienTai.QuocGia": {
                        String id = citizen.getNoiOHienTai().getQuocGia();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "NoiOHienTai.MaTinhThanh": {
                        String id = citizen.getNoiOHienTai().getMaTinhThanh();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "NoiOHienTai.MaQuanHuyen": {
                        String id = citizen.getNoiOHienTai().getMaQuanHuyen();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "NoiOHienTai.MaPhuongXa": {
                        String id = citizen.getNoiOHienTai().getMaPhuongXa();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "NoiOHienTai.ChiTiet":
                        value = citizen.getNoiOHienTai().getChiTiet();
                        break;
                    // **********************Field 19: NoiDangKyKhaiSinh" ************************
                    /**
                     * **********************NoiDangKyKhaiSinh************************
                     */
                    case "NoiDangKyKhaiSinh.QuocGia": {
                        String id = citizen.getNoiDangKyKhaiSinh().getQuocGia();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "NoiDangKyKhaiSinh.MaTinhThanh": {
                        String id = citizen.getNoiDangKyKhaiSinh().getMaTinhThanh();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "NoiDangKyKhaiSinh.MaQuanHuyen": {
                        String id = citizen.getNoiDangKyKhaiSinh().getMaQuanHuyen();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "NoiDangKyKhaiSinh.MaPhuongXa": {
                        String id = citizen.getNoiDangKyKhaiSinh().getMaPhuongXa();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "NoiDangKyKhaiSinh.ChiTiet":
                        value = citizen.getNoiDangKyKhaiSinh().getChiTiet();
                        break;
                    // **********************Field 20: ThuongTru" ************************
                    /**
                     * **********************ThuongTru************************
                     */
                    case "ThuongTru.QuocGia": {
                        String id = citizen.getThuongTru().getQuocGia();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }

                    case "ThuongTru.MaTinhThanh": {
                        String id = citizen.getThuongTru().getMaTinhThanh();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "ThuongTru.MaQuanHuyen": {
                        String id = citizen.getThuongTru().getMaQuanHuyen();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "ThuongTru.MaPhuongXa": {
                        String id = citizen.getThuongTru().getMaPhuongXa();
                        id = this.getCaterogyId(mapValue, id);
                        CaterogyDto valueItem = new CaterogyDto();
                        valueItem.setValue(id);
                        value = valueItem;

                        CitizenCaterogyRequestDto req = new CitizenCaterogyRequestDto();
                        req.setId(new ObjectId(id));
                        req.setType("place");
                        reqs.add(req);
                        break;
                    }
                    case "ThuongTru.ChiTiet":
                        value = citizen.getThuongTru().getChiTiet();
                        break;

                }

                obj.put(mapKey, value);

            }catch ( Exception e){
                System.out.println("Map value err:" + com.getMap().getValue());
            }
        }

        if (reqs.size() > 0) {
            List<CitizenCaterogyResponseDto> caterogies = getCaterogyEform(reqs);
            obj = generateObjWithCaterogies(obj, caterogies);
        }

        return obj;
    }

    private List<CitizenCaterogyResponseDto> getListIdByCode(ShareCitizenInfoResponseDto citizen, ShareCitizenOrginInfoResponseDto originInfo){
        /**
         * **********************ThuongTru************************
         */
        List<CitizenCaterogyRequestDto> reqs = new ArrayList<>();
        try{
            String code = citizen.getThuongTru().getQuocGia();
            String id = this.getCaterogyId("ThuongTru.QuocGia", code );
            CaterogyCitizenDto QuocGia = new CaterogyCitizenDto();
            QuocGia.setId(id);
            QuocGia.setValue(code);
            originInfo.getThuongTru().setQuocGia(QuocGia);
            reqs.add(new CitizenCaterogyRequestDto(new ObjectId(id),"place"));
        }catch (Exception e){}
        try {
            String code = citizen.getThuongTru().getMaTinhThanh();
            ObjectId id = new ObjectId(this.getCaterogyId("ThuongTru.MaTinhThanh", code ));
            originInfo.getThuongTru().setTinhThanh(new CaterogyCitizenDto());
            originInfo.getThuongTru().getTinhThanh().setId(id.toString());
            originInfo.getThuongTru().getTinhThanh().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}
        try{
            String code = citizen.getThuongTru().getMaQuanHuyen();
            ObjectId id = new ObjectId(this.getCaterogyId("ThuongTru.MaQuanHuyen", code ));
            originInfo.getThuongTru().setQuanHuyen(new CaterogyCitizenDto());
            originInfo.getThuongTru().getQuanHuyen().setId(id.toString());
            originInfo.getThuongTru().getQuanHuyen().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}

        try{
            String code = citizen.getThuongTru().getMaPhuongXa();
            ObjectId id = new ObjectId(this.getCaterogyId("ThuongTru.MaPhuongXa", code ));
            originInfo.getThuongTru().setPhuongXa(new CaterogyCitizenDto());
            originInfo.getThuongTru().getPhuongXa().setId(id.toString());
            originInfo.getThuongTru().getPhuongXa().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}

        /**
         * **********************NoiDangKyKhaiSinh************************
         */
        try{
            String code = citizen.getNoiDangKyKhaiSinh().getQuocGia();
            String id = this.getCaterogyId("NoiDangKyKhaiSinh.QuocGia", code );
            CaterogyCitizenDto QuocGia = new CaterogyCitizenDto();
            QuocGia.setId(id);
            QuocGia.setValue(code);
            originInfo.getNoiDangKyKhaiSinh().setQuocGia(QuocGia);
            reqs.add(new CitizenCaterogyRequestDto(new ObjectId(id),"place"));
        }catch (Exception e){}
        try {
            String code = citizen.getNoiDangKyKhaiSinh().getMaTinhThanh();
            ObjectId id = new ObjectId(this.getCaterogyId("NoiDangKyKhaiSinh.MaTinhThanh", code ));
            originInfo.getNoiDangKyKhaiSinh().setTinhThanh(new CaterogyCitizenDto());
            originInfo.getNoiDangKyKhaiSinh().getTinhThanh().setId(id.toString());
            originInfo.getNoiDangKyKhaiSinh().getTinhThanh().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}
        try{
            String code = citizen.getNoiDangKyKhaiSinh().getMaQuanHuyen();
            ObjectId id = new ObjectId(this.getCaterogyId("NoiDangKyKhaiSinh.MaQuanHuyen", code ));
            originInfo.getNoiDangKyKhaiSinh().setQuanHuyen(new CaterogyCitizenDto());
            originInfo.getNoiDangKyKhaiSinh().getQuanHuyen().setId(id.toString());
            originInfo.getNoiDangKyKhaiSinh().getQuanHuyen().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}

        try{
            String code = citizen.getNoiDangKyKhaiSinh().getMaPhuongXa();
            ObjectId id = new ObjectId(this.getCaterogyId("NoiDangKyKhaiSinh.MaPhuongXa", code ));
            originInfo.getNoiDangKyKhaiSinh().setPhuongXa(new CaterogyCitizenDto());
            originInfo.getNoiDangKyKhaiSinh().getPhuongXa().setId(id.toString());
            originInfo.getNoiDangKyKhaiSinh().getPhuongXa().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}

        /**
         * **********************NoiOHienTai************************
         */
        try{
            String code = citizen.getNoiOHienTai().getQuocGia();
            ObjectId id = new ObjectId(this.getCaterogyId("NoiOHienTai.QuocGia", code ));
            CaterogyCitizenDto QuocGia = new CaterogyCitizenDto();
            QuocGia.setId(id.toString());
            QuocGia.setValue(code);
            originInfo.getNoiOHienTai().setQuocGia(QuocGia);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}
        try {
            String code = citizen.getNoiOHienTai().getMaTinhThanh();
            ObjectId id = new ObjectId(this.getCaterogyId("NoiOHienTai.MaTinhThanh", code ));
            originInfo.getNoiOHienTai().setTinhThanh(new CaterogyCitizenDto());
            originInfo.getNoiOHienTai().getTinhThanh().setId(id.toString());
            originInfo.getNoiOHienTai().getTinhThanh().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}
        try{
            String code = citizen.getNoiOHienTai().getMaQuanHuyen();
            ObjectId id = new ObjectId(this.getCaterogyId("NoiOHienTai.MaQuanHuyen", code ));
            originInfo.getNoiOHienTai().setQuanHuyen(new CaterogyCitizenDto());
            originInfo.getNoiOHienTai().getQuanHuyen().setId(id.toString());
            originInfo.getNoiOHienTai().getQuanHuyen().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}

        try{
            String code = citizen.getNoiOHienTai().getMaPhuongXa();
            ObjectId id = new ObjectId(this.getCaterogyId("NoiOHienTai.MaPhuongXa", code ));
            originInfo.getNoiOHienTai().setPhuongXa(new CaterogyCitizenDto());
            originInfo.getNoiOHienTai().getPhuongXa().setId(id.toString());
            originInfo.getNoiOHienTai().getPhuongXa().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}

        /**
         * **********************QueQuan************************
         */
        try{
            String code = citizen.getQueQuan().getQuocGia();
            ObjectId id = new ObjectId(this.getCaterogyId("QueQuan.QuocGia", code ));
            CaterogyCitizenDto QuocGia = new CaterogyCitizenDto();
            QuocGia.setId(id.toString());
            QuocGia.setValue(code);
            originInfo.getQueQuan().setQuocGia(QuocGia);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}
        try {
            String code = citizen.getQueQuan().getMaTinhThanh();
            ObjectId id = new ObjectId(this.getCaterogyId("QueQuan.MaTinhThanh", code ));
            originInfo.getQueQuan().setTinhThanh(new CaterogyCitizenDto());
            originInfo.getQueQuan().getTinhThanh().setId(id.toString());
            originInfo.getQueQuan().getTinhThanh().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}
        try{
            String code = citizen.getQueQuan().getMaQuanHuyen();
            ObjectId id = new ObjectId(this.getCaterogyId("QueQuan.MaQuanHuyen", code ));
            originInfo.getQueQuan().setQuanHuyen(new CaterogyCitizenDto());
            originInfo.getQueQuan().getQuanHuyen().setId(id.toString());
            originInfo.getQueQuan().getQuanHuyen().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}

        try{
            String code = citizen.getQueQuan().getMaPhuongXa();
            ObjectId id = new ObjectId(this.getCaterogyId("QueQuan.MaPhuongXa", code ));
            originInfo.getQueQuan().setPhuongXa(new CaterogyCitizenDto());
            originInfo.getQueQuan().getPhuongXa().setId(id.toString());
            originInfo.getQueQuan().getPhuongXa().setValue(code);
            reqs.add(new CitizenCaterogyRequestDto(id,"place"));
        }catch (Exception e){}

        try{
            String code = citizen.getDanToc();
            ObjectId id = new ObjectId(this.getCaterogyId("DanToc", code ));
            CaterogyCitizenDto DanToc = new CaterogyCitizenDto();
            DanToc.setId(id.toString());
            DanToc.setValue(code);
            originInfo.setDanToc(DanToc);
            reqs.add(new CitizenCaterogyRequestDto(id,"ethnic"));
        }catch (Exception e){}

        try{
            String code = citizen.getTonGiao();
            ObjectId id = new ObjectId(this.getCaterogyId("TonGiao", code ));
            CaterogyCitizenDto TonGiao = new CaterogyCitizenDto();
            TonGiao.setId(id.toString());
            TonGiao.setValue(code);
            originInfo.setTonGiao(TonGiao);
            reqs.add(new CitizenCaterogyRequestDto(id,"religion"));
        }catch (Exception e){}

        try{
            String code = citizen.getQuocTich();
            ObjectId id = new ObjectId(this.getCaterogyId("QuocTich", code ));
            CaterogyCitizenDto QuocTich = new CaterogyCitizenDto();
            QuocTich.setId(id.toString());
            QuocTich.setValue(code);
            originInfo.setQuocTich(QuocTich);
            reqs.add(new CitizenCaterogyRequestDto(id,"nation"));
        }catch (Exception e){}

        try{
            String code = citizen.getCha().getQuocTich();
            ObjectId id = new ObjectId(this.getCaterogyId("QuocTich", code ));
            CaterogyCitizenDto QuocTich = new CaterogyCitizenDto();
            QuocTich.setId(id.toString());
            QuocTich.setValue(code);
            originInfo.getCha().setQuocTich(QuocTich);
            reqs.add(new CitizenCaterogyRequestDto(id,"nation"));
        }catch (Exception e){}

        try{
            String code = citizen.getMe().getQuocTich();
            ObjectId id = new ObjectId(this.getCaterogyId("QuocTich", code ));
            CaterogyCitizenDto QuocTich = new CaterogyCitizenDto();
            QuocTich.setId(id.toString());
            QuocTich.setValue(code);
            originInfo.getMe().setQuocTich(QuocTich);
            reqs.add(new CitizenCaterogyRequestDto(id,"nation"));
        }catch (Exception e){}

        try{
            String code = citizen.getNguoiDaiDien().getQuocTich();
            ObjectId id = new ObjectId(this.getCaterogyId("QuocTich", code ));
            CaterogyCitizenDto QuocTich = new CaterogyCitizenDto();
            QuocTich.setId(id.toString());
            QuocTich.setValue(code);
            originInfo.getNguoiDaiDien().setQuocTich(QuocTich);
            reqs.add(new CitizenCaterogyRequestDto(id,"nation"));
        }catch (Exception e){}

        try{
            String code = citizen.getVoChong().getQuocTich();
            ObjectId id = new ObjectId(this.getCaterogyId("QuocTich", code ));
            CaterogyCitizenDto QuocTich = new CaterogyCitizenDto();
            QuocTich.setId(id.toString());
            QuocTich.setValue(code);
            originInfo.getVoChong().setQuocTich(QuocTich);
            reqs.add(new CitizenCaterogyRequestDto(id,"nation"));
        }catch (Exception e){}

        List<CitizenCaterogyResponseDto> caterogies  = getCaterogyEform(reqs);
        return caterogies;
    }
    public List<String> getTokenPermissions(){
        try{
            String sPermissions = Context.getJwtParameterValue("permissions");
            Type listType = new TypeToken<ArrayList<Permission>>(){}.getType();
            List<Permission> data = new Gson().fromJson(sPermissions, listType);
            List<String> permissions = data.stream().map(i->i.getPermission().getCode()).collect(Collectors.toList());
            return permissions;
        }catch (Exception ex){
            return new ArrayList<>();
        }
    }

    private String toYYYYMMDD(String birthday){
        if(birthday.length() > 10){
            birthday = birthday.split("\\+")[0]+"Z";
            TemporalAccessor ta = DateTimeFormatter.ISO_INSTANT.parse(birthday);
            Instant i = Instant.from(ta);
            Date d = Date.from(i);
            String sDate = new SimpleDateFormat("yyyyMMdd").format(d);
            return sDate;
        }else if(birthday.length()==10){
            String[] birthdays = birthday.split("/");
            birthday = birthdays[2] + birthdays[1] + birthdays[0];
        }
        return birthday;
    }

	public Object mapCitizenInfoTEST(Object citizenInput, ObjectId EFormId,  boolean MAPPFillData, boolean getOriginInfo){
        CitizenResponseDto rs = new CitizenResponseDto();
        ShareCitizenInfoResponseDto citizen = GsonUtils.copyObject(citizenInput, ShareCitizenInfoResponseDto.class);
        try {
            if (Objects.nonNull(EFormId)) {
                HashMap<String, Object> obj = getMapData(citizen, EFormId);
                rs.setInfo(obj);
            } else if (MAPPFillData){
                rs.setInfo(this.defaultMap(citizen));
            }
            // get orginInfo
            if(getOriginInfo) {
                try {
                    ShareCitizenOrginInfoResponseDto originInfo = GsonUtils.copyObject(citizen, ShareCitizenOrginInfoResponseDto.class);
                    rs.setOriginInfo(originInfo);
                    List<CitizenCaterogyResponseDto> caterogiesOrginInfo = getListIdByCode(citizen, originInfo);
                    remapValueOriginInfo(originInfo, caterogiesOrginInfo);
                    rs.setOriginInfo(originInfo);
                } catch (Exception e) {
                    System.out.println("get orginInfo" + e.getMessage());
                }
            }
        } catch (Exception ex) {
            logger.info("Exception getCitizenInfo 1: " + ex.getMessage());
        }
        return rs;
    }

    @Cacheable(value = "getCitizenInfo", key = "{#obj.getHoVaTen(),#obj.getSoDinhDanh(),#obj.getNgayThangNamSinh().getNgayThangNam(),#obj.getSoSoHoKhau()}")
    private void addMemberToFamily(ShareCitizenInfoResponseDto obj){
        //Tìm trong collection citizen chưa có dữ liệu với số định danh của obj truyền vào này chưa thì thêm mới
        //Nếu có:
        //      Kiểm tra số hộ khẩu của dữ liệu tìm thấy trùng với số sổ hộ khẩu truyền vào => bỏ qua không xử lý gì
        //      Ngược lại trường hợp trên, có nghĩa là người này đã tách hộ khẩu mới =>
        //           đánh cờ lại cho dòng dữ liệu tìm thấy status = 0, và thêm dữ liệu mới bằng: Citizen citizen = new Citizen(obj);
    }

    @Cacheable(value = "getCitizenInfo", key = "{#req.getProcedureId(),#req.getIndentityNumber(),#req.getFullname(),#req.getBirthday(),#req.getSubsystemId(),#req.getKey(),#config.getParametersValue(\"type\"),#req.getEformId(), #req.isGetOriginInfo(), #req.getMaCanBo(), #username}")
    public CitizenResponseDto getCitizenInfo(ShareCitizenInfoRequestDto req,IntegratedConfigurationDto config, String username) throws IOException {
        String type = config.getParametersValue("type");

        //Bỏ qua kiểm tra các số định danh không phải là cmnd/cccd hoặc bỏ qua check (type is empty)
        List<Integer> identityLengths = Arrays.asList(9, 12);
        if(!identityLengths.contains(req.getIndentityNumber().length()) || !StringHelper.hasValue(type)){
            CitizenResponseDto res = new CitizenResponseDto();
            HashMap<String, Object> obj = new HashMap<>();
            res.setInfo(obj);
            return res;
        }

        //ghi log v2
        CSDLDCLog savedLog;
        CSDLDCLog csdldcLog = createLogV2(req, type, username, false, false);

        String accountId = Context.getJwtParameterValue("account_id");
        String typeToken = Context.getJwtParameterValue("type");
        String azp = Context.getJwtParameterValue("azp");
        List<String> tokenPermissions = this.getTokenPermissions();
        if(!StringHelper.hasValue(accountId)){
            if(!securityKey.equals(req.getKey())){
                throw new DigoHttpException(11011, HttpServletResponse.SC_BAD_REQUEST);
            }
        } else if(tokenPermissions.size()==0 && (
                (Objects.nonNull(typeToken) && !typeToken.equals("2")) ||
                (Objects.isNull(typeToken) && Objects.nonNull(azp) && "web-padsvc".equals(azp)))){
            String identityNumberToken = "", fullnameToken = "", birthdayToken = "";
            if (isConnectCSDLDCQBH && req.getMapfilldata().equals("true")) {
                identityNumberToken = req.getIndentityNumber();
                fullnameToken = req.getFullname();
                birthdayToken = req.getBirthday();
            }
            else {
                identityNumberToken = Context.getJwtParameterValue("identity_number");
                fullnameToken = Context.getJwtParameterValue("name");
                birthdayToken = Context.getJwtParameterValue("birthday");
            }
            if(!StringHelper.hasValue(identityNumberToken)||!StringHelper.hasValue(fullnameToken)||!StringHelper.hasValue(birthdayToken)){
                throw new DigoHttpException(11012, HttpServletResponse.SC_FORBIDDEN);
            }

            String birthday = toYYYYMMDD(birthdayToken);
            req.setFullname(fullnameToken);
            req.setBirthday(birthday);
            req.setIndentityNumber(identityNumberToken);
        }
        CitizenResponseDto rs = new CitizenResponseDto();

        if(type.equals("token")){
            String URL = microservice.adapterUri("citizen/--info").toUriString();
            URL += "?agency-id=012345678901234567890123";
            URL += "&subsystem-id=" + req.getSubsystemId();
            URL += "&indentity-number=" + req.getIndentityNumber();
            URL += "&fullname=" + req.getFullname();
            URL += "&birthday=" + toYYYYMMDD(req.getBirthday());

            String tokenCheckCitizen = config.getParametersValue("token");
            String decodeToken = StringHelper.decode(tokenCheckCitizen);
            String[] decodeTokens = decodeToken.split(",");
            String ssoURL = decodeTokens[0];
            String clientId = decodeTokens[1];
            String clientSecret = decodeTokens[2];
            String apiURL = decodeTokens[3];
            String key = decodeTokens[4];
            URL += "&key=" + key;
            CitizenResponseDto citizen = connectService.getJSon(URL,ssoURL,apiURL,clientId,clientSecret, CitizenResponseDto.class);
//            try {
//                ShareCitizenInfoResponseDto citizen2 =  GsonUtils.copyObject(citizen.getInfo(), ShareCitizenInfoResponseDto.class);
//                if (Objects.nonNull(req.getEformId())) {
//                    HashMap<String, Object> obj = getMapData(citizen2, req.getEformId());
//                    rs.setInfo(obj);
//                } else if (Objects.nonNull(req.getMapfilldata())){
//                    rs.setInfo(this.defaultMap(citizen2));
//                }
//                // get orginInfo
//                if(req.isGetOriginInfo()) {
//                    try {
//                        ShareCitizenOrginInfoResponseDto originInfo = GsonUtils.copyObject(citizen2, ShareCitizenOrginInfoResponseDto.class);
//                        rs.setOriginInfo(originInfo);
//                        List<CitizenCaterogyResponseDto> caterogiesOrginInfo = getListIdByCode(citizen2, originInfo);
//                        remapValueOriginInfo(originInfo, caterogiesOrginInfo);
//                        rs.setOriginInfo(originInfo);
//                    } catch (Exception e) {
//                        System.out.println("get orginInfo" + e.getMessage());
//                    }
//                }
//                return rs;
//            } catch (Exception ex) {
//                logger.info("Exception getCitizenInfo 1: " + ex.getMessage());
//            }

            if(updateCmndCccd){
                if(citizen != null && citizen.getOriginInfo() != null) {
                    Gson gson = new Gson();
                    ObjectMapper objectMapper = new ObjectMapper();
                    String jsonString = gson.toJson(citizen.getOriginInfo());
                    HashMap<String, String> resultMap = objectMapper.readValue(jsonString, HashMap.class);
                    String cmnd = null;
                    String cccd = null;
                    if(Objects.nonNull(resultMap.get("SoCMND"))){
                        cmnd = resultMap.get("SoCMND");
                    }
                    if(Objects.nonNull(resultMap.get("SoDinhDanh"))){
                        cccd = resultMap.get("SoDinhDanh");
                    }
                    if(cmnd != null && cccd != null) {
                        updateCmndCccd(cmnd, cccd);
                    }
                }
            }
            //ghi logv2
            if(citizen != null && isSoapEnvelope(citizen.toString())){
                csdldcLog.setResponse("Tra cứu thành công");
                csdldcLog.setStatus(1);
            }else{
                csdldcLog.setResponse(citizen);//ghi log v2
//                csdldcLog.setSendUrl(URL);
//                csdldcLog.setBodyContent("ssoURL, clientId, clientSecret, URL=[apiURL,key]: " + decodeToken);
            }
            if(req.isEnableLogCSDLDCBDH()){
                csdldcLog.setDossierId(req.getDossierId());
                csdldcLog.setDossierCode(req.getDossierCode());
            }
            savedLog = csdldcLogRepository.save(csdldcLog);

            if (Objects.nonNull(savedLog)) citizen.setLogId(savedLog.getId());

            return citizen;
        }

        String ssOrginURL = config.getParametersValue("ssOrginURL");
        String providerurl = config.getParametersValue("providerurl");
        String dstcode = config.getParametersValue("dstcode");
        String svURL = config.getParametersValue("shareCSDLDCEndpoint");
        String connectorType = config.getParametersValue("connectorType");

        HashMap<String, String> headers = new HashMap<>();
        headers = getExtendHeaders(config);
        if(connectorType != null && connectorType.equals("lgspHCM")){
            svURL = config.getParametersValue("shareCSDLDCLGSPHCMEndpoint");
        }
        String URL = ssOrginURL + svURL;
        if (StringHelper.hasValue(dstcode)) {
            URL += "?dstcode=" + dstcode;
        }
        if (StringHelper.hasValue(providerurl)) {
            URL += "&providerurl=" + providerurl;
        }
        String bodyData = createShareCitizenInfoBody(req, config);
        String body = createBody(bodyData);
        String response = "";
        String errResponse = "";
        csdldcLog.setBodyContent(body);
        try {
            logger.info("CSDLDC log URL: " + URL);
            logger.info("CSDLDC log body: " + body);
            if(type.equals("CMULGSP")){
                String token = this.getToken(config);
//                csdldcLog.setToken(token);
                logger.info("CSDLDC log token: " + token);
                HashMap<String,String> cmuHeader = new HashMap<>(){{put("Timestamp", String.valueOf(Instant.now().toEpochMilli()));}};
               response = MicroserviceExchange.soapRequest(this.restTemplate, URL,token, HttpMethod.POST, body,cmuHeader, String.class);
            }else{
                response = MicroserviceExchange.soapRequestNoAuth(this.restTemplate, URL, HttpMethod.POST, body, headers, String.class);
            }
            errResponse = response;
            //ghi log v2
            if(isSoapEnvelope(response)){
                csdldcLog.setResponse("Tra cứu thành công");
                csdldcLog.setStatus(1);
            }else{
                csdldcLog.setResponse(response);
//                csdldcLog.setSendUrl(URL);
//                csdldcLog.setToken(headers.toString());
//                csdldcLog.setBodyContent(body);
            }
            savedLog = csdldcLogRepository.save(csdldcLog);

            logger.info("CSDLDC log response: " + response);
            logger.info("Response from " + svURL + ": " + response);
            int start = response.indexOf("<ns1:CongDan>") + "<ns1:CongDan>".length();
            int end = response.indexOf("</ns1:CongDan>");
            String strTemp = response.substring(start, end);
            strTemp = strTemp.replaceAll("ns1:", "");

            JSONObject xmlJSONObj = XML.toJSONObject(strTemp);
            String json = xmlJSONObj.toString(PRETTY_PRINT_INDENT_FACTOR);
            ObjectMapper mapper = new ObjectMapper();
            ShareCitizenInfoResponseDto citizen = null;
            citizen = mapper.readValue(json, ShareCitizenInfoResponseDto.class);
            //KTM giới hạn trường dữ liệu tra cứu
            if(Objects.nonNull(req.getProcedureId())){
                try{
                    ShareCitizenInfoResponseDto ktmCitizen = new ShareCitizenInfoResponseDto();
                    if(isCheckCSDLDCSearch){
                        String procedureUrl = microservice.basepadUri("/procedure/" + req.getProcedureId()).toUriString();
                        //String procedureUrl = "http://localhost:8091/procedure/" + req.getProcedureId();
                        GetProcedureDetailDto procedure = MicroserviceExchange.get(restTemplate, procedureUrl, GetProcedureDetailDto.class);
                        if (Objects.nonNull(procedure.getLimitCheckCitizenFields())) {
                            logger.info("getCitizenInfo limitCheckCitizenFields");
                            var limitCheckCitizenFields = procedure.getLimitCheckCitizenFields();
                            logger.info("getCitizenInfo limitCheckCitizenFields: " + limitCheckCitizenFields);
                            List<String> allowed = new ArrayList<>();
                            for (int i = 0; i < limitCheckCitizenFields.length; i++) {
                                allowed.add(limitCheckCitizenFields[i]);
                                Field field = ShareCitizenInfoResponseDto.class.getDeclaredField(limitCheckCitizenFields[i].toString());

                                field.setAccessible(true);
                                Object value = field.get(citizen);
                                field.set(ktmCitizen, value);
                            }
                            citizen = ktmCitizen;
                            logger.info("getCitizenInfo ktmCitizen: " + citizen);
                            if (Objects.isNull(citizen.getChuHo()))
                                citizen.setChuHo(new ChuHoDto());
                        }
                    } else {
                        String procedureUrl = microservice.basepadUri("/procedure/" + req.getProcedureId()).toUriString();
                        //String procedureUrl = "http://localhost:8091/procedure/" + req.getProcedureId();
                        var procedure = new JSONObject(MicroserviceExchange.get(restTemplate, procedureUrl, String.class));
                        if (Objects.nonNull(procedure.get("limitCheckCitizenFields"))) {
                            var limitCheckCitizenFields = procedure.getJSONArray("limitCheckCitizenFields");
                            List<String> allowed = new ArrayList<>();
                            for (int i = 0; i < limitCheckCitizenFields.length(); i++) {
                                allowed.add(limitCheckCitizenFields.getString(i));
                                Field field = ShareCitizenInfoResponseDto.class.getDeclaredField(limitCheckCitizenFields.get(i).toString());

                                field.setAccessible(true);
                                Object value = field.get(citizen);
                                field.set(ktmCitizen, value);
                            }
                            citizen = ktmCitizen;
                            if (Objects.isNull(citizen.getChuHo()))
                                citizen.setChuHo(new ChuHoDto());
                        }
                    }
                }catch (Exception e){
                    logger.info("getCitizenInfo Exception getProcedureId: " + e.getMessage());
                }
            } else if (isCheckCSDLDCSearch && Objects.nonNull(listCSDLDCHCM) && listCSDLDCHCM.length > 0){
                ShareCitizenInfoResponseDto ktmCitizen = new ShareCitizenInfoResponseDto();
                for (int i = 0; i < listCSDLDCHCM.length; i++) {
                    Field field = ShareCitizenInfoResponseDto.class.getDeclaredField(listCSDLDCHCM[i].toString());

                    field.setAccessible(true);
                    Object value = field.get(citizen);
                    field.set(ktmCitizen, value);
                }
                citizen = ktmCitizen;
            }

            ShareCitizenInfoResponseDto finalCitizen = citizen;

            if(req.isAwait()){
                this.addMemberToFamily(finalCitizen);
            }else {
                CompletableFuture.runAsync(() -> {
                    // Thực hiện tác vụ bất đồng bộ ở đây
                    this.addMemberToFamily(finalCitizen);
                });
            }

            rs.setOriginInfo(citizen);
            rs.setInfo(citizen);
            try {
                if (Objects.nonNull(req.getEformId())) {
                    HashMap<String, Object> obj = getMapData(citizen, req.getEformId());
                    rs.setInfo(obj);
                } else if (Objects.nonNull(req.getMapfilldata())){
                    rs.setInfo(this.defaultMap(citizen));
                }
                // get orginInfo
                if(req.isGetOriginInfo()) {
                    try {
                        ShareCitizenOrginInfoResponseDto originInfo = GsonUtils.copyObject(citizen, ShareCitizenOrginInfoResponseDto.class);
                        rs.setOriginInfo(originInfo);
                        List<CitizenCaterogyResponseDto> caterogiesOrginInfo = getListIdByCode(citizen, originInfo);
                        remapValueOriginInfo(originInfo, caterogiesOrginInfo);
                        rs.setOriginInfo(originInfo);
                    } catch (Exception e) {
                        System.out.println("get orginInfo" + e.getMessage());
                        response = e.getMessage();
                    }
                }
            } catch (Exception ex) {
                logger.info("Exception getCitizenInfo 1: " + ex.getMessage());
                response = ex.getMessage();
            }
        } catch (Exception ex) {
            logger.info("Exception getCitizenInfo 2: " + ex.getMessage());
            response = ex.getMessage();
            //ghi log v2
            if(isSoapEnvelope(response)){
                csdldcLog.setResponse("Tra cứu thành công");
                csdldcLog.setStatus(1);
            }else{
                csdldcLog.setResponse(errResponse);
//                csdldcLog.setSendUrl(URL);
//                csdldcLog.setToken(headers.toString());
//                csdldcLog.setBodyContent(body);
            }
            savedLog = csdldcLogRepository.save(csdldcLog);
        }

        if(updateCmndCccd){
            if(rs != null && rs.getOriginInfo() != null) {
                Gson gson = new Gson();
                ObjectMapper objectMapper = new ObjectMapper();
                String jsonString = gson.toJson(rs.getOriginInfo());
                HashMap<String, String> resultMap = objectMapper.readValue(jsonString, HashMap.class);
                String cmnd = resultMap.get("SoCMND");
                String cccd = resultMap.get("SoDinhDanh");
                if(cmnd != null && cccd != null) {
                    updateCmndCccd(cmnd, cccd);
                }
            }
        }
        if(Objects.isNull(rs.getInfo()) || Objects.isNull(rs.getOriginInfo())){
            rs.setErrorMsg(response);
            rs.setErrorResponse(errResponse);
        }
        if (Objects.nonNull(savedLog)) rs.setLogId(savedLog.getId());
        return rs;
    }

    public ShareCitizenInfoResponseDto defaultMap(ShareCitizenInfoResponseDto req){
        ShareCitizenInfoResponseDto citizen = new ShareCitizenInfoResponseDto();
        DiaChiDto resident = new DiaChiDto();
        resident.setQuocGia(this.getCaterogyId("QuocTich",req.getQuocTich()));
        resident.setMaTinhThanh(this.getCaterogyId("ThuongTru.MaTinhThanh", req.getThuongTru().getMaTinhThanh()));
        resident.setMaQuanHuyen(this.getCaterogyId("ThuongTru.MaQuanHuyen", req.getThuongTru().getMaQuanHuyen()));
        resident.setMaPhuongXa(this.getCaterogyId("ThuongTru.MaPhuongXa", req.getThuongTru().getMaPhuongXa()));
        resident.setChiTiet(req.getThuongTru().getChiTiet());
        citizen.setThuongTru(resident);
        citizen.setGioiTinh(req.getGioiTinh());
        return citizen;
    }

    public String getLGSPAccessToken(IntegratedConfigurationDto config){

//        String accessKey = "5788aabce4b0836def367a27";
//        String secretKey = "A88jsF1K1FvgWcf9vWb30eCBz84UUiwRVSKdJiyG7v";
//        String appName = "vnpthcm";
//        String partnerCode = "000.00.15.H29";
//        String partnerCodeCus = "000.00.15.H29";

        String accessKey = config.getParametersValue("accessKey");
        String secretKey = config.getParametersValue("secretKey");
        String appName = config.getParametersValue("appName");
        String partnerCode = config.getParametersValue("partnerCode");
        String partnerCodeCus = config.getParametersValue("partnerCodeCus");

        //Kh?i t?o Authorization code
        String jsonString = new JSONObject()
                .put("AccessKey", accessKey)
                .put("SecretKey", secretKey)
                .put("AppName", appName)
                .put("PartnerCode", partnerCode)
                .put("PartnerCodeCus", partnerCodeCus)
                .toString().replaceAll(",", ",\n").replaceAll("\\{", "{\n").replaceAll("}", "\n}");

        String lgspaccesstoken = Base64.getEncoder().encodeToString(jsonString.getBytes());
        return lgspaccesstoken;
    }

    public String getToken(IntegratedConfigurationDto config) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(config.getParametersValue("accessKey"), config.getParametersValue("secretKey"));
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(headers);
        BusinessRegistrationTokenDto result = restTemplate.exchange(config.getParametersValue("ssOrginURL") + "/token?grant_type=client_credentials",
                HttpMethod.POST, request, BusinessRegistrationTokenDto.class).getBody();
        //Return body
        if (Objects.isNull(result.getAccess_token())) {
            return null;
        }
        return result.getAccess_token();
    }

    private RestTemplate getRestTemplate(){
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(this.tokenUrl + "/protocol/openid-connect/token");
        details.setClientId(this.clientId);
        details.setClientSecret(this.clientSecret);
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }

    public void updateCmndCccd (String cmnd, String cccd){
        try {
            UserCmndCccdDto userCmndCccdDto = new UserCmndCccdDto();
            userCmndCccdDto.setCmnd(cmnd);
            userCmndCccdDto.setCccd(cccd);
            String URL = microservice.humanUri("/user/update-cmnd-cccd").toUriString();
            logger.info("Start updateCmndCccd: URL=" + URL);
            String response = MicroserviceExchange.putJsonNoAuth(this.getRestTemplate(), URL, userCmndCccdDto, String.class);
            logger.info("End updateCmndCccd:" + response);
        }catch (Exception e){
            logger.info("Error updateCmndCccd:" + e.getMessage());
        }

    }

    public boolean isSoapEnvelope(String input) {
        if (input == null) {
            return false;
        }
        return input.startsWith(SOAP_PREFIX);
    }

    public CSDLDCLog createLogV2 (ShareCitizenInfoRequestDto req, String type, String username, Boolean isSpam, Boolean saveLog) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone("GMT+7"));
        CSDLDCLog csdldcLog = new CSDLDCLog();
        csdldcLog.setIsLogV2(true);
        csdldcLog.setIsSpam(isSpam);
        csdldcLog.setCreatedDate(calendar.getTime());
        csdldcLog.setFullname(req.getFullname());
        csdldcLog.setIdentityNumber(req.getIndentityNumber());
        csdldcLog.setBirthday(req.getBirthday());
        csdldcLog.setType(type);
        csdldcLog.setOfficerId(Context.getUserId().toString());
        csdldcLog.setOfficerUserName(username);
        csdldcLog.setStatus(0);
        csdldcLog.setLogHour(calendar.get(Calendar.HOUR_OF_DAY));
        csdldcLog.setLogDayOfWeek(calendar.get(Calendar.DAY_OF_WEEK));
        if (req.isEnableLogCSDLDCBDH()) {
          csdldcLog.setDossierId(req.getDossierId());
          csdldcLog.setDossierCode(req.getDossierCode());
        }
        if (saveLog) {
            return csdldcLogRepository.save(csdldcLog);
        } else {
            return csdldcLog;
        }
    }

    public String getPhoneNumber() {
        String URL = microservice.humanUri("/user/--oidc-format").toUriString();
        String phoneNumber = "";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
            HttpEntity<?> requests = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(URL, HttpMethod.GET, requests, String.class);
            org.codehaus.jackson.map.ObjectMapper objectMapper = new org.codehaus.jackson.map.ObjectMapper();
            JsonNode responseBodyJson = objectMapper.readTree(response.getBody());
            if (Objects.nonNull(responseBodyJson) && Objects.nonNull(responseBodyJson.get("phone_number")) && Objects.nonNull(responseBodyJson.get("phone_number").get(0))) {
                phoneNumber = this.changePhoneNumber(responseBodyJson.get("phone_number").get(0).asText());
            }
        } catch( Exception ex){

        }
        if(Objects.isNull(phoneNumber) || phoneNumber.isEmpty()){
            throw new DigoHttpException(10408, HttpServletResponse.SC_BAD_REQUEST);
        }
        return phoneNumber;
    }

    public String changePhoneNumber(String phoneNumber){
        if (phoneNumber.startsWith("+")) {
            phoneNumber = "0" + phoneNumber.substring(3);
        }
        return phoneNumber;
    }

    public boolean checkDossierRequest(Object dossierId, String dossierCode, Integer dossierRequestLimit) {
        if(dossierRequestLimit == -1){
            // Không giới hạn lượt truy cập
            return true;
        }
        
        Query query = new Query();
        Criteria criteria = new Criteria();
        List<Criteria> criteriaList = new ArrayList<Criteria>();
        criteriaList.add(where("dossierId").is(dossierId));
        criteriaList.add(where("dossierCode").is(dossierCode));

        if (criteriaList.size() > 0) {
            criteria = criteria.andOperator(criteriaList.toArray(new Criteria[criteriaList.size()]));
        }

        query.addCriteria(criteria);
        List<CSDLDCLog> logs = mongoTemplate.find(query, CSDLDCLog.class, "csdldcLog");
        
        if (!logs.isEmpty()) {
            if (logs.size() < dossierRequestLimit) {
                // số lần đã gọi nhỏ hơn dossierRequestLimit
                return true;
            } else {
                // số lần đã gọi lớn hơn dossierRequestLimit
                return false;
            }
        } else {
            return true;
        }
    }
}
