package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.KHCNLog;
import vn.vnpt.digo.adapter.dto.IdNameFileDto;
import vn.vnpt.digo.adapter.dto.khcn.*;
import vn.vnpt.digo.adapter.microservice.FilemanService;
import vn.vnpt.digo.adapter.microservice.PadmanService;
import vn.vnpt.digo.adapter.repository.KHCNLogRepository;
import vn.vnpt.digo.adapter.util.StringHelper;
import vn.vnpt.digo.adapter.util.Translator;

import java.util.*;
import java.util.Optional;

@Service
public class KHCNService {

    private static final Logger logger = LoggerFactory.getLogger(KHCNService.class);

    @Autowired
    private KHCNLogRepository khcnLogRepository;

    @Autowired
    private Translator translator;

    @Autowired
    private FilemanService filemanService;

    @Autowired
    private PadmanService padmanService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Value("${digo.khcn.security-key:default-khcn-key}")
    private String securityKey;

    @Value("${digo.khcn.enable:false}")
    private Boolean khcnEnable;

    /**
     * Đồng bộ hồ sơ KHCN
     */
    public KHCNResultDto dongBoHoSo(List<KHCNDossierDataDto> dossierDataDto, String securityKeyText, String requestPath) {
        String messageStr = translator.toLocale("lang.word.khcn-successful", "Đồng bộ hồ sơ KHCN thành công");
        KHCNResultDto result = new KHCNResultDto("1", messageStr);
        KHCNLog khcnLogResult = null;

        try {
            for (KHCNDossierDataDto dossier : dossierDataDto) {
                // Tạo log
                khcnLogResult = new KHCNLog();
                khcnLogResult.setApi(requestPath);
                khcnLogResult.setCallTime(new Date());
                khcnLogResult.setDossierCode(dossier.getCode());
                khcnLogResult.setNationCode(dossier.getNationCode());
                khcnLogResult.setAgencyCode(dossier.getAgencyCode());
                khcnLogResult.setProcedureCode(dossier.getProcedureCode());
                khcnLogResult.setSecurityKey(securityKeyText);
                khcnLogResult.setRequestBody(dossier);
                khcnLogResult.setStatus(1);
                khcnLogResult.setError(false);

                // Xử lý file đính kèm
                if (dossier.getAttachment() != null && !dossier.getAttachment().isEmpty()) {
                    List<KHCNDossierDataDto.FileAttachment> attachments = dossier.getAttachment();
                    for (KHCNDossierDataDto.FileAttachment attachment : attachments) {
                        if (attachment.getCanceled() == null || !attachment.getCanceled().equals(1)) {
                            if (attachment.getFiledata() != null && !attachment.getFiledata().isEmpty()) {
                                try {
                                    byte[] decodedBytes = Base64.getDecoder().decode(attachment.getFiledata());
                                    IdNameFileDto fileInfo = filemanService.uploadFileFromByteArrays(restTemplate, decodedBytes, attachment.getFilename());
                                    attachment.setFiledata(fileInfo.getId().toHexString());
                                    attachment.setSize(fileInfo.getSize());
                                } catch (Exception e) {
                                    logger.error("Error uploading file: " + e.getMessage());
                                }
                            }
                        }
                    }
                }

                // Lưu log
                khcnLogResult.setResponseBody(result);
                khcnLogRepository.save(khcnLogResult);

                // Gửi đến hệ thống xử lý (nếu enable)
                if (khcnEnable != null && khcnEnable) {
                    // Có thể gửi đến Kafka hoặc hệ thống khác
                    logger.info("Sending KHCN dossier to processing system: " + dossier.getCode());
                }
            }

        } catch (Exception e) {
            logger.error("Error processing KHCN dossier: " + e.getMessage(), e);
            if (khcnLogResult != null) {
                khcnLogResult.setResponseBody(e.getMessage());
                khcnLogResult.setStatus(0);
                khcnLogResult.setError(true);
                khcnLogRepository.save(khcnLogResult);
            }
            result.setStatus("0");
            result.setMessage("Lỗi xử lý hồ sơ KHCN: " + e.getMessage());
            return result;
        }

        return result;
    }

    /**
     * Cập nhật tiến độ hồ sơ KHCN
     */
    public KHCNResultDto capNhatTienDoHoSo(List<KHCNDossierTrackingDataDto> dossierTrackingDto, String securityKeyText, String requestPath) {
        String messageStr = translator.toLocale("lang.word.khcn-successful", "Cập nhật tiến độ hồ sơ KHCN thành công");
        KHCNResultDto result = new KHCNResultDto("1", messageStr);
        KHCNLog khcnLogResult = null;

        try {
            for (KHCNDossierTrackingDataDto tracking : dossierTrackingDto) {
                // Tạo log
                khcnLogResult = new KHCNLog();
                khcnLogResult.setApi(requestPath);
                khcnLogResult.setCallTime(new Date());
                khcnLogResult.setDossierCode(tracking.getCode());
                khcnLogResult.setNationCode(tracking.getNationCode());
                khcnLogResult.setAgencyCode(tracking.getAgencyCode());
                khcnLogResult.setProcedureCode(tracking.getProcedureCode());
                khcnLogResult.setSecurityKey(securityKeyText);
                khcnLogResult.setRequestBody(tracking);
                khcnLogResult.setStatus(1);
                khcnLogResult.setError(false);

                // Lưu log
                khcnLogResult.setResponseBody(result);
                khcnLogRepository.save(khcnLogResult);

                // Gửi đến hệ thống xử lý (nếu enable)
                if (khcnEnable != null && khcnEnable) {
                    logger.info("Sending KHCN tracking update to processing system: " + tracking.getCode());
                }
            }

        } catch (Exception e) {
            logger.error("Error updating KHCN dossier tracking: " + e.getMessage(), e);
            if (khcnLogResult != null) {
                khcnLogResult.setResponseBody(e.getMessage());
                khcnLogResult.setStatus(0);
                khcnLogResult.setError(true);
                khcnLogRepository.save(khcnLogResult);
            }
            result.setStatus("0");
            result.setMessage("Lỗi cập nhật tiến độ hồ sơ KHCN: " + e.getMessage());
            return result;
        }

        return result;
    }

    /**
     * Nhận hồ sơ KHCN (tương tự receiveRecord của DVCLT)
     */
    public KHCNHoSoResponseDto nhanHoSoKHCN(KHCNHoSoRequestDto request, String securityKey, String requestPath, String kafkaEnable) {
        String messageStr = translator.toLocale("lang.word.khcn-successful", "Nhận hồ sơ KHCN thành công");
        KHCNHoSoResponseDto result = new KHCNHoSoResponseDto("200", messageStr, "200", messageStr);
        KHCNLog khcnLogResult = null;

        try {
            // Tạo log
            khcnLogResult = new KHCNLog();
            khcnLogResult.setApi(requestPath);
            khcnLogResult.setCallTime(new Date());
            khcnLogResult.setDossierCode(request.getCode());
            khcnLogResult.setAgencyCode(request.getAgencyCode());
            khcnLogResult.setProcedureCode(request.getProcedureCode());
            khcnLogResult.setSecurityKey(securityKey);
            khcnLogResult.setRequestBody(request);
            khcnLogResult.setStatus(1);
            khcnLogResult.setError(false);

            // Xử lý file đính kèm
            if (request.getAttachment() != null && !request.getAttachment().isEmpty()) {
                List<KHCNHoSoRequestDto.FileAttachment> attachments = request.getAttachment();
                for (KHCNHoSoRequestDto.FileAttachment attachment : attachments) {
                    if (attachment.getCanceled() == null || !attachment.getCanceled().equals(1)) {
                        if (attachment.getFiledata() != null && !attachment.getFiledata().isEmpty()) {
                            try {
                                byte[] decodedBytes = Base64.getDecoder().decode(attachment.getFiledata());
                                IdNameFileDto fileInfo = filemanService.uploadFileFromByteArrays(restTemplate, decodedBytes, attachment.getFilename());
                                attachment.setFiledata(fileInfo.getId().toHexString());
                                attachment.setSize(fileInfo.getSize());
                            } catch (Exception e) {
                                logger.error("Error uploading file: " + e.getMessage());
                            }
                        }
                    }
                }
            }

            // Lưu log
            khcnLogResult.setResponseBody(result);
            khcnLogRepository.save(khcnLogResult);

            // Gửi đến hệ thống xử lý
            if (khcnEnable != null && khcnEnable) {
                logger.info("Sending KHCN dossier to processing system: " + request.getCode());
            }

            if ("true".equals(kafkaEnable)) {
                logger.info("Sending KHCN dossier to Kafka: " + request.getCode());
            }

        } catch (Exception e) {
            logger.error("Error receiving KHCN dossier: " + e.getMessage(), e);
            if (khcnLogResult != null) {
                khcnLogResult.setResponseBody(e.getMessage());
                khcnLogResult.setStatus(0);
                khcnLogResult.setError(true);
                khcnLogRepository.save(khcnLogResult);
            }
            result.setStatus("500");
            result.setStatusDescription("Lỗi hệ thống");
            result.setErrorCode("500");
            result.setErrorDescription(e.getMessage());
            return result;
        }

        return result;
    }

    /**
     * Lấy log KHCN
     */
    public Page<KHCNLog> getLog(String code, String nationCode, String api, Integer status, Boolean error, Pageable pageable) {
        return khcnLogRepository.findByFilters(code, nationCode, api, status, error, pageable);
    }

    /**
     * Lấy log KHCN theo ID
     */
    public KHCNLog getLogFull(ObjectId id) {
        return khcnLogRepository.findById(id).orElse(null);
    }

    /**
     * Lưu body logs HTTP
     */
    public void bodyLogsHTTP(KHCNHoSoRequestDto request) {
        try {
            String databaseName = "khcnBodyLogs";
            Query query = new Query(Criteria.where("code").is(request.getCode()));
            Map<String, Object> existingDossier = mongoTemplate.findOne(query, Map.class, databaseName);
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> dataInputMap = objectMapper.convertValue(request, Map.class);
            
            if (existingDossier != null) {
                Update update = new Update();
                dataInputMap.forEach(update::set);
                mongoTemplate.updateFirst(query, update, databaseName);
            } else {
                mongoTemplate.save(request, databaseName);
            }
        } catch (Exception e) {
            logger.error("Error saving KHCN body logs: " + e.getMessage());
        }
    }

    /**
     * Cập nhật trạng thái hồ sơ KHCN (tương tự capNhatTrangThaiHoSoDVCLTHoTich)
     */
    public Object capNhatTrangThaiHoSoKHCN(KHCNPostDto request, String requestPath) {
        KHCNResultDto result = new KHCNResultDto("1", "Cập nhật trạng thái thành công");
        KHCNLog khcnLogResult = null;

        try {
            // Tạo log
            khcnLogResult = new KHCNLog();
            khcnLogResult.setApi(requestPath);
            khcnLogResult.setCallTime(new Date());
            khcnLogResult.setDossierCode(request.getMaHoSo());
            khcnLogResult.setNationCode(request.getSoHoSoLT());
            khcnLogResult.setProcedureCode(request.getMaTTHC());
            khcnLogResult.setRequestBody(request);
            khcnLogResult.setStatus(1);
            khcnLogResult.setError(false);

            // Lưu log
            khcnLogResult.setResponseBody(result);
            khcnLogRepository.save(khcnLogResult);

            logger.info("Updated KHCN dossier status: " + request.getMaHoSo() + " - Status: " + request.getTrangThai());

        } catch (Exception e) {
            logger.error("Error updating KHCN dossier status: " + e.getMessage(), e);
            if (khcnLogResult != null) {
                khcnLogResult.setResponseBody(e.getMessage());
                khcnLogResult.setStatus(0);
                khcnLogResult.setError(true);
                khcnLogRepository.save(khcnLogResult);
            }
            result.setStatus("0");
            result.setMessage("Lỗi cập nhật trạng thái hồ sơ KHCN: " + e.getMessage());
        }

        return result;
    }

    /**
     * Xử lý hồ sơ lỗi KHCN
     */
    public KHCNResultDto dongBoHoSoLoi(ArrayList<String> listId) {
        Integer check = 0;
        for (String id : listId) {
            try {
                ObjectId objID = new ObjectId(id);
                Optional<KHCNLog> khcnLogOpt = khcnLogRepository.findById(objID);
                if (khcnLogOpt.isPresent()) {
                    KHCNLog khcnLog = khcnLogOpt.get();
                    // Xử lý lại hồ sơ lỗi
                    logger.info("Reprocessing KHCN dossier error: " + id);
                    check++;
                }
            } catch (Exception e) {
                logger.error("Error reprocessing KHCN dossier: " + e.getMessage());
            }
        }
        return new KHCNResultDto(check.toString(), String.valueOf(listId.size()));
    }

    /**
     * Xử lý cập nhật trạng thái lỗi KHCN
     */
    public KHCNResultDto capNhatTienDoHoSoLoi(ArrayList<String> listId) {
        Integer check = 0;
        for (String id : listId) {
            try {
                ObjectId objID = new ObjectId(id);
                Optional<KHCNLog> khcnLogOpt = khcnLogRepository.findById(objID);
                if (khcnLogOpt.isPresent()) {
                    KHCNLog khcnLog = khcnLogOpt.get();
                    // Xử lý lại cập nhật trạng thái lỗi
                    logger.info("Reprocessing KHCN tracking error: " + id);
                    check++;
                }
            } catch (Exception e) {
                logger.error("Error reprocessing KHCN tracking: " + e.getMessage());
            }
        }
        return new KHCNResultDto(check.toString(), String.valueOf(listId.size()));
    }

    /**
     * Lấy data body KHCN
     */
    public Object getDataBodyKHCN(String code) {
        try {
            String databaseName = "khcnBodyLogs";
            Query query = new Query(Criteria.where("code").is(code));
            return mongoTemplate.findOne(query, Map.class, databaseName);
        } catch (Exception e) {
            logger.error("Error getting KHCN body data: " + e.getMessage());
            return null;
        }
    }
}
