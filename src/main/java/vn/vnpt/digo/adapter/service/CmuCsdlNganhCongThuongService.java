package vn.vnpt.digo.adapter.service;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.*;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.CmuLogFaceRecognition;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.cmu.CongDanResponseDto;
import vn.vnpt.digo.adapter.dto.cmu.CsdlNganhCongThuongRequestDto;
import vn.vnpt.digo.adapter.dto.cmu.CsdlNganhCongThuongResponseDto;
import vn.vnpt.digo.adapter.dto.cmu.FaceRecognitionResponseDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 *
 * <AUTHOR>
 */

@Service
public class CmuCsdlNganhCongThuongService {

    Logger logger = LoggerFactory.getLogger(PaymentPlatformService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private Microservice microservice;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private RestTemplate restTemplate;

    private final ObjectId configIdTemp = new ObjectId("65f7f36588e2f03492cd95c2");

    @Autowired
    private MongoTemplate mongoTemplate;

    @Value(value = "${cmu.csdlNganhCT.agencyId}")
    private String agencyId;


    public CsdlNganhCongThuongResponseDto tiepNhanHoSo (CsdlNganhCongThuongRequestDto params, String requestPath){
        logger.info("DIGO-Info: begin tiepNhanHoSo: "+params.getMaHoSo());
        // Get config
        String configId = params.getConfigId();

        IntegratedConfigurationDto config;
        var result = new CsdlNganhCongThuongResponseDto();
        result.setStatus(false);
        result.setTotal(0);
        result.setMessage("Lỗi kết nối với hệ thống CSDL Ngành Công Thương. Vui lòng liên hệ quản trị viên.");

        var responseLog = new Object();
        String exception = "";

        if (Objects.nonNull(configId)) {
            config = configurationService.getConfig(new ObjectId(configId));
        } else {
            config = configurationService.getConfig(this.configIdTemp);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String endpoint = config.getParametersValue("tiep-nhan-ho-so");
            HttpHeaders headers = new HttpHeaders();
            HttpEntity<?> entity = new HttpEntity<>(params, headers);


            try {
                ResponseEntity<CsdlNganhCongThuongResponseDto> response = restTemplate.exchange(endpoint, HttpMethod.POST, entity, CsdlNganhCongThuongResponseDto.class);
                responseLog = response;
                if (response.getStatusCodeValue() == 201 && response.getBody() != null) {
                    result = response.getBody();
                }
            } catch (Exception ex) {
                result.setTotal(0);
                result.setMessage(ex.getMessage());
                result.setStatus(false);
            }
        } catch (Exception ex) {
            result.setTotal(0);
            result.setMessage(ex.getMessage());
            result.setStatus(false);
        }


        return result;
    }

    public CsdlNganhCongThuongResponseDto capNhatTrangThai (CsdlNganhCongThuongRequestDto params, String requestPath){
        logger.info("DIGO-Info: begin capNhatTrangThai: "+params.getMaHoSo());
        // Get config
        String configId = params.getConfigId();

        IntegratedConfigurationDto config;
        var result = new CsdlNganhCongThuongResponseDto();
        result.setStatus(false);
        result.setTotal(0);
        result.setMessage("Lỗi kết nối với hệ thống CSDL Ngành Công Thương. Vui lòng liên hệ quản trị viên.");

        var responseLog = new Object();
        String exception = "";

        if (Objects.nonNull(configId)) {
            config = configurationService.getConfig(new ObjectId(configId));
        } else {
            config = configurationService.getConfig(this.configIdTemp);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String endpoint = config.getParametersValue("cap-nhat-trang-thai");
            HttpHeaders headers = new HttpHeaders();
            HttpEntity<?> entity = new HttpEntity<>(params, headers);


            try {
                ResponseEntity<CsdlNganhCongThuongResponseDto> response = restTemplate.exchange(endpoint, HttpMethod.POST, entity, CsdlNganhCongThuongResponseDto.class);
                responseLog = response;
                if (response.getStatusCodeValue() == 201 && response.getBody() != null) {
                    result = response.getBody();
                }
            } catch (Exception ex) {
                result.setTotal(0);
                result.setMessage(ex.getMessage());
                result.setStatus(false);

            }
        } catch (Exception ex) {
            result.setTotal(0);
            result.setMessage(ex.getMessage());
            result.setStatus(false);
        }


        return result;
    }

    public List<CsdlNganhCongThuongResponseDto.DanhMucThuTucRes> getDanhMucThuTucHanhChinh() {
        logger.info("DIGO-Info: begin getDanhMucThuTucHanhChinh !");

        List<CsdlNganhCongThuongResponseDto.DanhMucThuTucResponse> result = new ArrayList<>();
        List<CsdlNganhCongThuongResponseDto.DanhMucThuTucRes> res = new ArrayList<>();
        try {

            if (agencyId == null || agencyId.isEmpty()) {
                agencyId = "637d7434f217d52a06d6d0f4";
            }

//            var urlContent = "procedure/--search?sort=createdDate,desc&page=0&size=50&spec=page&keyword=&agency-id="+agencyId+"&sector-id=&agency-level-id=&procedure-level-id=&status=-1&common-use=false";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + Context.getJwtAuthenticationTokenValue());
            HttpEntity<String> entity = new HttpEntity<>("body", headers);


            var urlContent ="/procedure/--cmu-get-by-agency-id?agency-id="+agencyId;
//            String url = "http://localhost:8069" + urlContent;
            String url = microservice.basepadUri(urlContent).toUriString();
            ResponseEntity<List<CsdlNganhCongThuongResponseDto.DanhMucThuTucResponse>> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<List<CsdlNganhCongThuongResponseDto.DanhMucThuTucResponse>>() {}
            );
             result = responseEntity.getBody();
             for (CsdlNganhCongThuongResponseDto.DanhMucThuTucResponse item : result) {
                 CsdlNganhCongThuongResponseDto.DanhMucThuTucRes itemRes = new CsdlNganhCongThuongResponseDto.DanhMucThuTucRes();
                 itemRes.setMaThuTuc(item.getCode());
                 itemRes.setTenThuTuc(item.getTranslate().get(0).getName());
                 itemRes.setMucDo(item.getLevel().getName().get(0).getName());
                 itemRes.setCapThuTuc(item.getAgencyLevel().get(0).getName().get(0).getName());
                 itemRes.setMaLinhVuc(item.getSector().getCode());
                 itemRes.setTenLinhVuc(item.getSector().getName().get(0).getName());
                 itemRes.setThoiGianGiaiQuyet(item.getTranslate().get(0).getProcessingTime());
                 itemRes.setDieuKienThucHien(item.getTranslate().get(0).getRequirement());
                 itemRes.setThanhPhanHoSo(item.getTranslate().get(0).getDossierComponent());
                 itemRes.setCachThucThucHien(item.getTranslate().get(0).getImplementationMethod());
                 itemRes.setDoiTuongThucHien(item.getTranslate().get(0).getImplementer());
                 itemRes.setKetQuaThucHien(item.getTranslate().get(0).getResults());
                 itemRes.setCoQuanThucHien(item.getTranslate().get(0).getAgencyAccept());
                 itemRes.setTrinhTuThucHien(item.getTranslate().get(0).getSteps());
                 res.add(itemRes);
             }
            return res;
        } catch (Exception e) {

        }

        return res;
    }
}
