package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.sun.xml.bind.marshaller.CharacterEscapeHandler;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.dto.AffectedMessageDto;
import vn.vnpt.digo.adapter.dto.CivilStatusJusticeParamsDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.SidNameDto;
import vn.vnpt.digo.adapter.dto.event_log.PostEventLogDto;
import vn.vnpt.digo.adapter.dto.IntegrationParamsDto;
import vn.vnpt.digo.adapter.dto.minhtue.TokenResDto;
import vn.vnpt.digo.adapter.dto.minhtue.civilstatus.*;
import vn.vnpt.digo.adapter.dto.minhtue.transportation.TransportationDossierDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.CivilStatusJusticeResponse;
import vn.vnpt.digo.adapter.repository.HistorySendTanDanDossierRepository;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.pojo.IntegratedService;
import java.text.DateFormat;
import java.util.List;
@Service
@Component
public class LGSPMinhTueService {
    private ObjectId serviceId = new ObjectId("5f7c16069abb62f511891037");
    Logger logger = LoggerFactory.getLogger(LGSPMinhTueService.class);

    @Autowired
    private IntegratedLogsService integratedLogsService;

    @Value(value = "${digo.oidc.client-id}")
    private String clientId;

    @Value(value = "${digo.oidc.client-secret}")
    private String clientSecret;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String igateTokenUrl;

    //lgsp minh tue 
    @Value(value = "${lgspminhtue.kgg.configId}")
    private String lgspKGGConfigId;

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private HistorySendTanDanDossierRepository historySendTanDanDossierRepository;
    
    private final ObjectId SERVICE_ID = new ObjectId("634fa08d1802493a28dd8f72");
    
    IntegratedConfigurationDto config;

    @Autowired
    private EventLogService eventLogService;

    @Autowired
    private MongoTemplate mongoTemplate;

//    private TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) throws Exception {
//        URL url = new URL(tokenUrl);
//        HttpURLConnection conn = (HttpURLConnection)url.openConnection();
//        String strConsumer = consumerKey + ":" + consumerSecret;
//        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
//        String strBase64Consumer = new String(base64Consumer);
//        String params = "grant_type=client_credentials";
//        String auth = "Basic " + strBase64Consumer;
//        conn.setRequestMethod("POST");
//        conn.setRequestProperty("ContentType", "application/x-www-form-urlencoded");
//        conn.setRequestProperty("Authorization", auth);
//        conn.setDoOutput(true);
//        DataOutputStream outStream = new DataOutputStream(conn.getOutputStream());
//        outStream.writeBytes(params);
//        outStream.flush();
//        outStream.close();
//        if (conn.getResponseCode() != 200) {
//            throw new Exception("Error " + conn.getResponseCode() + ": Failed to connect to Adapter");
//        } else {
//            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
//            StringBuffer responsedContent = new StringBuffer();
//
//            String bufferedLine;
//            while((bufferedLine = bufferedReader.readLine()) != null) {
//                responsedContent.append(bufferedLine);
//            }
//
//            bufferedReader.close();
//            conn.disconnect();
//            Gson gson = new Gson();
//            TokenResDto token = gson.fromJson(responsedContent.toString(), TokenResDto.class);
//            return token;
//        }
//    }

    public TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) {
        String strConsumer = consumerKey + ":" + consumerSecret;
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(tokenUrl);
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();

        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(auth);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST, request, Object.class);
            logger.info("Http result:");
            System.out.println(result);
            TokenResDto token = GsonUtils.copyObject(result.getBody(), TokenResDto.class);
            return token;
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
            throw new DigoHttpException(11003, new String[]{"get token LGSP Minh Tue:", e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    public CivilDossierResDto registerCivil(CivilDossierMinhTueDto req) throws Exception {
        logger.info("---------------------------START_GET_REGISTER_CIVIL_MINHTUE---------------------------");


        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        } else {
            config = configurationService.getConfig(req.getAgencyId(), req.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String registerCivilUrl = config.getParametersValue("registerCivilUrl");
        TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);
        String jsonEform = new Gson().toJson(req.getDataEform());

        if (Constant.MODULE_BIRTH.equals(req.getModule())) {
            BirthDossierDto hsks = new Gson().fromJson(jsonEform, BirthDossierDto.class);
            req.setData(serializeRegisterCivil(hsks));
        } else if (Constant.MODULE_MARRIED.equals(req.getModule())) {
            MarriedDossierDto hskh = new Gson().fromJson(jsonEform, MarriedDossierDto.class);
            req.setData(serializeRegisterCivil(hskh));
        } else if (Constant.MODULE_MARRIED_STATUS.equals(req.getModule())) {
            MarriedSttDossierDto hs = new Gson().fromJson(jsonEform, MarriedSttDossierDto.class);
            req.setData(serializeRegisterCivil(hs));
        } else if (Constant.MODULE_DAD_MOM_CHILD.equals(req.getModule())) {
            DadMomChildDossierDto hs = new Gson().fromJson(jsonEform, DadMomChildDossierDto.class);
            req.setData(serializeRegisterCivil(hs));
        } else if (Constant.MODULE_DIVORCE.equals(req.getModule())) {
            DivorceDossierDto hs = new Gson().fromJson(jsonEform, DivorceDossierDto.class);
            req.setData(serializeRegisterCivil(hs));
        } else if (Constant.MODULE_GUARDIAN.equals(req.getModule())) {
            GuardianDossierDto hs = new Gson().fromJson(jsonEform, GuardianDossierDto.class);
            req.setData(serializeRegisterCivil(hs));
        } else if (Constant.MODULE_END_GUARDIAN.equals(req.getModule())) {
            EndGuardianDossierDto hs = new Gson().fromJson(jsonEform, EndGuardianDossierDto.class);
            req.setData(serializeRegisterCivil(hs));
        } else if (Constant.MODULE_DEAD.equals(req.getModule())) {
            DeadDossierDto hs = new Gson().fromJson(jsonEform, DeadDossierDto.class);
            req.setData(serializeRegisterCivil(hs));
        }
        else {
            logger.error("No module found for LGSP Minh Tue");
            throw new DigoHttpException(11003, new String[]{"No module found for LGSP Minh Tue"}, HttpServletResponse.SC_NOT_FOUND);
        }

        return dangKyHoTich(registerCivilUrl, token.getAccessToken(), req);
    }

    private CivilDossierResDto dangKyHoTich(String apiUrl, String access_token, CivilDossierMinhTueDto dangKyHoTich) throws IOException {
        CivilDossierResDto objreturn = new CivilDossierResDto();

        try {
            URL url = new URL(apiUrl);
            HttpURLConnection request = (HttpURLConnection)url.openConnection();
            Gson gson = (new GsonBuilder()).setDateFormat("yyyy-MM-dd'T'HH:mm:ss").create();
            Format formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            String pattern = "yyyy-MM-dd'T'HH:mm:ss";
            DateFormat df = new SimpleDateFormat(pattern);
            String jsonObject = "{\"maDonVi\":\"" + dangKyHoTich.getMaDonVi() + "\",\"module\":\"" + dangKyHoTich.getModule() + "\",\"maHoSo\":\"" + dangKyHoTich.getMaHoSo() + "\",\"ngayTiepNhan\":\"" + df.format(dangKyHoTich.getNgayTiepNhan()) + "\",\"data\":\"" + dangKyHoTich.getData().trim() + "\"}";
            String auth = "Bearer " + access_token;
            request.setRequestMethod("POST");
            request.setRequestProperty("Content-Type", "application/json");
            request.setRequestProperty("Accept-Charset", "UTF-8");
            request.setRequestProperty("Authorization", auth);
            request.setDoOutput(true);
            request.setDoInput(true);
            OutputStream outStream = request.getOutputStream();
            outStream.write(jsonObject.getBytes());
            outStream.flush();
            outStream.close();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
            StringBuffer responsedContent = new StringBuffer();

            String bufferedLine;
            while((bufferedLine = bufferedReader.readLine()) != null) {
                responsedContent.append(bufferedLine);
            }

            bufferedReader.close();
            request.disconnect();
            JsonElement jsonElement = (JsonElement)gson.fromJson(responsedContent.toString(), JsonElement.class);
            JsonObject jsonObj = jsonElement.getAsJsonObject();
            JsonObject jsonObj2 = gson.fromJson(jsonObj.get("receiveRecordResponse").getAsString(), JsonElement.class).getAsJsonObject();
            int sts = jsonObj2.get("status").getAsInt();
            if (sts == 0) {
                objreturn.setStatus(sts);
                objreturn.setStatusDescription(jsonObj2.get("statusDescription").getAsString());
                objreturn.setErrorCode(jsonObj2.get("errorCode").getAsString());
                objreturn.setErrorDescription(jsonObj2.get("errorDescription").getAsString());
            } else {
                objreturn.setStatus(sts);
                objreturn.setValue("");
                objreturn.setStatusDescription(jsonObj2.get("statusDescription").getAsString());
                objreturn.setErrorCode("");
                objreturn.setErrorDescription("");
                // set response success
                Map<String, String> map = new HashMap<>();
                map.put("statusMessage", jsonObj2.get("statusDescription").toString());
                map.put("statusCode", String.valueOf(sts));
                updateDossierStatus(map, dangKyHoTich.getMaHoSo());
                //
            }
        } catch (Exception var20) {
            logger.error(var20.getMessage());
            objreturn.setStatus(-1);
            objreturn.setValue(dangKyHoTich.getData());
            objreturn.setErrorDescription(var20.getMessage());
            throw var20;
        }

        try {
            //bo sung luu logs
            if(config.getId().toHexString().equals(lgspKGGConfigId)) {
                //luu log
                IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                IntegratedService serviceNew = new IntegratedService(this.SERVICE_ID, "LGSP Minh Tue");
                configNew.setService(serviceNew);

                CivilDossierMinhTueKGGDto objLogs = setLogsDossierKGG(dangKyHoTich);

                integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(), dangKyHoTich.getMaHoSo(), dangKyHoTich.getModule()), 0, objreturn.getErrorDescription(), new Gson().toJson(objLogs));
            }
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
        }

        return objreturn;
    }
    public static CivilDossierMinhTueKGGDto setLogsDossierKGG(CivilDossierMinhTueDto obj) {
        CivilDossierMinhTueKGGDto objreturn = new CivilDossierMinhTueKGGDto();
        objreturn.setMaDonVi(obj.getMaDonVi());
        objreturn.setModule(obj.getModule());
        objreturn.setMaHoSo(obj.getMaHoSo());
        objreturn.setNgayTiepNhan(obj.getNgayTiepNhan());
        objreturn.setConfigId(obj.getConfigId().toHexString());
        objreturn.setAgencyId(obj.getAgencyId().toHexString());
        objreturn.setSubsystemId(obj.getSubsystemId().toHexString());
        objreturn.setDataEform(obj.getDataEform());
        return objreturn;
    }

    public static <T> String serializeRegisterCivil(T dataToSerialize) throws JAXBException {
        try {
//            System.setProperty("javax.xml.bind.context.factory", "org.eclipse.persistence.jaxb.JAXBContextFactory");
            JAXBContext jaxbContext = JAXBContext.newInstance(new Class[]{dataToSerialize.getClass()});
            Marshaller jaxbMarshaller = jaxbContext.createMarshaller();
            jaxbMarshaller.setProperty("jaxb.fragment", Boolean.TRUE);
            jaxbMarshaller.setProperty("jaxb.encoding", "UTF-8");
            jaxbMarshaller.setProperty("jaxb.formatted.output", Boolean.FALSE);
            jaxbMarshaller.setProperty("com.sun.xml.bind.marshaller.CharacterEscapeHandler",
                    new CharacterEscapeHandler() {
                        @Override
                        public void escape(char[] ch, int start, int length, boolean isAttVal, Writer writer)
                                throws IOException {
                            writer.write(ch, start, length);
                        }
                    });
            StringWriter sw = new StringWriter();
            jaxbMarshaller.marshal(dataToSerialize, sw);
            String xmlContent = "<hotich>".concat(sw.toString()).concat("</hotich>");
            return xmlContent;
        } catch (JAXBException var7) {
            throw var7;
        }
    }

    public AffectedMessageDto addLogDatabase(HttpServletRequest request,Map<String, Object> body,String code, String serviceId,Boolean Status, String message){
        AffectedMessageDto affectedMessageDto = new AffectedMessageDto();
        try {

            logger.info("---data base1 insert--");
            //region event log init
            PostEventLogDto event = new PostEventLogDto();
            event.setRequestAdapter(request, body, "ad");
            if (!serviceId.isEmpty() && serviceId != null){
                this.serviceId = new ObjectId(serviceId);
            }
            event.setServiceId(this.serviceId);
            event.setKey(new SidNameDto("Code", code));
            event.setStatus(Status);
            event.setErrMsg(message);
            ObjectId id = eventLogService.addNew(event).getId();
            String getConlectionnameData = eventLogService.getConlectionnameData(this.serviceId);
            affectedMessageDto.setAffectedRows(1);
            affectedMessageDto.setMessage("ID: " + id.toHexString() + " getConlectionnameData: " + getConlectionnameData);
            logger.info("---data end--");
        } catch (Exception e) {
            affectedMessageDto.setAffectedRows(0);
            logger.info("**DATA ERR HTTP **" + code + "serviceId"+ this.serviceId);
            affectedMessageDto.setMessage("**DATA ERR HTTP **" + code + "serviceId"+ this.serviceId + "err" + e.getMessage());
            logger.info(e.getMessage());
        }
        return affectedMessageDto;
    }

    public CivilDossierResDto writeLogHTTP(HttpServletRequest request, CivilDossierMinhTueDto body, String code, ObjectId logID) {
        try {
            logger.info("---data base insert--");
            String database = eventLogService.getConlectionnameData(this.serviceId);
            logger.info("---database-- " + database);
            logger.info("---code-- " + code);
            Query query = new Query(Criteria.where("key.name").is(code));
            Map<String, Object> existingDossier = mongoTemplate.findOne(query, Map.class, database);
            logger.info("---existingDossier-- " + existingDossier);
            if (Objects.isNull(existingDossier)){
                Query query1 = new Query(Criteria.where("status").is(false)).limit(1);
                List<Map> existingDossierList1 = mongoTemplate.find(query1, Map.class, database);
                if (!existingDossierList1.isEmpty() && existingDossierList1.size() > 0) {
                    // Get the first document
                    Map<String, Object> existingDossier1 = existingDossierList1.get(0);

                    // Update the req.body with the new value for key.name
                    HashMap<String, String> formattedl1 = new HashMap<>();
                    formattedl1.put("_id", "Code");
                    formattedl1.put("name", code.toString());
                    existingDossier1.put("key", formattedl1);
                    existingDossier1.remove("_id");
                    // Insert the updated data back into the database
                    try {
                        String id =  mongoTemplate.save(existingDossier1, database).get("_id").toString();
                        logger.info("_id "+ id);
                        logger.info("*** update start ***");
                        Query queryDB = new Query(Criteria.where("_id").is(new ObjectId(id)));
                        Update update = new Update()
                                .set("req.body", body);
                        mongoTemplate.updateFirst(queryDB, update, database);
                        logger.info("*** update end ***");
                    }catch (Exception e){
                        logger.info(e.getMessage());
                    }

                }
            }
        } catch (Exception e) {
        }
        //region event log init
        PostEventLogDto event = new PostEventLogDto();
        event.setRequestAdapter(request, body, "ad");
        //event.setRes(null);
        event.setPid(logID);
        event.setServiceId(this.serviceId);
        event.setKey(new SidNameDto("Code", code));
               //endregion event log init
        try {
            CivilDossierResDto response = new CivilDossierResDto();
            response = registerCivil(body);
            //region event log success (status)
            event.setStatus(true);
            event.setErrMsg(response.toString());
            eventLogService.addNew(event);
            //endregion event log success (status, message)
            logger.info("DIGO-Response: " + response.toString());
            return response;
        } catch (Exception e) {
            //region event log fail (status, message)
            event.setStatus(false);
            event.setErrMsg(e.getMessage());
            eventLogService.addNew(event);
            //endregion event log fail (status, message)
            // set up reponse error
            Map<String, String> map = new HashMap<>();
            map.put("statusMessage", translator.toLocale("lang.word.callLGSP"));
            map.put("statusCode", "-9999");
            updateDossierStatus(map,code);
            return new CivilDossierResDto();
        }
    }

    public AffectedMessageDto updateDossierStatus(Map response , String code){
        String updateUrl = microservice.padmanUri("/dossierAutoSync/" + code + "/status").toUriString();
//        String updateUrl = "http://localhost:8080/dossierAutoSync/"+code+"/status";
        logger.error("-----Update status dossier: " + updateUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(Context.getJwtAuthenticationTokenValue());
        //Create request with body
        HttpEntity<?> request = new HttpEntity<>(response, headers);
        AffectedMessageDto result = restTemplate.exchange(updateUrl, HttpMethod.PUT, request, AffectedMessageDto.class).getBody();
        return result;
    }

    // GTVT
    public ResponseEntity<Object> getListDossierTransportation(IntegrationParamsDto integrationParams, TransportationDossierDto body) throws Exception {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(integrationParams.getConfigId())) {
            config = configurationService.getConfig(integrationParams.getConfigId());
        } else {
            config = configurationService.getConfig(integrationParams.getAgencyId(), integrationParams.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String getListDossierTransportationUrl = config.getParametersValue("getListDossierTransportationUrl");
        String transportationGovAgencyCode = config.getParametersValue("transportationGovAgencyCode");
        String apiKey = config.getParametersValue("transportationApiKey");
        TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);

        try {
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(getListDossierTransportationUrl);
            uriBuilder.queryParam("GovAgencyCode", transportationGovAgencyCode);
            uriBuilder.queryParam("Start", body.getStart());
            uriBuilder.queryParam("End", body.getEnd());
            uriBuilder.queryParam("MauBien", body.getMauBien());
            uriBuilder.queryParam("FromDate", body.getFromDate());
            uriBuilder.queryParam("ToDate", body.getToDate());
            uriBuilder.queryParam("MaThuTuc", body.getMauBien());
            uriBuilder.queryParam("Status", body.getStatus());
            uriBuilder.queryParam("ReceiveFromDate", body.getReceiveFromDate());
            uriBuilder.queryParam("ReceiveToDate", body.getReceiveToDate());
            uriBuilder.queryParam("ReleaseFromDate", body.getReleaseFromDate());
            uriBuilder.queryParam("ReleaseToDate", body.getReleaseToDate());
            uriBuilder.queryParam("FinishFromDate", body.getFinishFromDate());

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token.getAccessToken());
            headers.add("MaDonVi", transportationGovAgencyCode);
            headers.add("ApiKey", apiKey);
            HttpEntity<?> request = new HttpEntity<>(headers);
            ResponseEntity<Object> result;
            result = restTemplate.exchange(
                    uriBuilder.toUriString(),
                    HttpMethod.GET, request, Object.class);
            return result;
        } catch (Exception e) {
            logger.info("Error calling getListDossierTransportation: " + e.getMessage());
            throw new DigoHttpException(11003, new String[]{"getListDossierTransportation error: " + e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    public ResponseEntity<Object> searchDossierTransportation(IntegrationParamsDto integrationParams, String dossierTransportationId) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(integrationParams.getConfigId())) {
            config = configurationService.getConfig(integrationParams.getConfigId());
        } else {
            config = configurationService.getConfig(integrationParams.getAgencyId(), integrationParams.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String searchDossierTransportationUrl = config.getParametersValue("searchDossierTransportationUrl");
        String transportationGovAgencyCode = config.getParametersValue("transportationGovAgencyCode");
        String apiKey = config.getParametersValue("transportationApiKey");
        TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);

        try {
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(searchDossierTransportationUrl);
            uriBuilder.queryParam("MaHoSo", dossierTransportationId);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token.getAccessToken());
            headers.add("MaDonVi", transportationGovAgencyCode);
            headers.add("ApiKey", apiKey);
            HttpEntity<?> request = new HttpEntity<>(headers);
            ResponseEntity<Object> result;
            result = restTemplate.exchange(
                    uriBuilder.toUriString(),
                    HttpMethod.GET, request, Object.class);
            return result;
        } catch (Exception e) {
            logger.info("Error calling searchDossierTransportation: " + e.getMessage());
            throw new DigoHttpException(11003, new String[]{"searchDossierTransportation error: " + e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    public ResponseEntity<Object> getStatisticalTransportation(IntegrationParamsDto integrationParams, int month, int year) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(integrationParams.getConfigId())) {
            config = configurationService.getConfig(integrationParams.getConfigId());
        } else {
            config = configurationService.getConfig(integrationParams.getAgencyId(), integrationParams.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String getStatisticalTransportation = config.getParametersValue("getStatisticalTransportation");
        String transportationGovAgencyCode = config.getParametersValue("transportationGovAgencyCode");
        String apiKey = config.getParametersValue("transportationApiKey");
        TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);

        try {
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(getStatisticalTransportation);
            uriBuilder.queryParam("MaDonVi", transportationGovAgencyCode);
            uriBuilder.queryParam("Thang", month);
            uriBuilder.queryParam("Nam", year);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token.getAccessToken());
            headers.add("MaDonVi", transportationGovAgencyCode);
            headers.add("ApiKey", apiKey);
            HttpEntity<?> request = new HttpEntity<>(headers);
            ResponseEntity<Object> result;
            result = restTemplate.exchange(
                    uriBuilder.toUriString(),
                    HttpMethod.GET, request, Object.class);
            return result;
        } catch (Exception e) {
            logger.info("Error calling getStatisticalTransportation: " + e.getMessage());
            throw new DigoHttpException(11003, new String[]{"getStatisticalTransportation error: " + e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    // GTVT QTI
    public ResponseEntity<Object> getListDossierTransportationQTI (IntegrationParamsDto integrationParams, TransportationDossierDto body) throws Exception {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(integrationParams.getConfigId())) {
            config = configurationService.getConfig(integrationParams.getConfigId());
        } else {
            config = configurationService.getConfig(integrationParams.getAgencyId(), integrationParams.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String getListDossierTransportationUrl = config.getParametersValue("getListDossierTransportationUrl");
        String transportationGovAgencyCode = config.getParametersValue("transportationGovAgencyCode");
        String apiKey = config.getParametersValue("transportationApiKey");
        TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);

        try {
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(getListDossierTransportationUrl);
            uriBuilder.queryParam("GovAgencyCode", transportationGovAgencyCode);
            uriBuilder.queryParam("Start", body.getStart());
            uriBuilder.queryParam("End", body.getEnd());
            uriBuilder.queryParam("MauBien", body.getMauBien());
            uriBuilder.queryParam("FromDate", body.getFromDate());
            uriBuilder.queryParam("ToDate", body.getToDate());
            uriBuilder.queryParam("MaThuTuc", body.getMauBien());
            uriBuilder.queryParam("Status", body.getStatus());
            uriBuilder.queryParam("ReceiveFromDate", body.getReceiveFromDate());
            uriBuilder.queryParam("ReceiveToDate", body.getReceiveToDate());
            uriBuilder.queryParam("ReleaseFromDate", body.getReleaseFromDate());
            uriBuilder.queryParam("ReleaseToDate", body.getReleaseToDate());
            uriBuilder.queryParam("FinishFromDate", body.getFinishFromDate());

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token.getAccessToken());
            headers.add("MaDonVi", transportationGovAgencyCode);
            headers.add("ApiKey", apiKey);
            HttpEntity<?> request = new HttpEntity<>("{}", headers);
            ResponseEntity<Object> result;
            result = restTemplate.exchange(
                    uriBuilder.toUriString(),
                    HttpMethod.POST, request, Object.class);
            return result;
        } catch (Exception e) {
            logger.info("Error calling getListDossierTransportation: " + e.getMessage());
            throw new DigoHttpException(11003, new String[]{"getListDossierTransportation error: " + e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    public ResponseEntity<Object> searchDossierTransportationQTI (IntegrationParamsDto integrationParams, String dossierTransportationId) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(integrationParams.getConfigId())) {
            config = configurationService.getConfig(integrationParams.getConfigId());
        } else {
            config = configurationService.getConfig(integrationParams.getAgencyId(), integrationParams.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String searchDossierTransportationUrl = config.getParametersValue("searchDossierTransportationUrl");
        String transportationGovAgencyCode = config.getParametersValue("transportationGovAgencyCode");
        String apiKey = config.getParametersValue("transportationApiKey");
        TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);

        try {
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(searchDossierTransportationUrl);
            uriBuilder.queryParam("MaHoSo", dossierTransportationId);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token.getAccessToken());
            headers.add("MaDonVi", transportationGovAgencyCode);
            headers.add("ApiKey", apiKey);
            HttpEntity<?> request = new HttpEntity<>("{}", headers);
            ResponseEntity<Object> result;
            result = restTemplate.exchange(
                    uriBuilder.toUriString(),
                    HttpMethod.POST, request, Object.class);
            return result;
        } catch (Exception e) {
            logger.info("Error calling searchDossierTransportation: " + e.getMessage());
            throw new DigoHttpException(11003, new String[]{"searchDossierTransportation error: " + e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    public ResponseEntity<Object> getStatisticalTransportationQTI (IntegrationParamsDto integrationParams, int month, int year) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(integrationParams.getConfigId())) {
            config = configurationService.getConfig(integrationParams.getConfigId());
        } else {
            config = configurationService.getConfig(integrationParams.getAgencyId(), integrationParams.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String getStatisticalTransportation = config.getParametersValue("getStatisticalTransportation");
        String transportationGovAgencyCode = config.getParametersValue("transportationGovAgencyCode");
        String apiKey = config.getParametersValue("transportationApiKey");
        TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);

        try {
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(getStatisticalTransportation);
            uriBuilder.queryParam("MaDonVi", transportationGovAgencyCode);
            uriBuilder.queryParam("Thang", month);
            uriBuilder.queryParam("Nam", year);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token.getAccessToken());
            headers.add("MaDonVi", transportationGovAgencyCode);
            headers.add("ApiKey", apiKey);
            HttpEntity<?> request = new HttpEntity<>("{}", headers);
            ResponseEntity<Object> result;
            result = restTemplate.exchange(
                    uriBuilder.toUriString(),
                    HttpMethod.POST, request, Object.class);
            return result;
        } catch (Exception e) {
            logger.info("Error calling getStatisticalTransportation: " + e.getMessage());
            throw new DigoHttpException(11003, new String[]{"getStatisticalTransportation error: " + e.getMessage()}, HttpServletResponse.SC_EXPECTATION_FAILED);
        }
    }

    //-----start namds.dlc-IGATESUPP-113362-----//
    public CivilDossierResDto sendDataJudicialCivilDLK(HttpServletRequest request, CivilDossierMinhTueDto body, String code, ObjectId logID) {
        try {
            logger.info("---data base insert--");
            String database = eventLogService.getConlectionnameData(this.serviceId);
            logger.info("---database-- " + database);
            logger.info("---code-- " + code);
            Query query = new Query(Criteria.where("key.name").is(code));
            Map<String, Object> existingDossier = mongoTemplate.findOne(query, Map.class, database);
            logger.info("---existingDossier-- " + existingDossier);
            if (Objects.isNull(existingDossier)){
                Query query1 = new Query(Criteria.where("status").is(false)).limit(1);
                List<Map> existingDossierList1 = mongoTemplate.find(query1, Map.class, database);
                if (!existingDossierList1.isEmpty() && existingDossierList1.size() > 0) {
                    Map<String, Object> existingDossier1 = existingDossierList1.get(0);
                    HashMap<String, String> formattedl1 = new HashMap<>();
                    formattedl1.put("_id", "Code");
                    formattedl1.put("name", code.toString());
                    existingDossier1.put("key", formattedl1);
                    existingDossier1.remove("_id");
                    try {
                        String id =  mongoTemplate.save(existingDossier1, database).get("_id").toString();
                        logger.info("_id "+ id);
                        logger.info("*** update start ***");
                        Query queryDB = new Query(Criteria.where("_id").is(new ObjectId(id)));
                        Update update = new Update()
                                .set("req.body", body);
                        mongoTemplate.updateFirst(queryDB, update, database);
                        logger.info("*** update end ***");
                    }catch (Exception e){
                        logger.info(e.getMessage());
                    }

                }
            }
        } catch (Exception e) {
        }
        //region event log init
        PostEventLogDto event = new PostEventLogDto();
        event.setRequestAdapter(request, body, "ad");
        event.setPid(logID);
        event.setServiceId(this.serviceId);
        event.setKey(new SidNameDto("Code", code));
        try {
            CivilDossierResDto response = new CivilDossierResDto();
            response = registerCivilDLK(body);
            event.setStatus(true);
            event.setErrMsg(response.toString());
            eventLogService.addNew(event);
            logger.info("DIGO-Response: " + response.toString());
            return response;
        } catch (Exception e) {
            event.setStatus(false);
            event.setErrMsg(e.getMessage());
            eventLogService.addNew(event);
            Map<String, String> map = new HashMap<>();
            map.put("statusMessage", translator.toLocale("lang.word.callLGSP"));
            map.put("statusCode", "-9999");
            return new CivilDossierResDto();
        }
    }

    public CivilDossierResDto registerCivilDLK(CivilDossierMinhTueDto req) throws Exception {
        logger.info("---------------------------START_GET_REGISTER_CIVIL_MINHTUE---------------------------");
        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        } else {
            config = configurationService.getConfig(req.getAgencyId(), req.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String registerCivilUrl = config.getParametersValue("registerCivilUrl");
        TokenResDto token = getToken(gatewayToken, consumerKey, consumerSecret);
        return dangKyHoTichDLK(registerCivilUrl, token.getAccessToken(), req);
    }

    private CivilDossierResDto dangKyHoTichDLK(String apiUrl, String access_token, CivilDossierMinhTueDto dangKyHoTich) throws IOException {
        CivilDossierResDto objreturn = new CivilDossierResDto();
        try {
            URL url = new URL(apiUrl);
            HttpURLConnection request = (HttpURLConnection) url.openConnection();
            Gson gson = (new GsonBuilder()).setDateFormat("yyyy-MM-dd'T'HH:mm:ss").create();
            Format formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            String pattern = "yyyy-MM-dd'T'HH:mm:ss";
            DateFormat df = new SimpleDateFormat(pattern);
            String jsonObject = "{\"maDonVi\":\"" + dangKyHoTich.getMaDonVi() + "\",\"module\":\"" + dangKyHoTich.getModule() + "\",\"maHoSo\":\"" + dangKyHoTich.getMaHoSo() + "\",\"ngayTiepNhan\":\"" + df.format(dangKyHoTich.getNgayTiepNhan()) + "\",\"data\":\"" + dangKyHoTich.getData().trim() + "\"}";
            String auth = "Bearer " + access_token;
            request.setRequestMethod("POST");
            request.setRequestProperty("Content-Type", "application/json");
            request.setRequestProperty("Accept-Charset", "UTF-8");
            request.setRequestProperty("Authorization", auth);
            request.setDoOutput(true);
            request.setDoInput(true);
            OutputStream outStream = request.getOutputStream();
            outStream.write(jsonObject.getBytes());
            outStream.flush();
            outStream.close();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
            StringBuffer responsedContent = new StringBuffer();
            String bufferedLine;
            while ((bufferedLine = bufferedReader.readLine()) != null) {
                responsedContent.append(bufferedLine);
            }
            bufferedReader.close();
            request.disconnect();
            JsonElement jsonElement = (JsonElement) gson.fromJson(responsedContent.toString(), JsonElement.class);
            JsonObject jsonObj = jsonElement.getAsJsonObject();
            JsonObject jsonObj2 = gson.fromJson(jsonObj.get("receiveRecordResponse").getAsString(), JsonElement.class).getAsJsonObject();
            int sts = jsonObj2.get("status").getAsInt();
            if (sts == 0) {
                objreturn.setStatus(sts);
                objreturn.setStatusDescription(jsonObj2.get("statusDescription").getAsString());
                objreturn.setErrorCode(jsonObj2.get("errorCode").getAsString());
                objreturn.setErrorDescription(jsonObj2.get("errorDescription").getAsString());
            } else {
                objreturn.setStatus(sts);
                objreturn.setValue("");
                objreturn.setStatusDescription(jsonObj2.get("statusDescription").getAsString());
                objreturn.setErrorCode("");
                objreturn.setErrorDescription("");
                Map<String, String> map = new HashMap<>();
                map.put("statusMessage", jsonObj2.get("statusDescription").toString());
                map.put("statusCode", String.valueOf(sts));
            }
        } catch (Exception var20) {
            logger.error(var20.getMessage());
            objreturn.setStatus(-1);
            objreturn.setValue(dangKyHoTich.getData());
            objreturn.setErrorDescription(var20.getMessage());
            throw var20;
        }
        return objreturn;
    }
    //-----end namds.dlc-IGATESUPP-113362-----//
}
