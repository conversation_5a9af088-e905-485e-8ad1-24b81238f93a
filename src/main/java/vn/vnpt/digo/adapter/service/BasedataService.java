package vn.vnpt.digo.adapter.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Oauth2RestTemplate;

import java.util.HashMap;

@Service
public class BasedataService {
    @Autowired
    Microservice microservice;

    @Autowired
    Oauth2RestTemplate oauth2RestTemplate;

    public <T> T findDeepAgencyByTag(String agencyId, String tagId, Class<T> className){
        UriComponentsBuilder URLBuilder = microservice.basedataUri("bdg/agency/" + agencyId + "/find-deep-by-tag")
                .queryParam("tag", "{tag}");
        HashMap<String, Object> params = new HashMap<>();
        params.put("tag", tagId);

        String URL = URLBuilder.encode().toUriString();

        RestTemplate rest = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(oauth2RestTemplate.getToken(false));
        HttpEntity<?> request = new HttpEntity<>(headers);
        return rest.exchange(URL, HttpMethod.GET, request, className, params).getBody();
    }


    public <T> T findOneAgency(String agencyCode, Class<T> className){
        UriComponentsBuilder URLBuilder = microservice.basedataUri("/agency/name+code+parent+ancestor/--fully-by-code?code=" + agencyCode);
        String URL = URLBuilder.encode().toUriString();
        RestTemplate rest = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(oauth2RestTemplate.getToken(false));
        HttpEntity<?> request = new HttpEntity<>(headers);
        return rest.exchange(URL, HttpMethod.GET, request, className).getBody();
    }

    public <T> T post(String absUrl, Object body, Class<T> returnType){
        String URL = microservice.basedataUri(absUrl).toUriString();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(oauth2RestTemplate.getToken(false));
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        return restTemplate.exchange(URL, HttpMethod.POST, request, returnType).getBody();
    }

    public <T> T post(String absUrl, Object body, ParameterizedTypeReference<T> returnTypeList){
        String URL = microservice.basedataUri(absUrl).toUriString();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(oauth2RestTemplate.getToken(false));
        HttpEntity<?> request = new HttpEntity<>(body, headers);
        return restTemplate.exchange(URL, HttpMethod.POST, request, returnTypeList).getBody();
    }

    public <T> T get(String absUrl, Class<T> returnType){
        String URL = microservice.basedataUri(absUrl).toUriString();
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(oauth2RestTemplate.getToken(false));
        HttpEntity<?> request = new HttpEntity<>(headers);
        return restTemplate.exchange(URL, HttpMethod.GET, request, returnType).getBody();
    }
}
