package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.http.Header;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.api.ApiIntegrationController;
import vn.vnpt.digo.adapter.cache.ServiceCache;
import vn.vnpt.digo.adapter.document.ApiIntegration;
import vn.vnpt.digo.adapter.document.ApiIntegrationEventLog;
import vn.vnpt.digo.adapter.document.ApiIntegrationLog;
import vn.vnpt.digo.adapter.document.errorLogs.ServiceLog;
import vn.vnpt.digo.adapter.dto.Base64Dto;
import vn.vnpt.digo.adapter.dto.Integration.ErrorDefine;
import vn.vnpt.digo.adapter.dto.Integration.FlatKeyDto;
import vn.vnpt.digo.adapter.dto.Integration.FuncValue;
import vn.vnpt.digo.adapter.dto.gtvt.DossierResponseDto;
import vn.vnpt.digo.adapter.dto.request.*;
import vn.vnpt.digo.adapter.dto.request.Permission;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.microservice.AdapterService;
import vn.vnpt.digo.adapter.pojo.Permissions;
import vn.vnpt.digo.adapter.repository.ApiIntegrationEventLogRepository;
import vn.vnpt.digo.adapter.repository.ApiIntegrationLogRepository;
import vn.vnpt.digo.adapter.repository.ApiIntegrationRepository;
import vn.vnpt.digo.adapter.repository.ServiceLogRepository;
import vn.vnpt.digo.adapter.util.*;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class ServiceService {

    @Autowired
    private RestTemplate restTemplate;

    private Integer depreciationSecond = 120;

    @Autowired
    private ApiIntegrationLogRepository apiIntegrationLogRepository;

    @Autowired
    private ApiIntegrationEventLogRepository apiIntegrationEventLogRepository;

    @Autowired
    private ApiIntegrationRepository apiIntegrationRepository;

    @Autowired
    private ServiceLogRepository serviceLogRepository;

    @Autowired
    private ServiceCache serviceCache;

    @Autowired
    private Microservice microservice;

    @Autowired
    private ApiIntegrationService apiIntegrationService;

    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String issuerUri;

    @Value("${digo.domain.list.api}")
    private String domainListAPI;

    private Boolean domainAPiEnable = false;

    private String processDefinitionId = null;

    @Value("${digo.oidc.client-id}")
    private String clientId;

    @Value("${digo.oidc.client-secret}")
    private String clientSecret;

    @Autowired
    private AdapterService adapterService;

    private String newSessionValue = null;

    private String sessionNameSend = null;

    List<ObjectId> filteredItems;

    Logger logger = LoggerFactory.getLogger(ServiceService.class);

    public String getDomainFromUrl(String url) {
        try {
            java.net.URL netUrl = new java.net.URL(url);
            String protocol = netUrl.getProtocol();
            String host = netUrl.getHost();
            return protocol + "://" + host;
        } catch (Exception e) {
            logger.info("getDomainFromUrl error line: 106, error: " + e.toString());
            e.printStackTrace();
            return null;
        }
    }

    public String getToken(String domainSSO, String clientId, String clientSecret){
        String token = null;
        String currentSSO = this.getDomainFromUrl(this.issuerUri);
        try{
            token = Context.getJwtAuthenticationTokenValue();
        }catch (Exception ex){
            logger.info("GetToken error line: 118, error: " + ex.toString());
        }
        if(!domainSSO.equals(currentSSO) || (domainSSO.equals(currentSSO) && Objects.isNull(token))){
            String URL = domainSSO + "/auth/realms/digo/protocol/openid-connect/token";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("client_id",clientId);
            map.add("client_secret",clientSecret);
            map.add("grant_type","client_credentials");
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
                HashMap<String,Object> response = restTemplate.postForObject(URL, request, HashMap.class);
            token = response.get("access_token").toString();
        }else {
            token = Context.getJwtAuthenticationTokenValue();
        }
        return token;
    }

    private ApiIntegration getConfig(List<ApiIntegration> configs,
                                     String serviceCode,
                                     String apiCode,
                                     String configCode,
                                     String option,
                                     String tokenTest){
        List<ApiIntegration> fconfigs = new ArrayList<>();
        apiCode = Objects.nonNull(apiCode)? "/" + apiCode:"";
        String pfcode = serviceCode + apiCode;
        String fiCode = null;
        logger.info("exchange start 2.8, configCode" + configCode);
        if(Objects.isNull(configCode)){
            fiCode = pfcode;
            if(pfcode.contains("/auth")){
                if (Objects.nonNull(tokenTest)){
                    fconfigs = configs.stream().filter(i->i.getType().equals(3) && i.getFcode().equals(tokenTest)).collect(Collectors.toList());
                }else {
                    fconfigs = configs.stream()
                            .filter(i ->
                                    i.getType() == 3 &&
                                            i.getPfcode().equals(pfcode) &&
                                            (
                                                    i.getRequest() == null ||
                                                            i.getRequest().getHeaders() == null ||
                                                            i.getRequest().getHeaders().isEmpty() ||
                                                            i.getRequest().getHeaders().stream()
                                                                    .noneMatch(h -> "tokenType".equals(h.getKey()) && "true".equals(h.getValue()))
                                            )
                            )
                            .collect(Collectors.toList());
//                    fconfigs = configs.stream().filter(i->i.getType().equals(3) && i.getPfcode().equals(pfcode)).collect(Collectors.toList());
                }
            }else {
                if(Objects.isNull(option)){
                    fconfigs = configs.stream().filter(i->i.getType().equals(3) && i.getPfcode().equals(pfcode)).collect(Collectors.toList());
                }else {
                    fconfigs = configs.stream().filter(i->i.getType().equals(3) && i.getPfcode().equals(pfcode) && option.equals(i.getOption())).collect(Collectors.toList());
                }
            }
        }else {
            String fcode = pfcode + "/" + configCode;
            Integer type = StringHelper.hasValue(apiCode)?3:2;
            if(type==3 && Objects.nonNull(option) && !fcode.contains("/auth/")){
                fcode += "/" + option;
            }
            String finalFcode = fcode;
            fiCode = fcode;
            logger.info("exchange start 2.8.1, finalFcode" + finalFcode);
            fconfigs = configs.stream().filter(i->i.getType().equals(type) && i.getFcode().equals(finalFcode)).collect(Collectors.toList());

        }
        String action = fiCode.contains("/auth/")?"lấy token":"thực thi tính năng";
        logger.info("exchange start 2.9, action" + action);
        String[] msgVars = new String[]{fiCode,action};
        if(fconfigs.size() == 0){
            logger.info("exchange start 2.10, error fconfigs size = 0" + fconfigs);
            throw new DigoHttpException(10401,msgVars, HttpServletResponse.SC_FORBIDDEN);
        }else if(fconfigs.size() > 1){
            logger.info("exchange start 2.11, error fconfigs size > 1" + fconfigs);
            throw new DigoHttpException(10402,msgVars, HttpServletResponse.SC_FORBIDDEN);
        }
        ApiIntegration config = fconfigs.get(0);
        return config;
    }

    private HttpMethod getMethod(String method){
        method = method.toUpperCase();
        switch (method){
            case "GET": return HttpMethod.GET;
            case "HEAD": return HttpMethod.HEAD;
            case "POST": return HttpMethod.POST;
            case "PUT": return HttpMethod.PUT;
            case "PATCH": return HttpMethod.PATCH;
            case "DELETE": return HttpMethod.DELETE;
            case "TRACE": return HttpMethod.TRACE;
            default:return HttpMethod.OPTIONS;
        }
    }

    private String getBasicToken(String username, String password){
        String authString = username + ":" + password;
        byte[] authBytes = authString.getBytes(StandardCharsets.UTF_8);
        String token = Base64.getEncoder().encodeToString(authBytes);
        return token;
    }

    private Request fillValue(ApiIntegration config, List<PramOrHeader> values, HashMap<String, Object> resource){
        Request request = config.getRequest();
        Request newRequest = new Request();
        ObjectMapper mapper = new ObjectMapper();
        try{
            String jsonString = mapper.writeValueAsString(request);
            for (PramOrHeader value:values) {
                String fivalue = value.getValue();
                if (fivalue.trim().startsWith("{") || fivalue.trim().startsWith("[")) {
                    String escapedValue = mapper.writeValueAsString(fivalue);
                    String key = "{{" + value.getKey() + "}}";
                    key = "\"" + key + "\"";
                    jsonString = jsonString.replace(key, escapedValue);
                }else if(!List.of("true","false").contains(fivalue)){
                    jsonString = jsonString.replace("{{" + value.getKey() + "}}",fivalue);
                }else {
                    String key = "{{" + value.getKey() + "}}";
                    key = "\"" + key + "\"";
                    jsonString = jsonString.replace(key,fivalue);
                }
            }
            newRequest = mapper.readValue(jsonString, new TypeReference<Request>() {});

        }catch (Exception ex){
            logger.info("fillValue error line: 218, error: " + ex.toString());
        }
        if(Objects.nonNull(resource)){
            try{
                if(Objects.nonNull(config.getJs()) && !config.getJs().isEmpty()){
                    Object data = resource.get("resource");
                    String json = this.exeJavascript(data,config.getJs());
                    data = Converter.toHashMap(json);
                    resource.put("resource",data);
                }
            }catch (Exception ex){
                logger.info("fillValue error line: 229, error: " + ex.toString());
            }

            try{
                HashMap<String, Object> output = Converter.toHashMap(newRequest);
                try{
                    String outputJson = Converter.toString(output);
                    if(outputJson.contains("{{resource.componentUploadedIds}}")){
                        List<String> ids = this.uploadFileIds(resource, config);
                        String sListId = "[" + String.join(",", ids) + "]";
                        outputJson = outputJson.replace("{{resource.componentUploadedIds}}", sListId);
                        output = Converter.toHashMap(outputJson);
                        System.out.println(output);
                    }
                }catch (Exception e){
                    logger.info("fillValue error line: 244, error: " + e.toString());
                }
                output = this.merge(resource,output,true,config);
                String jsonString = Converter.toString(output);
                newRequest = mapper.readValue(jsonString, new TypeReference<Request>() {});
            }catch (Exception ex){
                logger.info("fillValue error line: 250, error: " + ex.toString());
            }
        }
        return newRequest;
    }

    private ResponseEntity<byte[]> downloadBytes(String url, HttpMethod method, HttpEntity<?> entitySend) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpEntityEnclosingRequestBase request = new HttpEntityEnclosingRequestBase() {
            @Override
            public String getMethod() {
                return method.toString();
            }
        };

        if(Objects.nonNull(entitySend)){
            HttpHeaders sendHeaders = entitySend.getHeaders();
            if(Objects.nonNull(sendHeaders) && sendHeaders.size() > 0){
                var headers = sendHeaders.entrySet();
                for (var header:headers){
                    var headerValues = header.getValue();
                    for (String headerValue:headerValues){
                        request.addHeader(header.getKey(),headerValue);
                    }
                }
            }

            var bodySend = entitySend.getBody();
            if(Objects.nonNull(bodySend)){
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    String json = objectMapper.writeValueAsString(bodySend);
                    StringEntity stringEntity = new StringEntity(json);
                    request.setEntity(stringEntity);
                } catch (Exception e) {
                    logger.info("downloadBytes error line: 285, error: " + e.toString());
                }
            }
        }

        try{
            URIBuilder uriBuilder = new URIBuilder(url);
            request.setURI(uriBuilder.build());
            CloseableHttpResponse response = httpClient.execute(request);
            org.apache.http.HttpEntity entity = response.getEntity();
            byte[] content = EntityUtils.toByteArray(entity);
            HttpHeaders headers = new HttpHeaders();
            for (Header header : response.getAllHeaders()) {
                headers.add(header.getName(), header.getValue());
            }
            return new ResponseEntity<>(content, headers, HttpStatus.OK);
        }catch (Exception ex){
            logger.info("downloadBytes error line: 302, error: " + ex.toString());
        }

        return null;
    }

    private Response getResponse(String url, HttpMethod method, HttpEntity<?> entitySend, Map<String, Object> params,  boolean isFile){
        Response response = new Response();
//        if (url.contains("gw/send")) {
//            url = url.replace("gw/send", "send2");
//        }
        URI uri = URI.create(url);
        if(!isFile){
            ResponseEntity<String> rpJson = null;
            if(Objects.isNull(params))
                rpJson = restTemplate.exchange(uri, method, entitySend, String.class);
            else
                rpJson = restTemplate.exchange(url, method, entitySend,String.class,params);
            HttpHeaders headersRs = rpJson.getHeaders();
            if (headersRs.getContentDisposition() != null && headersRs.getContentDisposition().getFilename() != null) {
                String filename = headersRs.getContentDisposition().getFilename();
                String contentType = headersRs.getContentType().toString();
                response.setFilename(filename);
                response.setContentType(contentType);
            }
            response.setHttpStatus(rpJson.getStatusCode().value()+ "");
            response.setRpJson(rpJson.getBody());
        }else {
            ResponseEntity<byte[]> rpBytes = this.downloadBytes(url,method,entitySend);
            response.setRpBytes(rpBytes.getBody());
        }
        return response;
    }

    private MultiValueMap<String, Object> getFile(MultiValueMap<String, MultipartFile> file){
        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
        for (Map.Entry<String, List<MultipartFile>> entry : file.entrySet()) {
            for (MultipartFile multipartFile : entry.getValue()) {
                formData.add(entry.getKey(), multipartFile.getResource());
            }
        }
        return formData;
    }

    private void addFormToBody(MultiValueMap<String, Object> body, List<PramOrHeader> forms){
        forms.forEach(i->{
            body.add(i.getKey(),i.getValue());
        });
    }

    private Result getResult(ApiIntegration config,
                             MultiValueMap<String, MultipartFile> file,
                             List<PramOrHeader> values,
                             HashMap<String, Object> raw,
                             HashMap<String, Object> resource,
                             Boolean debugEnable){
        Result result = new Result();
        HttpHeaders headersSend = new HttpHeaders();
        Request request = this.fillValue(config,values,resource);
        List<PramOrHeader> headers = request.getHeaders();
        // Check if any PramOrHeader has the key "AuthorizationEnable"
        try {
            boolean isAuthorizationEnable = headers.stream()
                    .filter(value -> "AuthorizationEnable".equals(value.getKey()))
                    .map(value -> Boolean.parseBoolean(value.getValue()))
                    .findFirst()
                    .orElse(false);
            // Output the result
            if (isAuthorizationEnable){
                String sessionName = headers.stream()
                        .filter(value -> "sessionNameResult".equals(value.getKey()))
                        .map(value -> value.getValue())
                        .findFirst()
                        .orElse(null);

                String sessionNameSends = headers.stream()
                        .filter(value -> "sessionNameSend".equals(value.getKey()))
                        .map(value -> value.getValue())
                        .findFirst()
                        .orElse(null);

                sessionNameSend = sessionNameSends;

                String domainAPI = headers.stream()
                        .filter(value -> "domainAPI".equals(value.getKey()))
                        .map(value -> value.getValue())
                        .findFirst()
                        .orElse(null);

                Map<String, Object> resultData = adapterService.getAPICauHinh(domainAPI,null);
                if(resultData != null && resultData.containsKey(sessionName)){
                    newSessionValue =  resultData.get(sessionName).toString();
                }
            }
        }catch (Exception e){
            logger.info("getResult error line: 392, error: " + e.toString());
        }
        HttpMethod method = this.getMethod(request.getMethod());
        String url = request.getUrl();
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
        List<PramOrHeader> queries = new ArrayList<>();
        List<PramOrHeader> params = request.getParams();
        if(Objects.nonNull(params) && params.size() > 0){
            HashMap<String, Object> data = new HashMap<>();
            data.put("input",raw);
            for(PramOrHeader param: params){
                try{
                    param.setValue(this.getValue(data, param.getValue()).toString());
                }catch (Exception e){
                    param.setValue(param.getValue());
                    logger.info("getResult error line: 418, error: " + e.toString());
                }

            }
            queries.addAll(params);
        }
        Authentication auth = request.getAuth();
        MediaType mediaType = MediaType.APPLICATION_FORM_URLENCODED;
        if(Objects.nonNull(auth)){
            BasicAuth basic = auth.getBasic();
            if(Objects.nonNull(basic)){
                String basicToken = this.getBasicToken(basic.getUsername(),basic.getPassword());
                headersSend.set("Authorization", "Basic " + basicToken);
            }else if(Objects.nonNull(auth.getBearer())){
                BearerToken bearer = auth.getBearer();
                String bearerToken = bearer.getToken();
                headersSend.set("Authorization", "Bearer " + bearerToken);
            }
        }

        if(Objects.nonNull(headers) && headers.size() > 0){
            headers.forEach(header->{
                String headerKey = header.getKey();
                headerKey = headerKey.replaceAll("\\s+", "");
                headersSend.set(headerKey,header.getValue());
            });
            List<PramOrHeader> contentTypes = headers.stream().filter(i->i.getKey().toLowerCase().equals("content-type")).collect(Collectors.toList());
            if(Objects.nonNull(contentTypes) && contentTypes.size() > 0){
                PramOrHeader contentType = contentTypes.get(0);
                String sContentType = contentType.getValue();
                mediaType = MediaType.valueOf(sContentType);
            }
        }

        Object objSend = null;
        Body body = request.getBody();
        String xxxsSend = null;
        MultiValueMap<String, Object> fileData = null;
        if(Objects.nonNull(body)){
            List<PramOrHeader> xwwws = body.getXwww();
            if(Objects.nonNull(xwwws) && xwwws.size() > 0){
                xxxsSend = "";
                for (PramOrHeader xwww:xwwws) {
                    xxxsSend += "&" + xwww.getKey() + "=" + xwww.getValue();
                }
                xxxsSend = xxxsSend.substring(1);
                mediaType = MediaType.APPLICATION_FORM_URLENCODED;
                objSend = xxxsSend;
            }else if(Objects.nonNull(body.getMBodyRaw())){
                mediaType = MediaType.APPLICATION_JSON;
                HashMap<String,Object> data = new HashMap<>();
                objSend = body.getMBodyRaw();

                HashMap<String,Object> input = config.getInputRaw();
                data.put("input",input);
                data.put("resource",resource);
                HashMap<String,Object> template = (HashMap<String,Object>)objSend;
                if (newSessionValue != null && sessionNameSend != null){
                    template.put(sessionNameSend, newSessionValue);
                }
                template = this.merge(data,template,true,config);/**Merge request**/
                // đoạn check convert json to string
                try{
                    if(Objects.nonNull(config.getRequest().getHeaders())){
                        for (PramOrHeader header : headers) {
                            if ("convertJsontoString".equals(header.getKey())) {
                                String keyToConvert = header.getValue();
                                if(Objects.nonNull(template) && template.containsKey(keyToConvert)){
                                    Object value = template.get(keyToConvert);
                                    if (!(value instanceof String)) {
                                        String jsonString = new ObjectMapper().writeValueAsString(value);
                                        template.put(keyToConvert, jsonString);
                                    }
                                }
                                break;
                            }
                        }
                    }
                }catch(Exception ex){

                }
                objSend = template;
            }else if(Objects.nonNull(body.getForm())){
                mediaType = MediaType.MULTIPART_FORM_DATA;
                List<PramOrHeader> forms = body.getForm();
                forms = forms.stream().filter(i->!(i.getValue().startsWith("{{") && i.getValue().endsWith("}}"))).collect(Collectors.toList());
                //queries.addAll(forms);
                MultiValueMap<String, Object> formData = null;
                if(Objects.nonNull(file)){
                    formData = this.getFile(file);
                }
                if(forms.size() > 0){
                    if(Objects.isNull(formData)){
                        formData = new LinkedMultiValueMap<>();
                    }
                    this.addFormToBody(formData,forms);
                }
                if(Objects.nonNull(formData)){
                    objSend = formData;
                }
            }
        }

        HashMap<String,Object> paramObj = null;
        Gson gson = new Gson();
        if(queries.size() > 0){
            for (PramOrHeader param:queries) {
                String value = param.getValue();
                String key = param.getKey();
                HashMap<String,Object> oValue = StringHelper.toHashMap(value);
                if(Objects.nonNull(oValue)){
                    if(Objects.isNull(paramObj)){
                        paramObj = new HashMap<>();
                    }
                    paramObj.put(key, gson.toJson(oValue));
                }else {
                    builder.queryParam(key, value);
                }
            }
        }

        try{

            headersSend.setContentType(mediaType);
            HttpEntity<?> entitySend = new HttpEntity<>(headersSend);
            if(Objects.nonNull(objSend)){
                entitySend = new HttpEntity<>(objSend,headersSend);
            }
            url = builder.toUriString();
            url = URLDecoder.decode(url,"UTF-8");
            result.setBody(objSend);
            result.setUrl(url);
            boolean isFile = Objects.nonNull(config.getOutputIsFile()) && config.getOutputIsFile()==1;
            if (debugEnable) {
                String formattedUrl = url;
                if (paramObj != null && !paramObj.isEmpty()) {
                    UriComponentsBuilder builderDebug = UriComponentsBuilder.fromUriString(url);

                    for (Map.Entry<String, ?> entry : paramObj.entrySet()) {
                        Object value = entry.getValue();
                        if (value instanceof Collection<?>) {
                            builderDebug.queryParam(entry.getKey(), ((Collection<?>) value).toArray());
                        } else if (value instanceof Object[]) {
                            builderDebug.queryParam(entry.getKey(), (Object[]) value);
                        } else {
                            builderDebug.queryParam(entry.getKey(), value);
                        }
                    }
                    formattedUrl = builderDebug.build(true).toUriString();
                }
                String curlDebug = buildCurlDebug(formattedUrl, method, entitySend);
                result.setCurlDebug(curlDebug);
                return result;
            }

            Response response = this.getResponse(url,method,entitySend,paramObj,isFile);
            result.setResponse(response);
        } catch (Exception ex) {
            logger.info("getResult error line: 511, error: " + ex.toString());
            result.setErrMsg(ex.getMessage());
        }

        return result;
    }

    public Object getValue(HashMap<String, Object> input, String key){
        try {
            key = key.replace("{{","").replace("}}","");
            if(key.indexOf(".")==-1){
                return input.get(key);
            }else {
                String[] keys = key.split("\\.");
                String fkey = keys[0];
                Integer idx = key.indexOf(".") + 1;
                key = key.substring(idx);
                Object value = input.get(fkey);
                input = (HashMap<String, Object>)value;
                return getValue(input,key);
            }
        }catch (Exception e){
            logger.info("getValue error line: 533, error: " + e.toString());
            System.out.println("**key*** " + key);
            System.out.println("Error: " + e.getMessage());
            return null;
        }
    }

    /**
     * type:
     *    0: Không cần token
     *    1: Chỉ cần có token
     *    2: Cần có một trong những quyền trong danh sách codes
     *    3: Phải có tất cả quyền trong danh sách codes
     * permission.codes: danh sách mã quyền
     */
    private void checkPermission(Permission permission){
        Integer type = permission.getType();
        if(type == 1){
            String accountId = null;
            try{
                accountId = Context.getJwtParameterValue("azp");
            }catch (Exception ex){
                logger.info("checkPermission error line: 555, error: " + ex.toString());
            }
            if(Objects.isNull(accountId)){
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        }else if(type > 1){
            String sPermission = null;
            try{
                sPermission = Context.getJwtParameterValue("permissions");
            }catch (Exception ex){
                logger.info("checkPermission error line: 565, error: " + ex.toString());
            }
            if(Objects.isNull(sPermission)){
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }


            if(type > 1 && Objects.nonNull(permission.getItems()) && permission.getItems().size() > 0){
                List<String> perTokenCodes = new ArrayList<>();
                try {
                    Permissions ret = new Permissions();
                    Gson gson = new Gson();
                    ret.setPermissions(gson.fromJson(sPermission, vn.vnpt.digo.adapter.pojo.Permission[].class));
                    var items = ret.getPermissions();
                    for (var item:items) {
                        perTokenCodes.add(item.getPermission().getCode());
                    }
                } catch (Exception e) {
                    logger.info("checkPermission error line: 583, error: " + e.toString());
                }

                if(perTokenCodes.size()==0){
                    throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
                }

                List<String> perRequiredCodes = permission.getItems().stream().map(i->i.getKey()).collect(Collectors.toList());
                List<String> perFilterCodes = perRequiredCodes.stream().filter(i->perTokenCodes.contains(i)).collect(Collectors.toList());
                boolean perValid = (type==2 && perFilterCodes.size() > 0)||(type==3 && perFilterCodes.size() == perRequiredCodes.size());
                if(!perValid){
                    throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
                }
            }
        }
    }

    private List<PramOrHeader> convert(HashMap<String, String> obj){
        List<PramOrHeader> items = new ArrayList<>();
        try{
            if(Objects.nonNull(obj)){
                obj.entrySet().forEach(i->{
                    PramOrHeader item = new PramOrHeader();
                    item.setKey("input." + i.getKey());
                    item.setValue(i.getValue());
                    items.add(item);
                });
            }
            logger.info("exchange start 2.1, convert succes");
        }catch (Exception e){
            logger.info("convert error line: 611, error: " + e.toString());
        }
        return items;
    }

    private void pushContext(List<PramOrHeader> items, HashMap<String, Object> resource){
        try {
            String givenName = Context.getJwtParameterValue("given_name");
            try {
                if(Objects.nonNull(resource)) {
                    Map<String, Object> mainResource = (Map<String, Object>) resource.get("resource");
                    if (Objects.nonNull(mainResource) && mainResource.containsKey("contextGivenName")) {
                        // check trường hợp nhận từ kafka padman
                        givenName = (String) mainResource.get("contextGivenName");
                    }
                }
            }catch(Exception ex){
            }
            if(Objects.nonNull(givenName)){
                PramOrHeader givenNameItem = new PramOrHeader();
                givenNameItem.setKey("context.given_name");
                givenNameItem.setValue(givenName);
                items.add(givenNameItem);
            }

            String identityNumber = Context.getJwtParameterValue("identity_number");
            try {
                if(Objects.nonNull(resource)){
                    Map<String, Object> mainResource = (Map<String, Object>) resource.get("resource");
                    if(Objects.nonNull(mainResource) && mainResource.containsKey("contextIdentityNumber")){
                        // check trường hợp nhận từ kafka padman
                        identityNumber = (String) mainResource.get("contextIdentityNumber");
                    }
                }
            }catch(Exception ex){

            }
            if(Objects.nonNull(identityNumber)){
                PramOrHeader identityNumberItem = new PramOrHeader();
                identityNumberItem.setKey("context.identity_number");
                identityNumberItem.setValue(identityNumber);
                items.add(identityNumberItem);
            }

            String preferredUsername = Context.getJwtParameterValue("preferred_username");
            try {
                if(Objects.nonNull(resource)){
                    Map<String, Object> mainResource = (Map<String, Object>) resource.get("resource");
                    if(Objects.nonNull(mainResource) && mainResource.containsKey("contextPreferredUsername")){
                        // check trường hợp nhận từ kafka padman
                        preferredUsername = (String) mainResource.get("contextPreferredUsername");
                    }
                }
            }catch(Exception ex){

            }
            if(Objects.nonNull(preferredUsername)){
                PramOrHeader preferredUsernameItem = new PramOrHeader();
                preferredUsernameItem.setKey("context.preferred_username");
                preferredUsernameItem.setValue(preferredUsername);
                items.add(preferredUsernameItem);
            }


            String token = Context.getJwtAuthenticationTokenValue();
            if(Objects.nonNull(token)){
                PramOrHeader tokenItem = new PramOrHeader();
                tokenItem.setKey("context.token");
                tokenItem.setValue(token);
                items.add(tokenItem);
            }
        }catch (Exception e){

        }
    }

    private void pushAuth(String token, String refresh_token, List<PramOrHeader> items){
        if(Objects.nonNull(token)){
            PramOrHeader tokenItem = new PramOrHeader();
            tokenItem.setKey("auth.token");
            tokenItem.setValue(token);
            items.add(tokenItem);
        }

        if(Objects.nonNull(refresh_token)){
            PramOrHeader refreshTokenItem = new PramOrHeader();
            refreshTokenItem.setKey("auth.refresh_token");
            refreshTokenItem.setValue(token);
            items.add(refreshTokenItem);
        }
    }

    private Date addSecondToDate(Date time, int expiredTokenNumber) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.add(Calendar.SECOND, expiredTokenNumber);
        return calendar.getTime();
    }

    private HashMap<String, Object> copyHashMap(HashMap<String, Object> original) {
        Gson gson = new Gson();
        Type type = new TypeToken<HashMap<String, Object>>(){}.getType();
        String jsonString = gson.toJson(original);
        return gson.fromJson(jsonString, type);
    }

    private String getKeyOfList(HashMap<String, Object> input, String arrKey){
        arrKey = arrKey.replace("{{","").replace("}}","");
        String[] arrKeys = arrKey.split("\\.");
        arrKey = "";
        int count = 0;
        while (count < arrKeys.length && count > -1){
            arrKey += "." + arrKeys[count];
            if(arrKey.startsWith(".")){
                arrKey = arrKey.substring(1);
            }
            Object tempValue = this.getValue(input,arrKey);
            if(tempValue instanceof List){
                count = -2;
            }
            count++;
        }
        return arrKey;
    }

    private Map.Entry<String,Object> getSetValid(Object templateItem){
        HashMap<String, Object> firstItem = (HashMap<String, Object>) templateItem;
        Set<Map.Entry<String,Object>> sets = firstItem.entrySet();
        Map.Entry<String,Object> item = null;
        for (Map.Entry<String,Object> set:sets){
            String value = set.getValue().toString();
            if(value.startsWith("{{") && value.endsWith("}}")){
                item = set;
                break;
            }
        }
        return item;
    }

    public void onMerge(HashMap<String, Object> input, HashMap<String, Object> output, boolean setNullWhileNotFound){
        output.entrySet().forEach(entry -> {
            Object value = entry.getValue();
 // để danh debug không dc xóa
//            if(entry.getKey().equals("fileURL")){
//                System.out.println();
//            }
            try {
                if (value instanceof HashMap || value instanceof Map) {
                    HashMap<String, Object> nestedMap = Converter.toHashMap(value);
                    onMerge(input, nestedMap, setNullWhileNotFound);
                    entry.setValue(nestedMap);
                } else if (value instanceof List) {
                    List<Object> values = new ArrayList<>();
                    List<Object> items = (List<Object>) value;
                    if (items.size() > 0) {
                        Object templateItem = items.get(0);
                        if (templateItem instanceof String) {
                            Object rvalue = templateItem;
                            String subKey = null;
                            String key = templateItem.toString();
                            if(key.startsWith("{{") && key.endsWith("}}")){
                                key = key.replace("{{", "").replace("}}", "");
                                String arrKey = this.getKeyOfList(input, key);
                                subKey = arrKey;
                                if (key.contains(".")) {
                                    subKey = key.substring(arrKey.length() + 1);
                                }
                                rvalue = this.getValue(input, arrKey);
                            }

                            List<Object> rvalues = (List<Object>) rvalue;
                            for (Object _ivalue : rvalues) {
                                HashMap<String, Object> hivalue = new HashMap<>();
                                if (_ivalue instanceof String) {
                                    hivalue.put(subKey, _ivalue);
                                } else {
                                    hivalue = (HashMap<String, Object>) _ivalue;
                                }
                                Object newValue = this.getValue(hivalue, subKey);
                                values.add(newValue);
                            }
                        } else {

                            HashMap<String, Object> firstItem = (HashMap<String, Object>) templateItem;
                            //var sets = firstItem.entrySet();
                            //var set = sets.iterator().next();
                            Map.Entry<String,Object> set = this.getSetValid(templateItem);
                            String arrKey = set.getValue().toString();
                            arrKey = this.getKeyOfList(input, arrKey);
                            try {
                                org.codehaus.jackson.map.ObjectMapper objectMapper = new org.codehaus.jackson.map.ObjectMapper();
                                String json = objectMapper.writeValueAsString(firstItem);
                                json = json.replace("{{" + arrKey + ".", "{{");
                                firstItem = objectMapper.readValue(json, HashMap.class);
                                System.out.println();
                            } catch (Exception ex) {
                                logger.info("onMerge error line: 733, error: " + ex.toString());
                            }
                            Object rvalue = this.getValue(input, arrKey);
                            List<Object> rvalues = (List<Object>) rvalue;
                            if (Objects.nonNull(rvalues)) {
                                for (Object _ivalue : rvalues) {
                                    HashMap<String, Object> ivalue = new HashMap<>();
                                    if (_ivalue instanceof String) {
                                        ivalue.put(arrKey, _ivalue);
                                    } else {
                                        ivalue = (HashMap<String, Object>) _ivalue;
                                    }
                                    HashMap<String, Object> ovalue = this.copyHashMap(firstItem);
                                    onMerge(ivalue, ovalue, setNullWhileNotFound);
                                    values.add(ovalue);
                                }
                            }
                        }
                        entry.setValue(values);
                    }
                } else if (Objects.nonNull(value)) {
                    String key = value.toString();
                    if (key.startsWith("{{") && key.endsWith("}}")) {
                        Object newValue = null;
                        try {
                            newValue = this.getValue(input, key);
                        } catch (Exception ex) {
                            logger.info("onMerge error line: 760, error: " + ex.toString());
                        }
                        if (Objects.nonNull(newValue) || setNullWhileNotFound)
                            entry.setValue(newValue);
                    } else if (key.startsWith("[") && key.endsWith("]")) {
                        // Loại bỏ dấu ngoặc vuông và tách thành các phần tử
                        String stripped = key.substring(1, key.length() - 1).trim();
                        List<String> stringList = Arrays.asList(stripped.split("\\s*,\\s*")); // Tách dựa trên dấu phẩy, có loại bỏ khoảng trắng
                        List<String> finalValue = new ArrayList<>();
                        for (String sItem : stringList) {
                            if (sItem.startsWith("{{") && sItem.endsWith("}}")) {
                                Object newValue = null;
                                try {
                                    newValue = this.getValue(input, sItem);
                                } catch (Exception ex) {
                                    logger.info("onMerge error line: 775, error: " + ex.toString());
                                }
                                if (Objects.nonNull(newValue) || setNullWhileNotFound) {
                                    finalValue.add(newValue.toString());
                                }

                            } else {
                                finalValue.add(sItem.toString());
                            }
                        }
                        entry.setValue(finalValue);
                    } else {
                        if(List.of("true","false").contains(value.toString())){
                            entry.setValue(Boolean.valueOf(value.toString()));
                        }else {
                            String keyFind = null;
                            try {
                                if (!value.toString().startsWith("{{") || !value.toString().endsWith("}}")) {
                                    Pattern pattern = Pattern.compile("\\{\\{(.*?)\\}\\}");
                                    Matcher matcher = pattern.matcher(value.toString());
                                    while (matcher.find()) {
                                        keyFind = matcher.group(1); // Lấy giá trị bên trong {{ }}
                                        System.out.println("Found value: " + keyFind);
                                    }
                                    if (Objects.nonNull(keyFind)){
                                        keyFind = "{{"+ keyFind + "}}";
                                        if (keyFind.startsWith("{{") && keyFind.endsWith("}}")) {
                                            Object newValue = null;
                                            try {
                                                newValue = this.getValue(input, keyFind);
                                            } catch (Exception ex) {
                                                logger.info("onMerge error line: 760, error: " + ex.toString());
                                            }
                                            String updatedTest = value.toString().replace(keyFind, newValue.toString());

                                            // In ra kết quả
                                            System.out.println("Updated Test: " + updatedTest);
                                            if (Objects.nonNull(updatedTest) || setNullWhileNotFound)
                                                entry.setValue(updatedTest);
                                        }
                                    }else {
                                        entry.setValue(value);
                                    }
                                }else {
                                    entry.setValue(value);
                                }

                              }catch (Exception e){
                                entry.setValue(value);
                              }

                        }

                    }
                }
            } catch (Exception e){
                entry.setValue(value);
            }
        });
    }

    private String getString(String str){
        if(str.contains("{") && str.contains("}")){
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(str);
                String unescapedJson = objectMapper.writeValueAsString(jsonNode);
                if(unescapedJson.equals(str)){
                    str = Converter.toString(str);
                }
            } catch (Exception e) {}
        }else if (str.contains("\"")){
            System.out.println();
            str = str.replaceAll("\"","");
        }
        return str;
    }

    public boolean isValidJson(String json) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            objectMapper.readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private String fillValue(HashMap<String, Object> resource, String json){
        List<FlatKeyDto> items = Converter.getKeyValues(resource);
        for (FlatKeyDto item:items) {
            if (item.getKey().equals("input.eformData.noiDangKy")){
                System.out.println();
            }
            String replacement = getString(item.getValue());
            String keyName = "{{" + item.getKey() + "}}";
            if (item.getType() == 1) {
                String keyNameTemp = "\"{{" + item.getKey() + "}}\"";
                if(json.contains(keyNameTemp)){
                    keyName = keyNameTemp;
                }
            }
            json = json.replace(keyName, replacement);
        }
        if(!this.isValidJson(json)){
            json = json.replaceAll("\"\"", "\"");
        }
//        String result = removeAllDoubleBracePlaceholders(json);
        return json;
    }

    public static String removeAllDoubleBracePlaceholders(String input, String option) {
        if (input == null) return null;

        String prefix = option != null ? Pattern.quote(option) : "";
        String suffix = option != null ? Pattern.quote(option) : "";

//        String regex = prefix + "\\{\\{.*?}}" + suffix;
        String regex = prefix + "\\{\\{[^,]*?}}" + suffix;

        return input.replaceAll(regex, option+option);
    }

    public static String removeAllDoubleBracePlaceholders(String input) {
        return input.replaceAll("\\{\\{.*?}}", "");
    }

    public HashMap<String, Object> merge(HashMap<String, Object> input,
                      HashMap<String, Object> output,
                      boolean setNullWhileNotFound,
                      ApiIntegration config){
        String outputJson = Converter.toString(output);
        if(outputJson.contains("{{") && outputJson.contains("}}")){
            outputJson = this.fillValue(input,outputJson);
        }
        outputJson = this.fillValueFunc(outputJson,config, input);
        output = Converter.toHashMap(outputJson);
        this.onMerge(input,output,setNullWhileNotFound);
        return  output;
    }

    public ResponseEntity<Object> exchange(String serviceCode,
                                           String apiCode,
                                           String configCode,
                                           String option,
                                           MultiValueMap<String, MultipartFile> file,
                                           HashMap<String, String> req,
                                           HashMap<String, Object> raw,
                                           HashMap<String, Object> resource,
                                           String showIntputSendEnable,
                                           ServiceLog serviceLogRetry,
                                           Boolean debugEnable) throws Exception {

        /**
         * Đoạn này lấy tất cả cấu hình thuộc về deployment này
         **/
        List<ApiIntegration> configs = this.apiIntegrationService.findByFcodeStartsWith(serviceCode);
        if (Objects.nonNull(configs) && configs.size() > 0){
            this.filteredItems = configs.stream().filter( item -> item.getType() == 0).map(ApiIntegration::getId)
                    .collect(Collectors.toList());
        }
        /**
         * Đoạn này kiểm tra quyền từ hệ thống iGate
         */
        ApiIntegration apiConfig = this.getConfig(configs,serviceCode,null,apiCode,null, null);
        
        logger.info("exchange start 1, apiConfig: " + apiConfig);
        try {
            List<String> partsList = Arrays.asList(this.domainListAPI.split(","));
            this.domainAPiEnable = partsList.contains(apiConfig.getFcode());
            System.out.println("Contains value: " + this.domainAPiEnable);
        }catch (Exception e){

        }
        try {
            if (raw.containsKey("resource")){
                HashMap<String, Object> resourceData = new HashMap<>();
                resourceData.put("resource",raw.get("resource"));
                Map<String, Object> resourceMap = (Map<String, Object>) raw.get("resource");
                this.processDefinitionId = (String) resourceMap.get("processDefinitionId");
                resource = resourceData;
            }else if(Objects.nonNull(resource)){
                this.processDefinitionId = (String) resource.get("processDefinitionId");
            }
            if (raw.containsKey("dossierCode")){
                HashMap<String, String> reqJson = new HashMap<>();
                reqJson.put("dossierCode", raw.get("dossierCode").toString());
                req = reqJson;
            }
        }catch (Exception e){

        }

        Permission permission = apiConfig.getPermission();
        this.checkPermission(permission);
        logger.info("exchange start 2, checkPermission succes");

        List<PramOrHeader> values = this.convert(req);

        logger.info("exchange start 2.2, values:" + values);
        logger.info("exchange start 2.3, configs:" + configs);
        logger.info("exchange start 2.4, serviceCode:" + serviceCode);
        logger.info("exchange start 2.5, apiCode:" + apiCode);
        logger.info("exchange start 2.6, configCode:" + configCode);
        logger.info("exchange start 2.7, option:" + option);
        ApiIntegration featureConfig = this.getConfig(configs,serviceCode,apiCode,configCode,option, null);
        logger.info("exchange start 3, featureConfig:" + featureConfig);

        // Ghi log retry
        ServiceLog serviceLog = new ServiceLog(serviceCode,apiCode,configCode,option,req,raw,resource);
        if(Objects.nonNull(serviceLogRetry)){
            serviceLog = serviceLogRetry;
            if(Objects.isNull(featureConfig.getRetryLimit()) || 0 == featureConfig.getRetryLimit()){
                throw new DigoHttpException(11010, new String[]{"Chưa cấu hình số lần được phép gọi lại!"},
                        HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
            if(Objects.nonNull(featureConfig.getRetryLimit()) && featureConfig.getRetryLimit() > 0 && featureConfig.getRetryLimit() <= serviceLog.getCountRetry()){
                throw new DigoHttpException(11010, new String[]{"Đã vượt quá số lần được phép gọi lại!"},
                        HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
            serviceLog.setCountRetry(serviceLog.getCountRetry()+1);
            serviceLog.setErrMsg(null);
        }
        serviceLog.setUpdateDate(new Date());
        // end ghi log retry

        Request request = featureConfig.getRequest();
        List<PramOrHeader> headers = request.getHeaders();
        // Check if any PramOrHeader has the key "AuthorizationEnable"
        MultiValueMap<String, MultipartFile> ffile = null;
        String tokenTest = null;
        String nameFCodeToken = null;
        try {
            for (PramOrHeader header : headers) {
                if ("nameFCodeTokenTest".equals(header.getKey())) {
                    tokenTest = header.getValue().trim();
                    break;
                }
            }
        }catch (Exception e){

        }
        if(Objects.nonNull(file) && file.size() > 0){
            List<PramOrHeader> forms = null;
            List<Input> inputFiles = apiConfig.getInputs();
            if(Objects.nonNull(inputFiles)){
                inputFiles = inputFiles.stream().filter(i->i.getType().equals("File")).collect(Collectors.toList());
                if(inputFiles.size() > 0){
                    List<String> inputFileKeys = inputFiles.stream().map(i->i.getKey()).collect(Collectors.toList());
                    try{
                        forms = request.getBody().getForm();
                    }catch (Exception ex){
                        logger.info("exchange error line: 852, error: " + ex.toString());
                    }

                    if(Objects.nonNull(forms)){
                        System.out.println();
                        for (PramOrHeader form:forms) {
                            String value = form.getValue().replace("{{input.","").replace("}}","");
                            if(inputFileKeys.contains(value)){
                                if(Objects.isNull(ffile)){
                                    ffile = new LinkedMultiValueMap<>();
                                }
                                String key = form.getKey();
                                var fileValue = file.get(value);
                                ffile.put(key,fileValue);
                            }
                        }
                    }
                }
            }
        }
        logger.info("exchange start 5, ffile:" + ffile);

        /**
         * Lấy các dữ liệu trong context
         */
        String clientId = null;
        try{
            clientId = Context.getJwtParameterValue("azp");
        }catch (Exception ex){
            logger.info("exchange error line: 880, error: " + ex.toString());
        }

        /**
         * Đoạn này gọi api lấy token
         */
        Integer expiredNumber = 0;
        boolean pushedContext = false;
        String token = null;
        String refreshToken = null;
        Integer requiredToken = featureConfig.getRequiredToken();
        Object responseToken = null;
        if(debugEnable){
            //debug bỏ qua lấy token
            requiredToken = 0;
        }
        if(requiredToken==1){
            logger.info("exchange start 7");
            boolean requireGetToken = true;
            ApiIntegration tokenConfig = this.getConfig(configs,serviceCode,"auth",configCode,option, tokenTest);
            if(Objects.isNull(tokenConfig.getIgnoreToken())||tokenConfig.getIgnoreToken()==0){
                if(Objects.nonNull(tokenConfig.getExpiredNumber())){
                    expiredNumber = tokenConfig.getExpiredNumber();
                }
                refreshToken = tokenConfig.getRefreshTokenDefaultValue();
                if(expiredNumber > 0){
                    logger.info("exchange start 8");
                    ApiIntegrationLog logRecent = apiIntegrationLogRepository.getById(featureConfig.getId());
                    logger.info("exchange start 9, logRecent " + logRecent);
                    if(Objects.nonNull(logRecent)){
                        Date refreshTokenConfigTime = tokenConfig.getRefreshTokenDefaultValueTime();
                        Date expiredTime = logRecent.getExpiredTime();
                        if(Objects.nonNull(expiredTime)){
                            Date nowTime = new Date();
                            if(nowTime.before(expiredTime)){
                                requireGetToken = false;
                                token = logRecent.getToken();
//                                log.setToken(token);
//                                log.setExpiredTime(expiredTime);
                            }else if(refreshTokenConfigTime.before(expiredTime)){
                                refreshToken = logRecent.getRefreshToken();
                            }
                        }
                    }
                }

                if(StringHelper.hasValue(refreshToken)){
                    PramOrHeader refreshTokenItem = new PramOrHeader();
                    refreshTokenItem.setKey("refresh_token");
                    refreshTokenItem.setValue(refreshToken);
                    values.add(refreshTokenItem);
                }

                if(requireGetToken) {
                    if(tokenConfig.getRequiredContext()==1 && Objects.nonNull(clientId)){
                        pushedContext = true;
                        this.pushContext(values,resource);
                    }

                    Result tokenResult = this.getResult(tokenConfig,null,values,null,null,false);
                    System.out.println("tokenResult" + tokenResult);
//                    log.setTokenRs(tokenResult);

                    /**
                     * Lấy token, set vào log
                     */
                    String rpJson = null;
                    try{
                        rpJson = tokenResult.getResponse().getRpJson();
                    }catch (Exception ex){
                        serviceLog.setErrAction("Lỗi lấy token");
                        serviceLog.setErrMsg(ex.getCause().getMessage());
                        serviceLog.setStatus(0);
                        logger.info("exchange error line: 949, error: " + ex.toString());
                    }
                    if(Objects.nonNull(rpJson)){
                        logger.info("exchange start 10, rpJson " + rpJson);
                        ObjectMapper mapper = new ObjectMapper();
                        HashMap<String,Object> tokenResponse = mapper.readValue(rpJson, HashMap.class);
                        if(Objects.nonNull(tokenResponse)){
                            responseToken = tokenResponse;
                            String tokenKey = tokenConfig.getResponseTokenKey();
                            Object objToken = this.getValue(tokenResponse,tokenKey);
                            token = objToken.toString();
//                            if(expiredNumber > this.depreciationSecond){
//                                Integer depreciationNumber = expiredNumber - this.depreciationSecond;
//                                if(depreciationNumber > 0){
//                                    Date expiredTime = this.addSecondToDate(log.getTime(),depreciationNumber);
//                                    log.setToken(token);
//                                    log.setExpiredTime(expiredTime);
//                                }
//                            }


                            /**
                             * Lấy refreshToken, set vào log
                             */
//                            String refreshTokenKey = tokenConfig.getResponseRefreshTokenKey();
//                            if(StringHelper.hasValue(refreshTokenKey)){
//                                Object objRefreshToken = this.getValue(tokenResponse,refreshTokenKey);
//                                if(Objects.nonNull(objRefreshToken)){
//                                    refreshToken = objRefreshToken.toString();
//                                    log.setRefreshToken(refreshToken);
//                                }
//                            }
                        }
                    }
                }
            }
        }
        //nếu có lỗi token thì trả về lỗi
        if(Objects.nonNull(serviceLog.getErrMsg())){
            serviceLogRepository.save(serviceLog);
            throw new DigoHttpException(11010, new String[]{serviceLog.getErrMsg()},
                    HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

        logger.info("exchange start 11");
        if(Objects.nonNull(apiConfig.getInputRaw()) && Objects.nonNull(raw)){
            featureConfig.setInputRaw(raw);
        }

        /**
         * Đoạn này gọi api tính năng
         */
        if(featureConfig.getRequiredContext()==1 && Objects.nonNull(clientId) && !pushedContext){
            this.pushContext(values,resource);
        }else if(Objects.nonNull(clientId) && !pushedContext){
            this.pushContext(values,resource);
        }
        if(featureConfig.getRequiredToken()==1){
            this.pushAuth(token,refreshToken,values);
        }
        logger.info("exchange start 11, values " + values);

        featureConfig.setOutputIsFile(apiConfig.getOutputIsFile());
        // set token làm giá trị auth trong resource
        if(Objects.nonNull(responseToken)){
            if(Objects.isNull(resource)){
                resource = new HashMap<>();
            }
            if (Objects.nonNull(raw)){
                resource.put("input",raw);
            }
            else if (Objects.nonNull(req)){
                resource.put("input",values);
            }
            resource.put("auth",responseToken);
        }
        Result featureResult = this.getResult(featureConfig,ffile,values,raw,resource,debugEnable);
        if(debugEnable){
            return ResponseEntity.ok(featureResult);
        }
        Response response = featureResult.getResponse();
        ResponseEntity responseEntity = null;

        logger.info("exchange start 12, featureResult " + featureResult);
        if(Objects.nonNull(response)){
            if(Objects.nonNull(response.getRpJson())){
                String json = null;
                try{
                    json = response.getRpJson();
                }catch (Exception ex){
                    serviceLog.setErrAction("Lỗi thực thi");
                    serviceLog.setErrMsg(ex.getCause().getMessage());
                    serviceLog.setStatus(0);
                    logger.info("exchange error line: 1011, error: " + ex.toString());
                }
//                System.out.println("*** json ***" + json);
                if(Objects.nonNull(json)){
                    ObjectMapper objectMapper = new ObjectMapper();
                    // chỉnh code khi data là mảng [] thì code ko nhận dc data bên phía tích hợp trả về
                    logger.info("exchange start 13");
                    if (json.trim().startsWith("[")) {
                        List<Map<String, Object>> output = objectMapper.readValue(json, new TypeReference<List<Map<String, Object>>>() {});
                        responseEntity = ResponseEntity.ok(output);
                        HashMap<String, Object> data = new HashMap<>();
                        data.put("output", output);
                        featureResult.setData(data);
                    }else{
                        try {
                            // điều chỉnh ktra response trả về là string chứ không phải object

                            //HashMap<String, Object> output = objectMapper.readValue(json, HashMap.class);
                            HashMap<String, Object> output = Converter.toHashMapResult(json);
                            if(Objects.isNull(output)){
                                // nếu string thì output trả về là null
                                try {
                                    responseEntity = ResponseEntity.ok(json);
                                    featureResult.setData(output);
                                }catch (Exception ex){
                                    logger.info("check errorconfigv2 line: 1281, error: " + ex.toString());
                                }
                            }else {
                                if (Objects.nonNull(output.get("responseErr")) && Boolean.TRUE.equals(output.get("responseErr"))){
                                    output.put("title", "Dữ liệu trả về có ký tự đặc biệt");
                                }else {
                                    if (Objects.nonNull(request) && request.isMapResponse()) {
                                        HashMap<String, Object> template = request.getMResponse();
                                        HashMap<String, Object> data = new HashMap<>();
                                        data.put("output", output);
                                        logger.info("exchange start 14, template " + template);
                                        template = this.merge(data, template, true, featureConfig);/**Merge response**/
                                        output = template;
                                        logger.info("exchange start 15, output " + output);
                                    }
                                }
                                if (Objects.nonNull(showIntputSendEnable) && !showIntputSendEnable.isEmpty()) {
                                    if (Boolean.parseBoolean(showIntputSendEnable)) {
                                        output.put("inputSend", featureResult.getBody());
                                    }
                                }
                                responseEntity = ResponseEntity.ok(output);
                                featureResult.setData(output);
                            }
                        }catch (Exception e){
                            serviceLog.setErrAction("Lỗi thực thi");
                            serviceLog.setErrMsg(e.getCause().getMessage());
                            serviceLog.setStatus(0);
                            logger.info("exchange error line: 1041, error: " + e.toString());
                            HashMap<String, Object> outputResponse = new  HashMap<String, Object>();
                            if(Objects.nonNull(showIntputSendEnable) && !showIntputSendEnable.isEmpty()){
                                if (Boolean.parseBoolean(showIntputSendEnable)) {
                                    outputResponse.put("inputSend", featureResult.getBody());
                                }    
                            }
                            outputResponse.put("message", e.getMessage());
                            outputResponse.put("status", 500);
                            responseEntity =  new ResponseEntity<>(outputResponse, HttpStatus.INTERNAL_SERVER_ERROR);
                            featureResult.setData(outputResponse);
                        }

                    }
                }
                // check response trả về với cấu hình lỗi trên cauhinhv2
                serviceLog = this.checkErr(featureConfig,response,serviceLog);
            }else {
                byte[] bytes = null;
                try{
                    bytes = response.getRpBytes();
                }catch (Exception ex){
                    serviceLog.setErrAction("Lỗi thực thi");
                    serviceLog.setErrMsg(ex.getCause().getMessage());
                    serviceLog.setStatus(0);
                    logger.info("exchange error line: 1061, error: " + ex.toString());
                }
                if(Objects.nonNull(bytes)){
                    Resource resourceFile = new ByteArrayResource(bytes);
                    String filename = response.getFilename();
                    String sContentType = response.getContentType();
                    MediaType contentType = MediaType.valueOf(sContentType);
                    String mimeType = null;
                    if (contentType != null) {
                        mimeType = contentType.toString();
                    }
                    responseEntity = ResponseEntity.ok()
                            .contentType(MediaType.parseMediaType(mimeType))
                            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                            .body(resourceFile);
                }
            }
        }else{
            serviceLog.setErrAction("Lỗi thực thi");
            serviceLog.setErrMsg(featureResult.getErrMsg());
            serviceLog.setStatus(0);
        }
        featureResult.setResponse(null);
        if(Objects.nonNull(serviceLog.getErrMsg())){
            // Ghi log retry
            serviceLogRepository.save(serviceLog);
            // end ghi log retry

            if(Objects.nonNull(showIntputSendEnable) && !showIntputSendEnable.isEmpty()){
                HashMap<String, Object> outputResponse = new  HashMap<String, Object>();
                if (Boolean.parseBoolean(showIntputSendEnable)) {
                    outputResponse.put("inputSend", featureResult.getBody());
                }
                outputResponse.put("errMsg", serviceLog.getErrMsg());
                outputResponse.put("errAction", serviceLog.getErrAction());
                outputResponse.put("status", 500);
                responseEntity =  new ResponseEntity<>(outputResponse, HttpStatus.INTERNAL_SERVER_ERROR);
            }else {
                throw new DigoHttpException(10404, new String[]{serviceLog.getErrAction(),serviceLog.getErrMsg()}, HttpServletResponse.SC_BAD_REQUEST);
            }
        }else if(Objects.nonNull(serviceLogRetry)){
            serviceLog.setErrAction("Thành công");
            serviceLog.setErrMsg(Objects.nonNull(featureResult.getData()) ? featureResult.getData().toString() : responseEntity.getBody().toString());
            serviceLog.setStatus(1);
            serviceLogRepository.save(serviceLog);
        }
        //comment
        logger.info("exchange start 16, end ");
        return responseEntity;
    }

    private String exeJavascript(Object data, String subscript){
        if(!subscript.endsWith(";")){
            subscript += ";";
        }
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("JavaScript");
        String sdata = Converter.toString(data);
        engine.put("data", sdata);
        String script = "function toObject(jsonString) {" +
                        "    var obj = JSON.parse(jsonString);" + // Chuyển chuỗi JSON thành object
                        "    return obj;" + // Trả về object
                        "};" +
                        "var resource = toObject(data);" + // Gọi hàm và gán kết quả cho customData
                        subscript +
                        "JSON.stringify(resource);";
        try {
            String json = engine.eval(script).toString();
            return json;
        } catch (Exception ex) {
            logger.info("exeJavascript error line: 1149, error: " + ex.toString());
        }
        return null;
    }

    public HashMap<String,Object> convertObjectToJson(Object data) {
        String subScript = "resource.custom = 1";
        String json = this.exeJavascript(data,subScript);
        HashMap<String,Object> customData = Converter.toHashMap(json);
        return customData;
    }

    private List<String> extractVariables(String input, String start, String end) {
        List<String> variables = new ArrayList<>();
        String regex = start + "(.*?)" + end;
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String variable = matcher.group(1);
            variables.add(variable);
        }
        return variables;
    }

    public List<FuncValue> getFuncValues(String json){
        List<FuncValue> items = new ArrayList<>();
        List<FuncValue> inits = FuncValue.init();
        for(FuncValue init: inits){
            String funcName = init.getFuncName();
// để danh debug hong được xóa
//            if (funcName.equals("toDate")){
//                System.out.println();
//            }
            List<String> values = this.extractVariables(json, funcName + "\\('", "'\\)");
            List<FuncValue> subItems = values.stream().map(i->new FuncValue(funcName,i)).collect(Collectors.toList());
            items.addAll(subItems);
        }
        return items;
    }

    private String fillValueFunc(String json, ApiIntegration config, HashMap<String, Object> input ){
        // fix lỗi các json có duedate or date có định dạng +0700 thì hàm today lỗi
        // hàm này để tìm kiếm date có format +0700 thì replace bỏ
        // start thnghia
        String regex = "(\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d{3})?)(?:[+-]\\d{2}:?\\d{2})";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(json);
        json = matcher.replaceAll("$1");
        // end thnghia
        //json = removeAllDoubleBracePlaceholders(json,"'");
        List<FuncValue> items = this.getFuncValues(json);
        String[] fcodes = config.getFcode().split("/");
        String deploymentId = Context.getJwtParameterValue("deployment_id");
//        deploymentId = "66e105fa77d3483c16d3943f";
        String fcode = fcodes[0] + "/" + deploymentId;
        ApiIntegration igateConfig = this.apiIntegrationRepository.findByFcode(fcode);
        for (FuncValue item: items){
//            if (item.getFuncName().equals("getResource")){
//                System.out.printf("");
//            }
//            String newValue1 = this.apiIntegrationService.callFunc(item.getFuncName(),item.getValue(),igateConfig, input);
            String newValue = this.apiIntegrationService.callFunc(item.getFuncName(),item.getValue(),igateConfig, input, this.processDefinitionId, this.filteredItems);
//            String oldValue = item.getFuncName() + "\\('" + item.getValue() + "'\\)";
            String oldValue = this.checkTypeFuncName(item.getFuncName(),item.getValue());
            if(item.getValue().contains("{{") && item.getValue().contains("}}") && !config.getRequest().getUrl().contains("/--change-status")){
                json = replaceLastParamInToDateIfDoubleBrace(json,item.getFuncName());
            }else{
                json = json.replaceAll(oldValue,newValue);
            }
        }
        return json;
    }

    private String checkTypeFuncName(String getFuncName,String getValue) {
        String defaultValue = getFuncName + "\\('" + getValue + "'\\)";
        switch (getFuncName) {
            case "replaceData":
                if (Objects.nonNull(getValue) && !getValue.isEmpty()){
                    String[] values = getValue.split(",");
                    if (values.length > 2 && Objects.nonNull(values[2]) && !values[2].isEmpty()){
                        int checkType  = this.checkTypeVariables(values[2]);
                        if (checkType != 1){
                            defaultValue = "\""
                                    + getFuncName
                                    + "\\('"
                                    + getValue
                                    + "'\\)"
                                    + "\"";
                        }
                        return defaultValue;
                    }
                }
            default:
                return defaultValue; // Không xác định
        }
    }

    private int checkTypeVariables(String type) {
        switch (type) {
            case "Integer":
                return 2;
            case "Double":
                return 3;
            case "Boolean":
                return 4;
            default:
                return 1; // Kiểu String
        }
    }

    private List<String> uploadFileIds(HashMap<String, Object> resource, ApiIntegration config){
        try{

            List<String> fileIdsList = this.getFileIdsList(resource, config);
            return fileIdsList;

        }catch (Exception e){
            logger.info("uploadFileIds error line: 1206, error: " + e.toString());
        }
        return null;
    }

    private List<String> getFileIdsList(HashMap<String, Object> resource , ApiIntegration config){
        List<String> fileIds = new ArrayList<>();
        try{
            List<String> ids = new ArrayList<>();
            Map<String, Object> mainResource = (Map<String, Object>) resource.get("resource");
            if (mainResource.containsKey("dossierFormFile")) {
                Object dossierFormFileObj = mainResource.get("dossierFormFile");

                if (dossierFormFileObj instanceof List) {
                    List<Map<String, Object>> dossierFormFileList = (List<Map<String, Object>>) dossierFormFileObj;

                    for (Map<String, Object> dossierForm : dossierFormFileList) {

                        if (dossierForm.containsKey("file")) {
                            List<Map<String, Object>> files = (List<Map<String, Object>>) dossierForm.get("file");
                            for (Map<String, Object> file : files) {
                                ids.add(file.get("id").toString());
                            }
                        }
                    }
                } else {
                }
            } else {
                System.out.println("Không tìm thấy trường dossierFormFile trong resource.");
            }

            String URL = microservice.filemanUri("file/--base64").toUriString();
            URL += "?ids=" + String.join(",",ids);
            String token = MicroserviceExchange.getToken();
            try {
                if (Objects.isNull(token)){
                    token = Context.getJwtAuthenticationTokenValue();
                }
            }catch (Exception e){

            }
            System.out.println("get base64 url:" + URL + " token: " + token);
            String jsonString = MicroserviceExchange.get(this.restTemplate, URL, token,  String.class);
//            String base64 = mainResource.get("base64").toString();
//            String jsonString = "[\n" +
//                    "    {\n" +
//                    "        \"filename\": \"gcn 30-593_signed.pdf\",\n" +
//                    "        \"base64\": \"" + base64 + "\"\n" +
//                    "    }\n" +
//                    "]";
            System.out.println("get json base64: jsonString");
            fileIds = convertBase64ToMultipartFile(jsonString, config);

        }catch (Exception e){
            logger.info("getFileIdsList error line: 1245, error: " + e.toString());
        }

        return  fileIds;
    }

    private String callUploadFileIds(MultipartFile multipartFile, ApiIntegration config){
        String fileIdsList = "";
        try {
            String path = config.getFcode();
            if (path.contains("environmental-resources")) {
                //upload file
                //value 5 = file
                HashMap<String, String> req = new HashMap<>();
                String adapterUrl = "/service/environmental-resources/--upload";
                // Tạo FormData
                UriComponentsBuilder uriBuilder = microservice.adapterUri(adapterUrl);
//                uriBuilder.uri(URI.create("http://localhost:8081/service/environmental-resources/--upload"));
                UriComponents uriComponents = uriBuilder.build();
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                String token = null;
                try {
                    token = Context.getJwtAuthenticationTokenValue();
                }catch (Exception e){
                    token = MicroserviceExchange.getToken();
                }
                headers.setBearerAuth(token);
                MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
                try {
                    String fileName = multipartFile.getOriginalFilename();
                    InputStream inputStream = multipartFile.getInputStream();

                    formData.add("file", new MultipartInputStreamFileResource(inputStream, fileName));
                    formData.add("title", fileName);

                    System.out.println("Đã thêm file: " + fileName);

                } catch (Exception e) {
                    System.out.println("Lỗi đọc file: " + multipartFile.getOriginalFilename());
                }

                // Tạo đối tượng HttpEntity với headers và formData
                HttpEntity<?> requestEntity = new HttpEntity<>(formData, headers);
                ResponseEntity<Map> response = restTemplate.exchange(
                        uriComponents.toUriString(),
                        HttpMethod.POST,
                        requestEntity,
                        Map.class
                );
                HashMap<String,Object> fileData = Converter.toHashMap(response);
                Object file = fileData.get("body");
                Map<String, Object> fileMap = (Map<String, Object>) file;
                return fileMap.get("id").toString();
            }
        }catch (Exception e){
            logger.info("callUploadFileIds error line: 1266, error: " + e.toString());
        }
        return fileIdsList;
    }

    public List<String> convertBase64ToMultipartFile(String jsonString , ApiIntegration config) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            List<Base64Dto> data = mapper.readValue(jsonString, new TypeReference<List<Base64Dto>>() {});

            List<String> fileIds = new ArrayList<>();
            for (Base64Dto dto : data) {
                try {
                    // Giải mã base64 thành mảng byte
                    byte[] decodedBytes = Base64.getDecoder().decode(dto.getBase64());

                    // Tạo ByteArrayResource từ mảng byte
                    ByteArrayResource byteArrayResource = new ByteArrayResource(decodedBytes) {
                        @Override
                        public String getFilename() {
                            return dto.getFilename();  // trả về tên file từ dto
                        }
                    };

                    // Tạo MultipartFile từ ByteArrayResource
                    MultipartFile multipartFile = new MultipartFile() {
                        @Override
                        public String getName() {
                            return "file";  // Tên tham số file
                        }

                        @Override
                        public String getOriginalFilename() {
                            return byteArrayResource.getFilename();  // Tên file
                        }

                        @Override
                        public String getContentType() {
                            return dto.getContentType();  // Kiểu MIME (ví dụ "image/png")
                        }

                        @Override
                        public boolean isEmpty() {
                            return byteArrayResource.contentLength() == 0;
                        }

                        @Override
                        public long getSize() {
                            return byteArrayResource.contentLength();
                        }

                        @Override
                        public byte[] getBytes() throws IOException {
                            return byteArrayResource.getByteArray();
                        }

                        @Override
                        public InputStream getInputStream() throws IOException {
                            return byteArrayResource.getInputStream();
                        }

                        // Triển khai phương thức transferTo
                        @Override
                        public void transferTo(File dest) throws IOException, IllegalStateException {
                            // Lưu tệp vào hệ thống file
                            try (InputStream inputStream = byteArrayResource.getInputStream()) {
                                java.nio.file.Files.copy(inputStream, dest.toPath());
                            }
                        }
                    };
                    String fileid = this.callUploadFileIds(multipartFile, config);
                    fileIds.add(fileid);
                } catch (IllegalArgumentException e) {
                    e.printStackTrace(); // Xử lý lỗi nếu chuỗi base64 không hợp lệ
                }
            }
            return fileIds;
            // Tiến hành xử lý files...
        } catch (Exception e) {
            logger.info("convertBase64ToMultipartFile error line: 1350, error: " + e.toString());
            e.printStackTrace();
        }
        return null;
    }

    private static String getValueByPath(Map<String, Object> data, String path) {
        String[] keys = path.split("\\.");
        Object current = data;
        for (String key : keys) {
            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(key);
            } else {
                return null;
            }
        }
        return current.toString();
    }

    // đầu vào featureConfig (api cấu hình err trên cauhinhv2, response (dữ liệu trả về từ api chuyên ngành, serviceLog hiện tại.
    private ServiceLog checkErr(ApiIntegration featureConfig, Response response, ServiceLog serviceLog){
        try {
            if (Objects.nonNull(featureConfig) && Objects.nonNull(featureConfig.getErrs()) && featureConfig.getErrs().size() > 0) {
                String json = null;
                String httpStatusResult = null;
                json = response.getRpJson();
                httpStatusResult = response.getHttpStatus();
                if(Objects.nonNull(response)) {
                    HashMap<String, Object> output = Converter.toHashMap(json);
                    for (ErrorDefine err : featureConfig.getErrs()) {
                        if (Objects.nonNull(err.getHttpStatus()) && Objects.nonNull(httpStatusResult) && httpStatusResult.equals(err.getHttpStatus().toString())) {
                            // gán valueErrResult cần check là json trả về (có thể là string)
                            String valueErrResult = json;
                            String valueErrMsgResult = json;
                            // check nếu output khác null (là object) thì gán lại valueErrResult bằng object
                            if (Objects.nonNull(output)) {
                                if (Objects.nonNull(featureConfig.getRequest()) && featureConfig.getRequest().isMapResponse()) {
                                    HashMap<String, Object> template = featureConfig.getRequest().getMResponse();
                                    HashMap<String, Object> data = new HashMap<>();
                                    data.put("output", output);
                                    logger.info("exchange start 14, template " + template);
                                    template = this.merge(data, template, true, featureConfig);/**Merge response**/
                                    output = template;
                                    logger.info("exchange start 15, output " + output);
                                }
                            }
                            // check giá trị statusKey và statusValue
                            if (Objects.nonNull(err.getStatusKey()) && Objects.nonNull( err.getStatusValue())){
                                if(Objects.nonNull(output)){
                                    valueErrResult = getValueByPath(output, err.getStatusKey());
                                }
                                if(Objects.nonNull(valueErrResult)){
                                    for (String statusValue : err.getStatusValue()) {
                                        if (valueErrResult.equalsIgnoreCase(statusValue) || valueErrResult.toLowerCase().contains(statusValue.toLowerCase())) {
                                            if(Objects.isNull(err.getMethod()) || (Objects.nonNull(err.getMethod()) && 0==err.getMethod())){
                                                serviceLog.setErrAction("Lỗi thực thi");
                                                serviceLog.setErrMsg(json);
                                                serviceLog.setStatus(0);
                                            }else{
                                                serviceLog.setErrAction("Response hợp lệ theo cấu hình");
                                                serviceLog.setErrMsg(null);
                                            }
                                            return serviceLog;
                                        }
                                    }
                                }
                            }
                            // check giá trị errMsgKey và content
                            if (Objects.nonNull(err.getErrMsgKey()) && Objects.nonNull(err.getContent())){
                                if(Objects.nonNull(output)) {
                                    valueErrMsgResult = getValueByPath(output, err.getErrMsgKey());
                                }
                                if(Objects.nonNull(valueErrMsgResult)){
                                    for (String content : err.getContent()) {
                                        if (valueErrMsgResult.equalsIgnoreCase(content) || valueErrMsgResult.toLowerCase().contains(content.toLowerCase())) {
                                            if(Objects.isNull(err.getMethod()) || (Objects.nonNull(err.getMethod()) && 0==err.getMethod())){
                                                serviceLog.setErrAction("Lỗi thực thi");
                                                serviceLog.setErrMsg(json);
                                                serviceLog.setStatus(0);
                                            }else{
                                                serviceLog.setErrAction("Response hợp lệ theo cấu hình");
                                                serviceLog.setErrMsg(null);
                                            }
                                            return serviceLog;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            logger.info("check errorconfigv2 line: 1281, error: " + ex.toString());
        }
        return serviceLog;
    }

    public static String replaceLastParamInToDateIfDoubleBrace(String input, String value) {
        if (input == null) return null;
        Pattern pattern = Pattern.compile(value+"\\((.*?)\\)");
        Matcher matcher = pattern.matcher(input);

        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String args = matcher.group(1);
            String[] parts = args.split(",", -1);
            if (parts.length > 0) {
                boolean hasDoubleBrace = false;
                for (int i = 0; i < parts.length; i++) {
                    String part = parts[i].trim();
                    String stripped = part.replaceAll("^['\"]|['\"]$", "");
                    if (stripped.startsWith("{{") && stripped.endsWith("}}")) {
                        hasDoubleBrace = true;
                        String quoteStart = part.startsWith("'") ? "'" : (part.startsWith("\"") ? "\"" : "");
                        String quoteEnd = part.endsWith("'") ? "'" : (part.endsWith("\"") ? "\"" : "");
                        parts[i] = quoteStart.equals(quoteEnd) ? quoteStart + quoteEnd : quoteStart + "";
                    }
                }

                if (hasDoubleBrace) {
                    String newArgs = String.join(",", parts);
                    matcher.appendReplacement(result, value + "(" + newArgs + ")");
                }
            }
        }
        matcher.appendTail(result);
        return result.toString();
    }

    public static String buildCurlDebug(String url, HttpMethod method, HttpEntity<?> entity) {
        StringBuilder curl = new StringBuilder("curl -X ").append(method.name()).append(" '").append(url).append("'");

        HttpHeaders headers = entity.getHeaders();
        for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
            for (String value : entry.getValue()) {
                curl.append(" -H '").append(entry.getKey()).append(": ").append(value).append("'");
            }
        }

        Object body = entity.getBody();
        if (body != null) {
            curl.append(" -d '").append(body.toString().replace("'", "\\'")).append("'");
        }

        return curl.toString();
    }


}
