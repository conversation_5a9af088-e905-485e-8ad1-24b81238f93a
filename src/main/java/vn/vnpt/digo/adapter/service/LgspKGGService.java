package vn.vnpt.digo.adapter.service;

import java.util.*;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.iworkplace.TokenResponseDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.CoopDossierDetailDto;
import vn.vnpt.digo.adapter.pojo.HouseDossierDetailDto;
import vn.vnpt.digo.adapter.util.*;


@Service
public class LgspKGGService {

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IntegratedConfigurationService configurationService;

    private ObjectId serviceId = new ObjectId("5f7c16069abb62f511890011");

    @Value(value = "${lgsp.kgg.url}")
    private String url;

    @Value(value = "${lgsp.kgg.authorization}")
    private String authorization;

    @Value("${vnpt.permission.check-user}")
    private String scope;

    private final Logger logger = LoggerFactory.getLogger(BusinessRegistrationService.class);

    public String getTokenLGSPKGG(BusinessRegistrationParamsKGGDto params){
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", MediaType.valueOf("application/x-www-form-urlencoded").toString());
        headers.add(HttpHeaders.AUTHORIZATION, "Basic " + authorization);
        MultiValueMap<String, String> map= new LinkedMultiValueMap<>();
        map.add("grant_type", "client_credentials");
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);

        String uri = this.url + "/token";
        ResponseEntity<TokenResponseDto> result = restTemplate.exchange(uri, HttpMethod.POST, entity, TokenResponseDto.class);
        logger.info("Access Token LGSP: " + result.getBody().getAccess_token());
        return result.getBody().getAccess_token();
    }

    public EntInfoDetailDto getEntInfoKggDto(BusinessRegistrationParamsKGGDto params) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new EntInfoDetailDto();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(this.getTokenLGSPKGG(params));
        String url = this.url + "/NGSP-DKKD/1.0/DoanhNghiep/chiTietDoanhNghiep";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?msdn=" + params.getMsdn(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            JsonObject json = jsonObject.get("GetEntCert").getAsJsonArray().get(0).getAsJsonObject();
            EntInfoDetailDto result = g.fromJson(json, EntInfoDetailDto.class);
            return result;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public CoopInfoDetailDto getCoopInfoDetailDto(BusinessRegistrationParamsKGGDto params) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new CoopInfoDetailDto();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(this.getTokenLGSPKGG(params));
        String url = this.url + "/NGSP-DKKD/1.0/HopTacXa/chiTietHopTacXa";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?mst=" + params.getMst(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            JsonObject json = jsonObject.get("CoopCert").getAsJsonArray().get(0).getAsJsonObject();
            CoopInfoDetailDto result = g.fromJson(json, CoopInfoDetailDto.class);
            return result;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public HouseInfoDetailDto getHouseInfoDetailDto(BusinessRegistrationParamsKGGDto params) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new HouseInfoDetailDto();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(this.getTokenLGSPKGG(params));
        String url = this.url + "/NGSP-DKKD/1.0/HoKinhDoanh/chiTietHoKinhDoanh";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?mst=" + params.getMst(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            JsonObject json = jsonObject.get("HHCert").getAsJsonArray().get(0).getAsJsonObject();
            HouseInfoDetailDto result = g.fromJson(json, HouseInfoDetailDto.class);
            return result;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    // get detail dossier
    public EntDossierDetailDto getEntDosserDetailKggDto(String processId, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        String url = this.url + "/NGSP-DKKD/1.0/DoanhNghiep/thongTinHoSo";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?processID=" + processId, HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            if (jsonObject != null) {
                if (jsonObject.get("DongBoHoSoMC_DP") != null && jsonObject.get("DongBoHoSoMC_DP").getAsJsonArray().size()>0) {
                    JsonObject json = jsonObject.get("DongBoHoSoMC_DP").getAsJsonArray().get(0).getAsJsonObject();
                    EntDossierDetailDto result = g.fromJson(json, EntDossierDetailDto.class);
                    return result;
                }
            }
            return null;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public CoopDossierDetailDto getCoopDosserDetailKggDto(String processId, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        String url = this.url + "/NGSP-DKKD/1.0/HopTacXa/thongTinHoSo";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?processID=" + processId, HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            if (jsonObject != null) {
                if (jsonObject.get("GetDocCoop") != null && jsonObject.get("GetDocCoop").getAsJsonArray().size()>0) {
                    JsonObject json = jsonObject.get("GetDocCoop").getAsJsonArray().get(0).getAsJsonObject();
                    CoopDossierDetailDto result = g.fromJson(json, CoopDossierDetailDto.class);
                    return result;
                }
            }
            return null;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public HouseDossierDetailDto getHouseDosserDetailKggDto(String processId, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        String url = this.url + "/NGSP-DKKD/1.0/HoKinhDoanh/thongTinHoSo";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?processID=" + processId, HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            Gson g = new Gson();
            JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
            if (jsonObject != null) {
                if (jsonObject.get("DongBoHoSoMC_DP") != null && jsonObject.get("DongBoHoSoMC_DP").getAsJsonArray().size()>0) {
                    JsonObject json = jsonObject.get("DongBoHoSoMC_DP").getAsJsonArray().get(0).getAsJsonObject();
                    HouseDossierDetailDto result = g.fromJson(json, HouseDossierDetailDto.class);
                    return result;
                }
            }
            return null;
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    // get list dossier
    public Page<EntDossierDetailDto> getEntDossiersKggDto(BusinessRegistrationParamsKGGDto params, Pageable pageable) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }

        List<EntDossierDetailDto> entDossierDetailDtos = new ArrayList<EntDossierDetailDto>();
        HttpHeaders headers = new HttpHeaders();
        String token = this.getTokenLGSPKGG(params);
        headers.setBearerAuth(token);
        String url = this.url + "/NGSP-DKKD/1.0/DoanhNghiep/danhSachHoSo";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?from_date=" + params.getFromDate() + "&to_date=" + params.getToDate(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            int total = 0;
            if (response.getBody() != null) {
                JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
                JsonArray json = jsonObject.get("Ent_list").getAsJsonArray();
                Gson g = new Gson();
                List<Map<String, Object>> result = g.fromJson(json, ArrayList.class);

                total = result.size();
                int start = Math.toIntExact(pageable.getOffset());
                int end = (start + pageable.getPageSize()) > total ? total : (start + pageable.getPageSize());
                result = new PageImpl<>(result.subList(start, end), pageable, total).getContent();

                for (Map<String, Object> obj : result) {
                    String process_id = obj.get("process_id").toString();
                    EntDossierDetailDto entDossierDetailDto = this.getEntDosserDetailKggDto(process_id, token);
                    if (entDossierDetailDto != null) {
                        entDossierDetailDtos.add(entDossierDetailDto);
                    }
                }
            }
            return new PageImpl<>(entDossierDetailDtos, pageable, total);
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public Page<CoopDossierDetailDto> getCoopDossiersKggDto(BusinessRegistrationParamsKGGDto params, Pageable pageable) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
        List<CoopDossierDetailDto> coopDossierDetailDtos = new ArrayList<CoopDossierDetailDto>();
        HttpHeaders headers = new HttpHeaders();
        String token = this.getTokenLGSPKGG(params);
        headers.setBearerAuth(token);
        String url = this.url + "/NGSP-DKKD/1.0/HopTacXa/danhSachHoSo";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?from_date=" + params.getFromDate() + "&to_date=" + params.getToDate(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            int total = 0;
            if (response.getBody() != null) {
                JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
                JsonArray json = jsonObject.get("Coop_doc_list").getAsJsonArray();
                Gson g = new Gson();
                List<Map<String, Object>> result = g.fromJson(json, ArrayList.class);

                total = result.size();
                int start = Math.toIntExact(pageable.getOffset());
                int end = (start + pageable.getPageSize()) > total ? total : (start + pageable.getPageSize());

                result = new PageImpl<>(result.subList(start, end), pageable, total).getContent();

                for (Map<String, Object> obj : result) {
                    String process_id = obj.get("process_id").toString();
                    CoopDossierDetailDto coopDossierDetailDto = this.getCoopDosserDetailKggDto(process_id, token);
                    if (coopDossierDetailDto != null) {
                        coopDossierDetailDtos.add(coopDossierDetailDto);
                    }
                }
            }
            return new PageImpl<>(coopDossierDetailDtos, pageable, total);
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public Page<HouseDossierDetailDto> getHouseDossiersKggDto(BusinessRegistrationParamsKGGDto params, Pageable pageable) {
        IntegratedConfigurationDto config;
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        //check attt
        Boolean isAdmin = false;
        if (scope != null && !scope.isEmpty()) {
            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission(scope);
            if (permission != null) {
                isAdmin = true;
            }
        }
        if (!isAdmin) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
        List<HouseDossierDetailDto> houseDossierDetailDtos = new ArrayList<HouseDossierDetailDto>();
        HttpHeaders headers = new HttpHeaders();
        String token = this.getTokenLGSPKGG(params);
        headers.setBearerAuth(token);
        String url = this.url + "/NGSP-DKKD/1.0/HoKinhDoanh/danhSachHoSo";
        System.out.println("url " + url);
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url + "?from_date=" + params.getFromDate() + "&to_date=" + params.getToDate(), HttpMethod.GET, request, String.class);
        if (response != null) {
            System.out.println("result");
            System.out.println(response);
            int total = 0;
            if (response.getBody() != null) {
                JsonObject jsonObject = new JsonParser().parse(response.getBody()).getAsJsonObject();
                JsonArray json = jsonObject.get("hh_list").getAsJsonArray();
                Gson g = new Gson();
                List<Map<String, Object>> result = g.fromJson(json, ArrayList.class);

                total = result.size();
                int start = Math.toIntExact(pageable.getOffset());
                int end = (start + pageable.getPageSize()) > total ? total : (start + pageable.getPageSize());
                result = new PageImpl<>(result.subList(start, end), pageable, total).getContent();

                for (Map<String, Object> obj : result) {
                    String process_id = obj.get("process_id").toString();
                    HouseDossierDetailDto houseDossierDetailDto = this.getHouseDosserDetailKggDto(process_id, token);
                    if (houseDossierDetailDto != null) {
                        houseDossierDetailDtos.add(houseDossierDetailDto);
                    }
                }
            }
            return new PageImpl<>(houseDossierDetailDtos, pageable, total);
        } else {
            throw new DigoHttpException(10004, HttpServletResponse.SC_NOT_FOUND);
        }
    }
}
