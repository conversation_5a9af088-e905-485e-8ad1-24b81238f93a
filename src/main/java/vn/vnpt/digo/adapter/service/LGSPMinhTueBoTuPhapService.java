package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.IntegrationParamsDto;
import vn.vnpt.digo.adapter.dto.khdt_dto.tokenDTO;
import vn.vnpt.digo.adapter.dto.minhtue.TokenResDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.IntegratedService;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Translator;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.HashMap;
import java.util.Base64;
@Service
@Component
public class LGSPMinhTueBoTuPhapService {
    private ObjectId serviceId = new ObjectId("5f96885432c7c6aed84b7a7d");
    Logger logger = LoggerFactory.getLogger(LGSPMinhTueBoTuPhapService.class);

    @Autowired
    private IntegratedConfigurationService configurationService;
    
    @Autowired
    private IntegratedLogsService integratedLogsService;

    @Autowired
    private Translator translator;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Microservice microservice;
    
    //lgsp minh tue 
    @Value(value = "${lgspminhtue.kgg.configId}")
    private String lgspKGGConfigId;

    @Value(value = "${digo.custom-header}")
    private Integer digoCustomHeader;
    
    
    IntegratedConfigurationDto config;


    private TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) {
        String strConsumer = consumerKey + ":" + consumerSecret;
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(tokenUrl);
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();

        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(auth);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST, request, Object.class);
            logger.info("Http result:");
            System.out.println(result);
            TokenResDto token = GsonUtils.copyObject(result.getBody(), TokenResDto.class);
            return token;
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
            throw e;
        }
    }

    public TokenResDto getTokenFPT(String urlToken, String lgspaccesstoken) {

        String body = "grant_type=client_credentials";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("lgspaccesstoken", lgspaccesstoken);
        HttpEntity<String> request = new HttpEntity<>(body, headers);

        try {
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> result = restTemplate.exchange(
                    urlToken ,
                    HttpMethod.POST, request, String.class);

            System.out.println("result GET" +  result.getBody());

            JsonObject jsonResponse = new JsonParser().parse(result.getBody()).getAsJsonObject();


            String accessTokenData =  jsonResponse.get("access_token").getAsString();
            String scope =  jsonResponse.get("scope").getAsString();
            String token_type =  jsonResponse.get("token_type").getAsString();
            int expires_in =  jsonResponse.get("expires_in").getAsInt();

            // tokenDTO tokenData = new tokenDTO(accessTokenData,scope,token_type,expires_in);

            TokenResDto tokenData = new TokenResDto();
            tokenData.setAccessToken(accessTokenData);
            tokenData.setTokenType(token_type);
            tokenData.setExpiresIn(expires_in);


            return tokenData;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public  ResponseEntity<String> getAllLoaiVanBan(IntegrationParamsDto params,List<Integer> unitCodes){
        ResponseEntity<String> results = null;
        HttpHeaders cleanedHeaders = new HttpHeaders();

        try{
            IntegratedConfigurationDto config;
            if (Objects.nonNull(params.getConfigId())) {
                config = configurationService.getConfig(params.getConfigId());
            } else {
                config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
            }
            if (Objects.isNull(config)) {
                throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                        HttpServletResponse.SC_NOT_FOUND);
            }
            logger.info("getConfig: ");
            System.out.println(config);
            String gatewayToken = config.getParametersValue("gateway-token");// "https://api.quangnam.gov.vn/token";
            String consumerKey = config.getParametersValue("consumer-key"); //"GJbFfb2XnDMCJoTq0OfCD1kO3Zga";
            String consumerSecret = config.getParametersValue("consumer-secret");//"d3S6ZYqUGyqASLKrqKA41WhuiCka";
            String tokenValue = config.getParametersValue("token-value");
            String token = "";
            if (digoCustomHeader == 1) {
                token = getTokenFPT(gatewayToken, tokenValue).getAccessToken();
            } else {
                token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
            }

            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setBearerAuth(token);
                headers.setContentType(MediaType.APPLICATION_JSON);
                if (digoCustomHeader == 1) {
                    headers.set("lgspaccesstoken", tokenValue);
                }
                String t_gridRequest = "{\"sort\":[{\"field\":\"ID\",\"dir\":\"desc\"}]}";
                Map<String, Object> _params = new HashMap<>();
                _params.put("DonVi", unitCodes);
                _params.put("t_gridRequest", t_gridRequest);
                HttpEntity<?> request = new HttpEntity<>(_params,headers);
                String getAllLoaiVanBanUrl = config.getParametersValue("get-all-van-ban-url").toString(); //"https://api.quangnam.gov.vn/NGSP-VBQPPL/GetAllLoaiVanBan";//
                results = restTemplate.exchange(getAllLoaiVanBanUrl,
                        HttpMethod.POST, request, String.class);
                logger.info("Http results:");
                results.getHeaders().forEach((key, values) -> {
                    if (!key.equalsIgnoreCase("transfer-encoding")) {
                        cleanedHeaders.put(key, values);
                    }
                });
                System.out.println(results);
                var data = results.getBody();
                // Gọi qua padman để lưu thông tin danh sách hồ sơ lấy về
            } catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }

            try {
                //bo sung luu logs
                if (config.getId().toHexString().equals(lgspKGGConfigId)) {
                    //luu log

                    IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                    IntegratedService serviceNew = new IntegratedService(this.serviceId, "VBQPPL Service");
                    configNew.setService(serviceNew);
                    //save log
                    integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(),
                                    "getAllLoaiVanBanUrl",
                                    "NGSP-VBQPPL "),
                            0, results == null ? "" : results.getBody(), new Gson().toJson(unitCodes));

                }
            }catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }
        }catch(Exception e){
            logger.error("Error calling http: ", e.getMessage());
        }

        return ResponseEntity
                .status(results.getStatusCode())
                .headers(cleanedHeaders)
                .body(results.getBody());
    }

    public  ResponseEntity<String> getAllCoQuanBienTap(IntegrationParamsDto params){
        ResponseEntity<String> results = null;
        HttpHeaders cleanedHeaders = new HttpHeaders();
        try{
            IntegratedConfigurationDto config;
            if (Objects.nonNull(params.getConfigId())) {
                config = configurationService.getConfig(params.getConfigId());
            } else {
                config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
            }
            if (Objects.isNull(config)) {
                throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                        HttpServletResponse.SC_NOT_FOUND);
            }
            logger.info("getConfig: ");
            System.out.println(config);
            String gatewayToken = config.getParametersValue("gateway-token");// "https://api.quangnam.gov.vn/token";
            String consumerKey = config.getParametersValue("consumer-key"); //"GJbFfb2XnDMCJoTq0OfCD1kO3Zga";
            String consumerSecret = config.getParametersValue("consumer-secret");//"d3S6ZYqUGyqASLKrqKA41WhuiCka";
            String tokenValue = config.getParametersValue("token-value");
            String token = "";
            if (digoCustomHeader == 1) {
                token = getTokenFPT(gatewayToken, tokenValue).getAccessToken();
            } else {
                token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
            }

            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setBearerAuth(token);
                if (digoCustomHeader == 1) {
                    headers.set("lgspaccesstoken", tokenValue);
                }
                headers.setContentType(MediaType.APPLICATION_JSON);
                String t_gridRequest = "{\"sort\":[{\"field\":\"ID\",\"dir\":\"desc\"}]}";
                Map<String, Object> _params = new HashMap<>();
                _params.put("t_gridRequest", t_gridRequest);
                HttpEntity<?> request = new HttpEntity<>(_params,headers);
                String getAllCoQuanBienTapUrl = config.getParametersValue("get-co-quan-bien-tap-url").toString();
                //"https://api.quangnam.gov.vn/NGSP-VBQPPL/GetAllCoQuanBienTap";
                results = restTemplate.exchange(getAllCoQuanBienTapUrl,
                        HttpMethod.POST, request, String.class);
                // Sao chép tất cả header trừ Transfer-Encoding
                results.getHeaders().forEach((key, values) -> {
                    if (!key.equalsIgnoreCase("transfer-encoding")) {
                        cleanedHeaders.put(key, values);
                    }
                });

                var data = results.getBody();
                JSONObject jsonObj;
                jsonObj = new JSONObject(data);
            } catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }

            try {
                //bo sung luu logs
                if (config.getId().toHexString().equals(lgspKGGConfigId)) {
                    //luu log

                    IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                    IntegratedService serviceNew = new IntegratedService(this.serviceId, "VBQPPL Service");
                    configNew.setService(serviceNew);
                    //save log
                    integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(),
                                    "getAllLoaiVanBanUrl",
                                    "NGSP-VBQPPL "),
                            0, results == null ? "" : results.getBody(), new Gson().toJson(""));

                }
            }catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }
        }catch(Exception e){
            logger.error("Error calling http: ", e.getMessage());
        }
        return ResponseEntity
                .status(results.getStatusCode())
                .headers(cleanedHeaders)
                .body(results.getBody());
    }
    public  ResponseEntity<String> getvbplGetListAttach(IntegrationParamsDto params,Map<String, Object> body){
        ResponseEntity<String> results = null;
        HttpHeaders cleanedHeaders = new HttpHeaders();
        try{
            IntegratedConfigurationDto config;
            if (Objects.nonNull(params.getConfigId())) {
                config = configurationService.getConfig(params.getConfigId());
            } else {
                config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
            }
            if (Objects.isNull(config)) {
                throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                        HttpServletResponse.SC_NOT_FOUND);
            }
            logger.info("getConfig: ");
            System.out.println(config);
            String gatewayToken = config.getParametersValue("gateway-token");// "https://api.quangnam.gov.vn/token";
            String consumerKey = config.getParametersValue("consumer-key"); //"GJbFfb2XnDMCJoTq0OfCD1kO3Zga";
            String consumerSecret = config.getParametersValue("consumer-secret");//"d3S6ZYqUGyqASLKrqKA41WhuiCka";
            String tokenValue = config.getParametersValue("token-value");
            String token = "";
            if (digoCustomHeader == 1) {
                token = getTokenFPT(gatewayToken, tokenValue).getAccessToken();
            } else {
                token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
            }

            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setBearerAuth(token);
                headers.setContentType(MediaType.APPLICATION_JSON);
                if (digoCustomHeader == 1) {
                    headers.set("lgspaccesstoken", tokenValue);
                }
                HttpEntity<?> request = new HttpEntity<>(body,headers);
                String vbplGetListAttachUrl = config.getParametersValue("get-list-attach-url").toString();
                //"https://api.quangnam.gov.vn/NGSP-VBQPPL/vbplGetListAttach";
                // Lấy url gọi api lấy danh sách hồ sơ
                results = restTemplate.exchange(vbplGetListAttachUrl,
                        HttpMethod.POST, request, String.class);
                // Sao chép tất cả header trừ Transfer-Encoding
                results.getHeaders().forEach((key, values) -> {
                    if (!key.equalsIgnoreCase("transfer-encoding")) {
                        cleanedHeaders.put(key, values);
                    }
                });
                logger.info("Http results:");
                System.out.println(results);
                //var data = results.getBody();
                // Gọi qua padman để lưu thông tin danh sách hồ sơ lấy về
            } catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }

            try {
                //bo sung luu logs
                if (config.getId().toHexString().equals(lgspKGGConfigId)) {
                    //luu log

                    IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                    IntegratedService serviceNew = new IntegratedService(this.serviceId, "VBQPPL Service");
                    configNew.setService(serviceNew);
                    //save log
                    integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(),
                                    "getAllLoaiVanBanUrl",
                                    "NGSP-VBQPPL "),
                            0, results == null ? "" : results.getBody(), new Gson().toJson(body));

                }
            }catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }
        }catch(Exception e){
            logger.error("Error calling http: ", e.getMessage());
        }
        return ResponseEntity
                .status(results.getStatusCode())
                .headers(cleanedHeaders)
                .body(results.getBody());
    }
    public  ResponseEntity<String> getById(IntegrationParamsDto params,Integer ItemID){
        ResponseEntity<String> results = null;
        HttpHeaders cleanedHeaders = new HttpHeaders();

        try{
            IntegratedConfigurationDto config;
            if (Objects.nonNull(params.getConfigId())) {
                config = configurationService.getConfig(params.getConfigId());
            } else {
                config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
            }
            if (Objects.isNull(config)) {
                throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                        HttpServletResponse.SC_NOT_FOUND);
            }
            logger.info("getConfig: ");
            System.out.println(config);
            String gatewayToken = config.getParametersValue("gateway-token");// "https://api.quangnam.gov.vn/token";
            String consumerKey = config.getParametersValue("consumer-key"); //"GJbFfb2XnDMCJoTq0OfCD1kO3Zga";
            String consumerSecret = config.getParametersValue("consumer-secret");//"d3S6ZYqUGyqASLKrqKA41WhuiCka";
            String tokenValue = config.getParametersValue("token-value");
            String token = "";
            if (digoCustomHeader == 1) {
                token = getTokenFPT(gatewayToken, tokenValue).getAccessToken();
            } else {
                token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
            }

            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setBearerAuth(token);
                headers.setContentType(MediaType.APPLICATION_JSON);
                if (digoCustomHeader == 1) {
                    headers.set("lgspaccesstoken", tokenValue);
                }
                String t_gridRequest = "{\"sort\":[{\"field\":\"ID\",\"dir\":\"desc\"}]}";
                Map<String, Object> _params = new HashMap<>();
                _params.put("ItemID", ItemID);
                _params.put("isTiengAnhOrVN", 1);
                HttpEntity<?> request = new HttpEntity<>(_params,headers);
                String getByIdUrl = config.getParametersValue("get-by-id-url").toString();
                //"https://api.quangnam.gov.vn/NGSP-VBQPPL/GetById";
                // Lấy url gọi api lấy danh sách hồ sơ
                results = restTemplate.exchange(getByIdUrl,
                        HttpMethod.POST, request, String.class);
                // Sao chép tất cả header trừ Transfer-Encoding
                results.getHeaders().forEach((key, values) -> {
                    if (!key.equalsIgnoreCase("transfer-encoding")) {
                        cleanedHeaders.put(key, values);
                    }
                });
                logger.info("Http results:");
                System.out.println(results);
                var data = results.getBody();
                // Gọi qua padman để lưu thông tin danh sách hồ sơ lấy về
            } catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }

            try {
                //bo sung luu logs
                if (config.getId().toHexString().equals(lgspKGGConfigId)) {
                    //luu log

                    IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                    IntegratedService serviceNew = new IntegratedService(this.serviceId, "VBQPPL Service");
                    configNew.setService(serviceNew);
                    //save log
                    integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(),
                                    "getAllLoaiVanBanUrl",
                                    "NGSP-VBQPPL "),
                            0, results == null ? "" : results.getBody(), new Gson().toJson(ItemID));

                }
            }catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }
        }catch(Exception e){
            logger.error("Error calling http: ", e.getMessage());
        }
        return ResponseEntity
                .status(results.getStatusCode())
                .headers(cleanedHeaders)
                .body(results.getBody());
    }
    public  ResponseEntity<String> timKiemVanbanFull(IntegrationParamsDto params,Map<String, Object> body){
        ResponseEntity<String> results = null;
        HttpHeaders cleanedHeaders = new HttpHeaders();
        try{
            IntegratedConfigurationDto config;
            if (Objects.nonNull(params.getConfigId())) {
                config = configurationService.getConfig(params.getConfigId());
            } else {
                config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
            }
            if (Objects.isNull(config)) {
                throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                        HttpServletResponse.SC_NOT_FOUND);
            }
            logger.info("getConfig: ");
            System.out.println(config);
            String gatewayToken = config.getParametersValue("gateway-token");// "https://api.quangnam.gov.vn/token";
            String consumerKey = config.getParametersValue("consumer-key"); //"GJbFfb2XnDMCJoTq0OfCD1kO3Zga";
            String consumerSecret = config.getParametersValue("consumer-secret");//"d3S6ZYqUGyqASLKrqKA41WhuiCka";
            String tokenValue = config.getParametersValue("token-value");
            String token = "";
            if (digoCustomHeader == 1) {
                token = getTokenFPT(gatewayToken, tokenValue).getAccessToken();
            } else {
                token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
            }

            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setBearerAuth(token);
                if (digoCustomHeader == 1) {
                    headers.set("lgspaccesstoken", tokenValue);
                }

                headers.setContentType(MediaType.APPLICATION_JSON);
                String t_gridRequest = "{\"sort\":[{\"field\":\"ID\",\"dir\":\"desc\"}]}";
                HttpEntity<?> request = new HttpEntity<>(body,headers);
                String timKiemVanbanFullUrl = config.getParametersValue("tim-kiem-van-ban-full-url").toString();
                //"https://api.quangnam.gov.vn/NGSP-VBQPPL/TimKiemVanbanFull";
                // Lấy url gọi api lấy danh sách hồ sơ
                results = restTemplate.exchange(timKiemVanbanFullUrl,
                        HttpMethod.POST, request, String.class);
                // Sao chép tất cả header trừ Transfer-Encoding
                results.getHeaders().forEach((key, values) -> {
                    if (!key.equalsIgnoreCase("transfer-encoding")) {
                        cleanedHeaders.put(key, values);
                    }
                });
                logger.info("Http results:");
                System.out.println(results);
                var data = results.getBody();
                // Gọi qua padman để lưu thông tin danh sách hồ sơ lấy về
            } catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }

            try {
                //bo sung luu logs
                if (config.getId().toHexString().equals(lgspKGGConfigId)) {
                    //luu log

                    IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                    IntegratedService serviceNew = new IntegratedService(this.serviceId, "VBQPPL Service");
                    configNew.setService(serviceNew);
                    //save log
                    integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(),
                                    "getAllLoaiVanBanUrl",
                                    "NGSP-VBQPPL "),
                            0, results == null ? "" : results.getBody(), new Gson().toJson(body));

                }
            }catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }
        }catch(Exception e){
            logger.error("Error calling http: ", e.getMessage());
        }
        return ResponseEntity
                .status(results.getStatusCode())
                .headers(cleanedHeaders)
                .body(results.getBody());
    }
}
