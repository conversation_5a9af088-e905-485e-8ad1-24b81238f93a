package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import com.vnpt.vnptkyso.pdf.PdfHashSigner.FontName;
import com.vnpt.vnptkyso.pdf.PdfHashSigner.FontStyle;
import com.vnpt.vnptkyso.pdf.PdfHashSigner.RenderMode;
import com.vnpt.vnptkyso.pdf.PdfSignatureComment;
import com.vnpt.vnptkyso.pdf.PdfSignatureComment.Types;
import com.vnpt.vnptkyso.pdf.PdfSignatureView;
import com.vnpt.vnptkyso.signer.DocumentType;
import com.vnpt.vnptkyso.signer.HashSignerException;
import com.vnpt.vnptkyso.signer.HashSignerFactory;
import com.vnpt.vnptkyso.signer.IHashSginer;
import com.vnpt.vnptkyso.signer.SignatureParameter;
import com.vnpt.vnptkyso.utils.MessageDigestAlgorithm;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.util.TextUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.FileInfoDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.vnptsmartca.SignatureCommentDto;
import vn.vnpt.digo.adapter.dto.vnptsmartca.SignaturePosition;
import vn.vnpt.digo.adapter.dto.vnptsmartca.SmartCaSignatureDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.vnptsmartca.Certificate;
import vn.vnpt.digo.adapter.pojo.vnptsmartca.Credential;
import vn.vnpt.digo.adapter.pojo.vnptsmartca.Token;
import vn.vnpt.digo.adapter.dto.vnptsmartca.UserSignRawDto;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Translator;
import vn.vnpt.digo.adapter.pojo.vnptsmartca.FileHash;
import vn.vnpt.digo.adapter.pojo.vnptsmartca.PayloadTokenSmartCA;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.pojo.vnptsmartca.TranInfo;
import vn.vnpt.digo.adapter.pojo.vnptsmartca.TranInfoContent;
import vn.vnpt.digo.adapter.pojo.vnptsmartca.SignHash;
import vn.vnpt.digo.adapter.repository.IntegratedConfigurationRepository;
import vn.vnpt.digo.adapter.util.Context;
/**
 *
 * <AUTHOR>
 */
@RefreshScope
@Service
public class VnptSmartCAV2Service {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private Microservice microservice;
    
    @Autowired
    private Translator translator;
    
    @Autowired
    private IntegratedConfigurationService configurationService;
    
    @Autowired
    private IntegratedConfigurationRepository icRepository;
    
    @Value("${smartCA.config-id}")
    private String smartCAConfigId;

    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511899001");
    
    private final Logger logger = LoggerFactory.getLogger(VnptSmartCAV2Service.class);
    private final List<String> FONT_STYLE_LIST = Arrays.asList("Normal", "Bold", "Italic", "BoldItalic", "Underline");
    private final List<String> FONT_NAME_LIST = Arrays.asList("Times_New_Roman", "Roboto", "Arial");
    private final List<String> VISIBLE_TYPE_LIST = Arrays.asList("TEXT_ONLY", "TEXT_WITH_LOGO_LEFT", "LOGO_ONLY", "TEXT_WITH_LOGO_TOP", "TEXT_WITH_BACKGROUND");

    public IdDto signByFile(SmartCaSignatureDto document) {
        //Get config
        IntegratedConfigurationDto config;
        if (Objects.nonNull(document.getConfigId())) {
            config = configurationService.getConfig(document.getConfigId());
        } else {
            config = configurationService.getConfig(document.getAgencyId(), document.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String endpointToken = config.getParameterValue("vsc-endpoint-token");
        String endpointCredentialList = config.getParameterValue("vsc-endpoint-credentials-list");
        String endpointCredentialInfo = config.getParameterValue("vsc-endpoint-credentials-info");
        String endpointSignHash = config.getParameterValue("vsc-endpoint-signature-signhash");
        String endpointTranInfo = config.getParameterValue("vsc-endpoint-credentials-gettransactioninfo");
        
        String clientId = config.getParameterValue("vsc-client-id").toString();
        String clientSecret = config.getParameterValue("vsc-client-secret").toString();
        String grantType = config.getParameterValue("vsc-token-grant-type").toString();
        // Hash
        String hashAlgorithm = config.getParameterValue("hash-algorithm");
        String signDateFormat = getSignDateFormat(config.getParameterValue("sign-date-format"));
        // Validate config
        // Format text
        int signFontSize = getFontSize(document.getFontSize(), config.getParameterValue("sign-font-size"));
        String signFontFamily = getFontName(document.getFontName(), config.getParameterValue("sign-font-family"));
        String signFontStyle = getFontStyle(document.getFontStyle(), config.getParameterValue("sign-font-style"));
        String signFontColor = getTextColor(document.getTextColor(), config.getParameterValue("sign-font-color"));
        // Format comment
        int signCommentFontSize = config.getParameterValue("sign-comment-font-size");
        String signCommentFontFamily = config.getParameterValue("sign-comment-font-family");
        String signCommentFontStyle = config.getParameterValue("sign-comment-font-style");
        String signCommentFontColor = config.getParameterValue("sign-comment-font-color");
        // waiting time to confirm
        int waitingTime = config.getParameterValue("waiting-sign-confirm-time");
        // Check signature
        if(document.getPersonId() == null) {
            document.setPersonId(Context.getUserId());
            document.setSignerName(Context.getUserFullname());
        }
        
        String signImage = null;
        byte[] unsignedFile = new byte[0];
        String unsignedFilename = "";
        try{
            String unsignedUrl = microservice.filemanUri("file/" + document.getContentFileId().toHexString()).toUriString();
            ResponseEntity<byte[]> entity = MicroserviceExchange.getFileEntity(restTemplate, unsignedUrl);
            unsignedFile = entity.getBody();
            unsignedFilename = entity.getHeaders().getContentDisposition().getFilename();
        } catch (Exception e){
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.phrase.file-to-sign")}, HttpServletResponse.SC_NOT_FOUND);
        }
        UserSignRawDto userSign = getUserSign(document.getPersonId(), document.getSignatureTypeId());
        if(userSign == null) {
             throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String username = "";
        String password = "";
        for(UserSignRawDto.SignInfo signInfo : userSign.getSignInfo()) {
            if(this.serviceId.toHexString().equals(signInfo.getServiceId().toHexString())) {
                username = signInfo.getClient();
                password = signInfo.getPass();
                break;
            }
        }
        if(TextUtils.isEmpty(password) || TextUtils.isEmpty(username)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        try{
            String signImageUrl = microservice.filemanUri("file/" + userSign.getImage().getId().toHexString()).toUriString();
            ResponseEntity<byte[]> entity = MicroserviceExchange.getFileEntity(restTemplate, signImageUrl);
            byte[] contentFile = entity.getBody();
            signImage = Base64.encodeBase64String(contentFile);
        } catch (Exception e){
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.phrase.signature-image")}, HttpServletResponse.SC_NOT_FOUND);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        String _signedFilename = unsignedFilename.replaceAll(".pdf", "") + "_" + sdf.format(new Date()) + ".pdf";
        //
        document.setFontSize(signFontSize);
        document.setFontName(signFontFamily);
        document.setTextColor(signFontColor);
        document.setFontStyle(signFontStyle);
        document.setFileContent(unsignedFile);
        document.setFileName(unsignedFilename);
        document.setSignImage(signImage);
        document.setHashAlgorithm(hashAlgorithm);
        document.setVisibleType(getVisibleType(document.getVisibleType()));
        //
        if(document.getComments() != null) {
            for(SignatureCommentDto cmtDto : document.getComments()) {
                cmtDto.setFontSize(getFontSize(cmtDto.getFontSize(), signCommentFontSize));
                cmtDto.setTextColor(getTextColor(cmtDto.getTextColor(), signCommentFontColor));
                cmtDto.setFontName(getFontName(cmtDto.getFontName(), signCommentFontFamily));
                cmtDto.setFontStyle(getFontStyle(cmtDto.getFontStyle(), signCommentFontStyle));
                cmtDto.setPage(cmtDto.getPage());
                cmtDto.setPosition(cmtDto.getPosition());
                cmtDto.setText(cmtDto.getText());
            }
        }
        // Sign document
        String token = getToken(endpointToken, grantType, clientId, clientSecret, username, password);
        if(token == null) {
            throw new DigoHttpException(11005, new String[]{translator.toLocale("lang.sentence.check-signature-config")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        String credential = getCredential(endpointCredentialList, token);
        if(token == null) {
            throw new DigoHttpException(11005, new String[]{translator.toLocale("lang.sentence.check-signature-config")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        String certBase64 = getCertBase64(endpointCredentialInfo, token, credential);
        if(token == null) {
            throw new DigoHttpException(11005, new String[]{translator.toLocale("lang.sentence.check-signature-config")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        String fullname = getSignerName(token);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
        try {
            simpleDateFormat.applyPattern(signDateFormat);
        } catch(Exception e) {
            simpleDateFormat.applyPattern("dd/MM/yyyy");
        }
        String signatureText = "Ký bởi: " + fullname + "\nNgày ký: " + simpleDateFormat.format(new Date());
        document.setSignerName(signatureText);
        byte[] signedData = signHashPdf(endpointSignHash, endpointTranInfo, token, certBase64, credential, waitingTime, document);
        if(signedData == null || signedData.length == 0) {
            throw new DigoHttpException(11005, new String[]{translator.toLocale("lang.sentence.check-signature-config")}, HttpServletResponse.SC_BAD_REQUEST);
        }
        //Upload signed file to fileman
        final String signedFilename = _signedFilename;
        ByteArrayResource contentsAsResource = new ByteArrayResource(signedData) {
            @Override
            public String getFilename() {
                return signedFilename;
            }
        };
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add("file", contentsAsResource);
        String postFileUrl = microservice.filemanUri("file").toUriString();
        IdDto res = MicroserviceExchange.postMultipart(restTemplate, postFileUrl, map, IdDto.class);
        return res;
    }
    
    private byte[] signHashPdf(String endpointSignHash, String endpointTranInfo, String token, String certBase64, String credentialId, int waitingTime, SmartCaSignatureDto signDocument) {
        String hashValue = "";
        // [Bước 3] Khởi tạo HashSigner
        IHashSginer signer = getHashSignerPdf(signDocument, certBase64);
        if(signer == null) {
            return null;
        }
        List<FileHash> fileHashs = new ArrayList<>();

        try {
            hashValue = signer.getSecondHashAsBase64();
        } catch (HashSignerException ex) {
            logger.error(ex.getMessage());
        }
        if(!hashValue.isEmpty()) {
            fileHashs.add(new FileHash("pdf", signDocument.getFileName(), hashValue));
        }
        String tranId = signHash(endpointSignHash, token, credentialId, fileHashs);

        int count = 0;
        int limit = (waitingTime < 10)? 1 : waitingTime/10; 
        boolean isConfirm = false;
        String dataSigned = "";
        TranInfoContent tranInfoContent = null;
        while (count < limit && !isConfirm) {
            tranInfoContent = getTranInfo(endpointTranInfo, token, tranId);
            if (tranInfoContent.getTranStatus() != 1) {
                try {
                    count = count + 1;
                    Thread.sleep(10000);
                } catch (InterruptedException ex) {
                    logger.error(ex.getMessage());
                }
            } else {
                isConfirm = true;
                dataSigned = tranInfoContent.getDocuments().get(0).getSig();
            }
        }
        byte[] signedData = null;
        try {
            signedData = signer.sign(dataSigned);
        } catch (HashSignerException ex) {
            logger.error(ex.getMessage());
        }
        return signedData;

    }
    
    private IHashSginer getHashSignerPdf(SmartCaSignatureDto signDocument, String certBase64) {
        SignatureParameter param = new SignatureParameter(signDocument.getFileContent(), certBase64);
        IHashSginer signer = null;
        Base64 codec = new Base64();
        try {
            signer = HashSignerFactory.genenrateSigner(DocumentType.PDF, param);

            com.vnpt.vnptkyso.pdf.PdfHashSigner pdfSigner = (com.vnpt.vnptkyso.pdf.PdfHashSigner) signer;

            //Set Hash
            MessageDigestAlgorithm alg;
            switch(signDocument.getHashAlgorithm()) {
                case "SHA1":
                    alg  = MessageDigestAlgorithm.SHA1;
                    break;
                case "SHA256":
                    alg  = MessageDigestAlgorithm.SHA256;
                    break;
                case "SHA384":
                    alg  = MessageDigestAlgorithm.SHA384;
                    break;
                case "SHA512":
                    alg  = MessageDigestAlgorithm.SHA512;
                    break;
                default:
                    alg  = MessageDigestAlgorithm.SHA256;
                    break;
            }
            pdfSigner.setHashAlgorithm(alg);
            // [REQUIRED] Vị trí đặt chữ ký
            for(SignaturePosition signaturPosition : signDocument.getSignaturePositions()) {
                pdfSigner.addSignatureViews(new PdfSignatureView(signaturPosition.getPage(), signaturPosition.getPosition()));
            }
            // [OPTIONAL] Hình ảnh chữ ký (default=VNPT logo)
            pdfSigner.setImage(codec.decode(signDocument.getSignImage()));
            pdfSigner.setReason(signDocument.getReason());
            // [OPTIONAL] Kiểu hiển thị (default=TEXT_WITH_BACKGROUND)
            
            RenderMode mode = RenderMode.valueOf(signDocument.getVisibleType());
            pdfSigner.setRenderMode(mode);
            // [OPTIONAL] Màu chữ (default=000000)
            pdfSigner.setFontColor(signDocument.getTextColor());
            // [OPTIONAL] Font chữ (default=Times_New_Roman)
            pdfSigner.setFontName(FontName.valueOf(signDocument.getFontName()));
            // [OPTIONAL] Cỡ chữ (default=13)
            pdfSigner.setFontSize(signDocument.getFontSize());
            // [OPTIONAL] Kiển chữ (default=Normal)
            pdfSigner.setFontStyle(FontStyle.valueOf(signDocument.getFontStyle()));
            // [OPTIONAL] Nội dung hiển thị
            pdfSigner.setSignatureText(signDocument.getSignerName());
            // [OPTIONAL] Thông tin TSA server
            // pdfSigner.setTsaClient("http://timestamp.entrust.net/TSS/RFC3161sha2TS",
            // null, null);
            // [OPTIONAL] Thêm đầy đủ certchain vào file pdf (yêu cầu cho phép kết nối đến
            // CA)
            pdfSigner.useCertChain();
            // [OPTIONAL] Thêm text comment
            renderComment(pdfSigner, signDocument.getComments());
            
        } catch (HashSignerException e) {
            logger.error(e.getMessage());
        }
        return signer;
    }
    
    private void renderComment(com.vnpt.vnptkyso.pdf.PdfHashSigner pdfSigner, List<SignatureCommentDto> comments) {
        if(comments == null || comments.isEmpty()) {
            return;
        }
        for (SignatureCommentDto comment : comments) {
            String rect = comment.getPosition();
            int cmtPage = comment.getPage();
            String text = comment.getText();
            if (text == null || text.isEmpty() || rect == null || rect.isEmpty()) {
                continue;
            }
            PdfSignatureComment com = new PdfSignatureComment(Types.TEXT, rect, text,
                    cmtPage, FontName.valueOf(comment.getFontName()), FontStyle.valueOf(comment.getFontStyle()),
                    comment.getFontSize(), comment.getTextColor(), "");
            pdfSigner.addSignatureComment(com);
        }
    }

    private String getToken(String endpointToken, String grantType, String clientId, String clientSecret, String username, String password) {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("grant_type", grantType);
        body.add("client_id", clientId);
        body.add("client_secret", clientSecret);
        body.add("username", username);
        body.add("password", password);
        Token token = MicroserviceExchange.postFormUrlEncoded(restTemplate, endpointToken, body, Token.class);
        if (Objects.isNull(token.getAccess_token())) {
            logger.error("Token is empty");
            return null;
        }
        return token.getAccess_token();
    }

    private String getCredential(String endpointCredentialList, String token) {
        HashMap<String, Object> body = new HashMap<>();
        Credential credential = MicroserviceExchange.postJsonBearAuth(restTemplate, endpointCredentialList, token, body, Credential.class);
        if (credential == null || credential.getContent() == null || credential.getContent().length == 0) {
            logger.error("Credential is empty");
            return null;
        }
        return credential.getContent()[0];
    }
    
    private String getCertBase64(String endpointCredentialInfo, String token, String credentialId) {
        HashMap<String, Object> body = new HashMap<>();
        body.put("credentialId", credentialId);
        body.put("certificates", "chain");
        body.put("certInfo", true);
        body.put("authInfo", true);
        Certificate certificate = MicroserviceExchange.postJsonBearAuth(restTemplate, endpointCredentialInfo, token, body, Certificate.class);
        if (certificate == null || certificate.getCert() == null
                || certificate.getCert().getCertificates() == null || certificate.getCert().getCertificates().length == 0) {
            logger.error("Certificate is empty");
            return null;
        }
        String cert = certificate.getCert().getCertificates()[0];
        if(cert == null || cert.isEmpty()) {
            logger.error("Certificate is empty");
            return null;
        }
        return cert.replace("-----BEGIN CERTIFICATE-----", "")
                .replace("\r\n", "").replace("-----END CERTIFICATE-----", "");
    }
    
    private String signHash(String endpointSignHash, String token, String credentialId, List<FileHash> fileHashs) {
        HashMap<String, Object> body = new HashMap<>();
        body.put("credentialId", credentialId);
        body.put("datas", fileHashs);
        SignHash signHash = MicroserviceExchange.postJsonBearAuth(restTemplate, endpointSignHash, token, body, SignHash.class);
        if (signHash == null || signHash.getContent() == null) {
            logger.error("SignHash is empty");
            return null;
        }
        return signHash.getContent().getTranId();
    }
    
    // signHash with refTranId
    private String signHash(String endpointSignHash, String token, String credentialId, List<FileHash> fileHashs, String refTranId) {
        HashMap<String, Object> body = new HashMap<>();
        body.put("credentialId", credentialId);
        body.put("datas", fileHashs);
        body.put("refTranId", null);
        SignHash signHash = MicroserviceExchange.postJsonBearAuth(restTemplate, endpointSignHash, token, body, SignHash.class);
        if (signHash == null || signHash.getContent() == null) {
            logger.error("SignHash is empty");
            return null;
        }
        return signHash.getContent().getTranId();
    }

    private TranInfoContent getTranInfo(String endpointTranInfo, String token, String tranId) {
        HashMap<String, Object> body = new HashMap<>();
        body.put("tranId", tranId);
        TranInfo tranInfo = MicroserviceExchange.postJsonBearAuth(restTemplate, endpointTranInfo, token, body, TranInfo.class);
        if (tranInfo == null) {
            logger.error("TranInfo is empty");
            return null;
        }
        return tranInfo.getContent();
    }
 
    private int getFontSize(int param, int confVal) {
        if(param <= 0 && confVal <= 0) {
            return 13;
        }
        int value = param;
        if(value <= 0) {
            value = confVal;
        }
        return value;
    }

    private String getTextColor(String param, String confVal) {
        String def = "000000";
        if(isEmpty(param) && isEmpty(confVal)) {
            return def;
        }
        String value = param;
        if(isEmpty(value)) {
            value = confVal;
        }
        return value;
    }

    private String getFontName(String param, String confVal) {
        String def = "Times_New_Roman";
        if(isEmpty(param) && isEmpty(confVal)) {
            return def;
        }
        String value = param;
        if(isEmpty(value)) {
            value = confVal;
        }
        return FONT_NAME_LIST.contains(value)? value : def;
    }
    
    private String getFontStyle(String param, String confVal) {
        String def = "Normal";
        if(isEmpty(param) && isEmpty(confVal)) {
            return def;
        }
        String value = param;
        if(isEmpty(value)) {
            value = confVal;
        }
        return FONT_STYLE_LIST.contains(value)? value : def;
    }
    
    private String getVisibleType(String param) {
        String def = "LOGO_ONLY";
        if(isEmpty(param)) {
            return def;
        }
        return VISIBLE_TYPE_LIST.contains(param)? param : def;
    }
    
    private String getSignDateFormat(String param) {
        String def = "dd/MM/yyyy";
        if(isEmpty(param)) {
            return def;
        }
        return param;
    }

    private static boolean isEmpty(String txt) {
        return txt == null || txt.trim().isEmpty();
    } 

    public ResponseEntity<byte[]> getFileContent(ObjectId id){
        try{
            String unsignedUrl = microservice.filemanUri("file/" + id.toString()).toUriString();
            ResponseEntity<byte[]> entity = MicroserviceExchange.getFileEntity(restTemplate, unsignedUrl);
            return entity;
        } catch (Exception e){
            return null;
        }
    }
    
    public String getFileInfo(ObjectId fileId){
        try{
            String url = microservice.filemanUri("file/" + fileId.toString() + "/filename+size").toUriString();
            FileInfoDto response = MicroserviceExchange.get(restTemplate, url, FileInfoDto.class);
            return response.getFilename();
        } catch (HttpClientErrorException | NullPointerException e){
            return null;
        }
    }

    public UserSignRawDto getUserSign(ObjectId id, ObjectId signTypeId){
        try{
            String url = microservice.humanUri("user/" + id.toHexString() + "/signraw").toUriString();
            List<UserSignRawDto> list = Arrays.asList(MicroserviceExchange.getList(restTemplate, url, UserSignRawDto[].class));
            for(UserSignRawDto dto : list) {
                if(signTypeId.toHexString().equals(dto.getId().toHexString())) {
                    return dto;
                }
            }
        } catch (Exception e){
            return null;
        }
        return null;
    }
    
    private String getSignerName(String token) {
        String[] chunk = token.split("\\.");
        Base64 base64 = new Base64(true);
        String payload = new String(base64.decode(chunk[1]));
        Gson gson = new Gson();
        try {
            PayloadTokenSmartCA data = gson.fromJson(payload, PayloadTokenSmartCA.class);
            return data.getFullName();
        } catch(Exception e) {
            return "";
        }
    }
}
