/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.CheckCitizenLog;
import vn.vnpt.digo.adapter.dto.check_citizen_log.*;
import vn.vnpt.digo.adapter.dto.qti_check_citizen_log.*;
import vn.vnpt.digo.adapter.dto.qti_check_citizen_log.GetCheckCitizenLogCountDto;
import vn.vnpt.digo.adapter.dto.qti_check_citizen_log.GetCheckCitizenLogDto;
import vn.vnpt.digo.adapter.dto.qti_check_citizen_log.GroupCitizenLogDto;
import vn.vnpt.digo.adapter.dto.qti_check_citizen_log.PostCheckCitizenLogDto;
import vn.vnpt.digo.adapter.pojo.IdResponse;
import vn.vnpt.digo.adapter.repository.IntegrationServiceRepository;
import vn.vnpt.digo.adapter.stream.DataLoggerStream;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.Translator;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

/**
 *
 * <AUTHOR>
 */
@Service
public class QTICheckCitizenLogService {

    @Autowired
    private Translator translator;

    @Autowired
    private DataLoggerStream dataLoggerStream;

    @Autowired
    private IntegrationServiceRepository integrationServiceRepository;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    Microservice microservice;
    Logger logger = LoggerFactory.getLogger(QTICheckCitizenLogService.class);

    @Value(value = "${digo.agency.qti.ubnd_tinh}")
    public String UBND_TINH_QUANG_TRI;

    @Value(value = "${digo.agency.qti.cap_so}")
    public String CAP_SO;

    @Value(value = "${digo.agency.qti.cap_huyen}")
    public String CAP_HUYEN;

    @Value(value = "${digo.agency.qti.cap_xa}")
    public String CAP_XA;

    @Value(value = "${digo.agency.qti.cap_phong_ban_thuoc_huyen}")
    private String CAP_PHONG_HUYEN;


    public IdResponse addNew(PostCheckCitizenLogDto obj) {
        ObjectId deploymentId = Context.getDeploymentId();
        if(deploymentId == null){
            logger.info("DIGO-Request: " + "deployment-id is null");
            return new IdResponse(null);
        }

        CheckCitizenLog log = new CheckCitizenLog();
        log.setDeploymentId(deploymentId);
        // info
        log.setIdentityNumber(obj.getIdentityNumber());
        log.setName(obj.getName());
        log.setBirtDay(obj.getBirtDay());
        log.setAgency(obj.getAgency());
        log.setProcedure(obj.getProcedure());
        log.setReport(obj.getReport());
        log.setFunction(obj.getFunction());
        log.setUrl(obj.getUrl());
        // log
        log.setStatus(obj.getStatus());
        log.setCreateDate(new Date());
        //add extent filter
        var date = new Date();
        log.setDay(date.getDay());
        log.setMonth(date.getMonth());
        log.setYear(date.getYear());
        //user search
        log.setAccountId(Context.getAccountId());
        log.setUsername(Context.getUserFullname());
        //bổ sung ktm: log 033, 034
        log.setIpAddress(Objects.nonNull(obj.getIpAddress()) ? obj.getIpAddress() : "");
        log.setAuthIdentityNumber(Objects.nonNull(obj.getAuthIdentityNumber()) ? obj.getAuthIdentityNumber() : "");
        log.setMemberIdentityNumbers(Objects.nonNull(obj.getMemberIdentityNumbers()) ? obj.getMemberIdentityNumbers() : "");
        log.setMenuCode(Objects.nonNull(obj.getMenuCode()) ? obj.getMenuCode() : "");

        mongoTemplate.insert(log);

        logger.info("DIGO-Request: " + "CheckCitizenLog added for name: " + log.getName());
        return new IdResponse(log.getId());
    }

    public Page<GetCheckCitizenLogDto> getCheckCitizenLog(String startDate, String endDate, List<ObjectId> agencyIds, Boolean getAll, Pageable pageable) {
        if (agencyIds.isEmpty() && !getAll) {
            return new PageImpl<GetCheckCitizenLogDto>(Collections.emptyList());
        }
        Query query = this.getQuery(startDate, endDate, agencyIds, pageable, false);
        List<GetCheckCitizenLogDto> list = this.getCitizenLog(startDate, endDate, agencyIds, pageable, false);
        return PageableExecutionUtils.getPage(list, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1),
                        vn.vnpt.digo.adapter.dto.check_citizen_log.GetCheckCitizenLogDto.class));
    }

    /*public Page<GetCheckCitizenLogDto> getCheckCitizenLogQTI(String startDate, String endDate, List<ObjectId> agencyIds, Boolean getAll, Pageable pageable, int loaicoquan, List<ObjectId> idcanbo, List<ObjectId> iddonvi) throws ParseException, JsonProcessingException {
        // Kiểm tra điều kiện agencyIds và getAll
        if (agencyIds.isEmpty() && !getAll) {
            return new PageImpl<>(Collections.emptyList());
        }

        // Tạo query dựa trên các tham số đầu vào
        Query query = this.getQueryQTI(startDate, endDate, agencyIds, pageable, false, idcanbo);

        // Lấy danh sách dữ liệu từ MongoDB
        List<GetCheckCitizenLogDto> list = this.getCitizenLogQTI(startDate, endDate, agencyIds, pageable, false, loaicoquan, idcanbo,iddonvi);

        // Xử lý MenuCode cho các phần tử trong danh sách
        for (GetCheckCitizenLogDto item : list) {
            // Kiểm tra MenuCode khác null và không rỗng
            if (item.getMenuCode() != null && !item.getMenuCode().isEmpty()) {
                switch (item.getMenuCode()) {
                    case "033":
                        item.setMenuCode("033 - Xác nhận số định danh cá nhân và chứng minh nhân dân");
                        break;
                    case "034":
                        item.setMenuCode("034 - Xác thực thông tin hộ gia đình");
                        break;
                    case "037":
                        item.setMenuCode("037 - Tra cứu thông tin công dân");
                        break;
                    default:
                        // Không thay đổi MenuCode nếu không khớp
                        break;
                }
            }
        }

        // Trả về kết quả dưới dạng Page, với tính toán số lượng bản ghi phù hợp
        return PageableExecutionUtils.getPage(list, pageable,
                () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), GetCheckCitizenLogDto.class));
    }*/



    public List<GetCheckCitizenLogDto> getCheckCitizenLogToExcel(String startDate, String endDate, List<ObjectId> agencyIds, Boolean getAll, Pageable pageable) {
        if (agencyIds.isEmpty() && !getAll) {
            return null;
        }
        List<GetCheckCitizenLogDto> CheckCitizenLogFullList = this.getCitizenLog(startDate, endDate, agencyIds, pageable, true);
        return CheckCitizenLogFullList;
    }

    public List<GetCheckCitizenLogCountDto> getCheckCitizenLogCount(String startDate, String endDate, List<ObjectId> agencyIds, Boolean getAll, Pageable pageable) {
        if (agencyIds.isEmpty() && !getAll) {
            return null;
        }
        ObjectId deploymentId = Context.getDeploymentId();
        List<GetCheckCitizenLogCountDto> summaries = null;
        TimeZone timeZone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormat.setTimeZone(timeZone);
        try {
            Date startDateFormat = dateFormat.parse(startDate);
            Date endDateFormat = dateFormat.parse(endDate);
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(Criteria.where("deploymentId").is(deploymentId)),
//                    Aggregation.match(Criteria.where("deploymentId").is(new ObjectId("64251063a9abd9426d1f9a5e"))),
                    Aggregation.match(Criteria.where("createDate").gte(startDateFormat).lte(endDateFormat)),
                    Aggregation.group("username","agency").count().as("count"));
            AggregationResults<GetCheckCitizenLogCountDto> results = mongoTemplate.aggregate(aggregation, "checkCitizenLog", GetCheckCitizenLogCountDto.class);
            summaries = results.getMappedResults();
        } catch (ParseException parse) {
            logger.info(null, parse);
        }
        return summaries;
    }

    public List<Statistic0120206cQtiDto> Get_DS_Agent(String agent_level,String parent) throws ParseException, JSONException, JsonProcessingException {

        List<Statistic0120206cQtiDto> listAgencyShowId = new ArrayList<>();
        String token = Context.getJwtAuthenticationTokenValue();
        if (token == null) {
            token = Context.getJwtAuthenticationTokenValue();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.setBearerAuth(token);
        net.minidev.json.JSONObject json = new net.minidev.json.JSONObject();
        HttpEntity<String> request = new HttpEntity<>(json.toJSONString(), headers);
        // Get agencys
        var path="qti-agency/--by-parent-agency?sch_me="+UBND_TINH_QUANG_TRI;
        if(agent_level != null &&  (!agent_level.equals("")))
        {
            path= path+ "&agency-level-id="+agent_level;
        }
        if(parent != null &&  (!parent.equals("")))
        {
            path= path+ "&arr-parent-id="+parent;
        }

        //String getAgencyUrl = "http://localhost:8889/" +path;

        String getAgencyUrl = microservice.basedataUri(path).toUriString();
        ResponseEntity<String> response = restTemplate.exchange(getAgencyUrl, HttpMethod.GET, request, String.class);
        String agencyJson = response.getBody();
        JSONArray agencyObj = new JSONArray(agencyJson);
        for (int i = 0; i < agencyObj.length(); i++) {
            ObjectId agencyIdOj = new ObjectId(agencyObj.getJSONObject(i).get("id").toString());
            Statistic0120206cQtiDto kt = new Statistic0120206cQtiDto();
            kt.set_objid(agencyIdOj);
            kt.set_id(agencyObj.getJSONObject(i).get("id").toString());
            kt.set_id_cha(agencyObj.getJSONObject(i).get("id_cha").toString());
            kt.setName(agencyObj.getJSONObject(i).get("name").toString());

            listAgencyShowId.add(kt);
        }
        return listAgencyShowId;
    }

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public GetCheckCitizenLogCountByAgencyQTI setName3(String Ten, int Cap, String STT)
    {
        GetCheckCitizenLogCountByAgencyQTI.AgencyTrans  AgencyTrans  = new GetCheckCitizenLogCountByAgencyQTI.AgencyTrans();
        AgencyTrans.setId( new ObjectId("655ffb2f93abd219c70b5aaa"));
        AgencyTrans.setNameDto(Ten);
        GetCheckCitizenLogCountByAgencyQTI item = new GetCheckCitizenLogCountByAgencyQTI();
        item.setAgency(AgencyTrans);
        item.setCAP(Cap);
        item.setSTT(STT);
        return  item;
    }

    public  String  JoinStringId (List<Statistic0120206cQtiDto> ds)
    {
        StringBuilder strBuilder = new StringBuilder();
        for (Statistic0120206cQtiDto ob : ds) {
            if (strBuilder.length() > 0) {
                strBuilder.append(",");
            }
            strBuilder.append(ob.get_id());
        }
        String str_huyen = strBuilder.toString();
        return  str_huyen;
    }

    public List<GetCheckCitizenLogCountByAgencyQTI> getCheckCitizenLogCountByAgenCy(String startDate, String endDate) throws ParseException, JsonProcessingException {
        // Khởi tạo tiêu chí tìm kiếm
        Criteria criteria = new Criteria();

        // Thiết lập tiêu chí ngày tháng
        TimeZone timeZone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormat.setTimeZone(timeZone);

        if ((startDate != null && !startDate.isEmpty()) && (endDate != null && !endDate.isEmpty())) {
            try {
                Date startDateFormat = dateFormat.parse(startDate);
                Date endDateFormat = dateFormat.parse(endDate);
                criteria.and("createDate").gte(startDateFormat).lte(endDateFormat);
            } catch (ParseException parse) {
                logger.error("Failed to parse date range", parse); // Ghi lỗi nếu không phân tích được
                return new ArrayList<>(); // Trả về danh sách rỗng nếu có lỗi
            }
        } else if (startDate != null && !startDate.isEmpty()) {
            try {
                Date startDateFormat = dateFormat.parse(startDate);
                criteria.and("createDate").gte(startDateFormat);
            } catch (ParseException parse) {
                logger.error("Failed to parse start date", parse);
                return new ArrayList<>();
            }
        } else if (endDate != null && !endDate.isEmpty()) {
            try {
                Date endDateFormat = dateFormat.parse(endDate);
                criteria.and("createDate").lte(endDateFormat);
            } catch (ParseException parse) {
                logger.error("Failed to parse end date", parse);
                return new ArrayList<>();
            }
        }


        Aggregation aggregation = newAggregation(
                match(criteria), // Điều kiện thời gian
                //match(Criteria.where("agency._id").in(agencyIds)),
                project()
                        .and(ConditionalOperators.when(Criteria.where("").andOperator(
                                Criteria.where("menuCode").in("033"))).then(1)
                                .otherwise(0))
                        .as("total033")
                        .and(ConditionalOperators.when(Criteria.where("").andOperator(
                                Criteria.where("menuCode").in("034"))).then(1)
                                .otherwise(0))
                        .as("total034")
                        .and(ConditionalOperators.when(Criteria.where("").andOperator(
                                Criteria.where("menuCode").in("037"))).then(1)
                                .otherwise(0))
                        .as("total037")
                        .and("agency._id").as("agencyId"),

                group("agencyId")
                        .first("agencyId").as("agency_id")
                        .sum("total033").as("count033")
                        .sum("total034").as("count034")
                        .sum("total037").as("count037")
                        .count().as("sumcount") // Tính tổng số bản ghi
        );
        List<GetCheckCitizenLogCountByAgencyQTI> results = mongoTemplate.aggregate(aggregation, "checkCitizenLog", GetCheckCitizenLogCountByAgencyQTI.class).getMappedResults();

        return results;
    }

    public List<GetCheckCitizenLogCountByAgencyQTI> getDanhSachCheckCitizenLogQTI(String startDate, String endDate, int capDonVi) throws ParseException, JsonProcessingException {
        // Khởi tạo tiêu chí tìm kiếm
        Criteria criteria = new Criteria();

        // Thiết lập tiêu chí ngày tháng
        TimeZone timeZone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormat.setTimeZone(timeZone);

        if ((startDate != null && !startDate.isEmpty()) && (endDate != null && !endDate.isEmpty())) {
            try {
                Date startDateFormat = dateFormat.parse(startDate);
                Date endDateFormat = dateFormat.parse(endDate);
                criteria.and("createDate").gte(startDateFormat).lte(endDateFormat);
            } catch (ParseException parse) {
                logger.error("Failed to parse date range", parse); // Ghi lỗi nếu không phân tích được
                return new ArrayList<>(); // Trả về danh sách rỗng nếu có lỗi
            }
        } else if (startDate != null && !startDate.isEmpty()) {
            try {
                Date startDateFormat = dateFormat.parse(startDate);
                criteria.and("createDate").gte(startDateFormat);
            } catch (ParseException parse) {
                logger.error("Failed to parse start date", parse);
                return new ArrayList<>();
            }
        } else if (endDate != null && !endDate.isEmpty()) {
            try {
                Date endDateFormat = dateFormat.parse(endDate);
                criteria.and("createDate").lte(endDateFormat);
            } catch (ParseException parse) {
                logger.error("Failed to parse end date", parse);
                return new ArrayList<>();
            }
        }
        // lấy gọi 3 lần API để lấy danh sách sở, danh sac huyện và danh sách xã


        // Khởi tạo danh sách các cấp
        List<GetCheckCitizenLogCountByAgencyQTI> rs = new ArrayList<>();
        List<Statistic0120206cQtiDto> rs_new = new ArrayList<>();

        // Gọi API lấy danh sách sở
        List<Statistic0120206cQtiDto>  ds_so=    Get_DS_Agent(CAP_SO,UBND_TINH_QUANG_TRI);

        // Gọi API lấy danh sách huyện
        List<Statistic0120206cQtiDto>  ds_huyen=  Get_DS_Agent(CAP_HUYEN,UBND_TINH_QUANG_TRI);
        var arrSubAgencyId_huyen= JoinStringId(ds_huyen);

        var ds_xa = Get_DS_Agent(CAP_XA,arrSubAgencyId_huyen);

        // Gọi API lấy các đơn vị còn lại
        List<Statistic0120206cQtiDto>  ds_all= Get_DS_Agent(null,null);
        rs_new.addAll(ds_so);
        rs_new.addAll(ds_huyen);

        var dem = 0;

        if(capDonVi == 1){
            // Thêm các đơn vị cấp Sở
            for (Statistic0120206cQtiDto ob_unit : ds_so) {
                dem++;
                GetCheckCitizenLogCountByAgencyQTI.AgencyTrans agencyTrans = new GetCheckCitizenLogCountByAgencyQTI.AgencyTrans();
                agencyTrans.setId(ob_unit.get_objid());
                agencyTrans.setNameDto(ob_unit.getName());
                GetCheckCitizenLogCountByAgencyQTI item = new GetCheckCitizenLogCountByAgencyQTI();
                item.setAgency(agencyTrans);
                item.setCAP(1); // Cấp Sở
                item.setSTT(String.valueOf(dem));
                rs.add(item);
            }
        }else if(capDonVi == 2){
            // Thêm các đơn vị cấp Huyện
            dem = 0;
            for (Statistic0120206cQtiDto ob_unit : ds_huyen) {
                dem++;
                GetCheckCitizenLogCountByAgencyQTI.AgencyTrans agencyTrans = new GetCheckCitizenLogCountByAgencyQTI.AgencyTrans();
                agencyTrans.setId(ob_unit.get_objid());
                agencyTrans.setNameDto(ob_unit.getName());
                GetCheckCitizenLogCountByAgencyQTI item = new GetCheckCitizenLogCountByAgencyQTI();
                item.setAgency(agencyTrans);
                item.setCAP(2); // Cấp Huyện
                item.setSTT(String.valueOf(dem));
                rs.add(item);
            }
        } else if(capDonVi == 3){
            // Thêm các đơn vị cấp Xã
            for (Statistic0120206cQtiDto ob_unit : ds_huyen) {
                GetCheckCitizenLogCountByAgencyQTI.AgencyTrans agencyTrans = new GetCheckCitizenLogCountByAgencyQTI.AgencyTrans();
                agencyTrans.setId(ob_unit.get_objid());
                agencyTrans.setNameDto(ob_unit.getName().toUpperCase());
                GetCheckCitizenLogCountByAgencyQTI item = new GetCheckCitizenLogCountByAgencyQTI();
                item.setAgency(agencyTrans);
                item.setCAP(-4); // Cấp Xã
                rs.add(item);

                var dsxa = ds_xa.stream()
                        .filter(Statistic0120206cQtiDto -> Statistic0120206cQtiDto.get_id_cha().equals(ob_unit.get_id()))
                        .collect(Collectors.toList());
                rs_new.addAll(dsxa);
                dem = 0;
                for (Statistic0120206cQtiDto ob_unit_xa : dsxa) {
                    dem++;
                    GetCheckCitizenLogCountByAgencyQTI.AgencyTrans agencyTransXa = new GetCheckCitizenLogCountByAgencyQTI.AgencyTrans();
                    agencyTransXa.setId(ob_unit_xa.get_objid());
                    agencyTransXa.setNameDto(ob_unit_xa.getName());
                    GetCheckCitizenLogCountByAgencyQTI itemXa = new GetCheckCitizenLogCountByAgencyQTI();
                    itemXa.setAgency(agencyTransXa);
                    itemXa.setCAP(3); // Cấp Xã
                    itemXa.setSTT(String.valueOf(dem));
                    rs.add(itemXa);
                }
            }
        } else {
            // Thêm các đơn vị cấp Sở
            rs.add(setName3("CẤP SỞ", -1, "I"));
            for (Statistic0120206cQtiDto ob_unit : ds_so) {
                dem++;
                GetCheckCitizenLogCountByAgencyQTI.AgencyTrans agencyTrans = new GetCheckCitizenLogCountByAgencyQTI.AgencyTrans();
                agencyTrans.setId(ob_unit.get_objid());
                agencyTrans.setNameDto(ob_unit.getName());
                GetCheckCitizenLogCountByAgencyQTI item = new GetCheckCitizenLogCountByAgencyQTI();
                item.setAgency(agencyTrans);
                item.setCAP(1); // Cấp Sở
                item.setSTT(String.valueOf(dem));
                rs.add(item);
            }

            // Thêm các đơn vị cấp Huyện
            rs.add(setName3("CẤP QUẬN HUYỆN", -2, "II"));
            dem = 0;
            for (Statistic0120206cQtiDto ob_unit : ds_huyen) {
                dem++;
                GetCheckCitizenLogCountByAgencyQTI.AgencyTrans agencyTrans = new GetCheckCitizenLogCountByAgencyQTI.AgencyTrans();
                agencyTrans.setId(ob_unit.get_objid());
                agencyTrans.setNameDto(ob_unit.getName());
                GetCheckCitizenLogCountByAgencyQTI item = new GetCheckCitizenLogCountByAgencyQTI();
                item.setAgency(agencyTrans);
                item.setCAP(2); // Cấp Huyện
                item.setSTT(String.valueOf(dem));
                rs.add(item);
            }

            // Thêm các đơn vị cấp Xã
            rs.add(setName3("CẤP XÃ", -3, "III"));
            for (Statistic0120206cQtiDto ob_unit : ds_huyen) {
                GetCheckCitizenLogCountByAgencyQTI.AgencyTrans agencyTrans = new GetCheckCitizenLogCountByAgencyQTI.AgencyTrans();
                agencyTrans.setId(ob_unit.get_objid());
                agencyTrans.setNameDto(ob_unit.getName().toUpperCase());
                GetCheckCitizenLogCountByAgencyQTI item = new GetCheckCitizenLogCountByAgencyQTI();
                item.setAgency(agencyTrans);
                item.setCAP(-4); // Cấp Xã
                rs.add(item);

                var dsxa = ds_xa.stream()
                        .filter(Statistic0120206cQtiDto -> Statistic0120206cQtiDto.get_id_cha().equals(ob_unit.get_id()))
                        .collect(Collectors.toList());
                rs_new.addAll(dsxa);
                dem = 0;
                for (Statistic0120206cQtiDto ob_unit_xa : dsxa) {
                    dem++;
                    GetCheckCitizenLogCountByAgencyQTI.AgencyTrans agencyTransXa = new GetCheckCitizenLogCountByAgencyQTI.AgencyTrans();
                    agencyTransXa.setId(ob_unit_xa.get_objid());
                    agencyTransXa.setNameDto(ob_unit_xa.getName());
                    GetCheckCitizenLogCountByAgencyQTI itemXa = new GetCheckCitizenLogCountByAgencyQTI();
                    itemXa.setAgency(agencyTransXa);
                    itemXa.setCAP(3); // Cấp Xã
                    itemXa.setSTT(String.valueOf(dem));
                    rs.add(itemXa);
                }
            }
        }

        //lấy dữ liệu 1 lần
        List<GetCheckCitizenLogCountByAgencyQTI> data = getCheckCitizenLogCountByAgenCy(startDate, endDate);

        // xử lý dữ liệu
        for(GetCheckCitizenLogCountByAgencyQTI rs_item : rs){
            rs_item.setCount033(0);
            rs_item.setCount034(0);
            rs_item.setCount037(0);
            rs_item.setSumcount(0);
            if (rs_item.getCAP().intValue()==1 || rs_item.getCAP().intValue()==3 ) { // so  hoac xa
                var dscon1 = ds_all.stream()
                        .filter(Statistic0120206cQtiDto -> Statistic0120206cQtiDto.get_id_cha().equals(rs_item.getAgency().getId().toString()))
                        .collect(Collectors.toList());
                List<Statistic0120206cQtiDto>  dscon2  = new ArrayList<>();
                for (var obj_data : dscon1)
                {
                    var dscon_sub = ds_all.stream()
                            .filter(Statistic0120206cQtiDto -> Statistic0120206cQtiDto.get_id_cha().equals(obj_data.get_id().toString()))
                            .collect(Collectors.toList());
                    if(dscon_sub.size()>0)
                    {
                        dscon2.addAll(dscon_sub);
                    }
                }
                for (var obj_data : data)
                {
                    var check1= check_dk(dscon1,obj_data.getAgency_id());
                    var check2= check_dk2(rs_item,obj_data.getAgency_id());
                    var check3= check_dk(dscon2,obj_data.getAgency_id());
                    if(check1 || check2 || check3)
                    {
                        rs_item.setCount033(rs_item.getCount033()+ obj_data.getCount033());
                        rs_item.setCount034(rs_item.getCount034()+ obj_data.getCount034());
                        rs_item.setCount037(rs_item.getCount037()+ obj_data.getCount037());
                        rs_item.setSumcount(rs_item.getSumcount()+ obj_data.getSumcount());
                    }
                }
            }
            else  if (rs_item.getCAP().intValue()==2) // cap huyen
            {
                var dscon1 = ds_all.stream()
                        .filter(Statistic0120206cQtiDto -> Statistic0120206cQtiDto.get_id_cha().equals(rs_item.getAgency().getId().toString()))
                        .collect(Collectors.toList());
                List<Statistic0120206cQtiDto>  dscon_vp  = new ArrayList<>();
                for (var obj_data : dscon1)
                {
                    var check = false;
                    for (var xa : ds_xa)
                    {
                        if(obj_data.get_id().equals(xa.get_id()))
                        {
                            check= true;
                            continue;
                        }
                    }
                    if(check== false)
                    {
                        dscon_vp.add(obj_data);
                    }
                }

                for (var obj_data : data)
                {
                    var check1= check_dk(dscon_vp,obj_data.getAgency_id());
                    var check2= check_dk2(rs_item,obj_data.getAgency_id());
                    if(check1 || check2 )
                    {
                        rs_item.setCount033(rs_item.getCount033()+ obj_data.getCount033());
                        rs_item.setCount034(rs_item.getCount034()+ obj_data.getCount034());
                        rs_item.setCount037(rs_item.getCount037()+ obj_data.getCount037());
                        rs_item.setSumcount(rs_item.getSumcount()+ obj_data.getSumcount());
                    }
                }

            }

        }
        return rs;
    }
    public  boolean check_dk(List<Statistic0120206cQtiDto> lst,String dt)
    {
        if(lst == null) return  false;
        for(var rs_item : lst) {
            if(rs_item.get_id().equals(dt))
            {
                return  true;
            }
        }
        return  false;
    }
    public  boolean check_dk2(GetCheckCitizenLogCountByAgencyQTI kt,String dt)
    {
        if(kt.getAgency().getId().toString().equals(dt))
        {
            return  true;
        }
        return  false;
    }

    public List<GetCheckCitizenLogDto> getCitizenLog(String startDate, String endDate, List<ObjectId> agencyIds, Pageable pageable, Boolean unPageable) {
        Query query = this.getQuery(startDate, endDate, agencyIds, pageable, unPageable);
        return mongoTemplate.find(query, GetCheckCitizenLogDto.class);
    }

    public Query getQuery(String startDate, String endDate, List<ObjectId> agencyIds, Pageable pageable, Boolean unPageable) {
        Query query = new Query();
        if (unPageable) {
            pageable = Pageable.unpaged();
            query.with(pageable);
        } else {
            query.with(pageable);
        }

        // search with startDate and endDate
        TimeZone timeZone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormat.setTimeZone(timeZone);
        if ((startDate != null && !startDate.isEmpty()) && (endDate != null && !endDate.isEmpty())) {
            try {
                Date startDateFormat = dateFormat.parse(startDate);
                Date endDateFormat = dateFormat.parse(endDate);
                query.addCriteria(Criteria.where("createDate").gte(startDateFormat).lte(endDateFormat));
            } catch (ParseException parse) {
                logger.info(null, parse);
            }
        } else if (startDate != null && !startDate.isEmpty()) {
            try {
                Date startDateFormat = dateFormat.parse(startDate);
                query.addCriteria(Criteria.where("createDate").gte(startDateFormat));
            } catch (ParseException parse) {
                logger.info(null, parse);
            }
        } else if (endDate != null && !endDate.isEmpty()) {
            try {
                Date endDateFormat = dateFormat.parse(endDate);
                query.addCriteria(Criteria.where("createDate").lte(endDateFormat));
            } catch (ParseException parse) {
                logger.info(null, parse);
            }
        }

        // search with agency id
        if (!agencyIds.isEmpty()) {
            query.addCriteria(Criteria.where("agency._id").in(agencyIds));
        }

        ObjectId deploymentId = Context.getDeploymentId();
        query.addCriteria(Criteria.where("deploymentId").is(deploymentId));
        return query;
    }
/*
    public List<GetCheckCitizenLogDto> getCitizenLogQTI(String startDate, String endDate, List<ObjectId> agencyIds, Pageable pageable, Boolean unPageable, int loaicoquan,List<ObjectId> idcanbo,List<ObjectId> iddonvi) throws ParseException, JsonProcessingException {
        if(iddonvi.isEmpty()){
            if (loaicoquan == 1) {
                List<Statistic0120206cQtiDto>  ds_so_check= LayDonViCon(UBND_TINH_QUANG_TRI,null,null,"0",CAP_SO,null,"0","false");// UND tỉnh QUẢNG TRI
                for(Statistic0120206cQtiDto ds_so_check_item : ds_so_check){
                    List<Statistic0120206cQtiDto>  ds_con_so=  LayDonViCon(ds_so_check_item._id,null,null,"0",null,null,"0","true");
                    agencyIds.add(new ObjectId(ds_so_check_item.get_id()));

                    for (Statistic0120206cQtiDto ds_con_so_item : ds_con_so) {
                        agencyIds.add(new ObjectId(ds_con_so_item.get_id()));
                    }
                }
            }else if(loaicoquan == 2){
                List<Statistic0120206cQtiDto>  ds_huyen_check= LayDonViCon(UBND_TINH_QUANG_TRI,null,null,"0",CAP_HUYEN,null,"0","false");// UND tỉnh QUẢNG TRI

                for(Statistic0120206cQtiDto ds_huyen_check_item : ds_huyen_check){
                    List<Statistic0120206cQtiDto> ds_xa = LayDonViCon(ds_huyen_check_item._id, null, null, "0", CAP_XA, null, "0", "true");
                    List<Statistic0120206cQtiDto> ds_con_huyen = LayDonViCon(ds_huyen_check_item._id, null, null, "0", null, null, "0", "true");
                    for (Statistic0120206cQtiDto ob : ds_con_huyen) {
                        var check = true;
                        for (Statistic0120206cQtiDto ob2 : ds_xa) {
                            if (ob2.get_id().equals(ob.get_id())) {
                                check = false;
                            }
                        }
                        if (check) {
                            agencyIds.add(new ObjectId(ob.get_id()));
                        }
                    }
                }
            }else if(loaicoquan == 3){
                List<Statistic0120206cQtiDto>  ds_huyen_check= LayDonViCon(UBND_TINH_QUANG_TRI,null,null,"0",CAP_HUYEN,null,"0","false");// UND tỉnh QUẢNG TRI
                for(Statistic0120206cQtiDto ds_huyen_check_item : ds_huyen_check) {
                    var lstxa = LayDonViCon(ds_huyen_check_item.get_id(), null, null, "0", CAP_XA, null, "0", "false");
                    for (Statistic0120206cQtiDto dto_con : lstxa) {
                        List<Statistic0120206cQtiDto> ds_con_xa= LayDonViCon(dto_con.get_id(),null,null,"0",null,null,"0","true");

                        agencyIds.add(new ObjectId(dto_con.get_id()));

                        for(Statistic0120206cQtiDto ds_con_xa_item : ds_con_xa){
                            agencyIds.add(new ObjectId(ds_con_xa_item.get_id()));
                        }
                    }
                }
            }
        }else{
            for (ObjectId item : iddonvi) {
                List<Statistic0120206cQtiDto> ds_donvi = LayDonViCon(item.toString(), null, null, "0", null, null, "0", "true");

                for(Statistic0120206cQtiDto item_con : ds_donvi){
                    agencyIds.add(new ObjectId(item_con.get_id()));
                    String dsConSoJson = objectMapper.writeValueAsString(agencyIds);
                    logger.info("ds agencyIds: {}", item_con.get_id());

                    logger.info("ds dsConSoJson1: {}", agencyIds);
                }
            }

        }

        logger.info("ds dsConSoJson2222: {}", agencyIds);
        // Tạo truy vấn từ các thông tin đã có
        Query query = this.getQueryQTI(startDate, endDate, agencyIds, pageable, unPageable,idcanbo);

        // Thực hiện truy vấn MongoDB và trả về kết quả
        return mongoTemplate.find(query, GetCheckCitizenLogDto.class);
    }
*/

    public Query getQueryQTI(String startDate, String endDate, List<ObjectId> agencyIds, Pageable pageable, Boolean unPageable,List<ObjectId> idcanbo) throws ParseException, JsonProcessingException {
        Query query = new Query();
        if (unPageable) {
            pageable = Pageable.unpaged();
            query.with(pageable);
        } else {
            query.with(pageable);
        }

        // search with startDate and endDate
        TimeZone timeZone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormat.setTimeZone(timeZone);
        if ((startDate != null && !startDate.isEmpty()) && (endDate != null && !endDate.isEmpty())) {
            try {
                Date startDateFormat = dateFormat.parse(startDate);
                Date endDateFormat = dateFormat.parse(endDate);
                query.addCriteria(Criteria.where("createDate").gte(startDateFormat).lte(endDateFormat));
            } catch (ParseException parse) {
                logger.info(null, parse);
            }
        } else if (startDate != null && !startDate.isEmpty()) {
            try {
                Date startDateFormat = dateFormat.parse(startDate);
                query.addCriteria(Criteria.where("createDate").gte(startDateFormat));
            } catch (ParseException parse) {
                logger.info(null, parse);
            }
        } else if (endDate != null && !endDate.isEmpty()) {
            try {
                Date endDateFormat = dateFormat.parse(endDate);
                query.addCriteria(Criteria.where("createDate").lte(endDateFormat));
            } catch (ParseException parse) {
                logger.info(null, parse);
            }
        }

        // search with agency id
        if (!agencyIds.isEmpty()) {
            query.addCriteria(Criteria.where("agency._id").in(agencyIds));
        }

        // search with agency id
        if (!idcanbo.isEmpty()) {
            query.addCriteria(Criteria.where("accountId").in(idcanbo));
        }



        //ObjectId deploymentId = Context.getDeploymentId();
        //query.addCriteria(Criteria.where("deploymentId").is(deploymentId));
        return query;
    }

    public List<GroupCitizenLogDto> groupCitizenLogs(String startDate, String endDate, List<ObjectId> agencyIds) {
        // search in list of agency ids
        Criteria criteria = new Criteria();
        if (!agencyIds.isEmpty()) {
            criteria = Criteria.where("agency._id").in(agencyIds);
        } else {
            return new ArrayList<GroupCitizenLogDto>();
        }

        // search with startDate and endDate
        TimeZone timeZone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        dateFormat.setTimeZone(timeZone);
        if ((startDate != null && !startDate.isEmpty()) && (endDate != null && !endDate.isEmpty())) {
            try {
                Date startDateFormat = dateFormat.parse(startDate);
                Date endDateFormat = dateFormat.parse(endDate);
                criteria.and("createDate").gte(startDateFormat).lte(endDateFormat);
            } catch (ParseException parse) {
                logger.info(null, parse);
            }
        } else if (startDate != null && !startDate.isEmpty()) {
            try {
                Date startDateFormat = dateFormat.parse(startDate);
                criteria.and("createDate").gte(startDateFormat);
            } catch (ParseException parse) {
                logger.info(null, parse);
            }
        } else if (endDate != null && !endDate.isEmpty()) {
            try {
                Date endDateFormat = dateFormat.parse(endDate);
                criteria.and("createDate").lte(endDateFormat);
            } catch (ParseException parse) {
                logger.info(null, parse);
            }
        }

//        long skip = (long) pageable.getPageNumber() * pageable.getPageSize();
        Aggregation aggregation = newAggregation(
                match(criteria),
                group(
                        "agency._id",
                        "agency.code",
                        "agency.name"
                ).count().as("count")
//            skip(skip),
//            limit(pageable.getPageSize())
        );

//        Query query = new Query();
//        query.addCriteria(criteria);
//        query.with(pageable);

        return mongoTemplate.aggregate(aggregation, "checkCitizenLog", GroupCitizenLogDto.class).getMappedResults();
//        return PageableExecutionUtils.getPage(resultList, pageable, () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1), GroupCitizenLogDto.class));
    }
}
