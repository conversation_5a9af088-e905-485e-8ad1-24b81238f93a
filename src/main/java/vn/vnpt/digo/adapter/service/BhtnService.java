package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;
import vn.vnpt.digo.adapter.dto.bhtn.AdditionalStatusUpdateDossierDto;
import vn.vnpt.digo.adapter.dto.bhtn.BhtnResDto;
import vn.vnpt.digo.adapter.dto.bhtn.DongBoTrangThaiKetThucDto;
import vn.vnpt.digo.adapter.dto.bhtn.FeedbackProfileResultDossierDto;
import vn.vnpt.digo.adapter.dto.bhtn.PutTaxResultVbdlisDto;
import vn.vnpt.digo.adapter.dto.bhtn.ReceivingDossierDto;
import vn.vnpt.digo.adapter.dto.bhtn.ResultUpdateDossierDto;
import vn.vnpt.digo.adapter.dto.bhtn.SendProcessingNoticeDossierDto;
import vn.vnpt.digo.adapter.dto.bhtn.TiepTucXuLyDto;
import vn.vnpt.digo.adapter.dto.bhtn.YeuCauBoSungHoSoDto;
import vn.vnpt.digo.adapter.dto.bhtn.UpdateFinishDossierDto;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.HashMap;
import java.util.Base64;

@Service
public class BhtnService
{

    Logger logger = LoggerFactory.getLogger(PaymentPlatformService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private RestTemplate restTemplate;

    private Gson gson = new Gson();

    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890055");

    @Autowired
    private Microservice microservice;

    @Autowired
    private MongoTemplate mongoTemplate;

    private String getToken(String tokenUrl, String username, String password, String basicAuthHeader) {
        String tokenStr = "";

        if (basicAuthHeader != null && !basicAuthHeader.isEmpty()) {
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("Authorization", basicAuthHeader);

                MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
                map.add("grant_type", "password");
                map.add("username", username);
                map.add("password", password);

                HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<MultiValueMap<String, String>>(map,
                        headers);

                JSONObject response = new JSONObject(
                        restTemplate.exchange(tokenUrl, HttpMethod.POST, entity, String.class).getBody());
                logger.info("Response from " + tokenUrl + ": " + response);
                tokenStr = response.getString("access_token");
                logger.info("DIGO-Info: getToken value = " + tokenStr);
            } catch (Exception e) {
                logger.info("DIGO-Info: getToken Exception " + e.getMessage());
            }
        }

        return tokenStr;
    }

    public static String generateBasicAuthHeader(String username, String password) {
        String authString = username + ":" + password;
        byte[] authBytes = authString.getBytes(StandardCharsets.UTF_8);
        String encodedAuth = Base64.getEncoder().encodeToString(authBytes);
        return "Basic " + encodedAuth;
    }

    private String getTokenLGSP(String tokenUrl, String consumerKey, String consumerSecret) {
        String tokenStr = "";
        String clientId = "vilis-mobile-client";

        try {
            ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
            details.setAccessTokenUri(tokenUrl);
            details.setClientId(consumerKey);
            details.setClientSecret(consumerSecret);
            details.setGrantType("client_credentials");
            tokenStr = new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext()).getAccessToken()
                    .getValue();
            logger.info("DIGO-Info: getToken value = " + tokenStr);
        } catch (Exception e) {
            logger.info("DIGO-Info: getToken Exception " + e.getMessage());
        }

        return tokenStr;
    }

    public BhtnResDto.ReceivingDossierResponse receivingDossier(@Valid ReceivingDossierDto params) {
        logger.info("DIGO-Info: begin receivingDossier with MaHoSoMotCua = " + params.getMaHoSoMotCua());

        //Get config
        IntegratedConfigurationDto config;
        var result = new BhtnResDto.ReceivingDossierResponse();
        result.setIsError(true);
        result.setData(null);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String token = "";
            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("tiep-nhan-ho-so-url").toString();

            String username = config.getParametersValue("username");
            String password = config.getParametersValue("password");
            String usernameAuth = config.getParametersValue("basic-auth-username");
            String passwordAuth = config.getParametersValue("basic-auth-password");
            String basicAuthHeader = generateBasicAuthHeader(usernameAuth, passwordAuth);
            token = getToken(tokenUrl, username, password, basicAuthHeader);

            String linkFileBhtn = config.getParametersValue("link-file-bhtn");
            params.setLinkFileBhtn(linkFileBhtn);

            //setTimeout(restTemplate, 60000);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject.getBody());
                Gson gson = new Gson();
                BhtnResDto.BhtnResponse response = gson.fromJson(jsonModelInput, BhtnResDto.BhtnResponse.class);
                if (response == null) {
                    result.setData(response.getData().toString());
                    result.setMessage(response.getMessage());
                    result.setCode(Integer.parseInt(response.getCode()));
                } else {
                    Integer code = Integer.parseInt(response.getCode());
                    if (code == 0){
                        result.setIsError(false);
                    }

                    result.setData(response.getData().toString());
                    result.setMessage(response.getMessage());
                    result.setCode(code);
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                result.setMessage(jsonModelInput);
            }
        } catch (Exception ex) {
            result.setMessage(ex.getMessage());
        }

        return result;
    }

    public BhtnResDto.ResultUpdateDossierResponse resultUpdateDossier(ResultUpdateDossierDto params) {
        logger.info("DIGO-Info: begin resultUpdateDossier with soBienNhan = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;
        var result = new BhtnResDto.ResultUpdateDossierResponse();

        result.setIsError(true);
        result.setData(null);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {

            String endpoint = config.getParametersValue("cap-nhat-ket-qua-url").toString();
            String token = getToken(config);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), BhtnResDto.ResultUpdateDossierResponse.class);
                if (response.getData() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    result.setMessage(jsonModelInput);
                } else {
                    result = response;

                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                result.setMessage(jsonModelInput);
            }
        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        return result;
    }

    public BhtnResDto.AdditionalStatusUpdateDossierResponse aditionalStatusUpdateDossier(AdditionalStatusUpdateDossierDto params) {
        logger.info("DIGO-Info: begin aditionalStatusUpdateDossier with soBienNhan = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;
        var result = new BhtnResDto.AdditionalStatusUpdateDossierResponse();
        var status = new BhtnResDto.Status();
        result.isError = true;
        result.setData(null);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String endpoint = config.getParametersValue("cap-nhat-trang-thai-bo-sung-url").toString();
            String token = getToken(config);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), BhtnResDto.AdditionalStatusUpdateDossierResponse.class);
                if (response.getData() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    result.setMessage(jsonModelInput);
                } else {
                    result = response;
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                result.setMessage(jsonModelInput);
            }
        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }
        return result;
    }

    private String getToken(IntegratedConfigurationDto config){
        String token = "";

        String tokenUrl = config.getParametersValue("token-url");
//        String isGetTokenLGSP = config.getParametersValue("is-get-token-lgsp");
        String isGetTokenLGSP = "false";
        if (isGetTokenLGSP.equals("true")) {
            String username = config.getParametersValue("consumer-key");
            String password = config.getParametersValue("consumer-secret");
            String clientSecret = config.getParametersValue("token-value");
            token = getTokenLGSP(tokenUrl, username, password);
        } else {
            String username = config.getParametersValue("username");
            String password = config.getParametersValue("password");
            String usernameAuth = config.getParametersValue("basic-auth-username");
            String passwordAuth = config.getParametersValue("basic-auth-password");
            String basicAuthHeader = generateBasicAuthHeader(usernameAuth, passwordAuth);
            token = getToken(tokenUrl, username, password, basicAuthHeader);
        }

        return token;
    }

    public BhtnResDto.SendProcessingNoticeDossierResponse sendProcessingNoticeDossier(SendProcessingNoticeDossierDto params) {
        logger.info("DIGO-Info: begin sendProcessingNoticeDossier with soBienNhan = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String token = getToken(config);
            String endpoint = config.getParametersValue("thong-bao-url").toString();

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<BhtnResDto.SendProcessingNoticeDossierResponse> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, BhtnResDto.SendProcessingNoticeDossierResponse.class);
            //Send request
            return result.getBody();
        } catch (Exception e) {
            throw new DigoHttpException(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    public BhtnResDto.FeedbackProfileResultDossierResponse feedbackProfileResultDossier(FeedbackProfileResultDossierDto params) {
        logger.info("DIGO-Info: begin feedbackProfileResultDossier with soBienNhan = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {

            String token = getToken(config);
            String endpoint = config.getParametersValue("phan-hoi-sai-ket-qua-url").toString();

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<BhtnResDto.FeedbackProfileResultDossierResponse> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, BhtnResDto.FeedbackProfileResultDossierResponse.class);
            //Send request
            return result.getBody();
        } catch (Exception e) {
            throw new DigoHttpException(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    public BhtnResDto.DanhMucThuTucResponse getDanhMucThuTucHanhChinh() {
        logger.info("DIGO-Info: begin getDanhMucThuTucHanhChinh !");

        BhtnResDto.DanhMucThuTucResponse result = new BhtnResDto.DanhMucThuTucResponse();
        try {
            Map<String, Object> procedureParrams = new HashMap<>();

            var urlContent = "/procedure-process-definition/--get-procedure-bhtn";
//            String url = "http://localhost:8069" + urlContent;
            String url = microservice.basepadUri(urlContent).toUriString();
            result = MicroserviceExchange.get(restTemplate, url, BhtnResDto.DanhMucThuTucResponse.class, procedureParrams);

            return result;
        } catch (Exception e) {
            result.setResult(0);
            result.setMessage(e.getMessage());
        }

        return result;
    }

    public BhtnResDto.DanhMucNguoiDungResponse getDanhMucNguoiDung() {
        logger.info("DIGO-Info: begin getDanhMucNguoiDung !");

        var result = new BhtnResDto.DanhMucNguoiDungResponse();
        IntegratedConfigurationDto config;

        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(this.serviceId));
        query.addCriteria(Criteria.where("deleted").is(false));
        var configDoc = mongoTemplate.findOne(query, IntegratedConfiguration.class);
        config = GsonUtils.copyObject(configDoc, IntegratedConfigurationDto.class);

        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String agencyId = config.getParametersValue("agency-id");
            Map<String, Object> procedureParrams = new HashMap<>();
            String url = "/user/--user-vbdlis?agency-id=" + agencyId;
            String userUrl = microservice.humanUri(url).toUriString();
//            String userUrl = "http://localhost:8070" + url;
            String responseStr = MicroserviceExchange.get(restTemplate, userUrl, String.class, procedureParrams);

            JsonArray userContentArrays = new JsonParser().parse(responseStr).getAsJsonArray();

            var danhMucNguoiDungDtos = new ArrayList<BhtnResDto.DanhMucNguoiDungDto>();
            for (int i = 0; i < userContentArrays.size(); i++) {
                var target = new BhtnResDto.DanhMucNguoiDungDto();
                target.setMaNguoiDung(userContentArrays.get(i).getAsJsonObject().get("id").getAsString());
                target.setTenNguoiDung(userContentArrays.get(i).getAsJsonObject().get("fullname").getAsString());

                JsonArray experiences = userContentArrays.get(i).getAsJsonObject().get("experience").getAsJsonArray();
                for (int j = 0; j < experiences.size(); j++) {
                    var objectExperience = experiences.get(j).getAsJsonObject();
                    Boolean isPrimary = objectExperience.get("primary").getAsBoolean();
                    if (isPrimary) {
                        var positionName = objectExperience.get("position").getAsJsonObject().get("name").getAsString();
                        target.setChucDanh(positionName);

                        var agencyName = objectExperience.get("agency").getAsJsonObject().get("name").getAsString();
                        target.setTenPhongBan(agencyName);
                    }
                }

                danhMucNguoiDungDtos.add(target);
            }

            result.setResult(1);
            result.setData(danhMucNguoiDungDtos);
        } catch (Exception e) {
            result.setResult(0);
            result.setMessage(e.getMessage());
        }

        return result;
    }

    public BhtnResDto.DanhMucTrangThaiResponse getDanhMucTrangThai() {
        logger.info("DIGO-Info: begin getDanhMucTrangThai !");

        var result = new BhtnResDto.DanhMucTrangThaiResponse();
        try {
            Map<String, Object> procedureParrams = new HashMap<>();
            String tagUrl = microservice.basecatUri("/tag/--by-category-id?category-id=5f3a491c4e1bd312a6f00012&page=0&size=1000").toUriString();
            String responseStr = MicroserviceExchange.get(restTemplate, tagUrl, String.class, procedureParrams);
            Map<String, Object> data = new ObjectMapper().readValue(responseStr, Map.class);
            List<BhtnResDto.DanhMucTrangThaiDto.DossierStatusDto> dossierStatues = GsonUtils.copyList(gson.toJson(data.get("content")), BhtnResDto.DanhMucTrangThaiDto.DossierStatusDto.class);

            var danhMucTrangThais = new ArrayList<BhtnResDto.DanhMucTrangThaiDto>();
            for (BhtnResDto.DanhMucTrangThaiDto.DossierStatusDto dossierStatus : dossierStatues) {
                var target = new BhtnResDto.DanhMucTrangThaiDto();
                target.setMaTrangThai(dossierStatus.getId());
                target.setTenTrangThai(dossierStatus.getName());

                danhMucTrangThais.add(target);
            }
            result.setResult(1);
            result.setData(danhMucTrangThais);
        } catch (Exception e) {
            result.setResult(0);
            result.setMessage(e.getMessage());
        }

        return result;
    }

    public BhtnResDto.YeuCauBoSungResponse postDongBoTrangThaiKetThuc(DongBoTrangThaiKetThucDto dongBoTrangThaiKetThucDto) {
        logger.info("DIGO-Info: begin postDongBoTrangThaiKetThuc !");

        var result = new BhtnResDto.YeuCauBoSungResponse();
        result.setResult(0);
        try {
            if(dongBoTrangThaiKetThucDto.getSoBienNhan() == null || dongBoTrangThaiKetThucDto.getSoBienNhan() == ""){
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[] { "SoBienNhan" }));
                return result;
            }

            var urlContent = String.format("/bhtn/%s/--status2", dongBoTrangThaiKetThucDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
//            String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, dongBoTrangThaiKetThucDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");
            String message = responseFromPadmanObj.getString("message");

            if(affectedRows > 0){
                result.setResult(1);
            }else{
                result.setMessage(message);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        return result;
    }


    public BhtnResDto.YeuCauBoSungResponse postYeucauBoSung(YeuCauBoSungHoSoDto yeuCauBoSungHoSoDto) {
        logger.info("DIGO-Info: begin postYeucauBoSung !");

        var result = new BhtnResDto.YeuCauBoSungResponse();
        result.setResult(0);
        try {
            if (yeuCauBoSungHoSoDto.getSoBienNhan() == null || yeuCauBoSungHoSoDto.getSoBienNhan() == "") {
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[]{"SoBienNhan"}));
                return result;
            }

            var urlContent = String.format("/bhtn/%s/--additional-request", yeuCauBoSungHoSoDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
            //String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, yeuCauBoSungHoSoDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");
            String message = responseFromPadmanObj.getString("message");

            if (affectedRows > 0) {
                result.setResult(1);
            } else {
                result.setMessage(message);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        return result;
    }

    public BhtnResDto.YeuCauBoSungResponse postTiepTucXuLy(TiepTucXuLyDto tiepTucXuLyDto) {
        logger.info("DIGO-Info: begin postYeucauBoSung !");

        var result = new BhtnResDto.YeuCauBoSungResponse();
        result.setResult(0);
        try {
            if (tiepTucXuLyDto.getSoBienNhan() == null || tiepTucXuLyDto.getSoBienNhan() == "") {
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[]{"SoBienNhan"}));
                return result;
            }

            var urlContent = String.format("/bhtn/%s/--continue", tiepTucXuLyDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
            //String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, tiepTucXuLyDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");
            String message = responseFromPadmanObj.getString("message");

            if (affectedRows > 0) {
                result.setResult(1);
            } else {
                result.setMessage(message);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        return result;
    }

    private void setTimeout(RestTemplate restTemplate, int timeout) {
        //Explicitly setting ClientHttpRequestFactory instance to
        //SimpleClientHttpRequestFactory instance to leverage
        //set*Timeout methods
        restTemplate.setRequestFactory(new SimpleClientHttpRequestFactory());
        SimpleClientHttpRequestFactory rf = (SimpleClientHttpRequestFactory) restTemplate
                .getRequestFactory();
        rf.setConnectTimeout(timeout);
    }

    public BhtnResDto.AdditionalStatusUpdateDossierResponse updateAdditionalRequestDossier(AdditionalStatusUpdateDossierDto params) {
        logger.info("DIGO-Info: begin updateAdditionalRequestDossier with getSoBienNhan = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;
        var result = new BhtnResDto.AdditionalStatusUpdateDossierResponse();
        result.isError = true;
        result.setData(null);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            Query query = new Query();
            query.addCriteria(Criteria.where("service.id").is(this.serviceId));
            query.addCriteria(Criteria.where("deleted").is(false));
            var configDoc = mongoTemplate.findOne(query, IntegratedConfiguration.class);
            config = GsonUtils.copyObject(configDoc, IntegratedConfigurationDto.class);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String token = getToken(config);
            String endpoint = config.getParametersValue("cap-nhat-trang-thai-bo-sung-url").toString();

            String linkFileBhtn = config.getParametersValue("link-file-bhtn");
            params.setLinkFileBhtn(linkFileBhtn);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), BhtnResDto.AdditionalStatusUpdateDossierResponse.class);
                if (response.getData() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    result.setMessage(jsonModelInput);
                } else {
                    result = response;
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                result.setMessage(jsonModelInput);
            }
        } catch (Exception ex) {
            result.setMessage(ex.getMessage());
        }

        return result;
    }


    public BhtnResDto.GuiKetQuaThueResponse postTaxResult(PutTaxResultVbdlisDto putTaxResultVbdlisDto) {
        logger.info("DIGO-Info: begin postTaxResult !");

        var result = new BhtnResDto.GuiKetQuaThueResponse();
        result.setResult(0);
        try {
            if(putTaxResultVbdlisDto.getSoBienNhan() == null || putTaxResultVbdlisDto.getSoBienNhan() == ""){
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[] { "SoBienNhan" }));
                return result;
            }

            var urlContent = String.format("/bhtn/%s/--next-task-financial-v2", putTaxResultVbdlisDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
//            String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, putTaxResultVbdlisDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");
            String message = responseFromPadmanObj.getString("message");

            if(affectedRows > 0){
                result.setResult(1);
            }else{
                result.setMessage(message);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        return result;
    }

    public BhtnResDto.ResultUpdateDossierResponse updateResultFinishDossier(UpdateFinishDossierDto params) {
        logger.info("DIGO-Info: begin updateResultFinishDossier with MaHoSoMotCua = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;
        var result = new BhtnResDto.ResultUpdateDossierResponse();
        result.setIsError(true);
        result.setData(null);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String token = getToken(config);
            String endpoint = config.getParametersValue("cap-nhat-ket-qua-url").toString();
            String linkFileBhtn = config.getParametersValue("link-file-bhtn");

            // map status and link file dossier
            params.setLinkFileBhtn(linkFileBhtn);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            try {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject.getBody());
                Gson gson = new Gson();
                BhtnResDto.BhtnResponse response = gson.fromJson(jsonModelInput, BhtnResDto.BhtnResponse.class);
                if (response == null) {
                    result.setData(response.getData().toString());
                    result.setMessage(response.getMessage());
                    result.setCode(Integer.parseInt(response.getCode()));
                } else {
                    Integer code = Integer.parseInt(response.getCode());
                    if (code == 0){
                        result.setIsError(false);
                    }

                    result.setData(response.getData().toString());
                    result.setMessage(response.getMessage());
                    result.setCode(code);
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                result.setMessage(jsonModelInput);
            }
        } catch (Exception ex) {
            result.setMessage(ex.getMessage());
        }

        logger.info("DIGO-Info: End updateResultFinishDossier with status = " + result.getCode());
        return result;
    }
}
