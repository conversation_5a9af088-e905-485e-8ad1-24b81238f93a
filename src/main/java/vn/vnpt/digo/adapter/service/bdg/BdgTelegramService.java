package vn.vnpt.digo.adapter.service.bdg;

import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.bdg.TelegramAttachmentBodyDto;

@Service
public class BdgTelegramService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Value(value = "${notify.telegram.id}")
    private String telegramId;

    @Value(value = "${notify.telegram.token}")
    private String telegramToken;

    public static final String HTML_PARSE_MOD = "HTML";

    Logger logger = LoggerFactory.getLogger(BdgTelegramService.class);

    private static final String HOST_URL = "https://api.telegram.org/bot";

    @Async
    public void notify(String chatId, TelegramAttachmentBodyDto input) {
        try{
            String apiUrl = HOST_URL + telegramId + ":" + telegramToken + "/sendDocument";
            RestTemplate rest = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("chat_id", chatId);
            body.add("document", input.getResourceFile());
            if (!Strings.isNullOrEmpty(input.getMessage())) {
                body.add("caption", input.getMessage());
            }
            HttpEntity<?> request = new HttpEntity<>(body, headers);
            ResponseEntity<String> resEntity = rest.exchange(
                    apiUrl,
                    HttpMethod.POST,
                    request,
                    String.class
            );
            String res = resEntity.getBody();
        }catch (Exception e){
            logger.info("Bdg telegram notify failed: " + e.getMessage());
        }
    }

    @Async
    public void notify(String chatId, String message) {
        try{
            String apiUrl = HOST_URL + telegramId + ":" + telegramToken + "/sendMessage?chat_id=" + chatId + "&text=" + message;
            HttpHeaders headers = new HttpHeaders();
            RestTemplate rest = new RestTemplate();
            HttpEntity<?> request = new HttpEntity<>(headers);
            ResponseEntity<String> resEntity = rest.exchange(
                    apiUrl,
                    HttpMethod.GET,
                    request,
                    String.class
            );
            String res = resEntity.getBody();
        }catch (Exception e){
            logger.info("Bdg telegram notify message failed: " + e.getMessage());
        }
    }

    @Async
    public void notify(String chatId, String message, String parseMod) {
        try{
            String apiUrl = HOST_URL + telegramId + ":" + telegramToken + "/sendMessage?chat_id=" + chatId + "&parse_mode="+ parseMod +"&text=" + message;
            HttpHeaders headers = new HttpHeaders();
            RestTemplate rest = new RestTemplate();
            HttpEntity<?> request = new HttpEntity<>(headers);
            ResponseEntity<String> resEntity = rest.exchange(
                    apiUrl,
                    HttpMethod.GET,
                    request,
                    String.class
            );
            String res = resEntity.getBody();
        }catch (Exception e){
            logger.info("Bdg telegram notify message failed: " + e.getMessage());
        }
    }
}
