package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.DossierFormFileDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.qti.*;
import vn.vnpt.digo.adapter.dto.ktm_social_protection.KTMDossierApply;
import vn.vnpt.digo.adapter.dto.minhtue.tntm.PadPApplyBoTNMTDto;
import vn.vnpt.digo.adapter.dto.minhtue.tntm.TaiLieuNop;
import vn.vnpt.digo.adapter.pojo.Id;
import vn.vnpt.digo.adapter.pojo.ProcedureForm;
import vn.vnpt.digo.adapter.pojo.ValueData;
import vn.vnpt.digo.adapter.util.DateTimeUntil;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;

import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Component
public class QTIGTVTService {

    @Autowired
    private RestTemplate restTemplate;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;
    @Value(value = "${digo.oidc.client-id}")
    private String clientId;

    @Value(value = "${digo.oidc.client-secret}")
    private String clientSecret;

    @Value(value = "${gtvt.qti.configid}")
    private String configIdQTI;

    @Value(value = "${gtvt.qti.codeAgency}")
    private String codeAgency;

    @Autowired
    private Microservice microservice;
    private final ObjectId SERVICE_ID = new ObjectId("5f7c16069abb62f511890003");
    Logger logger = LoggerFactory.getLogger(QTIGTVTService.class);

    public String addDossierGTVT(GTVTDossierDto postGTVTDossierDto) {
        try {
            Gson gson = new Gson();
            String eform = "";

            logger.info(postGTVTDossierDto.getMaHoSo());
            PadApplyDossierGTVTDto callPad = new  PadApplyDossierGTVTDto();
            if(Objects.nonNull(postGTVTDossierDto.getMaHoSo())){
                callPad.setCode(postGTVTDossierDto.getMaHoSo());
            }
            if(!Objects.nonNull(postGTVTDossierDto.getMaHoSo()) || postGTVTDossierDto.getMaHoSo() == ""){
                String codeUrl = microservice.basecatUri("/pattern").toUriString() + "/" + configIdQTI + "/--get-next-value?code=" + codeAgency;
                logger.info("generateCode "+codeUrl);
                ValueData valueCode = null;
                try {
                    valueCode = MicroserviceExchange.putNoAuthNoBody(getRestTemplate(), codeUrl, ValueData.class);
                    callPad.setCode(valueCode.getValue());
                } catch (Exception e) {
                    logger.error(e.getMessage());
                }
            }


            String procedureUrl = microservice.basepadUri("/procedure/").toUriString() + "procedureGTVT/--full?code=" + postGTVTDossierDto.getMaTTHC()+"&codeAgency="+postGTVTDossierDto.getDonViXuLy() +"&codeSector="+postGTVTDossierDto.getMaLinhVuc();
//            String procedureUrl = "http://localhost:8091/procedure/procedureGTVT/--full?code=" + postGTVTDossierDto.getMaTTHC()+"&codeAgency="+postGTVTDossierDto.getDonViXuLy() +"&codeSector="+postGTVTDossierDto.getMaLinhVuc();
            PadApplyDossierGTVTDto.ProcedureDto procedure = MicroserviceExchange.get(this.getRestTemplate(), procedureUrl, PadApplyDossierGTVTDto.ProcedureDto.class);
            PadApplyDossierGTVTDto.ProcedureDto procedureDto = new PadApplyDossierGTVTDto.ProcedureDto(procedure.getId(), procedure.getCode(), procedure.getTranslate(), procedure.getSector(), procedure.getAgency(), procedure.getProcessDefinitionCount());
            callPad.setProcedure(procedureDto);
            String agencyUrl = microservice.basedataUri("/agency/" + postGTVTDossierDto.getIdCoQuanCongTac() ).toUriString();
            AgencyDto agency = MicroserviceExchange.getNoAuth(this.getRestTemplate(), agencyUrl, AgencyDto.class);

            PadApplyDossierGTVTDto.AgencyDto agencyDto = new PadApplyDossierGTVTDto.AgencyDto();
            if(Objects.nonNull(agency)){
                agencyDto.setId(agency.getId());
                agencyDto.setCode(agency.getCode());
                agencyDto.setName(agency.getName());
            }
            if(Objects.nonNull(procedure.getAgency().get(0))){
                PadApplyDossierGTVTDto.AgencyParentDto parent = new PadApplyDossierGTVTDto.AgencyParentDto();
                parent.setId(procedure.getAgency().get(0).getId().toString());
                parent.setName(procedure.getAgency().get(0).getName());
                agencyDto.setParent(parent);
//                    AgencyDto.setId(procedure.getAgency().get(0).getId().toString());
//                    AgencyDto.setCode(procedure.getAgency().get(0).getCode());
//                    AgencyDto.setName(procedure.getAgency().get(0).getName());
            }
            callPad.setAgency(agencyDto);
            if(Objects.nonNull(postGTVTDossierDto.getMaQuyTrinh())){
                String processDefinitionUrl = microservice.bpmUri("/process-definition/").toUriString() + postGTVTDossierDto.getMaQuyTrinh();
                ProcessDefinitionDto processDefinition =  MicroserviceExchange.get(this.getRestTemplate(), processDefinitionUrl, ProcessDefinitionDto.class);

                if(Objects.nonNull(processDefinition)){
                    String processUrl = microservice.basepadUri("/procedure-process-definition/find-by-procedure-processdefinition?idProcedure=").toUriString() +procedure.getId()+"&idProcessDefinition="+processDefinition.getId();
//                    String processUrl = "http://localhost:8091/procedure-process-definition/find-by-procedure-processdefinition?idProcedure="+procedure.getId()+"&idProcessDefinition="+processDefinition.getId();
                    PadApplyDossierGTVTDto.ProcessDto process = MicroserviceExchange.get(this.getRestTemplate(), processUrl, PadApplyDossierGTVTDto.ProcessDto.class);
                    callPad.setProcedureProcessDefinition(process);
                    try {
                        JSONObject json = new org.json.JSONObject(GsonUtils.getJson(process.getProcessDefinition()));
                        if(json.getJSONObject("applicantEForm").getString("id") != null)
                        {
                            eform = json.getJSONObject("applicantEForm").getString("id");
                        }
                    }
                    catch (Exception e) {
                        logger.info("GTVTQTI-synchronizeDossier: lỗi map eform " + e);
                    }
                }
            }
            // Lay thong tin phi / le phi
            String procostUrl = microservice.basepadUri("/procost/--for-online?procedure-id=" + procedure.getId()).toUriString();
            DossierFee.ProcostOnline[] procost = MicroserviceExchange.getNoAuth(this.getRestTemplate(), procostUrl, DossierFee.ProcostOnline[].class);
            ArrayList<PadApplyDossierGTVTDto.PostDossierFee> targetFees = new ArrayList<>();

            if(procost != null && procost.length > 0){
                for (int j = 0; j < procost.length; j++){
                    PadApplyDossierGTVTDto.PostDossierFee targetFee = new PadApplyDossierGTVTDto.PostDossierFee();
                    DossierFee.ProcostOnline sourceFee = procost[j];
                    List<ProcostTypeAdvance> advances = sourceFee.getAdvance();
                    double amount = 0;
                    if(Objects.nonNull(advances) && advances.size()>0 ){
                       var p = advances.get(0);
                        if(Objects.nonNull(p)){
                            amount = Double.parseDouble(p.getCost());
                        }
                    }
                    if(amount == 0) {
                        amount =   Double.parseDouble(sourceFee.getCost());
                    }
                    targetFee.setAmount(amount);
                    targetFee.setQuantity(sourceFee.getQuantity());
                    targetFee.setPaid(0.0);

                    // type
                    PadApplyDossierGTVTDto.Procost tempProcost = new PadApplyDossierGTVTDto.Procost();
                    PadApplyDossierGTVTDto.Type targetType  = new PadApplyDossierGTVTDto.Type();
                    DossierFee.ProcostTypeSimple sourceType = sourceFee.getType();
                    targetType.setId(sourceType.getId());
                    targetType.setType(sourceType.getType());
                    targetType.setName(sourceType.getName());
                    targetType.setQuantityEditable(sourceType.getQuantityEditable());
                    tempProcost.setType(targetType);
                    tempProcost.setId(new ObjectId(sourceFee.getId()));
                    tempProcost.setDescription(sourceType.getName());


                    targetFee.setProcost(tempProcost);
                    targetFees.add(targetFee);
                }
            }

            callPad.setDossierFee(targetFees);

            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
            PadApplyDossierGTVTDto.ApplicantDataDto applicant = new PadApplyDossierGTVTDto.ApplicantDataDto();
            applicant.setFullname(postGTVTDossierDto.getNguoiNop().getTen());
            applicant.setIdentityNumber(postGTVTDossierDto.getNguoiNop().getSoCMND());
            applicant.setPhoneNumber(postGTVTDossierDto.getNguoiNop().getSoDienThoai());
            applicant.setEmail(postGTVTDossierDto.getNguoiNop().getEmail());
            applicant.setProvince(postGTVTDossierDto.getNguoiNop().getTinh());
            applicant.setDistrict(postGTVTDossierDto.getNguoiNop().getHuyen());
            applicant.setVillage(postGTVTDossierDto.getNguoiNop().getXa());
            applicant.setAddress(postGTVTDossierDto.getNguoiNop().getDiaChi());
            if(Objects.nonNull(postGTVTDossierDto.getNguoiNop().getNgaySinh()) && !postGTVTDossierDto.getNguoiNop().getNgaySinh().equalsIgnoreCase("")){
                String ngaySinh = DateTimeUntil.formatDateTime(postGTVTDossierDto.getNguoiNop().getNgaySinh());
                applicant.setBirthday(ngaySinh);
            }

            applicant.setPhone(postGTVTDossierDto.getSoDienThoai());
//                applicant.setEmail(data.getEmail());
            applicant.setFax(postGTVTDossierDto.getFax());


            applicant.setOwnerFullname(postGTVTDossierDto.getChuHoSo().getTen());
            applicant.setOwnerPhoneNumber(postGTVTDossierDto.getChuHoSo().getSoDienThoai());
            applicant.setOwnerIdentityNumber(postGTVTDossierDto.getChuHoSo().getSoCMND());
            applicant.setOwnerEmail(postGTVTDossierDto.getChuHoSo().getEmail());
            applicant.setProvince1(postGTVTDossierDto.getChuHoSo().getTinh());
            applicant.setDistrict1(postGTVTDossierDto.getChuHoSo().getHuyen());
            applicant.setVillage1(postGTVTDossierDto.getChuHoSo().getXa());
            applicant.setOwnerAddress(postGTVTDossierDto.getChuHoSo().getDiaChi());

            if(Objects.nonNull(postGTVTDossierDto.getChuHoSo().getNgaySinh()) && !postGTVTDossierDto.getChuHoSo().getNgaySinh().equalsIgnoreCase("")){
                String ngaySinh1 = DateTimeUntil.formatDateTime(postGTVTDossierDto.getChuHoSo().getNgaySinh());
                applicant.setOwnerBirthday(ngaySinh1);
            }

            PadApplyDossierGTVTDto.ApplicantDto applicantMain = new PadApplyDossierGTVTDto.ApplicantDto();
            if(eform != ""){
                applicantMain.setEformId(eform);
            }
            applicantMain.setData(applicant);
            callPad.setApplicant(applicantMain);


            if(Objects.nonNull(postGTVTDossierDto.getNgayTiepNhan()) && !postGTVTDossierDto.getNgayTiepNhan().equalsIgnoreCase("")){
                Date ngayTiepNhan = formatter.parse(postGTVTDossierDto.getNgayTiepNhan());
                callPad.setAcceptedDate(ngayTiepNhan);
            }
            //get appointmentDate
            if(Objects.nonNull(postGTVTDossierDto.getNgayHenTra()) && !postGTVTDossierDto.getNgayHenTra().equalsIgnoreCase("")){
                Date ngayHenTra = formatter.parse(postGTVTDossierDto.getNgayHenTra());
                callPad.setAppointmentDate(ngayHenTra);
            }
            //get returnedDate
            if(Objects.nonNull(postGTVTDossierDto.getNgayTra())&& !postGTVTDossierDto.getNgayTra().equalsIgnoreCase("")) {
                Date ngayTra = formatter.parse(postGTVTDossierDto.getNgayTra());
                callPad.setAppointmentDate(ngayTra);
            }

//            PadApplyDossierGTVTDto.IdNameDto dossierTaskStatus = new PadApplyDossierGTVTDto.IdNameDto();
//            dossierTaskStatus.setId("658cec8de48fa03610a2d8d8");
//            List<PadApplyDossierGTVTDto.NameDto> name = new ArrayList<>() {
//                {
//                    add(new PadApplyDossierGTVTDto.NameDto((short) 228, "Vừa tiếp nhận"));
//
//                }
//            };
//            dossierTaskStatus.setName(name);
//            callPad.setDossierTaskStatus(dossierTaskStatus);

            List<PadApplyDossierGTVTDto.AttachmentDto> lstAttachTaiLieuNop = new ArrayList<>();
            if (postGTVTDossierDto.getTaiLieuNop() != null) {
                String uploadUrl = microservice.filemanUri("/file/--multiple").toUriString();
                MultiValueMap<String, Object> requestMap = new LinkedMultiValueMap<>();
                for (TaiLieuNop item : postGTVTDossierDto.getTaiLieuNop()) {
                    String fileUrl = item.getDuongDanTaiTepTin();
                    logger.info("save file update/nhanhsdvcqg form: " + fileUrl);
                    String shortUrl = "file";
                    String url = microservice.filemanUri(shortUrl).toUriString() + "/--by-url?file-url=" + URLEncoder.encode(fileUrl, StandardCharsets.UTF_8);
                    logger.info("save file: " + url);
                    var na = this.getRestTemplate();
                    Id idFile = MicroserviceExchange.postJson(this.getRestTemplate(), url, null, Id.class);
                    logger.info(idFile.toString());
                    PadApplyDossierGTVTDto.AttachmentDto att = new PadApplyDossierGTVTDto.AttachmentDto(
                            idFile.getId().toHexString(), item.getTenTepDinhKem(), null, null
                    );
                    lstAttachTaiLieuNop.add(att);

                    try
                    {
                        String urlRename = microservice.filemanUri(shortUrl).toUriString() + "/"+idFile.getId().toHexString()+"/rename?name=" + item.getTenTepDinhKem();
                        AffectedRowsDto resultRename = MicroserviceExchange.putJson(this.getRestTemplate(), urlRename, null, AffectedRowsDto.class);
                    }
                    catch(Exception e){

                    }
                };

            }

            String newDossierUrl = microservice.padmanUri("/dossier/--apply-online").toUriString();
//            String newDossierUrl = "http://localhost:8081/dossier/--apply-online";

            // Pretty res and return
            IdDto newDossier = MicroserviceExchange.postJson(this.restTemplate, newDossierUrl, callPad, IdDto.class);
            System.out.println(newDossier);
            logger.info("Luu thong tin vao dossier file");

            ArrayList<DossierFormFileDto> listDossierFormFileDto = new ArrayList<>();
            if (lstAttachTaiLieuNop.size() > 0) {
                DossierFormFileDto dossierFormFile = this.addTaiLieuNop(newDossier.getId().toHexString(), callPad.getProcedureProcessDefinition().getId().toString(), lstAttachTaiLieuNop);
                listDossierFormFileDto.add(dossierFormFile);
            }
            if (listDossierFormFileDto.size() > 0) {
                String newDossierFormFileURL = microservice.padmanUri("/dossier-form-file").toUriString() + "/--by-dossier?dossier-id=" + newDossier.getId().toHexString();
                // Pretty res and return
                AffectedRowsDto newDossierFormFile = MicroserviceExchange.putJson(this.restTemplate, newDossierFormFileURL, listDossierFormFileDto, AffectedRowsDto.class);
                System.out.println(newDossierFormFile);
            }

            if(newDossier != null){
                UserPassDto userPassDto = new UserPassDto();
                userPassDto.setUserName(postGTVTDossierDto.getTKTiepNhan());
                userPassDto.setPass(postGTVTDossierDto.getMatKhauTK());

                String acceptedDossierUrl =  microservice.padmanUri("/dossier/accepted-dossier-qti/").toUriString() + newDossier.getId().toHexString()+"?idAgency="+postGTVTDossierDto.getIdCoQuanCongTac();
//                String acceptedDossierUrl = "http://localhost:8081/dossier/accepted-dossier-qti/" + newDossier.getId().toHexString()+"?idAgency="+postGTVTDossierDto.getIdCoQuanCongTac();
                String newDossier2 = MicroserviceExchange.postJson(this.restTemplate, acceptedDossierUrl, userPassDto, String.class);
                System.out.println(newDossier2);
                logger.info("Tiep nhan ho so dong bo cua QTi");
                return newDossier2;
            }
            return newDossier.toString();
        }catch (Exception e) {
            logger.info("QTIGTVT-addDossierGTVT: add dossier GTVT fail " + e);
            return e.toString();
        }
    }

    private RestTemplate getRestTemplate() {
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(this.tokenUrl + "/protocol/openid-connect/token");
        details.setClientId(this.clientId);
        details.setClientSecret(this.clientSecret);
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }

    public DossierFormFileDto addTaiLieuNop(String dossierId, String processId ,List<PadApplyDossierGTVTDto.AttachmentDto> lstAttach){
        DossierFormFileDto dossierFormFile = new DossierFormFileDto();
        // set dossier
        DossierFormFileDto.Id dossier = new DossierFormFileDto.Id();
        dossier.setId(dossierId);
        dossierFormFile.setDossier(dossier);
        // set procedureForm
        ProcedureForm procedureForm = new ProcedureForm();
        procedureForm.setId("624ea3f1ce65a21305f3516b");
        String textFormName = new String("T\u00e0i li\u1ec7u n\u1ed9p t\u1eeb dvcqg".getBytes(StandardCharsets.UTF_8),
                Charset.forName("UTF-8"));
        procedureForm.setName(textFormName);
        dossierFormFile.setProcedureForm(procedureForm);
        // set case
        DossierFormFileDto.Id caze = new DossierFormFileDto.Id();
        caze.setId(processId);
        dossierFormFile.setCaze(caze);

        dossierFormFile.setOrder(1);
        dossierFormFile.setQuantity(1);

        // set detail
        String textDetail = new String("B\u1ea3n sao".getBytes(StandardCharsets.UTF_8),
                Charset.forName("UTF-8"));
        DossierFormFileDto.ProcedureFormDetail detail = new DossierFormFileDto.ProcedureFormDetail(
                new  DossierFormFileDto.ProcedureFormDetailType("623462c0f2e2ad4ed5787167",textDetail), 1
        );
        dossierFormFile.setDetail(detail);
        dossierFormFile.setFile((ArrayList) lstAttach);

        return dossierFormFile;
    };
}
