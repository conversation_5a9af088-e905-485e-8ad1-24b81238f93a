package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.KTM_Monre_Integration.*;
import vn.vnpt.digo.adapter.dto.ktm_social_protection.KTMDossierApply;
import vn.vnpt.digo.adapter.dto.ktm_social_protection.KTMSocialProtectionParamsDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.Id;
import vn.vnpt.digo.adapter.pojo.ProcedureForm;
import vn.vnpt.digo.adapter.pojo.Translate;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Service
public class AGGMonreIntegrationService {
    Logger logger = LoggerFactory.getLogger(PaymentPlatformService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private MappingDataService mappingDataService;

    @Autowired
    private RestTemplate restTemplate;

    private Gson gson = new Gson();

    @Autowired
    private Microservice microservice;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Value(value = "65b36ced21f1aeb5f2731f16")
    private ObjectId serviceId;

    @Value(value = "${ktm.monre.mapDossierStatus.servceid}")
    private ObjectId mapDossierStatusServiceId;

    @Value(value = "${ktm.monre.mapProvince.serviceid}")
    private ObjectId mapProvinceServiceId;

    @Value(value = "${ktm.monre.mapDistrict.serviceid}")
    private ObjectId mapDistrictServiceId;

    @Value(value = "${digo.oidc.client-id}")
    private String clientId;

    @Value(value = "${digo.oidc.client-secret}")
    private String clientSecret;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;

    @Value(value = "${digo.microservice.gateway-url}")
    private String gatewayUrl;

    final String INTEGRATED_FORMAT = "yyyy/MM/dd HH:mm:ss";
    final SimpleDateFormat integratedSDF = new SimpleDateFormat(INTEGRATED_FORMAT);

    final String LOCAL_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
    final SimpleDateFormat localSDF = new SimpleDateFormat(LOCAL_FORMAT);

    final String REMOTE_FORMAT = "yyyyMMddHHmmss";

    final SimpleDateFormat remoteSDF = new SimpleDateFormat(REMOTE_FORMAT);

    public IntegratedConfigurationDto getConfig(KTMSocialProtectionParamsDto params){
        IntegratedConfigurationDto config;

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        return config;
    }

    private String getToken(IntegratedConfigurationDto config) {
        String tokenStr = "";
        String tokenUrl = config.getParametersValue("token-url").toString();
        String consumerKey = config.getParametersValue("consumer-key").toString();
        String consumerSecret = config.getParametersValue("consumer-secret").toString();
        String tokenValue = config.getParametersValue("token-value").toString();

        if (tokenValue != null && !tokenValue.isEmpty()) {
            MultiValueMap<String, String> map = null;
            JSONObject response = null;
            HttpEntity<MultiValueMap<String, String>> entity = null;
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.set("Authorization", tokenValue);

                map = new LinkedMultiValueMap<>();
                map.add("grant_type", "client_credentials");

                entity = new HttpEntity<MultiValueMap<String, String>>(map,
                        headers);

                response = new JSONObject(
                        restTemplate.exchange(tokenUrl, HttpMethod.POST, entity, String.class).getBody());
                logger.info("Response from " + tokenUrl + ": " + response);
                tokenStr = response.getString("access_token");
                logger.info("DIGO-Info: getToken value = " + tokenStr);
            } catch (Exception e) {
                logger.info("DIGO-Info: getToken Exception " + e.getMessage());
//                throw new DigoHttpException(10003, new String[]{translator.toLocale("lang.word.token")}, HttpServletResponse.SC_BAD_REQUEST);
                writeLog("Token LGSP", false, tokenUrl, "POST", "token value : " + tokenValue, null, null, "Không lấy được token LGSP : " + e.getMessage(), null);
            }
        } else {
            ClientCredentialsResourceDetails details = null;
            try {
                details = new ClientCredentialsResourceDetails();
                details.setAccessTokenUri(tokenUrl);
                details.setClientId(consumerKey);
                details.setClientSecret(consumerSecret);
                details.setGrantType("client_credentials");
                tokenStr = new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext()).getAccessToken()
                        .getValue();
                logger.info("DIGO-Info: getToken value = " + tokenStr);
            } catch (Exception e) {
                logger.info("DIGO-Info: getToken Exception " + e.getMessage());
//                throw new DigoHttpException(10003, new String[]{translator.toLocale("lang.word.token")}, HttpServletResponse.SC_BAD_REQUEST);
                writeLog("Token LGSP", false, tokenUrl, "POST", "config id : " + config.getId().toString(), null, null, "Không lấy được token LGSP : " + e.getMessage(), null);
            }
        }
        return tokenStr;
    }

    //    @Scheduled(cron = "0 0/2 * * * *", zone = "Asia/Ho_Chi_Minh")
    @Scheduled(cron = "0 0/30 * * * ?", zone = "Asia/Ho_Chi_Minh")
    ////@SchedulerLock(name = "syncDossierFromMonreAgg", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void syncDossierFromMonre(){
        // Lay config
        IntegratedConfigurationDto config = configurationService.searchByServiceId(this.serviceId);
        if (Objects.isNull(config)) {
            writeLog("syncDossierFromMonre:Config", false, null, null, "service id : " + this.serviceId, null, null, "Không lấy được config", null);
            return;
        }

        // Kiem tra bien dong bo lay ho so
        Boolean enableGetDossier;
        try {
            enableGetDossier = config.getParametersValue("enable-get-dossier");
        } catch (Exception e){
            enableGetDossier = false;
        }

        if(!enableGetDossier){
            writeLog("syncDossierFromMonre:CheckEnableGetDossierAGG", false, null, null, null, null, null, "Biến đồng bộ lấy hồ sơ đang tắt", "enable-get-dossier");
            return;
        }

        // Lay token
        String token = getToken(config);
        if (Strings.isNullOrEmpty(token)) {
            return;
        }

        String url = config.getParametersValue("get-dossier-url").toString();
        String provinceCode = config.getParametersValue("province-code").toString();
        String fromDate = config.getParametersValue("from_date").toString() + " 00:00:00";
        LocalDateTime toDate = LocalDate.now().atTime(23,59,59);

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        HashMap<String, Object> body = new HashMap<>();
        body.put("MaTinh", provinceCode);
        body.put("TuNgay", convertDateTimeLocalToRemote(fromDate));
        body.put("DenNgay", convertDateTimeLocalToRemote(toDate));

        JSONObject JSONObject = new JSONObject(body);
        HttpEntity<?> request = new HttpEntity<>(JSONObject.toString(), headers);

        KTMMonreIntergrationResultDTO[] listData = new KTMMonreIntergrationResultDTO[0];
        try{
            ResponseEntity<String> result = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
            Gson g = new Gson();
            listData = g.fromJson(result.getBody(), KTMMonreIntergrationResultDTO[].class);
        } catch (Exception e){
            writeLog("syncDossierFromMonre:GetDataAGG", false, url, "POST", JSONObject.toString(), null, null, "Không lấy được danh sách hồ sơ từ Bộ TNMT : " + e.getMessage(), null);
        }

        if(listData != null && listData.length > 0){
            convertDossierFromMonreToIgate(listData, config);
        } else {
            writeLog("syncDossierFromMonre:GetDataAGG", false, url, "POST", JSONObject.toString(), null, null, "Danh sách hồ sơ từ Bộ TNMT null hoặc rỗng", null);
        }
    }
    public void convertDossierFromMonreToIgate(KTMMonreIntergrationResultDTO[] input, IntegratedConfigurationDto config){
        for(int i = 0; i < input.length; i++) {
            KTMMonreIntergrationResultDTO data = input[i];

            // Kiem tra ho so da ton tai
            String dossierUrl = microservice.padmanUri("/dossier/search?code=" + data.getMaHoSo()).toUriString();
            try {
                String dossierResponse = MicroserviceExchange.getNoAuth(this.getRestTemplate(), dossierUrl, String.class);
                JSONObject dossier = new JSONObject(dossierResponse);
                if (Integer.parseInt(dossier.get("numberOfElements").toString()) > 0) {
                    continue;
                }
            } catch (Exception e) {
                writeLog("syncDossierFromMonre:GetDataAGG", false, dossierUrl, "GET", null, null, null, "Kiểm tra hồ sơ tồn tại thất bại : " + e.getMessage(), "dossier : " + data.getMaHoSo());
                continue;
            }

            KTMDossierApply dossierApply = new KTMDossierApply();
            dossierApply.setCode(data.getMaHoSo());

            // Map don vi xu ly
            String procedureUrl = microservice.basepadUri("/procedure/--get-by-monre-code" + "?code=" + data.getMaTTHC() + "&agency-id=").toUriString();
//            String procedureUrl = "http://localhost:8069/procedure/--get-by-monre-code" + "?code=" + data.getMaTTHC() + "&agency-id=";
            if(data.getLoaiDoiTuong().equals("1")){
                try {
                    MappingDataDto mappingData = mappingDataService.getByDestNoDeployment(mapDistrictServiceId, data.getDonDangKy().getDiaChiThuaDat().getMaHuyen());
                    procedureUrl += mappingData.getSource().getId();
                } catch (Exception e){
                    writeLog("syncDossierFromMonre:MappingDataAGG", false, null, null, "LoaiDoiTuong = " + data.getLoaiDoiTuong() + " ; MaHuyen = " + data.getDonDangKy().getDiaChiThuaDat().getMaHuyen(), null, null, "Mapping đơn vị không thành công", "dossier : " + data.getMaHoSo());
                }
            } else {
                try {
                    MappingDataDto mappingData = mappingDataService.getByDestNoDeployment(mapProvinceServiceId, data.getDonDangKy().getDiaChiThuaDat().getMaTinh());
                    procedureUrl += mappingData.getSource().getId();
                } catch (Exception e){
                    writeLog("syncDossierFromMonre:MappingDataAGG", false, null, null, "LoaiDoiTuong = " + data.getLoaiDoiTuong() + " ; MaHuyen = " + data.getDonDangKy().getDiaChiThuaDat().getMaHuyen(), null, null, "Mapping đơn vị không thành công", "dossier : " + data.getMaHoSo());
                }
            }

            try {
                PostProcedureDto procedureFull = MicroserviceExchange.getNoAuth(this.getRestTemplate(), procedureUrl, PostProcedureDto.class);
                KTMDossierApply.ProcedureDto procedure = MicroserviceExchange.getNoAuth(this.getRestTemplate(), procedureUrl, KTMDossierApply.ProcedureDto.class);
                dossierApply.setProcedure(procedure);

//                Lay thong tin ngay nop
                String newAppliedDate = this.convertDateTimeRemoteToLocal(data.getNgayNopHoSo(), data.getMaTTHC());
                dossierApply.setAppliedDate(Strings.isNullOrEmpty(newAppliedDate) ? null : this.localSDF.parse(newAppliedDate));

                // Lay thong tin hinh thuc nhan KQ
                String hinhThucTra = data.getHinhThuc();
                KTMDossierApply.RecvKindDto recvKind = new KTMDossierApply.RecvKindDto();

                //
                if(hinhThucTra == null){
                    hinhThucTra = "2";
                }

                if (hinhThucTra.equals("1")) {
                    String receivingVNPostId = config.getParametersValue("receiving-vnpost-id").toString();
                    recvKind.setId(receivingVNPostId);
                    List<KTMDossierApply.NameDto> name = new ArrayList<>() {
                        {
                            add(new KTMDossierApply.NameDto((short) 228, "Nhận qua VNPost"));
                        }
                    };
                    recvKind.setName(name);
                } else {
                    String receivingDirectlyId = config.getParametersValue("receiving-directly-id").toString();
                    recvKind.setId(receivingDirectlyId);
                    List<KTMDossierApply.NameDto> name = new ArrayList<>() {
                        {
                            add(new KTMDossierApply.NameDto((short) 228, "Nhận trực tiếp"));
                        }
                    };
                    recvKind.setName(name);
                }
                dossierApply.setDossierReceivingKind(recvKind);

//              Lay thong tin agency
                AgencyFullyDto agencyResponse = procedureFull.getAgency().get(0);
                KTMDossierApply.AgencyDto newAgency = new KTMDossierApply.AgencyDto();
                KTMDossierApply.AgencyDto newParent = new KTMDossierApply.AgencyDto();
                newParent.setId(agencyResponse.getId().toString());
                newParent.setCode(agencyResponse.getCode());
                List<KTMDossierApply.NameDto> newName1 = new ArrayList<>();
                List<AgencyFullyDto.Name> nameResponse = agencyResponse.getName();
                for (int k = 0; k < nameResponse.size(); k++) {
                    AgencyFullyDto.Name name1 = nameResponse.get(k);
                    newName1.add(new KTMDossierApply.NameDto(name1.getLanguageId(), name1.getName()));
                }
                newParent.setName(newName1);
                newAgency.setParent(newParent);
                dossierApply.setAgency(newAgency);

//              Lay thong tin quy trinh
                String processUrl = "";
                KTMDossierApply.ProcessDto process = new KTMDossierApply.ProcessDto();
//                if(data.getMaTTHC().equals("1.003877")){
//                    String procedureProcessDefinition = config.getParametersValue("procedure-process-definition-id");
//                    processUrl = microservice.basepadUri("/procedure-process-definition/" + procedureProcessDefinition).toUriString();
//                    process = MicroserviceExchange.getNoAuth(this.getRestTemplate(), processUrl, KTMDossierApply.ProcessDto.class);
//                    dossierApply.setProcedureProcessDefinition(process);
//                } else {
                processUrl = microservice.basepadUri("/procedure-process-definition/--apply-online?procedure-id=" + procedure.getId()).toUriString();
                KTMDossierApply.ProcessDto[] processList = MicroserviceExchange.getNoAuth(this.getRestTemplate(), processUrl, KTMDossierApply.ProcessDto[].class);
                if (processList.length > 0) {
                    process = processList[0];
                    dossierApply.setProcedureProcessDefinition(process);
                } else {
                    dossierApply.setProcedureProcessDefinition(new KTMDossierApply.ProcessDto());
                }
//                }

                // Lay thong tin cap thu tuc
                PostProcedureDto.Tag procedureLevel = procedureFull.getLevel();
                KTMDossierApply.IdNameDto newProcedureLevel = new KTMDossierApply.IdNameDto();
                newProcedureLevel.setId(procedureLevel.getId().toString());

                List<Translate> procedureLevelName = procedureLevel.getName();
                List<KTMDossierApply.NameDto> newProcedureLevelName = new ArrayList<>();
                for (int n = 0; n < procedureLevelName.size(); n++) {
                    newProcedureLevelName.add(new KTMDossierApply.NameDto(procedureLevelName.get(n).getLanguageId(), procedureLevelName.get(n).getName()));
                }
                newProcedureLevel.setName(newProcedureLevelName);
                dossierApply.setProcedureLevel(newProcedureLevel);

//                Lay thong tin trang thai ho so
                KTMDossierApply.RecvKindDto dossierMenuTaskRemind = new KTMDossierApply.RecvKindDto();
                dossierMenuTaskRemind.setId("60f52e0d09cbf91d41f88834");
                List<KTMDossierApply.NameDto> namedossierMenuTaskRemind = new ArrayList<>() {
                    {
                        add(new KTMDossierApply.NameDto((short) 228, "Moi dang ky"));
                        add(new KTMDossierApply.NameDto((short) 46, "Just signed up"));

                    }
                };
                dossierMenuTaskRemind.setName(namedossierMenuTaskRemind);
                dossierApply.setDossierMenuTaskRemind(dossierMenuTaskRemind);

//                Lay thong tin eform
                LinkedHashMap processDefinition = (LinkedHashMap) process.getProcessDefinition();
                String applicantEformId = ((LinkedHashMap) processDefinition.get("applicantEForm")).get("id").toString();

                //get thông tin eForm Detail.
                String eFormDetail = ((LinkedHashMap) processDefinition.get("eForm")).get("id").toString();

                KTMDossierApply.ApplicantDto newApplicant = new KTMDossierApply.ApplicantDto();
                newApplicant.setEformId(applicantEformId);

                //Lay thong tin eForm
                KTMDossierApply.FormDto newEformDetail = new KTMDossierApply.FormDto();
                newEformDetail.setId(eFormDetail);
                dossierApply.setEForm(newEformDetail);

                // Lay thong tin applicant
                KTMDossierApply.ApplicanDatatDto applicantData = new KTMDossierApply.ApplicanDatatDto();
                KTMMonreIntergrationResultDTO.ChuHoSo chuHoSo = data.getChuHoSo();
                if (chuHoSo != null) {
                    applicantData.setFullname(chuHoSo.getTen());
                    applicantData.setOrganization(chuHoSo.getTen());
                    applicantData.setIdentityNumber(chuHoSo.getSoCMND());
                    applicantData.setEmail(chuHoSo.getEmail());
                    applicantData.setPhoneNumber(chuHoSo.getSoDienThoai());
                    applicantData.setAddress(chuHoSo.getDiaChi());
                    applicantData.setOwnerFullname(chuHoSo.getTen());
                }
                applicantData.setFax(data.getFax());
                applicantData.setGhiChu(data.getGhiChu());

                newApplicant.setData(applicantData);
                dossierApply.setApplicant(newApplicant);

                DossierExtend extend = new DossierExtend();
                extend.setIsSyncFromMonre(true);
                extend.setIsFinishedSyncToMonre(false);
                dossierApply.setExtendKTM(extend);

                // form file
                List<KTMDossierApply.AttachmentDto> lstAttachTaiLieuNop = new ArrayList<>();
                if (data.getTaiLieuNop() != null) {
                    String uploadUrl = microservice.filemanUri("/file/--multiple").toUriString();
                    MultiValueMap<String, Object> requestMap = new LinkedMultiValueMap<>();
                    for (KTMMonreIntergrationResultDTO.TaiLieuNop item : data.getTaiLieuNop()) {
                        String fileUrl = item.getDuongDanTaiTepTin();
                        String shortUrl = "file";
                        String url = microservice.filemanUri(shortUrl).toUriString() + "/--by-url?file-url=" + URLEncoder.encode(fileUrl, StandardCharsets.UTF_8);
                        try {
                            Id idFile = MicroserviceExchange.postJsonNoAuth(this.getRestTemplate(), url, null, Id.class);
                            KTMDossierApply.AttachmentDto att = new KTMDossierApply.AttachmentDto(
                                    idFile.getId().toHexString(), item.getTenTepDinhKem(), null, null
                            );
                            lstAttachTaiLieuNop.add(att);
                        } catch (Exception e){
                            writeLog("syncDossierFromMonre:InsertDataAGG", false, url, null, null, null, null, "Insert dữ liệu TPHS không thành công : " + e.getMessage(), "dossier : " + data.getMaHoSo());
                        }
                    }
                }

                // Lay thong tin phi / le phi
                String procostUrl = microservice.basepadUri("/procost/--for-online?procedure-id=" + procedure.getId()).toUriString();
                DossierFee.ProcostOnline[] procost = MicroserviceExchange.getNoAuth(this.getRestTemplate(), procostUrl, DossierFee.ProcostOnline[].class);
                ArrayList<KTMDossierApply.PostDossierFee> targetFees = new ArrayList<>();

                if(procost != null && procost.length > 0){
                    for (int j = 0; j < procost.length; j++){
                        KTMDossierApply.PostDossierFee targetFee = new KTMDossierApply.PostDossierFee();
                        DossierFee.ProcostOnline sourceFee = procost[j];
                        targetFee.setAmount(Double.valueOf(sourceFee.getCost()));
                        targetFee.setQuantity(sourceFee.getQuantity());
                        targetFee.setPaid(0.0);

                        // type
                        KTMDossierApply.Procost tempProcost = new KTMDossierApply.Procost();
                        KTMDossierApply.Type targetType  = new KTMDossierApply.Type();
                        DossierFee.ProcostTypeSimple sourceType = sourceFee.getType();
                        targetType.setId(sourceType.getId());
                        targetType.setType(sourceType.getType());
                        targetType.setName(sourceType.getName());
                        targetType.setQuantityEditable(sourceType.getQuantityEditable());
                        tempProcost.setType(targetType);
                        tempProcost.setId(new ObjectId(sourceFee.getId()));
                        tempProcost.setDescription(sourceType.getName());


                        targetFee.setProcost(tempProcost);
                        targetFees.add(targetFee);
                    }
                }

                dossierApply.setDossierFee(targetFees);

                // Luu ho so
                String newDossierUrl = microservice.padmanUri("/dossier/--apply-online").toUriString();
//                String newDossierUrl = "http://localhost:8081/dossier/--apply-online";
                IdDto dossier = null;
                try {
                    dossier = MicroserviceExchange.postJsonNoAuth(this.getRestTemplate(), newDossierUrl, dossierApply, IdDto.class);
                    writeLog("syncDossierFromMonre:InsertDataAGG", true, newDossierUrl, "POST", new JSONObject(dossierApply).toString(), null, null, "Insert dữ liệu hồ sơ thành công : " + data.getMaHoSo(), "dossier : " + data.getMaHoSo());
                } catch (Exception e) {
                    writeLog("syncDossierFromMonre:InsertDataAGG", false, newDossierUrl, "POST", new JSONObject(dossierApply).toString(), null, null, "Insert dữ liệu hồ sơ không thành công : " + e.getMessage(), "dossier : " + data.getMaHoSo());
                }

                // TPHS
                String newDossierFormFileURL = "";
                ArrayList<DossierFormFileDto> listDossierFormFileDto = new ArrayList<>();
                try {
                    listDossierFormFileDto = new ArrayList<>();
                    if (lstAttachTaiLieuNop.size() > 0) {
                        DossierFormFileDto dossierFormFile = this.addTaiLieuNop(dossier.getId().toHexString(), dossierApply.getProcedureProcessDefinition().getId(), lstAttachTaiLieuNop, config);
                        listDossierFormFileDto.add(dossierFormFile);
                    }
                    if (listDossierFormFileDto.size() > 0) {
                        newDossierFormFileURL = microservice.padmanUri("/dossier-form-file").toUriString() + "/--by-dossier?dossier-id=" + dossier.getId().toHexString();
                        AffectedRowsDto newDossierFormFile = MicroserviceExchange.putJsonNoAuth(this.getRestTemplate(), newDossierFormFileURL, listDossierFormFileDto, AffectedRowsDto.class);
                        if(newDossierFormFile.getAffectedRows() > 0){
                            writeLog("syncDossierFromMonre:InsertDataAGG", true, newDossierFormFileURL, "PUT", null, null, null, "Insert dữ liệu TPHS thành công : " + data.getMaHoSo(), "dossier : " + data.getMaHoSo());
                        } else {
                            writeLog("syncDossierFromMonre:InsertDataAGG", false, newDossierFormFileURL, "PUT", listDossierFormFileDto.toString(), null, null, "Insert dữ liệu TPHS không thành công", "dossier : " + data.getMaHoSo());
                        }
                    }
                } catch (Exception e) {
                    writeLog("syncDossierFromMonre:InsertDataAGG", false, newDossierFormFileURL, "PUT", listDossierFormFileDto.toString(), null, null, "Insert dữ liệu TPHS không thành công : " + e.getMessage(), "dossier : " + data.getMaHoSo());
                }
            } catch (Exception e){
                writeLog("syncDossierFromMonre:ConvertDataAGG", false, null, null, null, null, null, "Convert dữ liệu hồ sơ không thành công : " + e.getMessage(), "dossier : " + data.getMaHoSo());
            }
        }
    }

    //    @Scheduled(cron = "0 0/2 * * * *", zone = "Asia/Ho_Chi_Minh")
    @Scheduled(cron = "0 0 2 * * *", zone = "Asia/Ho_Chi_Minh")
    ////@SchedulerLock(name = "syncDossierToMonreAgg", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void syncDossierToMonre() {
        // Lay config
        IntegratedConfigurationDto config = configurationService.searchByServiceId(this.serviceId);
        if (Objects.isNull(config)) {
            writeLog("syncDossierToMonre:Config", false, null, null, "service id : " + this.serviceId, null, null, "Không lấy được config", null);
            return;
        }

        // Kiem tra bien dong bo day ho so
        Boolean enableSendDossier;
        try {
            enableSendDossier = config.getParametersValue("enable-get-dossier");

        } catch (Exception e){
            enableSendDossier = false;
        }

        if(!enableSendDossier){
            writeLog("syncDossierToMonre:CheckEnableSendDossierAGG", false, null, null, null, null, null, "Biến đồng bộ đẩy hồ sơ đang tắt", "enable-send-dossier");
            return;
        }

        // Lay token
        String token = getToken(config);
        if (Strings.isNullOrEmpty(token)) {
            return;
        }

        // Lay thong tin ma TTHC
        String[] procedureList;
        String getProcedureListUrl = "";
        try {
            String connectType = config.getParametersValue("connect-type").toString();
            String integratedType = config.getParametersValue("integrated-type").toString();
            getProcedureListUrl = microservice.basepadUri("/procedure/--find-by-connectType-and-integratedType-ktm?connect-type=" + connectType + "&integrated-type=" + integratedType).toUriString();
//            getProcedureListUrl = "http://localhost:8069/procedure/--find-by-connectType-and-integratedType-ktm?connect-type=" + connectType + "&integrated-type=" + integratedType;
            procedureList = MicroserviceExchange.getNoAuth(this.getRestTemplate(), getProcedureListUrl, String[].class);
        } catch (Exception e) {
            writeLog("syncDossierToMonre:GetDataAGG", false, getProcedureListUrl, "GET", null, null, null, "Không lấy được danh sách TTHC cần đồng bộ Bộ TNMT : " + e.getMessage(), null);
            return;
        }

        //Lay ho so can dong bo
        List<Dossier> dossierList;
        String getDossierListUrl = "";
        try {
            if (procedureList.length > 0) {
                String getDossierListSubUrl = "/dossier/--get-monre-dossier-ktm?procedure-code=";
                for (int i = 0; i < procedureList.length; i++) {
                    getDossierListSubUrl += procedureList[i] + ",";
                }

                if (procedureList.length > 0)
                    getDossierListSubUrl = getDossierListSubUrl.substring(0, getDossierListSubUrl.length() - 1);

                Boolean isSyncLocalDossier = config.getParametersValue("sync-local-dossier");

                if (isSyncLocalDossier) {
                    getDossierListSubUrl += "&is-from-monre=false";
                } else {
                    getDossierListSubUrl += "&is-from-monre=true";
                }

                String fromDate = config.getParametersValue("from_date").toString();
                getDossierListSubUrl += "&from-date=" + fromDate;

                getDossierListUrl = microservice.padmanUri(getDossierListSubUrl).toUriString();
//                getDossierListUrl = "http://localhost:8081" + getDossierListSubUrl;
                String dossierResponse = MicroserviceExchange.getNoAuth(this.getRestTemplate(), getDossierListUrl, String.class);
                ObjectMapper mapper = new ObjectMapper();
                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                dossierList = mapper.readValue(dossierResponse, new TypeReference<>() {
                });
            } else {
                writeLog("syncDossierToMonre:GetDataAGG", false, getProcedureListUrl, "GET", null, null, null, "Danh sách TTHC cần đồng bộ Bộ TNMT rỗng", null);
                return;
            }
        } catch (Exception e) {
            writeLog("syncDossierToMonre:GetDataAGG", false, getDossierListUrl, "GET", null, null, null, "Không lấy được danh sách hồ sơ cần đồng bộ Bộ TNMT : " + e.getMessage(), null);
            return;
        }

        // dong bo ho sơ len Bo TNMT
        if (dossierList != null && dossierList.size() > 0) {
            // Lay thong tin ho so
            POSTUpdateMonreDossierDTO body = new POSTUpdateMonreDossierDTO();
            ArrayList<UpdateMonreDossierDTO> bodyData = new ArrayList<>();
            for (int i = 0; i < dossierList.size(); i++) {
                Dossier sourceDossier = dossierList.get(i);
                UpdateMonreDossierDTO targetDossier = new UpdateMonreDossierDTO();

                // 1 MaHoSo
                targetDossier.setMaHoSo(sourceDossier.getCode());

                // 2 NgayTiepNhan
                targetDossier.setNgayTiepNhan(convertDateTimeLocalToRemote(sourceDossier.getAcceptedDate(), sourceDossier.getCode()));

                // 3 NgayHenTra
                targetDossier.setNgayHenTra(convertDateTimeLocalToRemote(sourceDossier.getAppointmentDate(), sourceDossier.getCode()));

                // 4 TrangThaiHoSo
                String status = String.valueOf(sourceDossier.getDossierStatus().getId());
                String taskStatus = sourceDossier.getDossierTaskStatus().getId();
                String targetStatus = mappingDossierStatus(status, taskStatus, config);
                targetDossier.setTrangThaiHoSo(targetStatus);

                // 5 NgayTra
                targetDossier.setNgayTra(convertDateTimeLocalToRemote(sourceDossier.getReturnedDate(), sourceDossier.getCode()));

                // 6 ThongTinTra (hard code)
                targetDossier.setThongTinTra("");

                // 7 HinhThuc
                String receivingDirectlyId = config.getParametersValue("receiving-directly-id").toString();
                String hinhThuc = "";
                if (sourceDossier.getDossierReceivingKind().getId().equals(receivingDirectlyId)) {
                    hinhThuc = "0";
                } else {
                    hinhThuc = "1";
                }
                targetDossier.setHinhThuc(hinhThuc);

                // 8 CompletedDate
                targetDossier.setNgayKetThucXuLy(convertDateTimeLocalToRemote(sourceDossier.getCompletedDate(), sourceDossier.getCode()));

                // 9 DonViXuLy
                String donViXuLy = "";
                for (int j = 0; j < sourceDossier.getAgency().getParent().getName().size(); j++) {
                    if (sourceDossier.getAgency().getParent().getName().get(j).getLanguageId() == 228) {
                        donViXuLy = !Strings.isNullOrEmpty(sourceDossier.getAgency().getParent().getName().get(j).getName()) ?
                                sourceDossier.getAgency().getParent().getName().get(j).getName() : "";
                        break;
                    }
                }
                targetDossier.setDonViXuLy(donViXuLy);

                // 10 GhiChu
                String ghiChu = "";
                if (sourceDossier.getApplicant() != null && sourceDossier.getApplicant().getData() != null) {
                    ghiChu = !Strings.isNullOrEmpty(sourceDossier.getApplicant().getData().getGhiChu()) ?
                            sourceDossier.getApplicant().getData().getGhiChu() : "";
                }
                targetDossier.setGhiChu(ghiChu);

                // 11 DanhSachLePhi
                List<UpdateMonreDossierDTO.DanhSachLePhi> targetFees = new ArrayList<>();
                String getDossierFeeListUrl = "";
                try {
                    getDossierFeeListUrl = microservice.padmanUri("dossier-fee?dossier-id=" + sourceDossier.getId()).toUriString();
                    DossierFee[] dossierFeeResponse = MicroserviceExchange.getNoAuth(this.getRestTemplate(), getDossierFeeListUrl, DossierFee[].class);
                    if (dossierFeeResponse != null && dossierFeeResponse.length > 0) {
                        for (int m = 0; m < dossierFeeResponse.length; m++) {
                            DossierFee sourceFee = dossierFeeResponse[m];
                            UpdateMonreDossierDTO.DanhSachLePhi targetFee = new UpdateMonreDossierDTO.DanhSachLePhi();
                            // 11.1 TenPhiLePhi
                            List<Dossier.TranslateName> name = sourceFee.getProcost().getType().getName();
                            String tenPhiLePhi = "";
                            for (int n = 0; n < name.size(); n++) {
                                if (name.get(n).getLanguageId() == 228) {
                                    tenPhiLePhi = !Strings.isNullOrEmpty(name.get(n).getName()) ? name.get(n).getName() : "";
                                    break;
                                }
                            }
                            targetFee.setTenPhiLePhi(tenPhiLePhi);

                            // 11.2 MaPhiLePhi (hard code)
                            String maPhiLePhi = !Strings.isNullOrEmpty(sourceFee.getProcost().getId().toString()) ? sourceFee.getProcost().getId().toString() : "";
                            targetFee.setMaPhiLePhi(maPhiLePhi);
//                            targetFee.setMaPhiLePhi("");

                            // 11.3 HinhThucThu (hard code)
                            targetFee.setHinhThucThu("1");

                            // 11.4 Gia
                            long gia;
                            try {
                                gia = (long)(sourceFee.getAmount() * (long)sourceFee.getQuantity());
                            } catch (Exception e) {
                                gia = 0;
                            }
                            targetFee.setGia(gia);

                            // 11.5 LoaiPhiLePhi
                            String loaiPhiLePhi;
                            try {
                                loaiPhiLePhi = String.valueOf(sourceFee.getProcost().getType().getType());
                                loaiPhiLePhi = loaiPhiLePhi.equals("0") ? "2" : "1";
                            } catch (Exception e){
                                loaiPhiLePhi = "";
                            }
                            targetFee.setLoaiPhiLePhi(loaiPhiLePhi);
//                            targetFee.setLoaiPhiLePhi("1");

                            targetFees.add(targetFee);
                        }
                    }
                } catch (Exception e) {
                    writeLog("syncDossierToMonre:GetDataAGG", false, getDossierFeeListUrl, "GET", null, null, null, "Không lấy được danh sách phí/lệ phí : " + e.getMessage(), "dossier : " + sourceDossier.getCode());
                }
                targetDossier.setDanhSachLePhi(targetFees);

                // 12 DanhSachTepDinhKemKhac
                Dossier dossierDetailResponse = new Dossier();
//                String getDossierDetailUrl = "";
//                try {
//                    getDossierDetailUrl = microservice.padmanUri("/dossier/" + sourceDossier.getId() + "/--online").toUriString();
//                    dossierDetailResponse = MicroserviceExchange.getNoAuth(this.getRestTemplate(), getDossierDetailUrl, Dossier.class);
//                } catch (Exception e) {
//                    writeLog("syncDossierToMonre:GetData", false, getDossierDetailUrl, "GET", null, null, null, "Không lấy được thông tin chi tiết hồ sơ : " + e.getMessage(), "dossier : " + sourceDossier.getCode());
//                }
//
                List<UpdateMonreDossierDTO.DanhSachTepDinhKemKhac> targetFormFiles = new ArrayList<>();
//                if (dossierDetailResponse != null && dossierDetailResponse.getDossierFormFile() != null && dossierDetailResponse.getDossierFormFile().size() > 0) {
//                    List<Dossier.DossierFormFile> sourceFormFiles = dossierDetailResponse.getDossierFormFile();
//                    for (int m = 0; m < sourceFormFiles.size(); m++) {
//                        Dossier.DossierFormFile sourceFormFile = sourceFormFiles.get(m);
//                        UpdateMonreDossierDTO.DanhSachTepDinhKemKhac targetFormFile = new UpdateMonreDossierDTO.DanhSachTepDinhKemKhac();
//                        // 12.1 TenGiayTo
//                        String tenGiayTo = !Strings.isNullOrEmpty(sourceFormFile.getFormName()) ? sourceFormFile.getFormName() : "";
//                        targetFormFile.setTenGiayTo(tenGiayTo);
//                        // 12.2 SoLuong
//                        targetFormFile.setSoLuong(sourceFormFile.getRequirement().getQuantity());
//                        // 12.3 LoaiGiayTo (hard code)
//                        targetFormFile.setLoaiGiayTo(1);
//                        targetFormFiles.add(targetFormFile);
//                    }
//                }
                targetDossier.setDanhSachTepDinhKemKhac(targetFormFiles);

                // 13 DanhSachHoSoBoSung (hard code)
                targetDossier.setDanhSachHoSoBoSung(new ArrayList<>());

                // 14 DanhSachGiayToKetQua
                List<UpdateMonreDossierDTO.DanhSachGiayToKetQua> targetAttachments = new ArrayList<>();
                if (dossierDetailResponse != null && dossierDetailResponse.getAttachment() != null && dossierDetailResponse.getAttachment().size() > 0) {
                    List<Dossier.AttachmentDto> sourceAttachments = dossierDetailResponse.getAttachment();
                    for (int m = 0; m < sourceAttachments.size(); m++) {
                        Dossier.AttachmentDto sourceAttachment = sourceAttachments.get(m);
                        UpdateMonreDossierDTO.DanhSachGiayToKetQua targetAttachment = new UpdateMonreDossierDTO.DanhSachGiayToKetQua();
                        // 14.1 TenGiayTo
                        String tenGiayTo = !Strings.isNullOrEmpty(sourceAttachment.getFilename()) ? sourceAttachment.getFilename() : "";
                        targetAttachment.setTenGiayTo(tenGiayTo);
                        // 14.3 GiayToId
                        String giayToId = !Strings.isNullOrEmpty(sourceAttachment.getId()) ? sourceAttachment.getId() : "";
                        targetAttachment.setGiayToId(giayToId);
                        // 14.4 DuongDanTepTinKetQua
                        String duongDanTepTinKetQua = !Strings.isNullOrEmpty(sourceAttachment.getId()) ?
                                gatewayUrl + "/fi/wopi/files/" + sourceAttachment.getId() + "/contents" : "";
                        targetAttachment.setDuongDanTepTinKetQua(duongDanTepTinKetQua);
                        targetAttachments.add(targetAttachment);
                    }
                }
                targetDossier.setDanhSachGiayToKetQua(targetAttachments);

                //15 QuaTrinhXuLy
                ArrayList<Dossier.DossierTask> sourceTasks = sourceDossier.getTask();
                List<UpdateMonreDossierDTO.QuaTrinhXuLy> targetTasks = new ArrayList<>();
                if (sourceTasks != null) {
                    for (int k = 0; k < sourceTasks.size(); k++) {
                        Dossier.DossierTask sourceTask = sourceTasks.get(k);
                        UpdateMonreDossierDTO.QuaTrinhXuLy targetTask = new UpdateMonreDossierDTO.QuaTrinhXuLy();
                        // 15.1 MaHoSo
                        targetTask.setMaHoSo(sourceDossier.getCode());
                        // 15.2 NguoiXuLy
                        String nguoiXuLy = !Strings.isNullOrEmpty(sourceTask.getAssignee().getFullname()) ? sourceTask.getAssignee().getFullname() : "";
                        targetTask.setNguoiXuLy(nguoiXuLy);
                        // 15.3 ChucDanh
                        String chucDanh = "";
                        if (sourceTask.getCandidatePosition() != null) {
                            try {
                                for (int m = 0; m < sourceTask.getCandidatePosition().getName().size(); m++) {
                                    if (sourceTask.getCandidatePosition().getName().get(m).getLanguageId() == 228) {
                                        chucDanh = !Strings.isNullOrEmpty(sourceTask.getCandidatePosition().getName().get(m).getName()) ? sourceTask.getCandidatePosition().getName().get(m).getName() : "";
                                        break;
                                    }
                                }
                            } catch(Exception e) {
                                writeLog("syncDossierToMonre:GetData", false, "", "GET", null, null, null, "Không lấy được thông tin chức danh : " + e.getMessage(), "dossier : " + sourceDossier.getCode());
                            }
                        }
                        targetTask.setChucDanh(chucDanh);
                        // 15.4 ThoiDiemXuLy
                        targetTask.setThoiDiemXuLy(convertDateTimeLocalToRemote(sourceTask.getCompletedDate(), sourceDossier.getCode()));
                        // 15.5 PhongBanXuLy
                        if (sourceTask.getCandidateGroup() != null && sourceTask.getCandidateGroup().size() > 0) {
                            for (int m = 0; m < sourceTask.getCandidateGroup().get(0).getName().size(); m++) {
                                if (sourceTask.getCandidateGroup().get(0).getName().get(m).getLanguageId() == 228) {
                                    String phongBanXuLy = !Strings.isNullOrEmpty(sourceTask.getCandidateGroup().get(0).getName().get(m).getName()) ?
                                            sourceTask.getCandidateGroup().get(0).getName().get(m).getName() : "";
                                    targetTask.setPhongBanXuLy(phongBanXuLy);
                                }
                            }
                        }
                        // 15.6 NoiDungXuLy (hard code)
                        targetTask.setNoiDungXuLy("");

                        // 15.7 TrangThai
                        if (sourceTask.getActivitiTask() != null && sourceTask.getActivitiTask().getStatus() != null && sourceTask.getActivitiTask().getStatus().equals("COMPLETED")) {
                            targetTask.setTrangThai("1");
                        } else {
                            targetTask.setTrangThai("0");
                        }

                        // 15.8 NgayBatDau
                        if (sourceTask.getAssignedDate() != null) {
                            targetTask.setNgayBatDau(convertDateTimeLocalToRemote(sourceTask.getAssignedDate(), sourceDossier.getCode()));
                        }

                        // 15.9 NgayKetThucTheoQuyDinh
                        if (sourceTask.getDueDate() != null) {
                            targetTask.setNgayKetThucTheoQuyDinh(convertDateTimeLocalToRemote(sourceTask.getDueDate(), sourceDossier.getCode()));
                        }

                        targetTasks.add(targetTask);
                    }
                }
                targetDossier.setQuaTrinhXuLy(targetTasks);
                bodyData.add(targetDossier);
            }
            body.setData(bodyData);

            // Day ho so len Bo TNMT
            String monreUrl = "";
            try {
                monreUrl = config.getParametersValue("push-dossier-url");
                String token2 = getToken(config);

                HttpHeaders headers = new HttpHeaders();
                headers.setBearerAuth(token2);
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.setAccept(Collections.singletonList(MediaType.ALL));

                String provinceCode = config.getParametersValue("province-code");
                body.setMaTinh(provinceCode);


                ResponseEntity<syncDossierToMonreResponseDTO> result = null;
                //Integer count = 0;

                HttpEntity<?> request = new HttpEntity<>(body, headers);

                result = restTemplate.exchange(monreUrl, HttpMethod.POST, request, syncDossierToMonreResponseDTO.class);

                if (result.getBody().getSuccess()) {
                    try {
                        //body = body1;
                        writeLog("syncDossierToMonre:SendDataAGG", true, monreUrl, "POST", new JSONObject(body).toString(), new JSONObject(result.getBody()).toString(), result.getStatusCode().toString(), null, "Gửi thông tin hồ sơ thành công");
                        List<String> listId = new ArrayList<>();
                        for (int i = 0; i < dossierList.size(); i++) {
                            Dossier dossier = dossierList.get(i);
                            if (dossier.getDossierStatus().getId() == 5) {
                                listId.add(dossier.getId());
                            }
                        }
                        // Update ho so da hoan thanh xu ly, khong dong bo lan sau
                        if (listId.size() > 0) {
                            String updateFinishDossierUrl = "";
                            try {
                                updateFinishDossierUrl = microservice.padmanUri("/dossier/--update-finish-dossier-monre-ktm").toUriString();
//                            updateFinishDossierUrl = "http://localhost:8081/dossier/--update-finish-dossier-monre-ktm";
                                long updatedossierResponse = MicroserviceExchange.postJsonNoAuth(this.getRestTemplate(), updateFinishDossierUrl, listId, long.class);
                                if (updatedossierResponse > 0) {
                                    writeLog("syncDossierToMonre:UpdateDataAGG", true, updateFinishDossierUrl, "POST", listId.toString(), String.valueOf(updatedossierResponse), null, null, "Update " + updatedossierResponse + "/" + listId.size() + " hồ sơ thành công");
                                } else {
                                    writeLog("syncDossierToMonre:UpdateDataAGG", false, updateFinishDossierUrl, "POST", listId.toString(), String.valueOf(updatedossierResponse), null, "Update hồ sơ không thành công", "Tổng : " + listId.size() + " hồ sơ");
                                }
                            } catch (Exception e) {
                                writeLog("syncDossierToMonre:UpdateDataAGG", false, updateFinishDossierUrl, "POST", listId.toString(), null, null, "Update hồ sơ không thành công : " + e.getMessage(), "Tổng : " + listId.size() + " hồ sơ");
                            }
                        }
                    } catch (Exception e) {
                        writeLog("syncDossierToMonre:SendDataAGG", false, monreUrl, "POST", new JSONObject(body).toString(), null, null, "Gửi thông tin hồ sơ không thành công : " + e.getMessage(), null);
                    }
                } else {
                    writeLog("syncDossierToMonre:SendDataAGG", false, monreUrl, "POST", new JSONObject(body).toString(), new JSONObject(result.getBody()).toString(), result.getStatusCode().toString(), "Gửi thông tin hồ sơ không thành công", null);
                }
            } catch (Exception e) {
                writeLog("syncDossierToMonre:SendDataAGG", false, monreUrl, "POST", new JSONObject(body).toString(), null, null, "Gửi thông tin hồ sơ không thành công : " + e.getMessage(), null);
            }
        } else {
            writeLog("syncDossierToMonre:GetDataAGG", false, getDossierListUrl, "GET", null, null, null, "Danh sách hồ sơ cần đồng bộ Bộ TNMT rỗng", null);
        }
    }

    private String convertDateTimeLocalToRemote(String _localDate){
        String remoteDate = null;
        try{
            Date localDate = integratedSDF.parse(_localDate);
            remoteDate = remoteSDF.format(localDate);
        } catch (Exception e){
            writeLog("convertDateTimeLocalToRemote:ConvertDataAGG", false, null, null, "String _localDate : " + _localDate, null, null, "Convert ngày không thành công", null);
        }
        return remoteDate;
    }

    private String convertDateTimeLocalToRemote(LocalDateTime localDate){
        String remoteDate = null;
        try{
            Date out = Date.from(localDate.atZone(ZoneId.systemDefault()).toInstant());
            remoteDate = remoteSDF.format(out);
        } catch (Exception e){
            writeLog("convertDateTimeLocalToRemote:ConvertDataAGG", false, null, null, "LocalDateTime localDate : " + localDate, null, null, "Convert ngày không thành công", null);
        }
        return remoteDate;
    }

    private String convertDateTimeLocalToRemote(Date localDate, String dossierCode){
        if( localDate == null) return "";
        String remoteDateTime = "";

        if(localDate != null){
            try {
                remoteDateTime = remoteSDF.format(localDate);
            } catch (Exception e) {
                writeLog("convertDateTimeLocalToRemote:ConvertDataAGG", false, null, null, "dossier code : " + dossierCode +" ; localDate : " + localDate, null, null, "Convert ngày không thành công", null);
            }
        }
        return remoteDateTime;
    }

    private String convertDateTimeRemoteToLocal(String _remoteDate, String dossierCode){
        String localDate = null;
        try{
            Date remoteDate = remoteSDF.parse(_remoteDate);
            localDate = localSDF.format(remoteDate);
        } catch (Exception e){
            writeLog("convertDateTimeRemoteToLocal:ConvertDataAGG", false, null, null, "dossier code : " + dossierCode +" ; remoteDate : " + _remoteDate, null, null, "Convert ngày không thành công", null);
        }
        return localDate;
    }

    public String mappingDossierStatus(String status, String taskStatus, IntegratedConfigurationDto config){
        String statusMapping = "";
        try {
            String dossierReceivedStatusId = config.getParametersValue("dossier-received-status-id").toString();
            if(taskStatus.equals(dossierReceivedStatusId)){
                return "2";
            }

            String dossierRefusedStatusId = config.getParametersValue("dossier-refused-status-id").toString();
            if(taskStatus.equals(dossierRefusedStatusId)){
                return "3";
            }

            String financialObligationId = config.getParametersValue("financial-obligation-id").toString();
            if(taskStatus.equals(financialObligationId)){
                return "6";
            }

            statusMapping = mappingDataService.getBySourceNoDeployment(mapDossierStatusServiceId, status).getDest().getId();
        } catch (Exception e){
            writeLog("mappingDossierStatus:MappingDataAGG", false, null, null, "status id = " + status + " ; taskStatus = " + taskStatus, null, null, "Mapping trạng thái hồ sơ không thành công", null);
        }

        return statusMapping;
    }

    public DossierFormFileDto addTaiLieuNop(String dossierId, String processId ,List<KTMDossierApply.AttachmentDto> lstAttach, IntegratedConfigurationDto config){
        DossierFormFileDto dossierFormFile = new DossierFormFileDto();
        // set dossier
        DossierFormFileDto.Id dossier = new DossierFormFileDto.Id();
        dossier.setId(dossierId);
        dossierFormFile.setDossier(dossier);
        // set procedureForm
        ProcedureForm procedureForm = new ProcedureForm();
        String procedureFormValue = config.getParametersValue("procedure-form");
        procedureForm.setId(procedureFormValue);
        String textFormName = new String("T\u00e0i li\u1ec7u n\u1ed9p t\u1eeb B\u1ED9 TNMT".getBytes(StandardCharsets.UTF_8),
                Charset.forName("UTF-8"));
        procedureForm.setName(textFormName);
        dossierFormFile.setProcedureForm(procedureForm);
        // set case
        DossierFormFileDto.Id caze = new DossierFormFileDto.Id();
        caze.setId(processId);
        dossierFormFile.setCaze(caze);

        dossierFormFile.setOrder(1);
        dossierFormFile.setQuantity(1);

        // set detail
        String textDetail = new String("B\u1ea3n sao".getBytes(StandardCharsets.UTF_8),
                Charset.forName("UTF-8"));
        String ProcedureFormDetailValue = config.getParametersValue("procedure-form-detail");
        DossierFormFileDto.ProcedureFormDetail detail = new DossierFormFileDto.ProcedureFormDetail(
                new  DossierFormFileDto.ProcedureFormDetailType(ProcedureFormDetailValue,textDetail), 1
        );
        dossierFormFile.setDetail(detail);
        dossierFormFile.setFile((ArrayList) lstAttach);

        return dossierFormFile;
    };


    private RestTemplate getRestTemplate(){
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(this.tokenUrl + "/protocol/openid-connect/token");
        details.setClientId(this.clientId);
        details.setClientSecret(this.clientSecret);
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }

    private void writeLog(String key, Boolean isSuccessful, String url, String method, String body, String response, String statusHTTP, String error, String note){
        MonreIntegrationLogs log = new MonreIntegrationLogs();
//        ObjectId deploymentId = Context.getDeploymentId();
//        log.setDeploymentId(deploymentId);
        if(!Strings.isNullOrEmpty(key)){
            log.setKey(key);
        }
        log.setIsSuccessful(isSuccessful);
        if(!Strings.isNullOrEmpty(method)){
            log.setMethod(method);
        }
        if(!Strings.isNullOrEmpty(url)){
            log.setUrl(url);
        }
        if(!Strings.isNullOrEmpty(body)){
            log.setBody(body);
        }
        if(!Strings.isNullOrEmpty(response)){
            log.setResponse(response);
        }
        if(!Strings.isNullOrEmpty(statusHTTP)){
            log.setStatusHTTP(statusHTTP);
        }
        if(!Strings.isNullOrEmpty(error)){
            log.setError(error);
        }
        if(!Strings.isNullOrEmpty(note)){
            log.setNote(note);
        }
        log.setCreatedAt(new Date());
        mongoTemplate.insert(log);
    }
}
