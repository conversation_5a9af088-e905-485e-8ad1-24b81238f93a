package vn.vnpt.digo.adapter.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.dto.IntegratedSidewalkRequestDto;
import vn.vnpt.digo.adapter.dto.IntegratedSidewalkResponseDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;

import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> le<PERSON><PERSON><PERSON>
 * @since : 13/03/2024
 * @description : Tí<PERSON> hợp thu phí lòng đường vỉa hè địa bàn TP.HCM
 */
@Service
public class IntegratedSidewalkService {

    @Value("${digo.upload-by-url-whitelist}")
    private String uploadWhitelist;

    @Autowired
    private RestTemplate restTemplate;

    Logger logger = LoggerFactory.getLogger(IntegratedSidewalkService.class);

    public IntegratedSidewalkResponseDto getSidewalkFee (IntegratedSidewalkRequestDto requestDto) {
        IntegratedSidewalkResponseDto res = new IntegratedSidewalkResponseDto();
        try {
            SimpleDateFormat dt = new SimpleDateFormat("yyyy-MM-dd");

            List<String> whitelist = new ArrayList<>();

            try {
                whitelist = new ArrayList<String>(Arrays.asList(this.uploadWhitelist.split(";")));
                whitelist.removeAll(Arrays.asList("", null));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
            if (whitelist.size() > 0) {
                URI uri = URI.create(requestDto.getApiUrl());
                String domain = uri.getHost();
                if (domain.toLowerCase().startsWith("www.")) {
                    domain = domain.replaceFirst("^www\\.", "");
                }
                int count = 0;
                for (String url : whitelist) {
                    if (domain.equalsIgnoreCase(url)) {
                        count++;
                    }
                }
                if (count == 0) {
                    throw new DigoHttpException(11415, new String[]{}, HttpServletResponse.SC_BAD_REQUEST);
                }
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set(requestDto.getApiKeyName(), requestDto.getApiKeyValue());

            String uri = requestDto.getApiUrl() + "/{urnFee}";
            Map<String, String> urnFee = new HashMap<>();
            urnFee.put("urnFee", requestDto.getApiUrnGetFee());

            UriComponents builder = UriComponentsBuilder.fromHttpUrl(uri)
                    .queryParam("purpose", requestDto.getPurpose())
                    .queryParam("routeID", requestDto.getRouteID())
                    .queryParam("arceage", requestDto.getArceage())
                    .queryParam("useStart", dt.format(requestDto.getUseStart()))
                    .queryParam("useEnd", dt.format(requestDto.getUseEnd())).build();

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<IntegratedSidewalkResponseDto> response = restTemplate.exchange(builder.toUriString(), HttpMethod.GET, requestEntity,
                    IntegratedSidewalkResponseDto.class,urnFee);
            res = response.getBody();
        } catch (Exception e) {
            logger.info("IntegratedSidewalkService - getSidewalkFee - Exception: " + e.getMessage());
            res.setStatus(0);
            res.setMessage(e.getMessage());
            res.setFee(0.0);
        }
        return res;
    }

    public List<IntegratedSidewalkResponseDto.RouteIdName> getListRouteIdName (IntegratedSidewalkRequestDto.Route requestDto) {
        IntegratedSidewalkResponseDto.RouteResponseIdName res = new IntegratedSidewalkResponseDto.RouteResponseIdName();
        try {
            SimpleDateFormat dt = new SimpleDateFormat("yyyy-MM-dd");

            List<String> whitelist = new ArrayList<>();

            try {
                whitelist = new ArrayList<String>(Arrays.asList(this.uploadWhitelist.split(";")));
                whitelist.removeAll(Arrays.asList("", null));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
            if (whitelist.size() > 0) {
                URI uri = URI.create(requestDto.getApiUrl());
                String domain = uri.getHost();
                if (domain.toLowerCase().startsWith("www.")) {
                    domain = domain.replaceFirst("^www\\.", "");
                }
                int count = 0;
                for (String url : whitelist) {
                    if (domain.equalsIgnoreCase(url)) {
                        count++;
                    }
                }
                if (count == 0) {
                    throw new DigoHttpException(11415, new String[]{}, HttpServletResponse.SC_BAD_REQUEST);
                }
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set(requestDto.getApiKeyName(), requestDto.getApiKeyValue());

            String uri = requestDto.getApiUrl() + "/{urnRoute}"; 
            Map<String, String> urnRoute = new HashMap<>();
            urnRoute.put("urnRoute", requestDto.getApiUrnGetRoute());

            UriComponents builder = UriComponentsBuilder.fromHttpUrl(uri).build();

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<IntegratedSidewalkResponseDto.RouteResponseIdName> response = restTemplate.exchange(builder.toUriString(), HttpMethod.GET, requestEntity,
                    IntegratedSidewalkResponseDto.RouteResponseIdName.class,urnRoute);
            res = response.getBody();
        } catch (Exception e) {
            logger.info("IntegratedSidewalkService - getListRouteIdName - Exception: " + e.getMessage());
            res = null;
        }
        return res != null ? res.getRoutes() : null;
    }
}