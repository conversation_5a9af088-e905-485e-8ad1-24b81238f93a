package vn.vnpt.digo.adapter.service;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import org.codehaus.jackson.JsonProcessingException;
import org.bson.types.ObjectId;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.ag_esb.vneid.AGGReceiveDossierFromVneidDto;
import vn.vnpt.digo.adapter.dto.ag_esb.vneid.AggReceiveDossierFromMcdtDto;
import vn.vnpt.digo.adapter.dto.ag_esb.vneid.AggReceiveDossierSendLltpDto;
import vn.vnpt.digo.adapter.dto.dbn.TokenResDto;
import vn.vnpt.digo.adapter.util.GsonUtils;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class AGGVneidLltpAgesbService
{
    @Value("${digo.agg.integrated-vneid-lltp-id-config}")
    private ObjectId integratedVneidLltpIdConfig;
    private final IntegratedConfigurationService integratedConfigurationService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper() ;
    private final XmlMapper xmlMapper =new XmlMapper();
    private static final DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final int MAX_DEPTH = 10;
    private static final String TOKEN_VNEID_URL = "https://vneid.teca.vn/api/idp/third-party/authenticate";

    AGGVneidLltpAgesbService (IntegratedConfigurationService integratedConfigurationService,
                              RestTemplate restTemplate)
    {
        this.integratedConfigurationService = integratedConfigurationService;
        this.restTemplate = restTemplate;
    }
    public ResponseEntity<Map<String, Object>> sendDossier(Object aggReceiveDossierDto) {
        try {
            Map<String, Object> result;
            if (aggReceiveDossierDto instanceof AGGReceiveDossierFromVneidDto) {
                AGGReceiveDossierFromVneidDto vneidDto =(AGGReceiveDossierFromVneidDto) aggReceiveDossierDto;
                AggReceiveDossierSendLltpDto lltpDto = mapToAggReceiveDossier(vneidDto);
                result = sendDossierFromMCDTThroughLLTP(lltpDto);
            } else if (aggReceiveDossierDto instanceof AggReceiveDossierFromMcdtDto) {
                AggReceiveDossierFromMcdtDto mcdtDto = (AggReceiveDossierFromMcdtDto) aggReceiveDossierDto;
                result = sendDossierFromMCDTThroughLLTP(mcdtDto);
            } else {
                return ResponseEntity.badRequest().body(Collections.singletonMap("error", "Invalid request body type"));
            }
            if (Boolean.TRUE.equals(result.get("isSuccess"))) {
                String jsonString = (String) result.get("result");
                Map<String, Object> responseMap;
                try {
                    responseMap = objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {});
                } catch (JsonProcessingException ex) {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                            Collections.singletonMap("error", "Invalid JSON format: " + jsonString)
                    );
                }
                return ResponseEntity.ok(responseMap);
            } else {
                Map<String, Object> errorResponse = objectMapper.readValue(
                        result.get("result").toString(),
                        new TypeReference<Map<String, Object>>() {}
                );
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
            }

        } catch (Exception e) {
            Map<String, Object> errorMap = Map.ofEntries(
                    Map.entry("statusDescription", "Đã có lỗi xảy ra từ hệ thống MCDT!"),
                    Map.entry("errorDescription", e.getMessage()),
                    Map.entry("errorCode", 500),
                    Map.entry("status", 0)
            );
            return ResponseEntity.ok().body(errorMap);
        }
    }
    private AggReceiveDossierSendLltpDto mapToAggReceiveDossier(AGGReceiveDossierFromVneidDto vneidDto)
    {
        try {
            AggReceiveDossierSendLltpDto aggReceiveDossierSendLltpDto = new AggReceiveDossierSendLltpDto();
            Optional.ofNullable(vneidDto.getMaHoSoMCDT()).ifPresent(aggReceiveDossierSendLltpDto::setMaHoSoMCDT);
            Optional.of(vneidDto.getNguonDangKy()).ifPresent(aggReceiveDossierSendLltpDto::setNguonDangKy);
            Optional.ofNullable(vneidDto.getNgayTiepNhan()).ifPresent(aggReceiveDossierSendLltpDto::setNgayTiepNhan);
            Optional.ofNullable(vneidDto.getNgayHenTra()).ifPresent(aggReceiveDossierSendLltpDto::setNgayHenTra);
            Optional.ofNullable(vneidDto.getTenNguonDangKy()).ifPresent(aggReceiveDossierSendLltpDto::setTenNguonDangKy);
            Optional.ofNullable(vneidDto.getThongTinKhac()).ifPresent(aggReceiveDossierSendLltpDto::setThongTinKhac);
            Optional.ofNullable(vneidDto.getTenToChuc()).ifPresent(aggReceiveDossierSendLltpDto::setTenToChuc);
            Optional.ofNullable(vneidDto.getTenNguoiNop()).ifPresent(aggReceiveDossierSendLltpDto::setTenNguoiNop);
            Optional.ofNullable(vneidDto.getDiaChiNguoiNop()).ifPresent(aggReceiveDossierSendLltpDto::setDiaChiNguoiNop);
            Optional.ofNullable(vneidDto.getSoCCCDNguoiNop()).ifPresent(aggReceiveDossierSendLltpDto::setSoCCCDNguoiNop);
            Optional.ofNullable(vneidDto.getSoDienThoaiNguoiNop()).ifPresent(aggReceiveDossierSendLltpDto::setSoDienThoaiNguoiNop);
            Optional.ofNullable(vneidDto.getTenDichVuCong()).ifPresent(aggReceiveDossierSendLltpDto::setTenDichVuCong);
            Optional.ofNullable(vneidDto.getTenTrangThaiHoSo()).ifPresent(aggReceiveDossierSendLltpDto::setTenTrangThaiHoSo);
            Optional.ofNullable(vneidDto.getMaTrangThaiHoSo()).ifPresent(aggReceiveDossierSendLltpDto::setMaTrangThaiHoSo);
            Optional.ofNullable(vneidDto.getPhongBanXuLy()).ifPresent(aggReceiveDossierSendLltpDto::setPhongBanXuLy);
            Optional.ofNullable(vneidDto.getChuyenVienXuLy()).ifPresent(aggReceiveDossierSendLltpDto::setChuyenVienXuLy);
            Optional.ofNullable(aggReceiveDossierSendLltpDto.getToKhai())
                    .orElseGet(() -> {
                        AggReceiveDossierSendLltpDto.NycNoiSinh nycNoiSinh = new AggReceiveDossierSendLltpDto.NycNoiSinh();
                        AggReceiveDossierSendLltpDto.NuqNoiSinh nuqNoiSinh = new AggReceiveDossierSendLltpDto.NuqNoiSinh();
                        AggReceiveDossierSendLltpDto.NuqThuongTruChiTiet nuqThuongTruChiTiet = new AggReceiveDossierSendLltpDto.NuqThuongTruChiTiet();
                        AggReceiveDossierSendLltpDto.NycThuongTru nycThuongTru = new AggReceiveDossierSendLltpDto.NycThuongTru();
                        AggReceiveDossierSendLltpDto.ToKhai toKhai = new AggReceiveDossierSendLltpDto.ToKhai();
                        aggReceiveDossierSendLltpDto.setToKhai(toKhai);
                        aggReceiveDossierSendLltpDto.getToKhai().setNycNoiSinh(nycNoiSinh);
                        aggReceiveDossierSendLltpDto.getToKhai().setNuqNoiSinh(nuqNoiSinh);
                        aggReceiveDossierSendLltpDto.getToKhai().setNuqThuongTruChiTiet(nuqThuongTruChiTiet);
                        aggReceiveDossierSendLltpDto.getToKhai().setNycThuongTru(nycThuongTru);
                        return toKhai;
                    });
            Optional.ofNullable(vneidDto.getToKhai()).ifPresent(toKhai -> {
                Optional.ofNullable(toKhai.getNycTenQuocTich()).ifPresent(nycTenQuocTich ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycTenQuocTich(nycTenQuocTich)
                );

                Optional.ofNullable(toKhai.getThongTinUyQuyen()).ifPresent(thongTinUyQuyen -> {
                    Optional.ofNullable(thongTinUyQuyen.getNuqDienThoai()).ifPresent(nuqDienThoai ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNuqDienThoai(nuqDienThoai)
                    );
                    Optional.ofNullable(thongTinUyQuyen.getNuqDanToc()).ifPresent(nuqDanToc ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNuqDanToc(nuqDanToc)
                    );
                    Optional.ofNullable(thongTinUyQuyen.getNuqQuanHe()).ifPresent(nuqQuanHe ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNyqQuanHe(nuqQuanHe.getTen())
                    );
                    Optional.of(thongTinUyQuyen.getNuqGioiTinh()).ifPresent(nuqGioiTinh ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNuqGioiTinh(String.valueOf(nuqGioiTinh))
                    );
                    Optional.of(thongTinUyQuyen.getNuqHoTen()).ifPresent(nuqHoTen ->
                            aggReceiveDossierSendLltpDto.getToKhai().setUyQuyenHoTen(nuqHoTen)
                    );
                });

                Optional.ofNullable(toKhai.getNycEmail()).ifPresent(nycEmail ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycEmail(nycEmail)
                );

                aggReceiveDossierSendLltpDto.getToKhai().setVoChongSoGiayTo("");

                Optional.ofNullable(toKhai.getNycHoTenCha()).ifPresent(nycHoTenCha ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycHoTenCha(nycHoTenCha)
                );

                Optional.of(toKhai.getNycLoaiGiayTo()).ifPresent(nycLoaiGiayTo ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycLoaiGiayTo(String.valueOf(nycLoaiGiayTo))
                );

                Optional.ofNullable(toKhai.getNycNoiCapGiayTo()).ifPresent(nycNoiCapGiayTo ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycNoiCapGiayTo(nycNoiCapGiayTo)
                );

                Optional.ofNullable(toKhai.getNycNgayCapGiayTo()).ifPresent(nycNgayCapGiayTo ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycNgayCapGiayTo(nycNgayCapGiayTo)
                );
                Optional.ofNullable(toKhai.getNycTenGoiKhac()).ifPresent(nycTenGoiKhac ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycTenGoiKhac((String) nycTenGoiKhac)
                );

                Optional.ofNullable(toKhai.getNycQuocTich()).ifPresent(nycQuocTich ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycQuocTich(nycQuocTich)
                );

                Optional.of(toKhai.getUyQuyen()).ifPresent(uyQuyen ->
                        aggReceiveDossierSendLltpDto.getToKhai().setUyQuyen(String.valueOf(uyQuyen))
                );

                Optional.ofNullable(toKhai.getThongTinUyQuyen()).ifPresent(thongTinUyQuyen -> {
                    Optional.ofNullable(thongTinUyQuyen.getNuqSoGiayTo()).ifPresent(nuqSoGiayTo ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNuqSoGiayTo(nuqSoGiayTo)
                    );
                    Optional.ofNullable(thongTinUyQuyen.getNuqTenGioiTinh()).ifPresent(nuqTenGioiTinh ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNuqTenGioiTinh(nuqTenGioiTinh)
                    );
                    Optional.ofNullable(thongTinUyQuyen.getNuqNgaySinh()).ifPresent(nuqNgaySinh ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNuqNgaySinh(nuqNgaySinh)
                    );
                });

                Optional.ofNullable(toKhai.getNycHoTenVoChong()).ifPresent(nycHoTenVoChong ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycHoTenVoChong((String) nycHoTenVoChong)
                );

                Optional.of(toKhai.getYeuCauCDNCV()).ifPresent(yeuCauCDNCV ->
                        aggReceiveDossierSendLltpDto.getToKhai().setYeuCauCDNCV(String.valueOf(yeuCauCDNCV))
                );

                Optional.ofNullable(toKhai.getTenMucDich()).ifPresent(tenMucDich ->
                        aggReceiveDossierSendLltpDto.getToKhai().setTenMucDich(tenMucDich)
                );

                Optional.ofNullable(toKhai.getNycSoGiayTo()).ifPresent(nycSoGiayTo ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycSoGiayTo(nycSoGiayTo)
                );

                Optional.ofNullable(toKhai.getNycTamTru()).ifPresent(nycTamTru -> {
                    Optional.ofNullable(nycTamTru.getTenQuanHuyen()).ifPresent(tenQuanHuyen ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycTamTru().setTenQuanHuyen((String) tenQuanHuyen)
                    );
                    Optional.ofNullable(nycTamTru.getChiTiet()).ifPresent(dcChiTiet ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycTamTru().setDcChiTiet((String) dcChiTiet)
                    );
                    Optional.ofNullable(nycTamTru.getMaQuanHuyen()).ifPresent(maQuanHuyen ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycTamTru().setMaQuanHuyen((String) maQuanHuyen)
                    );
                    Optional.ofNullable(nycTamTru.getTenTinhThanh()).ifPresent(tenTinhThanh ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycTamTru().setTenTinhThanh((String) tenTinhThanh)
                    );
                    Optional.ofNullable(nycTamTru.getMaPhuongXa()).ifPresent(maPhuongXa ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycTamTru().setMaPhuongXa((String) maPhuongXa)
                    );
                    Optional.ofNullable(nycTamTru.getMaTinhThanh()).ifPresent(maTinhThanh ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycTamTru().setMaTinhThanh((String) maTinhThanh)
                    );
                    Optional.ofNullable(nycTamTru.getTenPhuongXa()).ifPresent(tenPhuongXa ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycTamTru().setTenPhuongXa((String) tenPhuongXa)
                    );
                });
                Optional.ofNullable(toKhai.getNycNoiSinh()).ifPresent(nycNoiSinh -> {
                    Optional.ofNullable(nycNoiSinh.getTenQuanHuyen()).ifPresent(tenQuanHuyen ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycNoiSinh().setTenQuanHuyen((String) tenQuanHuyen)
                    );
                    Optional.ofNullable(nycNoiSinh.getChiTiet()).ifPresent(dcChiTiet ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycNoiSinh().setDcChiTiet((String) dcChiTiet)
                    );
                    Optional.ofNullable(nycNoiSinh.getMaQuanHuyen()).ifPresent(maQuanHuyen ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycNoiSinh().setMaQuanHuyen((String) maQuanHuyen)
                    );
                    Optional.ofNullable(nycNoiSinh.getTenTinhThanh()).ifPresent(tenTinhThanh ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycNoiSinh().setTenTinhThanh(tenTinhThanh)
                    );
                    Optional.ofNullable(nycNoiSinh.getMaPhuongXa()).ifPresent(maPhuongXa ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycNoiSinh().setMaPhuongXa((String) maPhuongXa)
                    );
                    Optional.ofNullable(nycNoiSinh.getMaTinhThanh()).ifPresent(maTinhThanh ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycNoiSinh().setMaTinhThanh(maTinhThanh)
                    );
                    Optional.ofNullable(nycNoiSinh.getTenPhuongXa()).ifPresent(tenPhuongXa ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycNoiSinh().setTenPhuongXa((String) tenPhuongXa)
                    );
                });

                Optional.ofNullable(toKhai.getVoChongNamSinh()).ifPresent(voChongNamSinh ->
                        aggReceiveDossierSendLltpDto.getToKhai().setVoChongNgaySinh((String) voChongNamSinh)
                );

                Optional.of(toKhai.getLoaiPhieu()).ifPresent(loaiPhieu ->
                        aggReceiveDossierSendLltpDto.getToKhai().setLoaiPhieu(String.valueOf(loaiPhieu))
                );
                Optional.ofNullable(toKhai.getNgayHenTra()).ifPresent(ngayHenTra ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNgayHenTra(ngayHenTra)
                );

                Optional.ofNullable(toKhai.getThongTinAnTich()).ifPresent(thongTinAnTich ->
                        aggReceiveDossierSendLltpDto.getToKhai().setThongTinAnTich(thongTinAnTich)
                );

                Optional.ofNullable(toKhai.getNycTenLoaiGiayTo()).ifPresent(nycTenLoaiGiayTo ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycTenLoaiGiayTo(nycTenLoaiGiayTo)
                );

                Optional.ofNullable(toKhai.getThongTinUyQuyen()).ifPresent(thongTinUyQuyen -> {

                    Optional.ofNullable(thongTinUyQuyen.getNuqNgayCapGiayTo()).ifPresent(nuqNgayCapGiayTo ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNuqNgayCapGiayTo(nuqNgayCapGiayTo)
                    );
                    Optional.ofNullable(thongTinUyQuyen.getNuqNoiOHienTai()).ifPresent(nuqNoiOHienTai -> {
                        Optional.ofNullable(nuqNoiOHienTai.getTenQuanHuyen()).ifPresent(tenQuanHuyen ->
                                aggReceiveDossierSendLltpDto.getToKhai().getNuqThuongTruChiTiet().setTenQuanHuyen(tenQuanHuyen)
                        );
                        Optional.ofNullable(nuqNoiOHienTai.getChiTiet()).ifPresent(dcChiTiet ->
                                aggReceiveDossierSendLltpDto.getToKhai().getNuqThuongTruChiTiet().setDcChiTiet(dcChiTiet)
                        );
                        Optional.ofNullable(nuqNoiOHienTai.getMaQuanHuyen()).ifPresent(maQuanHuyen ->
                                aggReceiveDossierSendLltpDto.getToKhai().getNuqThuongTruChiTiet().setMaQuanHuyen(maQuanHuyen)
                        );
                        Optional.ofNullable(nuqNoiOHienTai.getTenTinhThanh()).ifPresent(tenTinhThanh ->
                                aggReceiveDossierSendLltpDto.getToKhai().getNuqThuongTruChiTiet().setTenTinhThanh(tenTinhThanh)
                        );
                        Optional.ofNullable(nuqNoiOHienTai.getMaPhuongXa()).ifPresent(maPhuongXa ->
                                aggReceiveDossierSendLltpDto.getToKhai().getNuqThuongTruChiTiet().setMaPhuongXa(maPhuongXa)
                        );
                        Optional.ofNullable(nuqNoiOHienTai.getMaTinhThanh()).ifPresent(maTinhThanh ->
                                aggReceiveDossierSendLltpDto.getToKhai().getNuqThuongTruChiTiet().setMaTinhThanh(maTinhThanh)
                        );
                        Optional.ofNullable(nuqNoiOHienTai.getTenPhuongXa()).ifPresent(tenPhuongXa ->
                                aggReceiveDossierSendLltpDto.getToKhai().getNuqThuongTruChiTiet().setTenPhuongXa(tenPhuongXa)
                        );
                    });
                    Optional.ofNullable(thongTinUyQuyen.getNuqTenLoaiGiayTo()).ifPresent(nuqTenLoaiGiayTo ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNuqLoaiGiayto(nuqTenLoaiGiayTo)
                    );
                });

                Optional.of(toKhai.getMaMucDich()).ifPresent(maMucDich ->
                        aggReceiveDossierSendLltpDto.getToKhai().setMaMucDich(String.valueOf(maMucDich))
                );

                Optional.of(toKhai.getNycNoiSinhNuocNgoai()).ifPresent(nycNoiSinhNuocNgoai ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycNoiSinhNuocNgoai(String.valueOf(nycNoiSinhNuocNgoai))
                );

                Optional.ofNullable(toKhai.getMeNamSinh()).ifPresent(meNamSinh ->
                        aggReceiveDossierSendLltpDto.getToKhai().setMeNgaySinh(meNamSinh)
                );

                Optional.ofNullable(toKhai.getNycHoTenMe()).ifPresent(nycHoTenMe ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycHoTenMe(nycHoTenMe)
                );

                Optional.ofNullable(toKhai.getMucDich()).ifPresent(mucDich ->
                        aggReceiveDossierSendLltpDto.getToKhai().setMucDich(String.valueOf(mucDich))
                );

                Optional.ofNullable(toKhai.getNycDienThoai()).ifPresent(nycDienThoai ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycDienThoai(nycDienThoai)
                );

                Optional.of(toKhai.getNycGioiTinh()).ifPresent(nycGioiTinh ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycGioiTinh(String.valueOf(nycGioiTinh))
                );

                Optional.ofNullable(toKhai.getNycHoTen()).ifPresent(nycHoTen ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycHoTen(nycHoTen)
                );
                Optional.ofNullable(toKhai.getChaNamSinh()).ifPresent(chaNamSinh ->
                        aggReceiveDossierSendLltpDto.getToKhai().setChaNgaySinh(chaNamSinh)
                );

                Optional.ofNullable(toKhai.getThongTinUyQuyen()).ifPresent(thongTinUyQuyen -> {
                    Optional.ofNullable(thongTinUyQuyen.getNuqQuocTich()).ifPresent(nuqQuocTich ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNuqQuocTich(nuqQuocTich)
                    );
                    Optional.ofNullable(thongTinUyQuyen.getNuqEmail()).ifPresent(nuqEmail ->
                            aggReceiveDossierSendLltpDto.getToKhai().setNuqEmail(nuqEmail)
                    );
                });

                Optional.ofNullable(toKhai.getNycTenGioiTinh()).ifPresent(nycTenGioiTinh ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycTenGioiTinh(nycTenGioiTinh)
                );

                Optional.of(toKhai.getSoLuongCap()).ifPresent(soLuongCap ->
                        aggReceiveDossierSendLltpDto.getToKhai().setSoLuongCap(soLuongCap)
                );

                Optional.ofNullable(toKhai.getNycThuongTru()).ifPresent(nycThuongTru -> {
                    Optional.ofNullable(nycThuongTru.getTenQuanHuyen()).ifPresent(tenQuanHuyen ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycThuongTru().setTenQuanHuyen(tenQuanHuyen)
                    );
                    Optional.ofNullable(nycThuongTru.getChiTiet()).ifPresent(dcChiTiet ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycThuongTru().setDcChiTiet(dcChiTiet)
                    );
                    Optional.ofNullable(nycThuongTru.getMaQuanHuyen()).ifPresent(maQuanHuyen ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycThuongTru().setMaQuanHuyen(maQuanHuyen)
                    );
                    Optional.ofNullable(nycThuongTru.getTenTinhThanh()).ifPresent(tenTinhThanh ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycThuongTru().setTenTinhThanh(tenTinhThanh)
                    );
                    Optional.ofNullable(nycThuongTru.getMaPhuongXa()).ifPresent(maPhuongXa ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycThuongTru().setMaPhuongXa(maPhuongXa)
                    );
                    Optional.ofNullable(nycThuongTru.getMaTinhThanh()).ifPresent(maTinhThanh ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycThuongTru().setMaTinhThanh(maTinhThanh)
                    );
                    Optional.ofNullable(nycThuongTru.getTenPhuongXa()).ifPresent(tenPhuongXa ->
                            aggReceiveDossierSendLltpDto.getToKhai().getNycThuongTru().setTenPhuongXa(tenPhuongXa)
                    );
                });

                Optional.ofNullable(toKhai.getNycTenDanToc()).ifPresent(nycTenDanToc ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycTenDanToc(nycTenDanToc)
                );

                Optional.ofNullable(toKhai.getNycDanToc()).ifPresent(nycDanToc ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycDanToc(nycDanToc)
                );

                Optional.ofNullable(toKhai.getNycNgaySinh()).ifPresent(nycNgaySinh ->
                        aggReceiveDossierSendLltpDto.getToKhai().setNycNgaySinh(nycNgaySinh)
                );

            });
            Optional.ofNullable(vneidDto.getToKhai())
                    .map(AGGReceiveDossierFromVneidDto.ToKhai::getGiayToDinhKem)
                    .ifPresent(giayToDinhKemList -> {
                        ArrayList<AggReceiveDossierSendLltpDto.GiayToDinhKem> fileHoSoList = new ArrayList<>();
                        for (AGGReceiveDossierFromVneidDto.GiayToDinhKem source : giayToDinhKemList) {
                            AggReceiveDossierSendLltpDto.GiayToDinhKem target = new AggReceiveDossierSendLltpDto.GiayToDinhKem();
                            target.setTenLoaiFile(source.getTenLoaiFile());
                            target.setTenFile(source.getTen());
                            target.setNoiDungFile(source.getNoiDungFile());
                            fileHoSoList.add(target);
                        }
                        aggReceiveDossierSendLltpDto.getToKhai().setFileHoSo(fileHoSoList);
                    });

            return aggReceiveDossierSendLltpDto;
        }
        catch (Exception e) {
            throw new RuntimeException("Error mapping AGGReceiveDossierFromVneidDto: " + e.getMessage());
        }
    }

    private Map<String, Object> sendDossierFromMCDTThroughLLTP(Object aggReceiveDossierDto) throws Exception {
        try {
            Map<String, String> config = getConfigIntegrated();
            String registerUrl = config.get("register-url");
            TokenResDto token = getToken(config);
            String accessToken = Optional.ofNullable(token)
                    .map(TokenResDto::getAccessToken)
                    .orElseThrow(() -> new IllegalStateException("Access token is missing"));
            HttpURLConnection connection = configureXMLConnection(registerUrl, accessToken);
            String xmlPayload;
            if (aggReceiveDossierDto instanceof AggReceiveDossierSendLltpDto) {
                AggReceiveDossierSendLltpDto vneidDto = (AggReceiveDossierSendLltpDto) aggReceiveDossierDto;
                xmlPayload = formatXML(createXML(vneidDto));
            } else if (aggReceiveDossierDto instanceof AggReceiveDossierFromMcdtDto) {
                AggReceiveDossierFromMcdtDto mcdtDto = (AggReceiveDossierFromMcdtDto) aggReceiveDossierDto;
                xmlPayload = formatXML(createXML(mcdtDto));
            } else {
                throw new IllegalArgumentException("Unsupported DTO type: " + aggReceiveDossierDto.getClass().getName());
            }
            sendRequest(connection, xmlPayload);
            Map<String, String> responseContent = readResponse(connection);
            JsonNode resultResponse = convertXMLtoJSON(formatXML(responseContent.get("responseContent")));
            return handleResponse(responseContent.get("code"),resultResponse);
        } catch (Exception e) {
            throw new Exception("Error occurred during dossier processing: " + e.getMessage(), e);
        }
    }


    private Map<String, Object> handleResponse(String code, JsonNode result) throws IOException {
        Map<String, Object> response = new HashMap<>();
        int statusCode = Integer.parseInt(code);
        Optional<JsonNode> bodyNode = Optional.ofNullable(result).map(r -> r.path("Body"));
        boolean isSuccess = (statusCode >= 200 && statusCode < 300);

        String defaultErrorDescription = "Đã có lỗi xảy ra trong quá trình gửi thông tin!";
        String undefinedError = "Không xác định";
        Map<String, Object> defaultErrorMap = Map.ofEntries(
                Map.entry("statusDescription", defaultErrorDescription),
                Map.entry("errorDescription", undefinedError),
                Map.entry("errorCode", 500),
                Map.entry("status", 0)
        );

        if (isSuccess || bodyNode.map(b -> b.path("MessageResponse").has("MessageCode")).orElse(false)) {
            String content = bodyNode
                    .map(b -> b.path("MessageResponse").path("MessageCode").asText())
                    .orElseGet(() -> {
                        response.put("isSuccess", false);
                        try {
                            return objectMapper.writeValueAsString(defaultErrorMap);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
            response.putIfAbsent("isSuccess", isSuccess);
            response.put("result", content);
        } else {
            String faultCode = bodyNode
                    .map(b -> b.path("Fault").path("faultcode").asText(undefinedError))
                    .orElse(undefinedError);
            String faultString = bodyNode
                    .map(b -> b.path("Fault").path("faultstring").asText(undefinedError))
                    .orElse(undefinedError);
            String detail = bodyNode
                    .map(b -> b.path("Fault").path("detail").asText(undefinedError))
                    .orElse(undefinedError);

            String errorContent = String.format("Mã lỗi: %s, Tên lỗi: %s, Chi tiết lỗi: %s", faultCode, faultString, detail);
            Map<String, Object> errorMap = Map.ofEntries(
                    Map.entry("statusDescription", "Đã có lỗi xảy ra từ trục liên thông LGSP AG ESB!"),
                    Map.entry("errorDescription", errorContent),
                    Map.entry("errorCode", 500),
                    Map.entry("status", 0)
            );

            response.put("isSuccess", false);
            response.put("result", objectMapper.writeValueAsString(errorMap));
        }

        return response;
    }


    private JsonNode convertXMLtoJSON(String responseContentFormatter) throws IOException {
        return xmlMapper.readTree(responseContentFormatter.getBytes());
    }
    private String formatXML(String xml) throws Exception
    {
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        StreamSource source = new StreamSource(new StringReader(xml));
        StringWriter resultWriter = new StringWriter();
        StreamResult result = new StreamResult(resultWriter);
        transformer.transform(source, result);
        return resultWriter.toString();
    }
    private void sendRequest(HttpURLConnection connection, String jsonObject) throws IOException {
        try {
            connection.setConnectTimeout(30000); // Thời gian chờ kết nối
            connection.setReadTimeout(30000);    // Thời gian chờ đọc dữ liệu
            try (OutputStream outStream = connection.getOutputStream()) {
                outStream.write(jsonObject.getBytes());
            }
        } catch (SocketTimeoutException e) {
            throw new IOException("Request timed out after 30 seconds", e);
        }
    }

    private Map<String,String> readResponse(HttpURLConnection connection) throws IOException {
        Map<String,String> responseResult = new HashMap<>();
        int responseCode = connection.getResponseCode();
        InputStream inputStream;
        try {
            inputStream = (responseCode >= 200 && responseCode < 300) ? connection.getInputStream() : connection.getErrorStream();
        } catch (IOException e) {
            inputStream = connection.getErrorStream();
        }
        if (inputStream != null) {
            try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                String responseBody = bufferedReader.lines().collect(StringBuilder::new, StringBuilder::append, StringBuilder::append).toString();
                responseResult.put("code", Integer.toString(responseCode));
                responseResult.put("responseContent",responseBody);
                return responseResult;
            }
        } else {
            responseResult.put("code", Integer.toString(responseCode));
            responseResult.put("responseContent","No response received");
            return responseResult;
        }
    }
    private String createXML(Object aggReceiveDossierDto) throws Exception {
        String ngayTiepNhanFormatted;
        String ngayHenTraFormatted;
        Map<String, Object> duLieuChuyenNganhValueMapped;
        if (aggReceiveDossierDto instanceof AggReceiveDossierSendLltpDto) {
            AggReceiveDossierSendLltpDto vneidDto = (AggReceiveDossierSendLltpDto) aggReceiveDossierDto;
            ngayTiepNhanFormatted = formatDate(vneidDto.getNgayTiepNhan());
            ngayHenTraFormatted = formatDate(vneidDto.getNgayHenTra());
            duLieuChuyenNganhValueMapped = Map.ofEntries(
                    Map.entry("maTinh","89"),
                    Map.entry("tenTinh","An Giang"),
                    Map.entry("maHoSoMCDT", vneidDto.getMaHoSoMCDT()),
                    Map.entry("nguonDangKy","2"),
                    Map.entry("tenNguonDangKy","Hồ sơ đăng ký trên ứng dụng VNeID "),
                    Map.entry("ngayTiepNhan", vneidDto.getNgayTiepNhan()),
                    Map.entry("toKhai", vneidDto.getToKhai())
            );
        } else if (aggReceiveDossierDto instanceof AggReceiveDossierFromMcdtDto) {
            AggReceiveDossierFromMcdtDto mcdtDto = (AggReceiveDossierFromMcdtDto) aggReceiveDossierDto;
            Map<String, Object> toKhaiMapped = extractValuesOnly(((AggReceiveDossierFromMcdtDto) aggReceiveDossierDto).getToKhai());
            toKhaiMapped.put("fileHoSo",((AggReceiveDossierFromMcdtDto) aggReceiveDossierDto).getFileHoSo());
            ngayTiepNhanFormatted = formatDate(mcdtDto.getNgayTiepNhan());
            ngayHenTraFormatted = formatDate(mcdtDto.getNgayHenTra());
            duLieuChuyenNganhValueMapped = Map.ofEntries(
                    Map.entry("maTinh","89"),
                    Map.entry("tenTinh","An Giang"),
                    Map.entry("maHoSoMCDT", mcdtDto.getMaHoSoMCDT()),
                    Map.entry("nguonDangKy","2"),
                    Map.entry("tenNguonDangKy","Hồ sơ đăng ký trên hệ thống một cửa điện tử"),
                    Map.entry("ngayTiepNhan", mcdtDto.getNgayTiepNhan()),
                    Map.entry("toKhai", toKhaiMapped)
            );
        } else {
            throw new IllegalArgumentException("Unsupported DTO type: " + aggReceiveDossierDto.getClass().getName());
        }
        String duLieuChuyenNganhValue = objectMapper.writeValueAsString(duLieuChuyenNganhValueMapped);
        Document doc;
        if (aggReceiveDossierDto instanceof AggReceiveDossierSendLltpDto) {
             doc = createVneidXml((AggReceiveDossierSendLltpDto) aggReceiveDossierDto, ngayTiepNhanFormatted, ngayHenTraFormatted, duLieuChuyenNganhValue);
        } else {
             doc = createMcdtXml((AggReceiveDossierFromMcdtDto) aggReceiveDossierDto, ngayTiepNhanFormatted, ngayHenTraFormatted, duLieuChuyenNganhValue);
        }
        StringWriter stringWriter = new StringWriter();
        javax.xml.transform.TransformerFactory.newInstance().newTransformer().transform(new javax.xml.transform.dom.DOMSource(doc), new javax.xml.transform.stream.StreamResult(stringWriter));
        return stringWriter.toString().replace("&gt;", ">").replace("&lt;", "<");
    }
    private Document createVneidXml(AggReceiveDossierSendLltpDto vneidDto, String ngayTiepNhanFormatted, String ngayHenTraFormatted, String duLieuChuyenNganhValue) throws ParserConfigurationException {
        // Create DocumentBuilderFactory
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

        DocumentBuilder dBuilder = factory.newDocumentBuilder();

        // Create Document
        Document doc = dBuilder.newDocument();

        // Create phần tử Envelope
        Element envelope = doc.createElement("soapenv:Envelope");
        envelope.setAttribute("xmlns:soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
        envelope.setAttribute("xmlns:edx", "http://angiang.gov.vn/schemas/agesb/edXML");
        envelope.setAttribute("xmlns:ages", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        envelope.setAttribute("xmlns:oneg", "http://angiang.gov.vn/schemas/agesb/onegateXML");
        doc.appendChild(envelope);

        // Create phần tử Header
        Element header = doc.createElement("soapenv:Header");
        envelope.appendChild(header);

        // Create phần tử MessageHeader
        Element messageHeader = doc.createElement("ns3:MessageHeader");
        messageHeader.setAttribute("xmlns:ns3", "http://angiang.gov.vn/schemas/agesb/edXML");
        header.appendChild(messageHeader);

        // Create phần tử From
        Element from = doc.createElement("ns3:From");
        messageHeader.appendChild(from);
        // Create phần tử OrganId
        Element organId = doc.createElement("ns3:OrganId");
        organId.appendChild(doc.createTextNode("28"));
        from.appendChild(organId);

        // Create phần tử FieldCode
        Element fieldCode = doc.createElement("ns2:FieldCode");
        fieldCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        fieldCode.appendChild(doc.createTextNode("06"));
        messageHeader.appendChild(fieldCode);

        // Create phần tử ProcessCode
        Element processCode = doc.createElement("ns2:ProcessCode");
        processCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        processCode.appendChild(doc.createTextNode("2000"));
        messageHeader.appendChild(processCode);

        // Create phần tử TypeCode
        Element typeCode = doc.createElement("ns2:TypeCode");
        typeCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        typeCode.appendChild(doc.createTextNode("3000"));
        messageHeader.appendChild(typeCode);

        // Create phần tử Body
        Element body = doc.createElement("soapenv:Body");
        envelope.appendChild(body);

        // Create phần tử MessageBody
        Element messageBody = doc.createElement("edx:MessageBody");
        body.appendChild(messageBody);

        // Create phần tử BusinessData
        Element businessData = doc.createElement("ns2:BusinessData");
        businessData.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        businessData.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
        businessData.setAttribute("xsi:type", "ns2:BusinessData");
        messageBody.appendChild(businessData);

        // Create phần tử ThongTinHoSo
        Element thongTinHoSo = doc.createElement("ns1:ThongTinHoSo");
        thongTinHoSo.setAttribute("xmlns:ns1", "http://angiang.gov.vn/schemas/agesb/onegateXML");
        businessData.appendChild(thongTinHoSo);

        // Thêm các phần tử con vào ThongTinHoSo
        Element maBNHS = doc.createElement("ns1:MaBNHS");
        maBNHS.appendChild(doc.createTextNode(vneidDto.getMaHoSoMCDT()));
        thongTinHoSo.appendChild(maBNHS);

        Element tenToChuc = doc.createElement("ns1:TenToChuc");
        tenToChuc.appendChild(doc.createTextNode(vneidDto.getTenToChuc()));
        thongTinHoSo.appendChild(tenToChuc);

        Element tenNguoiNop = doc.createElement("ns1:TenNguoiNop");
        tenNguoiNop.appendChild(doc.createTextNode(vneidDto.getTenNguoiNop()));
        thongTinHoSo.appendChild(tenNguoiNop);

        Element diaChiNguoiNop = doc.createElement("ns1:DiaChiNguoiNop");
        diaChiNguoiNop.appendChild(doc.createTextNode(vneidDto.getDiaChiNguoiNop()));
        thongTinHoSo.appendChild(diaChiNguoiNop);

        Element soCMND = doc.createElement("ns1:SoCMND");
        soCMND.appendChild(doc.createTextNode(vneidDto.getSoCCCDNguoiNop()));
        thongTinHoSo.appendChild(soCMND);

        Element dienThoai = doc.createElement("ns1:DienThoai");
        dienThoai.appendChild(doc.createTextNode(vneidDto.getSoDienThoaiNguoiNop()));
        thongTinHoSo.appendChild(dienThoai);

        Element tenDVCong = doc.createElement("ns1:TenDVCong");
        tenDVCong.appendChild(doc.createTextNode(vneidDto.getTenDichVuCong()));
        thongTinHoSo.appendChild(tenDVCong);

        Element ngayNhanHS = doc.createElement("ns1:NgayNhanHS");
        ngayNhanHS.appendChild(doc.createTextNode(ngayTiepNhanFormatted));
        thongTinHoSo.appendChild(ngayNhanHS);

        Element ngayHenTraHS = doc.createElement("ns1:NgayHenTraHS");
        ngayHenTraHS.appendChild(doc.createTextNode(ngayHenTraFormatted));
        thongTinHoSo.appendChild(ngayHenTraHS);

        Element tinhTrangHS = doc.createElement("ns1:TinhTrangHS");
        tinhTrangHS.appendChild(doc.createTextNode(vneidDto.getTenTrangThaiHoSo()));
        thongTinHoSo.appendChild(tinhTrangHS);

        Element trangThaiXuLy = doc.createElement("ns1:TrangThaiXuLy");
        trangThaiXuLy.appendChild(doc.createTextNode(vneidDto.getMaTrangThaiHoSo()));
        thongTinHoSo.appendChild(trangThaiXuLy);

        Element phongBanXuLy = doc.createElement("ns1:PhongBanXuLy");
        phongBanXuLy.appendChild(doc.createTextNode(vneidDto.getPhongBanXuLy()));
        thongTinHoSo.appendChild(phongBanXuLy);

        Element cvXuLy = doc.createElement("ns1:CVXuLy");
        cvXuLy.appendChild(doc.createTextNode(vneidDto.getChuyenVienXuLy()));
        thongTinHoSo.appendChild(cvXuLy);

        Element duLieuChuyenNganh = doc.createElement("ns1:DuLieuChuyenNganh");
        duLieuChuyenNganh.appendChild(doc.createTextNode(duLieuChuyenNganhValue));
        thongTinHoSo.appendChild(duLieuChuyenNganh);
        return doc;
    }
    private Document createMcdtXml(AggReceiveDossierFromMcdtDto mcdtDto, String ngayTiepNhanFormatted, String ngayHenTraFormatted, String duLieuChuyenNganhValue) throws ParserConfigurationException {
        // Create DocumentBuilderFactory
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

        DocumentBuilder dBuilder = factory.newDocumentBuilder();

        // Create Document
        Document doc = dBuilder.newDocument();

        // Create phần tử Envelope
        Element envelope = doc.createElement("soapenv:Envelope");
        envelope.setAttribute("xmlns:soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
        envelope.setAttribute("xmlns:edx", "http://angiang.gov.vn/schemas/agesb/edXML");
        envelope.setAttribute("xmlns:ages", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        envelope.setAttribute("xmlns:oneg", "http://angiang.gov.vn/schemas/agesb/onegateXML");
        doc.appendChild(envelope);

        // Create phần tử Header
        Element header = doc.createElement("soapenv:Header");
        envelope.appendChild(header);

        // Create phần tử MessageHeader
        Element messageHeader = doc.createElement("ns3:MessageHeader");
        messageHeader.setAttribute("xmlns:ns3", "http://angiang.gov.vn/schemas/agesb/edXML");
        header.appendChild(messageHeader);

        // Create phần tử From
        Element from = doc.createElement("ns3:From");
        messageHeader.appendChild(from);
        // Create phần tử OrganId
        Element organId = doc.createElement("ns3:OrganId");
        organId.appendChild(doc.createTextNode("28"));
        from.appendChild(organId);

        // Create phần tử FieldCode
        Element fieldCode = doc.createElement("ns2:FieldCode");
        fieldCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        fieldCode.appendChild(doc.createTextNode("06"));
        messageHeader.appendChild(fieldCode);

        // Create phần tử ProcessCode
        Element processCode = doc.createElement("ns2:ProcessCode");
        processCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        processCode.appendChild(doc.createTextNode("2000"));
        messageHeader.appendChild(processCode);

        // Create phần tử TypeCode
        Element typeCode = doc.createElement("ns2:TypeCode");
        typeCode.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        typeCode.appendChild(doc.createTextNode("3000"));
        messageHeader.appendChild(typeCode);

        // Create phần tử Body
        Element body = doc.createElement("soapenv:Body");
        envelope.appendChild(body);

        // Create phần tử MessageBody
        Element messageBody = doc.createElement("edx:MessageBody");
        body.appendChild(messageBody);

        // Create phần tử BusinessData
        Element businessData = doc.createElement("ns2:BusinessData");
        businessData.setAttribute("xmlns:ns2", "http://angiang.gov.vn/schemas/agesb/agesbXML");
        businessData.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
        businessData.setAttribute("xsi:type", "ns2:BusinessData");
        messageBody.appendChild(businessData);

        // Create phần tử ThongTinHoSo
        Element thongTinHoSo = doc.createElement("ns1:ThongTinHoSo");
        thongTinHoSo.setAttribute("xmlns:ns1", "http://angiang.gov.vn/schemas/agesb/onegateXML");
        businessData.appendChild(thongTinHoSo);

        // Thêm các phần tử con vào ThongTinHoSo
        Element maBNHS = doc.createElement("ns1:MaBNHS");
        maBNHS.appendChild(doc.createTextNode(mcdtDto.getMaHoSoMCDT()));
        thongTinHoSo.appendChild(maBNHS);

        Element tenToChuc = doc.createElement("ns1:TenToChuc");
        tenToChuc.appendChild(doc.createTextNode(mcdtDto.getTenToChuc()));
        thongTinHoSo.appendChild(tenToChuc);

        Element tenNguoiNop = doc.createElement("ns1:TenNguoiNop");
        tenNguoiNop.appendChild(doc.createTextNode(mcdtDto.getTenNguoiNop()));
        thongTinHoSo.appendChild(tenNguoiNop);

        Element diaChiNguoiNop = doc.createElement("ns1:DiaChiNguoiNop");
        diaChiNguoiNop.appendChild(doc.createTextNode(mcdtDto.getDiaChiNguoiNop()));
        thongTinHoSo.appendChild(diaChiNguoiNop);

        Element soCMND = doc.createElement("ns1:SoCMND");
        soCMND.appendChild(doc.createTextNode(mcdtDto.getSoCCCDNguoiNop()));
        thongTinHoSo.appendChild(soCMND);

        Element dienThoai = doc.createElement("ns1:DienThoai");
        dienThoai.appendChild(doc.createTextNode(mcdtDto.getSoDienThoaiNguoiNop()));
        thongTinHoSo.appendChild(dienThoai);

        Element tenDVCong = doc.createElement("ns1:TenDVCong");
        tenDVCong.appendChild(doc.createTextNode(mcdtDto.getTenDichVuCong()));
        thongTinHoSo.appendChild(tenDVCong);

        Element ngayNhanHS = doc.createElement("ns1:NgayNhanHS");
        ngayNhanHS.appendChild(doc.createTextNode(ngayTiepNhanFormatted));
        thongTinHoSo.appendChild(ngayNhanHS);

        Element ngayHenTraHS = doc.createElement("ns1:NgayHenTraHS");
        ngayHenTraHS.appendChild(doc.createTextNode(ngayHenTraFormatted));
        thongTinHoSo.appendChild(ngayHenTraHS);

        Element tinhTrangHS = doc.createElement("ns1:TinhTrangHS");
        tinhTrangHS.appendChild(doc.createTextNode(mcdtDto.getTenTrangThaiHoSo()));
        thongTinHoSo.appendChild(tinhTrangHS);

        Element trangThaiXuLy = doc.createElement("ns1:TrangThaiXuLy");
        trangThaiXuLy.appendChild(doc.createTextNode(mcdtDto.getMaTrangThaiHoSo()));
        thongTinHoSo.appendChild(trangThaiXuLy);

        Element phongBanXuLy = doc.createElement("ns1:PhongBanXuLy");
        phongBanXuLy.appendChild(doc.createTextNode(mcdtDto.getPhongBanXuLy()));
        thongTinHoSo.appendChild(phongBanXuLy);

        Element cvXuLy = doc.createElement("ns1:CVXuLy");
        cvXuLy.appendChild(doc.createTextNode(mcdtDto.getChuyenVienXuLy()));
        thongTinHoSo.appendChild(cvXuLy);

        Element duLieuChuyenNganh = doc.createElement("ns1:DuLieuChuyenNganh");
        duLieuChuyenNganh.appendChild(doc.createTextNode(duLieuChuyenNganhValue));
        thongTinHoSo.appendChild(duLieuChuyenNganh);
        return doc;
    }


    private String formatDate(String dateString) {
        try {
            if (dateString == null || dateString.trim().isEmpty()) {
                return "";
            }
            LocalDate date = LocalDate.parse(dateString, inputFormatter);
            return date.atStartOfDay().format(outputFormatter);
        } catch (Exception e) {
            throw new RuntimeException("Ngày không hợp lệ: " + dateString);
        }
    }

    private HttpURLConnection configureXMLConnection(String registerUrl, String accessToken) throws IOException {
        URL url;
        try {
            url = new URL(registerUrl);
        } catch (MalformedURLException e) {
            throw new IllegalArgumentException("Invalid URL: " + registerUrl, e);
        }
        String auth = String.format("Bearer %s", accessToken);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        try {
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "text/xml");
            connection.setRequestProperty("Accept-Charset", "UTF-8");
            connection.setRequestProperty("SOAPAction", registerUrl);
            connection.setRequestProperty("Authorization", auth);
            connection.setDoOutput(true);
            connection.setDoInput(true);
            return connection;
        } catch (ProtocolException e) {
            throw new IOException("Failed to set HTTP method: POST", e);
        }
    }
    private Map<String, String> getConfigIntegrated() throws Exception {
        IntegratedConfigurationDto config = Optional.ofNullable(integratedVneidLltpIdConfig)
                .map(integratedConfigurationService::getConfig)
                .orElseThrow(() -> new Exception("Đã có lỗi xảy ra khi truy xuất tham số cấu hình tích hợp!"));

        return Stream.of("gateway-token", "consumer-key",
                        "consumer-secret", "register-url")
                .collect(Collectors.toMap(key -> key, config::getParametersValue));
    }
    private TokenResDto getToken(Map<String,String> config)
    {
        String strConsumer = config.get("consumer-key") + ":" + config.get("consumer-secret");
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(config.get("gateway-token"));
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();
        ResponseEntity<Object> result;
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(auth);
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<?> request = new HttpEntity<>(headers);
        result = restTemplate.exchange(
                uriComponents.toUriString(),
                HttpMethod.POST, request, Object.class);
        return GsonUtils.copyObject(result.getBody(), TokenResDto.class);
    }
    private Map<String, Object> extractValuesOnly(Map<String, Object> map) {
        return extractValuesOnly(map, new HashSet<>(), 0);
    }

    private Map<String, Object> extractValuesOnly(Map<String, Object> map, Set<Map<String, Object>> visited, int depth) {
        Optional.of(depth)
                .filter(d -> d <= MAX_DEPTH)
                .orElseThrow(() -> new StackOverflowError("Độ sâu đệ quy vượt quá giới hạn cho phép."));
        if (visited.contains(map)) {
            return Collections.emptyMap();
        }
        visited.add(map);

        return Optional.of(map)
                .map(m -> processMapEntries(m, visited, depth))
                .orElse(Collections.emptyMap());
    }

    private Map<String, Object> processMapEntries(Map<String, Object> map, Set<Map<String, Object>> visited, int depth) {
        if (depth > MAX_DEPTH) {
            throw new StackOverflowError("Độ sâu đệ quy vượt quá giới hạn cho phép.");
        }
        Map<String, Object> result = new LinkedHashMap<>();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if ("uyQuyen".equals(key) && "1".equals(value.toString()) && map.containsKey("thongTinUyQuyen")) {
                Object thongTinUyQuyen = map.get("thongTinUyQuyen");
                if (thongTinUyQuyen instanceof Map) {
                    Map<?, ?> nestedMap = (Map<?, ?>) thongTinUyQuyen;
                    nestedMap.forEach((nestedKey, nestedValue) -> {
                        result.put(nestedKey.toString(), nestedValue);
                        processNestedValues(nestedKey.toString(), nestedValue, result, visited, depth);
                    });
                }
                continue;
            }
            Optional.ofNullable("chiTiet".equals(key) ? key.replace("chiTiet", "dcChiTiet") : null)
                    .ifPresent(newKey -> result.put(newKey, value));
            processNestedValues(key, value, result, visited, depth);
        }

        return result;
    }

    private void processNestedValues(String key, Object value, Map<String, Object> result, Set<Map<String, Object>> visited, int depth) {
        if (depth > MAX_DEPTH) {
            throw new StackOverflowError("Độ sâu đệ quy vượt quá giới hạn cho phép.");
        }
        Optional.of(value)
                .filter(v -> v instanceof Map)
                .map(v -> (Map<?, ?>) v)
                .ifPresent(nestedMap -> {
                    if (nestedMap.containsKey("value") && (nestedMap.containsKey("key") || nestedMap.containsKey("subValue"))) {
                        result.put(key, nestedMap.get("value").toString());
                        Optional.ofNullable(key.startsWith("ma") ? key.replace("ma", "ten") : null)
                                .ifPresent(newKey -> result.put(newKey, nestedMap.get("key")));
                    } else {
                        result.put(key, extractValuesOnly((Map<String, Object>) nestedMap, visited, depth + 1));
                    }
                });

        Optional.of(value)
                .filter(v -> v instanceof List)
                .map(v -> (List<?>) v)
                .ifPresent(list -> result.put(key, processList(list, visited, depth + 1)));

        Optional.of(value)
                .filter(v -> !(v instanceof Map) && !(v instanceof List))
                .ifPresent(v -> result.put(key, v.toString()));
    }



    @SuppressWarnings("unchecked")
    private List<Object> processList(List<?> list, Set<Map<String, Object>> visited, int depth) {
        List<Object> resultList = new ArrayList<>();
        for (Object item : list) {
            if (item instanceof Map) {
                resultList.add(extractValuesOnly((Map<String, Object>) item, visited, depth + 1));
            } else {
                resultList.add(item);
            }
        }
        return resultList;
    }
    public Map<String, String> isCheckConnectAPI(String type) throws Exception {
        Map<String, String> result = new LinkedHashMap<>();

        Optional<String> token = Optional.empty();

        if ("btp".equals(type)) {
            Map<String, String> config = getConfigIntegrated();
            token = Optional.ofNullable(getToken(config))
                    .map(TokenResDto::getAccessToken);
        } else if ("vneid".equals(type)) {
            token = Optional.ofNullable(getTokenVneid());
        }

        result.put("message", "Lấy dữ liệu thành công!");
        result.put("status", token.isPresent() ? "true" : "false");

        if (token.isEmpty() && !"btp".equals(type) && !"vneid".equals(type)) {
            result.put("message", "Lấy dữ liệu thất bại!");
        }

        return result;
    }

    public String getTokenVneid() throws Exception {
        Map<String, String> config = getConfigIntegratedVneid();
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("clientId", config.get("vneid-client-id"));
        requestBody.put("clientSecret", config.get("vneid-client-secret"));
        requestBody.put("grantType", "client_credentials");
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<Map<String, String>> entity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<Map> response = restTemplate.exchange(
                TOKEN_VNEID_URL,
                HttpMethod.POST,
                entity,
                Map.class
        );
        if (response.getStatusCode().is2xxSuccessful()) {
            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null && "00".equals(responseBody.get("statusCode"))) {
                return (String) responseBody.get("accessToken");
            } else {
                throw new Exception("Lỗi từ API: " + responseBody);
            }
        } else {
            throw new Exception("Lỗi HTTP: " + response.getStatusCode());
        }
    }

    public Map<String, String> getConfigIntegratedVneid() throws Exception {
        IntegratedConfigurationDto config = Optional.ofNullable(integratedVneidLltpIdConfig)
                .map(integratedConfigurationService::getConfig)
                .orElseThrow(() -> new Exception("Đã có lỗi xảy ra khi truy xuất tham số cấu hình tích hợp!"));
        return Stream.of("vneid-client-secret", "vneid-client-id")
                .collect(Collectors.toMap(key -> key, config::getParametersValue));
    }

}
