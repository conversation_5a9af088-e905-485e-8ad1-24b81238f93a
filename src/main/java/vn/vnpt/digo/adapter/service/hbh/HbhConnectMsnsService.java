package vn.vnpt.digo.adapter.service.hbh;

import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.hbh.HistoryInputDto;
import vn.vnpt.digo.adapter.dto.hbh.log.LogParam;
import vn.vnpt.digo.adapter.dto.hbh.msns.*;
import vn.vnpt.digo.adapter.dto.vnptctdt.FileSignInfoDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.*;
import vn.vnpt.digo.adapter.service.IntegratedConfigurationService;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Oauth2RestTemplate;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.text.SimpleDateFormat;
import java.util.*;
import org.apache.commons.codec.binary.Base64;

@Service
@Slf4j
public class HbhConnectMsnsService {
    @Autowired
    private Translator translator;
    @Autowired
    private LgspTriNamService lgspTriNamService;
    @Autowired
    private IntegratedConfigurationService configurationService;
    @Autowired
    private HbhEventLogService eventLogService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private Microservice microservice;
    @Autowired
    private Oauth2RestTemplate oauth2RestTemplate;

    public final static ObjectId SERVICE_ID = new ObjectId("65a0a255a3799ac9b30eeec1");
    private final static SimpleDateFormat dd_MM_yyyy_SDF = new SimpleDateFormat("dd/MM/yyyy");
    private final static SimpleDateFormat Db_DateTime_Sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
    private final static SimpleDateFormat dd_MM_yyyy_hh_mm_ssSDF = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");

    @Scheduled(cron = "${digo.schedule.hbh-btc.sync-capMsDvNs.cron:-}")
    ////@SchedulerLock(name = "syncDsHsTheoNgay", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void syncDsHsTheoNgay() {
        IntegratedConfigurationDto config = configurationService.searchByServiceId(SERVICE_ID);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        log.info("syncDsHsTheoNgay Config "+config);
        Boolean enable = config.getParametersValue("EnableSync_DanhSachHoSoTheoNgay");
        log.info("EnableSync_DanhSachHoSoTheoNgay : "+enable);
        if(enable == null || !enable) {
            return;
        }
        PadPApplyDto.ProcedureDtoTemp procedure = getFullProcedure(config);
        log.info("syncDsHsTheoNgay procedure: "+procedure);
        if(procedure == null) {
            return;
        }
        PadPApplyDto.ProcessDto process = getProcessByProcedure(procedure.getId());
        log.info("syncDsHsTheoNgay process: "+process);
        if(process == null) {
            return;
        }
        if(procedure.getAgency() == null || procedure.getAgency().isEmpty()) {
            log.info("syncDsHsTheoNgay procedure agency is empty");
            return;
        }
        String agencyId = config.getParametersValue("AgencyId");
        PadPApplyDto.AgencyDto agency = getAgency(agencyId);
        log.info("syncDsHsTheoNgay agency: "+agency);
        if(agency == null) {
            return;
        }
        String url = config.getParametersValue("URL_DanhSachHoSoTheoNgay");
        String lastSync = config.getParametersValue("LastSync_DanhSachHoSoTheoNgay");
        Integer numberDate = config.getParametersValue("SoNgay_DanhSachHoSoTheoNgay");
        if(numberDate == null || numberDate < 1 || numberDate > 5) {
            numberDate = 1;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("ddMMyyyy");
        Calendar from = Calendar.getInstance();
        try {
            Date fromDate = dd_MM_yyyy_SDF.parse(lastSync);
            from.setTime(fromDate);
        } catch (Exception e) {
            log.error(e.getMessage());
            from = Calendar.getInstance();
            from.set(Calendar.HOUR, 0);
            from.set(Calendar.MINUTE, 0);
            from.set(Calendar.SECOND, 0);
        }
        String fromTime = sdf.format(from.getTime());

        Calendar to = Calendar.getInstance();
        to.setTime(from.getTime());
        to.add(Calendar.DATE, numberDate);
        String toTime = sdf.format(to.getTime());

        HashMap<String, Object> body = new HashMap<>();
        body.put("tuNgay", fromTime);
        body.put("denNgay", toTime);
        DanhSachHoSoTheoNgayDto response = null;
        //Log
        LogParam logParams = new LogParam();
        logParams.setRequestUrl(url);
        logParams.setRequestMethod("GET");
        logParams.setServiceName("HBH-MSNS-syncDsHsTheoNgay");
        try {
            response = lgspTriNamService.getRequest(null, url, body, DanhSachHoSoTheoNgayDto.class);
            logResponse(response);
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, true, null);
        } catch (Exception e) {
            log.error("syncKetQuaCuoiCungDossier "+e.getMessage());
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, false, e.getMessage());
        }
        if(response == null || response.getData() == null) {
            log.info("syncDsHsTheoNgay: No data");
            return;
        }
        String data = xmlToJson(response.getData(),"getDetailHoSoTheoNgayResponse");
        BudgetDossierListReturnDto list = getBudgetDossierListReturn(data);
        logParams = new LogParam();
        logParams.setServiceName("HBH-MSNS-syncDsHsTheoNgay");
        try {
            syncToIgate(procedure, process, agency, list);
            updateLastSync(from, to, "LastSync_DanhSachHoSoTheoNgay");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public HashMap<String, Object> syncDsHsTheoNgayHB(String tuNgay, String denNgay, boolean showDetail) {
        HashMap<String, Object> result = new HashMap<>();
        IntegratedConfigurationDto config = configurationService.searchByServiceId(SERVICE_ID);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        log.info("syncDsHsTheoNgay Config " + config);

        PadPApplyDto.ProcedureDtoTemp procedure = getFullProcedure(config);
        log.info("syncDsHsTheoNgay procedure: " + procedure);
        if (procedure == null) {
            result.put("msg", "procedure null");
            return result;
        }
        PadPApplyDto.ProcessDto process = getProcessByProcedure(procedure.getId());
        log.info("syncDsHsTheoNgay process: " + process);
        if (process == null) {
            result.put("msg", "process null");
            return result;
        }
        if (procedure.getAgency() == null || procedure.getAgency().isEmpty()) {
            log.info("syncDsHsTheoNgay procedure agency is empty");
            result.put("msg", "syncDsHsTheoNgay procedure agency is empty");
            return result;

        }
        String agencyId = config.getParametersValue("AgencyId");
        PadPApplyDto.AgencyDto agency = getAgency(agencyId);
        log.info("syncDsHsTheoNgay agency: " + agency);
        if (agency == null) {
            result.put("msg", "agency null");
            return result;
        }
        String url = config.getParametersValue("URL_DanhSachHoSoTheoNgay");

        HashMap<String, Object> body = new HashMap<>();
        body.put("tuNgay", tuNgay);
        body.put("denNgay", denNgay);
        DanhSachHoSoTheoNgayDto response = null;
        // Log
        LogParam logParams = new LogParam();
        logParams.setRequestUrl(url);
        logParams.setRequestMethod("GET");
        logParams.setServiceName("HBH-MSNS-syncDsHsTheoNgay");
        try {
            response = lgspTriNamService.getRequest(null, url, body, DanhSachHoSoTheoNgayDto.class);
            logResponse(response);
            if(showDetail){
                result.put("DanhSachHoSoTheoNgay", response);
            }
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, true, null);
        } catch (Exception e) {
            log.error("syncKetQuaCuoiCungDossier " + e.getMessage());
            result.put("msg","syncDsHsTheoNgay Fail: "+ e.getMessage());
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, false, e.getMessage());
        }
        if (response == null || response.getData() == null) {
            log.info("syncDsHsTheoNgay: No data");
            result.put("msg", "syncDsHsTheoNgay Done");
            return result;
        }
        String data = xmlToJson(response.getData(), "getDetailHoSoTheoNgayResponse");
        BudgetDossierListReturnDto list = getBudgetDossierListReturn(data);
        logParams = new LogParam();
        logParams.setServiceName("HBH-MSNS-syncDsHsTheoNgay");
        try {
            HashMap<String, Object> res = syncToIgate(procedure, process, agency, list);
            if(showDetail){
                result.put("syncToIgate", res);
            }
            result.put("syncDsHsTheoNgayHB", "Done");
            return result;
            // updateLastSync(tuNgay, denNgay, "LastSync_DanhSachHoSoTheoNgay");
        } catch (Exception e) {
            log.error(e.getMessage());
            result.put("msg", "syncToIgate: fail = " +e.getMessage());
            result.put("error", e);
            return result;
        }
    }

    private HashMap<String, Object> syncToIgate(PadPApplyDto.ProcedureDtoTemp procedure,
            PadPApplyDto.ProcessDto process,
            PadPApplyDto.AgencyDto agency, BudgetDossierListReturnDto data) {
        HashMap<String, Object> res = new HashMap<>();
        if (data == null || data.getRETURN() == null) {
            return res;
        }
        List<BudgetDossierDataDto> listDto = data.getRETURN();
        HashMap<String, Object> updateDossierLog = new HashMap<>();
        for(BudgetDossierDataDto dto : listDto) {
            int trangThai = dto.getTrang_thai();
            if(trangThai == 0) {
                log.info("syncToIgate:trang thai ho so "+ dto.getHsid()+" khong hop le "+trangThai);
                continue;
            }
            IdCode idCode = checkExist(dto.getHsid());
            log.info("syncToIgate: ho so "+ dto.getHsid()+" da ton tai "+idCode);
            if(idCode != null && idCode.getId() != null) {
                log.info("syncToIgate: cap nhat trang thai ho so "+idCode);
                HashMap<String, Object> resData = updateDossierStatus(agency, idCode.getId(), dto);
                updateDossierLog.putAll(resData);
                res.put("UPDATE_"+dto.getHsid(), resData);
            } else {
                log.info("syncToIgate: Luu moi ho so " + dto.getHsid());
                HashMap<String, Object> resData = convertToIgate(procedure, process, agency, dto);
                res.put("CONVERT_"+dto.getHsid(), resData);
                updateDossierLog.putAll(resData);
            }
        }
        return res;
    }

    private HashMap<String, Object> updateDossierStatus(PadPApplyDto.AgencyDto agency, ObjectId objectId, BudgetDossierDataDto dto) {
        HashMap<String, Object> res =  new HashMap<>();
        int trangThai = dto.getTrang_thai();
        UpdatePadPApplyDto dossierOnline = new UpdatePadPApplyDto();
        dossierOnline.setDossierTaskStatus(getDossierTaskStatus(trangThai));
        dossierOnline.setDossierMenuTaskRemind(getDossierTaskReminder(trangThai));
        dossierOnline.setDossierStatus(getDossierStatus(trangThai));
        dossierOnline.setCompletedDate(convertDbSdf(dto.getNgay_tra(), dd_MM_yyyy_SDF));
        //Agency
        dossierOnline.setAgency(agency);
        // eform data
        if (dto.getMa() != null && !dto.getMa().isEmpty()) {
            HashMap<String, Object> dataGCN = this.getGiayChungNhan(objectId.toString(), dossierOnline, null, dto.getMa());
            res.put(dto.getMa(), dataGCN);
        }
        String updateDossierUrl = microservice.padmanUri("/dossier/"+objectId.toString()+"/--online").toUriString();
        AffectedRowsDto updateDossier = null;
        log.info("Cap nhat ho so id "+updateDossierUrl);
        log.info("Cap nhat ho so body "+dossierOnline);
        try {
            updateDossier = MicroserviceExchange.putJsonNoAuth(getRestTemplate(), updateDossierUrl, dossierOnline, AffectedRowsDto.class);
        } catch (Exception e) {
            res.put(dto.getMa(), e);
            log.error("Cap nhat ho so bi loi: "+e.getMessage());
        }
        log.info("Cap nhat ho so id: "+updateDossier);
        if(dto.getRow() != null && dto.getRow().getDATA_PROCESS() != null) {
            //Update history
            deleteHistory(objectId);
            log.info("Luu tien trinh xu ly ho so");
            Date defaultDate = null;
            try {
                defaultDate = dd_MM_yyyy_SDF.parse(dto.getNgay_dk());
            } catch (Exception e) {
                res.put(dto.getMa(), e);
                log.error(e.getMessage());
            }
            if (defaultDate == null) {
                defaultDate = new Date();
            }
            for (BudgetDossierDataDto.BudgetDataProcess dataProcess : dto.getRow().getDATA_PROCESS()) {
                writeHistory(objectId, defaultDate, dataProcess);
            }
        }
        return res;
    }

    private HashMap<String, Object> convertToIgate(PadPApplyDto.ProcedureDtoTemp procedure, PadPApplyDto.ProcessDto process,
                                PadPApplyDto.AgencyDto agency,
                                BudgetDossierDataDto dto) {
        log.info("syncToIgate: ho so "+ dto.getHsid());
        HashMap<String, Object> res = new HashMap<>();
        PadPApplyDto dossierOnline = new PadPApplyDto();
        PadPApplyDto.SyncDto sync = new PadPApplyDto.SyncDto(1, dto.getHsid());
        dossierOnline.setSync(sync);
        dossierOnline.setCode(dto.getHsid());
        dossierOnline.setNationCode(dto.getHsid());
        dossierOnline.setAcceptedDate(convertDbSdf(dto.getNgay_dk(), dd_MM_yyyy_SDF));
        dossierOnline.setAppliedDate(convertDbSdf(dto.getNgay_tao(), dd_MM_yyyy_SDF));
        dossierOnline.setAppointmentDate(convertDbSdf(dto.getNgay_tra(), dd_MM_yyyy_SDF));
        dossierOnline.setCompletedDate(convertDbSdf(dto.getNgay_tra(), dd_MM_yyyy_SDF));
        //Agency
        dossierOnline.setAgency(agency);
        //Procedure
        PadPApplyDto.ProcedureDto procedureDto = new PadPApplyDto.ProcedureDto();
        procedureDto.setCode(procedure.getCode());
        procedureDto.setId(procedure.getId());
        procedureDto.setSector(procedure.getSector());
        procedureDto.setTranslate(procedure.getTranslate());
        dossierOnline.setProcedure(procedureDto);
        dossierOnline.setProcedureLevel(procedure.getLevel());
        //Thong tin nguoi nop
        PadPApplyDto.ApplicantDto applicant = new PadPApplyDto.ApplicantDto();
        PadPApplyDto.ApplicantDataDto applicantData = new PadPApplyDto.ApplicantDataDto();
        applicantData.setFullname(dto.getNguoi_dk());
        applicantData.setOwnerFullname(dto.getNguoi_dk());
        applicantData.setEmail(dto.getEmail());
        applicantData.setPhoneNumber(dto.getSdt_didong());
        applicantData.setPhone(dto.getSdt_didong());
        applicantData.setNation(new PadPApplyDto.AddressDetailDto("Việt Nam", "5f39f4a95224cf235e134c5c"));
        if(dto.getKieu_tiep_nhan() == 1) {
            applicantData.setHinhThucNop(2); // Trực tuyến
        } else if(dto.getKieu_tiep_nhan() == 2) {
            applicantData.setHinhThucNop(1); // Trực tiếp
        }
        applicantData.setHinhThucNop(dto.getKieu_tiep_nhan());
        applicant.setData(applicantData);
        dossierOnline.setApplicant(applicant);
        //eform data
        StringBuilder noteBuilder = new StringBuilder();
        if("1".equals(dto.getKieu_hs())) {
            noteBuilder.append("Hồ sơ dùng cho đơn vị dự toán, đơn vị có quan hệ với ngân sách nhà nược, đơn vị khác có quan hệ với ngân sách");
        } else if("2".equals(dto.getKieu_hs())) {
            noteBuilder.append("Hồ sơ dùng cho dự án đầu tư ở giai đoạn chuẩn bị đầu tư");
        } else if("3".equals(dto.getKieu_hs())) {
            noteBuilder.append("Hồ sơ dùng cho dự án đầu tư ở giai đoạn thực hiện đầu tư");
        } else if("4".equals(dto.getKieu_hs())) {
            noteBuilder.append("Hồ sơ dùng cho dự án thay đổi giai đoạn từ chuẩn bị đầu tư sang thực hiện đầu tư");
        } else if("6".equals(dto.getKieu_hs())) {
            noteBuilder.append("Hồ sơ dùng cho dự án/đơn vị đăng ký thay đổi thông tin");
        } else if("10".equals(dto.getKieu_hs())) {
            noteBuilder.append("Hồ sơ dùng cho nhiệm vụ quy hoạch");
        }
        HashMap<String, String> eformData = new HashMap<>();
        eformData.put("ndgq", dto.getTen_hs());
        eformData.put("sotiepnhan", dto.getHsid());
        eformData.put("note", noteBuilder.toString());
        eformData.put("ngaynophoso", dto.getNgay_dk());
        eformData.put("ma", dto.getMa());
        if (dto.getMa() != null && !dto.getMa().isEmpty()) {
            HashMap<String, Object> dataGCN = getGiayChungNhan("", null, dossierOnline, dto.getMa());
            res.put(dto.getMa(), dataGCN);
        }

        PadPApplyDto.FormDto formDto = new PadPApplyDto.FormDto();
        formDto.setData(eformData);
        dossierOnline.setEForm(formDto);
        //Process
        dossierOnline.setProcedureProcessDefinition(process);
        if(process.getProcessDefinition() != null) {
            LinkedHashMap processDefinition = (LinkedHashMap) process.getProcessDefinition();
            try {
                String eformId = ((LinkedHashMap) processDefinition.get("eForm")).get("id").toString();
                formDto.setId(eformId);
            } catch (Exception e){
                res.put(dto.getMa(), e);
                log.error("eForm error: "+e.getMessage());
            }
            try {
                String applicantEformId = ((LinkedHashMap) processDefinition.get("applicantEForm")).get("id").toString();
                applicant.setEformId(applicantEformId);
            } catch (Exception e){
                res.put(dto.getMa(), e);
                log.error("applicantEForm error: "+e.getMessage());
            }
        }
        //Trang thai
        dossierOnline.setDossierTaskStatus(getDossierTaskStatus(dto.getTrang_thai()));
        //Task reminder
        dossierOnline.setDossierMenuTaskRemind(getDossierTaskReminder(dto.getTrang_thai()));
        dossierOnline.setDossierStatus(getStatus(dto.getTrang_thai()));
        String newDossierUrl = microservice.padmanUri("/dossier/--apply-online").toUriString();
        IdDto newDossier = null;
        try {
            newDossier = MicroserviceExchange.postJsonNoAuth(getRestTemplate(), newDossierUrl, dossierOnline, IdDto.class);
        } catch (Exception e) {
            res.put(dto.getMa(), e);
            res.put("dataconvertToIgateFail", dossierOnline);
            log.error("Luu ho so bi loi: "+e.getMessage());
        }
        log.info("Luu ho so id: "+newDossier);
        if(newDossier != null && dto.getRow() != null && dto.getRow().getDATA_PROCESS() != null) {
            log.info("Luu tien trinh xu ly ho so");
            Date defaultDate = null;
            try {
                defaultDate = dd_MM_yyyy_SDF.parse(dto.getNgay_dk());
            } catch (Exception e) {
                res.put(dto.getMa(), e);
                log.error(e.getMessage());
            }
            if(defaultDate == null) {
                defaultDate = new Date();
            }
            for(BudgetDossierDataDto.BudgetDataProcess dataProcess : dto.getRow().getDATA_PROCESS()) {
                writeHistory(newDossier.getId(), defaultDate, dataProcess);
            }
        }
        return res;
    }

    private HashMap<String, Object> getGiayChungNhan(String objectId, UpdatePadPApplyDto dossiers,
            PadPApplyDto dossiersnew, String dvqhns) {
        HashMap<String, Object> res = new HashMap<>();
        IntegratedConfigurationDto config = configurationService.searchByServiceId(SERVICE_ID);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                    HttpServletResponse.SC_NOT_FOUND);
        }
        Boolean enable = config.getParametersValue("EnableSync_GetGiayChungNhan");
        log.info("EnableSync_GetGiayChungNhan : " + enable);
        if (enable == null || !enable) {
            return res;
        }

        String mURL_GetGiayChungNhan = config.getParametersValue("URL_GetGiayChungNhan");
        if (mURL_GetGiayChungNhan == null || mURL_GetGiayChungNhan.isEmpty()) {
            mURL_GetGiayChungNhan = "https://lgspgateway.hoabinh.gov.vn/ngsp/Budget/GetGiayChungNhan";
        }
        String urlGet = mURL_GetGiayChungNhan + "?dvqhns=" + dvqhns;
        Dossier mDossier = null;
        if (objectId != null && !objectId.isEmpty()) {
            String getDossierUrl = microservice.padmanUri("/dossier/" + objectId + "/--online").toUriString();
            log.info("lay thong tin ho so id " + getDossierUrl);
            try {
                mDossier = MicroserviceExchange.getJsonNoAuth(getRestTemplate(), getDossierUrl, Dossier.class);
            } catch (Exception e) {
                res.put(objectId, e.getMessage());
                log.error("Lay thong tin ho so bi loi: " + e.getMessage());
            }
        }
        if (objectId != null && !objectId.isEmpty() &&  mDossier!=null &&  Objects.nonNull(mDossier.getAttachment())) {
            boolean needSysFile = true;
            List<Dossier.Attachment> attachments = mDossier.getAttachment();
            for (Dossier.Attachment attach : attachments) {
                if (attach.getDownloadURL() != null
                        && (attach.getDownloadURL().equals(urlGet) || attach.getFilename().equals(dvqhns + ".pdf"))) {
                    needSysFile = false;
                    break; // Exit the loop early once found
                }
            }
            res.put("needSysFile", needSysFile);
            if (needSysFile == true) {
                ArrayList<Attachment> attachmentArrayList = loadAndSaveFileData(mURL_GetGiayChungNhan, dvqhns,res);
                res.put("loadAndSaveFileData", attachmentArrayList);
                if (dossiers != null) {
                    dossiers.setAttachment(attachmentArrayList);
                }
                if (dossiersnew != null) {
                    List<PadPApplyDto.AttachmentDto> attachmentList = new ArrayList<>();
                    attachmentArrayList.forEach(data -> {
                        PadPApplyDto.AttachmentDto fi = new PadPApplyDto.AttachmentDto();
                        fi.setId(data.getId());
                        fi.setFilename(data.getFilename());
                        fi.setGroup(data.getGroup());
                        fi.setSize(data.getSize());
                        fi.setDownloadURL(urlGet);
                        attachmentList.add(fi);
                    });
                    dossiersnew.setAttachment(attachmentList);
                }
            }
        } else {
            res.put("needSysFile", true);
            ArrayList<Attachment> attachmentArrayList = loadAndSaveFileData(mURL_GetGiayChungNhan, dvqhns,res);
            res.put("loadAndSaveFileData", attachmentArrayList);
            if (dossiers != null) {
                dossiers.setAttachment(attachmentArrayList);
            }
            if (dossiersnew != null) {
                List<PadPApplyDto.AttachmentDto> attachmentList = new ArrayList<>();
                attachmentArrayList.forEach(data -> {
                    PadPApplyDto.AttachmentDto a = new PadPApplyDto.AttachmentDto();
                    a.setId(data.getId());
                    a.setFilename(data.getFilename());
                    a.setGroup(data.getGroup());
                    a.setSize(data.getSize());
                    a.setDownloadURL(urlGet);
                    attachmentList.add(a);
                });

                dossiersnew.setAttachment(attachmentList);
            }
        }
        return res;
    }

    private ArrayList<Attachment> loadAndSaveFileData(String urlGet, String dvqhns, HashMap<String, Object> mess) {
        LogParam logParams = new LogParam();
        logParams.setRequestUrl(urlGet);
        logParams.setRequestMethod("GET");
        logParams.setServiceName("HBH-MSNS-loadAndSaveFileData");
        String response = null;
        HashMap<String, Object> body = new HashMap<>();
        body.put("dvqhns", dvqhns);
        try {
            response = lgspTriNamService.getRequest(null, urlGet, body, String.class);
            logResponse(response);
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, true, null);
        } catch (Exception e) {
            log.error("loadAndSaveFileData " + e.getMessage());
            mess.put("loadAndSaveFileData_LGSP", e.getMessage());
            eventLogService.writeLog(SERVICE_ID, logParams, body, response, false, e.getMessage());
            return null;
        }

        if (response == null) {
            log.info("loadAndSaveFileData: No data");
            mess.put("loadAndSaveFileData_LGSP", "No data");
            return null;
        }

        String data = xmlToJson(response.toString(), "getGiayChungNhanResponse");
        GetGiayChungNhanReturnDto list = getGiayChungNhanReturn(data);

        logParams = new LogParam();
        logParams.setServiceName("HBH-MSNS-GetGiayChungNhan");
        try {
            String URL = microservice.filemanUri("file/--multiple").toUriString();
            Map<String, Object> outputResponse = new HashMap<>();
            MultiValueMap<String, Object> bodyFile = new LinkedMultiValueMap<>();
            if (list != null && list.getRETURN().size() > 0) {
                for (var file : list.getRETURN()) {
                    if (file != null && file.getDATA() != null && !file.getDATA().isEmpty()) {
                        byte[] decodedBytes = Base64.decodeBase64(file.getDATA());
                        String finalFileName = dvqhns + ".pdf";
                        ByteArrayResource byteArrayResource = new ByteArrayResource(decodedBytes) {
                            @Override
                            public String getFilename() {
                                return finalFileName;
                            }
                        };
                        bodyFile.add("files", byteArrayResource);
                    }
                }
                try { // save data to dossier
                    RestTemplate rest = oauth2RestTemplate.getOAuth2RestTemplate();
                    FileSignInfoDto[] fileSignInfoDtos = MicroserviceExchange.postMultipartNoAuth(rest, URL, bodyFile,
                            FileSignInfoDto[].class);
                    if (Objects.isNull(fileSignInfoDtos)) {
                        log.info("ERROR function uploadToFileman");
                        mess.put("uploadToFileman", "ERROR function uploadToFileman");
                        return null;
                    } else {
                        outputResponse.put("ID_File", fileSignInfoDtos);
                        ArrayList<Attachment> attachmentList = new ArrayList<>();
                        for (var fileSignInfo : fileSignInfoDtos) {
                            Attachment attachment = new Attachment();
                            attachment.setId(fileSignInfo.getId().toString());
                            attachment.setFilename(fileSignInfo.getFilename());
                            attachment.setSize(Integer.parseInt(fileSignInfo.getSize().toString()));
                            attachment.setGroup("5f9bd9692994dc687e68b5a6"); // file ket qua
                            attachment.setDownloadURL(urlGet + "?dvqhns=" + dvqhns);
                            attachmentList.add(attachment);
                        }
                        mess.put("uploadToFileman", "success");
                        return attachmentList;
                    }
                    // ObjectMapper mapper = new ObjectMapper();
                    // List<FileSignInfoDto> data = Arrays.asList(fileSignInfoDtos);
                } catch (Exception e) {
                    mess.put("uploadToFileman", e.toString());
                    log.info("ERROR save data");
                    log.info(e.toString());
                    return null;
                }
            }
        } catch (Exception e) {
            // TODO: handle exception
            mess.put("uploadToFileman", e.toString());
            log.info("ERROR function uploadToFileman");
            log.info(e.toString());
            return null;
        }
        return null;
    }

    private void logResponse(Object res) {
        if (res == null) {
            log.info("DIGO-Response: NULL");
            return;
        }
        log.info("DIGO-Response: " + res.toString());
    }

    public AffectedRowsDto updateLastSync(Calendar fromDate, Calendar toDate, String key) {
        Calendar toDay = Calendar.getInstance();
        Date lastSyncDate = (DateUtils.isSameDay(toDay, fromDate)) ? fromDate.getTime() : toDate.getTime();
        String lastDateUpdateSync = dd_MM_yyyy_SDF.format(lastSyncDate.getTime());
        IntegratedConfigurationDto config = configurationService.searchByServiceId(SERVICE_ID);
        if (Objects.isNull(config)) {
            return new AffectedRowsDto(-1, "Config not found");
        }
        config.setParametersValue(key, lastDateUpdateSync, new ParametersType(1, "String"));

        IntegratedConfiguration configuration = mongoTemplate.findById(config.getId(), IntegratedConfiguration.class);
        configuration.setParameters(config.getParameters());
        mongoTemplate.save(configuration);
        return new AffectedRowsDto(1);
    }

    private String xmlToJson(String data, String type) {
        String resultReplace = data.replace("<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\"><soap:Body>", "");
        resultReplace = resultReplace.replace("ns2:", "");
        resultReplace = resultReplace.replace(" >", ">");
        resultReplace = resultReplace.replace("xmlns:ns2=\"http://services.cms.fpt/\"", "");
        resultReplace = resultReplace.replace("</soap:Body></soap:Envelope>", "");
        resultReplace = resultReplace.replace(type, type.toUpperCase());
        resultReplace = resultReplace.replace(" >", ">");
        resultReplace = resultReplace.replace("return", "RETURN");
        return resultReplace;
    }

    public static BudgetDossierListReturnDto getBudgetDossierListReturn(String result) {
        BudgetDossierListReturnDto budgetDataDto = new BudgetDossierListReturnDto();
        try {
            StringReader data = new StringReader(result);
            JAXBContext jaxbContext = JAXBContext.newInstance(BudgetDossierListReturnDto.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            budgetDataDto = (BudgetDossierListReturnDto) unmarshaller.unmarshal(data);
        } catch (JAXBException e) {
            System.out.println(e.getMessage());
        }
        return budgetDataDto;
    }

    public static GetGiayChungNhanReturnDto getGiayChungNhanReturn(String result) {
        GetGiayChungNhanReturnDto mGiayChungNhanReturnDto = new GetGiayChungNhanReturnDto();
        try {
            StringReader data = new StringReader(result);
            JAXBContext jaxbContext = JAXBContext.newInstance(GetGiayChungNhanReturnDto.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            mGiayChungNhanReturnDto = (GetGiayChungNhanReturnDto) unmarshaller.unmarshal(data);
        } catch (JAXBException e) {
            System.out.println(e.getMessage());
        }
        return mGiayChungNhanReturnDto;
    }

    private PadPApplyDto.ProcedureDtoTemp getFullProcedure(IntegratedConfigurationDto config) {
        Map<String, Object> procedureParrams = new HashMap<>();
        procedureParrams.put("id", config.getParametersValue("ProcedureId"));
        String procedureUrl = microservice.basepadUri("/procedure/").toUriString() + "{id}/--full";
        PadPApplyDto.ProcedureDtoTemp procedure = null;
        try {
            procedure = MicroserviceExchange.getNoAuth(getRestTemplate(), procedureUrl, PadPApplyDto.ProcedureDtoTemp.class, procedureParrams);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return procedure;
    }

    private RestTemplate getRestTemplate() {
        return oauth2RestTemplate.getOAuth2RestTemplate();
    }

    private Date convertDbSdf(String input, SimpleDateFormat inputSdf) {
        if (input == null || input.isEmpty()) {
            return null;
        }
        String newDate = "";
        try {
            Date oldDate = inputSdf.parse(input);
            newDate = Db_DateTime_Sdf.format(oldDate);
        } catch (Exception ignored) {
        }
        try {
            return Db_DateTime_Sdf.parse(newDate);
        } catch (Exception e) {

        }
        return null;
    }

    public PadPApplyDto.ProcessDto getProcess(IntegratedConfigurationDto config) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", config.getParametersValue("ProcedureProcessDefinitionId"));
        String processUrl = microservice.basepadUri("/procedure-process-definition/").toUriString() + "{id}";
        log.info("getProcess "+processUrl);
        PadPApplyDto.ProcessDto process = null;
        try {
            process = MicroserviceExchange.getNoAuth(this.getRestTemplate(), processUrl, PadPApplyDto.ProcessDto.class, params);
        } catch (Exception e) {
            log.error("getProcess "+e.getMessage());
        }
        return process;
    }

    private PadPApplyDto.AgencyDto getAgency(String agencyId) {
        try{
            String endpoint = microservice.basedataUri("/agency/"+agencyId+"/name+code+parent+ancestor/--fully").toUriString();
            return MicroserviceExchange.getNoAuth(getRestTemplate(), endpoint, PadPApplyDto.AgencyDto.class);
        } catch (Exception e) {
            log.info(e.getMessage());
            return null;
        }
    }

    private PadPApplyDto.RecvKindDto getDossierTaskStatus(int status) {
        PadPApplyDto.RecvKindDto dossierTaskStatus = new PadPApplyDto.RecvKindDto();
        switch (status) {
            case 1:
                dossierTaskStatus.setId("60e409823dfc9609723e493c");
                List<PadPApplyDto.NameDto> name0 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Mới đăng ký"));
                    }
                };
                dossierTaskStatus.setName(name0);
                break;
            case 2:
                dossierTaskStatus.setId("60ed1a5909cbf91d41f87f9f");
                List<PadPApplyDto.NameDto> name2 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Hồ sơ đã hủy"));
                    }
                };
                dossierTaskStatus.setName(name2);
                break;
            case 3:
                dossierTaskStatus.setId("631e4a0967411b0b0d000002");
                List<PadPApplyDto.NameDto> name3 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Đang xử lý"));
                    }
                };
                dossierTaskStatus.setName(name3);
                break;
            case 4:
                dossierTaskStatus.setId("60ed1a5909cbf91d41f87f9f");
                List<PadPApplyDto.NameDto> name4 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Hồ sơ đã hủy"));
                    }
                };
                dossierTaskStatus.setName(name4);
                break;
            case 5:
                dossierTaskStatus.setId("60ed1a5909cbf91d41f87f9f");
                List<PadPApplyDto.NameDto> name5 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Hồ sơ đã hủy"));
                    }
                };
                dossierTaskStatus.setName(name5);
                break;
            case 6:
                dossierTaskStatus.setId("60ebf17309cbf91d41f87f8e");
                List<PadPApplyDto.NameDto> name6 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Đã trả kết quả"));
                    }
                };
                dossierTaskStatus.setName(name6);
                break;
        }
        return dossierTaskStatus;
    }

    private PadPApplyDto.RecvKindDto getDossierTaskReminder(int status) {
        PadPApplyDto.RecvKindDto dossierTaskStatus = new PadPApplyDto.RecvKindDto();
        switch (status) {
            case 1:
                dossierTaskStatus.setId("60f52e0d09cbf91d41f88834");
                List<PadPApplyDto.NameDto> name1 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Mới đăng ký"));
                    }
                };
                dossierTaskStatus.setName(name1);
                break;
            case 2:
                dossierTaskStatus.setId("60f52ed209cbf91d41f88838");
                List<PadPApplyDto.NameDto> name2 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Từ chối"));
                    }
                };
                dossierTaskStatus.setName(name2);
                break;
            case 3:
                dossierTaskStatus.setId("63da3841ee48c32f84775aab");
                List<PadPApplyDto.NameDto> name3 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Hồ sơ chờ xử lý"));
                    }
                };
                dossierTaskStatus.setName(name3);
                break;
            case 4:
                dossierTaskStatus.setId("60f52ed209cbf91d41f88838");
                List<PadPApplyDto.NameDto> name4 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Từ chối"));
                    }
                };
                dossierTaskStatus.setName(name4);
                break;
            case 5:
                dossierTaskStatus.setId("60f52ed209cbf91d41f88838");
                List<PadPApplyDto.NameDto> name5 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Từ chối"));
                    }
                };
                dossierTaskStatus.setName(name5);
                break;
            case 6:
                dossierTaskStatus.setId("60f52f0109cbf91d41f88839");
                List<PadPApplyDto.NameDto> name6 = new ArrayList<>() {
                    {
                        add(new PadPApplyDto.NameDto((short) 228, "Đã trả kết quả"));
                    }
                };
                dossierTaskStatus.setName(name6);
                break;
        }
        return dossierTaskStatus;
    }

    private String generateCode(String procedureId, PadPApplyDto.AgencyDto agency) {
        String agencyId = agency.getId();
        if(agency.getParent() != null && agency.getParent().getId() != null) {
            agencyId = agency.getParent().getId();
        }
        String patternUrl = microservice.basepadUri("/config/get-pattern").toUriString() + "?agency-id=" + agencyId + "&procedure-id=" + procedureId;
        log.info("generateCode "+patternUrl);
        Id patternId = null;
        try {
            patternId = MicroserviceExchange.getNoAuth(getRestTemplate(), patternUrl, Id.class);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        if(patternId == null || patternId.getId() == null) {
            return null;
        }
        String codeUrl = microservice.basecatUri("/pattern").toUriString() + "/" + patternId.getId().toHexString() + "/--get-next-value?code=" + agency.getCode();
        log.info("generateCode "+codeUrl);
        ValueData valueCode = null;
        try {
            valueCode = MicroserviceExchange.putNoAuthNoBody(getRestTemplate(), codeUrl, ValueData.class);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        if(valueCode != null) {
            return valueCode.getValue();
        }
        return null;
    }

    private Object writeHistory(ObjectId dossierId, Date defaultDate, BudgetDossierDataDto.BudgetDataProcess dto){
        HistoryInputDto.User user = new HistoryInputDto.User();
        user.setName(dto.getCAN_BO_XL());
        List<HistoryInputDto.Action> actions = new ArrayList<>();
        actions.add(new HistoryInputDto.Action("Cán bộ xử lý", dto.getCAN_BO_XL()));
        actions.add(new HistoryInputDto.Action("Đơn vị xử lý", dto.getDON_VI_XL()));
        actions.add(new HistoryInputDto.Action("Nội dung xử lý", dto.getNOI_DUNG_XL()));
        actions.add(new HistoryInputDto.Action("Trạng thái", dto.getTRANG_THAI_MA()+" "+dto.getTRANG_THAI_TEN()));
        actions.add(new HistoryInputDto.Action("Ngày xử lý", dto.getNGAY_XL()));

        HistoryInputDto historyDto = new HistoryInputDto();
        historyDto.setUser(user);
        historyDto.setItemId(dossierId);
        historyDto.setAction(actions);
        try {
            Date date = dd_MM_yyyy_hh_mm_ssSDF.parse(dto.getNGAY_XL());
            historyDto.setCreatedDate(date);
        } catch (Exception e) {
            log.error("writeHistory "+e.getMessage());
            historyDto.setCreatedDate(defaultDate);
        }
        String url = microservice.logmanUri("history").toUriString();
        String idHistory = "";
        try {
            idHistory = MicroserviceExchange.postJsonNoAuth(getRestTemplate(), url, historyDto, String.class);
            log.info("writeHistory success: "+idHistory);
        } catch (Exception e) {
            log.error("writeHistory "+e.getMessage());
        }
        return historyDto;
    }

    private IdCode checkExist(String code) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        String dosUrl = microservice.padmanUri("/dossier/").toUriString() + "{code}/--by-nation-code";
        try {
            return MicroserviceExchange.getNoAuth(getRestTemplate(), dosUrl, IdCode.class, params);
        } catch (Exception e) {
            log.error("checkExist "+code+" err: "+e.getMessage());
        }
        return null;
    }

    private void deleteHistory(ObjectId dossierId){
        String url = microservice.logmanUri("history/delete-by-group-and-item").toUriString()
                +"?group-id=1&item-id="+dossierId.toHexString();
        log.info("deleteHistory "+url);
        try {
            AffectedRowsDto affectedRowsDto = MicroserviceExchange.deleteNoAuth(getRestTemplate(), url, AffectedRowsDto.class);
            log.info("deleteHistory success");
        } catch (Exception e) {
            log.error("writeHistory "+e.getMessage());
        }
    }

    private int getStatus(int status) {
        switch (status) {
            case 1:
                return 0;
            case 2:
                return 19;
            case 3:
                return 2;
            case 4:
                return 6;
            case 5:
                return 6;
            case 6:
                return 4;
        }
        return 0;
    }

    private UpdatePadPApplyDto.DossierStatus getDossierStatus(int status) {
        switch (status) {
            case 1:
                UpdatePadPApplyDto.DossierStatus status1 = new UpdatePadPApplyDto.DossierStatus(0, new ArrayList<>());
                status1.getName().add(new Translate((short)228, "Chờ tiếp nhận"));
                return status1;
            case 2:
                UpdatePadPApplyDto.DossierStatus status2 = new UpdatePadPApplyDto.DossierStatus(19, new ArrayList<>());
                status2.getName().add(new Translate((short)228, "Từ chối"));
                return status2;
            case 3:
                UpdatePadPApplyDto.DossierStatus status3 = new UpdatePadPApplyDto.DossierStatus(2, new ArrayList<>());
                status3.getName().add(new Translate((short)228, "Đang xử lý"));
                return status3;
            case 4:
                UpdatePadPApplyDto.DossierStatus status4 = new UpdatePadPApplyDto.DossierStatus(6, new ArrayList<>());
                status4.getName().add(new Translate((short)228, "Đã hủy"));
                return status4;
            case 5:
                UpdatePadPApplyDto.DossierStatus status5 = new UpdatePadPApplyDto.DossierStatus(6, new ArrayList<>());
                status5.getName().add(new Translate((short)228, "Đã hủy"));
                return status5;
            case 6:
                UpdatePadPApplyDto.DossierStatus status6 = new UpdatePadPApplyDto.DossierStatus(4, new ArrayList<>());
                status6.getName().add(new Translate((short)228, "Có kết quả"));
                return status6;
        }
        UpdatePadPApplyDto.DossierStatus status0 = new UpdatePadPApplyDto.DossierStatus(0, new ArrayList<>());
        status0.getName().add(new Translate((short)228, "Chờ tiếp nhận"));
        return status0;
    }

    public PadPApplyDto.ProcessDto getProcessByProcedure(String procedureId) {
        String processUrl = microservice.basepadUri("/procedure-process-definition/--apply-online?procedure-id=" + procedureId).toUriString();
        log.info("getProcess "+processUrl);
        PadPApplyDto.ProcessDto[] process = null;
        try {
            process = MicroserviceExchange.getNoAuth(this.getRestTemplate(), processUrl, PadPApplyDto.ProcessDto[].class);
            log.info("getProcess "+process);
        } catch (Exception e) {
            log.error("getProcess "+e.getMessage());
        }

        if(process != null && process.length > 0) {
            return process[0];
        }
        return null;
    }
}
