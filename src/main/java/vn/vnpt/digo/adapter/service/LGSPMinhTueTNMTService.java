package vn.vnpt.digo.adapter.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.dto.Integration.CodeName;
import vn.vnpt.digo.adapter.dto.minhtue.TokenResDto;
import vn.vnpt.digo.adapter.dto.minhtue.tntm.*;
import vn.vnpt.digo.adapter.dto.minhtue.tntm.TaiLieuNop;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierFindDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.AgencyCodeName;
import vn.vnpt.digo.adapter.pojo.Id;
import vn.vnpt.digo.adapter.pojo.IntegratedService;
import vn.vnpt.digo.adapter.pojo.ProcedureForm;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.util.Microservice;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Date;
import java.util.HashMap;
import java.util.Base64;

@Service
@Component
public class LGSPMinhTueTNMTService {
    private ObjectId serviceId = new ObjectId("5f7c16069abb62f511891037");

    Logger logger = LoggerFactory.getLogger(LGSPMinhTueTNMTService.class);
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private IntegratedConfigurationService configurationService;
    @Autowired
    private Translator translator;
    @Autowired
    private IntegratedLogsService integratedLogsService;
    @Value(value = "${lgspminhtue.kgg.configId}")
    private String lgspKGGConfigId;

    @Value(value = "${lgspminhtue.tntm.configid}")
    private String configId;

    @Value(value = "${lgsp.tnmt.hcm.configid}")
    private String configIdHCM;

    @Value(value = "${lgsp.tnmt.hcm.schedule.enable}")
    private Boolean enableScheduleReceivingDocument;

    @Value(value = "${lgsp.tnmt.ishcm}")
    private Boolean isHCM;

    @Value(value = "${digo.oidc.client-id}")
    private String clientId;

    @Value(value = "${digo.oidc.client-secret}")
    private String clientSecret;

    @Value(value = "${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String tokenUrl;
    @Autowired
    private Microservice microservice;
    private final ObjectId SERVICE_ID = new ObjectId("5f7c16069abb62f511890003");

    @Value(value = "${lgsp.tnmt.isgli}")
    private Boolean isGLI;
    @Value(value = "${lgsp.tnmt.isqnm}")
    private Boolean isQNM;

    @Value(value = "${lgsp.tnmt.hgg}")
    private Boolean isHGG; // IGATESUPP-79592 Hỗ trợ Lấy dữ liệu Object khi gọi api LGSP DVCTY

    @Value(value = "${lgspminhtue.tntm.configAgencyBoTNMT.enable}")
    private boolean configAgencyBoTNMT;
    private TokenResDto getToken(String tokenUrl, String consumerKey, String consumerSecret) {
        String strConsumer = consumerKey + ":" + consumerSecret;
        byte[] base64Consumer = Base64.getEncoder().encode(strConsumer.getBytes());
        String auth = new String(base64Consumer);

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(tokenUrl);
        uriBuilder.queryParam("grant_type", "client_credentials");
        UriComponents uriComponents = uriBuilder.encode().build();

        ResponseEntity<Object> result;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBasicAuth(auth);
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<?> request = new HttpEntity<>(headers);
            result = restTemplate.exchange(
                    uriComponents.toUriString(),
                    HttpMethod.POST, request, Object.class);
            logger.info("Http result:");
            System.out.println(result);
            TokenResDto token = GsonUtils.copyObject(result.getBody(), TokenResDto.class);
            return token;
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
            throw e;
        }
    }

    public TokenResDto getTokenHCM(String uri, String lgspaccesstoken){
        logger.info("Start get accesstoken GTVT !!");
        String URL = uri;
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", MediaType.valueOf("application/x-www-form-urlencoded").toString());
        headers.add("lgspaccesstoken", lgspaccesstoken);
        MultiValueMap<String, String> map= new LinkedMultiValueMap<String, String>();
        map.add("grant_type", "client_credentials");
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<MultiValueMap<String, String>>(map, headers);

        ResponseEntity<Object> result = restTemplate.exchange(URL, HttpMethod.POST, entity, Object.class);
        TokenResDto token = GsonUtils.copyObject(result.getBody(), TokenResDto.class);
        logger.info("Access Token GTVT: " + result.getBody());
        return token;
    }

    public ArrayList<IdDto> getListDossierByDate(Map<String, Object> body) throws Exception {
        System.out.println(body);
        IdDto  idDosserNew = new IdDto();
        IntegratedConfigurationDto config;
        String url="";
        String token="";
        String lgspaccesstoken ="";
        if(isHCM){
            config = configurationService.getConfig(new ObjectId(configIdHCM));
            if (Objects.isNull(config)) {
                throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                        HttpServletResponse.SC_NOT_FOUND);
            }

            logger.info("getConfig: ");
            String gatewayToken = config.getParametersValue("gateway-token");
            lgspaccesstoken = config.getParametersValue("lgspaccesstoken");
            url = config.getParametersValue("listDossierUrl");
            token = getTokenHCM(gatewayToken, lgspaccesstoken).getAccessToken();
        }else{
            config = configurationService.getConfig(new ObjectId(configId));
            if (Objects.isNull(config)) {
                throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                        HttpServletResponse.SC_NOT_FOUND);
            }

            logger.info("getConfig: ");
            String gatewayToken = config.getParametersValue("gateway-token");
            String consumerKey = config.getParametersValue("consumer-key");
            String consumerSecret = config.getParametersValue("consumer-secret");
            url = config.getParametersValue("listDossierUrl");
            token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
        }

        ResponseEntity<String> result = null;

        ResponseEntity<Object> response = null; // IGATESUPP-79592 Hỗ trợ Lấy dữ liệu Object khi gọi api LGSP DVCTY

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            headers.setContentType(MediaType.APPLICATION_JSON);
            if(isHCM){
                headers.add("lgspaccesstoken", lgspaccesstoken);
            }

            HttpEntity<?> request = new HttpEntity<>(body, headers);
            if (isHGG) { // IGATESUPP-79592 Hỗ trợ Lấy dữ liệu Object khi gọi api LGSP DVCTY
                response = restTemplate.exchange(url, HttpMethod.POST, request, Object.class);
            } else {
                result = restTemplate.exchange(
                        url,
                        HttpMethod.POST, request, String.class);
                logger.info("Http result:");
                HttpStatus status = result.getStatusCode();
                System.out.println(result.getStatusCode());
            }
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
            //throw e;
        }

        try {
            //bo sung luu logs
            if(config.getId().toHexString().equals(lgspKGGConfigId)) {
                //luu log

                IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                IntegratedService serviceNew = new IntegratedService(this.serviceId, "LGSP Minh Tue");
                configNew.setService(serviceNew);
                //save log
                integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(), "laydanhsachhosotheongay", "tai nguyen moi truong"), 0, result == null ? "" : result.getBody(), new Gson().toJson(body));

            }
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
        }

        if (isHGG) { // IGATESUPP-79592 Hỗ trợ Lấy dữ liệu Object khi gọi api LGSP DVCTY
            if (!Objects.isNull(response) && !Objects.isNull(response.getBody())) {
                Gson gson = new Gson();

                String json = gson.toJson(response.getBody());

                List<TNMTListDossierDto> tnmtList = gson.fromJson(json, new TypeToken<List<TNMTListDossierDto>>(){}.getType());

                ArrayList<IdDto> idDosserArray = new ArrayList<>();
                for (TNMTListDossierDto dto : tnmtList) {
                    if (dto.getTrangThaiHoSo().equalsIgnoreCase("1")) {
                        IdDto idDosser = new IdDto();
                        idDosser = synchronizeDossier(dto);
                        idDosserArray.add(idDosser);
                    }
                }
                return idDosserArray;
            }
        }

        Gson gson = new Gson();
        Type founderListType = new TypeToken<ArrayList<TNMTListDossierDto>>(){}.getType();
        List<TNMTListDossierDto> res;
         res = gson.fromJson(result.getBody(),founderListType);
        ArrayList<IdDto> idDosserArray = new ArrayList<>();
         if(Objects.nonNull(res)){

             for(TNMTListDossierDto dto : res){
                 if(dto.getTrangThaiHoSo().equalsIgnoreCase("1")) {
                     idDosserNew = synchronizeDossier(dto);
                     idDosserArray.add(idDosserNew);
                 }
             }
         }
        return idDosserArray;
    }

   @Scheduled(cron = "0 */15 * * * *") //15 phut chay 1 lan tai giay thu 0
   ////@SchedulerLock(name = "getListDossierDefault", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public List<TNMTListDossierDto> getListDossierDefault() throws Exception {
       logger.info("getListDossierDefault: ");
        IntegratedConfigurationDto config;
        config = configurationService.getConfig(new ObjectId(configId));
       logger.info("config: " + config);
        var sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date();
        String dateDefaultValue = sdf.format(new Date());
        String denNgay = sdf.format(new Date(date.getTime() + (1000 * 60 * 60 * 24)));
        String tomorrow = sdf.format(new Date(date.getTime() - (1000 * 60 * 60 * 24)));
        String maTinh = config.getParametersValue("ma-tinh");
        HashMap<String, Object> body = new HashMap<>();
        body.put("matinh", maTinh);
        body.put("tungay", tomorrow);
        body.put("denngay", denNgay);
        // body.put("tungay", "20250321104022");
        //body.put("denngay", "20250421104022");
        logger.info("body: " + body);
        logger.info("getConfig: ");
        String gatewayToken = config.getParametersValue("gateway-token");
        String consumerKey = config.getParametersValue("consumer-key");
        String consumerSecret = config.getParametersValue("consumer-secret");
        String url = config.getParametersValue("listDossierUrl");
        String token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
        ResponseEntity<String> result = null;
        ResponseEntity<Object> response = null; // IGATESUPP-79592 Hỗ trợ Lấy dữ liệu Object khi gọi api LGSP DVCTY

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            headers.setContentType(MediaType.APPLICATION_JSON);
            logger.info("isHGG: " + isHGG);
            HttpEntity<?> request = new HttpEntity<>(body, headers);
            if (isHGG) { // IGATESUPP-79592 Hỗ trợ Lấy dữ liệu Object khi gọi api LGSP DVCTY
                response = restTemplate.exchange(url, HttpMethod.POST, request, Object.class);
            } else {
                result = restTemplate.exchange(
                        url,
                        HttpMethod.POST, request, String.class);
                logger.info("Http result:");
                HttpStatus status = result.getStatusCode();
                System.out.println(result.getStatusCode());
            }
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
            //throw e;
        }

        try {
            //bo sung luu logs
            if(config.getId().toHexString().equals(lgspKGGConfigId)) {
                //luu log

                IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                IntegratedService serviceNew = new IntegratedService(this.serviceId, "LGSP Minh Tue");
                configNew.setService(serviceNew);
                //save log
                integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(), "laydanhsachhosomacdich", "tai nguyen moi truong"), 0, result == null ? "" : result.getBody(), new Gson().toJson(body));

            }
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
        }

       if (isHGG) { // IGATESUPP-79592 Hỗ trợ Lấy dữ liệu Object khi gọi api LGSP DVCTY
           if (!Objects.isNull(response) && !Objects.isNull(response.getBody())) {
               Gson gson = new Gson();

               String json = gson.toJson(response.getBody());

               List<TNMTListDossierDto> tnmtList = gson.fromJson(json, new TypeToken<List<TNMTListDossierDto>>(){}.getType());

               for (TNMTListDossierDto dto : tnmtList) {
                   if (dto.getTrangThaiHoSo().equalsIgnoreCase("1")) {
                       scheduledDossier(dto);
                   }
               }
               return tnmtList;
           }
       }

        Gson gson = new Gson();
        Type founderListType = new TypeToken<ArrayList<TNMTListDossierDto>>(){}.getType();
        List<TNMTListDossierDto> res;
        res = gson.fromJson(result.getBody(),founderListType);
       logger.info("danhsachhoso: " + res);
        if(Objects.nonNull(res)){

            for(TNMTListDossierDto dto : res){
                if(dto.getTrangThaiHoSo().equalsIgnoreCase("1")) {
                    synchronizeDossier(dto);
                }
            }
        }
        return res;
    }

    @Scheduled(cron = "${lgsp.tnmt.hcm.schedule.moc.get-dossier.cron}") //15 phut chay 1 lan tai giay thu 0
    ////@SchedulerLock(name = "getListDossierDefaultHCM", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public List<TNMTListDossierDto> getListDossierDefaultHCM() throws Exception {
        if (enableScheduleReceivingDocument) {
            IntegratedConfigurationDto config;
            config = configurationService.getConfig(new ObjectId(configIdHCM));
            var sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            Date date = new Date();
            String dateDefaultValue = sdf.format(new Date());
            String denNgay = sdf.format(new Date(date.getTime() + (1000 * 60 * 60 * 24)));
            String tomorrow = sdf.format(new Date(date.getTime() - (1000 * 60 * 60 * 24)));
            String maTinh = config.getParametersValue("ma-tinh");
            HashMap<String, Object> body = new HashMap<>();
            body.put("matinh", maTinh);
            body.put("tungay", tomorrow);
            body.put("denngay", denNgay);
//            body.put("tungay", "20210101010101");
//            body.put("denngay", "20230101010101");

            logger.info("getConfig: ");
            String gatewayToken = config.getParametersValue("gateway-token");
            String lgspaccesstoken = config.getParametersValue("lgspaccesstoken");
            String url = config.getParametersValue("listDossierUrl");
            String token = getTokenHCM(gatewayToken, lgspaccesstoken).getAccessToken();
            ResponseEntity<String> result = null;

            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setBearerAuth(token);
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.add("lgspaccesstoken", lgspaccesstoken);

                HttpEntity<?> request = new HttpEntity<>(body, headers);
                result = restTemplate.exchange(
                        url,
                        HttpMethod.POST, request, String.class);
                logger.info("Http result:");
                HttpStatus status = result.getStatusCode();
                System.out.println(result.getStatusCode());
            } catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
                //throw e;
            }

            try {
                //bo sung luu logs
                if (config.getId().toHexString().equals(lgspKGGConfigId)) {
                    //luu log

                    IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                    IntegratedService serviceNew = new IntegratedService(this.serviceId, "LGSP Minh Tue");
                    configNew.setService(serviceNew);
                    //save log
                    integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(), "laydanhsachhosomacdich", "tai nguyen moi truong"), 0, result == null ? "" : result.getBody(), new Gson().toJson(body));

                }
            } catch (Exception e) {
                logger.error("Error calling http: ", e.getMessage());
            }

            Gson gson = new Gson();
            Type founderListType = new TypeToken<ArrayList<TNMTListDossierDto>>() {
            }.getType();
            List<TNMTListDossierDto> res;
            res = gson.fromJson(result.getBody(), founderListType);
            logger.info("danhsachhoso: " + res);
            if (Objects.nonNull(res)) {

                for (TNMTListDossierDto dto : res) {
                    if (dto.getTrangThaiHoSo().equalsIgnoreCase("1")) {
                        scheduledDossier(dto);
                    }
                }
            }
            return res;
        }
        return null;
    }

    public TNMTUpdateDossierDto updateDossier(Map<String, Object> body) throws Exception {
        IntegratedConfigurationDto config;
        String url="";
        String token="";
        String lgspaccesstoken ="";
        if(isHCM){
            config = configurationService.getConfig(new ObjectId(configIdHCM));
            if (Objects.isNull(config)) {
                throw new DigoHttpException(11000, new String[] { translator.toLocale("lang.word.configuration") },
                        HttpServletResponse.SC_NOT_FOUND);
            }

            logger.info("getConfig: ");
            String gatewayToken = config.getParametersValue("gateway-token");
            lgspaccesstoken = config.getParametersValue("lgspaccesstoken");
            url = config.getParametersValue("updateDossierUrl");
            token = getTokenHCM(gatewayToken, lgspaccesstoken).getAccessToken();
        }else {
            config = configurationService.getConfig(new ObjectId(configId));

            logger.info("getConfig: ");
            String gatewayToken = config.getParametersValue("gateway-token");
            String consumerKey = config.getParametersValue("consumer-key");
            String consumerSecret = config.getParametersValue("consumer-secret");
            url = config.getParametersValue("updateDossierUrl");
            token = getToken(gatewayToken, consumerKey, consumerSecret).getAccessToken();
        }
        ResponseEntity<String> result = null;
        ResponseEntity<Object> response = null;

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            headers.setContentType(MediaType.APPLICATION_JSON);
            if(isHCM){
                headers.add("lgspaccesstoken", lgspaccesstoken);
            }
            HttpEntity<?> request = new HttpEntity<>(body, headers);
            if (isHGG) { // IGATESUPP-79592 Hỗ trợ Lấy dữ liệu Object khi gọi api LGSP DVCTY
                response = restTemplate.exchange(url, HttpMethod.POST, request, Object.class);
            } else {
                result = restTemplate.exchange(
                        url,
                        HttpMethod.POST, request, String.class);
                logger.info("Http result:");
                HttpStatus status = result.getStatusCode();
                System.out.println(result.getStatusCode());
            }
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
            //throw e;
        }

        try {
            //bo sung luu logs
            if(config.getId().toHexString().equals(lgspKGGConfigId)) {
                //luu log

                IntegratedConfigurationDto configNew = new IntegratedConfigurationDto();
                IntegratedService serviceNew = new IntegratedService(this.serviceId, "LGSP Minh Tue");
                configNew.setService(serviceNew);
                //save log
                integratedLogsService.save(configNew, new IdCodeNameSimpleDto(new ObjectId(), "laydanhsachhosotheongay", "tai nguyen moi truong"), 0, result == null ? "" : result.getBody(), new Gson().toJson(body));

            }
        } catch (Exception e) {
            logger.error("Error calling http: ", e.getMessage());
        }

        if (isHGG) {
            if (!Objects.isNull(response) && !Objects.isNull(response.getBody())) {
                Gson gson = new Gson();
                String json = gson.toJson(response.getBody());
                TNMTUpdateDossierDto res = gson.fromJson(json, TNMTUpdateDossierDto.class);
                return res;
            }
        }

        Gson gson = new Gson();
        TNMTUpdateDossierDto res = gson.fromJson(result.getBody(), TNMTUpdateDossierDto.class);
        return res;
    }

    private RestTemplate getOAuth2RestTemplate(IntegratedConfigurationDto config) {
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(config.getParametersValue("pad-sso-url"));
        details.setClientId(config.getParametersValue("pad-client-id"));
        details.setClientSecret(config.getParametersValue("pad-client-secret"));
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }
    private RestTemplate getRestTemplate() {
        ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
        details.setAccessTokenUri(this.tokenUrl + "/protocol/openid-connect/token");
        details.setClientId(this.clientId);
        details.setClientSecret(this.clientSecret);
        details.setGrantType("client_credentials");
        return new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
    }
    public IdDto synchronizeDossier(TNMTListDossierDto data) {
        IdDto newDossier = new IdDto();
            try {

                Gson gson = new Gson();
                String eform = "";
                logger.info(data.getMaHoSo());
                // Convert object data
                PadPApplyBoTNMTDto callPad = new PadPApplyBoTNMTDto();
                callPad.setCode(data.getMaHoSo());
                String[] splits = data.getMaHoSo().split("-");
                String codeAgency = splits[0];
                if(configAgencyBoTNMT){
                    String basedataUri =  microservice.basedataUri("/agency/").toUriString();
                    //String basedataUri =  "http://localhost:8888/agency/";
                    String urlAgencyDetail = basedataUri +"/lgsp-minh-tue-tnmt/--by-code-agency-ministry?code-agency-ministry="+codeAgency;
                    PadPApplyBoTNMTDto.AgencyDto agency = MicroserviceExchange.getNoAuth(this.getRestTemplate(), urlAgencyDetail, PadPApplyBoTNMTDto.AgencyDto.class);
                    if(agency.getCode() != null){
                        codeAgency = agency.getCode();
                    }
                }
                var token = this.getRestTemplate();
                String procedureUrl = microservice.basepadUri("/procedure/").toUriString() + "codeBoTNMT/--full?codeBoTNMT=" + data.getMaTTHC()+"&codeAgency="+codeAgency;
                //String procedureUrl = "http://localhost:8181/procedure/code/--full?code=" + data.getString("MaThuTuc");
                PadPApplyBoTNMTDto.ProcedureDto procedure = new PadPApplyBoTNMTDto.ProcedureDto();
                try {
                    procedure = MicroserviceExchange.getNoAuth(this.getRestTemplate(), procedureUrl, PadPApplyBoTNMTDto.ProcedureDto.class);
                } catch (Exception e) {
                    logger.info("MinhTueQNM-synchronizeDossier: không thể tìm thấy được thông tin cơ quan hoặc thủ tục của cơ quan: "+ codeAgency +", Thủ tục:"+data.getMaTTHC() , e);
                    throw new RuntimeException(e);
                }

                PadPApplyBoTNMTDto.ProcedureDto procedureDto = new PadPApplyBoTNMTDto.ProcedureDto(procedure.getId(), procedure.getCode(), procedure.getTranslate(), procedure.getSector(), procedure.getAgency(), procedure.getProcessDefinitionCount());
                callPad.setProcedure(procedureDto);
                logger.info("thu tuc" + procedure);
                //set Agency
                PadPApplyBoTNMTDto.AgencyDto agencyDto = new PadPApplyBoTNMTDto.AgencyDto();
                if(Objects.nonNull(procedure.getAgency().get(0))){
                    PadPApplyBoTNMTDto.AgencyDto parent = new PadPApplyBoTNMTDto.AgencyDto();
                    parent.setId(procedure.getAgency().get(0).getId().toString());
                    parent.setName(procedure.getAgency().get(0).getName());
                    agencyDto.setParent(parent);
//                    AgencyDto.setId(procedure.getAgency().get(0).getId().toString());
//                    AgencyDto.setCode(procedure.getAgency().get(0).getCode());
//                    AgencyDto.setName(procedure.getAgency().get(0).getName());
                }
                callPad.setAgency(agencyDto);

                //set procedure-process-definition
                if(procedure.getProcessDefinitionCount() > 0){
                    String processUrl = microservice.basepadUri("/procedure-process-definition/").toUriString() + "idProcedure-boTNMT?idProcedure="+procedure.getId();
                    PadPApplyBoTNMTDto.ProcessDto process = MicroserviceExchange.getNoAuth(this.getRestTemplate(), processUrl, PadPApplyBoTNMTDto.ProcessDto.class);
                    callPad.setProcedureProcessDefinition(process);
                    logger.info("quy trinh thu tuc" + process);
                    try {
                        JSONObject json = new org.json.JSONObject(GsonUtils.getJson(process.getProcessDefinition()));
                        if(json.getJSONObject("applicantEForm").getString("id") != null)
                        {
                            eform = json.getJSONObject("applicantEForm").getString("id");
                        }
                    }
                    catch (Exception e) {
                        logger.info("MinhTueQNM-synchronizeDossier: lỗi map eform " + e);
                    }
                }

                // set ApplyMethod
                if(data.getKenhThucHien()== 1){
                    PadPApplyBoTNMTDto.ApplyMethod ApplyMethod = new PadPApplyBoTNMTDto.ApplyMethod("1", "Trực tiếp");
                    callPad.setApplyMethod(ApplyMethod);
                }
                if(data.getKenhThucHien()== 2){
                    PadPApplyBoTNMTDto.ApplyMethod ApplyMethod = new PadPApplyBoTNMTDto.ApplyMethod("0", "Trực tuyến");
                    callPad.setApplyMethod(ApplyMethod);
                }
                if(data.getKenhThucHien()== 3){
                    PadPApplyBoTNMTDto.ApplyMethod ApplyMethod = new PadPApplyBoTNMTDto.ApplyMethod("2", "Qua bưu điện");
                    callPad.setApplyMethod(ApplyMethod);
                }

                //Set dossierReceivingKind
                if(data.getHinhThuc() == "1" ){
                    PadPApplyBoTNMTDto.RecvKindDto dossierReceivingKind = new PadPApplyBoTNMTDto.RecvKindDto();
                    dossierReceivingKind.setId("5fd1c7591b53d8779bc9ea78");
                    List<PadPApplyBoTNMTDto.NameDto> name = new ArrayList<>() {
                        {
                            add(new PadPApplyBoTNMTDto.NameDto((short) 228, "Trực tuyến"));
                            add(new PadPApplyBoTNMTDto.NameDto((short) 46, "Online"));

                        }
                    };
                    dossierReceivingKind.setName(name);
                    callPad.setDossierReceivingKind(dossierReceivingKind);
                }


                // Get applicant
                PadPApplyBoTNMTDto.ApplicantDataDto applicant = new PadPApplyBoTNMTDto.ApplicantDataDto();
                applicant.setOwnerFullname(data.getChuHoSo().getTen());
                applicant.setIdentityNumber(data.getChuHoSo().getSoCMND());
                applicant.setPhoneNumber(data.getChuHoSo().getSoDienThoai());
                applicant.setEmail(data.getChuHoSo().getEmail());
                applicant.setAddress(data.getChuHoSo().getDiaChi());
                applicant.setPhone(data.getSoDienThoai());
//                applicant.setEmail(data.getEmail());
                applicant.setFax(data.getFax());
                applicant.setFullname(data.getChuHoSo().getTen());
                applicant.setPhone(data.getChuHoSo().getSoDienThoai());
                applicant.setSobiado(data.getDonDangKy().getSoPhatHanh());

                /// set Thua dat
                try {
                    PadPApplyBoTNMTDto.AddressDetailDto province2 = new PadPApplyBoTNMTDto.AddressDetailDto();
                    province2.setLabel(data.getDonDangKy().getDiaChiThuaDat().getTenTinh());
                    province2.setValue(data.getDonDangKy().getDiaChiThuaDat().getMaTinh());
                    applicant.setProvince2(province2);

                    try{
                        PadPApplyBoTNMTDto.AddressDetailDto district2 = new PadPApplyBoTNMTDto.AddressDetailDto();
                        district2.setLabel(data.getDonDangKy().getDiaChiThuaDat().getTenHuyen());
                        district2.setValue(data.getDonDangKy().getDiaChiThuaDat().getMaHuyen());
                        applicant.setDistrict2(district2);
                    }catch (Exception e){

                    }
                    try{

                    }catch (Exception e){
                        PadPApplyBoTNMTDto.AddressDetailDto village2 = new PadPApplyBoTNMTDto.AddressDetailDto();
                        village2.setLabel(data.getDonDangKy().getDiaChiThuaDat().getTenXa());
                        village2.setValue(data.getDonDangKy().getDiaChiThuaDat().getMaXa());
                        applicant.setVillage2(village2);
                    }




                    applicant.setDiaChiThuaDat(data.getDonDangKy().getDiaChiThuaDat().getDiaChiChiTiet());
                }
                catch (Exception e) {
                    logger.info("MinhTueQNM-synchronizeDossier: lỗi thửa đất " + e);
                }
                if(data.getKenhThucHien()== 1){
                    applicant.setHinhThucNop(1);
                }
                if(data.getKenhThucHien()== 2){
                    applicant.setHinhThucNop(2);
                }
                if(data.getKenhThucHien()== 3){
                    applicant.setHinhThucNop(4);
                }
                PadPApplyBoTNMTDto.ApplicantDto applicantMain = new PadPApplyBoTNMTDto.ApplicantDto();
                if(eform != ""){
                    applicantMain.setEformId(eform);
                }
                applicantMain.setData(applicant);
                callPad.setApplicant(applicantMain);

                //set OtherInForBoTNMT
                OtherInForBoTNMT otherInForBoTNMT = new OtherInForBoTNMT();
                otherInForBoTNMT.setIsBoTNMT("true");
                otherInForBoTNMT.setMaHuyen(data.getMaHuyen());
                otherInForBoTNMT.setLoaiDoiTuong(data.getLoaiDoiTuong());
                otherInForBoTNMT.setMaDoiTuong(data.getMaDoiTuong());
                otherInForBoTNMT.setThongTinKhac(data.getThongTinKhac());
                otherInForBoTNMT.setTrichYeuHoSo(data.getTrichYeuHoSo());
                otherInForBoTNMT.setThongTinTra(data.getThongTinTra());
                otherInForBoTNMT.setGhiChu(data.getGhiChu());
                otherInForBoTNMT.setDonDangKy(data.getDonDangKy());
                callPad.setOtherInForBoTNMT(otherInForBoTNMT);


                //get acceptedDate
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
                if(Objects.nonNull(data.getNgayTiepNhan()) && !data.getNgayTiepNhan().equalsIgnoreCase("")){
                    Date ngayTiepNhan = formatter.parse(data.getNgayTiepNhan());
                    callPad.setAcceptedDate(ngayTiepNhan);
                }
                //get appointmentDate
                if(Objects.nonNull(data.getNgayHenTra()) && !data.getNgayHenTra().equalsIgnoreCase("")){
                    Date ngayHenTra = formatter.parse(data.getNgayHenTra());
                    callPad.setAppointmentDate(ngayHenTra);
                }
                //get returnedDate
                if(Objects.nonNull(data.getNgayTra())&& !data.getNgayTra().equalsIgnoreCase("")) {
                    Date ngayTra = formatter.parse(data.getNgayTra());
                    callPad.setAppointmentDate(ngayTra);
                }
                List<PadPApplyBoTNMTDto.AttachmentDto> lstAttachTaiLieuNop = new ArrayList<>();
                if (data.getTaiLieuNop() != null) {
                    String uploadUrl = microservice.filemanUri("/file/--multiple").toUriString();
                    MultiValueMap<String, Object> requestMap = new LinkedMultiValueMap<>();
                    for (TaiLieuNop item : data.getTaiLieuNop()) {
                        String fileUrl = item.getDuongDanTaiTepTin();
                        logger.info("save file update/nhanhsdvcqg form: " + fileUrl);
                        String shortUrl = "file";
                        String url = microservice.filemanUri(shortUrl).toUriString() + "/--by-url?file-url=" + URLEncoder.encode(fileUrl, StandardCharsets.UTF_8);
                        logger.info("save file: " + url);
                        var na = this.getRestTemplate();
                        Id idFile = MicroserviceExchange.postJsonNoAuth(this.getRestTemplate(), url, null, Id.class);
                        logger.info(idFile.toString());
                        PadPApplyBoTNMTDto.AttachmentDto att = new PadPApplyBoTNMTDto.AttachmentDto(
                                idFile.getId().toHexString(), item.getTenTepDinhKem(), null, null
                        );
                        lstAttachTaiLieuNop.add(att);

                        try
                        {
                            String urlRename = microservice.filemanUri(shortUrl).toUriString() + "/"+idFile.getId().toHexString()+"/rename?name=" + item.getTenTepDinhKem();
                            AffectedRowsDto resultRename = MicroserviceExchange.putJsonNoAuth(this.getRestTemplate(), urlRename, null, AffectedRowsDto.class);
                        }
                        catch(Exception e){

                        }
                    };

                }

                String newDossierUrl = microservice.padmanUri("/dossier/--apply-online").toUriString();
                // Pretty res and return
                 newDossier = MicroserviceExchange.postJsonNoAuth(this.getRestTemplate(), newDossierUrl, callPad, IdDto.class);
                System.out.println(newDossier);
                newDossier.setMessage("Thanh cong "+  data.getMaHoSo());
                logger.info("Luu thong tin vao dossier file");
                ArrayList<DossierFormFileDto> listDossierFormFileDto = new ArrayList<>();
                if (lstAttachTaiLieuNop.size() > 0) {
                    DossierFormFileDto dossierFormFile = this.addTaiLieuNop(newDossier.getId().toHexString(), callPad.getProcedureProcessDefinition().getId().toString(), lstAttachTaiLieuNop);
                    listDossierFormFileDto.add(dossierFormFile);
                }
                if (listDossierFormFileDto.size() > 0) {
                    String newDossierFormFileURL = microservice.padmanUri("/dossier-form-file").toUriString() + "/--by-dossier?dossier-id=" + newDossier.getId().toHexString();
                    // Pretty res and return
                    AffectedRowsDto newDossierFormFile = MicroserviceExchange.putJsonNoAuth(this.getRestTemplate(), newDossierFormFileURL, listDossierFormFileDto, AffectedRowsDto.class);
                    System.out.println(newDossierFormFile);
                }
                return newDossier;

            } catch (Exception e) {
                newDossier.setMessage(data.getMaHoSo()+ " / " + data.getMaTTHC() + " ** ERR ** "  + e.getMessage());
                logger.info("MinhTueQNM-synchronizeDossier: Synchronize dossier fail " + e);
            }
        return newDossier;
    }

    public DossierFormFileDto addTaiLieuNop(String dossierId, String processId ,List<PadPApplyBoTNMTDto.AttachmentDto> lstAttach){
        DossierFormFileDto dossierFormFile = new DossierFormFileDto();
        // set dossier
        DossierFormFileDto.Id dossier = new DossierFormFileDto.Id();
        dossier.setId(dossierId);
        dossierFormFile.setDossier(dossier);
        // set procedureForm
        ProcedureForm procedureForm = new ProcedureForm();
        procedureForm.setId("624ea3f1ce65a21305f3516b");
        String textFormName = new String("T\u00e0i li\u1ec7u n\u1ed9p t\u1eeb dvcqg".getBytes(StandardCharsets.UTF_8),
                Charset.forName("UTF-8"));
        procedureForm.setName(textFormName);
        dossierFormFile.setProcedureForm(procedureForm);
        // set case
        DossierFormFileDto.Id caze = new DossierFormFileDto.Id();
        caze.setId(processId);
        dossierFormFile.setCaze(caze);

        dossierFormFile.setOrder(1);
        dossierFormFile.setQuantity(1);

        // set detail
        String textDetail = new String("B\u1ea3n sao".getBytes(StandardCharsets.UTF_8),
                Charset.forName("UTF-8"));
        DossierFormFileDto.ProcedureFormDetail detail = new DossierFormFileDto.ProcedureFormDetail(
                new  DossierFormFileDto.ProcedureFormDetailType("623462c0f2e2ad4ed5787167",textDetail), 1
        );
        dossierFormFile.setDetail(detail);
        dossierFormFile.setFile((ArrayList) lstAttach);

        return dossierFormFile;
    };

    public String getConfig(){
        IntegratedConfigurationDto config;
        if(isHCM) {
            config = configurationService.getConfig(new ObjectId(configIdHCM));
        }else{
            config = configurationService.getConfig(new ObjectId(configId));
        }
        String maTinh = config.getParametersValue("ma-tinh");
        return maTinh;
    };

    public IdDto scheduledDossier(TNMTListDossierDto data) {

        try {
            Gson gson = new Gson();
            String eform = "";
            logger.info(data.getMaHoSo());
            // Convert object data
            PadPApplyBoTNMTDto callPad = new PadPApplyBoTNMTDto();
            callPad.setCode(data.getMaHoSo());
            String[] splits = data.getMaHoSo().split("-");
            String procedureUrl = microservice.basepadUri("/procedure/").toUriString() + "codeBoTNMT/--full?codeBoTNMT=" + data.getMaTTHC()+"&codeAgency="+splits[0];
            //String procedureUrl = "http://localhost:8181/procedure/code/--full?code=" + data.getString("MaThuTuc");
            PadPApplyBoTNMTDto.ProcedureDto procedure = MicroserviceExchange.getNoAuth(this.getRestTemplate(), procedureUrl, PadPApplyBoTNMTDto.ProcedureDto.class);
            PadPApplyBoTNMTDto.ProcedureDto procedureDto = new PadPApplyBoTNMTDto.ProcedureDto(procedure.getId(), procedure.getCode(), procedure.getTranslate(), procedure.getSector(), procedure.getAgency(), procedure.getProcessDefinitionCount());
            callPad.setProcedure(procedureDto);
            //set Agency
            PadPApplyBoTNMTDto.AgencyDto agencyDto = new PadPApplyBoTNMTDto.AgencyDto();
            if(Objects.nonNull(procedure.getAgency().get(0))){
                PadPApplyBoTNMTDto.AgencyDto parent = new PadPApplyBoTNMTDto.AgencyDto();
                parent.setId(procedure.getAgency().get(0).getId().toString());
                parent.setName(procedure.getAgency().get(0).getName());
                agencyDto.setParent(parent);
//                    AgencyDto.setId(procedure.getAgency().get(0).getId().toString());
//                    AgencyDto.setCode(procedure.getAgency().get(0).getCode());
//                    AgencyDto.setName(procedure.getAgency().get(0).getName());
            }
            callPad.setAgency(agencyDto);

            //set procedure-process-definition
            if(procedure.getProcessDefinitionCount() > 0){
                String processUrl = microservice.basepadUri("/procedure-process-definition/").toUriString() + "idProcedure-boTNMT?idProcedure="+procedure.getId();
                PadPApplyBoTNMTDto.ProcessDto process = MicroserviceExchange.getNoAuth(this.getRestTemplate(), processUrl, PadPApplyBoTNMTDto.ProcessDto.class);
                callPad.setProcedureProcessDefinition(process);
                try {
                    JSONObject json = new org.json.JSONObject(GsonUtils.getJson(process.getProcessDefinition()));
                    if(json.getJSONObject("applicantEForm").getString("id") != null)
                    {
                        eform = json.getJSONObject("applicantEForm").getString("id");
                    }
                }
                catch (Exception e) {
                    logger.info("MinhTueQNM-synchronizeDossier: lỗi map eform " + e);
                }
            }

            // set ApplyMethod
            if(data.getKenhThucHien()== 1){
                PadPApplyBoTNMTDto.ApplyMethod ApplyMethod = new PadPApplyBoTNMTDto.ApplyMethod("1", "Trực tiếp");
                callPad.setApplyMethod(ApplyMethod);
            }
            if(data.getKenhThucHien()== 2){
                PadPApplyBoTNMTDto.ApplyMethod ApplyMethod = new PadPApplyBoTNMTDto.ApplyMethod("0", "Trực tuyến");
                callPad.setApplyMethod(ApplyMethod);
            }
            if(data.getKenhThucHien()== 3){
                PadPApplyBoTNMTDto.ApplyMethod ApplyMethod = new PadPApplyBoTNMTDto.ApplyMethod("2", "Qua bưu điện");
                callPad.setApplyMethod(ApplyMethod);
            }

            //Set dossierReceivingKind
            if(data.getHinhThuc() == "1" ){
                PadPApplyBoTNMTDto.RecvKindDto dossierReceivingKind = new PadPApplyBoTNMTDto.RecvKindDto();
                dossierReceivingKind.setId("5fd1c7591b53d8779bc9ea78");
                List<PadPApplyBoTNMTDto.NameDto> name = new ArrayList<>() {
                    {
                        add(new PadPApplyBoTNMTDto.NameDto((short) 228, "Trực tuyến"));
                        add(new PadPApplyBoTNMTDto.NameDto((short) 46, "Online"));

                    }
                };
                dossierReceivingKind.setName(name);
                callPad.setDossierReceivingKind(dossierReceivingKind);
            }

            // Get applicant
            PadPApplyBoTNMTDto.ApplicantDataDto applicant = new PadPApplyBoTNMTDto.ApplicantDataDto();
            applicant.setOwnerFullname(data.getChuHoSo().getTen());
            applicant.setIdentityNumber(data.getChuHoSo().getSoCMND());
            applicant.setPhoneNumber(data.getChuHoSo().getSoDienThoai());
            applicant.setEmail(data.getChuHoSo().getEmail());
            applicant.setAddress(data.getChuHoSo().getDiaChi());
            applicant.setPhone(data.getSoDienThoai());
//                applicant.setEmail(data.getEmail());
            applicant.setFax(data.getFax());
            applicant.setFullname(data.getChuHoSo().getTen());
            applicant.setPhone(data.getChuHoSo().getSoDienThoai());
            applicant.setSobiado(data.getDonDangKy().getSoPhatHanh());

            /// set Thua dat
            try {
                PadPApplyBoTNMTDto.AddressDetailDto province2 = new PadPApplyBoTNMTDto.AddressDetailDto();
                province2.setLabel(data.getDonDangKy().getDiaChiThuaDat().getTenTinh());
                province2.setValue(data.getDonDangKy().getDiaChiThuaDat().getMaTinh());
                applicant.setProvince2(province2);

                try{
                    PadPApplyBoTNMTDto.AddressDetailDto district2 = new PadPApplyBoTNMTDto.AddressDetailDto();
                    district2.setLabel(data.getDonDangKy().getDiaChiThuaDat().getTenHuyen());
                    district2.setValue(data.getDonDangKy().getDiaChiThuaDat().getMaHuyen());
                    applicant.setDistrict2(district2);
                }catch (Exception e){

                }
                try{
                    PadPApplyBoTNMTDto.AddressDetailDto village2 = new PadPApplyBoTNMTDto.AddressDetailDto();
                    village2.setLabel(data.getDonDangKy().getDiaChiThuaDat().getTenXa());
                    village2.setValue(data.getDonDangKy().getDiaChiThuaDat().getMaXa());
                    applicant.setVillage2(village2);
                }catch (Exception e){

                }

                applicant.setDiaChiThuaDat(data.getDonDangKy().getDiaChiThuaDat().getDiaChiChiTiet());
            }
            catch (Exception e) {
                logger.info("MinhTueQNM-synchronizeDossier: lỗi thửa đất " + e);
            }
            if(data.getKenhThucHien()== 1){
                applicant.setHinhThucNop(1);
            }
            if(data.getKenhThucHien()== 2){
                applicant.setHinhThucNop(2);
            }
            if(data.getKenhThucHien()== 3){
                applicant.setHinhThucNop(4);
            }
            PadPApplyBoTNMTDto.ApplicantDto applicantMain = new PadPApplyBoTNMTDto.ApplicantDto();
            if(eform != ""){
                applicantMain.setEformId(eform);
            }
            applicantMain.setData(applicant);
            callPad.setApplicant(applicantMain);

            //set OtherInForBoTNMT
            OtherInForBoTNMT otherInForBoTNMT = new OtherInForBoTNMT();
            otherInForBoTNMT.setIsBoTNMT("true");
            otherInForBoTNMT.setMaHuyen(data.getMaHuyen());
            otherInForBoTNMT.setLoaiDoiTuong(data.getLoaiDoiTuong());
            otherInForBoTNMT.setMaDoiTuong(data.getMaDoiTuong());
            otherInForBoTNMT.setThongTinKhac(data.getThongTinKhac());
            otherInForBoTNMT.setTrichYeuHoSo(data.getTrichYeuHoSo());
            otherInForBoTNMT.setThongTinTra(data.getThongTinTra());
            otherInForBoTNMT.setGhiChu(data.getGhiChu());
            otherInForBoTNMT.setDonDangKy(data.getDonDangKy());
            callPad.setOtherInForBoTNMT(otherInForBoTNMT);


            //get acceptedDate
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
            if(Objects.nonNull(data.getNgayTiepNhan()) && !data.getNgayTiepNhan().equalsIgnoreCase("")){
                Date ngayTiepNhan = formatter.parse(data.getNgayTiepNhan());
                callPad.setAcceptedDate(ngayTiepNhan);
            }
            //get appointmentDate
            if(Objects.nonNull(data.getNgayHenTra()) && !data.getNgayHenTra().equalsIgnoreCase("")){
                Date ngayHenTra = formatter.parse(data.getNgayHenTra());
                callPad.setAppointmentDate(ngayHenTra);
            }
            //get returnedDate
            if(Objects.nonNull(data.getNgayTra())&& !data.getNgayTra().equalsIgnoreCase("")) {
                Date ngayTra = formatter.parse(data.getNgayTra());
                callPad.setAppointmentDate(ngayTra);
            }
            List<PadPApplyBoTNMTDto.AttachmentDto> lstAttachTaiLieuNop = new ArrayList<>();
            if (data.getTaiLieuNop() != null) {
                String uploadUrl = microservice.filemanUri("/file/--multiple").toUriString();
                MultiValueMap<String, Object> requestMap = new LinkedMultiValueMap<>();
                for (TaiLieuNop item : data.getTaiLieuNop()) {
                    String fileUrl = item.getDuongDanTaiTepTin();
                    logger.info("save file update/nhanhsdvcqg form: " + fileUrl);
                    String shortUrl = "file";
                    String url = microservice.filemanUri(shortUrl).toUriString() + "/--by-url?file-url=" + URLEncoder.encode(fileUrl, StandardCharsets.UTF_8);
                    logger.info("save file: " + url);
                    var na = this.getRestTemplate();
                    Id idFile = MicroserviceExchange.postJsonNoAuth(this.getRestTemplate(), url, null, Id.class);
                    logger.info(idFile.toString());
                    PadPApplyBoTNMTDto.AttachmentDto att = new PadPApplyBoTNMTDto.AttachmentDto(
                            idFile.getId().toHexString(), item.getTenTepDinhKem(), null, null
                    );
                    lstAttachTaiLieuNop.add(att);

                    try
                    {
                        String urlRename = microservice.filemanUri(shortUrl).toUriString() + "/"+idFile.getId().toHexString()+"/rename?name=" + item.getTenTepDinhKem();
                        AffectedRowsDto resultRename = MicroserviceExchange.putJsonNoAuth(this.getRestTemplate(), urlRename, null, AffectedRowsDto.class);
                    }
                    catch(Exception e){

                    }
                };

            }


            String newDossierUrl = microservice.padmanUri("/dossier/--apply-online").toUriString();
            // Pretty res and return
            IdDto newDossier = MicroserviceExchange.postJsonNoAuth(this.getRestTemplate(), newDossierUrl, callPad, IdDto.class);
            System.out.println(newDossier);
            logger.info("Luu thong tin vao dossier file");
            ArrayList<DossierFormFileDto> listDossierFormFileDto = new ArrayList<>();
            if (lstAttachTaiLieuNop.size() > 0) {
                DossierFormFileDto dossierFormFile = this.addTaiLieuNop(newDossier.getId().toHexString(), callPad.getProcedureProcessDefinition().getId().toString(), lstAttachTaiLieuNop);
                listDossierFormFileDto.add(dossierFormFile);
            }
            if (listDossierFormFileDto.size() > 0) {
                String newDossierFormFileURL = microservice.padmanUri("/dossier-form-file").toUriString() + "/--by-dossier?dossier-id=" + newDossier.getId().toHexString();
                // Pretty res and return
                AffectedRowsDto newDossierFormFile = MicroserviceExchange.putJsonNoAuth(this.getRestTemplate(), newDossierFormFileURL, listDossierFormFileDto, AffectedRowsDto.class);
                System.out.println(newDossierFormFile);
            }
            return newDossier;

        } catch (Exception e) {
            logger.info("MinhTueQNM-synchronizeDossier: Synchronize dossier fail " + e);
        }
        return null;
    }

}
