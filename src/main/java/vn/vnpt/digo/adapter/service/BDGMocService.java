package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.AgencyExperienceDto;
import vn.vnpt.digo.adapter.dto.ProcedureFullDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.TagDto;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.PostCommentDto;
import vn.vnpt.digo.adapter.dto.PlaceDetailDto;
import vn.vnpt.digo.adapter.dto.FileUResDto;
import vn.vnpt.digo.adapter.dto.FormFileDto;
import vn.vnpt.digo.adapter.dto.DossierFormFileDto;
import vn.vnpt.digo.adapter.dto.bdg.ConnectLogDto;
import vn.vnpt.digo.adapter.dto.bdg.SelectFormIO;
import vn.vnpt.digo.adapter.dto.bdg.moc.*;
import vn.vnpt.digo.adapter.dto.bdg.moc.AgencyDto;
import vn.vnpt.digo.adapter.dto.bdg.moc.UserDto;
import vn.vnpt.digo.adapter.dto.bdg_vilis.UpdateLastSyncBody;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.microservice.MessengerService;
import vn.vnpt.digo.adapter.pojo.Dossier;
import vn.vnpt.digo.adapter.pojo.ParametersType;
import vn.vnpt.digo.adapter.pojo.ProcedureForm;
import vn.vnpt.digo.adapter.pojo.Translate;
import vn.vnpt.digo.adapter.pojo.bdg.DossierApplyOnline;
import vn.vnpt.digo.adapter.pojo.bdg.DossierExtend;
import vn.vnpt.digo.adapter.properties.bdg.IntegratedConfigurationProperties;
import vn.vnpt.digo.adapter.util.*;
import vn.vnpt.digo.adapter.dto.PadPApplyDto.AttachmentDto;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Date;
import java.util.HashMap;
import java.util.Calendar;
import java.util.LinkedHashMap;
import java.util.Base64;

@Service
public class BDGMocService {
    @Autowired
    private Microservice microservice;

    @Autowired
    private MongoTemplate mongoTemplate;

    private IntegratedConfigurationDto configLGSP;

    private IntegratedConfigurationDto configMoc;
    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private Translator translator;

    @Autowired
    private Oauth2RestTemplate oauth2RestTemplate;

    @Autowired
    private BDGConnectLogService connectLogService;

    @Autowired
    private UtilService utilService;


    @Autowired
    private MessengerService messengerService;

    private String fileGroupId = "";

    @Autowired
    private BDGConnectLogService bdgConnectLogService;

    public final static String GROUP_ID = "BXD";

    @Scheduled(cron = "${digo.schedule.moc.get-dossier.cron}")
    ////@SchedulerLock(name = "syncGetMocDossier", lockAtLeastFor = "PT20S", lockAtMostFor = "PT25S")
    public void syncGetMocDossier(){
        List<String> response = new ArrayList<>();
        IntegratedConfigurationDto configLGSP =  this.getConfigLGSP();
        configMoc = this.getConfig(IntegratedConfigurationProperties.VNPT_IGATE_SUBSYSTEM_ID, IntegratedConfigurationProperties.MOC_SERVICE_ID);
        String strFromDate = configMoc.getParametersValue("LanCuoiDongBoLayHoSo");
        DateFormat dfFromDate = new SimpleDateFormat("yyyyMMdd");
        DateFormat dfToDate = new SimpleDateFormat("yyyyMMdd");
        Date fromDate = null;
        try {
            fromDate = dfFromDate.parse(strFromDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Calendar c = Calendar.getInstance();
        c.setTime(fromDate);
        c.add(Calendar.DATE, 5);
        String token = MicroserviceExchange.getTokenBDG(configLGSP.getParametersValue("gateway"), configLGSP.getParametersValue("consumer-key"), configLGSP.getParametersValue("consumer-secret"));
        String url = UriComponentsBuilder.fromHttpUrl(configMoc.getParametersValue("LayThongTinHoSo"))
                .queryParam("tuNgay", "{tuNgay}")
                .queryParam("denNgay", "{denNgay}")
                .queryParam("maDonVi", "{maDonVi}")
                .encode().toUriString();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> params = new HashMap<>();
        params.put("tuNgay", strFromDate);
        params.put("denNgay", dfToDate.format(c.getTime()));
        params.put("maDonVi", "000.00.16.H09");
        GetDossierListResponseDto result =  MicroserviceExchange.get(new RestTemplate(), url, headers, GetDossierListResponseDto.class, params);
        if (result.getStatus()){
            List<MocDossierDto> mocDossierList = result.getData();
            for (MocDossierDto mocDossier: mocDossierList) {
                IdDto id = null;
                try {
                    id = convertDossierFromMocToIgate(mocDossier);
                    writeLog(mocDossier.getCode(), GROUP_ID,200, url, mocDossier.toString(), id.toString(), null);
                }catch (Exception e){
                    writeLog(mocDossier.getCode(), GROUP_ID,200, url, mocDossier.toString(), e.getMessage(), null);
                    id = null;
                }
                if (Objects.nonNull(id)){
                    response.add(id.getId().toHexString());
                    DossierExtend extendBDG  = new DossierExtend();
                    extendBDG.setConnectTo(GROUP_ID);
                    this.updateSyncStatusOnIgate(id.getId(),extendBDG );
                }

            }
            if (response.size() > 0){
                //Cập nhật thời gian đồng bộ
                UpdateLastSyncBody lastSyncBody = new UpdateLastSyncBody();
                DateFormat dflastSync = new SimpleDateFormat("yyyyMMdd");
                lastSyncBody.setLastUpdateSync(dflastSync.format(new Date()));
                this.updateLastSync(lastSyncBody);
                writeLog("SYNC_MOC", GROUP_ID,200, url, new Date().toString(), lastSyncBody.toString(), null);
            }
        }
    }
    public List<IdDto> getDossierListByPeriodTime(GetDossierListDto body){
        List<IdDto> response = new ArrayList<>();
        IntegratedConfigurationDto configLGSP =  this.getConfigLGSP();
        configMoc = this.getConfig(IntegratedConfigurationProperties.VNPT_IGATE_SUBSYSTEM_ID, IntegratedConfigurationProperties.MOC_SERVICE_ID);
        String mocUrl = configMoc.getParametersValue("LayThongTinHoSo");
        String token = MicroserviceExchange.getTokenBDG(configLGSP.getParametersValue("gateway"), configLGSP.getParametersValue("consumer-key"), configLGSP.getParametersValue("consumer-secret"));
        GetDossierListResponseDto result =  MicroserviceExchange.postJsonBearAuth(new RestTemplate(), mocUrl, token, body, GetDossierListResponseDto.class);
        if (result.getStatus()){
            List<MocDossierDto> mocDossierList = result.getData();
            for (MocDossierDto mocDossier: mocDossierList) {
                response.add(convertDossierFromMocToIgate(mocDossier));
            }

        }
        return response;
    }

    /**
     * Gửi hồ sơ từ địa phương lên bộ
     * @param dossier
     * @return
     */
    public AffectedRowsDto postDossierToMoc(Dossier dossier){
        //lấy thông tin cấu hình
        configMoc = this.getConfig(IntegratedConfigurationProperties.VNPT_IGATE_SUBSYSTEM_ID, IntegratedConfigurationProperties.MOC_SERVICE_ID);
        IntegratedConfigurationDto configGateWay = this.getGateWay(configMoc.getParametersValue("QuaTruc"));
        String mocUrl = configMoc.getParametersValue("GuiThongTinHoSo");
        String token = getToken(configGateWay);

        MocDossierDto mocDossier =  convertDossierFromIgateToMoc(dossier);
        String rawJsonMocDossier = null;
        try {
            rawJsonMocDossier = new ObjectMapper().writeValueAsString(mocDossier);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        writeLog(dossier.getCode(), GROUP_ID,200, mocUrl, rawJsonMocDossier, null, null);
        List<MocDossierDto> mocDossierDtoList = new ArrayList<>();
        mocDossierDtoList.add(mocDossier);
        try{
            String result = MicroserviceExchange.postJsonBearAuth(new RestTemplate(), mocUrl, token, mocDossierDtoList, String.class);
            writeLog(dossier.getCode(), GROUP_ID,200, mocUrl, rawJsonMocDossier, result.toString(), null);
            PostCommentDto.User user = new PostCommentDto.User(new ObjectId(configMoc.getParametersValue("userSystemId").toString()), "System");
            messengerService.comment(dossier.getId(), "Đã gửi hồ sơ lên Bộ XD thành công", user);
//            if (result.getStatus()){
//                return new AffectedRowsDto(1, result.getMessage());
//            }
            return new AffectedRowsDto(1, result);
        }catch (Exception e){
            writeLog(dossier.getCode(), GROUP_ID,500, mocUrl, rawJsonMocDossier, e.toString(), null);
            PostCommentDto.User user = new PostCommentDto.User(new ObjectId(configMoc.getParametersValue("userSystemId").toString()), "System");
            messengerService.comment(dossier.getId(), "Lỗi, gửi hồ sơ lên Bộ XD thất bại.Cần xem log chi tiết", user);
            return new AffectedRowsDto(-1, e.getMessage());
        }
    }

    public AffectedRowsDto updateProcessingMocDossier(Dossier dossier){
        RestTemplate restTemplate = oauth2RestTemplate.getOAuth2RestTemplate();
        configMoc = this.getConfig(IntegratedConfigurationProperties.VNPT_IGATE_SUBSYSTEM_ID, IntegratedConfigurationProperties.MOC_SERVICE_ID);
        IntegratedConfigurationDto configGateWay = this.getGateWay(configMoc.getParametersValue("QuaTruc"));
        String mocUrl = configMoc.getParametersValue("GuiThongTinXuLyHoSo");
        String token = getToken(configGateWay);
        //Map du lieu theo body cua bo xd
        PostUpdateProcessingMocDossierDto processingMocDossier =  convertProcessingDossier(dossier);
        String rawJsonMocDossier = null;
        List<PostUpdateProcessingMocDossierDto> processingMocDossierList = new ArrayList<>();
        processingMocDossierList.add(processingMocDossier);
        try{
            rawJsonMocDossier = new ObjectMapper().writeValueAsString(processingMocDossierList);
            Object result = MicroserviceExchange.postJsonBearAuth(new RestTemplate(), mocUrl, token, processingMocDossierList, Object.class);
            Boolean statusResult = (Boolean) ((LinkedHashMap)result).get("status");
            String message = ((LinkedHashMap)result).get("message").toString();
            if (statusResult){
                writeLog(dossier.getCode(), GROUP_ID, 200, mocUrl, rawJsonMocDossier, message, null);
                PostCommentDto.User user = new PostCommentDto.User(new ObjectId(configMoc.getParametersValue("userSystemId").toString()), "System");
                messengerService.comment(dossier.getId(), "Đã gửi hồ sơ lên Bộ XD thành công", user);
                return new AffectedRowsDto(1, message);
            }else {
                writeLog(dossier.getCode(), GROUP_ID, 500, mocUrl, rawJsonMocDossier, message, null);
                PostCommentDto.User user = new PostCommentDto.User(new ObjectId(configMoc.getParametersValue("userSystemId").toString()), "System");
                messengerService.comment(dossier.getId(), "Lỗi, gửi hồ sơ lên Bộ XD thất bại.Cần xem log chi tiết", user);
                return new AffectedRowsDto(-1, message);
            }

        }catch (Exception e){
            writeLog(dossier.getCode(), GROUP_ID,500, mocUrl, rawJsonMocDossier, e.toString(), null);
            PostCommentDto.User user = new PostCommentDto.User(new ObjectId(configMoc.getParametersValue("userSystemId").toString()), "System");
            messengerService.comment(dossier.getId(), "Lỗi, gửi hồ sơ lên Bộ XD thất bại. Cần xem log chi tiết", user);
            return new AffectedRowsDto(-1, e.getMessage());
        }
    }

    public IdDto convertDossierFromMocToIgate(MocDossierDto mocDossier) {
        configMoc = this.getConfig(IntegratedConfigurationProperties.VNPT_IGATE_SUBSYSTEM_ID, IntegratedConfigurationProperties.MOC_SERVICE_ID);
        RestTemplate restTemplate = oauth2RestTemplate.getOAuth2RestTemplate();
        DossierApplyOnline dossierApplyOnline = new DossierApplyOnline();
        dossierApplyOnline.setCode(mocDossier.getCode());

        dossierApplyOnline.setAppointmentDate(mocDossier.getAppointmentDate());
        dossierApplyOnline.setAppliedDate(mocDossier.getReceptionDate());
        //Thêm thủ tục
        Map<String, Object> procedureParrams = new HashMap<>();
        procedureParrams.put("id", configMoc.getParametersValue("procedure-id"));
        String procedureUrl = microservice.basepadUri("/procedure/").toUriString() + "{id}/--full";
        DossierApplyOnline.ProcedureDtoTemp procedure = MicroserviceExchange.getNoAuth(restTemplate, procedureUrl, DossierApplyOnline.ProcedureDtoTemp.class, procedureParrams);
        DossierApplyOnline.ProcedureDto procedureDto = new DossierApplyOnline.ProcedureDto(procedure.getId(), procedure.getCode(), procedure.getTranslate(), procedure.getSector());
        dossierApplyOnline.setProcedure(procedureDto);
        dossierApplyOnline.setProcedureLevel(procedure.getLevel());

        //Thêm thông tin đơn vị
        String agencyUrl = microservice.basedataUri("/agency/" + configMoc.getParametersValue("agency-reception-id") + "/name+code+parent+ancestor/--fully").toUriString();
        DossierApplyOnline.AgencyDto agencyDto = MicroserviceExchange.getNoAuth(restTemplate, agencyUrl, DossierApplyOnline.AgencyDto.class);
        dossierApplyOnline.setAgency(agencyDto);

        //Thêm quy trình
        Map<String, Object> processParrams = new HashMap<>();
        processParrams.put("id", configMoc.getParametersValue("process-id"));
        String processUrl = microservice.basepadUri("/procedure-process-definition/").toUriString() + "{id}";
        DossierApplyOnline.ProcessDto process = MicroserviceExchange.getNoAuth(restTemplate, processUrl, DossierApplyOnline.ProcessDto.class, processParrams);
        dossierApplyOnline.setProcedureProcessDefinition(process);

        //Thêm Phương thức nhận kết quả
        DossierApplyOnline.RecvKindDto dossierReceivingKind = new DossierApplyOnline.RecvKindDto();
        dossierReceivingKind.setId("5f8968888fffa53e4c073ded");
        List<DossierApplyOnline.NameDto> name = new ArrayList<>() {
            {
                add(new DossierApplyOnline.NameDto((short) 228, "Nhan Truc Tiep"));
                add(new DossierApplyOnline.NameDto((short) 46, "Receive directly"));

            }
        };
        dossierReceivingKind.setName(name);
        dossierApplyOnline.setDossierReceivingKind(dossierReceivingKind);

        //Thêm Trạng thái hồ sơ
        DossierApplyOnline.RecvKindDto dossierMenuTaskRemind = new DossierApplyOnline.RecvKindDto();
        dossierMenuTaskRemind.setId("60f52e0d09cbf91d41f88834");
        List<DossierApplyOnline.NameDto> namedossierMenuTaskRemind = new ArrayList<>() {
            {
                add(new DossierApplyOnline.NameDto((short) 228, "Moi dang ky"));
                add(new DossierApplyOnline.NameDto((short) 46, "Just signed up"));

            }
        };
        dossierMenuTaskRemind.setName(namedossierMenuTaskRemind);
        dossierApplyOnline.setDossierMenuTaskRemind(dossierMenuTaskRemind);


        //Thêm thông tin biểu mẫu
        ApplicantDto form = new ApplicantDto();

        //Thông tin người nộp
        MocApplicantDto mocDossierApplicant = mocDossier.getApplicant();
        form.setFullname(mocDossierApplicant.getFullName());
        form.setIdentityNumber(mocDossierApplicant.getIdentity());
        form.setEmail(mocDossierApplicant.getEmail());
        form.setPhoneNumber(mocDossierApplicant.getPhone());
        form.setAddress(mocDossierApplicant.getAddress());

        //Thông tin chủ đầu tư
        MocInvestorDto mocDossierInvestor = mocDossier.getInvestor();
        form.setFullNameInvestor(mocDossierInvestor.getFullName());
        form.setLegalRepresentativeName(mocDossierInvestor.getLegalRepresentativeName());
        form.setPositionInvestor(mocDossierInvestor.getPosition());
        form.setPhoneInvestor(mocDossierInvestor.getPhone());

        PlaceDetailDto provinceInvestor = utilService.getProvinceByCode(mocDossierInvestor.getProvinceCode());
        form.setProvinceInvestor(new SelectFormIO(provinceInvestor.getId(), provinceInvestor.getName()));
        PlaceDetailDto districtInvestor = utilService.getDistrictByCode(mocDossierInvestor.getDistrictCode());
        form.setDistrictInvestor(new SelectFormIO(districtInvestor.getId(), districtInvestor.getName()));
        PlaceDetailDto wardInvestor = utilService.getWardByCode(mocDossierInvestor.getWardCode());
        form.setWardInvestor(new SelectFormIO(wardInvestor.getId(), wardInvestor.getName()));

        form.setStreetInvestor(mocDossierInvestor.getStreet());
        form.setContactAddressInvestor(mocDossierInvestor.getContactAddress());

        //Thông tin công trình
        MocConstructionInfoDto mocDossierConstruction = mocDossier.getConstruction();
        form.setLocationConstruction(mocDossierConstruction.getLocation());
        form.setSerialLandConstruction(mocDossierConstruction.getSerialLand());
        form.setAcreageConstruction(mocDossierConstruction.getAcreage());

        PlaceDetailDto provinceConstruction = utilService.getProvinceByCode(mocDossierInvestor.getProvinceCode());
        form.setProvinceInvestor(new SelectFormIO(provinceConstruction.getId(), provinceConstruction.getName()));
        PlaceDetailDto districtConstruction = utilService.getDistrictByCode(mocDossierInvestor.getDistrictCode());
        form.setDistrictInvestor(new SelectFormIO(districtConstruction.getId(), districtConstruction.getName()));
        PlaceDetailDto wardConstruction = utilService.getWardByCode(mocDossierInvestor.getWardCode());
        form.setWardInvestor(new SelectFormIO(wardConstruction.getId(), wardConstruction.getName()));

        form.setStreetConstruction(mocDossierConstruction.getStreet());
        //Thời gian hoàn thành
        form.setEndDateConstruction(mocDossier.getEndDateConstruction());

        //Lấy applicantion eform từ quy trinh
        LinkedHashMap processDefinition = (LinkedHashMap) process.getProcessDefinition();
        String applicantEformId = ((LinkedHashMap) processDefinition.get("applicantEForm")).get("id").toString();

        DossierApplyOnline.ApplicantDto applicant = new DossierApplyOnline.ApplicantDto();
        applicant.setEformId(applicantEformId);
        applicant.setData(form);
        dossierApplyOnline.setApplicant(applicant);

        //Lưu hồ sơ
        String newDossierUrl = microservice.padmanUri("/dossier/--apply-online").toUriString();
        IdDto dossier = null;
        try {
            dossier = MicroserviceExchange.postJsonNoAuth(restTemplate, newDossierUrl, dossierApplyOnline, IdDto.class);
        }catch (Exception e){
            return dossier;
        }
        //Upload file
        List<DossierApplyOnline.AttachmentDto> attachmentList = new ArrayList<>();
        List<MocDossierAttachmentDto> mocDossierAttachmentList = mocDossier.getAttachmentList();

        if (mocDossierAttachmentList != null && mocDossierAttachmentList.size() > 0) {
            String uploadUrl = microservice.filemanUri("/file/--multiple").toUriString();
            for (MocDossierAttachmentDto mocDossierAttachment : mocDossierAttachmentList) {
                String nameFile = mocDossier.getCode() + "_" + mocDossierAttachment.getCode();
                MultiValueMap<String, Object> requestMap = new LinkedMultiValueMap<>();
                if (Objects.isNull(mocDossierAttachment.getBase64()) || mocDossierAttachment.getBase64().equals("")){
                    return dossier;
                }
                byte[] bytes = Base64.getDecoder().decode(mocDossierAttachment.getBase64());
                ByteArrayResource byteArrayResource = new ByteArrayResource(bytes) {
                    @Override
                    public String getFilename() {
                        return nameFile;
                    }
                };

                requestMap.add("files", byteArrayResource);
                FileUResDto[] lstFile = MicroserviceExchange.postMultipartNoAuth(restTemplate, uploadUrl, requestMap, FileUResDto[].class);
                // For and create attachment
                for (FileUResDto item : lstFile) {
                    DossierApplyOnline.AttachmentDto att = new DossierApplyOnline.AttachmentDto(
                            item.getId(), item.getFilename(), item.getSize(), fileGroupId
                    );
                    attachmentList.add(att);
                    List<DossierFormFileDto> dossierFormFileList = new ArrayList<>();
                    if (attachmentList.size() > 0) {
                        DossierFormFileDto dossierFormFile = this.addFormFile(dossier.getId().toHexString(), configMoc.getParametersValue("process-id"), attachmentList, mocDossierAttachment.getCode());
                        dossierFormFileList.add(dossierFormFile);
                        String newDossierFormFileURL = microservice.padmanUri("/dossier-form-file").toUriString() + "/--by-dossier?dossier-id=" + dossier.getId().toHexString();
                        AffectedRowsDto newDossierFormFile = MicroserviceExchange.putJsonNoAuth(restTemplate, newDossierFormFileURL, dossierFormFileList, AffectedRowsDto.class);
                        System.out.println(newDossierFormFile);
                    }
                }
            }
        }
        return dossier;
    }

    /**
     * Chuyển đổi thuộc tính hồ sơ Igate sang thuộc tính hồ sơ Bộ XD
     * - Chuyển đổi các thông tin cơ bản
     * - Thông tin: Người nộp, chủ đầu tư, công trình được đổ từ form nhập trên igate
     * @param dossier
     * @return
     */
    public MocDossierDto convertDossierFromIgateToMoc(Dossier dossier){

        ProcedureFullDto procedure = getProcedureById(dossier.getProcedure().getId());
        ProcedureFullDto.Agency agency = procedure.getAgency().get(0);
        //Lấy ma don vi cu
        AgencyDto agencyFully = getAgency(agency.getId().toHexString());
        //Biến đổi dữ liệu
        Gson gson = new Gson();
        ApplicantDto applicant = gson.fromJson(new Gson().toJson(dossier.getApplicant().getData()), ApplicantDto.class);
        MocDossierDto mocDossier = new MocDossierDto();

        //Không lấy theo dossierStatus lý do trạng thái này không map đúng với trạng thái trên CSDL QG
        //Thay vào đó dùng trạng thái DossierTaskStatus ( Trường IntegratedCode )
        String dossierTaskStatusId = dossier.getDossierTaskStatus().getId();
        String statusDossierVDXPId = getStatusDossierVDXP(dossierTaskStatusId).getIntegratedCode();


        //Thông tin cơ bản
        mocDossier.setCode(dossier.getCode());
        mocDossier.setAgencyCode(agencyFully.getExtendBDG().getCodeNationalPublicBDG());
        mocDossier.setAgencyName(Translate.toTranslate(agency.getName()));
        mocDossier.setStatus(statusDossierVDXPId);
        mocDossier.setAppointmentDate(dossier.getAppointmentDate());
        mocDossier.setReceptionDate(dossier.getAcceptedDate());

        //Thông tin người nộp
        MocApplicantDto mocApplicant = new MocApplicantDto();
        mocApplicant.setFullName(applicant.getFullname());
        mocApplicant.setAddress(applicant.getAddress());
        mocApplicant.setEmail(applicant.getEmail());
        mocApplicant.setIdentity(applicant.getIdentityNumber());
        mocApplicant.setPhone(applicant.getPhoneNumber());

        mocDossier.setApplicant(mocApplicant);

        //Thông tin về chủ đầu tư
        MocInvestorDto mocInvestor = new MocInvestorDto();
        mocInvestor.setFullName(applicant.getFullNameInvestor());
        mocInvestor.setPosition(applicant.getPositionInvestor());
        mocInvestor.setPhone(applicant.getPhoneInvestor());
        mocInvestor.setLegalRepresentativeName(applicant.getLegalRepresentativeName());
        mocInvestor.setContactAddress(applicant.getContactAddressInvestor());
        mocInvestor.setProvinceCode(utilService.getProvinceCode(applicant.getProvinceInvestor().getValue()));
        mocInvestor.setDistrictCode(utilService.getDistrictCode(applicant.getDistrictInvestor().getValue()));
        mocInvestor.setWardCode(utilService.getWardCode(applicant.getWardInvestor().getValue()));
        mocInvestor.setStreet(applicant.getStreetInvestor());

        mocDossier.setInvestor(mocInvestor);

        //Thông tin về công trình
        MocConstructionInfoDto mocConstructionInfo = new MocConstructionInfoDto();
        mocConstructionInfo.setLocation(applicant.getLocationConstruction());
        mocConstructionInfo.setAcreage(applicant.getAcreageConstruction());
        mocConstructionInfo.setSerialLand(applicant.getSerialLandConstruction());
        mocConstructionInfo.setProvinceCode(utilService.getProvinceCode(applicant.getProvinceConstruction().getValue()));
        mocConstructionInfo.setWardCode(utilService.getDistrictCode(applicant.getDistrictConstruction().getValue()));
        mocConstructionInfo.setDistrictCode(utilService.getWardCode(applicant.getWardConstruction().getValue()));
        mocConstructionInfo.setStreet(applicant.getStreetConstruction());

        mocDossier.setConstruction(mocConstructionInfo);

        //Thời gian hoàn thành công trình
        mocDossier.setEndDateConstruction(applicant.getEndDateConstruction());

        //Thông tin tập tin đính kèm
        List<MocDossierAttachmentDto> attachmentList  = new ArrayList<>();
        //Lấy danh sách thành phần hồ sơ
        List<DossierFormFileDto> formFileList = this.getDossierFiles(dossier);
        for (DossierFormFileDto formFile: formFileList ) {
            for (AttachmentDto file: formFile.getFile()) {
                MocDossierAttachmentDto attachment = new MocDossierAttachmentDto();
                attachment.setName(formFile.getProcedureForm().getName());
                attachment.setBase64(getFileBase64(new ObjectId(file.getId())));
                attachment.setCode(getFormById(new ObjectId(formFile.getProcedureForm().getId())).getCode());
                attachmentList.add(attachment);
                String[] splitFileName = file.getFilename().split("[.]");
                String extendFile = splitFileName[splitFileName.length-1];
                attachment.setExtend(extendFile);
            }
        }
        mocDossier.setAttachmentList(attachmentList);
//        try {
//            String rawJsonMocDossier = new ObjectMapper().writeValueAsString(mocDossier);
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
        writeLog(dossier.getCode(), GROUP_ID,200, "convertDossierFromIgateToMoc", mocDossier.toString(), null, null);
        return mocDossier;
    }

    public PostUpdateProcessingMocDossierDto convertProcessingDossier(Dossier dossier){
        ProcedureFullDto procedure = getProcedureById(dossier.getProcedure().getId());
        ProcedureFullDto.Agency agencyProcedure = procedure.getAgency().get(0);
        //Lấy ma don vi cu
        AgencyDto agencyFully = getAgency(agencyProcedure.getId().toHexString());

        Dossier.DossierTask currentTask = dossier.getCurrentTask().get(0);
        Dossier.Agency agency = currentTask.getAgency();
        UserDto user = getUserInfo(currentTask.getAssignee().getId());
        AgencyExperienceDto experienceList =  user.getExperience().stream().filter(exp -> exp.getAgency().getId().equals(agency.getId())).findFirst().get();

        //Không lấy theo dossierStatus lý do trạng thái này không map đúng với trạng thái trên CSDL QG
        //Thay vào đó dùng trạng thái DossierTaskStatus ( Trường IntegratedCode )
        String dossierTaskStatusId = dossier.getDossierTaskStatus().getId();
        String statusDossierVDXPId = getStatusDossierVDXP(dossierTaskStatusId).getIntegratedCode();

        PostUpdateProcessingMocDossierDto processingMocDossier = new PostUpdateProcessingMocDossierDto();
        processingMocDossier.setDossierCode(dossier.getCode());
        processingMocDossier.setAgencyCode(agencyFully.getExtendBDG().getCodeNationalPublicBDG());
        processingMocDossier.setAgencyName(Translate.toTranslate(agencyProcedure.getName()));
        processingMocDossier.setAppointmentDate(dossier.getAppointmentDate());

        List<ProcessingInfoDto> mocProcessingList = new ArrayList<>();
        ProcessingInfoDto mocProcessing = new ProcessingInfoDto();
        mocProcessing.setStatus(statusDossierVDXPId);
        mocProcessing.setOfficerName(currentTask.getAssignee().getFullname());
        mocProcessing.setDepartment(Translate.toTranslate(currentTask.getAgency().getName()));
        mocProcessing.setPosition(experienceList.getPosition().getName());
        mocProcessing.setStartProcessing(currentTask.getAssignedDate());
        mocProcessing.setEndProcessing(currentTask.getDueDate());
        mocProcessing.setDateOfHandle(currentTask.getAssignedDate());
        mocProcessing.setContent("Không");
        mocProcessingList.add(mocProcessing);

        processingMocDossier.setThongTinXuLy(mocProcessingList);

//        try {
//            String rawJsonMocDossier = new ObjectMapper().writeValueAsString(processingMocDossier);
//            return rawJsonMocDossier;
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
        writeLog(dossier.getCode(), GROUP_ID,200, "convertProcessingDossier", processingMocDossier.toString(), null, null);
        return processingMocDossier;
    }

    public TagDto getStatusDossierVDXP(String id){
        String labelUrl = microservice.basecatUri("/tag/" + id).toUriString();
        return MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), labelUrl, TagDto.class);
    }
    public UserDto getUserInfo(String id){
        String userUrl = microservice.humanUri("/user/" + id +"/--fully").toUriString();
        return MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), userUrl, UserDto.class);
    }
    public AgencyDto getAgency(String id){
        String agencyUrl = microservice.basedataUri("/agency/" + id ).toUriString();
        return MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), agencyUrl, AgencyDto.class);
    }
    public String getFileBase64(ObjectId id){
        String fileUrl = microservice.filemanUri("file/" + id).toUriString();
        byte[] file = MicroserviceExchange.get(oauth2RestTemplate.getOAuth2RestTemplate(), fileUrl, byte[].class);
        return Base64.getEncoder().encodeToString(file);
    }
    public List<DossierFormFileDto> getDossierFiles(Dossier dossier){
        String dossierFormFileUrl = microservice.padmanUri("/dossier-form-file/getBy?dossierId=" + dossier.getId().toHexString()).toUriString();
        DossierFormFileDto[] newDossierFormFile = MicroserviceExchange.get(oauth2RestTemplate.getOAuth2RestTemplate(), dossierFormFileUrl, DossierFormFileDto[].class);
        return List.of(newDossierFormFile);
    }
    public FormFileDto getFormById(ObjectId id){
        String formFileUrl = microservice.basepadUri("/form/" + id.toHexString()).toUriString();
        return MicroserviceExchange.get(oauth2RestTemplate.getOAuth2RestTemplate(), formFileUrl , FormFileDto.class);
    }

    public FormFileDto getFormByCode(String code){
        String formFileUrl = microservice.basepadUri("/form/--by-code?code=" + code).toUriString();
        return MicroserviceExchange.get(oauth2RestTemplate.getOAuth2RestTemplate(), formFileUrl , FormFileDto.class);
    }

    public ProcedureFullDto getProcedureById(ObjectId id){
        String procedureUrl = microservice.basepadUri("/bdg-procedure").toUriString() + "/" + id.toHexString();
        return MicroserviceExchange.getNoAuth(oauth2RestTemplate.getOAuth2RestTemplate(), procedureUrl, ProcedureFullDto.class);
    }
    public DossierFormFileDto addFormFile(String dossierId, String processId ,List<DossierApplyOnline.AttachmentDto> lstAttach, String formFileCode){
        DossierFormFileDto dossierFormFile = new DossierFormFileDto();
        // set dossier
        DossierFormFileDto.Id dossier = new DossierFormFileDto.Id();
        dossier.setId(dossierId);
        dossierFormFile.setDossier(dossier);
        // set procedureForm -- Thuc chat la Form

        FormFileDto formFile = getFormByCode(formFileCode);

        ProcedureForm procedureForm = new ProcedureForm();
        procedureForm.setId(formFile.getId().toHexString());
        String textFormName = Translate.toTranslate(formFile.getName());
        procedureForm.setName(textFormName);
        dossierFormFile.setProcedureForm(procedureForm);
        // set case
        DossierFormFileDto.Id caze = new DossierFormFileDto.Id();
        caze.setId(processId);
        dossierFormFile.setCaze(caze);

        dossierFormFile.setOrder(1);
        dossierFormFile.setQuantity(1);

        // set detail
        String textDetail = new String("B\u1ea3n sao".getBytes(StandardCharsets.UTF_8),
                Charset.forName("UTF-8"));
        DossierFormFileDto.ProcedureFormDetail detail = new DossierFormFileDto.ProcedureFormDetail(
                new  DossierFormFileDto.ProcedureFormDetailType("623462c0f2e2ad4ed5787167",textDetail), 1
        );
        dossierFormFile.setDetail(detail);
        dossierFormFile.setFile((ArrayList) lstAttach);

        return dossierFormFile;
    };

    public IntegratedConfigurationDto getConfigLGSP(){
        IntegratedConfigurationDto configLGSP = configurationService.getConfig(null, IntegratedConfigurationProperties.VNPT_IGATE_SUBSYSTEM_ID, IntegratedConfigurationProperties.LGSP_MINHTUE_SERVICE_ID);
        configLGSP.setValue((short) 228);
        if (Objects.isNull(configLGSP)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        return configLGSP;
    }

    public IntegratedConfigurationDto getConfig(ObjectId subsystemId, ObjectId  serviceId){
        IntegratedConfigurationDto config = configurationService.getConfig(null, subsystemId, serviceId);
        config.setValue((short) 228);
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        return config;
    }

    public IntegratedConfigurationDto getGateWay(String key){
        switch (key){
            case "LGSP_MINHTUE":
               return getConfig(IntegratedConfigurationProperties.VNPT_IGATE_SUBSYSTEM_ID, IntegratedConfigurationProperties.LGSP_MINHTUE_SERVICE_ID);
        }
        return null;
    }

    public String getToken(IntegratedConfigurationDto configGateWay){
        //If else token theo tỉnh tại đây
        return MicroserviceExchange.getTokenBDG(configGateWay.getParametersValue("gateway"), configGateWay.getParametersValue("consumer-key"), configGateWay.getParametersValue("consumer-secret"));
    }

    public AffectedRowsDto updateSyncStatusOnIgate(ObjectId dossierId, DossierExtend extendBDG){
        String dossier2Url = microservice.padmanUri("/bdg-dossier/" + dossierId.toHexString() + "/updateStatusReceivedConnect").toUriString();
        return MicroserviceExchange.postJson(new RestTemplate(), dossier2Url, extendBDG, AffectedRowsDto.class);
    }

    /**
     * Phần ghi log mặc định BDG, có thể dùng hoặc đặt lồng if-else để thêm log riêng
     * @param key
     * @param group
     * @param status
     * @param url
     * @param body
     * @param response
     * @param error
     */
    public void writeLog(String key, String group,  Integer status, String url,  String body, String response, String error){
        ConnectLogDto connectLog = new ConnectLogDto();
        connectLog.setKey(key);
        connectLog.setGroupLog(group);
        connectLog.setBody(body);
        connectLog.setUrl(url);
        connectLog.setStatusHTTP(status);
        connectLog.setResponse(response);
        connectLog.setError(error);
        bdgConnectLogService.writeLog(connectLog);
    }
    public AffectedRowsDto updateLastSync(UpdateLastSyncBody body){
        configMoc = this.getConfig(IntegratedConfigurationProperties.VNPT_IGATE_SUBSYSTEM_ID, IntegratedConfigurationProperties.MOC_SERVICE_ID);
        configMoc.setParametersValue("LanCuoiDongBoLayHoSo", body.getLastUpdateSync(), new ParametersType(1, "String"));

        IntegratedConfiguration configuration = mongoTemplate.findById(configMoc.getId(), IntegratedConfiguration.class);
        configuration.setParameters(configMoc.getParameters());
        mongoTemplate.save(configuration);
        return new AffectedRowsDto(1);
    }
}
