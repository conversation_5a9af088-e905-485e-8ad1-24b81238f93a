package vn.vnpt.digo.adapter.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.document.TokenPartner;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCDossierTrackingReqDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCResultDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMLogInputDto;
import vn.vnpt.digo.adapter.dto.vbdlis.*;
import vn.vnpt.digo.adapter.dto.vbdlis.DanhMucTrangThaiDto.DossierStatusDto;
import vn.vnpt.digo.adapter.dto.vbdlis.VbdlisResDto.*;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.repository.TokenPartnerRepository;
import vn.vnpt.digo.adapter.stream.LGSPHCMDossierProducerStream;
import vn.vnpt.digo.adapter.util.*;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.HttpServletResponse;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Date;
import java.util.HashMap;
/**
 *
 * <AUTHOR>
 */
@Service
public class VBDLISService {

    Logger logger = LoggerFactory.getLogger(PaymentPlatformService.class);

    @Autowired
    private Translator translator;

    @Autowired
    private IntegratedConfigurationService configurationService;

    private RestTemplate restTemplate;

    @Value(value = "${digo.schedule.VBDLISServiceGetTokenV2.enable}")
    private Boolean getTokenV2Enable;

    @Value(value = "${digo.vbdilis.fbt.enable}")
    private Boolean vbdilisFBTEnable;

    @Autowired
    public VBDLISService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    private Gson gson = new Gson();

    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890038");

    @Autowired
    private Microservice microservice;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private LGSPHCMLogService lgspHCMLogService;

    @Autowired
    private LGSPHCMDossierProducerStream lgspHCMDossierProducerStream;

    @Value(value = "${digo.rest.connection.module.set-timeout}")
    private Boolean enableSetTimeOut;

    @Value(value = "${digo.qni.enable-vbdlis-timeout}")
    private Boolean qniEnableVBdlisTimeout;

    private Boolean refresh = false;

    @Autowired
    private TokenPartnerRepository tokenPartnerRepository;

    public void setCustomTimeouts(int connectTimeout, int readTimeout) throws NoSuchAlgorithmException, KeyManagementException {
        this.restTemplate = restTemplateCustom(connectTimeout, readTimeout);
    }

    public RestTemplate restTemplateCustom(Integer restConnectTimeout, Integer restReadTimeout) throws NoSuchAlgorithmException, KeyManagementException {
        RestTemplateBuilder builder = new RestTemplateBuilder();
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }

                    @Override
                    public void checkClientTrusted(
                            java.security.cert.X509Certificate[] certs, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(
                            java.security.cert.X509Certificate[] certs, String authType) {
                    }
                }
        };
        SSLContext sslContext = SSLContext.getInstance("SSL");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLContext(sslContext)
                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                .build();
        HttpComponentsClientHttpRequestFactory customRequestFactory = new HttpComponentsClientHttpRequestFactory();
        customRequestFactory.setHttpClient(httpClient);
        //set timeout
        logger.info("change time out VBDLISService: restConnectTimeout=" + restConnectTimeout + " ,restReadTimeout=" + restReadTimeout);
        customRequestFactory.setConnectTimeout(restConnectTimeout);
        customRequestFactory.setConnectionRequestTimeout(restConnectTimeout);
        customRequestFactory.setReadTimeout(restReadTimeout);

        return builder.requestFactory(() -> customRequestFactory).build();
        //return builder.build();
    }

    private String getToken(String tokenUrl, String consumerKey, String consumerSecret, String tokenValue) {
        String tokenStr = "";

        if (tokenValue != null && !tokenValue.isEmpty()) {
            try {
                HttpHeaders headers = new HttpHeaders();
                if (Objects.nonNull(vbdilisFBTEnable) && vbdilisFBTEnable){
                    headers.set("Lgspaccesstoken", tokenValue);
                }else {
                    headers.set("Authorization", tokenValue);
                }
                MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
                map.add("grant_type", "client_credentials");

                HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<MultiValueMap<String, String>>(map,
                        headers);

                JSONObject response = new JSONObject(
                        restTemplate.exchange(tokenUrl, HttpMethod.POST, entity, String.class).getBody());
                logger.info("Response from " + tokenUrl + ": " + response);
                tokenStr = response.getString("access_token");
                logger.info("DIGO-Info: getToken value = " + tokenStr);
            } catch (Exception e) {
                logger.info("DIGO-Info: getToken Exception " + e.getMessage());
            }
        } else {
            try {
                ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
                details.setAccessTokenUri(tokenUrl);
                details.setClientId(consumerKey);
                details.setClientSecret(consumerSecret);
                details.setGrantType("client_credentials");
                tokenStr = new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext()).getAccessToken()
                        .getValue();
                logger.info("DIGO-Info: getToken value = " + tokenStr);
            } catch (Exception e) {
                logger.info("DIGO-Info: getToken Exception " + e.getMessage());
            }

        }

        return tokenStr;
    }

    private String getTokenV2(IntegratedConfigurationDto config) {
        if(!this.getTokenV2Enable){
            String tokenUrl = config.getParametersValue("token-url");
            String consumerKey = config.getParametersValue("consumer-key");
            String consumerSecret = config.getParametersValue("consumer-secret");
            String tokenValue = config.getParametersValue("token-value");
            return getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);
        }

        String tokenStr = "";
        TokenPartner tokenPartner = this.getValidToken(new Date());
        if(Objects.nonNull(tokenPartner) && this.refresh == false){
            tokenStr = tokenPartner.getToken();
        }else{
            String tokenUrl = config.getParametersValue("token-url");
            String consumerKey = config.getParametersValue("consumer-key");
            String consumerSecret = config.getParametersValue("consumer-secret");
            String tokenValue = config.getParametersValue("token-value");

            if (tokenValue != null && !tokenValue.isEmpty()) {
                try {
                    HttpHeaders headers = new HttpHeaders();
                    headers.set("Authorization", tokenValue);

                    MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
                    map.add("grant_type", "client_credentials");

                    HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<MultiValueMap<String, String>>(map,
                            headers);

                    JSONObject response = new JSONObject(
                            restTemplate.exchange(tokenUrl, HttpMethod.POST, entity, String.class).getBody());
                    logger.info("Response from " + tokenUrl + ": " + response);
                    tokenStr = response.getString("access_token");
                    saveToken(tokenStr, response.getInt("expires_in"), tokenUrl);
                    logger.info("DIGO-Info: getToken value = " + tokenStr);
                } catch (Exception e) {
                    logger.info("DIGO-Info: getToken Exception " + e.getMessage());
                }
            } else {
                try {
                    ClientCredentialsResourceDetails details = new ClientCredentialsResourceDetails();
                    details.setAccessTokenUri(tokenUrl);
                    details.setClientId(consumerKey);
                    details.setClientSecret(consumerSecret);
                    details.setGrantType("client_credentials");
                    OAuth2RestTemplate oAuth2RestTemplate = new OAuth2RestTemplate(details, new DefaultOAuth2ClientContext());
                    tokenStr = oAuth2RestTemplate.getAccessToken().getValue();
                    int expiryDate = oAuth2RestTemplate.getAccessToken().getExpiresIn();
                    saveToken(tokenStr, expiryDate, tokenUrl);
                    logger.info("DIGO-Info: getToken value = " + tokenStr);
                } catch (Exception e) {
                    logger.info("DIGO-Info: getToken Exception " + e.getMessage());
                }
            }
            this.refresh = false;
        }

        return tokenStr;
    }


    private TokenPartner getValidToken(Date curentDate){
        Query query = new Query();
        query.addCriteria(Criteria.where("expiryDate").gte(curentDate));
        query.addCriteria(Criteria.where("type.id").is(5));

        query.with(Sort.by(Sort.Direction.DESC, "expiryDate"));

        TokenPartner tokenPartner = mongoTemplate.findOne(query, TokenPartner.class);

        if(Objects.isNull(tokenPartner)){
            return null;
        }

        return tokenPartner;
    }

    private void saveToken(String token, Integer expiryDate, String tokenUrl){
        if(token != null && token != ""){
            Date currentDate = new Date();

            TokenPartner tokenPartner = new TokenPartner();
            tokenPartner.setType(5, "Vbdlis", tokenUrl);

            tokenPartner.setAccountId(Context.getAccountId());
            tokenPartner.setUsername(Context.getUserFullname());
            tokenPartner.setUserId(Context.getUserId());
            tokenPartner.setCreatedDate(currentDate);
            tokenPartner.setUpdatedDate(currentDate);
            tokenPartner.setExpiryDate(new Date(currentDate.getTime() + (expiryDate-120) * 1000L));
            tokenPartner.setToken(token);
            tokenPartnerRepository.save(tokenPartner);
        }
    }

    public ReceivingDossierResponse receivingDossier(ReceivingDossierDto params) {
        logger.info("DIGO-Info: begin receivingDossier with MaHoSoMotCua = " + params.getMaHoSoMotCua());

        //Get config
        IntegratedConfigurationDto config;
        var result = new ReceivingDossierResponse();
        var status = new VbdlisResDto.Status();
        status.setSuccess(false);
        result.setData(0);

        Boolean shouldRetry = true;
        int attempts = 0;

        while (shouldRetry && attempts < 2) {
        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
//            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("tiep-nhan-ho-so-url").toString();
//            String consumerKey = config.getParametersValue("consumer-key");
//            String consumerSecret = config.getParametersValue("consumer-secret");
//            String tokenValue = config.getParametersValue("token-value");
//
//            String token = getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);
            String token = getTokenV2(config);

            // get status and link file dossier in quantritichhop
            String dossierTaskStatusWaitWithHadresult = config.getParametersValue("dossier-task-status-wait-with-had-result-id");
            String dossierMenuTaskRemindWithHadresult = config.getParametersValue("dossier-menu-task-remind-with-had-result-id");
            
            String dossierTaskStatusWaitWithdrawn = config.getParametersValue("dossier-task-status-wait-withdrawn-id");
            String dossierMenuTaskRemindWithdrawalRequest = config.getParametersValue("dossier-menu-task-remind-withdrawal-request-id");
            
            String dossierTaskStatusWaitWithCancle = config.getParametersValue("dossier-task-status-wait-with-cancle-id");
            String dossierMenuTaskRemindWithCancle = config.getParametersValue("dossier-menu-task-remind-with-cancle-id");
            
            String linkFileVbdlis = config.getParametersValue("link-file-vbdlis");



            try{
                if(enableSetTimeOut && Objects.nonNull(config.getParametersValue("enable-timeout")) && (Boolean) config.getParameterValue("enable-timeout") == true){
                    this.setCustomTimeouts((Integer) config.getParameterValue("connect-timeout"), (Integer) config.getParameterValue("read-timeout"));
                }
            }catch (Exception e){
            }
            
            // map status and link file dossier
            params.setDossierTaskStatusWaitWithHadresult(dossierTaskStatusWaitWithHadresult);
            params.setDossierMenuTaskRemindWithHadresult(dossierMenuTaskRemindWithHadresult);
            params.setDossierTaskStatusWaitWithdrawn(dossierTaskStatusWaitWithdrawn);
            params.setDossierMenuTaskRemindWithdrawalRequest(dossierMenuTaskRemindWithdrawalRequest);
            params.setDossierTaskStatusWaitWithCancle(dossierTaskStatusWaitWithCancle);
            params.setDossierMenuTaskRemindWithCancle(dossierMenuTaskRemindWithCancle);
            params.setLinkFileVbdlis(linkFileVbdlis);

            //setTimeout(restTemplate, 60000);
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            if (Objects.nonNull(vbdilisFBTEnable) && vbdilisFBTEnable){
                headers.set("Lgspaccesstoken", config.getParametersValue("token-value"));
            }
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), ReceivingDossierResponse.class);
                if (response.getData() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    status.setMessage(jsonModelInput);
                    result.setStatus(status);
                } else {
                    result = response;
                }
                shouldRetry = false;
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                status.setMessage(jsonModelInput);
                result.setStatus(status);
                shouldRetry = false;
            }
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            if(ex.getStatusCode() == HttpStatus.UNAUTHORIZED && attempts == 0 && this.getTokenV2Enable){
                attempts++;
                this.refresh = true;
            }else {
                shouldRetry = false;
                status.setMessage(ex.getMessage());
                result.setStatus(status);
            }
        } catch (Exception ex) {
            status.setMessage(ex.getMessage());
            result.setStatus(status);
            shouldRetry = false;
        }
        }
        return result;
    }

    public ResultUpdateDossierResponse resultUpdateDossier(ResultUpdateDossierDto params) {
        logger.info("DIGO-Info: begin resultUpdateDossier with soBienNhan = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;
        var result = new ResultUpdateDossierResponse();
        var status = new VbdlisResDto.Status();
        status.setSuccess(false);
        result.setData(0);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
//            setTimeout(restTemplate, 60000);

            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("cap-nhat-ket-qua-url").toString();
            String consumerKey = config.getParametersValue("consumer-key");
            String consumerSecret = config.getParametersValue("consumer-secret");
            String tokenValue = config.getParametersValue("token-value");
            String token = getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            if (Objects.nonNull(vbdilisFBTEnable) && vbdilisFBTEnable){
                headers.set("Lgspaccesstoken", config.getParametersValue("token-value"));
            }
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), ResultUpdateDossierResponse.class);
                if (response.getData() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    status.setMessage(jsonModelInput);
                    result.setStatus(status);
                } else {
                    result = response;
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                status.setMessage(jsonModelInput);
                result.setStatus(status);
            }
        } catch (Exception e) {
            status.setMessage(e.getMessage());
            result.setStatus(status);
        }

        return result;
    }

    public AdditionalStatusUpdateDossierResponse aditionalStatusUpdateDossier(AdditionalStatusUpdateDossierDto params) {
        logger.info("DIGO-Info: begin aditionalStatusUpdateDossier with soBienNhan = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;
        var result = new AdditionalStatusUpdateDossierResponse();
        var status = new VbdlisResDto.Status();
        status.setSuccess(false);
        result.setData(0);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("cap-nhat-trang-thai-bo-sung-url").toString();
            String consumerKey = config.getParametersValue("consumer-key");
            String consumerSecret = config.getParametersValue("consumer-secret");
            String tokenValue = config.getParametersValue("token-value");
            String token = getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            if (Objects.nonNull(vbdilisFBTEnable) && vbdilisFBTEnable){
                headers.set("Lgspaccesstoken", config.getParametersValue("token-value"));
            }
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), AdditionalStatusUpdateDossierResponse.class);
                if (response.getData() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    status.setMessage(jsonModelInput);
                    result.setStatus(status);
                } else {
                    result = response;
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                status.setMessage(jsonModelInput);
                result.setStatus(status);
            }
        } catch (Exception e) {
            status.setMessage(e.getMessage());
            result.setStatus(status);
        }
        return result;
    }

    public SendProcessingNoticeDossierResponse sendProcessingNoticeDossier(SendProcessingNoticeDossierDto params) {
        logger.info("DIGO-Info: begin sendProcessingNoticeDossier with soBienNhan = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;
        var result = new SendProcessingNoticeDossierResponse();
        var status = new VbdlisResDto.Status();
        status.setSuccess(false);
        result.setData(0);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        Boolean shouldRetry = true;
        int attempts = 0;

        while (shouldRetry && attempts < 2) {
        try {
//            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("thong-bao-url").toString();
//            String consumerKey = config.getParametersValue("consumer-key");
//            String consumerSecret = config.getParametersValue("consumer-secret");
//            String tokenValue = config.getParametersValue("token-value");

//            String token = getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);
            String token = getTokenV2(config);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            if (Objects.nonNull(vbdilisFBTEnable) && vbdilisFBTEnable){
                headers.set("Lgspaccesstoken", config.getParametersValue("token-value"));
            }
            HttpEntity<?> request = new HttpEntity<>(params, headers);

            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), SendProcessingNoticeDossierResponse.class);
                if (response.getData() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    status.setMessage(jsonModelInput);
                    result.setStatus(status);
                } else {
                    result = response;
                }
                shouldRetry = false;
            } catch (HttpClientErrorException | HttpServerErrorException ex) {
                if(ex.getStatusCode() == HttpStatus.UNAUTHORIZED && attempts == 0 && this.getTokenV2Enable){
                    attempts++;
                    this.refresh = true;
                }else {
                    shouldRetry = false;
                    status.setMessage(ex.getMessage());
                    result.setStatus(status);
                }
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                status.setMessage(jsonModelInput);
                result.setStatus(status);
                shouldRetry = false;
            }

        } catch (Exception e) {
            status.setMessage(e.getMessage());
            result.setStatus(status);
            shouldRetry = false;
        }
        }
        return result;
    }

    public FeedbackProfileResultDossierResponse feedbackProfileResultDossier(FeedbackProfileResultDossierDto params) {
        logger.info("DIGO-Info: begin feedbackProfileResultDossier with soBienNhan = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;
        var result = new FeedbackProfileResultDossierResponse();
        var status = new VbdlisResDto.Status();
        status.setSuccess(false);
        result.setData(0);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        Boolean shouldRetry = true;
        int attempts = 0;

        while (shouldRetry && attempts < 2) {
        try {
//            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("phan-hoi-sai-ket-qua-url").toString();
//            String consumerKey = config.getParametersValue("consumer-key");
//            String consumerSecret = config.getParametersValue("consumer-secret");
//            String tokenValue = config.getParametersValue("token-value");

//            String token = getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);
            String token = getTokenV2(config);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            if (Objects.nonNull(vbdilisFBTEnable) && vbdilisFBTEnable){
                headers.set("Lgspaccesstoken", config.getParametersValue("token-value"));
            }
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            //ResponseEntity<FeedbackProfileResultDossierResponse> result = restTemplate.exchange(endpoint, HttpMethod.POST, request, FeedbackProfileResultDossierResponse.class);

            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), FeedbackProfileResultDossierResponse.class);
                if (response.getData() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    status.setMessage(jsonModelInput);
                    result.setStatus(status);
                } else {
                    result = response;
                }
                shouldRetry = false;
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                status.setMessage(jsonModelInput);
                result.setStatus(status);
            }

        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            if(ex.getStatusCode() == HttpStatus.UNAUTHORIZED && attempts == 0 && this.getTokenV2Enable){
                attempts++;
                this.refresh = true;
            }else {
                shouldRetry = false;
                status.setMessage(ex.getMessage());
                result.setStatus(status);
            }
        }catch (Exception e) {
            status.setMessage(e.getMessage());
            result.setStatus(status);
            shouldRetry = false;
        }
        }
        return result;
    }

    public DanhMucThuTucResponse getDanhMucThuTucHanhChinh() {
        logger.info("DIGO-Info: begin getDanhMucThuTucHanhChinh !");

        DanhMucThuTucResponse result = new DanhMucThuTucResponse();
        try {
            Map<String, Object> procedureParrams = new HashMap<>();

            var urlContent = "/procedure-process-definition/--get-procedure-vbdlis";
//            String url = "http://localhost:8069" + urlContent;
            String url = microservice.basepadUri(urlContent).toUriString();
            result = MicroserviceExchange.get(restTemplate, url, DanhMucThuTucResponse.class, procedureParrams);

            return result;
        } catch (Exception e) {
            result.setResult(0);
            result.setMessage(e.getMessage());
        }

        return result;
    }

    public DanhMucThuTucResponse getDanhMucThuTucHanhChinhQNI() {
        logger.info("DIGO-Info: begin getDanhMucThuTucHanhChinh !");

        DanhMucThuTucResponse result = new DanhMucThuTucResponse();
        try {
            Map<String, Object> procedureParrams = new HashMap<>();

            var urlContent = "/procedure-process-definition/--get-procedure-vbdlis-qni";
            //String url = "http://localhost:8069" + urlContent;
            String url = microservice.basepadUri(urlContent).toUriString();
            result = MicroserviceExchange.get(restTemplate, url, DanhMucThuTucResponse.class, procedureParrams);

            return result;
        } catch (Exception e) {
            result.setResult(0);
            result.setMessage(e.getMessage());
        }

        return result;
    }

    public DanhMucNguoiDungResponse getDanhMucNguoiDung() {
        logger.info("DIGO-Info: begin getDanhMucNguoiDung !");

        var result = new DanhMucNguoiDungResponse();
        IntegratedConfigurationDto config;

        Query query = new Query();
        query.addCriteria(Criteria.where("service.id").is(this.serviceId));
        query.addCriteria(Criteria.where("deleted").is(false));
        var configDoc = mongoTemplate.findOne(query, IntegratedConfiguration.class);
        config = GsonUtils.copyObject(configDoc, IntegratedConfigurationDto.class);

        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        try {
            String agencyId = config.getParametersValue("agency-id");
            Map<String, Object> procedureParrams = new HashMap<>();
            String url = "/user/--user-vbdlis?agency-id=" + agencyId;
            String userUrl = microservice.humanUri(url).toUriString();
//            String userUrl = "http://localhost:8070" + url;
            String responseStr = MicroserviceExchange.get(restTemplate, userUrl, String.class, procedureParrams);

            JsonArray userContentArrays = new JsonParser().parse(responseStr).getAsJsonArray();

            var danhMucNguoiDungDtos = new ArrayList<DanhMucNguoiDungDto>();
            for (int i = 0; i < userContentArrays.size(); i++) {
                var target = new DanhMucNguoiDungDto();
                target.setMaNguoiDung(userContentArrays.get(i).getAsJsonObject().get("id").getAsString());
                target.setTenNguoiDung(userContentArrays.get(i).getAsJsonObject().get("fullname").getAsString());

                JsonArray experiences = userContentArrays.get(i).getAsJsonObject().get("experience").getAsJsonArray();
                for (int j = 0; j < experiences.size(); j++) {
                    var objectExperience = experiences.get(j).getAsJsonObject();
                    Boolean isPrimary = objectExperience.get("primary").getAsBoolean();
                    if (isPrimary) {
                        var positionName = objectExperience.get("position").getAsJsonObject().get("name").getAsString();
                        target.setChucDanh(positionName);

                        var agencyName = objectExperience.get("agency").getAsJsonObject().get("name").getAsString();
                        target.setTenPhongBan(agencyName);
                    }
                }

                danhMucNguoiDungDtos.add(target);
            }

            result.setResult(1);
            result.setData(danhMucNguoiDungDtos);
        } catch (Exception e) {
            result.setResult(0);
            result.setMessage(e.getMessage());
        }

        return result;
    }

    public DanhMucTrangThaiResponse getDanhMucTrangThai() {
        logger.info("DIGO-Info: begin getDanhMucTrangThai !");

        var result = new DanhMucTrangThaiResponse();
        try {
            Map<String, Object> procedureParrams = new HashMap<>();
            String tagUrl = microservice.basecatUri("/tag/--by-category-id?category-id=5f3a491c4e1bd312a6f00012&page=0&size=1000").toUriString();
            String responseStr = MicroserviceExchange.get(restTemplate, tagUrl, String.class, procedureParrams);
            Map<String, Object> data = new ObjectMapper().readValue(responseStr, Map.class);
            List<DossierStatusDto> dossierStatues = GsonUtils.copyList(gson.toJson(data.get("content")), DossierStatusDto.class);

            var danhMucTrangThais = new ArrayList<DanhMucTrangThaiDto>();
            for (DossierStatusDto dossierStatus : dossierStatues) {
                var target = new DanhMucTrangThaiDto();
                target.setMaTrangThai(dossierStatus.getId());
                target.setTenTrangThai(dossierStatus.getName());

                danhMucTrangThais.add(target);
            }
            result.setResult(1);
            result.setData(danhMucTrangThais);
        } catch (Exception e) {
            result.setResult(0);
            result.setMessage(e.getMessage());
        }

        return result;
    }

    public YeuCauBoSungResponse postDongBoTrangThaiKetThuc(DongBoTrangThaiKetThucDto dongBoTrangThaiKetThucDto) {
        logger.info("DIGO-Info: begin postDongBoTrangThaiKetThuc !");

        var result = new YeuCauBoSungResponse();
        result.setResult(0);
        try {
            if(dongBoTrangThaiKetThucDto.getSoBienNhan() == null || dongBoTrangThaiKetThucDto.getSoBienNhan() == ""){
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[] { "SoBienNhan" }));
                return result;
            }

            var urlContent = String.format("/vbdlis/%s/--status", dongBoTrangThaiKetThucDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
//            String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, dongBoTrangThaiKetThucDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");
            String message = responseFromPadmanObj.getString("message");
            
            if(affectedRows > 0){
                result.setResult(1);
            }else{
                result.setMessage(message);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        if(qniEnableVBdlisTimeout && result.getMessage() != null && result.getMessage().contains("Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out")){
            result.setResult(499);
        }

        return result;
    }

    public YeuCauBoSungResponse postDongBoTrangThaiKetThucv2(DongBoTrangThaiKetThucDto dongBoTrangThaiKetThucDto) {
        logger.info("DIGO-Info: begin postDongBoTrangThaiKetThuc !");

        var result = new YeuCauBoSungResponse();
        result.setResult(0);
        try {
            if(dongBoTrangThaiKetThucDto.getSoBienNhan() == null || dongBoTrangThaiKetThucDto.getSoBienNhan() == ""){
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[] { "SoBienNhan" }));
                return result;
            }

            var urlContent = String.format("/vbdlis/%s/--status2", dongBoTrangThaiKetThucDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
//            String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, dongBoTrangThaiKetThucDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");
            String message = responseFromPadmanObj.getString("message");

            if(affectedRows > 0){
                result.setResult(affectedRows);
                result.setMessage(message);
            }else{
                result.setResult(affectedRows);
                result.setMessage(message);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        if(qniEnableVBdlisTimeout && result.getMessage() != null && result.getMessage().contains("Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out")){
            result.setResult(499);
        }


        return result;
    }


    public YeuCauBoSungResponse postYeucauBoSung(YeuCauBoSungHoSoDto yeuCauBoSungHoSoDto) {
        logger.info("DIGO-Info: begin postYeucauBoSung !");

        var result = new YeuCauBoSungResponse();
        result.setResult(0);
        try {
            if (yeuCauBoSungHoSoDto.getSoBienNhan() == null || yeuCauBoSungHoSoDto.getSoBienNhan() == "") {
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[]{"SoBienNhan"}));
                return result;
            }

            var urlContent = String.format("/vbdlis/%s/--additional-request", yeuCauBoSungHoSoDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
//            String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, yeuCauBoSungHoSoDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");
            String message = "";
            try{
                message = responseFromPadmanObj.getString("message"); // phục vụ cho trường hợp return true ko trả message
            }catch (Exception e){

            }

            if (affectedRows > 0) {
                result.setResult(affectedRows);
                result.setMessage(message);
            } else {
                result.setResult(affectedRows);
                result.setMessage(message);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        if(qniEnableVBdlisTimeout && result.getMessage() != null && result.getMessage().contains("Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out")){
            result.setResult(499);
        }

        return result;
    }

    private void setTimeout(RestTemplate restTemplate, int timeout) {
        //Explicitly setting ClientHttpRequestFactory instance to
        //SimpleClientHttpRequestFactory instance to leverage
        //set*Timeout methods
        restTemplate.setRequestFactory(new SimpleClientHttpRequestFactory());
        SimpleClientHttpRequestFactory rf = (SimpleClientHttpRequestFactory) restTemplate
                .getRequestFactory();
        rf.setConnectTimeout(timeout);
    }

    public AdditionalStatusUpdateDossierResponse updateAdditionalRequestDossier(AdditionalStatusUpdateDossierDto params) {
        logger.info("DIGO-Info: begin updateAdditionalRequestDossier with getSoBienNhan = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;
        var result = new AdditionalStatusUpdateDossierResponse();
        var status = new VbdlisResDto.Status();
        status.setSuccess(false);
        result.setData(0);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            Query query = new Query();
            query.addCriteria(Criteria.where("service.id").is(this.serviceId));
            query.addCriteria(Criteria.where("deleted").is(false));
            var configDoc = mongoTemplate.findOne(query, IntegratedConfiguration.class);
            config = GsonUtils.copyObject(configDoc, IntegratedConfigurationDto.class);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        Boolean shouldRetry = true;
        int attempts = 0;

        while (shouldRetry && attempts < 2) {
        try {
//            setTimeout(restTemplate, 60000);

//            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("cap-nhat-trang-thai-bo-sung-url").toString();
//            String consumerKey = config.getParametersValue("consumer-key");
//            String consumerSecret = config.getParametersValue("consumer-secret");
//            String tokenValue = config.getParametersValue("token-value");

//            String token = getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);
            String token = getTokenV2(config);

            String linkFileVbdlis = config.getParametersValue("link-file-vbdlis");
            params.setLinkFileVbdlis(linkFileVbdlis);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), AdditionalStatusUpdateDossierResponse.class);
                if (response.getData() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    status.setMessage(jsonModelInput);
                    result.setStatus(status);
                } else {
                    result = response;
                }
                shouldRetry = false;
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                status.setMessage(jsonModelInput);
                result.setStatus(status);
                shouldRetry = false;
            }
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            if(ex.getStatusCode() == HttpStatus.UNAUTHORIZED && attempts == 0 && this.getTokenV2Enable){
                attempts++;
                this.refresh = true;
            }else {
                shouldRetry = false;
                status.setMessage(ex.getMessage());
                result.setStatus(status);
            }
        }  catch (Exception ex) {
            status.setMessage(ex.getMessage());
            result.setStatus(status);
            shouldRetry = false;
        }
        }
        return result;
    }

    public GuiKetQuaThueResponse postTaxResult(PutTaxResultVbdlisDto putTaxResultVbdlisDto) {
        logger.info("DIGO-Info: begin postTaxResult !");

        var result = new GuiKetQuaThueResponse();
        result.setResult(0);
        try {
            if(putTaxResultVbdlisDto.getSoBienNhan() == null || putTaxResultVbdlisDto.getSoBienNhan() == ""){
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[] { "SoBienNhan" }));
                return result;
            }

            var urlContent = String.format("/vbdlis/%s/--next-task-financial", putTaxResultVbdlisDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
//            String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, putTaxResultVbdlisDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");
            String message = responseFromPadmanObj.getString("message");

            if(affectedRows > 0){
                result.setResult(affectedRows);
                result.setMessage(message);
            }else{
                result.setResult(affectedRows);
                result.setMessage(message);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        if(qniEnableVBdlisTimeout && result.getMessage() != null && result.getMessage().contains("Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out")){
            result.setResult(499);
        }

        return result;
    }

    public GuiKetQuaThueResponse postTaxResultv2(PutTaxResultVbdlisDto putTaxResultVbdlisDto) {
        logger.info("DIGO-Info: begin postTaxResult !");

        var result = new GuiKetQuaThueResponse();
        result.setResult(0);
        try {
            if(putTaxResultVbdlisDto.getSoBienNhan() == null || putTaxResultVbdlisDto.getSoBienNhan() == ""){
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[] { "SoBienNhan" }));
                return result;
            }

            var urlContent = String.format("/vbdlis/%s/--next-task-financial-v2", putTaxResultVbdlisDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
//            String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, putTaxResultVbdlisDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");

            if(affectedRows > 0){
                result.setResult(affectedRows);
            }else{
                result.setResult(affectedRows);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        if(qniEnableVBdlisTimeout && result.getMessage() != null && result.getMessage().contains("Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out")){
            result.setResult(499);
        }

        return result;
    }

    public ResultUpdateDossierResponse updateResultFinishDossier(UpdateFinishDossierDto params) {
        logger.info("DIGO-Info: begin updateResultFinishDossier with MaHoSoMotCua = " + params.getSoBienNhan());

        //Get config
        IntegratedConfigurationDto config;
        var result = new ResultUpdateDossierResponse();
        var status = new VbdlisResDto.Status();
        status.setSuccess(false);
        result.setData(0);

        if (Objects.nonNull(params.getConfigId())) {
            config = configurationService.getConfig(params.getConfigId());
        } else {
            config = configurationService.getConfig(params.getAgencyId(), params.getSubsystemId(), this.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        Boolean shouldRetry = true;
        int attempts = 0;

        while (shouldRetry && attempts < 2) {
        try {
//            String tokenUrl = config.getParametersValue("token-url");
            String endpoint = config.getParametersValue("cap-nhat-ket-qua-url").toString();
//            String consumerKey = config.getParametersValue("consumer-key");
//            String consumerSecret = config.getParametersValue("consumer-secret");
//            String tokenValue = config.getParametersValue("token-value");

//            String token = getToken(tokenUrl, consumerKey, consumerSecret, tokenValue);
            String token = getTokenV2(config);

            String linkFileVbdlis = config.getParametersValue("link-file-vbdlis");

            // map status and link file dossier
            params.setLinkFileVbdlis(linkFileVbdlis);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token);
            HttpEntity<?> request = new HttpEntity<>(params, headers);
            ResponseEntity<Object> responseObject = restTemplate.exchange(endpoint, HttpMethod.POST, request, Object.class);

            // bat truong hop truc nsgp tra ve ko co reponse hoac ko dung format
            try {
                var response = GsonUtils.copyObject(responseObject.getBody(), ResultUpdateDossierResponse.class);
                if (response.getData() == null) {
                    ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                    String jsonModelInput = ow.writeValueAsString(responseObject);
                    status.setMessage(jsonModelInput);
                    result.setStatus(status);
                } else {
                    result = response;
                }
                shouldRetry = false;
            } catch (Exception ex) {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String jsonModelInput = ow.writeValueAsString(responseObject);
                status.setMessage(jsonModelInput);
                result.setStatus(status);
                shouldRetry = false;
            }
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            if(ex.getStatusCode() == HttpStatus.UNAUTHORIZED && attempts == 0 && this.getTokenV2Enable){
                attempts++;
                this.refresh = true;
            }else {
                shouldRetry = false;
                status.setMessage(ex.getMessage());
                result.setStatus(status);
            }
        }catch (Exception ex) {
            status.setMessage(ex.getMessage());
            result.setStatus(status);
            shouldRetry = false;
        }
        }
        logger.info("DIGO-Info: End updateResultFinishDossier with status = " + result.getStatus());
        return result;
    }

    public YeuCauBoSungResponse postYeuCauCapNhatHoSo(YeuCauCapNhatHoSoDto yeuCauCapNhatHoSoDto) {
        logger.info("DIGO-Info: begin postDongBoTrangThaiKetThuc !");

        var result = new YeuCauBoSungResponse();
        result.setResult(0);
        try {
            if(yeuCauCapNhatHoSoDto.getSoBienNhan() == null || yeuCauCapNhatHoSoDto.getSoBienNhan() == ""){
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[] { "SoBienNhan" }));
                return result;
            }

            var urlContent = String.format("/vbdlis/%s/--updatedossier", yeuCauCapNhatHoSoDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
//            String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, yeuCauCapNhatHoSoDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");
            String message = responseFromPadmanObj.getString("message");

            if(affectedRows > 0){
                result.setResult(affectedRows);
                result.setMessage(message);
            }else{
                result.setResult(affectedRows);
                result.setMessage(message);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        if(qniEnableVBdlisTimeout && result.getMessage() != null && result.getMessage().contains("Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out")){
            result.setResult(499);
        }

        return result;
    }

    public ChuyenHoSoResponse capNhatTienDoHoSoMCv2(ChuyenHoSoDto chuyenHoSoDto) throws ParseException {

        var result = new ChuyenHoSoResponse();
        result.setResult(0);

        ChuyenHoSoDto chuyenPadmanv2 = new ChuyenHoSoDto();

        try {
            if (chuyenHoSoDto.getSoBienNhan() == null || chuyenHoSoDto.getSoBienNhan() == "") {
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[]{"SoBienNhan"}));
                return result;
            }

            //gen status
            String tenTrangThai = "Xử lý công việc tiếp theo";
            if (ObjectId.isValid(chuyenHoSoDto.getMaTrangThai())) {
                var urlTag = String.format("/tag/%s", chuyenHoSoDto.getMaTrangThai());
                String uriTag = microservice.basecatUri(urlTag).toUriString();
                var resultTag = MicroserviceExchange.getFptEncode(restTemplate, uriTag, String.class);
                JSONObject jsonTag = new JSONObject(resultTag);
                JSONObject TrangThai = jsonTag.getJSONArray("trans").getJSONObject(0);
                tenTrangThai = TrangThai.getString("name").toString();
            }

            chuyenPadmanv2.setSoBienNhan(chuyenHoSoDto.getSoBienNhan());
            chuyenPadmanv2.setMaTrangThai(tenTrangThai);
            chuyenPadmanv2.setMaCanBoChuyen(chuyenHoSoDto.getMaCanBoNhan());
            chuyenPadmanv2.setNoiDungXuLy(chuyenHoSoDto.getNoiDungXuLy());
            chuyenPadmanv2.setDanhSachTapTin(chuyenHoSoDto.getDanhSachTapTin());
            chuyenPadmanv2.setNgayChuyen(chuyenHoSoDto.getNgayChuyen());

            try {
                var urlContentChuyen = "/vbdlis/--chuyen-dang-xuly";
                String urlChuyen = microservice.padmanUri(urlContentChuyen).toUriString();
//              String urlChuyen = "http://localhost:8081" + urlContentChuyen;
                var responseFromPadman = MicroserviceExchange.putJson(restTemplate, urlChuyen, chuyenPadmanv2, String.class);
                result.setResult(1);
                result.setMessage(responseFromPadman);
            } catch (RuntimeException e) {
                result.setMessage(e.toString());
                return result;
            }
        }
        catch (Exception er)
        {
            result.setMessage(er.toString());
            return result;
        }

        if(qniEnableVBdlisTimeout && result.getMessage() != null && result.getMessage().contains("Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out")){
            result.setResult(499);
        }

        return result;
    }

    public ChuyenHoSoResponse capNhatTienDoHoSoMC(ChuyenHoSoDto chuyenHoSoDto) throws ParseException {

        DVCDossierTrackingReqDto dossierTrackingDto = new DVCDossierTrackingReqDto();

        String messageStr = translator.toLocale("lang.phrase.lgsp-successful-response");
        // Set variables
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date currentDate = new Date();
        String callTime = dateFormat.format(currentDate);
        List<String> codeArr = new ArrayList<String>();
        // Generate result
        var result = new ChuyenHoSoResponse();
        result.setResult(0);
        String session = "";

        try {
            if (chuyenHoSoDto.getSoBienNhan() == null || chuyenHoSoDto.getSoBienNhan() == "") {
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[]{"SoBienNhan"}));
                return result;
            }

            var arrData = new ArrayList<DVCDossierTrackingReqDto.DVCUpdateProgressDossierDataDto>();
            DVCDossierTrackingReqDto.DVCUpdateProgressDossierDataDto data = new DVCDossierTrackingReqDto.DVCUpdateProgressDossierDataDto();

            DateFormat simpleDateFormatInput = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
            DateFormat simpleDateFormatOutput = new SimpleDateFormat("yyyyMMddHHmmss");

            data.setMaHoSo(chuyenHoSoDto.getSoBienNhan());
            data.setNguoiXuLy(chuyenHoSoDto.getMaCanBoNhan());
            data.setTrangThai(chuyenHoSoDto.getMaTrangThai());


            String dateStart = simpleDateFormatOutput.format(chuyenHoSoDto.getNgayChuyen());
            data.setNgayBatDau(dateStart);


            //gen timesheet
            var urlContent = String.format("/v2/timesheet-gen/--by-dossier-id");
            String url = microservice.basecatUri(urlContent).toUriString();

            var timesheetGenDossierInputDtoList = new ArrayList<TimesheetGenDossierInputDto>();
            TimesheetGenDossierInputDto timesheetGenDossierInputDto = new TimesheetGenDossierInputDto();
            IdDto dossierId = new IdDto();
            dossierId.setId(new ObjectId("000000000011110123456789"));
            timesheetGenDossierInputDto.setDossier(dossierId);

            Query query = new Query();
            query.addCriteria(Criteria.where("service.id").is(this.serviceId));
            query.addCriteria(Criteria.where("deleted").is(false));
            List<IntegratedConfigurationDto> configs = mongoTemplate.find(query, IntegratedConfigurationDto.class, "integratedConfiguration");
            if (configs.size() != 0 && Objects.nonNull(configs.get(0)) && Objects.nonNull(configs.get(0).getParametersValue("timesheet")) && configs.get(0).getParametersValue("timesheet") != "") {
                IdDto timesheetId = new IdDto();
                timesheetId.setId(new ObjectId(configs.get(0).getParametersValue("timesheet").toString()));
                session = configs.get(0).getParametersValue("token-value").toString();
                timesheetGenDossierInputDto.setTimesheet(timesheetId);
            }
            timesheetGenDossierInputDto.setCheckOffDay(true);
            timesheetGenDossierInputDto.setOffTime("17:00");
            timesheetGenDossierInputDto.setStartDate(chuyenHoSoDto.getNgayChuyen());
            timesheetGenDossierInputDto.setProcessingTimeUnit("h");
            timesheetGenDossierInputDto.setDuration(chuyenHoSoDto.getHanXuLy());
            timesheetGenDossierInputDtoList.add(timesheetGenDossierInputDto);

            var timesheets = MicroserviceExchange.postJson(restTemplate, url, timesheetGenDossierInputDtoList, String.class);
            JSONArray timesheetsJson = new JSONArray(timesheets);
            JSONObject timesheet = timesheetsJson.getJSONObject(0);
            String dateStr = timesheet.getString("due");

            Date date = simpleDateFormatInput.parse(dateStr);
            String dateout = simpleDateFormatOutput.format(date);
            data.setNgayKetThucTheoQuyDinh(dateout);


            //gen status
            var urlTag = String.format("/tag/%s", chuyenHoSoDto.getMaTrangThai());
            String uriTag = microservice.basecatUri(urlTag).toUriString();
            var resultTag = MicroserviceExchange.getFptEncode(restTemplate, uriTag, String.class);
            JSONObject jsonTag = new JSONObject(resultTag);
            Integer integratedCode = jsonTag.getInt("integratedCode");
            System.out.println(integratedCode);
            data.setTrangThai(integratedCode.toString());

            //gen office name
            var urlGetFully = String.format("/user/%s/--fully", chuyenHoSoDto.getMaCanBoNhan());
            String uriGetFully = microservice.humanUri(urlGetFully).toUriString();
            var resultGetFully = MicroserviceExchange.getFptEncode(restTemplate, uriGetFully, String.class);
            JSONObject jsonGetFully = new JSONObject(resultGetFully);
            String fullname = jsonGetFully.getString("fullname");
            System.out.println(fullname);
            data.setNguoiXuLy(fullname);
            JSONArray agencyObj = new JSONArray(jsonGetFully.getJSONArray("experience"));
            System.out.println("agencyObj  " + agencyObj);

            if (Objects.nonNull(agencyObj)) {
                for(int i=0; i < agencyObj.length(); i++) {
                    if (agencyObj.getJSONObject(i).get("primary").toString().equals("true")) {
                        JSONObject agencyTemp = agencyObj.getJSONObject(i).getJSONObject("agency");
                        JSONObject positionTemp = agencyObj.getJSONObject(i).getJSONObject("position");
                        String agency = agencyTemp.getString("name");
                        data.setChucDanh(positionTemp.getString("name"));
                        data.setPhongBanXuLy(agencyTemp.getString("name"));
                        dossierTrackingDto.setMadonvi(agencyTemp.getString("id"));
                        break;
                    }
                }
            }


            data.setNoiDungXuLy(chuyenHoSoDto.getNoiDungXuLy());
            arrData.add(data);
            dossierTrackingDto.setData(arrData);
            dossierTrackingDto.setSession(session);
            dossierTrackingDto.setService("000000000000000000000022");
             }
            catch (Exception er)
            {
                    result.setMessage(er.toString());
                return result;
            }

            try {
            // Write log
            for (var item : dossierTrackingDto.getData()) {
                codeArr.add(item.getMaHoSo());
            }
            String deploymentId = Context.getJwtParameterValue("deployment_id");
            dossierTrackingDto.setDeploymentId(deploymentId);


            // Send message to Kafka
            lgspHCMDossierProducerStream.sendNotificationDossierTracking(dossierTrackingDto);
            // Write log
            result.setResult(1);
            LGSPHCMLogInputDto lgspHCMLogInputDto = new LGSPHCMLogInputDto(dossierTrackingDto.getService(),
                    String.join(",", codeArr),
                    dossierTrackingDto.getMadonvi(),
                    callTime,
                    true,
                    result.toString(),
                    dossierTrackingDto.toString(),
                    dossierTrackingDto.getSession(),
                    "POST",
                    "3");
            IdDto idLogDto = lgspHCMLogService.addLog(lgspHCMLogInputDto);

            logger.info("capNhatTienDoHoSoMC - Save log with id " + idLogDto.getId().toHexString());
        } catch (Exception e) {
            // Write log
            LGSPHCMLogInputDto lgspHCMLogInputDto = new LGSPHCMLogInputDto(dossierTrackingDto.getService(),
                    String.join(",", codeArr),
                    dossierTrackingDto.getMadonvi(),
                    callTime,
                    false,
                    e.getMessage(),
                    dossierTrackingDto.toString(),
                    dossierTrackingDto.getSession(),
                    "POST",
                    "3");
            IdDto idLogDto = lgspHCMLogService.addLog(lgspHCMLogInputDto);
            logger.info("capNhatTienDoHoSoMC - Save log with id " + idLogDto.getId().toHexString() +". Exception: " + e.getMessage());
            result.setMessage(e.toString());
            throw e;
            }
        return result;
    }

    public NhanBoSungHoSoResponse postNhanBoSungHoSo(NhanBoSungHoSoDto nhanBoSungHoSoDto) {
        logger.info("DIGO-Info: begin postDongBoTrangThaiKetThuc !");

        var result = new NhanBoSungHoSoResponse();
        result.setResult(0);
        try {
            if(nhanBoSungHoSoDto.getSoBienNhan() == null || nhanBoSungHoSoDto.getSoBienNhan() == ""){
                result.setMessage(translator
                        .toLocale("digo.http.response.error.10000", new String[] { "SoBienNhan" }));
                return result;
            }

            var urlContent = String.format("/vbdlis/%s/--update-pause-request", nhanBoSungHoSoDto.getSoBienNhan());
            String url = microservice.padmanUri(urlContent).toUriString();
//            String url = "http://localhost:8081" + urlContent;
            var responseFromPadman = MicroserviceExchange.putJson(restTemplate, url, nhanBoSungHoSoDto, String.class);

            JSONObject responseFromPadmanObj = new JSONObject(responseFromPadman);
            Integer affectedRows = responseFromPadmanObj.getInt("affectedRows");
            String message = "";
            try {
                message = responseFromPadmanObj.getString("message");
            } catch (Exception e) {
                result.setResult(1); // đối vs trường hợp bên VBDLIS đồng bộ thừa
            }

            if(affectedRows > 0){
                result.setResult(affectedRows);
                result.setMessage(message);
            }else{
                result.setResult(affectedRows);
                result.setMessage(message);
            }

        } catch (Exception e) {
            result.setMessage(e.getMessage());
        }

        if(qniEnableVBdlisTimeout && result.getMessage() != null && result.getMessage().contains("Read timed out; nested exception is java.net.SocketTimeoutException: Read timed out")){
            result.setResult(499);
        }

        return result;
    }

}
