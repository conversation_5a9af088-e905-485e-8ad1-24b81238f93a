package vn.vnpt.digo.adapter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.adapter.document.ChungThucToken;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface ChungThucTokenRepository extends MongoRepository<ChungThucToken, ObjectId> {
    @Query(value = "{'token': ?0}")
    ChungThucToken getChungThucTokenByToken(String token);

    @Query(value = "{'id': ?0}")
    ChungThucToken findByUuid(UUID uuid);
}
