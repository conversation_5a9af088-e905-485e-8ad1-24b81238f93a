/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.adapter.document.MappingData;
import vn.vnpt.digo.adapter.dto.MappingDataByIdDto;
import vn.vnpt.digo.adapter.dto.MappingDataDataTypeDto;
import vn.vnpt.digo.adapter.dto.MappingDataDto;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface MappingDataRepository extends PagingAndSortingRepository<MappingData, ObjectId> {

    @Query(value = "{'type.id': ?0, 'source.id': ?1, 'status': 1, $or: [{'ignoreDeployment': true}, {'deploymentId': ?2}]}")
    MappingDataDto getBySource(ObjectId typeId, String sourceId, ObjectId deploymentId);

    @Query(value = "{'type.id': ?0, 'dest.id': ?1, 'status': 1, $or: [{'ignoreDeployment': true}, {'deploymentId': ?2}]}")
    MappingDataDto getByDest(ObjectId typeId, String sourceId, ObjectId deploymentId);

    @Query(value = "{'id': ?0, $or: [{'ignoreDeployment': true}, {'deploymentId': ?1}]}")
    MappingDataByIdDto getByIdAndDeploymentId(ObjectId id, ObjectId deploymentId);

    public Integer deleteMappingDataById(ObjectId id);

    @Query(value = "{'type.id': ?0, 'dest.id': ?1, 'status': 1, 'deleted': false, $or: [{'ignoreDeployment': true}, {'deploymentId': ?2}]}")
    MappingDataDataTypeDto getByDestId(ObjectId typeId, String sourceId, ObjectId deploymentId);

    @Query(value = "{'type.id': ?0, 'dest.name': {$regex:?1,$options:'i'}, 'status': 1, $or: [{'ignoreDeployment': true}, {'deploymentId': ?2}]}")
    MappingDataDataTypeDto getByDestName(ObjectId typeId, String destName, ObjectId deploymentId);
}
