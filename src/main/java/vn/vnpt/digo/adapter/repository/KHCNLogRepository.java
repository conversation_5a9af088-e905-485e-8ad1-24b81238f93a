package vn.vnpt.digo.adapter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.adapter.document.KHCNLog;

@Repository
public interface KHCNLogRepository extends MongoRepository<KHCNLog, ObjectId> {
    
    @Query("{ $and: [ " +
           "{ $or: [ { 'dossierCode': { $regex: ?0, $options: 'i' } }, { $expr: { $eq: [?0, null] } } ] }, " +
           "{ $or: [ { 'nationCode': { $regex: ?1, $options: 'i' } }, { $expr: { $eq: [?1, null] } } ] }, " +
           "{ $or: [ { 'api': { $regex: ?2, $options: 'i' } }, { $expr: { $eq: [?2, null] } } ] }, " +
           "{ $or: [ { 'status': ?3 }, { $expr: { $eq: [?3, null] } } ] }, " +
           "{ $or: [ { 'error': ?4 }, { $expr: { $eq: [?4, null] } } ] } " +
           "] }")
    Page<KHCNLog> findByFilters(String code, String nationCode, String api, Integer status, Boolean error, Pageable pageable);
}
