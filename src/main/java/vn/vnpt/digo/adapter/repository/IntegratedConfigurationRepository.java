/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.repository;

import java.util.ArrayList;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.adapter.document.IntegratedConfiguration;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationByIdDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationCheckApplyDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationCheckExceptDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface IntegratedConfigurationRepository
                extends PagingAndSortingRepository<IntegratedConfiguration, ObjectId> {

        @Query(value = "{'service.id': :#{#serviceId}, 'subsystem.id': :#{#subsystemId}, 'applyAgencies.id': :#{#applyAgencyId}, "
                        + "'exceptAgencies.id': {$ne: :#{#exceptAgencyId}},"
                        + "'status': 1, 'deleted': false}")
        public IntegratedConfigurationDto getAndExcept(ObjectId serviceId, ObjectId subsystemId, ObjectId applyAgencyId,
                        ObjectId exceptAgencyId, ObjectId deploymentId);

        @Query(value = "{'service.id': :#{#serviceId}, 'subsystem.id': :#{#subsystemId}, 'applyAgencies.id': :#{#applyAgencyId}, "
                        + "'exceptAgencies.id': {$ne: :#{#exceptAgencyId}}, "
                        + "'status': 1, 'deleted': false}")
        public IntegratedConfigurationDto getAndExcept(ObjectId serviceId, ObjectId subsystemId, ObjectId applyAgencyId,
                        ObjectId exceptAgencyId);

        @Query(value = "{'service.id': :#{#serviceId}, 'subsystem.id': :#{#subsystemId}, "
                        + "'exceptAgencies.id': {$ne: :#{#exceptAgencyId}},"
                        + "'applyAll': true, 'status': 1, 'deleted': false}")
        public IntegratedConfigurationDto getDefault(@Param("serviceId") ObjectId serviceId,
                        @Param("subsystemId") ObjectId subsystemId, @Param("exceptAgencyId") ObjectId exceptAgencyId,
                        @Param("deploymentId") ObjectId deploymentId);

        @Query(value = "{'service.id': :#{#serviceId}, 'subsystem.id': :#{#subsystemId}, "
                        + "'exceptAgencies.id': {$ne: :#{#exceptAgencyId}}, "
                        + "'applyAll': true, 'status': 1, 'deleted': false}")
        public IntegratedConfigurationDto getDefault(@Param("serviceId") ObjectId serviceId,
                        @Param("subsystemId") ObjectId subsystemId, @Param("exceptAgencyId") ObjectId exceptAgencyId);

        @Query(value = "{'service.id': :#{#serviceId}, 'subsystem.id': :#{#subsystemId}, "
                + "'applyAll': true, 'status': 1, 'deleted': false, 'ignoreDeployment': true}")
        public List<IntegratedConfigurationDto> getDefault(@Param("serviceId") ObjectId serviceId,
                                                     @Param("subsystemId") ObjectId subsystemId);

        @Query(value = "{'service.id': :#{#serviceId}, 'subsystem.id': :#{#subsystemId}, "
                        + "'applyAll': true, 'status': 1, 'deleted': false}")
        public IntegratedConfigurationDto checkApplyAll(ObjectId serviceId, ObjectId subsystemId);

        @Query(value = "{'id': :#{#id}, 'deploymentId': :#{#deploymentId}, "
                        + "'status': 1, 'deleted': false}")
        public IntegratedConfigurationDto getByIdAndDeploymentId(@Param("id") ObjectId id,
                        @Param("deploymentId") ObjectId deploymentId);

        @Query(value = "{'id': :#{#id}, 'status': 1, 'deleted': false}")
        public IntegratedConfigurationDto getById(@Param("id") ObjectId id);

        @Query(value = "{'code': :#{#code}, 'status': 1, 'deleted': false}")
        public IntegratedConfigurationDto getByCode(@Param("code") String code);

        @Query(value = "{'service.id': :#{#id}, 'status': 1, 'deleted': false}")
        public IntegratedConfigurationDto getByServiceId(@Param("id") ObjectId id);

        @Query(value = "{'subsystem.id': { $in: ?0}, 'service.id': ?1, 'applyAgencies.id': ?2, 'deploymentId': ?3, 'deleted': false}")
        IntegratedConfigurationCheckApplyDto checkApply(List<ObjectId> subsystemId, ObjectId serviceId,
                        ObjectId agencyId, ObjectId deploymentId);

        @Query(value = "{'subsystem.id': ?0, 'service.id': ?1, 'exceptAgencies.id': ?2, 'deploymentId': ?3}")
        IntegratedConfigurationCheckExceptDto checkExcept(ObjectId subsystemId, ObjectId serviceId, ObjectId agencyId,
                        ObjectId deploymentId);

        @Query(value = "{'id': ?0, 'deploymentId': ?1, 'deleted': false}")
        IntegratedConfigurationByIdDto getIntegratedConfigurationById(ObjectId id, ObjectId deploymentId);

        @Query(value = "{'service.id': ?0, "
                        + "'parameters.key': ?1, "
                        + "'parameters.originValue': ?2"
                        + "'deploymentId': ?3, "
                        + "'applyAll': true, 'status': 1, 'deleted': false}")
        public IntegratedConfigurationDto getDefaultZalo(ObjectId serviceId, String key, String value,
                        ObjectId deploymentId);

        @Query(value = "{'service.id': ?0, "
                        + "'status': 1,'applyAll': true, 'deleted': false}")
        public ArrayList<IntegratedConfigurationDto> getDefaultVNPTPay(ObjectId serviceId);

        @Query(value = "{'applyAgencies.id': ?0, 'deleted': false}")
        public List<IntegratedConfigurationDto> getByApplyAgencyId(ObjectId applyAgencyId);
        
        @Query(value = "{'service.id': :#{#serviceId}, 'subsystem.id': :#{#subsystemId}, 'applyAgencies.id': :#{#applyAgencyId}, "
                        + "'exceptAgencies.id': {$ne: :#{#exceptAgencyId}}, "
                        + "'status': 1, 'deleted': false}")
        public ArrayList<IntegratedConfigurationDto> getListAndExcept(ObjectId serviceId, ObjectId subsystemId, ObjectId applyAgencyId,
                        ObjectId exceptAgencyId);
        
        @Query(value = "{'service.id': :#{#serviceId}, 'subsystem.id': :#{#subsystemId}, "
                        + "'exceptAgencies.id': {$ne: :#{#exceptAgencyId}}, "
                        + "'applyAll': true, 'status': 1, 'deleted': false}")
        public  ArrayList<IntegratedConfigurationDto> getListDefault(@Param("serviceId") ObjectId serviceId,
                        @Param("subsystemId") ObjectId subsystemId, @Param("exceptAgencyId") ObjectId exceptAgencyId);

        @Query(value = "{'service.id': :#{#serviceId}, 'subsystem.id': :#{#subsystemId}, 'applyAgencies.id': :#{#applyAgencyId}, "
                + "'exceptAgencies.id': {$ne: :#{#exceptAgencyId}},"
                + "'status': 1, 'deleted': false}")
        public List<IntegratedConfigurationDto> getListConfigAgencyId(ObjectId serviceId, ObjectId subsystemId, ObjectId applyAgencyId,
                                                                           ObjectId exceptAgencyId, ObjectId deploymentId);

        
}
