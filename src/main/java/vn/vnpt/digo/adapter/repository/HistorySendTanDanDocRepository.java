package vn.vnpt.digo.adapter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import vn.vnpt.digo.adapter.document.HistorySendTanDanDoc;

@Repository
public interface HistorySendTanDanDocRepository extends MongoRepository<HistorySendTanDanDoc, ObjectId> {
    @Query(value = "{'unitCode': ?0, 'dossierCode': ?1}")
    HistorySendTanDanDoc findByUnitCodeAndDossierCode(String unitCode, String dossierCode);
}
