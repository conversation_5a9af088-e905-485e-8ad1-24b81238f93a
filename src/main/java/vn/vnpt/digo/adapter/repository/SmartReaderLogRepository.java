package vn.vnpt.digo.adapter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import vn.vnpt.digo.adapter.document.SmartReaderLog;

import java.util.List;

public interface SmartReaderLogRepository extends MongoRepository<SmartReaderLog, ObjectId> {
    @Query(value = "{'hash' : ?0, 'first': 1}")
    SmartReaderLog findByFirstHash(String hash);
}
