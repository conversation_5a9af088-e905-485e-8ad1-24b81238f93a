package vn.vnpt.digo.adapter.repository;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import vn.vnpt.digo.adapter.document.DailyLogRequests;

import java.util.Date;

public interface DailyLogRequestsRepository extends MongoRepository<DailyLogRequests, ObjectId> {
    @Query(value = "{'type' : ?0,'date' : ?1}")
    DailyLogRequests findByType(Integer type, String date);
}
