package vn.vnpt.digo.adapter.api;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.service.EdigOcrService;

import java.io.IOException;
import java.util.HashMap;

/**
 *
 * <AUTHOR>
 */
@RestController
@IcodeAuthorize("vnpt.permission.edig")
@RequestMapping("/edig-ocr")
public class EdigOcrController {
    @Autowired
    private EdigOcrService edigOcrService;

    @PostMapping("/{code}")
    public ResponseEntity<Object> upload(
            @ModelAttribute MultipartFile[] files,
            @PathVariable(value = "code", required = true) String code,
            @RequestParam(value = "subsystem-id", required = true) ObjectId subsystemId,
            @RequestParam(value = "eform-id", required = false) ObjectId eformId) throws IOException {
        HashMap<String,Object> response = edigOcrService.getData(files,code,eformId,subsystemId);
        return ResponseEntity.ok(response);
    }
}
