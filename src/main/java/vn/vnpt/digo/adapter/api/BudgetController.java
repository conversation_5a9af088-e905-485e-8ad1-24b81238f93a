package vn.vnpt.digo.adapter.api;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.BudgetDossierByPeriodDataDto;
import vn.vnpt.digo.adapter.dto.BudgetDossierDataDto;
import vn.vnpt.digo.adapter.dto.GettingConfigurationParamsDto;
import vn.vnpt.digo.adapter.service.BudgetService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/budget")
@IcodeAuthorize("vnpt.permission.budget")
public class BudgetController {

    @Autowired
    private BudgetService budgetService;

    Logger logger = LoggerFactory.getLogger(BudgetController.class);

    @GetMapping("/--list")
    public Page<BudgetDossierDataDto> getBudgetDossierList(HttpServletRequest request,
            Pageable pageable,
            @Valid @RequestParam GettingConfigurationParamsDto config,
            @RequestParam(name = "from-date", required = true) String fromDate,
            @RequestParam(name = "to-date", required = true) String toDate) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return budgetService.getBudgetDossierList(fromDate, toDate, config, pageable);
    }

    @GetMapping("/--detail")
    public BudgetDossierDataDto getBudgetDossierDetail(HttpServletRequest request,
            @Valid @RequestParam GettingConfigurationParamsDto config,
            @RequestParam(name = "id", required = true) String id) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return budgetService.getBudgetDossierDetail(id, config);
    }

    @GetMapping("/--by-period")
    public BudgetDossierByPeriodDataDto getBudgetDossierByPeriod(HttpServletRequest request,
            @Valid @RequestParam GettingConfigurationParamsDto config,
            @RequestParam(name = "from-date", required = true) String fromDate,
            @RequestParam(name = "to-date", required = true) String toDate) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return budgetService.getBudgetDossierByPeriod(fromDate, toDate, config);
    }

}
