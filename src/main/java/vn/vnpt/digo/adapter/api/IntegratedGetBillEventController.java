package vn.vnpt.digo.adapter.api;

import java.text.ParseException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.document.IntegratedGetBillEvent;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.service.IntegratedEventService;
import vn.vnpt.digo.adapter.dto.GetDossierDetailDto;
import vn.vnpt.digo.adapter.service.IntegratedGetBillEventService;

@RestController
@RequestMapping("/integrated-get-bill-event")
@IcodeAuthorize("vnpt.permission.integratedevent")
public class IntegratedGetBillEventController {
    
    @Autowired
    private IntegratedGetBillEventService integratedGetBillEventService;

    Logger logger = LoggerFactory.getLogger(IntegratedGetBillEventController.class);

    @GetMapping()
    public String changeState(
            HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        if(EventProperties.DOSSIER_GET_BILL_EVENT_AVAILABLE){
            EventProperties.DOSSIER_GET_BILL_EVENT_AVAILABLE = false;
        } else {
            EventProperties.DOSSIER_GET_BILL_EVENT_AVAILABLE = true;
        }
        
        return "Done!!!";
    }
    
    @PostMapping(value = "/call-run-event", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public AffectedRowsDto callRunEvent(@RequestBody List<IntegratedGetBillEvent> listEvent) throws ParseException {
        return integratedGetBillEventService.callRunEvent(listEvent);
    }
    
    @PostMapping(value = "/call-run-event-all", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public AffectedRowsDto callRunEventAll() throws ParseException {
        return integratedGetBillEventService.callRunEventAll();
    }
}
