/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.GettingConfigurationParamsDto;
import vn.vnpt.digo.adapter.dto.UbmttqAddReqDto;
import vn.vnpt.digo.adapter.dto.UbmttqResultReqDto;
import vn.vnpt.digo.adapter.service.UbmttqService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ubmttq")
@IcodeAuthorize("vnpt.permission.ubmttq")
public class UbmttqController {
    
    @Autowired
    private UbmttqService ubmttqService;
    
    Logger logger = LoggerFactory.getLogger(UbmttqController.class);
    
    @PostMapping("/--add-ticket")
    public AffectedRowsDto add(HttpServletRequest request, @Valid @RequestBody UbmttqAddReqDto body) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        
        AffectedRowsDto res = ubmttqService.addTicket(body);
        logger.info("DIGO-Info: " + res.toString());
        return res;
    }
    
    @PostMapping("/--result")
    public AffectedRowsDto result(HttpServletRequest request, @Valid @RequestParam GettingConfigurationParamsDto config, @RequestBody UbmttqResultReqDto body ) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        
        AffectedRowsDto res = ubmttqService.updateResult(config, body);
        logger.info("DIGO-Info: " + res.toString());
        return res;
    }
}
