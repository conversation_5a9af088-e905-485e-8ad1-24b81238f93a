/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import org.springframework.data.domain.Pageable;
import javax.servlet.http.HttpServletRequest;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Slice;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.document.IntegrationService;
import vn.vnpt.digo.adapter.dto.ListIntegrationServiceDto;
import vn.vnpt.digo.adapter.service.IntegrationServiceService;
import vn.vnpt.digo.adapter.dto.IntegrationServiceDto;
import vn.vnpt.digo.adapter.dto.IntegrationServiceSearchDto;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.PostServiceDto;
import vn.vnpt.digo.adapter.dto.UpdateIServiceDto;
/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/integration-service")
@IcodeAuthorize("vnpt.permission.integrationservice")
public class IntegrationServiceController {

    @Autowired
    private IntegrationServiceService integrationService;

    Logger logger = LoggerFactory.getLogger(IntegrationServiceController.class);

    @GetMapping()
    public Slice<ListIntegrationServiceDto> getListService(
            HttpServletRequest request, Pageable pageable,
            @RequestParam(name = "keyword", required = false) String keyword
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        Slice<ListIntegrationServiceDto> listService = integrationService.getListService(pageable, keyword);
        logger.info("DIGO-Response: size = " + listService.getNumberOfElements() + "");
        return listService;
    }

    @GetMapping("/{id}")
    public IntegrationServiceDto getListService(HttpServletRequest request, @PathVariable(name = "id") ObjectId id) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        IntegrationServiceDto integrationServiceDto = integrationService.getById(id);
        logger.info("DIGO-Response: size = " + integrationServiceDto.toString());
        return integrationServiceDto;
    }

    // Created: Task DIGO-3902
    @GetMapping("/--search")
    public ResponseEntity<Object> search(
            HttpServletRequest request,
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "phrase", defaultValue = "1", required = false) Integer phrase,
            @RequestParam(name = "status", required = false) Integer status,
            @RequestParam(name = "write-log", required = false) Boolean writeLog,
            Pageable pageable
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        Page<IntegrationServiceSearchDto> page = integrationService.search(keyword, phrase, status, writeLog, pageable);
        logger.info("DIGO-Response: " + page.getContent().size());

        return ResponseEntity.ok(page);
    }
    
    @PutMapping("/{id}/status")
    public AffectedRowsDto updateStatus(HttpServletRequest request, @PathVariable(name = "id") ObjectId id) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        AffectedRowsDto res = integrationService.updateStatus(id);
        logger.info("DIGO-Response: size = " + res.toString());
        return res;
    }

    @PostMapping
    public IntegrationService add(HttpServletRequest request,
                               @RequestBody PostServiceDto input) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        IntegrationService res = integrationService.add(input);
        logger.info("DIGO-Response: size = " + res.toString());
        return res;
    }
    
    @PutMapping("/{id}")
    public AffectedRowsDto update(HttpServletRequest request, @PathVariable(name = "id") ObjectId id,
            @RequestBody @Validated UpdateIServiceDto input) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        AffectedRowsDto res = integrationService.update(id, input);
        logger.info("DIGO-Response: size = " + res.toString());
        return res;
    }

    @PutMapping("/{id}/--write-log")
    public AffectedRowsDto updateStatusWriteLog(HttpServletRequest request, @PathVariable(name = "id") ObjectId id)
     {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        AffectedRowsDto res = integrationService.updateStatusWriteLog(id);
        logger.info("DIGO-Response: size = " + res.toString());
        return res;
    }

}
