package vn.vnpt.digo.adapter.api;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.ag_esb.criminal_record_status.CriminalRecordDossierAGESBDto;
import vn.vnpt.digo.adapter.dto.ag_esb.criminal_record_status.CriminalRecordDossierResDto;
import vn.vnpt.digo.adapter.dto.tsdc.InfoDataNguyenVongDto;
import vn.vnpt.digo.adapter.dto.tsdc.InfoDataStudentDto;
import vn.vnpt.digo.adapter.dto.tsdc.InfoStudentResDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.service.AGGLienThongTSDCService;
import vn.vnpt.digo.adapter.service.AGGLltpAgesbService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.xml.bind.JAXBException;
import java.util.ArrayList;

@RestController
@RequestMapping("/tsdc")
@IcodeAuthorize("digo.permission.tsdc")
public class AGGLienThongTSDCController {
    @Autowired
    private AGGLienThongTSDCService aggLienThongTSDCService;

    @GetMapping("/--get-info-student")
    public InfoStudentResDto getInfoStudent(
            @Valid @RequestParam String cccd,
            @Valid @RequestParam String configId
    ) {
        InfoStudentResDto info = new InfoStudentResDto();
        try {
            info = aggLienThongTSDCService.getInfoStudent(cccd, new ObjectId(configId));
        } catch (Exception exception) {
            info.setSuccess(false);
            info.setMsg(exception.getMessage());
        }
        return info;
    }
}
