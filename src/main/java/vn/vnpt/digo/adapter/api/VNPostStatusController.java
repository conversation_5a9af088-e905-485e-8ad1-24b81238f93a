
package vn.vnpt.digo.adapter.api;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.bson.types.ObjectId;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.VNPostParamsDto;
import vn.vnpt.digo.adapter.dto.VNPostResultDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import vn.vnpt.digo.adapter.document.VNPostStatus;
import vn.vnpt.digo.adapter.service.VNPostService;
import vn.vnpt.digo.adapter.service.VNPostStatusService;

@RestController
@RequestMapping("/vnpost-status")
@IcodeAuthorize("vnpt.permission.vnpost-status")
public class VNPostStatusController {
    @Autowired
    private VNPostStatusService VNPostStatusService;
    Logger logger = LoggerFactory.getLogger(VNPostStatusController.class);
    
    @PostMapping("")
    public VNPostResultDto postVNPostStatus(HttpServletRequest request, @Valid @RequestBody List<VNPostStatus> body) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        VNPostResultDto res = VNPostStatusService.postVNPostStatus(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    @GetMapping("")
    public List<VNPostStatus> search(HttpServletRequest request,
                                                  @RequestParam(value = "customerCode", required = false) String customerCode,
                                                  @RequestParam(value = "orderNumber", required = false) String orderNumber,
                                                  @RequestParam(value = "deploymentId", required = false) String deploymentId,
                                                  Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        List<VNPostStatus> result = VNPostStatusService.search(customerCode, orderNumber, deploymentId);
        return result;
    }

    @GetMapping("/lastId")
    public VNPostStatus getLastId(HttpServletRequest request,
                                     @RequestParam(value = "customerCode", required = false) String customerCode,
                                     @RequestParam(value = "orderNumber", required = false) String orderNumber,
                                     @RequestParam(value = "deploymentId", required = false) String deploymentId,
                                     Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        return VNPostStatusService.getLastVnPostStatus(customerCode, orderNumber, deploymentId);
    }
}
