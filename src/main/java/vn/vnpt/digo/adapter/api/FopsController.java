/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.PostFopsResponseDto;
import vn.vnpt.digo.adapter.service.FopsService;

import java.text.ParseException;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fops")
@IcodeAuthorize("vnpt.permission.fops")
public class FopsController {

    @Autowired
    private FopsService fopsService;

    Logger logger = LoggerFactory.getLogger(FopsController.class);

    @PostMapping("/{configId}/nhanhsdvcqg")
    public PostFopsResponseDto nhanHsDvcqg(HttpServletRequest request,
            @PathVariable(name = "configId", required = true) ObjectId configId,
            @Valid @RequestBody Object postBody) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return fopsService.nhanHsDvcqg(null, configId, postBody);
    }

    @PostMapping("/--nhan-chung-tu-thue-dat")
    public PostFopsResponseDto nhanHsDvcqg(HttpServletRequest request,
                                           @RequestParam(value = "config-code", required = true) String configCode,
                                           @Valid @RequestBody Object postBody) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return fopsService.nhanHsDvcqg(configCode, null, postBody);
    }

    @PostMapping("/{configId}/nhantbthuedvcqg")
    public PostFopsResponseDto nhantbthuedvcqg(HttpServletRequest request,
            @PathVariable(name = "configId", required = true) ObjectId configId,
            @Valid @RequestBody Object postBody) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return fopsService.nhantbthuedvcqg(null, configId, postBody);
    }

    @PostMapping("/--nhan-thong-bao-thue-dat")
    public PostFopsResponseDto nhantbthuedvcqg(HttpServletRequest request,
                                               @RequestParam(value = "config-code", required = true) String configCode,
                                               @Valid @RequestBody Object postBody) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return fopsService.nhantbthuedvcqg(configCode, null, postBody);
    }

    @GetMapping("/get-enterprise-cert")
    public ResponseEntity<Object> getCert(HttpServletRequest request) {
        fopsService.getCert();
        return ResponseEntity.ok("");
    }

    @GetMapping("/--get-enterprise-tax-logs-by-time")
    public Slice getKTMMonreIntegrationLogsByTime(HttpServletRequest request,
                                                  @RequestParam(value = "from-date") String fromDate,
                                                  @RequestParam(value = "to-date") String toDate,
                                                  Pageable pageable) throws ParseException {
        //   monreIntegrationService.getKTMMonreIntegrationLogsByTime(fromDate,toDate, pageable);
        return fopsService.getEnterpriseTaxLogsByTime(fromDate,toDate, pageable);
    }
}
