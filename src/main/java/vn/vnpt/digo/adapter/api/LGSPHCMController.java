package vn.vnpt.digo.adapter.api;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.DossierResponse;
import vn.vnpt.digo.adapter.dto.DossierRequestDto;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.SyncDossierPMCRequestDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCDossierReqDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCResultDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCDossierTrackingReqDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierSyncReqDto;
import vn.vnpt.digo.adapter.pojo.ErrorMessage;
import vn.vnpt.digo.adapter.service.LGSPHCMService;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVC_V2TrangthaiHoSoReqDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVC_V2DongBoHoSoReqDto;
import vn.vnpt.digo.adapter.service.HCMLGSP_DossierSyncV2Service;
import vn.vnpt.digo.adapter.dto.GettingConfigurationParamsDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCDossierPaymentReqDto;
import vn.vnpt.digo.adapter.properties.EventProperties;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lgsp-hcm")
@IcodeAuthorize("vnpt.permission.lgsp-hcm")
public class LGSPHCMController {

    @Autowired
    private LGSPHCMService lgspHCMService;

    @Autowired
    private HCMLGSP_DossierSyncV2Service lgspHCM_DossierSyncV2Service;

    Logger logger = LoggerFactory.getLogger(LGSPHCMController.class);

    @GetMapping("/--get-agency-code")
    public ArrayList<String> getConfigId(HttpServletRequest request) {
        return lgspHCMService.getListAgencyCode();
    }

    @GetMapping("/--sync-dossier-from-lgsphcm")
    public String syncDossier(HttpServletRequest request) {
        return lgspHCMService.syncDossierFromLGSPHCM();
    }

    @PostMapping("/--send-dossier-to-lgsphcm")
    public DossierResponse sendDossier(HttpServletRequest request, @RequestBody @Validated DossierRequestDto req) {
        DossierResponse res = lgspHCMService.sendDossier(req);
        return res;
    }

    @GetMapping("/--sync-agency-from-lgsphcm")
    public AffectedRowsDto syncAgencyFromLGSPHCM(HttpServletRequest request) {
        return lgspHCMService.syncAgencyFromLGSPHCM();
    }

    @PostMapping(value = "/DongBoHoSoMC", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> dongBoHoSoMC(HttpServletRequest request,
            @RequestBody @Validated DVCDossierReqDto req) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String username = Context.getJwtParameterValue("preferred_username");
        if (username != null) {
            logger.info("DIGO-Authorization: username " + username);
        } else {
            String clientId = Context.getJwtParameterValue("azp");
            logger.info("DIGO-Authorization: clientId " + clientId);
        }
        DVCResultDto res = lgspHCMService.dongBoHoSoMC(req);

        logger.info("DIGO-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    @PostMapping(value = "/CapNhatTienDoHoSoMC", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> dongBoHoSoMC(HttpServletRequest request,
            @RequestBody @Validated DVCDossierTrackingReqDto req) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String username = Context.getJwtParameterValue("preferred_username");
        if (username != null) {
            logger.info("DIGO-Authorization: username " + username);
        } else {
            String clientId = Context.getJwtParameterValue("azp");
            logger.info("DIGO-Authorization: clientId " + clientId);
        }
        DVCResultDto res = lgspHCMService.capNhatTienDoHoSoMC(req);

        logger.info("DIGO-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    @PostMapping(value = "/YeuCauThanhToanHoSoMC", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> yeuCauThanhToanHoSoMC(HttpServletRequest request,
            @RequestBody @Validated DVCDossierPaymentReqDto req) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String username = Context.getJwtParameterValue("preferred_username");
        if (username != null) {
            logger.info("DIGO-Authorization: username " + username);
        } else {
            String clientId = Context.getJwtParameterValue("azp");
            logger.info("DIGO-Authorization: clientId " + clientId);
        }
        DVCResultDto res = lgspHCMService.yeuCauThanhToanHoSoMC(req);

        logger.info("DIGO-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }



    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({MethodArgumentNotValidException.class, Exception.class})
    public ErrorMessage handleValidationExceptions(MethodArgumentNotValidException ex, HttpServletRequest request) {
        List<String> errors = new ArrayList<String>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            errors.add(error.getDefaultMessage());
        }
        for (ObjectError error : ex.getBindingResult().getGlobalErrors()) {
            errors.add(error.getDefaultMessage());
        }
        ErrorMessage errorMessage = new ErrorMessage("-1", String.join(" ", errors));
        return errorMessage;
    }

    //paquoc74 ThursDay 29/09/2022
    @PostMapping(value = "/UpdateTrangThaiHoSoMCV2", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> dongBoHoSoMCV2(HttpServletRequest my_request,
            @RequestBody @Validated DVC_V2TrangthaiHoSoReqDto obj_req,
            @Valid @RequestParam GettingConfigurationParamsDto configParams) {
String requestPath = my_request.getMethod() + " " + my_request.getRequestURI() + (my_request.getQueryString() != null ? "?" + my_request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String username = Context.getJwtParameterValue("preferred_username");
        if (username != null) {
            logger.info("DIGO-Authorization: username " + username);
        } else {
            String clientId = Context.getJwtParameterValue("azp");
            logger.info("DIGO-Authorization: clientId " + clientId);
        }
        DVCResultDto ketQua = lgspHCM_DossierSyncV2Service.update_TrangThaiHoSoV2(obj_req, configParams);
        logger.info("DIGO-Response: " +ketQua.toString() );
        return ResponseEntity.ok(ketQua);
    }

    //paquoc74 Friday 07/10/2022
    @PostMapping(value = "/DongBoHoSoMCV2", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> dongBoHoSoMCV2(HttpServletRequest my_request,
            @RequestBody @Validated DVC_V2DongBoHoSoReqDto obj_req,
            @Valid @RequestParam GettingConfigurationParamsDto configParams) {
        String requestPath = my_request.getMethod() + " " + my_request.getRequestURI() + (my_request.getQueryString() != null ? "?" + my_request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String username = Context.getJwtParameterValue("preferred_username");
        if (username != null) {
            logger.info("DIGO-Authorization: username " + username);
        } else {
            String clientId = Context.getJwtParameterValue("azp");
            logger.info("DIGO-Authorization: clientId " + clientId);
        }
        DVCResultDto ketQua = lgspHCM_DossierSyncV2Service.syncHoSoV2(obj_req, configParams);

        logger.info("DIGO-Response: " + ketQua.toString());
        return ResponseEntity.ok(ketQua);
    }

    @GetMapping
    public String changeState(
            @RequestParam(name = "status") Boolean status,
            HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        if(EventProperties.DOSSIER_EVENT_AVAILABLE){
            EventProperties.DOSSIER_EVENT_AVAILABLE = false;
        } else {
            EventProperties.DOSSIER_EVENT_AVAILABLE = true;
        }
        if (status != null) {
            EventProperties.DOSSIER_EVENT_AVAILABLE = status;
        }

        return "Done!!!";
    }

    @PostMapping(value = "/PMC/DongBoHoSoMC", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> dongBoHoSoPMC(HttpServletRequest request,
                                               @RequestBody @Validated SyncDossierPMCRequestDto req) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String username = Context.getJwtParameterValue("preferred_username");
        if (username != null) {
            logger.info("DIGO-Authorization: username " + username);
        } else {
            String clientId = Context.getJwtParameterValue("azp");
            logger.info("DIGO-Authorization: clientId " + clientId);
        }
        DVCResultDto res = lgspHCMService.dongBoHoSoPMC(req);

        logger.info("DIGO-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

}
