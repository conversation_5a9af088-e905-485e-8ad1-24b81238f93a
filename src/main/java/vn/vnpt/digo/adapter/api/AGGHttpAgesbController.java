package vn.vnpt.digo.adapter.api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.ag_esb.civilstatus.CivilDossierAGESBDto;
import vn.vnpt.digo.adapter.dto.ag_esb.civilstatus.CivilDossierResDto;
import vn.vnpt.digo.adapter.dto.event_log.PostEventLogDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.IdResponse;
import vn.vnpt.digo.adapter.service.AGGHttpAgesbService;
import vn.vnpt.digo.adapter.service.EventLogService;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
@RestController
@RequestMapping("/ag-esb-http")
@IcodeAuthorize("digo.permission.ag-esb-http")
public class AGGHttpAgesbController {
    @Autowired
    private AGGHttpAgesbService agEsbService;
    @Autowired
    private EventLogService eventLogService;
    Logger logger = LoggerFactory.getLogger(IportalCategoryController.class);

    @PostMapping("/--register-civil")
    public CivilDossierResDto registerCivil(
            HttpServletRequest request,
            @Valid @RequestBody CivilDossierAGESBDto civilDossierDto
    ) {
        try {
            return agEsbService.writeLogHTTP(request, civilDossierDto, civilDossierDto.getMaHoSo());
        } catch (DigoHttpException digoEx) {
            return handleHttpException(digoEx);
        } catch (Exception exception) {
            return handleGenericException(exception);
        }
    }

    private CivilDossierResDto handleHttpException(DigoHttpException digoEx) {
        CivilDossierResDto ret = new CivilDossierResDto();
        ret.setStatus(-1);
        ret.setErrorDescription(digoEx.getArguments()[0]);
        return ret;
    }

    private CivilDossierResDto handleGenericException(Exception exception) {
        CivilDossierResDto ret = new CivilDossierResDto();
        ret.setStatus(-1);
        ret.setErrorDescription(exception.getMessage());
        return ret;
    }
    @PostMapping("--write-log")
    public IdResponse addNewAGG(HttpServletRequest request,
                                HttpServletResponse response,
                                @RequestBody PostEventLogDto input) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        PostEventLogDto event = new PostEventLogDto();
        try {
            IdResponse res = eventLogService.addNewAGG(input);
            logger.info("DIGO-Response: eventLog added with id:  " + res.getId().toString());
            return res;
        }catch (Exception e){
            event.setStatus(true);
            event.setRequest(request, input);
            event.setRes(response);
            event.setErrMsg(e.getMessage());
            event.setServiceId(input.getServiceId());
            eventLogService.addNewAGG(event);
            logger.info("DIGO-Response: eventLog added err:  " + e.getMessage());
            return new IdResponse(null);
        }
    }

}
