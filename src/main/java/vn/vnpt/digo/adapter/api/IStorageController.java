package vn.vnpt.digo.adapter.api;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.istorage.*;
import vn.vnpt.digo.adapter.dto.lgsphcm.AffectedRowsDto;
import vn.vnpt.digo.adapter.service.IStorageService;
import vn.vnpt.digo.adapter.service.UploadDossierIStorageService;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/istorage")
@IcodeAuthorize("digo.permission.integrated")
public class IStorageController {
    @Autowired
    private IStorageService iStorageService;
    @Autowired
    private UploadDossierIStorageService uploadDossierIStorageService;

    Logger logger = LoggerFactory.getLogger(IStorageController.class);

    @PostMapping("/saveHoSo/")
    public IStorageSaveHoSoResponseDto saveHoSo(HttpServletRequest request,
                                        @RequestBody SaveHoSoIStorageRequestDto saveHoSoIStorageRequestDto) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageSaveHoSoResponseDto iStorageDatasResponseDto = iStorageService.saveHoSo(saveHoSoIStorageRequestDto);
        logger.info("DIGO-Response: " + iStorageDatasResponseDto);
        return iStorageDatasResponseDto;
    }

    @PostMapping("/saveHoSoThanhPhan/")
    public IStorageSaveHoSoThanhPhanResponseDto saveHoSo(HttpServletRequest request,
                                                @RequestBody IStorageSaveHoSoThanhPhanRequestDto iStorageSaveHoSoThanhPhanRequestDto) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageSaveHoSoThanhPhanResponseDto iStorageSaveHoSoThanhPhanResponseDto = iStorageService.saveThanhPhanHoSo(iStorageSaveHoSoThanhPhanRequestDto);
        logger.info("DIGO-Response: " + iStorageSaveHoSoThanhPhanResponseDto);
        return iStorageSaveHoSoThanhPhanResponseDto;
    }

    @GetMapping("/getListUnit/")
    public IStorageDatasResponseDto getListDonVi(HttpServletRequest request,
                                                @RequestParam(name = "ma-don-vi", required = false) String maDonVi) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageDatasResponseDto iStorageDatasResponseDto = iStorageService.getListDonVi(maDonVi);
        logger.info("DIGO-Response: " + iStorageDatasResponseDto);
        return iStorageDatasResponseDto;
    }

    @GetMapping("/getListThbq/")
    public IStorageDatasResponseDto getListThoiHanBaoQuan(HttpServletRequest request,
                                                @RequestParam(name = "ten-thoi-han", required = false) String tenThoiHan,
                                                @RequestParam(name = "pageNo", required = false) String pageNo,
                                                @RequestParam(name = "pageRec", required = false) String pageRec){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageDatasResponseDto iStorageDatasResponseDto = iStorageService.getListThoiHanBaoQuan(tenThoiHan,pageNo,pageRec);
        logger.info("DIGO-Response: " + iStorageDatasResponseDto);
        return iStorageDatasResponseDto;
    }

    @PostMapping("/getListDm/")
    public IStorageDatasResponseDto getListDeMuc(HttpServletRequest request,
                                        @RequestParam(name = "id-don-vi", required = true) String idDonVi,
                                        @RequestParam(name = "nam-de-muc", required = false) String namDemuc) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageDatasResponseDto iStorageDatasResponseDto = iStorageService.getListDeMuc(idDonVi, namDemuc);
        logger.info("DIGO-Response: " + iStorageDatasResponseDto);
        return iStorageDatasResponseDto;
    }

    @GetMapping("/getListTtvl/")
    public IStorageDatasResponseDto getListTinhTrangVatLy(HttpServletRequest request,
                                                          @RequestParam(name = "ten-tinh-trang-vat-ly", required = false) String tenTinhTrangVatLy,
                                                          @RequestParam(name = "pageNo", required = false) String pageNo,
                                                          @RequestParam(name = "pageRec", required = false) String pageRec){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageDatasResponseDto iStorageDatasResponseDto = iStorageService.getListTinhTrangVatLy(tenTinhTrangVatLy,pageNo,pageRec);
        logger.info("DIGO-Response: " + iStorageDatasResponseDto);
        return iStorageDatasResponseDto;
    }

    @GetMapping("/getTrangThaiLTLS/")
    public IStorageDatasResponseDto getTrangThaiHoSoTheoId(HttpServletRequest request,
                                                          @RequestParam(name = "id-ho-so", required = true) String idHoSo){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageDatasResponseDto iStorageDatasResponseDto = iStorageService.getTrangThaiHoSoTheoId(idHoSo);
        logger.info("DIGO-Response: " + iStorageDatasResponseDto);
        return iStorageDatasResponseDto;
    }
    @GetMapping("/getListTrangThaiHoSo/")
    public IStorageDatasResponseDto getListTrangThaiHoSo(HttpServletRequest request){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageDatasResponseDto iStorageDatasResponseDto = iStorageService.getListTrangThaiHoSo();
        logger.info("DIGO-Response: " + iStorageDatasResponseDto);
        return iStorageDatasResponseDto;
    }

    @GetMapping("/getListLoaiVanBan/")
    public IStorageDatasResponseDto getListLoaiVanBan(HttpServletRequest request,
                                                      @RequestParam(name = "loai-van-ban", required = false) String loaiVanBan,
                                                      @RequestParam(name = "ten-loai-van-ban", required = false) String tenLoaiVanBan,
                                                      @RequestParam(name = "pageNo", required = false) String pageNo,
                                                      @RequestParam(name = "pageRec", required = false) String pageRec){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageDatasResponseDto iStorageDatasResponseDto = iStorageService.getListLoaiVanBan(loaiVanBan, tenLoaiVanBan, pageNo, pageRec);
        logger.info("DIGO-Response: " + iStorageDatasResponseDto);
        return iStorageDatasResponseDto;
    }
    @GetMapping("/getListMucDoTinCay/")
    public IStorageDatasResponseDto getListMucDoTinCay(HttpServletRequest request,
                                                          @RequestParam(name = "ten-muc-do", required = false) String tenMucDo,
                                                          @RequestParam(name = "pageNo", required = false) String pageNo,
                                                          @RequestParam(name = "pageRec", required = false) String pageRec){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageDatasResponseDto iStorageDatasResponseDto = iStorageService.getListMucDoTinCay(tenMucDo,pageNo,pageRec);
        logger.info("DIGO-Response: " + iStorageDatasResponseDto);
        return iStorageDatasResponseDto;
    }

    @GetMapping("/getListTinhChatVatLyTriLieu/")
    public IStorageDatasResponseDto getListTinhChatVatLyTriLieu(HttpServletRequest request,
                                                       @RequestParam(name = "ten-tinhchat-vatly", required = false) String tenTinhChatVatLy,
                                                       @RequestParam(name = "pageNo", required = false) String pageNo,
                                                       @RequestParam(name = "pageRec", required = false) String pageRec){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IStorageDatasResponseDto iStorageDatasResponseDto = iStorageService.getListTinhChatVatLyTriLieu(tenTinhChatVatLy,pageNo,pageRec);
        logger.info("DIGO-Response: " + iStorageDatasResponseDto);
        return iStorageDatasResponseDto;
    }

    @PutMapping("/updateStatuaSyncIStorage/{id}")
    public AffectedRowsDto getListTinhChatVatLyTriLieu(HttpServletRequest request
                        , @PathVariable(name = "id", required = true) ObjectId id){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedRowsDto affectedRowsDto = iStorageService.updateStatuaSyncIStorage(id.toString(),2);
        logger.info("DIGO-Response: " + affectedRowsDto);
        return affectedRowsDto;
    }

    @PostMapping("/uploadDossier/{id}")
    public vn.vnpt.digo.adapter.dto.AffectedRowsDto uploadDossierIStorage(@PathVariable(name = "id", required = true) String id,
                                                                          @RequestParam(name = "creatorDepartmentCode", required = true) String creatorDepartmentCode,
                                                                          @RequestParam(name = "identityNumber", required = false, defaultValue = "") String identityNumber) {
        vn.vnpt.digo.adapter.dto.AffectedRowsDto affectedRowsDto = uploadDossierIStorageService.uploadDossier(id,creatorDepartmentCode,identityNumber);
        return affectedRowsDto;
    }
}
