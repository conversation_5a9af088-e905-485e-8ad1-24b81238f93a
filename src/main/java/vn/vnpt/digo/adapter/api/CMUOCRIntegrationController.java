package vn.vnpt.digo.adapter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.cmu.CMUOCRRecognizeDTO;
import vn.vnpt.digo.adapter.service.CMUOCRIntegrationService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/cmu-ocr-intergration")
@IcodeAuthorize("vnpt.permission.integrated")
public class CMUOCRIntegrationController {
    Logger logger = LoggerFactory.getLogger(CMUOCRIntegrationController.class);

    @Autowired
    private CMUOCRIntegrationService cmuocrIntegrationService;

    public void writeLogByRequest(HttpServletRequest request){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
    }

    @GetMapping("/--recognize")
    public Map<String, Object> recognize(HttpServletRequest request, @Valid CMUOCRRecognizeDTO cmuocrRecognizeDTO) throws IOException {
        writeLogByRequest(request);
        return cmuocrIntegrationService.recognize(cmuocrRecognizeDTO);
    }
}
