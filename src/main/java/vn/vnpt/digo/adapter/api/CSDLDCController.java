package vn.vnpt.digo.adapter.api;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.csdldc.CitizenResponseDto;
import vn.vnpt.digo.adapter.dto.csdldc.ShareCitizenInfoRequestDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.service.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.Translator;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/citizen")
@IcodeAuthorize("vnpt.permission.citizen-info")
public class CSDLDCController {

    Logger logger = LoggerFactory.getLogger(CSDLDCController.class);

    @Autowired
    private CSDLDCService csdldcService;

    @Autowired
    private Translator translator;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private DailyLogRequestsService dailyLogRequestsService;

    @Autowired
    private UserRequestCountsByDateService userRequestCountsByDateService;

    @Autowired
    private AlertQuotaService alertQuotaService;

    @GetMapping("/--info")
    public CitizenResponseDto getCitizenInfo(HttpServletRequest request,
            @Valid @RequestParam ShareCitizenInfoRequestDto req)  throws IOException {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IntegratedConfigurationDto config;
        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        } else {
            config = configurationService.getConfig(req.getAgencyId(), req.getSubsystemId(), CSDLDCService.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        if(Objects.nonNull(config) && Objects.nonNull(config.getEncryptedFieldList())){
            config.setParameters(configurationService.decodeParameters(config.getParameters(), config.getEncryptedFieldList(), false));
        }
        String username = Context.getJwtParameterValue("preferred_username");
        IntegratedConfigurationDto configCSDLDC = configurationService.getConfig(null, new ObjectId("5f7c16069abb62f511880003"), new ObjectId("5f7c16069abb62f511890034"));
        Integer dailyRequestLimit = configCSDLDC.getParameterValue("dailyRequestLimit")!= null?configCSDLDC.getParametersValue("dailyRequestLimit"):9000;
        Integer userRequestLimit = configCSDLDC.getParametersValue("userRequestLimit")!= null?configCSDLDC.getParametersValue("userRequestLimit"):200;
        // cấu hình quota captcha
        Boolean quotaCaptchaEnable = configCSDLDC.getParametersValue("quotaCaptchaEnable")!= null && "true".equals(configCSDLDC.getParametersValue("quotaCaptchaEnable").toString())?true:false;
        int quotaTimeCallCaptcha = configCSDLDC.getParametersValue("quotaTimeCallCaptcha")!= null?configCSDLDC.getParametersValue("quotaTimeCallCaptcha"):30;
        int quotaLimitCaptcha = configCSDLDC.getParametersValue("quotaLimitCaptcha")!= null?configCSDLDC.getParametersValue("quotaLimitCaptcha"):1;
        int dossierRequestLimit = configCSDLDC.getParametersValue("dossierRequestLimit")!= null?configCSDLDC.getParametersValue("dossierRequestLimit"):50;

        vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission("admin");
        List<String> tokenPermissions = csdldcService.getTokenPermissions();
        // check nếu tài khoản được phân quyền (cán bộ), thì check xem có quyền tra cứu 037 không
        // trường hợp công dân tra ở trang dichvucong thì tokenPermissions.size() = 0
        if(tokenPermissions.size()>0){
            vn.vnpt.digo.adapter.pojo.Permission permission037 = Context.getPermission("oneGateResidentialInfo");
            if(permission037 == null){
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        }
        // có quyền admin thì bỏ qua check
        if (permission == null) {
            // check username
            String userId = Context.getJwtParameterValue("user_id");
            if(Objects.isNull(userId)){
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
            // Kiểm tra thời gian 2 lần gọi captcha
            if(quotaCaptchaEnable && !alertQuotaService.alertQuota(username, "getCitizenInfoCapcha", quotaTimeCallCaptcha, quotaLimitCaptcha)){
                csdldcService.createLogV2(req, config.getParametersValue("type"), username, true, true);
                throw new DigoHttpException(10444, HttpServletResponse.SC_BAD_REQUEST);
            }else if (!userRequestCountsByDateService.checkUserRequest(username,userRequestLimit,1)){
                // Hết lượt truy vấn theo ngày của cán bộ
                throw new DigoHttpException(10442, HttpServletResponse.SC_BAD_REQUEST);
            }else if(!dailyLogRequestsService.checkDailyLogRequests(dailyRequestLimit,1)){
                // Hết lượt truy vấn theo ngày của site
                throw new DigoHttpException(10443, HttpServletResponse.SC_BAD_REQUEST);
            }else if(req.isEnableLogCSDLDCBDH()){
                 if(!csdldcService.checkDossierRequest(req.getDossierId(), req.getDossierCode(), dossierRequestLimit)) {
                    throw new DigoHttpException(10444, HttpServletResponse.SC_BAD_REQUEST);
                }
            }
            // update lại giá trị cho các collection
            Boolean updateUserRequest = userRequestCountsByDateService.updateUserRequest(username,userRequestLimit,1);
            Boolean dailyLogRequests = dailyLogRequestsService.updateDailyLogRequests(dailyRequestLimit,1);
        }
        CitizenResponseDto res = csdldcService.getCitizenInfo(req,config,username);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @GetMapping("/--info-test")
    public Object getCitizenInfo(HttpServletRequest request,
                                             @Valid @RequestBody Object citizen,
                                             @RequestParam(name = "get-origin-info", required = false) boolean getOriginInfo,
                                             @RequestParam(name = "map-fill-data", required = false) boolean MAPPFillData,
                                             @RequestParam(name = "eform-id", required = false) ObjectId EFormId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        Object res = csdldcService.mapCitizenInfoTEST(citizen, EFormId, MAPPFillData, getOriginInfo);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @PostMapping("/--info")
    public CitizenResponseDto postCitizenInfo(HttpServletRequest request,
            @Valid @RequestBody ShareCitizenInfoRequestDto req)  throws IOException {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IntegratedConfigurationDto config;
        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        } else {
            config = configurationService.getConfig(req.getAgencyId(), req.getSubsystemId(), CSDLDCService.serviceId);
        }
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        if(Objects.nonNull(config) && Objects.nonNull(config.getEncryptedFieldList())){
            config.setParameters(configurationService.decodeParameters(config.getParameters(), config.getEncryptedFieldList(), false));
        }
        String username = Context.getJwtParameterValue("preferred_username");
        CitizenResponseDto res = csdldcService.getCitizenInfo(req,config,username);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
}
