package vn.vnpt.digo.adapter.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.service.ScanIndexService;
import java.io.IOException;

@RestController
@RequestMapping("/scan-index")
@IcodeAuthorize("vnpt.permission.integratedconfiguration")
public class ScanIndexController {

    @Autowired
    private ScanIndexService scanIndexService;

    @PostMapping
    public ResponseEntity<Object> ScanIndex(@RequestParam(value = "prefix-db", required = true) String prefixDB,
                                            @RequestParam(required = false) MultipartFile file) throws IOException {
        return ResponseEntity.ok(scanIndexService.ScanIndex(prefixDB,file));
    }

    @GetMapping("/--export-indexs")
    public ResponseEntity<Object> getAllIndexs(){
        Resource resource = scanIndexService.getAllIndexs();
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + "indexs.xlsx" + "\"")
                .body(resource);
    }
}
