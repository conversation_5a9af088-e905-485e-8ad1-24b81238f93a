/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCDossierTrackingReqDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.DVCResultDto;
import vn.vnpt.digo.adapter.dto.vbdlis.*;
import vn.vnpt.digo.adapter.dto.vbdlis.VbdlisResDto.*;
import vn.vnpt.digo.adapter.service.VBDLISService;
import vn.vnpt.digo.adapter.util.Context;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.text.ParseException;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/vbdlis")
@IcodeAuthorize("vnpt.permission.vbdlis")
public class VbdlisController {
    
    @Autowired
    private VBDLISService vbdlisService;

    Logger logger = LoggerFactory.getLogger(VbdlisController.class);
    @Value("${digo.vbdlisKetThuc.used.v2}")
    private Boolean vbdlisKetThucUserV2;

    @Value("${digo.vbdlisKetQuaThue.used.v2}")
    private Boolean vbdlisKetThucThueUserV2;
    
    @PostMapping("/--receiving-dossier")
    public ReceivingDossierResponse receivingDossier(HttpServletRequest request, 
            @Valid @RequestBody ReceivingDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        
        var res = vbdlisService.receivingDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @PostMapping("/--result-update-dossier")
    public ResultUpdateDossierResponse resultUpdateDossier(HttpServletRequest request, 
            @Valid @RequestBody ResultUpdateDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.resultUpdateDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    @PostMapping("/--additional-status-update")
    public AdditionalStatusUpdateDossierResponse additionalStatusUpdateDossier(HttpServletRequest request, 
            @Valid @RequestBody AdditionalStatusUpdateDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.aditionalStatusUpdateDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    @PostMapping("/--feedback-profile-result-dossier")
    public FeedbackProfileResultDossierResponse feedbackProfileResultDossier(HttpServletRequest request, 
            @Valid @RequestBody FeedbackProfileResultDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.feedbackProfileResultDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    @PostMapping("/--send-processing-notice-dossier")
    public SendProcessingNoticeDossierResponse feedbackProfileResultDossier(HttpServletRequest request, 
            @Valid @RequestBody SendProcessingNoticeDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.sendProcessingNoticeDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    @GetMapping("/GetDanhMucThuTucHanhChinh")
    public DanhMucThuTucResponse getDanhMucThuTucHanhChinh(HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.getDanhMucThuTucHanhChinh();
        logger.info("DIGO-Response: " + res.toString());
        
        return res;
    }

    @GetMapping("/GetDanhMucThuTucHanhChinhQNI")
    public DanhMucThuTucResponse getDanhMucThuTucHanhChinhQNI(HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.getDanhMucThuTucHanhChinhQNI();
        logger.info("DIGO-Response: " + res.toString());

        return res;
    }

    @GetMapping("/GetDanhMucNguoiDung")
    public DanhMucNguoiDungResponse getDanhMucNguoiDung(HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.getDanhMucNguoiDung();
        logger.info("DIGO-Response: " + res.toString());
        
        return res;
    }
    
    @GetMapping("/GetDanhMucTrangThai")
    public DanhMucTrangThaiResponse GetDanhMucTrangThai(HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.getDanhMucTrangThai();
        logger.info("DIGO-Response: " + res.toString());
        
        return res;
    }
    
    @PostMapping("/KetThucHoSo")
    public YeuCauBoSungResponse finishDossier(HttpServletRequest request, 
            @Valid @RequestBody DongBoTrangThaiKetThucDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        if(vbdlisKetThucUserV2 == true){
            var res = vbdlisService.postDongBoTrangThaiKetThucv2(body);
            logger.info("DIGO-Response: " + res.toString());
            return res;
        }
        else{
            
            var res = vbdlisService.postDongBoTrangThaiKetThuc(body);
            logger.info("DIGO-Response: " + res.toString());
            return res;
        }
        
    }

    @PostMapping("/KetThucHoSo2")
    public YeuCauBoSungResponse finishDossierv2(HttpServletRequest request,
                                              @Valid @RequestBody DongBoTrangThaiKetThucDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.postDongBoTrangThaiKetThucv2(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }


    @PostMapping("/--update-additional-request")
    public AdditionalStatusUpdateDossierResponse updateAdditionalRequestDossier(HttpServletRequest request,
                                                                                @Valid @RequestBody AdditionalStatusUpdateDossierDto body
                                                                                ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.updateAdditionalRequestDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @PostMapping("/YeuCauBoSungHoSo")
    public YeuCauBoSungResponse postYeuCauBoSungHoSo(HttpServletRequest request,
                                                     @Valid @RequestBody YeuCauBoSungHoSoDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.postYeucauBoSung(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @PostMapping("/GuiKetQuaThue")
    public VbdlisResDto.GuiKetQuaThueResponse finishDossier(HttpServletRequest request,
                                                            @Valid @RequestBody PutTaxResultVbdlisDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        if(vbdlisKetThucThueUserV2 == true){
            var res = vbdlisService.postTaxResultv2(body);
            logger.info("DIGO-Response: vbdlisService.postTaxResultv2 " + res.toString());
            return res;
        }else{
            var res = vbdlisService.postTaxResult(body);
            logger.info("DIGO-Response: " + res.toString());
            return res;
        }

    }

    @PostMapping("/GuiKetQuaThue2")
    public VbdlisResDto.GuiKetQuaThueResponse finishDossierv2(HttpServletRequest request,
                                                            @Valid @RequestBody PutTaxResultVbdlisDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.postTaxResultv2(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @PutMapping("/--update-result-finish-dossier")
    public ResultUpdateDossierResponse updateResultFinishDossier(HttpServletRequest request,
                                                     @Valid @RequestBody UpdateFinishDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.updateResultFinishDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    // 5.11
    @PostMapping("/YeuCauCapNhatHoSo")
    public VbdlisResDto.YeuCauBoSungResponse postYeuCauCapNhatHoSo(HttpServletRequest request,
                                              @Valid @RequestBody YeuCauCapNhatHoSoDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.postYeuCauCapNhatHoSo(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }


    // API 5.5
    @PostMapping(value = "/ChuyenHoSo", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public VbdlisResDto.ChuyenHoSoResponse dongBoHoSoMC(HttpServletRequest request,
                                               @RequestBody @Validated ChuyenHoSoDto req) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String username = Context.getJwtParameterValue("preferred_username");
        if (username != null) {
            logger.info("DIGO-Authorization: username " + username);
        } else {
            String clientId = Context.getJwtParameterValue("azp");
            logger.info("DIGO-Authorization: clientId " + clientId);
        }
        ChuyenHoSoResponse res = vbdlisService.capNhatTienDoHoSoMC(req);

        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @PostMapping(value = "/ChuyenHoSo2", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public VbdlisResDto.ChuyenHoSoResponse dongBoHoSoMCv2(HttpServletRequest request,
                                                        @RequestBody @Validated ChuyenHoSoDto req) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String username = Context.getJwtParameterValue("preferred_username");
        if (username != null) {
            logger.info("DIGO-Authorization: username " + username);
        } else {
            String clientId = Context.getJwtParameterValue("azp");
            logger.info("DIGO-Authorization: clientId " + clientId);
        }
        ChuyenHoSoResponse res = vbdlisService.capNhatTienDoHoSoMCv2(req);

        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    //API 5.7
    @PostMapping("/NhanBoSungHoSo")
    public VbdlisResDto.NhanBoSungHoSoResponse postNhanBoSungHoSo(HttpServletRequest request,
                                                      @Valid @RequestBody NhanBoSungHoSoDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = vbdlisService.postNhanBoSungHoSo(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
}
