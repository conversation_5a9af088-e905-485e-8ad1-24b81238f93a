package vn.vnpt.digo.adapter.api;

import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.hibernate.validator.internal.engine.ConstraintViolationImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.DossierRequestDto;
import vn.vnpt.digo.adapter.dto.DossierResponse;
import vn.vnpt.digo.adapter.dto.GettingConfigurationParamsDto;
import vn.vnpt.digo.adapter.dto.dvclt.DVCLTHoTichPostDto;
import vn.vnpt.digo.adapter.dto.dvclt.DVCLTHoTichRequestDto;
import vn.vnpt.digo.adapter.dto.dvclt.DVCLTHoTichResponseDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.*;
import vn.vnpt.digo.adapter.pojo.ErrorMessage;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.service.DVCLTService;
import vn.vnpt.digo.adapter.service.HCMLGSP_DossierSyncV2Service;
import vn.vnpt.digo.adapter.service.LGSPHCMService;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.StringHelper;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Valid;
import javax.validation.Validator;
import javax.validation.constraints.NotEmpty;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/lienthongDVCLT")
@IcodeAuthorize("vnpt.permission.dvc-lt")
@Validated
public class DVCLTController {

    @Autowired
    private DVCLTService dvcltService;

    @Autowired
    private Translator translator;

    Logger logger = LoggerFactory.getLogger(DVCLTController.class);

    @Value("${digo.dvclt.security-key}")
    private String securityKeyIgate;


    @PostMapping(value = "/nhanHoSoDVCLT", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> nhanHoSoDVCLT(
            HttpServletRequest request,
            @RequestHeader(value = "securityKey", required = true) String securityKey,
            @RequestBody List<@Valid DVCLTDossierDataDto> req) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String key = StringHelper.toHmacSha256(securityKeyIgate, req.get(0).getNationCode());
        if(!key.equals(securityKey)){
          Object ob =  JSON.parse("{'401':'Unauthorized'}");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ob);
            //return ResponseEntity.badRequest().body(ob);
        }
        DVCLTResultDto res = dvcltService.dongBoHoSo(req, securityKey, requestPath);
        logger.info("DIGO-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    @PostMapping(value = "/capNhatTrangThaiHoSoDVCLT", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> capNhatTrangThaiHoSoDVCLT(
            HttpServletRequest request,
            @RequestHeader(value = "securityKey", required = true) String securityKey,
            @RequestBody  List<@Valid DVCLTDossierTrackingDataDto> req) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String key = StringHelper.toHmacSha256(securityKeyIgate, req.get(0).getNationCode());
        if(!key.equals(securityKey)){
            Object ob =  JSON.parse("{'401':'Unauthorized'}");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ob);
            //return ResponseEntity.badRequest().body(ob);
        }
        DVCLTResultDto res = dvcltService.capNhatTienDoHoSo(req, securityKey,requestPath);
        logger.info("DIGO-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    @ResponseStatus(HttpStatus.OK)
   @ExceptionHandler({ConstraintViolationException.class, Exception.class})
    public Object handleValidationExceptions(ConstraintViolationException ex, HttpServletRequest request) {
        String requestPath = request.getRequestURI();
        
        List<String> errors = new ArrayList<String>();
        List<String> DateErrors = new ArrayList<String>();
        for(ConstraintViolation constraintViolation : ex.getConstraintViolations()){
            if(constraintViolation.getMessage().contains("Ngay") || constraintViolation.getMessage().contains("ThoiDiemXuLy")){
                DateErrors.add(constraintViolation.getMessage());
            }else {
                errors.add(constraintViolation.getMessage());
            }
        }
        
        if(Objects.equals(requestPath, "/api/lienthongDVCLT/receiveRecord")){
            if(DateErrors.size() > 0){
                DVCLTHoTichResponseDto errorMessage = new DVCLTHoTichResponseDto("400", 
                    translator.toLocale("lang.word.dvclt-invalid-input"),
                    "400",
                    translator.toLocale("lang.word.tag") + "<<" + String.join(",", DateErrors).trim() + ">>" + translator.toLocale("lang.word.dvclt-date-error"));
                return errorMessage;
            }else {
                DVCLTHoTichResponseDto errorMessage = new DVCLTHoTichResponseDto("400", 
                    translator.toLocale("lang.word.dvclt-invalid-input"),
                    "400",
                    translator.toLocale("lang.word.tag") + "<<" + String.join(",", errors).trim() + ">>" + translator.toLocale("lang.word.dvcl-is-not-valid"));
                return errorMessage;
            }
        } else {
            DVCLTResultDto errorMessage = new DVCLTResultDto();
            if(DateErrors.size() > 0){
                errorMessage = new DVCLTResultDto("0",
                        translator.toLocale("lang.word.tag") + "<<" + String.join(",", DateErrors).trim() + ">>" + translator.toLocale("lang.word.dvclt-date-error"));

            }else {
                errorMessage = new DVCLTResultDto("0",
                        translator.toLocale("lang.word.tag") + "<<" + String.join(",", errors).trim() + ">>" + translator.toLocale("lang.word.dvcl-is-not-valid"));

            }
            return errorMessage;
        }   
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public Object handleValidationExceptions(MethodArgumentNotValidException ex, HttpServletRequest request) {
        String requestPath = request.getRequestURI();
        if(Objects.equals(requestPath, "/api/lienthongDVCLT/receiveRecord")){
            List<String> errors = new ArrayList<String>();
            for (FieldError error : ex.getBindingResult().getFieldErrors()) {
                errors.add(error.getDefaultMessage());
            }
            for (ObjectError error : ex.getBindingResult().getGlobalErrors()) {
                errors.add(error.getDefaultMessage());
            }

            DVCLTHoTichResponseDto errorMessage = new DVCLTHoTichResponseDto("400", 
                    translator.toLocale("lang.word.dvclt-invalid-input"),
                    "400",
                    translator.toLocale("lang.word.tag") + " " + String.join(",", errors) + " " +  translator.toLocale("lang.phrase.is-not-valid"));

            return errorMessage;
        } else {
            List<String> errors = new ArrayList<String>();
            for (FieldError error : ex.getBindingResult().getFieldErrors()) {
                errors.add(error.getDefaultMessage());
            }
            for (ObjectError error : ex.getBindingResult().getGlobalErrors()) {
                errors.add(error.getDefaultMessage());
            }

            DVCLTResultDto errorMessage = new DVCLTResultDto("0",
                    translator.toLocale("lang.word.tag") + " " + String.join(",", errors) + " " +  translator.toLocale("lang.phrase.is-not-valid"));
            return errorMessage;
        }
    }

    @PostMapping(value = "/nhanHoSoDVCLTLoi", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> nhanHoSoDVCLTLoi(
            HttpServletRequest request,
            @RequestBody ArrayList<String> listCode) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
       
       
        DVCLTResultDto res = dvcltService.dongBoHoSoLoi(listCode);
        logger.info("DIGO-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    @PostMapping(value = "/capNhatTrangThaiHoSoDVCLTLoi", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> capNhatTrangThaiHoSoDVCLTLoi(
            HttpServletRequest request,
            @RequestBody ArrayList<String> listCode) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);


        DVCLTResultDto res = dvcltService.capNhatTienDoHoSoLoi(listCode);
        logger.info("DIGO-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    @PostMapping("/--getLogBodyDvclts")
    public Object syncDossierLogs(HttpServletRequest request,
                                  @RequestParam(value = "code", required = false) String code){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        Logger logger = LoggerFactory.getLogger(JudicialCivilStatusController.class);
        logger.info("DIGO-Info: " + requestPath);
        return dvcltService.getdataBodyDVCLT(code);
    }

    @PostMapping(value = "/receiveRecord", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> receiveRecord(
            HttpServletRequest request,
            @RequestParam(value = "kafkaEnable", required = false) String kafkaEnable,
            @RequestHeader(value = "securityKey", required = true) String securityKey,
            @RequestBody @Valid DVCLTHoTichRequestDto req) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        try {
            dvcltService.bodyLogsHTTP(req);
        }catch (Exception e){

        }
        String key = StringHelper.toHmacSha256(securityKeyIgate, req.getCode());
        if(!key.equals(securityKey)){
            Object ob =  JSON.parse("{'401':'Unauthorized'}");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ob);
        }
        DVCLTHoTichResponseDto res = dvcltService.dongDongBoHSHoTich(req, securityKey, requestPath, kafkaEnable);
        logger.info("DIGO-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    @PostMapping(value = "/{id}/--resend")
    public ResponseEntity<Object> receiveRecord(
            HttpServletRequest request,
            @RequestHeader(value = "securityKey", required = true) String securityKey,
            @PathVariable(value = "id", required = true) ObjectId id,
            @RequestHeader(value = "kafkaEnable", required = false) String kafkaEnable) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        HashMap<String, Object> res = dvcltService.dongDongBoHSHoTich(id, securityKey, kafkaEnable);
        return ResponseEntity.ok(res);
    }

    @PostMapping(value = "/capNhatTrangThaiHoSoDVCLTHoTich", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> capNhatTrangThaiHoSoDVCLTHoTich(
            HttpServletRequest request,
//            @RequestHeader(value = "securityKey", required = true) String securityKey,
            @RequestBody DVCLTHoTichPostDto req
    ) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        return ResponseEntity.ok(dvcltService.capNhatTrangThaiHoSoDVCLTHoTich(req, requestPath));
    }

    @GetMapping(value = "/getLog")
    public ResponseEntity<Object> getLog(HttpServletRequest request,
                                         @RequestParam(value = "code", required = false) String code,
                                         @RequestParam(value = "nationCode", required = false) String nationCode,
                                         @RequestParam(value = "api", required = false) String api,
                                         @RequestParam(value = "status", required = false) Integer status,
                                         @RequestParam(value = "error", required = false) Boolean error,
                                         Pageable pageable) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        return ResponseEntity.ok(dvcltService.getLog(code, nationCode, api, status, error, pageable));
    }

    @GetMapping(value = "/getLog/{id}")
    public ResponseEntity<Object> getLogFull(HttpServletRequest request,
                                             @PathVariable(value = "id", required = true) ObjectId id) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        return ResponseEntity.ok(dvcltService.getLogFull(id));
    }

    @GetMapping(value = "/getLogCapNhatTrangThai")
    public ResponseEntity<Object> getLogCapNhatTrangThai(HttpServletRequest request,
                                         @RequestParam(value = "code", required = false) String code,
                                         @RequestParam(value = "nationCode", required = false) String nationCode,
                                         @RequestParam(value = "status", required = false) Integer status,
                                         Pageable pageable) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        return ResponseEntity.ok(dvcltService.getLogCapNhatTrangThai(code, nationCode, status, pageable));
    }
}
