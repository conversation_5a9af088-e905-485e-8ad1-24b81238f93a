/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import java.text.ParseException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.BudgetDossierByPeriodDataDto;
import vn.vnpt.digo.adapter.dto.BudgetDossierDataDto;
import vn.vnpt.digo.adapter.dto.BudgetDossierDetailReturnDto;
import vn.vnpt.digo.adapter.dto.BudgetDossierListReturnDto;
import vn.vnpt.digo.adapter.dto.BudgetRelationshipCodeParamsDto;
import vn.vnpt.digo.adapter.service.BudgetRelationshipCodeService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/budget-relationship")
@IcodeAuthorize("vnpt.permission.budget-relationship")
public class BudgetRelationshipCodeController { 
    @Autowired
    private BudgetRelationshipCodeService budgetRelationshipCodeService;
    Logger logger = LoggerFactory.getLogger(BudgetRelationshipCodeController.class);
    
    @GetMapping("/--list-file")
    public BudgetDossierListReturnDto getListFile(HttpServletRequest request, @Valid @RequestParam BudgetRelationshipCodeParamsDto params) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        //String res = socialInsuranceService.getTthgd(params, body);
        BudgetDossierListReturnDto res = budgetRelationshipCodeService.getListFile(params);
        logger.info("DIGO-Response: " + res.toString());
        return res; 
    }
    
    @GetMapping("/--file-details")
    public BudgetDossierDataDto getFileDetails(HttpServletRequest request, @Valid @RequestParam BudgetRelationshipCodeParamsDto params){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        //String res = socialInsuranceService.getTthgd(params, body);
        BudgetDossierDataDto res = budgetRelationshipCodeService.getFileDetails(params);
        logger.info("DIGO-Response: " + res.toString());
        return res; 
    }
    
    @GetMapping("/--results-list")
    public BudgetDossierByPeriodDataDto getResultsList(HttpServletRequest request, @Valid @RequestParam BudgetRelationshipCodeParamsDto params) throws ParseException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        //String res = socialInsuranceService.getTthgd(params, body);
        BudgetDossierByPeriodDataDto res = budgetRelationshipCodeService.getResultsList(params);
        logger.info("DIGO-Response: " + res.toString());
        return res; 
    }
}
