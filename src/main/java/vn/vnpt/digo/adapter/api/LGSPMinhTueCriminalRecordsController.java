package vn.vnpt.digo.adapter.api;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.IntegrationParamsDto;
import vn.vnpt.digo.adapter.dto.minhtue.criminalrecords.CRDossierDto;
import vn.vnpt.digo.adapter.dto.minhtue.criminalrecords.CRMarkAsDoneDto;
import vn.vnpt.digo.adapter.dto.minhtue.criminalrecords.CRSendResDto;
import vn.vnpt.digo.adapter.dto.minhtue.criminalrecords.CRStatusDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.Permission;
import vn.vnpt.digo.adapter.service.LGSPMinhTueCriminalRecordsService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import vn.vnpt.digo.adapter.dto.minhtue.criminalrecords.CRResDto;
import vn.vnpt.digo.adapter.util.Context;

@RestController
@RequestMapping("/lgsp-minhtue-cr")
//@IcodeAuthorize("vnpt.permission.integrated.scope")
public class LGSPMinhTueCriminalRecordsController {
    @Autowired
    private LGSPMinhTueCriminalRecordsService service;

    @Value("${vnpt.permission.integrated.scope}")
    private String scope;

    Logger logger = LoggerFactory.getLogger(LGSPMinhTueCriminalRecordsController.class);


    @PostMapping("/--send")
    public CRSendResDto sendRecord(HttpServletRequest request, @Valid @RequestParam IntegrationParamsDto params,
                                   @Valid @RequestBody Map<String, Object> body,
                                   @RequestParam(value = "code", required = false) String code,
                                   @RequestParam(value = "log-id", required = false) ObjectId logID) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        //check permission scope
        if (scope != null && !scope.isEmpty()) {
            Permission permission = Context.getPermission(scope);
            if (permission == null) {
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        }
        logger.info("DIGO-Info: " + requestPath);
        CRSendResDto res = service.writeLogHTTP(request, params, body, code, logID);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @GetMapping("/--receive")
    public List<CRDossierDto> receiveRecord(HttpServletRequest request,
                                            @Valid @RequestParam IntegrationParamsDto params) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        //check permission scope
        if (scope != null && !scope.isEmpty()) {
            Permission permission = Context.getPermission(scope);
            if (permission == null) {
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        }
        List<CRDossierDto> res = service.receiveRecord(params);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @GetMapping("/--receive-status")
    public List<CRStatusDto> receiveStatus(HttpServletRequest request, @Valid @RequestParam IntegrationParamsDto params)
            throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        //check permission scope
        if (scope != null && !scope.isEmpty()) {
            Permission permission = Context.getPermission(scope);
            if (permission == null) {
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        }
        logger.info("DIGO-Info: " + requestPath);
        List<CRStatusDto> res = service.receiveStatus(params);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @PostMapping("/--mark-as-done")
    public CRSendResDto markAsDone(HttpServletRequest request, @Valid @RequestParam IntegrationParamsDto params,
                                   @Valid @RequestBody CRMarkAsDoneDto body) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        //check permission scope
        if (scope != null && !scope.isEmpty()) {
            Permission permission = Context.getPermission(scope);
            if (permission == null) {
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        }
        logger.info("DIGO-Info: " + requestPath);
        CRSendResDto res = service.markAsDone(params, body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    @GetMapping("/--tra-danh-muc")
    public CRResDto traDanhMuc(HttpServletRequest request, @Valid @RequestParam IntegrationParamsDto params,@RequestParam(value = "infoType", defaultValue = "1") String infoType) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        CRResDto res = service.getCategory(params,infoType);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
}
