package vn.vnpt.digo.adapter.api;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.bhtn.AdditionalStatusUpdateDossierDto;
import vn.vnpt.digo.adapter.dto.bhtn.BhtnResDto;
import vn.vnpt.digo.adapter.dto.bhtn.DongBoTrangThaiKetThucDto;
import vn.vnpt.digo.adapter.dto.bhtn.FeedbackProfileResultDossierDto;
import vn.vnpt.digo.adapter.dto.bhtn.PutTaxResultVbdlisDto;
import vn.vnpt.digo.adapter.dto.bhtn.ReceivingDossierDto;
import vn.vnpt.digo.adapter.dto.bhtn.ResultUpdateDossierDto;
import vn.vnpt.digo.adapter.dto.bhtn.SendProcessingNoticeDossierDto;
import vn.vnpt.digo.adapter.dto.bhtn.TiepTucXuLyDto;
import vn.vnpt.digo.adapter.dto.bhtn.YeuCauBoSungHoSoDto;
import vn.vnpt.digo.adapter.dto.bhtn.UpdateFinishDossierDto;
import vn.vnpt.digo.adapter.service.BhtnService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/bhtn")
@IcodeAuthorize("vnpt.permission.integrated")
public class BhtnController {

    @Autowired
    private BhtnService bhtnService;

    Logger logger = LoggerFactory.getLogger(BhtnController.class);


    // 2.1 Tiếp nhận hồ sơ một cửa
    @PostMapping("/--receiving-dossier")
    public BhtnResDto.ReceivingDossierResponse receivingDossier(HttpServletRequest request,
                                                                @Valid @RequestBody ReceivingDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.receivingDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @PostMapping("/--result-update-dossier")
    public BhtnResDto.ResultUpdateDossierResponse resultUpdateDossier(HttpServletRequest request,
                                                                        @Valid @RequestBody ResultUpdateDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.resultUpdateDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }



    @PostMapping("/--additional-status-update")
    public BhtnResDto.AdditionalStatusUpdateDossierResponse additionalStatusUpdateDossier(HttpServletRequest request,
                                                                                            @Valid @RequestBody AdditionalStatusUpdateDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.aditionalStatusUpdateDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @PostMapping("/--feedback-profile-result-dossier")
    public BhtnResDto.FeedbackProfileResultDossierResponse feedbackProfileResultDossier(HttpServletRequest request,
                                                                                          @Valid @RequestBody FeedbackProfileResultDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.feedbackProfileResultDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @PostMapping("/--send-processing-notice-dossier")
    public BhtnResDto.SendProcessingNoticeDossierResponse feedbackProfileResultDossier(HttpServletRequest request,
                                                                                         @Valid @RequestBody SendProcessingNoticeDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.sendProcessingNoticeDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    //ILIS 3.2
    @GetMapping("/GetDanhMucThuTucHanhChinh")
    public BhtnResDto.DanhMucThuTucResponse getDanhMucThuTucHanhChinh(HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.getDanhMucThuTucHanhChinh();
        logger.info("DIGO-Response: " + res.toString());

        return res;
    }

    @GetMapping("/GetDanhMucNguoiDung")
    public BhtnResDto.DanhMucNguoiDungResponse getDanhMucNguoiDung(HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.getDanhMucNguoiDung();
        logger.info("DIGO-Response: " + res.toString());

        return res;
    }

    @GetMapping("/GetDanhMucTrangThai")
    public BhtnResDto.DanhMucTrangThaiResponse GetDanhMucTrangThai(HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.getDanhMucTrangThai();
        logger.info("DIGO-Response: " + res.toString());

        return res;
    }


    // ILIS 3.6
    @PostMapping("/KetThucHoSo")
    public BhtnResDto.YeuCauBoSungResponse finishDossierv2(HttpServletRequest request,
                                                             @Valid @RequestBody DongBoTrangThaiKetThucDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.postDongBoTrangThaiKetThuc(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }


    // 2.2 Cập nhật trạng thái bổ sung
    @PostMapping("/--update-additional-request")
    public BhtnResDto.AdditionalStatusUpdateDossierResponse updateAdditionalRequestDossier(HttpServletRequest request,
                                                                                             @Valid @RequestBody AdditionalStatusUpdateDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.updateAdditionalRequestDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    // ILIS 3.3
    @PostMapping("/YeuCauBoSungHoSo")
    public BhtnResDto.YeuCauBoSungResponse postYeuCauBoSungHoSo(HttpServletRequest request,
                                                                  @Valid @RequestBody YeuCauBoSungHoSoDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.postYeucauBoSung(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    // ILIS 3.4
    @PostMapping("/TiepTucXuLy")
    public BhtnResDto.YeuCauBoSungResponse postTiepTucXuLy(HttpServletRequest request,
                                                                @Valid @RequestBody TiepTucXuLyDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.postTiepTucXuLy(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }


    // ILIS 3.5
    @PostMapping("/GuiKetQuaThue")
    public BhtnResDto.GuiKetQuaThueResponse finishDossier(HttpServletRequest request,
                                                              @Valid @RequestBody PutTaxResultVbdlisDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.postTaxResult(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }


    //2.3 Cập nhật kết quả trả hồ sơ
    @PutMapping("/--update-result-finish-dossier")
    public BhtnResDto.ResultUpdateDossierResponse updateResultFinishDossier(HttpServletRequest request,
                                                                              @Valid @RequestBody UpdateFinishDossierDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = bhtnService.updateResultFinishDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
}
