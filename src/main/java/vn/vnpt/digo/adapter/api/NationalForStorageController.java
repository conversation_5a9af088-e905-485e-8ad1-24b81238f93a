package vn.vnpt.digo.adapter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.pndp.GetPersonalOriginDto;
import vn.vnpt.digo.adapter.service.NationalForStorageService;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;

@RestController
@RequestMapping("/national-for-storage")
@IcodeAuthorize("vnpt.permission.national-for-storage")
public class NationalForStorageController {
    @Autowired
    private NationalForStorageService nationalForStorageService;

    Logger logger = LoggerFactory.getLogger(NationalPublicServiceController.class);

    @GetMapping("/--sync-procedures")
    public ResponseEntity<Object> syncProcedures(
            HttpServletRequest request,
            @RequestParam(name = "agency-code") String agencyCode,
            @RequestParam(name = "form-time", required = false) String fromTime,
            @RequestParam(name = "to-time", required = false) String toTime) throws UnsupportedEncodingException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        Object response = nationalForStorageService.syncProcedures(agencyCode,fromTime,toTime);
        logger.info("DIGO-Response: " + response.toString());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/--sync-detail-procedure")
    public ResponseEntity<Object> getProcedureDetailBasic(
            HttpServletRequest request,
            @RequestParam(name = "code") String code,
            @RequestParam(name = "agency-code") String agencyCode) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        Object procedureDetail = nationalForStorageService.getProcedureDetailBasic(code, agencyCode);
        logger.info("DIGO-Response: " + procedureDetail.toString());
        return ResponseEntity.ok(procedureDetail);
    }

    @GetMapping("/--sync-forms")
    public ResponseEntity<Object> syncNationalForms(
            HttpServletRequest request,
            @RequestParam(name = "agency-code", required = true) String agencyCode,
            @RequestParam(name = "code", required = false) String code,
            @RequestParam(name = "service", required = true) String service,
            Pageable pageable
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        Object response = nationalForStorageService.syncNationalForms(agencyCode, code, service, pageable);
        logger.info("DIGO-Response: " + response.toString());
        return ResponseEntity.ok(response);
    }

    @PostMapping("/--forms-citizen")
    public ResponseEntity<Object> getFormsCitizen(HttpServletRequest request,
                                                   @RequestBody GetPersonalOriginDto input){
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        Object response = nationalForStorageService.getPersonalOrigin(input);
        logger.info("DIGO-Response: " + response);
        return ResponseEntity.ok(response);
    }
}
