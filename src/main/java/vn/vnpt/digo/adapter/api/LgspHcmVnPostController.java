package vn.vnpt.digo.adapter.api;

import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.LgspVNPostItemDto;
import vn.vnpt.digo.adapter.dto.LgspVNPostMDocumentDto;
import vn.vnpt.digo.adapter.dto.LgspVNPostPostageVasDto;
import vn.vnpt.digo.adapter.dto.LgspVNPostPriceResponseDto;
import vn.vnpt.digo.adapter.dto.VNPostResultDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.ApiResultDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LLTPDeclarationTraHoSoDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LLTPNhanHoSoDangKyDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LLTPNhanHoSoDangKyOutputDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LLTPTraHoSoDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LgspHcmDsTrangThaiHsLltpResDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LgspHcmTraDsTrangThaiHoSoLttpDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.HsNhanThanhCongInputDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.lltpInfoTypeDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.lltpReturnDossierResDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.markSuccessfulReceivedDossierDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LgspHcmDsTrangThaiHsLltpDto;
import vn.vnpt.digo.adapter.service.IntegratedConfigurationService;
import vn.vnpt.digo.adapter.service.LgspHcmVnPostService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lgspHcmVnpost")
@IcodeAuthorize("vnpt.permission.lgsphcm-vnpost")
public class LgspHcmVnPostController {

    @Autowired
    private LgspHcmVnPostService serviceVNpost;
    
    
    Logger logger = LoggerFactory.getLogger(LgspHcmVnPostController.class);
    
        //    @PostMapping("/--token-lgsp")
       //    public String getTokenLGSP(HttpServletRequest request){
       //        return lgspHCMLLTPService.getTokenLGSP();
       //    }


    /**
     * Date 20220711
     * Author: phucnh.it2
     * Note: 3.9 API hien thi gia tri niem yet khi nop
     * Link API: https://api.ngsp.gov.vn/apiVNPostNGSP/p1.0/info/PriceHCC
     */
    @GetMapping("/priceHCC")
    public ApiResultDto getPrice(HttpServletRequest request){        
        ApiResultDto ret = new ApiResultDto();        
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        
        logger.info("getPrice: DIGO-Info 1.1: " + requestPath);
        ret = serviceVNpost.getPriceHCC();
        logger.info("DIGO-Response: " + ret);
                
        return ret;
    }

    /**
     * Date 20220711
     * Author: phucnh.it2
     * Note: 3.8 API Lay thong tin buu cuc
     * Link API: https://api.ngsp.gov.vn/apiVNPostNGSP/p1.0/info/GetInfomationPost
     */
    @GetMapping("/getInfo")
    public ApiResultDto getVnPostInfomation(HttpServletRequest request, 
             @Valid @RequestParam("ProvinceCode") Integer provinceCode, 
             @Valid @RequestParam("DistrictCode") Integer districtCode,
             @Valid @RequestParam("CommuneCode") String communeCode
    ) throws Exception 
    {
        ApiResultDto ret = new ApiResultDto();
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        
        logger.info("syncConversationInfomation: DIGO-Info 1.1: " + requestPath);
        ret = serviceVNpost.getVnPostInfomation(provinceCode, districtCode, communeCode);
        logger.info("DIGO-Response: " + ret);        
        return ret;
    }
    
    /**
     * Date 20220712
     * Author: phucnh.it2
     * Note: 3.6 API Lay thong tin cuoc thu ho
     * Link API: https://api.ngsp.gov.vn/apiVNPostNGSP/p1.0/info/GetPostageVas
     */
    @GetMapping("/getInfoCollectionFee")
    public ApiResultDto getInfoCollectionFee(HttpServletRequest request, 
             @Valid @RequestParam("ProfileFee") Integer profileFee
    ) throws Exception 
    {  
        ApiResultDto ret = new ApiResultDto();
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        
        logger.info("getInfoCollectionFee: DIGO-Info 1.1: " + requestPath);        
        ret = serviceVNpost.getInfoCollectionFee(profileFee);        
        logger.info("DIGO-Response: " + ret);        
        return ret;
    }
    
    /**
     * Date 20220712
     * Author: phucnh.it2
     * Note: 3.5 API Lay thong tin van chuyen
     * Link API: https://api.ngsp.gov.vn/apiVNPostNGSP/p1.0/order/tracking
     */
    @GetMapping("/getShippingInfo")
    public ApiResultDto getShippingInfo(HttpServletRequest request, 
            @Valid @RequestParam("pagesize") String pagesize,
            @Valid @RequestParam("lastId") String lastId
    ) throws Exception 
    {
        ApiResultDto ret = new ApiResultDto();   
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        
        logger.info("getShippingInfo: DIGO-Info 1.1: " + requestPath);        
        ret = serviceVNpost.getShippingInfo(pagesize, lastId);        
        logger.info("DIGO-Response: " + ret);        
        return ret;
    }
       
    
    /**
     * Date 20220712
     * Author: phucnh.it2
     * Note: 3.4 API huy van don
     * Link API: https://api.ngsp.gov.vn/apiVNPostNGSP/p1.0/order/cancel
     */
    @GetMapping("/getCancelBilling")
    public String getCancelBilling(HttpServletRequest request, 
            @Valid @RequestParam("CustomerCode") String customerCode,
            @Valid @RequestParam("OrderNumber") String orderNumber
    ) throws Exception 
    {
            
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        
        logger.info("getCancelBilling: DIGO-Info 1.1: " + requestPath);        
        String ret = serviceVNpost.getCancelBilling(customerCode, orderNumber);        
        logger.info("DIGO-Response: " + ret);        
        return ret;
    }

    @PostMapping("/getOrder")
    public VNPostResultDto getOrder(HttpServletRequest request, 
            @Valid @RequestBody Map<String, Object> body) throws Exception 
    {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        
        logger.info("postOrder: DIGO-Info 1.1: " + requestPath);        
            VNPostResultDto ret = serviceVNpost.getOrder(body);        
        logger.info("DIGO-Response: " + ret);        
        return ret;
    }

    @PostMapping("/getOrder-sync")
    public VNPostResultDto getOrderSync(HttpServletRequest request,
                                    @Valid @RequestBody Map<String, Object> body) throws Exception
    {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");

        logger.info("postOrder: DIGO-Info 1.1: " + requestPath);
        VNPostResultDto ret = serviceVNpost.saveOrder(body);
        logger.info("DIGO-Response: " + ret);
        return ret;
    }
    
    @PostMapping("/getPrice")
    public LgspVNPostPriceResponseDto getPrice(HttpServletRequest request,
             @Valid @RequestBody Map<String, Object> body) throws Exception
    {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        
        logger.info("postOrder: DIGO-Info 1.1: " + requestPath);        
        LgspVNPostPriceResponseDto ret = serviceVNpost.getPrice(body);        
        logger.info("DIGO-Response: " + ret);
        return ret;
    }
}
