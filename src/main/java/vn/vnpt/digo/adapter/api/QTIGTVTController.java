package vn.vnpt.digo.adapter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;

import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.qti.GTVTDossierDto;
import vn.vnpt.digo.adapter.service.QTIGTVTService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/qti-gtvt")
@IcodeAuthorize("vnpt.permission.qti-gtvt")

public class QTIGTVTController {

    @Autowired
    private QTIGTVTService service;

    Logger logger = LoggerFactory.getLogger(QTIGTVTController.class);

    @PostMapping("/--apply-online")
    // @IcodeAuthorize(permission = "manageDossier")
    public String postDossierGTVTOnline(HttpServletRequest request,
                                   @Valid @RequestBody GTVTDossierDto postGTVTDossierDto) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        Logger logger = LoggerFactory.getLogger(QTIGTVTController.class);
        logger.info("DIGO-Info: " + requestPath);
        String res = service.addDossierGTVT(postGTVTDossierDto);
        return res;
    }

}
