package vn.vnpt.digo.adapter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.IntegrationParamsDto;
import vn.vnpt.digo.adapter.dto.minhtue.criminalrecords.CRSendResDto;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import vn.vnpt.digo.adapter.dto.minhtue.tntm.TNMTListDossierDto;
import vn.vnpt.digo.adapter.dto.minhtue.tntm.TNMTUpdateDossierDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.pojo.Permission;
import vn.vnpt.digo.adapter.service.LGSPMinhTueTNMTService;
import vn.vnpt.digo.adapter.util.Context;

@RestController
@RequestMapping("/lgsp-minhtue-tnmt")
@IcodeAuthorize("vnpt.permission.lgsp-minhtue-tnmt")
public class LGSPMinhTueTNMTController {
    @Autowired
    private LGSPMinhTueTNMTService service;

    @Value("${vnpt.permission.integrated.scope}")
    private String scope;

    Logger logger = LoggerFactory.getLogger(LGSPMinhTueCriminalRecordsController.class);

    @PostMapping("/danh-sach-ho-so-theo-ngay")
    public ArrayList<IdDto> getListDossierByDate(HttpServletRequest request,
                                                 @Valid @RequestBody Map<String, Object> body) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        ArrayList<IdDto> res = service.getListDossierByDate(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    @PostMapping("/danh-sach-ho-so-mac-dinh")
    public List<TNMTListDossierDto> getListDossierDefault() throws Exception {
        if (scope != null && !scope.isEmpty()) {
            Permission permission = Context.getPermission(scope);
            if (permission == null) {
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        }
        List<TNMTListDossierDto> res = service.getListDossierDefault();
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    @PostMapping("/danh-sach-ho-so-mac-dinh-hcm")
    public List<TNMTListDossierDto> getListDossierDefaultHCM() throws Exception {
        if (scope != null && !scope.isEmpty()) {
            Permission permission = Context.getPermission(scope);
            if (permission == null) {
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        }
        List<TNMTListDossierDto> res = service.getListDossierDefaultHCM();
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    @PostMapping("/cap-nhat-ho-so")
    public TNMTUpdateDossierDto updateDossier(HttpServletRequest request,
                                              @Valid @RequestBody Map<String, Object> body) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        TNMTUpdateDossierDto res = service.updateDossier(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @GetMapping("/config-ma-tinh")
    public String getConfig() throws Exception {
        String res = service.getConfig();
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
}
