package vn.vnpt.digo.adapter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.service.AGGMonreIntegrationService;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/monre-agg")
@IcodeAuthorize("digo.permission.monre-agg")
public class AGGMonreIntegrationController {
    Logger logger = LoggerFactory.getLogger(AGGMonreIntegrationController.class);

    @Autowired
    private AGGMonreIntegrationService monreIntegrationService;

    @GetMapping("/--sync-from-monre")
    public ResponseEntity<Object> syncFromMonre(HttpServletRequest request) {
        monreIntegrationService.syncDossierFromMonre();
        return ResponseEntity.ok("");
    }
    @GetMapping("/--sync-to-monre")
    public ResponseEntity<Object> syncToMonre(HttpServletRequest request) {
        monreIntegrationService.syncDossierToMonre();
        return ResponseEntity.ok("");
    }
}
