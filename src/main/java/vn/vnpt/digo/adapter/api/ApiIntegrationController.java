package vn.vnpt.digo.adapter.api;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.bson.types.ObjectId;
import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.document.ApiIntegration;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.Integration.ProcessProcedureConfig;
import vn.vnpt.digo.adapter.dto.PostDossierIntegrationDto;
import vn.vnpt.digo.adapter.dto.PostDossierIntegrationResponseDto;
import vn.vnpt.digo.adapter.dto.nps.NpsAsyncReceiveDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.service.ApiIntegrationService;
import vn.vnpt.digo.adapter.util.Converter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api-integration")
@IcodeAuthorize("vnpt.permission.apiintegration")
public class ApiIntegrationController {

    Logger logger = LoggerFactory.getLogger(ApiIntegrationController.class);

    @Autowired
    private ApiIntegrationService apiIntegrationService;

    @Value(value = "${digo.api-integration.secret}")
    private String secret;

    @GetMapping()
    public Page<ApiIntegration> getListService(HttpServletRequest request, Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        Page<ApiIntegration> apiIntegration =  apiIntegrationService.getListApiIntegration(pageable);
        return apiIntegration;
    }

    @PostMapping("/--apply-dossier")
    public PostDossierIntegrationResponseDto applyDossier(HttpServletRequest request,
                                                               @Valid @RequestBody PostDossierIntegrationDto postDossierIntegrationDto) throws JsonProcessingException {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        Logger logger = LoggerFactory.getLogger(ApiIntegrationController.class);
        logger.info("DIGO-Info: " + requestPath);
        PostDossierIntegrationResponseDto res = apiIntegrationService.applyDossier(postDossierIntegrationDto);
        return res;
    }

    @GetMapping("/--get-caterogy")
    public String getCaterogy(HttpServletRequest request,
                              @RequestParam(name = "keyword", required = true) String keyword,
                              @RequestParam(name = "type", required = true) String type) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        Logger logger = LoggerFactory.getLogger(ApiIntegrationController.class);
        logger.info("DIGO-Info: " + requestPath);
        String res = apiIntegrationService.getCaterogy(keyword, type);
        return res;
    }

    @PostMapping("/--test")
    public Map<String, Object> test(HttpServletRequest request,
                                    @RequestBody NpsAsyncReceiveDto msg) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        Logger logger = LoggerFactory.getLogger(ApiIntegrationController.class);
        logger.info("DIGO-Info: " + requestPath);
       return apiIntegrationService.sendDossierAuto(msg);
    }

    @PostMapping("/{service}/--update-dossier")
    public AffectedRowsDto updateDossier(HttpServletRequest request,
                                         @PathVariable(value = "service", required = true) String service,
                                         @RequestParam(name = "key", required = true) String key,
                                         @RequestBody HashMap<String,Object> body) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        Logger logger = LoggerFactory.getLogger(ApiIntegrationController.class);
        logger.info("DIGO-Info: " + requestPath);
        return apiIntegrationService.updateDossier(service, key, body);
    }

    @GetMapping("/--detail-apiIntegration")
    public List<ProcessProcedureConfig> getDetailApiIntegration(HttpServletRequest request,
                                                                @RequestParam(name = "apiIntegrationId", required = true) String apiIntegrationId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        Logger logger = LoggerFactory.getLogger(ApiIntegrationController.class);
        logger.info("DIGO-Info: " + requestPath);
        List<ProcessProcedureConfig> res = apiIntegrationService.getListProcessProcedureConfig(apiIntegrationId);
        return res;
    }

    @GetMapping("/service")
    public ResponseEntity<Object> get(@RequestParam(value = "keyword", required = false) String keyword,
                                      @RequestParam(value = "pfcode", required = false) String pfcode,
                                      @RequestParam(value = "fcodes", required = false) List<String> fcodes,
                                      @RequestParam(value = "show-all-service", defaultValue = "false") boolean showAllService,
                                      @RequestParam(value = "deployment-id", required = false) ObjectId deploymentId,
                                      Pageable pageable){
        ResponseEntity<Object> response = apiIntegrationService.search(deploymentId,keyword, pfcode,fcodes, showAllService,pageable);
        return response;
    }

    @DeleteMapping("/dossierAutoSendLog")
    public ResponseEntity<Object> deleteDossierAutoSendLog(HttpServletRequest request,
                                                           @RequestParam(value = "code", required = true) String code) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        AffectedRowsDto affectedRows = apiIntegrationService.deleteDossierAutoSendLog(code);
        logger.info("DIGO-Response: " + affectedRows.toString());

        return ResponseEntity.ok(affectedRows);
    }

    @PostMapping("/--send-kafka")
    public Map<String, Object> sendKafka(HttpServletRequest request,
                                    @RequestBody NpsAsyncReceiveDto msg) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        Logger logger = LoggerFactory.getLogger(ApiIntegrationController.class);
        logger.info("DIGO-Info: " + requestPath);
        Map<String, Object> resultMap = apiIntegrationService.sendDossierAuto(msg);
        Map<String, Object> response = new HashMap<>();
        response.put("response", resultMap.get("response"));
        return response;
    }
}
