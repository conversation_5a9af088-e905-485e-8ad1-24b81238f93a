package vn.vnpt.digo.adapter.api;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.dbn.DBNInputHoSoDto;
import vn.vnpt.digo.adapter.dto.dbn.DBNReportRequestDto;
import vn.vnpt.digo.adapter.service.DBNDigitizeService;
import vn.vnpt.digo.adapter.service.DBNVNPostService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/dbn-vnpost")
@IcodeAuthorize("vnpt.permission.dbn-vnpost")
public class DBNVNPostController {
    Logger logger = LoggerFactory.getLogger(DBNDigitizeController.class);
    @Autowired
    DBNVNPostService dbnvnPostService;

    @PostMapping("/--report")
    public ResponseEntity<Object> digitizeDossier(HttpServletRequest request, @Valid @RequestBody DBNReportRequestDto body) throws JsonProcessingException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        var res = dbnvnPostService.getReport(body);
        logger.info("DIGO-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }
}
