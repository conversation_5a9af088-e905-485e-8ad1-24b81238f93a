/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.websocket.server.PathParam;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.document.LGSPHCMLog;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMDanhMucDonViDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMLogInputDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMLogListDto;
import vn.vnpt.digo.adapter.service.LGSPHCMDvcqgService;
import vn.vnpt.digo.adapter.util.ApiLogTypes;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lgsp-dvcqg")
@IcodeAuthorize("vnpt.permission.lgsphcm-dvcqg")
public class LGSPHCMTraCuuHoSoController {
    @Autowired
    private LGSPHCMDvcqgService service;

    Logger logger = LoggerFactory.getLogger(LGSPHCMLogController.class);
    
    @GetMapping("/--traCuuHoSo")
    public Object getLGSPHCMLog(HttpServletRequest request,
            @RequestParam(value = "maHoSo", required = false) String maHoSo,
            @RequestParam(value = "agency-code", required = false) String agencyCode){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        Object ret = service.traCuuHoSo(maHoSo);
        logger.info("DIGO-Response: " + ret.toString());
        return ret;
    }
    
}
