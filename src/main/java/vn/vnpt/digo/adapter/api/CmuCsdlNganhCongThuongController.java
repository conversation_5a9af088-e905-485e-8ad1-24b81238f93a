/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.asxh.AsxhAddRequestDto;
import vn.vnpt.digo.adapter.dto.asxh.AsxhAddResponseDto;
import vn.vnpt.digo.adapter.dto.cmu.CongDanResponseDto;
import vn.vnpt.digo.adapter.dto.cmu.CsdlNganhCongThuongRequestDto;
import vn.vnpt.digo.adapter.dto.cmu.CsdlNganhCongThuongResponseDto;
import vn.vnpt.digo.adapter.dto.cmu.FaceRecognitionResponseDto;
import vn.vnpt.digo.adapter.dto.ilis.IlisResDto;
import vn.vnpt.digo.adapter.dto.ilis.ReceivingDossierDto;
import vn.vnpt.digo.adapter.service.AsxhService;
import vn.vnpt.digo.adapter.service.CmuCsdlNganhCongThuongService;
import vn.vnpt.digo.adapter.service.CmuFaceRecognitionService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/csdl-cong-thuong")
@IcodeAuthorize("vnpt.permission.integrated")
public class CmuCsdlNganhCongThuongController {
    
    @Autowired
    private CmuCsdlNganhCongThuongService cmuCsdlNganhCongThuongService;

    Logger logger = LoggerFactory.getLogger(CmuCsdlNganhCongThuongController.class);


    // Tiếp nhận hồ sơ
    @PostMapping("/--tiep-nhan-ho-so")
    public CsdlNganhCongThuongResponseDto tiepNhanHoSo(HttpServletRequest request,
                                                       @Valid @RequestBody CsdlNganhCongThuongRequestDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        CsdlNganhCongThuongResponseDto res = cmuCsdlNganhCongThuongService.tiepNhanHoSo(body, requestPath);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }


    // Cập nhật hồ sơ
    @PostMapping("/--cap-nhat-trang-thai")
    public CsdlNganhCongThuongResponseDto capNhatTrangThai(HttpServletRequest request,
                                                       @Valid @RequestBody CsdlNganhCongThuongRequestDto body
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = cmuCsdlNganhCongThuongService.tiepNhanHoSo(body, requestPath);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @GetMapping("/GetDanhMucThuTucHanhChinh")
    public List<CsdlNganhCongThuongResponseDto.DanhMucThuTucRes> getDanhMucThuTucHanhChinh(HttpServletRequest request
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        var res = cmuCsdlNganhCongThuongService.getDanhMucThuTucHanhChinh();
        logger.info("DIGO-Response: " + res.toString());

        return res;
    }



}
