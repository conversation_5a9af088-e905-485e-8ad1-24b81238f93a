package vn.vnpt.digo.adapter.api;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.AffectedMessageDto;
import vn.vnpt.digo.adapter.dto.GetIportalCategoryDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.csdldc.CitizenResponseDto;
import vn.vnpt.digo.adapter.dto.csdldc.ShareCitizenInfoRequestDto;
import vn.vnpt.digo.adapter.dto.iworkplace.IWorkplaceRequestDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.service.CSDLDCService;
import vn.vnpt.digo.adapter.service.IWorkplaceService;
import vn.vnpt.digo.adapter.util.Context;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Objects;

@RestController
@RequestMapping("/iworkplace")
@IcodeAuthorize("vnpt.permission.integrated")
public class IWorkplaceController {

    @Autowired
    private IWorkplaceService iWorkplaceService;

    Logger logger = LoggerFactory.getLogger(IWorkplaceController.class);

    @GetMapping("")
    public String getToken(HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);

        String token = iWorkplaceService.getToken();
        logger.info("DIGO-Info: " + token);
        return token;
    }

    @PostMapping("")
    public AffectedMessageDto postIWorkplace(HttpServletRequest request,
                                             @RequestBody IWorkplaceRequestDto iWorkplaceRequestDto) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        AffectedMessageDto affectedMessageDto = iWorkplaceService.postIWorkplace(iWorkplaceRequestDto);
        logger.info("DIGO-Response: " + affectedMessageDto);
        return affectedMessageDto;
    }
}
