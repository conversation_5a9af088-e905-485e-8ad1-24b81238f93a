package vn.vnpt.digo.adapter.api;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.api.IportalCategoryController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.AffectedMessageDto;
import vn.vnpt.digo.adapter.dto.event_log.PostEventLogDto;
import vn.vnpt.digo.adapter.dto.powaco.PowacoDossierResDto;
import vn.vnpt.digo.adapter.dto.powaco.PowacoDossierDto;
import vn.vnpt.digo.adapter.dto.so_tnmt_agg.RequestUpdateStatusDto;
import vn.vnpt.digo.adapter.dto.so_tnmt_agg.ResponseUpdateStatusDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.service.PowacoService;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.xml.bind.JAXBException;
import java.util.Map;


@RestController
@RequestMapping("/powaco")
@IcodeAuthorize("digo.permission.powaco")
public class PowacoController {

    @Autowired
    private PowacoService powacoService;
    Logger logger = LoggerFactory.getLogger(IportalCategoryController.class);

    @PostMapping("/--register-powaco")
    public PowacoDossierResDto registerPowaco(
            HttpServletRequest request,
            @Valid @RequestBody PowacoDossierDto powacoDossierDto
    ) {
        try {
            String configId = "";
            ObjectId logID = null;
            // Gọi dịch vụ để ghi log Powaco và trả về kết quả
            PowacoDossierResDto reponse = new PowacoDossierResDto();
            reponse = powacoService.writeLogPowaco(request, powacoDossierDto, powacoDossierDto.getMaHoSo(), logID, configId);
            return reponse;
        } catch (DigoHttpException digoEx) {
            // Xử lý ngoại lệ DigoHttpException
            return handleHttpException(digoEx);
        } catch (Exception exception) {
            // Xử lý ngoại lệ khác
            return handleGenericException(exception);
        }
    }

    // Xử lý ngoại lệ DigoHttpException
    private PowacoDossierResDto handleHttpException(DigoHttpException digoEx) {
        PowacoDossierResDto ret = new PowacoDossierResDto();
        ret.setStatus(-1);
        ret.setErrorDescription(digoEx.getArguments()[0]);
        return ret;
    }

    // Xử lý ngoại lệ chung
    private PowacoDossierResDto handleGenericException(Exception exception) {
        PowacoDossierResDto ret = new PowacoDossierResDto();
        ret.setStatus(-1);
        ret.setErrorDescription(exception.getMessage());
        return ret;
    }

    @PostMapping("/--update-status")
    public ResponseEntity<Object> updateStatusFromAGESB(
            HttpServletRequest request,
            @Valid @RequestBody String xml
    ) throws JAXBException {
        var result = powacoService.updateStatusFromAGESB(request, xml);
        return ResponseEntity.ok(result);
    }
}