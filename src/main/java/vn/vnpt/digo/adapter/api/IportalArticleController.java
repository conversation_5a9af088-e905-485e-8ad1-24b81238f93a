// /*
//  * To change this license header, choose License Headers in Project Properties.
//  * To change this template file, choose Tools | Templates
//  * and open the template in the editor.
//  */
// package vn.vnpt.digo.adapter.api;

// import javax.servlet.http.HttpServletRequest;
// import javax.validation.Valid;
// import org.bson.types.ObjectId;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.web.bind.annotation.DeleteMapping;
// import org.springframework.web.bind.annotation.ModelAttribute;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.PutMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.RestController;
// import vn.vnpt.digo.adapter.config.IcodeAuthorize;
// import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
// import vn.vnpt.digo.adapter.dto.HotArticleInputDto;
// import vn.vnpt.digo.adapter.dto.IdDto;
// import vn.vnpt.digo.adapter.dto.PostIportalArticleDto;
// import vn.vnpt.digo.adapter.dto.PublishArticleInputDto;
// import vn.vnpt.digo.adapter.dto.PutIportalArticleDto;
// import vn.vnpt.digo.adapter.dto.PutNewArticleDto;
// import vn.vnpt.digo.adapter.service.IportalArticleService;

// /**
//  *
//  * <AUTHOR>
//  */
// @RestController
// @RequestMapping("/iportal-article")
// @IcodeAuthorize("vnpt.permission.iportal-article")
// public class IportalArticleController {

//     @Autowired
//     private IportalArticleService iportalArticleService;

//     Logger logger = LoggerFactory.getLogger(IportalArticleController.class);

//     @PostMapping()
//     public IdDto postIportalArticle(HttpServletRequest request, @Valid @RequestBody @ModelAttribute PostIportalArticleDto postArticle) {
//         String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//         logger.info("DIGO-Info: " + requestPath);

//         IdDto newIportalArticle = iportalArticleService.postArticle(postArticle);
//         logger.info("DIGO-Info: " + newIportalArticle.toString());
//         return newIportalArticle;
//     }

//     @PutMapping("/{id}")
//     public AffectedRowsDto updateIportalArticle(HttpServletRequest request, @Valid @PathVariable(value = "id", required = true) String id,
//             @Valid @ModelAttribute PutIportalArticleDto inputArticle){
//         String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//         logger.info("DIGO-Info: " + requestPath);
        
//         AffectedRowsDto affectedRowsDto = iportalArticleService.putIportalArticle(id, inputArticle);
//         logger.info("DIGO-Info: " + affectedRowsDto);
//         return affectedRowsDto;
//     }
    
//     @DeleteMapping("/{id}")
//     public AffectedRowsDto deleteIportalArticle(HttpServletRequest request,
//             @PathVariable(value = "id", required = true) String id,
//             @RequestParam(value = "agency-id", required = true) ObjectId agencyId) {
//         String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//         logger.info("DIGO-Info: " + requestPath);

//         AffectedRowsDto affectedRows = iportalArticleService.deleteIportalArticle(id, agencyId);
//         logger.info("Affected Rows: " + affectedRows.getAffectedRows() + "");
//         return affectedRows;
//     }

//     @PutMapping("/{id}/--new")
//     public AffectedRowsDto updateNewIportalArticle(HttpServletRequest request, @PathVariable(value = "id", required = true) String id,
//             @Valid @RequestBody @ModelAttribute PutNewArticleDto putNewArticle) throws Exception {
//         String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//         logger.info("DIGO-Info: " + requestPath);

//         AffectedRowsDto affectedRows = iportalArticleService.updateNewArticle(putNewArticle, id);
//         logger.info("Affected Rows: " + affectedRows.getAffectedRows() + "");
//         return affectedRows;
//     }

//     @PutMapping("/{id}/--hot")
//     public AffectedRowsDto updateHotByIportal(HttpServletRequest request, @Valid @PathVariable("id") String id,
//             @Valid @ModelAttribute HotArticleInputDto input) {
//         String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//         logger.info("DIGO-Info: " + requestPath);

//         AffectedRowsDto affectedRows = iportalArticleService.updatedHotArticleByIportal(id, input);
//         logger.info("DIGO-Info: " + affectedRows);
//         return affectedRows;
//     }

//     @PutMapping("/{id}/--publish")
//     public AffectedRowsDto updateNewByIportal(HttpServletRequest request, @Valid @PathVariable("id") String id,
//             @Valid @ModelAttribute PublishArticleInputDto input) {

//         String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//         logger.info("DIGO-Info: " + requestPath);

//         AffectedRowsDto affectedRowsDto = iportalArticleService.updatePublishArticleByIportal(id, input);
//         logger.info("DIGO-Info: " + affectedRowsDto);
//         return affectedRowsDto;
//     }
// }
