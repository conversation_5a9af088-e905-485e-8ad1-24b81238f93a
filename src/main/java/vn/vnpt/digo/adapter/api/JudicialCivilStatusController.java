/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.CivilStatusJusticeGetCategoryDto;
import vn.vnpt.digo.adapter.dto.CivilStatusJusticeParamsDto;
import vn.vnpt.digo.adapter.dto.CivilStatusJusticeTokenDto;
import vn.vnpt.digo.adapter.dto.GetListCategoryMapLLTPHTTPDto;
import vn.vnpt.digo.adapter.dto.JudicialRecordsParamsDto;
import vn.vnpt.digo.adapter.dto.tandan.civil.CivilDossierStatusDto;
import vn.vnpt.digo.adapter.dto.tandan.civil.CivilDossierStatusResDto;
import vn.vnpt.digo.adapter.pojo.BodyCategoryMapHTTP;
import vn.vnpt.digo.adapter.pojo.BodyCategoryMapLLTP;
import vn.vnpt.digo.adapter.pojo.CivilStatusJusticeResponse;
import vn.vnpt.digo.adapter.pojo.ListValueCategoryMapHTTP;
import vn.vnpt.digo.adapter.service.JudicialCivilStatusService;

@RestController
@RequestMapping("/civil-status")
@IcodeAuthorize("vnpt.permission.civil-status")
public class JudicialCivilStatusController {
    @Autowired
    private JudicialCivilStatusService civilStatusJusticeService;
    
    Logger logger = LoggerFactory.getLogger(JudicialCivilStatusController.class);

    @Value("${digo.judicial.civil.status.case}")
    private Boolean judicalCivilStatusCase;
    
    @PostMapping("/--get-category")
    public CivilStatusJusticeGetCategoryDto getCategory(HttpServletRequest request, @Valid @RequestParam CivilStatusJusticeParamsDto params,@Valid @RequestBody BodyCategoryMapHTTP body)throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        CivilStatusJusticeGetCategoryDto res = civilStatusJusticeService.getCategory(params,body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
        
    }
    
    @PostMapping("/--send")
    public CivilStatusJusticeResponse sendCriminalRecord(
        HttpServletRequest request,
        @Valid @RequestParam CivilStatusJusticeParamsDto params,
        @Valid @RequestBody Map<String, Object> body,
        @RequestParam(value = "log-id", required = false) ObjectId logID,
        @RequestParam(value = "code", required = false) String code
    )throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        CivilStatusJusticeResponse res = null;
        if (judicalCivilStatusCase){
            res = civilStatusJusticeService.writeLogHTTPV2(request,params,body,code,logID,1);
        }else {
            res = civilStatusJusticeService.writeLogHTTP(request,params,body,code,logID,2);
        }
        logger.info("DIGO-Response: " + res.toString());
        return res;
        
    }
    
    @GetMapping("/--category")
    public ArrayList<ListValueCategoryMapHTTP> getListCategory(HttpServletRequest request,
            @Valid @RequestParam JudicialRecordsParamsDto params,
                             @RequestParam(value = "loaiDanhMuc", required = true) String infoType){
        return civilStatusJusticeService.getListCategory(infoType,params);
    }
    
    @PostMapping("/--get-category-qni")
    public CivilStatusJusticeGetCategoryDto getCategoryGet(HttpServletRequest request, @Valid @RequestParam CivilStatusJusticeParamsDto params,@Valid @RequestBody BodyCategoryMapHTTP body)throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        CivilStatusJusticeGetCategoryDto res = civilStatusJusticeService.getCategoryGet(params,body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
        
    }
    
    @PostMapping("/--send-qni")
    public CivilStatusJusticeResponse sendCriminalRecordGet(
        HttpServletRequest request,
        @Valid @RequestParam CivilStatusJusticeParamsDto params,
        @Valid @RequestBody Map<String, Object> body,
        @RequestParam(value = "log-id", required = false) ObjectId logID,
        @RequestParam(value = "code", required = false) String code
    )throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        CivilStatusJusticeResponse res = civilStatusJusticeService.writeLogHTTP(request,params,body,code,logID,1);
        logger.info("DIGO-Response: " + res.toString());
        return res;
        
    }
    
    @GetMapping("/--token-lgsp")
    public CivilStatusJusticeTokenDto getTokenLGSP(HttpServletRequest request,
            @Valid @RequestParam CivilStatusJusticeParamsDto params){
        return civilStatusJusticeService.getTokenLGSP(params);
    }

    @PostMapping("/--get-dossier-status")
    public CivilDossierStatusResDto.InfoStatus getDossierStatus(HttpServletRequest request, @Valid @RequestBody CivilDossierStatusDto obj){
        return civilStatusJusticeService.getDossierStatus(obj);
    }

    @PostMapping("/--get-list-status-from-lgsp")
    public List<CivilDossierStatusResDto.InfoStatus> getListStatusFromLGSP(HttpServletRequest request, @Valid @RequestBody CivilDossierStatusDto obj){
        return civilStatusJusticeService.getListStatusFromLGSP(obj);
    }

}
