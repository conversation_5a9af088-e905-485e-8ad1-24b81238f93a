package vn.vnpt.digo.adapter.api;

import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.IntegrationParamsDto;
import vn.vnpt.digo.adapter.dto.zalo.ZaloSendMessageKTMDto;
import vn.vnpt.digo.adapter.service.ZaloKTMService;
import vn.vnpt.digo.adapter.service.ZaloQBHService;

import javax.validation.Valid;
import java.text.ParseException;

@RestController
@RequestMapping("/webhookQBH")
@IcodeAuthorize("vnpt.permission.zalo-qbh")
public class ZaloQBHController {

    @Autowired
    private ZaloQBHService zaloQBHService;

    Logger logger = LoggerFactory.getLogger(ZaloV2Controller.class);

    @PostMapping
    public ResponseEntity<String> print(@RequestBody String requestBody, @Valid @RequestParam IntegrationParamsDto params) throws JSONException, ParseException {
        System.out.println("webhook for QBH" + requestBody);
        var gson = new Gson();
        var zalo = gson.fromJson(requestBody, ZaloSendMessageKTMDto.class);
        String userId = zalo.sender.id;
        String trimMessage = zalo.message.text.trim().toUpperCase();
        if(trimMessage.contains("H46")){
            String result = zaloQBHService.postDossierByCode(userId, zalo.message.text, params);
        }else {
            logger.info("DIGO-Info: " + "không phải mã hồ sơ");
        }
        return new ResponseEntity<String>(requestBody, HttpStatus.OK);
    }
}
