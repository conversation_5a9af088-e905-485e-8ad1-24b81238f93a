/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.api;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.DVCQGSyncResponseDto;
import vn.vnpt.digo.adapter.dto.PadPResDto;
import vn.vnpt.digo.adapter.pojo.SynthesisReport;
import vn.vnpt.digo.adapter.service.DVCQGService;

/**
 * <AUTHOR>
 */
@RestController
@IcodeAuthorize("vnpt.permission.dvcqg")
@RequestMapping("/dvcqg")
public class DVCQGController {
    @Autowired
    private DVCQGService dvcQGService;

    Logger logger = LoggerFactory.getLogger(DVCQGController.class);

    @PutMapping(value = "/--sync-synthesis-report")
    public void syncSynthesisReport(
            HttpServletRequest request,
            @RequestBody SynthesisReport synthesisReport
    ) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        dvcQGService.syncSynthesisReport(synthesisReport);
    }
    
    @PutMapping(value = "/--sync-synthesis-report-result")
    public DVCQGSyncResponseDto syncSynthesisReportValue(
            HttpServletRequest request,
            @RequestBody SynthesisReport synthesisReport
    ) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return dvcQGService.syncSynthesisReportValue(synthesisReport);
    }

}
