/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import java.io.File;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.IpccGetRecordParamsDto;
import vn.vnpt.digo.adapter.service.IpccVoiceService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ipcc-voice")
@IcodeAuthorize("vnpt.permission.ipcc-voice")
public class IpccVoiceController {
    
    @Autowired
    private IpccVoiceService ipccVoiceService;
    
    Logger logger = LoggerFactory.getLogger(IpccVoiceController.class);
    
    @GetMapping("/--get-record")
    public IdDto getRecord(HttpServletRequest request, @Valid @RequestParam IpccGetRecordParamsDto params) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        IdDto res = ipccVoiceService.getRecord(params);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    @GetMapping("/--get-record-file")
    public byte[] getRecordFile(HttpServletRequest request, @Valid @RequestParam IpccGetRecordParamsDto params) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        byte[] res = ipccVoiceService.getRecordFile(params);
//        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
}