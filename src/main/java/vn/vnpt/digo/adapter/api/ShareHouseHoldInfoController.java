
package vn.vnpt.digo.adapter.api;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.ShareHouseHoldInfoRequestDto;
import vn.vnpt.digo.adapter.dto.ShareHouseHoldInfoResponseDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import vn.vnpt.digo.adapter.pojo.Permission;
import vn.vnpt.digo.adapter.service.*;
import vn.vnpt.digo.adapter.util.Context;
import vn.vnpt.digo.adapter.util.Translator;

import java.util.Objects;

@RestController
@RequestMapping("/family")
@IcodeAuthorize("vnpt.permission.family")
public class ShareHouseHoldInfoController {

    Logger logger = LoggerFactory.getLogger(ShareHouseHoldInfoController.class);

    @Autowired
    private ShareHouseHoldInfoService shareHouseHoldInfoService;

    @Autowired
    private CSDLDCService csdldcService;

    @Autowired
    private Translator translator;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Autowired
    private DailyLogRequestsService dailyLogRequestsService;

    @Autowired
    private UserRequestCountsByDateService userRequestCountsByDateService;

    @Autowired
    private AlertQuotaService alertQuotaService;

    @Value("${vnpt.permission.integrated.scope}")
    private String scope;


    @GetMapping("/--info")
    public ShareHouseHoldInfoResponseDto getCitizenInfo(HttpServletRequest request,
                                                        @Valid @RequestParam ShareHouseHoldInfoRequestDto req) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        IntegratedConfigurationDto config;
        if (scope != null && !scope.isEmpty()) {
            Permission permission = Context.getPermission(scope);
            if (permission == null) {
                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
            }
        }
        if (Objects.nonNull(req.getConfigId())) {
            config = configurationService.getConfig(req.getConfigId());
        } else {
            config = configurationService.getConfig(req.getAgencyId(), req.getSubsystemId(), ShareHouseHoldInfoService.serviceId);
        }

        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }

        if(Objects.nonNull(config) && Objects.nonNull(config.getEncryptedFieldList())){
            config.setParameters(configurationService.decodeParameters(config.getParameters(), config.getEncryptedFieldList(),false));
        }
        String username = Context.getJwtParameterValue("preferred_username");
        IntegratedConfigurationDto configCSDLDC = configurationService.getConfig(null, new ObjectId("5f7c16069abb62f511880003"), new ObjectId("5f7c16069abb62f511890034"));
        if(Objects.nonNull(configCSDLDC)) {
            Integer dailyRequestLimit = configCSDLDC.getParameterValue("dailyRequestLimit") != null ? configCSDLDC.getParametersValue("dailyRequestLimit") : 9000;
            Integer userRequestLimit = configCSDLDC.getParametersValue("userRequestLimit") != null ? configCSDLDC.getParametersValue("userRequestLimit") : 200;
            // cấu hình quota captcha
            Boolean quotaCaptchaEnable = configCSDLDC.getParametersValue("quotaCaptchaEnable") != null && "true".equals(configCSDLDC.getParametersValue("quotaCaptchaEnable").toString()) ? true : false;
            int quotaTimeCallCaptcha = configCSDLDC.getParametersValue("quotaTimeCallCaptcha") != null ? configCSDLDC.getParametersValue("quotaTimeCallCaptcha") : 30;
            int quotaLimitCaptcha = configCSDLDC.getParametersValue("quotaLimitCaptcha") != null ? configCSDLDC.getParametersValue("quotaLimitCaptcha") : 1;
            int dossierRequestLimit = configCSDLDC.getParametersValue("dossierRequestLimit")!= null?configCSDLDC.getParametersValue("dossierRequestLimit"):50;

            vn.vnpt.digo.adapter.pojo.Permission permission = Context.getPermission("admin");
            // có quyền admin thì bỏ qua check
            if (permission == null) {
                // check username
                String userId = Context.getJwtParameterValue("user_id");
                if (Objects.isNull(userId)) {
                    throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
                }
                // Kiểm tra thời gian 2 lần gọi captcha
                if (quotaCaptchaEnable && !alertQuotaService.alertQuota(username, "getCitizenInfoCapcha", quotaTimeCallCaptcha, quotaLimitCaptcha)) {
//                csdldcService.createLogV2(req, config.getParametersValue("type"), username, true, true);
                    throw new DigoHttpException(10444, HttpServletResponse.SC_BAD_REQUEST);
                } else if (!userRequestCountsByDateService.checkUserRequest(username, userRequestLimit, 1)) {
                    // Hết lượt truy vấn theo ngày của cán bộ
                    throw new DigoHttpException(10442, HttpServletResponse.SC_BAD_REQUEST);
                } else if (!dailyLogRequestsService.checkDailyLogRequests(dailyRequestLimit, 1)) {
                    // Hết lượt truy vấn theo ngày của site
                    throw new DigoHttpException(10443, HttpServletResponse.SC_BAD_REQUEST);
                } else if(req.isEnableLogCSDLDCBDH()){
                    if(!csdldcService.checkDossierRequest(req.getDossierId(), req.getDossierCode(), dossierRequestLimit)) {
                       throw new DigoHttpException(10444, HttpServletResponse.SC_BAD_REQUEST);
                   }
               }
                // update lại giá trị cho các collection
                Boolean updateUserRequest = userRequestCountsByDateService.updateUserRequest(username, userRequestLimit, 1);
                Boolean dailyLogRequests = dailyLogRequestsService.updateDailyLogRequests(dailyRequestLimit, 1);
            }
        }

        ShareHouseHoldInfoResponseDto res = shareHouseHoldInfoService.getCitizenInfo(req,config);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }



}
