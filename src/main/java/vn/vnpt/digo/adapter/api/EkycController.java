package vn.vnpt.digo.adapter.api;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.EkycMapDto;
import vn.vnpt.digo.adapter.dto.EkycResponseDto;
import vn.vnpt.digo.adapter.dto.EkycTranslateRequestDto;
import vn.vnpt.digo.adapter.service.EkycService;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ekyc")
@IcodeAuthorize("vnpt.permission.ekyc")
public class EkycController {

    @Autowired
    private EkycService ekycService;

    Logger logger = LoggerFactory.getLogger(OcrController.class);

    @PostMapping(value = "/--translate")
    public EkycResponseDto translate(@RequestBody EkycTranslateRequestDto req) {
        EkycResponseDto data = ekycService.translate(req);
        return data;
    }

    @GetMapping(value = "/--form")
    public List<EkycMapDto> form(@RequestParam(value = "id") String id) {
        return ekycService.getFormById(new ObjectId(id));
    }
}
