package vn.vnpt.digo.adapter.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.EReceiptBodyDto;
import vn.vnpt.digo.adapter.dto.hcm_vietinfo_ereceipt.CanelEReceiptBodyDto;
import vn.vnpt.digo.adapter.dto.hcm_vietinfo_ereceipt.EReceiptVietInfoDto;
import vn.vnpt.digo.adapter.dto.hcm_vietinfo_ereceipt.IssueEReceiptBodyDto;
import vn.vnpt.digo.adapter.dto.hcm_vietinfo_ereceipt.ViewEReceiptBodyDto;
import vn.vnpt.digo.adapter.service.HCMVietInfoEReceiptService;

import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

@RestController
@RequestMapping("/hcm-vietinfo-receipt")
@IcodeAuthorize("vnpt.permission.hcm-vietinfo-receipt")
public class HCMVietInfoEReceiptController {

    @Autowired
    private HCMVietInfoEReceiptService hcmVietInfoEReceiptService;

    @PostMapping("/preview")
    public ResponseEntity<byte[]> previewEReceipt( @RequestBody IssueEReceiptBodyDto body) throws NoSuchAlgorithmException, KeyManagementException {
        EReceiptVietInfoDto receiptVietInfo =  hcmVietInfoEReceiptService.previewEReceipt(body);
        byte[] file = Base64.getDecoder().decode(receiptVietInfo.getBase64());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentDispositionFormData( "attachment",receiptVietInfo.getFileName());
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.add("Pragma", "no-cache");
        headers.add("Cache-Control", "no-cache");

        return ResponseEntity.ok().headers(headers).body(file);
    }

    @PostMapping("/getListEReceipt")
    public AffectedRowsDto getListEReceipt(@Validated @RequestBody IssueEReceiptBodyDto body){
        return null;
    }

    @PostMapping("/issue")
    public ResponseEntity<byte[]> issueEReceipt(@Validated @RequestBody IssueEReceiptBodyDto body) throws NoSuchAlgorithmException, KeyManagementException {
        EReceiptVietInfoDto receiptVietInfo =  hcmVietInfoEReceiptService.issueEReceipt(body);
        byte[] file = Base64.getDecoder().decode(receiptVietInfo.getBase64());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentDispositionFormData( "attachment",receiptVietInfo.getFileName());
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.add("Pragma", "no-cache");
        headers.add("Cache-Control", "no-cache");

        return ResponseEntity.ok().headers(headers).body(file);
    }

    @PostMapping("/payment")
    public AffectedRowsDto paymentEReceipt(@Validated @RequestBody IssueEReceiptBodyDto body){
        return null;
    }

    @PostMapping("/view")
    public ResponseEntity<byte[]> viewEReceipt(@Validated @RequestBody ViewEReceiptBodyDto body){

        EReceiptVietInfoDto receiptVietInfo =  hcmVietInfoEReceiptService.viewEReceipt(body);
        byte[] file = Base64.getDecoder().decode(receiptVietInfo.getBase64());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentDispositionFormData( "attachment",receiptVietInfo.getFileName());
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.add("Pragma", "no-cache");
        headers.add("Cache-Control", "no-cache");

        return ResponseEntity.ok().headers(headers).body(file);
    }

    @PostMapping("/cancel")
    public AffectedRowsDto cancelEReceipt(@Validated @RequestBody CanelEReceiptBodyDto body){
        return hcmVietInfoEReceiptService.cancelEReceipt(body);
    }

}
