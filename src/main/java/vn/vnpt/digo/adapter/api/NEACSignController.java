/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import com.itextpdf.text.DocumentException;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.NEACSignRequestDto;
import vn.vnpt.digo.adapter.dto.NEACCertRequestDto;
import vn.vnpt.digo.adapter.dto.sign_neac.NEACSignBase64RequestDto;
import vn.vnpt.digo.adapter.dto.vnptsmartca.SmartCADigitalSignatureWithBase64Dto;
import vn.vnpt.digo.adapter.service.NEACSignService;

/**
 *
 * <AUTHOR>
 */
@RestController 
@RequestMapping("/neac-sign")
@IcodeAuthorize("vnpt.permission.neac-sign")
public class NEACSignController {
    @Autowired
    private NEACSignService service;

    Logger logger = LoggerFactory.getLogger(NEACSignController.class);
    
    //Lay danh sach chung thu cua nguoi dung
    @PostMapping("/--get-certificate")
    public Object getCertificate(HttpServletRequest request, @Valid @RequestBody NEACCertRequestDto body) throws IOException, NoSuchAlgorithmException, KeyStoreException, CertificateException, UnrecoverableKeyException, KeyManagementException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        logger.info("DIGO-Body: " + body.toString());
        JSONObject res = service.getCertificate(body);
        logger.info("DIGO-Response: " + res.toString());
        return new ResponseEntity<>(res.toMap(), HttpStatus.OK);
    }
    
    //Yeu cau ky so
    @PostMapping("/--sign")
    public Object signR(HttpServletRequest request, @Valid @RequestBody NEACSignRequestDto body) throws GeneralSecurityException, CertificateException, IOException, DocumentException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        logger.info("DIGO-Body: " + body.toString());
        Object res = service.sign(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    // dung cho svc.storage
    @PostMapping(value = "/--sign-with-base64")
    public ResponseEntity<String> signSmartCAWithBase64(HttpServletRequest request, @Validated @RequestBody NEACSignBase64RequestDto body) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        return ResponseEntity.ok(service.signNEACWithBase64(body));
    }
}
