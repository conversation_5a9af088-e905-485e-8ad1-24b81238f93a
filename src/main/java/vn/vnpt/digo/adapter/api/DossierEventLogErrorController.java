package vn.vnpt.digo.adapter.api;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.document.DossierEventLogError;
import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.service.IntegratedEventService;
import vn.vnpt.digo.adapter.dto.GetDossierDetailDto;
import vn.vnpt.digo.adapter.dto.InputFileDto;
import vn.vnpt.digo.adapter.service.DossierEventLogErrorService;

@RestController
@RequestMapping("/dossier-event-log-error")
@IcodeAuthorize("vnpt.permission.dossier-event-log-error")
public class DossierEventLogErrorController {
    
    @Autowired
    private DossierEventLogErrorService dossierEventLogErrorService;

    Logger logger = LoggerFactory.getLogger(DossierEventLogErrorController.class);

//    @GetMapping()
//    public String changeState(
//            HttpServletRequest request) {
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if(EventProperties.DOSSIER_EVENT_AVAILABLE){
//            EventProperties.DOSSIER_EVENT_AVAILABLE = false;
//        } else {
//            EventProperties.DOSSIER_EVENT_AVAILABLE = true;
//        }
//        
//        return "Done!!!";
//    }
    
    @GetMapping()
    //@IcodeAuthorize(permission = "manageSector")
    public ResponseEntity<Object> getSectors(HttpServletRequest request,
            @RequestParam(value = "keyword", defaultValue = "") String keyword,
            @RequestParam(value = "update-status", required = false) Integer updateStatus,
            @RequestParam(value = "spec", defaultValue = "slice") String spec,
            @RequestParam(value = "agency-id", required = false) String agencyId,
            @RequestParam(value = "not-agency-id", required = false) String notAgencyId,
            @RequestParam(value = "all-agency", required = false) Integer allAgency,
            @RequestParam(value = "only-agency-id", defaultValue = "0") Integer onlyAgencyId,
            @RequestParam(value = "related-token", required = false) Boolean relatedToken,
            Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        Slice<DossierEventLogError> result = dossierEventLogErrorService.search(keyword, updateStatus, spec, pageable);

        logger.info("DIGO-Response: Size = " + result.getSize());
        return ResponseEntity.ok(result);
    }
    
    @PutMapping(value = "/updateEvent", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public AffectedRowsDto updateCountProcessProcedure() throws ParseException {
        return dossierEventLogErrorService.updateEventKM();
    }
}
