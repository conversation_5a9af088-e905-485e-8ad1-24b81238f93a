//<PERSON><PERSON><PERSON> bổ sung
package vn.vnpt.digo.adapter.api;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.*;
import vn.vnpt.digo.adapter.pojo.CoopDossierDetailDto;
import vn.vnpt.digo.adapter.pojo.HouseDossierDetailDto;
import vn.vnpt.digo.adapter.service.LgspKGGService;

import java.util.List;

@RestController
@RequestMapping("/lgsp-kgg")
@IcodeAuthorize("vnpt.permission.lgsp-kgg")
public class LgspKGGController {

    @Autowired
    private LgspKGGService lgspKGGService;

    Logger logger = LoggerFactory.getLogger(BudgetController.class);

    @GetMapping("/--get-ent-kgg")
    public EntInfoDetailDto getEntInfoKggDto(HttpServletRequest request, @Valid @RequestParam BusinessRegistrationParamsKGGDto params)throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        EntInfoDetailDto res = lgspKGGService.getEntInfoKggDto(params);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @GetMapping("/--get-coop-kgg")
    public CoopInfoDetailDto getCoopInfoKggDto(HttpServletRequest request, @Valid @RequestParam BusinessRegistrationParamsKGGDto params)throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        CoopInfoDetailDto res = lgspKGGService.getCoopInfoDetailDto(params);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @GetMapping("/--get-house-kgg")
    public HouseInfoDetailDto getHouseInfoKggDto(HttpServletRequest request, @Valid @RequestParam BusinessRegistrationParamsKGGDto params)throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        HouseInfoDetailDto res = lgspKGGService.getHouseInfoDetailDto(params);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @GetMapping("/--get-ent-dossiers-kgg")
    public Page<EntDossierDetailDto> getEntDossiersKggDto(HttpServletRequest request, @Valid @RequestParam BusinessRegistrationParamsKGGDto params,
                                                           Pageable pageable)throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        Page<EntDossierDetailDto> res = lgspKGGService.getEntDossiersKggDto(params, pageable);
        logger.info("DIGO-Response: Size = " + res.getSize());
        return res;
    }

    @GetMapping("/--get-coop-dossiers-kgg")
    public Page<CoopDossierDetailDto> getCoopDossiersKggDto(HttpServletRequest request, @Valid @RequestParam BusinessRegistrationParamsKGGDto params,
                                                            Pageable pageable)throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        Page<CoopDossierDetailDto> res = lgspKGGService.getCoopDossiersKggDto(params, pageable);
        logger.info("DIGO-Response: Size = " + res.getSize());
        return res;
    }

    @GetMapping("/--get-house-dossiers-kgg")
    public Page<HouseDossierDetailDto> getHouseDossiersKggDto(HttpServletRequest request, @Valid @RequestParam BusinessRegistrationParamsKGGDto params,
                                                              Pageable pageable)throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        Page<HouseDossierDetailDto> res = lgspKGGService.getHouseDossiersKggDto(params, pageable);
        logger.info("DIGO-Response: Size = " + res.getSize());
        return res;
    }
}
