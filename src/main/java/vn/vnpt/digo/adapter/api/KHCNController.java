package vn.vnpt.digo.adapter.api;

import com.mongodb.util.JSON;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.khcn.*;
import vn.vnpt.digo.adapter.service.KHCNService;
import vn.vnpt.digo.adapter.util.StringHelper;
import vn.vnpt.digo.adapter.util.Translator;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/api/lienthongKHCN")
@IcodeAuthorize("vnpt.permission.khcn")
@Validated
public class KHCNController {

    @Autowired
    private KHCNService khcnService;

    @Autowired
    private Translator translator;

    private static final Logger logger = LoggerFactory.getLogger(KHCNController.class);

    @Value("${digo.khcn.security-key:default-khcn-key}")
    private String securityKeyIgate;

    /**
     * API nhận hồ sơ KHCN
     */
    @PostMapping(value = "/nhanHoSoKHCN", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> nhanHoSoKHCN(
            HttpServletRequest request,
            @RequestHeader(value = "securityKey", required = true) String securityKey,
            @RequestBody List<@Valid KHCNDossierDataDto> req) throws Exception {
        
        String requestPath = request.getMethod() + " " + request.getRequestURI() + 
                           (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("KHCN-Request: " + requestPath);
        
        // Xác thực security key
        String key = StringHelper.toHmacSha256(securityKeyIgate, req.get(0).getNationCode());
        if (!key.equals(securityKey)) {
            Object ob = JSON.parse("{'401':'Unauthorized'}");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ob);
        }
        
        KHCNResultDto res = khcnService.dongBoHoSo(req, securityKey, requestPath);
        logger.info("KHCN-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    /**
     * API cập nhật trạng thái hồ sơ KHCN
     */
    @PostMapping(value = "/capNhatTrangThaiHoSoKHCN", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> capNhatTrangThaiHoSoKHCN(
            HttpServletRequest request,
            @RequestHeader(value = "securityKey", required = true) String securityKey,
            @RequestBody List<@Valid KHCNDossierTrackingDataDto> req) throws Exception {
        
        String requestPath = request.getMethod() + " " + request.getRequestURI() + 
                           (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("KHCN-Request: " + requestPath);
        
        // Xác thực security key
        String key = StringHelper.toHmacSha256(securityKeyIgate, req.get(0).getNationCode());
        if (!key.equals(securityKey)) {
            Object ob = JSON.parse("{'401':'Unauthorized'}");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ob);
        }
        
        KHCNResultDto res = khcnService.capNhatTienDoHoSo(req, securityKey, requestPath);
        logger.info("KHCN-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    /**
     * API nhận hồ sơ KHCN (tương tự receiveRecord của DVCLT)
     */
    @PostMapping(value = "/receiveRecord", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> receiveRecord(
            HttpServletRequest request,
            @RequestParam(value = "kafkaEnable", required = false) String kafkaEnable,
            @RequestHeader(value = "securityKey", required = true) String securityKey,
            @RequestBody @Valid KHCNHoSoRequestDto req) throws Exception {
        
        String requestPath = request.getMethod() + " " + request.getRequestURI() + 
                           (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("KHCN-Request: " + requestPath);
        
        try {
            khcnService.bodyLogsHTTP(req);
        } catch (Exception e) {
            logger.warn("Error saving body logs: " + e.getMessage());
        }
        
        // Xác thực security key
        String key = StringHelper.toHmacSha256(securityKeyIgate, req.getCode());
        if (!key.equals(securityKey)) {
            Object ob = JSON.parse("{'401':'Unauthorized'}");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ob);
        }
        
        KHCNHoSoResponseDto res = khcnService.nhanHoSoKHCN(req, securityKey, requestPath, kafkaEnable);
        logger.info("KHCN-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    /**
     * API lấy log KHCN
     */
    @GetMapping(value = "/getLog")
    public ResponseEntity<Object> getLog(
            HttpServletRequest request,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "nationCode", required = false) String nationCode,
            @RequestParam(value = "api", required = false) String api,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "error", required = false) Boolean error,
            Pageable pageable) throws Exception {
        
        String requestPath = request.getMethod() + " " + request.getRequestURI() + 
                           (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("KHCN-Request: " + requestPath);
        
        return ResponseEntity.ok(khcnService.getLog(code, nationCode, api, status, error, pageable));
    }

    /**
     * API lấy log KHCN theo ID
     */
    @GetMapping(value = "/getLog/{id}")
    public ResponseEntity<Object> getLogFull(
            HttpServletRequest request,
            @PathVariable(value = "id", required = true) ObjectId id) throws Exception {
        
        String requestPath = request.getMethod() + " " + request.getRequestURI() + 
                           (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("KHCN-Request: " + requestPath);
        
        return ResponseEntity.ok(khcnService.getLogFull(id));
    }

    /**
     * API cập nhật trạng thái hồ sơ KHCN
     */
    @PostMapping(value = "/capNhatTrangThaiHoSoKHCNPost", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> capNhatTrangThaiHoSoKHCNPost(
            HttpServletRequest request,
            @RequestBody KHCNPostDto req) throws Exception {

        String requestPath = request.getMethod() + " " + request.getRequestURI() +
                           (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("KHCN-Request: " + requestPath);

        return ResponseEntity.ok(khcnService.capNhatTrangThaiHoSoKHCN(req, requestPath));
    }

    /**
     * API xử lý hồ sơ lỗi KHCN
     */
    @PostMapping(value = "/nhanHoSoKHCNLoi", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> nhanHoSoKHCNLoi(
            HttpServletRequest request,
            @RequestBody ArrayList<String> listCode) throws Exception {

        String requestPath = request.getMethod() + " " + request.getRequestURI() +
                           (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("KHCN-Request: " + requestPath);

        KHCNResultDto res = khcnService.dongBoHoSoLoi(listCode);
        logger.info("KHCN-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    /**
     * API xử lý cập nhật trạng thái lỗi KHCN
     */
    @PostMapping(value = "/capNhatTrangThaiHoSoKHCNLoi", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> capNhatTrangThaiHoSoKHCNLoi(
            HttpServletRequest request,
            @RequestBody ArrayList<String> listCode) throws Exception {

        String requestPath = request.getMethod() + " " + request.getRequestURI() +
                           (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("KHCN-Request: " + requestPath);

        KHCNResultDto res = khcnService.capNhatTienDoHoSoLoi(listCode);
        logger.info("KHCN-Response: " + res.toString());
        return ResponseEntity.ok(res);
    }

    /**
     * API lấy body data KHCN
     */
    @PostMapping("/--getLogBodyKhcns")
    public Object getLogBodyKhcns(
            HttpServletRequest request,
            @RequestParam(value = "code", required = false) String code) {

        String requestPath = request.getMethod() + " " + request.getRequestURI() +
                           (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("KHCN-Info: " + requestPath);

        return khcnService.getDataBodyKHCN(code);
    }

    /**
     * Exception handler cho ConstraintViolationException
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({ConstraintViolationException.class, Exception.class})
    public Object handleValidationExceptions(ConstraintViolationException ex, HttpServletRequest request) {
        String requestPath = request.getRequestURI();
        
        List<String> errors = new ArrayList<>();
        List<String> dateErrors = new ArrayList<>();
        
        for (ConstraintViolation constraintViolation : ex.getConstraintViolations()) {
            if (constraintViolation.getMessage().contains("Ngay") || 
                constraintViolation.getMessage().contains("ThoiDiemXuLy")) {
                dateErrors.add(constraintViolation.getMessage());
            } else {
                errors.add(constraintViolation.getMessage());
            }
        }
        
        if (Objects.equals(requestPath, "/api/lienthongKHCN/receiveRecord")) {
            if (dateErrors.size() > 0) {
                KHCNHoSoResponseDto errorMessage = new KHCNHoSoResponseDto("400", 
                    translator.toLocale("lang.word.khcn-invalid-input", "Dữ liệu đầu vào không hợp lệ"),
                    "400",
                    translator.toLocale("lang.word.tag", "Trường") + "<<" + String.join(",", dateErrors).trim() + ">>" + 
                    translator.toLocale("lang.word.khcn-date-error", " có định dạng ngày không đúng"));
                return errorMessage;
            } else {
                KHCNHoSoResponseDto errorMessage = new KHCNHoSoResponseDto("400", 
                    translator.toLocale("lang.word.khcn-invalid-input", "Dữ liệu đầu vào không hợp lệ"),
                    "400",
                    translator.toLocale("lang.word.tag", "Trường") + "<<" + String.join(",", errors).trim() + ">>" + 
                    translator.toLocale("lang.phrase.is-not-valid", " không hợp lệ"));
                return errorMessage;
            }
        } else {
            KHCNResultDto errorMessage;
            if (dateErrors.size() > 0) {
                errorMessage = new KHCNResultDto("0",
                    translator.toLocale("lang.word.tag", "Trường") + "<<" + String.join(",", dateErrors).trim() + ">>" + 
                    translator.toLocale("lang.word.khcn-date-error", " có định dạng ngày không đúng"));
            } else {
                errorMessage = new KHCNResultDto("0",
                    translator.toLocale("lang.word.tag", "Trường") + "<<" + String.join(",", errors).trim() + ">>" + 
                    translator.toLocale("lang.phrase.is-not-valid", " không hợp lệ"));
            }
            return errorMessage;
        }   
    }

    /**
     * Exception handler cho MethodArgumentNotValidException
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public Object handleValidationExceptions(MethodArgumentNotValidException ex, HttpServletRequest request) {
        String requestPath = request.getRequestURI();
        
        if (Objects.equals(requestPath, "/api/lienthongKHCN/receiveRecord")) {
            List<String> errors = new ArrayList<>();
            for (FieldError error : ex.getBindingResult().getFieldErrors()) {
                errors.add(error.getDefaultMessage());
            }
            for (ObjectError error : ex.getBindingResult().getGlobalErrors()) {
                errors.add(error.getDefaultMessage());
            }

            KHCNHoSoResponseDto errorMessage = new KHCNHoSoResponseDto("400", 
                translator.toLocale("lang.word.khcn-invalid-input", "Dữ liệu đầu vào không hợp lệ"),
                "400",
                translator.toLocale("lang.word.tag", "Trường") + " " + String.join(",", errors) + " " + 
                translator.toLocale("lang.phrase.is-not-valid", "không hợp lệ"));

            return errorMessage;
        } else {
            List<String> errors = new ArrayList<>();
            for (FieldError error : ex.getBindingResult().getFieldErrors()) {
                errors.add(error.getDefaultMessage());
            }
            for (ObjectError error : ex.getBindingResult().getGlobalErrors()) {
                errors.add(error.getDefaultMessage());
            }

            KHCNResultDto errorMessage = new KHCNResultDto("0",
                translator.toLocale("lang.word.tag", "Trường") + " " + String.join(",", errors) + " " + 
                translator.toLocale("lang.phrase.is-not-valid", "không hợp lệ"));
            return errorMessage;
        }
    }
}
