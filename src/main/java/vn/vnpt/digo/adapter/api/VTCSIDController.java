package vn.vnpt.digo.adapter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.service.VTCSIDService;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Map;

@RestController
@RequestMapping("/vtcs-id")
@IcodeAuthorize("vnpt.permission.idCheck")
public class VTCSIDController {

    Logger logger = LoggerFactory.getLogger(VTCSIDController.class);

    @Autowired
    VTCSIDService vtcsidService;

    @PostMapping("/validation")
    public ResponseEntity<Object> validation(
            HttpServletRequest request,
            @RequestBody Object requestBody
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        Object response = vtcsidService.validation(requestBody);
        logger.info("DIGO-Response: " + response.toString());

        final HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        return new ResponseEntity<Object>(response, httpHeaders, HttpStatus.OK);
    }

}
