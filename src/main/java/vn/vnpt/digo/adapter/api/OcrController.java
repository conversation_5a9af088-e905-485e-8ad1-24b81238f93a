package vn.vnpt.digo.adapter.api;

import javax.servlet.http.HttpServletRequest;
import javax.websocket.server.PathParam;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.service.OcrService;

import java.io.IOException;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ocr")
@IcodeAuthorize("vnpt.permission.ocr")
public class OcrController {

    @Autowired
    private OcrService ocrService;

    Logger logger = LoggerFactory.getLogger(OcrController.class);

    @PostMapping(value = "/--idg", produces = MediaType.APPLICATION_JSON_VALUE)
    public String scanIdgOcr(
            HttpServletRequest request,
            @RequestParam(value = "file", required = true) MultipartFile file
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return ocrService.idgScan(file, request);
    }

    @PostMapping(value = "/--jka", produces = MediaType.APPLICATION_JSON_VALUE)
    public String scanJkaOcr(
            HttpServletRequest request,
            @RequestParam(value = "file", required = true) MultipartFile files
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return ocrService.jkaScan(files, request);
    }

    @PostMapping("/{key}")
    public Object smartReaderOCR(HttpServletRequest request,
                                 @PathVariable(value = "key", required = true) String key,
                                 @RequestParam(value = "file", required = true) MultipartFile file,
                                 @RequestParam(value = "eform-id", required = false) ObjectId eformId,
                                 @RequestParam(value = "agency-code", required = false) String agencyCode,
                                 @RequestParam(value = "agency-name", required = false) String agencyName,
                                 @RequestParam(value = "agency-subcode", required = false) String agencySubCode,
                                 @RequestParam(value = "agency-subname", required = false) String agencySubName) throws IOException {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return ocrService.smartReaderOCR(file, key, eformId, agencyCode, agencyName, agencySubCode, agencySubName);
    }
}
