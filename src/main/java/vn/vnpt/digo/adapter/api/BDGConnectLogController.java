package vn.vnpt.digo.adapter.api;

import net.minidev.json.JSONObject;
import org.bson.types.ObjectId;
import org.codehaus.jackson.JsonParseException;
import org.codehaus.jackson.map.JsonMappingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.dto.DossierFormFileDto;
import vn.vnpt.digo.adapter.dto.bdg.*;
import vn.vnpt.digo.adapter.service.BDGConnectLogService;
import vn.vnpt.digo.adapter.service.LGSPMinhTueService;
import vn.vnpt.digo.adapter.service.MappingDataService;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import vn.vnpt.digo.adapter.util.Context;

import java.util.HashMap;

@RestController
@RequestMapping("/bdg-connect-log")
@IcodeAuthorize("vnpt.permission.bdg-connect-log")
public class BDGConnectLogController {
    Logger logger = LoggerFactory.getLogger(BDGConnectLogController.class);

    @Autowired
    BDGConnectLogService bdgConnectLogService;

    @Autowired
    private LGSPMinhTueService lgspMinhTueService;

    @Autowired
    private MappingDataService mappingDataService;

    private final String fptTokenGateway = "https://api.binhduong.gov.vn:8687/token";
    private final String fptKey = "iFibF2hYfVVNtFDbuGAr0vRtniUa";
    private final String fptSecret = "zpn1y2EdaSwMSjWTtK0DGF39Lvca";


    //Chỉ để test POSTMAN, vui lòng gọi qua event-listener hoặc kaffa
    @PostMapping("/save")
    public AffectedRowsDto save(HttpServletRequest request, @RequestBody ConnectLogDto body){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        return bdgConnectLogService.saveLog(body);
    }

    @GetMapping("/getBy")
    public Page<ConnectLogDto> getBy(HttpServletRequest request, @ModelAttribute GetLogByDto params, Pageable pageable){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        return bdgConnectLogService.getBy(params, pageable);
    }

    @GetMapping("/getByPeriod")
    public Page<ConnectLogDto> getByPeriod (HttpServletRequest request, @ModelAttribute GetLogByPeriod params, Pageable pageable) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        return bdgConnectLogService.getByPeriod(params, pageable);
    }

    @GetMapping("/getAll")
    public Page<ConnectLogDto> getAll(HttpServletRequest request, Pageable pageable){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        return bdgConnectLogService.getAll(pageable);
    }

    @PostMapping("/--payment")
    public AffectedRowsDto savePayment(HttpServletRequest request, @RequestBody paymentRequestDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        return bdgConnectLogService.saveLogPayment(body);
    }

    @PostMapping("/--post-fpt1gate-expertise")
    public Fpt1GateResponseDto postFpt1GateExpertise(HttpServletRequest request, @RequestBody PostFpt1GateExpertise dataExpertise) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        ObjectId deploymentId = Context.getDeploymentId();
//        ObjectId deploymentId = new ObjectId("6194504d6147b84e3d0c1624");

        String token = lgspMinhTueService.getToken(fptTokenGateway, fptKey, fptSecret).getAccessToken();
        logger.info("Token Fpt1Gate: " + token);
        JSONObject json = new JSONObject(bdgConnectLogService.transferExpertiseDataToHashmap(dataExpertise, deploymentId));
        logger.info("ExpertiseDossier body:" + json);
        Fpt1GateResponseDto response = bdgConnectLogService.sendExpertiseDossier(token, json, dataExpertise.getDossierOnlineApplyId());
        if (response != null && response.getStatusCode() == 200 && "OK".equals(response.getStatus()) && dataExpertise.getDossierFormFile().size() > 0) {
            for (DossierFormFileDto dossierFile :
                    dataExpertise.getDossierFormFile()) {
                bdgConnectLogService.sendAttachFile(token, dataExpertise.getDossierOnlineApplyId(), dossierFile);
            }
        }
        return response;
    }

    @PostMapping("/--post-fpt1gate-payment")
    public PostFpt1GatePayment postFpt1GatePayment(HttpServletRequest request,
                                     @RequestParam(value = "dossier-online-apply-id", required = true) String dossierOnlineApplyId,
                                     @RequestParam(value = "dossier-id", required = true) ObjectId dossierId) throws JsonParseException, JsonMappingException, IOException {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        String token = lgspMinhTueService.getToken(fptTokenGateway, fptKey, fptSecret).getAccessToken();
        logger.info("Token Fpt1Gate: " + token);
        // JSONObject json = new JSONObject(body);
        // logger.info("ExpertiseDossier body:" + json);
        return bdgConnectLogService.sendPaymentDossier(token, dossierOnlineApplyId, dossierId);
    }

    @PostMapping("/--save-auto-sync")
    public AffectedRowsDto saveAutoSync(HttpServletRequest request, @RequestBody ConnectLogDto body){
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        return bdgConnectLogService.saveLogAutoSync(body);
    }
}
