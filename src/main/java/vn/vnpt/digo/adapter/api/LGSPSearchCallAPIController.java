/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;

import vn.vnpt.digo.adapter.dto.GettingConfigurationParamsDto;
import org.json.*;  
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.service.IntegratedConfigurationService;
import vn.vnpt.digo.adapter.util.Translator;
/*
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lgsp-msns")
@IcodeAuthorize("vnpt.permission.budget")
//@IcodeAuthorize("vnpt.permission.mappingdata")
public class LGSPSearchCallAPIController {
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private Translator translator;
    
//    @Value("${lgsp.hcm.token}")
//    private String lgspaccesstoken;
    
//    @Value(value = "${digo.lgsphcm.configid}")
//    private String configMsns;

    @Autowired
    private IntegratedConfigurationService configurationService;
    
//    private ObjectId configid = new ObjectId("${digo.lgsphcm.configMsns}");
    
    @Value(value = "${digo.lgsphcm.configid}")
    private String configid;
    
    public String HTTP_GET(String url, boolean authorization, String sercet, String lgspaccesstoken) throws UnsupportedEncodingException {
		HttpClient httpClient = HttpClientBuilder.create().build();
		HttpGet httpGet = new HttpGet(url);
		if (authorization) {
			httpGet.setHeader("Authorization", sercet);
                        httpGet.setHeader("lgspaccesstoken", lgspaccesstoken);
		}
		String responseString = "";
		try {
			HttpResponse response = httpClient.execute(httpGet);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				responseString = EntityUtils.toString(entity, "UTF-8");
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		System.out.println(responseString);
		return responseString;
    }

    public String getTokenLGSPHCM(String uri,
                                String getToken,
                                String accessKey,
                                String secretKey,
                                String appName,
                                String partnerCode,
                                String partnerCodeCus,
                                String lgspaccesstoken){
        String token = null;
        //apiLGSP url = new apiLGSP();
        String URL = uri + getToken;
//        String temp = "{\n" +
//                        "\"AccessKey\":\""+ accessKey +"\",\n" +
//                        "\"SecretKey\":\""+ secretKey +"\",\n" +
//                        "\"AppName\":\""+ appName +"\",\n" +
//                        "\"PartnerCode\":\""+ partnerCode +"\",\n" +
//                        "\"PartnerCodeCus\":\""+ partnerCodeCus +"\"\n" +
//                        "}";
//        String lgspaccesstoken = StringHelper.toBase64Encode(temp);

        HttpHeaders headers = new HttpHeaders();
        headers.set("lgspaccesstoken", lgspaccesstoken);
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> map= new LinkedMultiValueMap<String, String>();
        map.add("grant_type", "client_credentials");
        org.springframework.http.HttpEntity<MultiValueMap<String, String>> entity = new org.springframework.http.HttpEntity<MultiValueMap<String, String>>(map, headers);

        JSONObject response = new JSONObject(restTemplate.exchange(URL, HttpMethod.POST, entity, String.class).getBody());
        token = response.getString("access_token");
        return token;
    }
    @GetMapping("/--get-msnstn")
    public String GetDanhSachHoSoTheoNgay(
            HttpServletRequest request, 
            @Valid @RequestParam GettingConfigurationParamsDto config,
            @RequestParam (value = "tuNgay", required = true) String tuNgay, 
            @RequestParam (value = "denNgay", required = true) String denNgay) {
        boolean authorization = true;
        //IntegratedConfigurationDto configMSNS = configurationService.getConfig(new ObjectId(configid));
        //IntegratedConfigurationDto configMSNS;
//        if (Objects.nonNull(config.getConfigId())) {
//            configMSNS = configurationService.getConfig(config.getConfigId());
//        } else {
//            configMSNS = configurationService.getConfig(config.getAgencyId(), config.getSubsystemId(), this.configid);
//        }
        IntegratedConfigurationDto configMSNS;
        configMSNS = configurationService.getConfig(new ObjectId(configid));
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String uri = configMSNS.getParameterValue("adapter");
        String getToken = configMSNS.getParameterValue("getToken");
        String accessKey = configMSNS.getParametersValue("accessKey");
        String secretKey = configMSNS.getParametersValue("secretKey");
        String appName = configMSNS.getParametersValue("appName");
        String partnerCode = configMSNS.getParametersValue("partnerCode");
        String partnerCodeCus = configMSNS.getParametersValue("partnerCodeCus");
        String getDanhSachHoSoTheoNgay = configMSNS.getParametersValue("getDanhSachHoSoTheoNgay");
        String lgspaccesstoken = configMSNS.getParametersValue("authorLGSP");
        //String lgspaccesstoken = "ewoiQWNjZXNzS2V5IjoiNTc4OGFhYmNlNGIwODM2ZGVmMzY3YTI3IiwKIlNlY3JldEtleSI6IkE4OGpzRjFLMUZ2Z1djZjl2V2IzMGVDQno4NFVVaXdSVlNLZEppeUc3diIsCiJBcHBOYW1lIjogInZucHRoY20iLAoiUGFydG5lckNvZGUiOiAiMDAwLjAwLjE1LkgyOSIsCiJQYXJ0bmVyQ29kZUN1cyI6ICIwMDAuMDAuMTUuSDI5Igp9";
        String token = getTokenLGSPHCM(uri, getToken, accessKey, secretKey, appName, partnerCode, partnerCodeCus, lgspaccesstoken);
        String sceret = "Bearer " + token;
        String urlGetDanhSachHoSoTheoNgay = String.format(uri + getDanhSachHoSoTheoNgay, tuNgay, denNgay);
        String res = null;
        JSONArray json = null;
        try{
            res = HTTP_GET(urlGetDanhSachHoSoTheoNgay, authorization, sceret, lgspaccesstoken);
            if(res.indexOf("<return>") >= 0){
                String subRes = res.substring(res.indexOf("<return>"), res.lastIndexOf("</return>") + 9);
                try{
                    json = (JSONArray) XML.toJSONObject(subRes).get("return");
                }
                catch (Exception e){
                    JSONObject jsonObj = null;
                    jsonObj = (JSONObject) XML.toJSONObject(subRes).get("return");
                    //json = (JSONArray) jsonObj.toString();
                    List<JSONObject> sList = new ArrayList<JSONObject>();
                    sList.add(jsonObj);
                    return  sList.toString();
                }
            }
            else{
                return "";
            }
        }
        catch(UnsupportedEncodingException e){
            e.printStackTrace();
        }
        return json.toString();
    }
    @GetMapping("/--get-chitieths")
    public String GetChiTietHoSo(
            HttpServletRequest request, 
            @Valid @RequestParam GettingConfigurationParamsDto config,
            @RequestParam (value = "id", required = true) String id) {
        boolean authorization = true;
        //IntegratedConfigurationDto configMSNS = configurationService.getConfig(new ObjectId(configid));
//        IntegratedConfigurationDto configMSNS;
//        if (Objects.nonNull(config.getConfigId())) {
//            configMSNS = configurationService.getConfig(config.getConfigId());
//        } else {
//            configMSNS = configurationService.getConfig(config.getAgencyId(), config.getSubsystemId(), this.configid);
//        }
        IntegratedConfigurationDto configMSNS;
        configMSNS = configurationService.getConfig(new ObjectId(configid));
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String uri = configMSNS.getParameterValue("adapter");
        String getToken = configMSNS.getParameterValue("getToken");
        String accessKey = configMSNS.getParametersValue("accessKey");
        String secretKey = configMSNS.getParametersValue("secretKey");
        String appName = configMSNS.getParametersValue("appName");
        String partnerCode = configMSNS.getParametersValue("partnerCode");
        String partnerCodeCus = configMSNS.getParametersValue("partnerCodeCus");
        String getChiTietHoSo = configMSNS.getParametersValue("getChiTietHoSo");
        String lgspaccesstoken = configMSNS.getParametersValue("authorLGSP");
        String token = getTokenLGSPHCM(uri, getToken, accessKey, secretKey, appName, partnerCode, partnerCodeCus, lgspaccesstoken);
        String sceret = "Bearer " + token;
        //apiLGSP url = new apiLGSP();
        String urlGetDanhSachHoSoTheoNgay = String.format(uri + getChiTietHoSo, id);
        String res = "";
        JSONObject json = null;
        try{
            res = HTTP_GET(urlGetDanhSachHoSoTheoNgay, authorization, sceret, lgspaccesstoken);
            if(res.indexOf("<return>") >= 0){
                String subRes = res.substring(res.indexOf("<return>"), res.lastIndexOf("</return>") + 9);
                json = (JSONObject) XML.toJSONObject(subRes).get("return");
            }
            else{
                return "";
            }
        }
        catch(UnsupportedEncodingException e){
            e.printStackTrace();
        }
        return json.toString();
    }
    
    @GetMapping("/--get-hstheoky")
    public String GetHoSoTheoKyTn(
            HttpServletRequest request, 
            @Valid @RequestParam GettingConfigurationParamsDto config,
            @RequestParam (value = "tuNgay", required = true) String tuNgay, 
            @RequestParam (value = "denNgay", required = true) String denNgay) {
        boolean authorization = true;
        //IntegratedConfigurationDto configMSNS = configurationService.getConfig(new ObjectId(configid));
//        IntegratedConfigurationDto configMSNS;
//        if (Objects.nonNull(config.getConfigId())) {
//            configMSNS = configurationService.getConfig(config.getConfigId());
//        } else {
//            configMSNS = configurationService.getConfig(config.getAgencyId(), config.getSubsystemId(), this.configid);
//        }
        IntegratedConfigurationDto configMSNS;
        configMSNS = configurationService.getConfig(new ObjectId(configid));
        if (Objects.isNull(config)) {
            throw new DigoHttpException(11000, new String[]{translator.toLocale("lang.word.configuration")}, HttpServletResponse.SC_NOT_FOUND);
        }
        String uri = configMSNS.getParameterValue("adapter");
        String getToken = configMSNS.getParameterValue("getToken");
        String accessKey = configMSNS.getParametersValue("accessKey");
        String secretKey = configMSNS.getParametersValue("secretKey");
        String appName = configMSNS.getParametersValue("appName");
        String partnerCode = configMSNS.getParametersValue("partnerCode");
        String partnerCodeCus = configMSNS.getParametersValue("partnerCodeCus");
        String getHoSoTheoKyTn = configMSNS.getParametersValue("getHoSoTheoKyTn");
        String lgspaccesstoken = configMSNS.getParametersValue("authorLGSP");
        String token = getTokenLGSPHCM(uri, getToken, accessKey, secretKey, appName, partnerCode, partnerCodeCus, lgspaccesstoken);
        String sceret = "Bearer " + token;
        //apiLGSP url = new apiLGSP();
        String urlGetDanhSachHoSoTheoNgay = String.format(uri + getHoSoTheoKyTn, tuNgay, denNgay);
        String res = "";
        JSONObject json = null;
        try{
            res = HTTP_GET(urlGetDanhSachHoSoTheoNgay, authorization, sceret, lgspaccesstoken);
            if(res.indexOf("<return>") >= 0){
                String subRes = res.substring(res.indexOf("<return>"), res.lastIndexOf("</return>") + 9);
                json = (JSONObject) XML.toJSONObject(subRes).get("return");
            }
            else{
                return "";
            }
        }
        catch(UnsupportedEncodingException e){
            e.printStackTrace();
        }
        return json.toString();
    }
    
//    @Data
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public class apiLGSP{
//        public String domainTest = "https://hcmesb-test.tphcm.gov.vn";
//        public String domainMain = "https://hcmlgsp.tphcm.gov.vn";
//        public String getToken = "/nganSach/token";
//        public String getDanhSachHoSoTheoNgay = "/nganSach/GetDanhSachHoSoTheoNgay?tuNgay=%s&denNgay=%s";
//        public String getChiTietHoSo = "/nganSach/ChiTietHoSo?id=%s";
//        public String getHoSoTheoKyTn = "/nganSach/GetHoSoTheoKyTn?tuNgay=%s&denNgay=%s";
//    }
    
}
