//package vn.vnpt.digo.adapter.api;
//
//import org.bson.types.ObjectId;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.web.bind.annotation.*;
//import vn.vnpt.digo.adapter.config.IcodeAuthorize;
//import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
//import vn.vnpt.digo.adapter.dto.industry_trade_bd.*;
//import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
//import vn.vnpt.digo.adapter.exception.DigoHttpException;
//import vn.vnpt.digo.adapter.pojo.IntegratedService;
//import vn.vnpt.digo.adapter.pojo.Permission;
//import vn.vnpt.digo.adapter.service.IndustryAndTradeBDService;
//import vn.vnpt.digo.adapter.util.Context;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import javax.validation.Valid;
//
//@RestController
//@RequestMapping("/sct")
////@IcodeAuthorize("vnpt.permission.bdg-sct")
//public class IndustryAndTradeBDController {
//    @Autowired
//    private IndustryAndTradeBDService industryAndTradeBDService;
//
//    @Value("${vnpt.permission.integrated.SCT}")
//    private String scope;
//
//    Logger logger = LoggerFactory.getLogger(IndustryAndTradeBDController.class);
//
//    @PostMapping("{configId}/{deploymentId}/WSDanhSachQuanHuyen")
//    public SCTResponce1Dto getPlacesLv2(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody BasicReqDto data
//    ) {
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//        SCTResponce1Dto res = industryAndTradeBDService.getPlacesLv2(configId, deploymentId, data);
//
//        logger.info("DIGO-Response: Get WSDanhSachQuanHuyen success");
//        return  res;
//    }
//
//    @PostMapping("{configId}/{deploymentId}/WSDanhSachPhuongXa")
//    public SCTResponce1Dto getPlacesLv3(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody BasicReqDto data
//    ) {
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//        SCTResponce1Dto res = industryAndTradeBDService.getPlacesLv3(configId, deploymentId, data);
//        logger.info("DIGO-Response: Get WSDanhSachPhuongXa success");
//        return  res;
//    }
//
//    @PostMapping("{configId}/{deploymentId}/WSDanhSachLinhVuc")
//    public SCTResponce1Dto getSectors(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody SectorReqDto data
//    ) {
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//        SCTResponce1Dto res = industryAndTradeBDService.getSectors(configId, deploymentId, data);
//
//        logger.info("DIGO-Response: Get WSDanhSachLinhVuc success");
//        return  res;
//    }
//
//    @PostMapping("{configId}/{deploymentId}/WSDanhSachThuTucSCT")
//    public SCTResponce1Dto getProcedures(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody SectorReqDto data
//    ){
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//        SCTResponce1Dto res = industryAndTradeBDService.getProcedures(configId, deploymentId, data);
//        return  res;
//    }
//
//    @PostMapping("{configId}/{deploymentId}/WSDongBoThanhPhanKemTheoThuTuc")
//    public SCTResponce1Dto getProcedueForm(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody ProcedureFormReqDto data
//    ){
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//        SCTResponce1Dto res = industryAndTradeBDService.getProcedueForm(configId, deploymentId, data);
//        return  res;
//    }
//
//    @PostMapping("{configId}/{deploymentId}/GetDanhSachHoSoSCT")
//    public SCTResponce1Dto getDossierByPeriod(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody DossierReqDto data
//    ){
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//        SCTResponce1Dto res = industryAndTradeBDService.getDossierByPeriod(configId, deploymentId, data);
//        return  res;
//    }
//
//    @PostMapping("{configId}/{deploymentId}/GetDinhKemHS")
//    public SCTResponce1Dto getDossierFile(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody DossierFileReqDto data
//    ){
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//        SCTResponce1Dto res = industryAndTradeBDService.getDossierFile(configId, deploymentId, data);
//        return  res;
//    }
//
//    @PostMapping("{configId}/{deploymentId}/TiepNhanHSThanhCong")
//    public StatusResDto receiveStatus(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody ReceiveStatusReq data
//    ){
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//        StatusResDto res = industryAndTradeBDService.receiveStatus(configId, deploymentId, data);
//        return  res;
//    }
//
//    @PostMapping("{configId}/{deploymentId}/DongBoTrangThaiHS")
//    public StatusResDto getDossierFile(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody SyncStatusDossierReqDto data
//    ){
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//
//        StatusResDto res = industryAndTradeBDService.syncStatusDossier(configId, deploymentId, data);
//        return  res;
//    }
//
//    @PostMapping("{configId}/{deploymentId}/DinhKemVanBanHS")
//    public StatusResDto attachEdocDossier(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody AttachmentEDocDossierReqDto data
//    ){
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//
//        StatusResDto res = industryAndTradeBDService.attachEdocDossier(configId, deploymentId, data);
//        return  res;
//    }
//
//    @PostMapping("{configId}/{deploymentId}/LayDSNguoiDungDonVi")
//    public UserResDto getUsersByAgency(
//            HttpServletRequest request,
//            @PathVariable(value = "configId", required = true) ObjectId configId,
//            @PathVariable(value = "deploymentId", required = true) ObjectId deploymentId,
//            @Valid @RequestBody UserInfoReqDto data
//    ){
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//        if (scope != null && !scope.isEmpty()) {
//            String clientId = Context.getJwtParameterValue("azp");
//            if (!scope.equals(clientId)) {
//                throw new DigoHttpException(10403, HttpServletResponse.SC_FORBIDDEN);
//            }
//        }
//        UserResDto res = industryAndTradeBDService.getUsersByAgency(configId, deploymentId, data);
//
//        return  res;
//    }
//}
