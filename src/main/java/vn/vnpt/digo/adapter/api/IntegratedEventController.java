package vn.vnpt.digo.adapter.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.bson.types.ObjectId;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.Message;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.document.IntegratedReCallEvent;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.service.IntegratedEventService;
import vn.vnpt.digo.adapter.dto.GetDossierDetailDto;
import vn.vnpt.digo.adapter.dto.GetListIntegratedLogsByCodeDto;
import vn.vnpt.digo.adapter.dto.InputFileDto;
import vn.vnpt.digo.adapter.dto.ListCodeToLogsDto;
import vn.vnpt.digo.adapter.dto.LogEventStorageDto;
import vn.vnpt.digo.adapter.dto.nps.NpsAsyncReceiveDto;
import vn.vnpt.digo.adapter.repository.IntegratedReCallEventRepository;

@RestController
@RequestMapping("/integrated-event")
@IcodeAuthorize("vnpt.permission.integratedevent")
public class IntegratedEventController {
    
    @Autowired
    private IntegratedEventService integratedEventService;
    
    @Autowired
    private IntegratedReCallEventRepository eventReCallRepository;

    Logger logger = LoggerFactory.getLogger(IntegratedEventController.class);

    @GetMapping()
    public String changeState(
            HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        if(EventProperties.DOSSIER_EVENT_AVAILABLE){
            EventProperties.DOSSIER_EVENT_AVAILABLE = false;
        } else {
            EventProperties.DOSSIER_EVENT_AVAILABLE = true;
        }
        
        return "Done!!!";
    }
    
    @PutMapping(value = "/updateEvent", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public AffectedRowsDto updateCountProcessProcedure() throws ParseException {
        return integratedEventService.updateEventKM();
    }
    
    @PutMapping(value = "/updateEvent-listCode", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public AffectedRowsDto updateDossierList(@RequestBody List<String> listcode) throws ParseException {
        return integratedEventService.updateEventListCode(listcode);
    }
    
    @PostMapping(value = "/event-storage", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public LogEventStorageDto updateEventStorage(@RequestBody NpsAsyncReceiveDto inputObj) throws ParseException {
        IntegratedReCallEvent event = new IntegratedReCallEvent();
        try{
//            ObjectMapper objectMapper = new ObjectMapper();
//            NpsAsyncReceiveDto inputObj = objectMapper.readValue(message.getPayload().toString(), NpsAsyncReceiveDto.class);

            event = new IntegratedReCallEvent();
            event.setStatus(2);
            event.setData(inputObj);
            IntegratedReCallEvent saved = eventReCallRepository.save(event);
//            if(!EventProperties.DOSSIER_RECALL_EVENT_AVAILABLE){
//                EventProperties.DOSSIER_RECALL_EVENT_AVAILABLE = true;
//            }
//            List<IntegratedReCallEvent> listEvent = new ArrayList<>();
//            listEvent.add(saved);
            logger.info("NPS-Sync-Storage: " + saved.getId().toHexString());
//            integratedReCallEventService.callRunEvent(listEvent);
        } catch (Exception e){
            logger.info("NPS-Exception-Storage: " + e.getMessage() + ", " + inputObj);
        }
        logger.info("NPS-Exception-Storage: event " + event.toString());
        return integratedEventService.updateEventStorage(event);
    }
    
    @PostMapping("/updateEvent-form-file")
    public AffectedRowsDto importData(
        HttpServletRequest request,
        @ModelAttribute InputFileDto input
    ) {
        return integratedEventService.importFromExcell(input.getFile());
    }
    
    @PutMapping(value = "/updateEvent-status-listcode", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public AffectedRowsDto updateDossierListStatus(@RequestBody List<String> listcode) throws ParseException {
        return integratedEventService.updateEventListCodeStatus(listcode);
    }
    
    @PutMapping(value = "/re-add-event", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public AffectedRowsDto reAddEventDossier(@RequestBody List<GetDossierDetailDto> listDossier) throws ParseException {
        return integratedEventService.reAddEventDossier(listDossier);
    }
    
    @PostMapping(value = "/call-run-event", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public AffectedRowsDto callRunEvent(@RequestBody List<IntegratedEvent> listEvent) throws ParseException {
        return integratedEventService.callRunEvent(listEvent);
    }

    @PostMapping(value = "/call-run-event-all", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public AffectedRowsDto callRunEventAll() throws ParseException {
        return integratedEventService.callRunEventAll();
    }
    
    @GetMapping("/--get-status-dossier-sync-dvcqg")
    public String getStatusDossierSyncDVCQG(
            HttpServletRequest request,
            @RequestParam(name = "dossier-id", required = true) ObjectId dossierId) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        
        String response = integratedEventService.getStatusDossierSyncDVCQG(dossierId);
        
        return response;
    }
    
    @PostMapping(value = "/--import-excel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity importEventSyncStatusDossierNPS(
            HttpServletRequest request,
            @Valid @ModelAttribute @RequestBody InputFileDto input,
            @RequestParam(name = "config-id", required = true) ObjectId configId) throws IOException, ParseException {
        return ResponseEntity.ok(integratedEventService.importEventSyncStatusDossierNPS(input, configId));
    }
    
    @GetMapping(value = "/get-log", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public List<Object> getLogByCode(@RequestParam(name = "code", required = true) String code) throws ParseException {
        return integratedEventService.getLogByCode(code);
    }
    
    @PostMapping(value = "/get-log-by-list-code", produces = "application/json")
    //@IcodeAuthorize(permission = "manageProcedure")
    public ArrayList<GetListIntegratedLogsByCodeDto> getLogByListCode(@RequestBody List<String> listcode) throws ParseException {
        return integratedEventService.getLogByListCode(listcode);
    }

    // api gọi log chi tiết cho đồng nai bằng mã
    @PostMapping(value = "/get-detailed-log-by-code", produces = "application/json")
    // @IcodeAuthorize(permission = "manageProcedure")
    public GetListIntegratedLogsByCodeDto getDetailedLogByCode(@RequestBody String listCode) throws ParseException {
        return integratedEventService.getDetailedFormattedLogByCode(listCode);
    }

    @PostMapping("/get-list-log-form-file")
    public ArrayList<ListCodeToLogsDto> importDataToGetListLog(
        HttpServletRequest request,
        @ModelAttribute InputFileDto input,
        @RequestParam(value = "date-from", required = false) String appliedFrom,
        @RequestParam(value = "date-to", required = false) String appliedTo
    ) throws ParseException {
        return integratedEventService.getListDossierLogImportFromExcell(input.getFile(), appliedFrom, appliedTo);
    }
    
//    @GetMapping("reync-dossier")
//    public String resyncDossier(
//            HttpServletRequest request,
//            @RequestParam(name = "is-all", required = false, defaultValue = "false") Boolean isAll) {
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//
//        return integratedEventService.reOfferDossier(isAll);
//    }
//    @GetMapping("reync-dossier-status")
//    public String resyncDossierStatus(
//            HttpServletRequest request,
//            @RequestParam(name = "is-all", required = false, defaultValue = "false") Boolean isAll) {
//        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//
//        return integratedEventService.reOfferDossierStatus(isAll);
//    }
}
