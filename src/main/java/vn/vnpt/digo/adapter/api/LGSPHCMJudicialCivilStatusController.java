/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import java.util.ArrayList;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPDossierDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSHCMAsyncResponseDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMCivilStatusJusticeParamDossierStatusDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMCivilStatusJusticeTokenDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMCivilStatusJusticeGetCategoryDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMCivilStatusJusticePostRegistratioDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMCivilStatusJusticeDossierStatusDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMCivilStatusJusticeResultRegistrationDto;
//import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMCivilStatusJusticeDossierRegistrationListDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMCivilStatusJusticePostRegistratioParamDto;
import vn.vnpt.digo.adapter.service.LGSPHCMJudicialCivilStatusService;
import vn.vnpt.digo.adapter.pojo.BodyCategoryMapHTTP;
import vn.vnpt.digo.adapter.service.LGSPHCMLogService;

/**
 *
 * <AUTHOR> Pham
 */
@RestController
@RequestMapping("/lgsp-hcm-civil-status")
@IcodeAuthorize("vnpt.permission.lgsp-hcm-civil-status")
public class LGSPHCMJudicialCivilStatusController {
    @Autowired
    private LGSPHCMJudicialCivilStatusService lgspHCMJudicialCivilStatusService;

    Logger logger = LoggerFactory.getLogger(LGSPHCMLogController.class);

    @Value(value = "${digo.schedule.LGSPHCMJudicialCivilStatus.enable}")
    private Boolean enableLGSPHCMV2;

    @PostMapping("/sync-nhan-hoso-dangky")
    public LGSHCMAsyncResponseDto syncNhanHoSoDangKy(HttpServletRequest request,
                                                     @RequestParam(value = "showIntputSendEnable", defaultValue = "false", required = false) String showIntputSendEnable,
                                                     @Valid @RequestBody HCMLGSPDossierDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        logger.info("body: " + body);
        if(Boolean.TRUE.equals(enableLGSPHCMV2)){
            LGSHCMAsyncResponseDto res = lgspHCMJudicialCivilStatusService.syncNhanHoSoDangKyV2(body);
            return res;
        }
        LGSHCMAsyncResponseDto res = lgspHCMJudicialCivilStatusService.syncNhanHoSoDangKy(body, showIntputSendEnable);
        return res; 
    }
   
    @PostMapping("/--gettoken")
    public LGSPHCMCivilStatusJusticeTokenDto getToken(HttpServletRequest request) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() 
                            + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        LGSPHCMCivilStatusJusticeTokenDto res = lgspHCMJudicialCivilStatusService.getTokenLGSPOld();
        logger.info("DIGO-Response: " + res.toString());
        return res;

    }
    
    @PostMapping("/--getcategory")
    public LGSPHCMCivilStatusJusticeGetCategoryDto getLGSPHCMCategory(HttpServletRequest request, @Valid @RequestBody BodyCategoryMapHTTP body)throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        LGSPHCMCivilStatusJusticeGetCategoryDto res = lgspHCMJudicialCivilStatusService.getCategory(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
        
    }
    
    @PostMapping("/--civil-status-registration")
    public LGSPHCMCivilStatusJusticePostRegistratioDto postCivilStatusRegistration( 
            HttpServletRequest request,
            @RequestBody LGSPHCMCivilStatusJusticePostRegistratioParamDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        LGSPHCMCivilStatusJusticePostRegistratioDto ret = lgspHCMJudicialCivilStatusService.postCivilStatusRegistration(body);
        logger.info("DIGO-Response: " + ret);

        return ret;
    }
       
    @PostMapping("/--return-dossier-status")
    public LGSPHCMCivilStatusJusticeDossierStatusDto postReturnDossierStatus( 
            HttpServletRequest request,
            @RequestBody LGSPHCMCivilStatusJusticeParamDossierStatusDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        LGSPHCMCivilStatusJusticeDossierStatusDto ret = lgspHCMJudicialCivilStatusService.postReturnDossierStatus(body);
        logger.info("DIGO-Response: " + ret);

        return ret;
    }
    
    @PostMapping("/--result-dossier-registration")
    public LGSPHCMCivilStatusJusticeResultRegistrationDto resultDossierRegistration( 
            HttpServletRequest request,
            @RequestBody LGSPHCMCivilStatusJusticeParamDossierStatusDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        LGSPHCMCivilStatusJusticeResultRegistrationDto ret = lgspHCMJudicialCivilStatusService.resultDossierRegistration(body);
        logger.info("DIGO-Response: " + ret);

        return ret;
    }
    
//    @PostMapping("/--dossier-registration-list")
//    public LGSPHCMCivilStatusJusticeDossierRegistrationListDto dossierRegistrationList (
//            HttpServletRequest request,
//            @RequestBody LGSPHCMCivilStatusJusticeRegistrationListParamDto body) {
//        String requestPath = request.getMethod() + " " + request.getRequestURI()
//                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
//        logger.info("DIGO-Request: " + requestPath);
//
//        LGSPHCMCivilStatusJusticeDossierRegistrationListDto ret = lgspHCMJudicialCivilStatusService.dossierRegistrationList(body);
//        logger.info("DIGO-Response: " + ret);
//
//        return ret;
 //   }
    
}
