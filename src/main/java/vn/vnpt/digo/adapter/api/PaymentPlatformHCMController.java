/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.api;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.lgsphcm.PaymentPlatformHCMGetOrderInfoDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.PaymentPlatformHCMPayGateDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.PaymentPlatformHCMQRCodeDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.PaymentPlatformHCMReceiveDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.PaymentPlatformHCMRefundDto;
import vn.vnpt.digo.adapter.service.PaymentPlatformHCMService;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/payment-platform-hcm")
@IcodeAuthorize("vnpt.permission.payment-platform-hcm")
public class PaymentPlatformHCMController {
    
    @Autowired
    private PaymentPlatformHCMService paymentPlatformHCMService;

    Logger logger = LoggerFactory.getLogger(PaymentPlatformHCMController.class);
    
    //Thanh toan truc tuyen
    @PostMapping("/--payGate")
    public Object payGate(HttpServletRequest request, @Valid @RequestBody PaymentPlatformHCMPayGateDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        logger.info("DIGO-Body: " + body.toString());
        Object res = paymentPlatformHCMService.payGate(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    //Nhan ket quan thanh toan
    @PostMapping("/--payReceive")
    public Object paygate(HttpServletRequest request, @Valid @RequestBody PaymentPlatformHCMReceiveDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        logger.info("DIGO-Body: " + body.toString());
        Object res = paymentPlatformHCMService.payReceive(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }

    @GetMapping("/--payOrderInfo")
    public Object paymentPlatformHCMInfo(HttpServletRequest request,
                          @RequestParam(name = "docCode", required = true) String docCode) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        Object res = paymentPlatformHCMService.paymentPlatformHCMInfo(docCode);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    //Hoan tra
    @PostMapping("/--refund")
    public Object paygate(HttpServletRequest request, @Valid @RequestBody PaymentPlatformHCMRefundDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        logger.info("DIGO-Body: " + body.toString());
        Object res = paymentPlatformHCMService.refund(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    //Tra cuu giao dich
    @PostMapping("/--getOrderInfo")
    public Object getOrderInfo(HttpServletRequest request, @Valid @RequestBody PaymentPlatformHCMGetOrderInfoDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        logger.info("DIGO-Body: " + body.toString());
        Object res = paymentPlatformHCMService.getOrderInfo(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    
    //Tao QRCode
    @PostMapping("/--genderQRCodeImage")
    public Object genderQRCodeImage(HttpServletRequest request, @Valid @RequestBody PaymentPlatformHCMQRCodeDto body) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);
        logger.info("DIGO-Body: " + body.toString());
        Object res = paymentPlatformHCMService.genderQRCodeImage(body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
}
