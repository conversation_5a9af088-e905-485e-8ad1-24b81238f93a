package vn.vnpt.digo.adapter.api.vpc;

import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.HandlerMapping;
import vn.vnpt.digo.adapter.dto.IntegrationParamsDto;
import vn.vnpt.digo.adapter.dto.MinistryTransportDossierDto;
import vn.vnpt.digo.adapter.dto.StatisticTransportDossierDto;
import vn.vnpt.digo.adapter.dto.dbn.gplx.DetailDossierGPLXResponseDto;
import vn.vnpt.digo.adapter.dto.dbn.gplx.DossierGPLXDto;
import vn.vnpt.digo.adapter.dto.dbn.gplx.DossierGPLXListResponseDto;
import vn.vnpt.digo.adapter.dto.hbh.log.LogParam;
import vn.vnpt.digo.adapter.service.dbn.GPLXService;
import vn.vnpt.digo.adapter.service.hbh.HbhEventLogService;
import vn.vnpt.digo.adapter.service.vpc.VpcConnecGtvtService;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/transport-vpc")
@Slf4j
//@IcodeAuthorize("vnpt.permission.transport")
public class VpcConnectGtvtController {
    @Autowired
    private VpcConnecGtvtService gtvtService;

    @Autowired
    protected HbhEventLogService eventLogService;

    @GetMapping("/--detail")
    public MinistryTransportDossierDto getDetailTranSportDossier(HttpServletRequest request,
                                                         @RequestParam(name = "id", required = true) String id) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        log.info("DIGO-Info: " + requestPath);
        return gtvtService.getDetailTranSportDossier(id);

    }
    @GetMapping("/--list")
    public Slice<MinistryTransportDossierDto> getDetailListDossier(HttpServletRequest request,
                                                                   Pageable pageable,
                                                                   @RequestParam(name = "fromDate", required = true) String fromDate,
                                                                   @RequestParam(name="trangthai",required = false)String trangthai,
                                                                   @RequestParam(name="mathutuc",required= false)String mathutuc,
                                                                   @RequestParam(name = "toDate", required = true) String toDate
                                                         ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        log.info("DIGO-Info: " + requestPath);
        return gtvtService.getDetailListDossier(fromDate, toDate, trangthai,mathutuc,pageable);
    }
    @GetMapping("/--statistic")
    public StatisticTransportDossierDto getStatisticDossierTransport(HttpServletRequest request,
                                                                 @RequestParam(name = "thang", required = true) String month,

                                                                 @RequestParam(name = "nam", required = true) String year) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        log.info("DIGO-Info: " + requestPath);
        return gtvtService.getStatisticDossierTransport(month,year);
    }

}
