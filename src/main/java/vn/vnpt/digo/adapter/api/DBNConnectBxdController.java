package vn.vnpt.digo.adapter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.document.DBNConnectBxdLog;
import vn.vnpt.digo.adapter.dto.AffectedRowsDto;
import vn.vnpt.digo.adapter.service.DBNConnectBxdService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/dbn-connect-bxd")
@IcodeAuthorize("vnpt.permission.dbn-connect-bxd")
public class DBNConnectBxdController {
    Logger logger = LoggerFactory.getLogger(BDGMocController.class);

    @Autowired
    private DBNConnectBxdService dbnConnectBxdService;

    // đồng bộ hồ sơ từ địa ph<PERSON><PERSON> sang hệ thống NOHTTTL
    @PostMapping("/--send-dossier")
    public AffectedRowsDto sendDossier(HttpServletRequest request,
                                       @RequestParam(value = "dossier-id", required = true) String dossierId
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        AffectedRowsDto res = dbnConnectBxdService.sendDossier(dossierId);
        logger.info("DIGO-Response:" + res.toString());
        return res;
    }

    // gửi thông tin xử lý hồ sơ sang hệ thống NOHTTTL
    @PostMapping("/--update-process-dossier")
    public AffectedRowsDto updateProcessDossier(HttpServletRequest request,
                                                @RequestParam(value = "dossier-id", required = true) String dossierId
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI() + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Request: " + requestPath);

        AffectedRowsDto res = dbnConnectBxdService.updateProcessDossier(dossierId);
        logger.info("DIGO-Response:" + res.toString());
        return res;
    }

    @GetMapping("/log-bxd")
    public List<Map<String, Object>> logBxd(HttpServletRequest request,
                                            @RequestParam(value = "code-dossier", required = false) String codeDossier,
                                            @RequestParam(value = "tu-ngay", required = false) String tuNgay,
                                            @RequestParam(value = "den-ngay", required = false) String denNgay
    ) {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        return dbnConnectBxdService.logBxd(codeDossier, tuNgay, denNgay);
    }
}
