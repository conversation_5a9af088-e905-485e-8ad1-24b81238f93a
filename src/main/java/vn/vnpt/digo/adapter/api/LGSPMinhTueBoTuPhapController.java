package vn.vnpt.digo.adapter.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
// import vn.vnpt.digo.adapter.api.IportalCategoryController;
import vn.vnpt.digo.adapter.config.IcodeAuthorize;
import vn.vnpt.digo.adapter.dto.IntegrationParamsDto;
import vn.vnpt.digo.adapter.dto.minhtue.civilstatus.CivilDossierMinhTueDto;
import vn.vnpt.digo.adapter.dto.minhtue.civilstatus.CivilDossierResDto;
import vn.vnpt.digo.adapter.dto.minhtue.criminalrecords.CRDossierDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.service.LGSPMinhTueBoTuPhapService;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/bo-tuphap")
@IcodeAuthorize("vnpt.permission.lgspminhtue-bo-tuphap")
public class LGSPMinhTueBoTuPhapController {
    @Autowired
    private LGSPMinhTueBoTuPhapService lGSPMinhTueBoTPService;
    Logger logger = LoggerFactory.getLogger(LGSPMinhTueBoTuPhapController.class);

    @PostMapping("/get-all-document-type")
    public ResponseEntity<String> getAllLoaiVanBan(HttpServletRequest request,
                                                 @Valid @RequestParam IntegrationParamsDto params,
                                                 @RequestBody List<Integer> unitCodes) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        var res = lGSPMinhTueBoTPService.getAllLoaiVanBan(params,unitCodes);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    @GetMapping("/get-all-don-vi")
    public ResponseEntity<String> getAllCoQuanBienTap(HttpServletRequest request,
                                                   @Valid @RequestParam IntegrationParamsDto params) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        var res = lGSPMinhTueBoTPService.getAllCoQuanBienTap(params);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    @PostMapping("/get-list-attach")
    public ResponseEntity<String> getvbplGetListAttach(HttpServletRequest request,
                                                      @Valid @RequestParam IntegrationParamsDto params,
                                                       @Valid @RequestBody Map<String, Object> body) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        var res = lGSPMinhTueBoTPService.getvbplGetListAttach(params,body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    @GetMapping("/get-by-id/{ItemID}")
    public ResponseEntity<String> getById(HttpServletRequest request,
                                                       @Valid @RequestParam IntegrationParamsDto params,
                                                       @PathVariable (value="ItemID")Integer ItemID) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        var res = lGSPMinhTueBoTPService.getById(params,ItemID);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
    @PostMapping("/timkiem-vanban")
    public ResponseEntity<String> timKiemVanbanFull(HttpServletRequest request,
                                          @Valid @RequestParam IntegrationParamsDto params,
                                                    @Valid @RequestBody Map<String, Object> body) throws Exception {
        String requestPath = request.getMethod() + " " + request.getRequestURI()
                + (request.getQueryString() != null ? "?" + request.getQueryString() : "");
        logger.info("DIGO-Info: " + requestPath);
        var res = lGSPMinhTueBoTPService.timKiemVanbanFull(params,body);
        logger.info("DIGO-Response: " + res.toString());
        return res;
    }
}
