/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.util;

import java.sql.Blob;
import java.sql.SQLException;
import java.text.Normalizer;
import java.util.*;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletResponse;
import javax.sql.rowset.serial.SerialBlob;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.CaseFormat;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.dto.Integration.FlatKeyDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;

/**
 *
 * <AUTHOR>
 */
public class Converter {
    public static Blob byteArrayToBlob(byte[] byteArray) {
        try {
            return new SerialBlob(byteArray);
        } catch (SQLException ex) {
            throw new IllegalArgumentException(ex);
        }
    }

    public static byte[] blobToByteArray(Blob blob) {
        return blobToByteArray(blob, true);
    }

    public static byte[] blobToByteArray(Blob blob, boolean freeup) {
        try {
            byte[] bytes = blob.getBytes(1, (int) blob.length());
            if (freeup) {
                blob.free();
            }
            return bytes;
        } catch (SQLException ex) {
            throw new IllegalArgumentException(ex);
        }
    }

    public static Integer stringToInteger(String input, Integer defaultValue) {
        try {
            return Integer.parseInt(input);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    public static ObjectId stringToObjectId(String oidStr){
        if (oidStr != null) {
            try {
                return new ObjectId(oidStr);
            } catch(IllegalArgumentException ex){
                throw new DigoHttpException(10001, new String[]{oidStr}, HttpServletResponse.SC_BAD_REQUEST);
            }
        }
        return null;
    }

    public static String removeAccent(String oidStr) {
        // remove dau tieng viet
        try {
            String temp = Normalizer.normalize(oidStr, Normalizer.Form.NFD);
            Pattern pattern = Pattern.compile("\\p{InCombiningDiacriticalMarks}+");
            return pattern.matcher(temp).replaceAll("").replace('Đ', 'D').replace("đ", "d");
        }catch (Exception e){
            return oidStr;
        }
    }
    public static String stringToCameCase(String oidStr){
        try{
            String stringResult = removeAccent(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, oidStr.toLowerCase().replaceAll(" ","_").replaceAll("__","_")));
            return stringResult;
        }catch (Exception e){
            return oidStr;
        }
    }

    public static String toString(Object obj){
        try{
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(obj);
            return jsonString;
        }catch (Exception ex){}
        return null;
    }

    public static HashMap<String,Object> toHashMap(String jsonString){
        try{
            jsonString = jsonString.replace("\n", "");
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            HashMap<String, Object> data = objectMapper.readValue(jsonString, HashMap.class);
            return data;
        }catch (Exception ex){
        }
        return null;
    }

    public static HashMap<String,Object> toHashMapResult(String jsonString){
        HashMap<String,Object> data = new HashMap<>();
        try{
            jsonString = jsonString.replace("\n", "");
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            data = objectMapper.readValue(jsonString, HashMap.class);
            return data;
        }catch (Exception ex){
            // Escape toàn bộ chuỗi JSON thành chuỗi Java hợp lệ
            try {
                ObjectMapper mapper = new ObjectMapper();
                String escaped = mapper.writeValueAsString(jsonString);
                data.put("errors", escaped);
                data.put("responseErr", true);
            }catch (Exception e){

            }
        }
        return data;
    }

    public static HashMap<String,Object> toHashMap(Object obj){
        String jsonString = toString(obj);
        if(Objects.nonNull(jsonString)){
            HashMap<String, Object> data = toHashMap(jsonString);
            return data;
        }
        return null;
    }

    private static Integer getObjectType(Object obj){
        if(obj instanceof Number){
            return 1;
        }else if(obj instanceof Boolean){
            return 2;
        }
        return 0;
    }


    private static void getKeyValues(Map<String, Object> map, String parentKey, List<FlatKeyDto> keysList) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            if(!(value instanceof List)  && value != null){
                String key = entry.getKey();
                String fullKey = parentKey.isEmpty() ? key : parentKey + "." + key;
                if (value instanceof Map) {
                    getKeyValues((Map<String, Object>) value, fullKey, keysList);
                } else{
                    FlatKeyDto item = new FlatKeyDto();
                    item.setKey(fullKey);
                    item.setValue(value.toString());
                    Integer type = Converter.getObjectType(value);
                    item.setType(type);
                    keysList.add(item);
                }
            }
        }
    }

    public static List<FlatKeyDto> getKeyValues(HashMap<String, Object> map) {
        List<FlatKeyDto> keysList = new ArrayList<>();
        if(Objects.nonNull(map)){
            getKeyValues(map, "", keysList);
        }
        return keysList;
    }
}
