/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.util;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;

/**
 *
 * <AUTHOR>
 */
@Retention(RUNTIME)
@Target({ElementType.TYPE, ElementType.ANNOTATION_TYPE})
@Constraint(validatedBy = CheckConfigBodyValidator.class)
public @interface CheckConfigBody {

    String message() default "Either configId or agencyId and subsystemId must be not null";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
