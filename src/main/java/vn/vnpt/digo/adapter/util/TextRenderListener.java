/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.util;

import com.itextpdf.text.pdf.parser.ImageRenderInfo;
import com.itextpdf.text.pdf.parser.LineSegment;
import com.itextpdf.text.pdf.parser.RenderListener;
import com.itextpdf.text.pdf.parser.TextRenderInfo;
import com.itextpdf.text.pdf.parser.Vector;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TextRenderListener implements RenderListener {

    private StringBuilder textBuilder = new StringBuilder();
    private List<Float> yCoords = new ArrayList<>();
    private List<Float> xCoords = new ArrayList<>();
    private List<Float> endxCoords = new ArrayList<>();

    public TextRenderListener() {
        super();
    }

    public void beginTextBlock() {
//		 System.out.println("beginTextBlock");
        // TODO Auto-generated method stub
    }

    public void renderText(TextRenderInfo renderInfo) {

        LineSegment segment = renderInfo.getBaseline();

        // TODO Auto-generated method stub
//		LineSegment segment = renderInfo.getBaseline();
        float x = segment.getStartPoint().get(Vector.I1);
        // smaller Y means closer to the BOTTOM of the page. So we negate the Y to get
        // proper top-to-bottom ordering
        float y = segment.getStartPoint().get(Vector.I2);
        float endx = segment.getEndPoint().get(Vector.I1);

        String text = renderInfo.getText();

        textBuilder.append(text);
        for (int i = 0; i < text.length(); i++) {
            yCoords.add(y);
            xCoords.add(x);
            endxCoords.add(endx);
        }

//		 System.out.println("renderText "+x+".."+endx+"/"+y+":"+renderInfo.getText());
    }

    public void endTextBlock() {

    }

    public void renderImage(ImageRenderInfo renderInfo) {
        // TODO Auto-generated method stub
        // System.out.println("renderImage");
    }

    public StringBuilder getTextBuilder() {
        return textBuilder;
    }

    public void setTextBuilder(StringBuilder textBuilder) {
        this.textBuilder = textBuilder;
    }

    public List<Float> getyCoords() {
        return yCoords;
    }

    public void setyCoords(List<Float> yCoords) {
        this.yCoords = yCoords;
    }

    public List<Float> getxCoords() {
        return xCoords;
    }

    public void setxCoords(List<Float> xCoords) {
        this.xCoords = xCoords;
    }

    public List<Float> getEndxCoords() {
        return endxCoords;
    }

    public void setEndxCoords(List<Float> endxCoords) {
        this.endxCoords = endxCoords;
    }
}
