/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class ListObjectIdJsonSerializer extends JsonSerializer<List<ObjectId>> {
    @Override
    public void serialize(List<ObjectId> list, JsonGenerator j, SerializerProvider s) throws IOException, JsonProcessingException {
        if(list == null) {
            j.writeNull();
        } else {
            j.writeStartArray();
            for(ObjectId o : list)
            {
                j.writeString(o.toString());
            }
            j.writeEndArray();
        }
    }
}
