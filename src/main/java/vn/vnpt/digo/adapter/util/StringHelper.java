package vn.vnpt.digo.adapter.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.common.protocol.types.Field;
import org.apache.poi.ss.usermodel.Cell;
import org.bson.types.ObjectId;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Formatter;
import java.util.Map;
import java.util.Objects;
import java.util.HashMap;
import java.util.Base64;

public class StringHelper {
    public static boolean hasValue(String str) {
        return Objects.nonNull(str) && !str.isEmpty() && !str.isBlank();
    }

    public static String getStringCell(Cell cell) {
        if (Objects.isNull(cell)) {
            return "";
        }
        cell.getCellType();
        switch (cell.getCellType()) {
            case STRING: {
                return cell.getStringCellValue().trim();
            }
            case NUMERIC: {
                return String.valueOf((int) cell.getNumericCellValue());
            }
            default: {
                try {
                    return cell.toString().trim();
                } catch (IllegalStateException ex) {
                    return "";
                }
            }
        }
    }

    public static String encodeUTF8(String input) {
        byte[] bytes = input.getBytes(StandardCharsets.UTF_8);
        String output = new String(bytes, StandardCharsets.UTF_8);

        return output;
    }

    public static ObjectId tryToBojectId(String str) {
        try {
            return new ObjectId(str);
        } catch (Exception ex) {
            return null;
        }
    }

    public static String tryToString(Object obj) {
        try {
            return obj.toString();
        } catch (Exception ex) {
            return null;
        }
    }

    public static byte[] toBytesSha256(String str) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(str.getBytes(StandardCharsets.UTF_8));
        return hash;
    }

    public static String toHex64(String str) {
        try {
            Formatter result = new Formatter();
            byte[] bytes = toBytesSha256(str);
            try (result) {
                for (var b : bytes) {
                    result.format("%02x", b & 0xff);
                }
                return result.toString();
            }
        } catch (Exception ex) {
        }
        return null;
    }

    public static String toBase64Encode(String str) {
        return Base64.getEncoder().encodeToString((str).getBytes());
    }

    public static String endcode(String message) {
        Base64.Encoder encoder = Base64.getMimeEncoder();
        String response = encoder.encodeToString(message.getBytes());
        return response;
    }

    public static String decode(String eStr) {
        Base64.Decoder decoder = Base64.getMimeDecoder();
        String dStr = new String(decoder.decode(eStr));
        return dStr;
    }

    public static String toHmacSha256(String secureKey, String input) throws NoSuchAlgorithmException, InvalidKeyException {
        Charset asciiCs = Charset.forName("US-ASCII");
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new javax.crypto.spec.SecretKeySpec(asciiCs.encode(secureKey).array(), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        byte[] mac_data = sha256_HMAC.doFinal(asciiCs.encode(input).array());
        String result = "";
        for (byte element : mac_data) {
            result += Integer.toString((element & 0xff) + 0x100, 16).substring(1);
        }
        return result;
    }

    public static Map<String, String> getParamsURL(String urlString) throws MalformedURLException, UnsupportedEncodingException {
        URL url = new URL(urlString);
        String query = url.getQuery();

        Map<String, String> params = new HashMap<>();
        String[] pairs = query.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            String key = URLDecoder.decode(pair.substring(0, idx), "UTF-8");
            String value = URLDecoder.decode(pair.substring(idx + 1), "UTF-8");
            params.put(key, value);
        }
        return params;
    }

    public static String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    public static HashMap<String,Object> toHashMap(String jsonString){
        try{
            ObjectMapper objectMapper = new ObjectMapper();
            HashMap<String, Object> data = objectMapper.readValue(jsonString, HashMap.class);
            return data;
        }catch (Exception ex){}
        return null;
    }

    public static String maskExceptLast(String input, int visibleCount) {
        if (input == null || input.length() <= visibleCount) {
            return input != null ? "*".repeat(input.length()) : null;
        }
        int maskLength = input.length() - visibleCount;
        return "*".repeat(maskLength) + input.substring(maskLength);
    }
}
