package vn.vnpt.digo.adapter.util;

import java.time.format.DateTimeFormatter;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
public class DateTimeUntil {
    public static String formatDateTime(String input) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");

        LocalDateTime dateTime = LocalDateTime.parse(input, inputFormatter);

        String formattedDateTime = dateTime.atOffset(ZoneOffset.ofHours(7)).format(outputFormatter);

        return formattedDateTime;

    }
}
