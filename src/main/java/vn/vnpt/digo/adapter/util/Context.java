/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.util;

import javax.servlet.http.HttpServletRequest;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import vn.vnpt.digo.adapter.pojo.Permissions;
import vn.vnpt.digo.adapter.pojo.Permission;
import com.google.gson.Gson;

import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.util.Objects;
import java.util.logging.Level;

/**
 *
 * <AUTHOR>
 */
public class Context {
    
    private static Logger logger = LoggerFactory.getLogger(Context.class);
    private static Gson gson = new Gson();
    
    public static SecurityContext getSecurityContext() {
        return SecurityContextHolder.getContext();
    }
    
    public static JwtAuthenticationToken getJwtAuthenticationToken() {
        return (JwtAuthenticationToken) getSecurityContext().getAuthentication();
    }
    
    public static WebAuthenticationDetails getWebAuthenticationDetails() {
        return (WebAuthenticationDetails) getJwtAuthenticationToken().getDetails();
    }
    
    @Deprecated
    public static String getOAuth2AccessTokenValue() {
        return getJwtAuthenticationTokenValue();
    }
    
    public static String getJwtAuthenticationTokenValue() {
        return getJwtAuthenticationToken().getToken().getTokenValue();
    }
    
    public static ServletRequestAttributes getServletRequestAttributes() {
        return (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
    }
    
    public static HttpServletRequest getHttpServletRequest() {
        return getServletRequestAttributes().getRequest();
    }
    
    public static String getRootURL() {
        String result = getHttpServletRequest().getScheme() + "://" + getHttpServletRequest().getServerName();
        if (getHttpServletRequest().getServerPort() != 80) {
            result += ":" + getHttpServletRequest().getServerPort();
        }
        return result;
    }
    
    public static ObjectId getDeploymentId() {
        Map<String, Object> tokenMap = getJwtAuthenticationToken().getTokenAttributes();
        try{
            logger.info("DIGO-Info: deployment_id = " + tokenMap.get("deployment_id").toString());
            return new ObjectId(tokenMap.get("deployment_id").toString());
        }
        catch(NullPointerException nullex){
            logger.info("DIGO-Info: deployment_id = null");
            return null;
        }
    }
    
    public static ObjectId getAccountId() {
        Map<String, Object> tokenMap = getJwtAuthenticationToken().getTokenAttributes();
        try{
            return new ObjectId(tokenMap.get("account_id").toString());
        } catch (NullPointerException nullex) {
            return null;
        }
    }
    
    public static String getJwtParameterValue(String parameter) {
        // Get username from JWT token
        try{
            String jwtToken = getJwtAuthenticationTokenValue();
            String[] split_string = jwtToken.split("\\.");
            String base64EncodedBody = split_string[1];
            byte[] decodedBytes = Base64.getUrlDecoder().decode(base64EncodedBody);
            String decodedString = new String(decodedBytes);
            ObjectMapper mapper = new ObjectMapper();
            try {
                Map<String, Object> payload = mapper.readValue(decodedString, new TypeReference<Map<String, Object>>() {
                    //Do nothing here
                });
                return payload.get(parameter).toString();
            } catch (IOException | NullPointerException ex) {
                // Do nothing
            }
        } catch (Exception e){
            
        }
        
        return null;
    }
    
    public static ObjectId getUserId() {
        Map<String, Object> tokenMap = getJwtAuthenticationToken().getTokenAttributes();
        try{
            return new ObjectId(tokenMap.get("user_id").toString());
        } catch (NullPointerException nullex) {
            return null;
        }
    }
    
    public static String getUserFullname() {
        Map<String, Object> tokenMap = getJwtAuthenticationToken().getTokenAttributes();
        try{
            return tokenMap.get("given_name").toString();
        } catch (NullPointerException nullex) {
            return "";
        }
    }

    public static Boolean getPermissions(List<String> permissions){
        Boolean check = false;
        try{
            for (String permission : permissions) {
                Permission permissionCheck = getPermission(permission);
                if (permissionCheck != null) {
                    check = true;
                    break;
                }
            }
        } catch (Exception e){
            //Do nothing
        }
        return check;
    }

    public static Permission getPermission(String permission) {
        Permissions ret = new Permissions();
        try {
            Map<String, Object> tokenMap = getJwtAuthenticationToken().getTokenAttributes();
            String s = tokenMap.get("permissions").toString();
            ret.setPermissions(gson.fromJson(s, Permission[].class));
        } catch (Exception e) {
            java.util.logging.Logger.getLogger(Context.class.getName()).log(Level.SEVERE, null, e);
            //Do nothing
        }
        return ret.getPermission(permission);
    }

    public static boolean hasPermission(String permission) {
        if (Objects.nonNull(getPermission(permission))) {
            return true;
        }
        return false;
    }

    public static Boolean getRole(String role){
        Boolean check = false;
        try{
            Map<String, Object> tokenMap = getJwtAuthenticationToken().getTokenAttributes();
            Gson gson = new Gson();
            Map<String, Object> realmAccess = (Map<String, Object>) tokenMap.get("realm_access");
            List<String> roles = (List<String>) realmAccess.get("roles");
            if (roles.contains(role)) {
                check = true;
            }
        } catch (Exception e){
            logger.info(e.getMessage());
        }
        return check;
    }

}
