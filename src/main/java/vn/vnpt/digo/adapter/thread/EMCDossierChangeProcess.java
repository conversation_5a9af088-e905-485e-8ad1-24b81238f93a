package vn.vnpt.digo.adapter.thread;

import com.google.common.primitives.Ints;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.document.IntegratedEventCompleted;
import vn.vnpt.digo.adapter.dto.emc.EmcAsyncReceiveDto;
import vn.vnpt.digo.adapter.repository.IntegratedEventCompletedRepository;
import vn.vnpt.digo.adapter.repository.IntegratedEventRepository;
import vn.vnpt.digo.adapter.service.NpsDossierSyncService;
import vn.vnpt.digo.adapter.stream.EMCDossierSyncConsumerStream;
import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Service
public class EMCDossierChangeProcess
{
    @Autowired
    TaskExecutor taskExecutor;
    @Autowired
    private IntegratedEventRepository integratedEventRepository;
    @Autowired
    private IntegratedEventCompletedRepository integratedEventCompletedRepository;
    @Autowired
    private NpsDossierSyncService service;

    @Value("${digo.thread.sync.hcm.emc.enable}")
    private Boolean syncEnable;

    @Value("${thread.sleeptime}")
    private Integer sleeptime;

    Logger log = LoggerFactory.getLogger(EMCDossierChangeProcess.class);
    @PostConstruct
    public void init() {

        taskExecutor.execute(new EMCDossierChangeProcess.InitThreadDossier());
    }

    private class InitThreadDossier implements  Runnable{

        @Override
        public void run() {
            while(syncEnable){
                try{
                    if(EMCDossierSyncConsumerStream.emcDossierReceive == null || EMCDossierSyncConsumerStream.emcDossierReceive.isEmpty())
                    {
                        Thread.sleep(sleeptime);
                        continue;
                    }
                    EmcAsyncReceiveDto payload = EMCDossierSyncConsumerStream.emcDossierReceive.poll(3, TimeUnit.SECONDS);
                    if(payload != null)
                    {
                        taskExecutor.execute(new EMCDossierChangeProcess.Process(payload));
                    }
                }catch (Exception e) {
                    log.error("[InitThread-NBRS]: ERROR {}", e.getStackTrace(), e);
                }
            }
        }
    }
    private class Process implements  Runnable{
        EmcAsyncReceiveDto payload;
        public Process(EmcAsyncReceiveDto payload)
        {
            try{
                log.info("[ProcessThread]: DataChangeInfo: " + payload);
                this.payload = payload;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        @Override
        public void run() {
            try{
                log.info("[Process]: Thread [{}] process data {}", Thread.currentThread().getName(), payload);
                String res = "";
                IntegratedEvent event = new IntegratedEvent();
                if(Objects.nonNull(payload))
                {
                    event.setData(payload);
                    res = service.syncEMCDossierHCM(payload).getRes();
                    try {
                        JSONObject jsonObject = new JSONObject(res);
                        String error_code = jsonObject.getString("error_code");
                        Integer codeNumber = Ints.tryParse(error_code);
                        if (codeNumber != null) {
                            if (codeNumber == 0) {
                                event.setStatus(2); // event completed
                            }
                            else {
                                event.setStatus(codeNumber);
                            }
                        }
                        else {
                            if (error_code.contains("X")) {
                                event.setStatus(5); // thu tuc nation code
                            }
                            else {
                                event.setStatus(4);
                            }
                        }
                        event.setMessage(res);
                    } catch (Exception e) {
                        event.setStatus(4);
                        event.setMessage("Exception JSON - Message: " + e.getMessage() + ". res = " + res);
                    }

                }else{
                    event.setStatus(3);
                    event.setMessage("error: data type null");
                }
                event.setUpdatedDate(new Date());
                event.setType(3);
                event.setIsLGSPHCM(null);
                event.setIsEMC(true);
                if (event.getStatus() == 0 || event.getStatus() == 4) {
                    integratedEventRepository.save(event);
                } else {
                    integratedEventCompletedRepository.save(new IntegratedEventCompleted(event));
                }
            }catch(Exception e)
            {

            }
        }
    }

}
