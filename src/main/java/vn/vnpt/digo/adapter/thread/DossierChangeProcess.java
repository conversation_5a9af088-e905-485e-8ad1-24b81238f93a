package vn.vnpt.digo.adapter.thread;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.primitives.Ints;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.task.TaskExecutor;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Value;

import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.document.IntegratedEventCompleted;
import vn.vnpt.digo.adapter.dto.nps.NpsAsyncReceiveDto;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.repository.IntegratedEventCompletedRepository;
import vn.vnpt.digo.adapter.repository.IntegratedEventRepository;
import vn.vnpt.digo.adapter.service.NpsDossierSyncService;
import vn.vnpt.digo.adapter.stream.NBRSDossierHCMConsumerStream;
import vn.vnpt.digo.adapter.stream.NpsDossierConsumerStream;
import vn.vnpt.digo.adapter.stream.NpsDossierStatusConsumerStream;
import vn.vnpt.digo.adapter.util.GsonUtils;

@Service
public class DossierChangeProcess {

    @Autowired
    TaskExecutor taskExecutor;
    @Autowired
    private NpsDossierSyncService service;
    @Autowired
    private IntegratedEventRepository integratedEventRepository;
    @Autowired
    private IntegratedEventCompletedRepository integratedEventCompletedRepository;
    
    @Value("${digo.thread.sync.dvcqg.enable}")
    private Boolean syncEnable;

    @Value("${thread.sleeptime}")
    private Integer sleeptime;

    Logger log = LoggerFactory.getLogger(DossierChangeProcess.class);

    @PostConstruct
    public void init() {

        taskExecutor.execute(new DossierChangeProcess.InitThreadDossier());
        taskExecutor.execute(new DossierChangeProcess.InitThreadDossierStatus());
        taskExecutor.execute(new DossierChangeProcess.InitNBRSThreadDossier());
    }
    private class InitNBRSThreadDossier implements  Runnable{
        @Override
        public void run() {
            while(syncEnable){
                try{
                    if(NBRSDossierHCMConsumerStream.nbrsDossierHCMReceive == null){
                        Thread.sleep(sleeptime);
                        continue;
                    }else if(NBRSDossierHCMConsumerStream.nbrsDossierHCMReceive.isEmpty()){
                        Thread.sleep(sleeptime);
                        continue;
                    }
                    NpsAsyncReceiveDto payload = NBRSDossierHCMConsumerStream.nbrsDossierHCMReceive.poll(5, TimeUnit.SECONDS);
                    if(payload != null)
                    {
                        log.info("[InitThread-NBRS]: data change capture in processing");
                        //
                        //Khởi tạo multi thread
                        //
                        log.info("[StartThread-NBRS]: Start execute with payload: " + payload);
                        taskExecutor.execute(new DossierChangeProcess.Process(payload));
                    }
                }catch (Exception e) {
                    log.error("[InitThread-NBRS]: ERROR {}", e.getStackTrace(), e);
                }
            }
        }
    }
    private class InitThreadDossierStatus implements  Runnable{
        @Override
        public void run() {
              while(syncEnable){
                   try{
                       if(NpsDossierStatusConsumerStream.dossierStatusReceive == null){
                             Thread.sleep(sleeptime);
                             continue;
                       }else if(NpsDossierStatusConsumerStream.dossierStatusReceive.isEmpty()){
                             Thread.sleep(sleeptime);
                             continue;
                       }
                       NpsAsyncReceiveDto payload = NpsDossierStatusConsumerStream.dossierStatusReceive.poll(5, TimeUnit.SECONDS);
                       if(payload != null)
                       {
                           log.info("[InitThread]: data change capture in processing");
                           //
                           //Khởi tạo multi thread
                           //
                           log.info("[StartThread]: Start execute with payload: " + payload);
                           taskExecutor.execute(new DossierChangeProcess.Process(payload));
                       }
                   }catch (Exception e) {
                       log.error("[InitThread]: ERROR {}", e.getStackTrace(), e);
                   }
              }
        }
    }

    private class InitThreadDossier implements Runnable {
        @Override
        public void run() {
            while (syncEnable) {
                try {
                    if (NpsDossierConsumerStream.dossierReceive == null) {
//                        log.info("Queue empty. Thread sleep " + sleeptime + "ms.");
                        Thread.sleep(sleeptime);
                        continue;
                    } else if (NpsDossierConsumerStream.dossierReceive.isEmpty()) {
                        Thread.sleep(sleeptime);
                        continue;
                    }
                    NpsAsyncReceiveDto payload = NpsDossierConsumerStream.dossierReceive.poll(5, TimeUnit.SECONDS);
                    //
                    if (payload != null) {
                        //DataChangeInfo dataChangeInfo = JsonUtils.unJson(payload, DataChangeInfo.class);
                        log.info("[InitThread]: data change capture in processing");
                        //
                        //Khởi tạo multi thread
                        //
                        log.info("[StartThread]: Start execute with payload: " + payload);
                        taskExecutor.execute(new DossierChangeProcess.Process(payload));
                    }
                } catch (Exception e) {
                    log.error("[InitThread]: ERROR {}", e.getStackTrace(), e);
                }
            }
        }
    }


    private class Process implements Runnable {
        NpsAsyncReceiveDto dataChangeInfo;
        public Process(NpsAsyncReceiveDto dataChangeInfo) {
            log.info("[ProcessThread]: DataChangeInfo: " + dataChangeInfo);
            try {
                this.dataChangeInfo = dataChangeInfo;
            } catch (Exception e) {
                log.info("Exception: " + e);
            }
        }

        @Override
        public void run() {
            try {
                log.info("[Process]: Thread [{}] process data {}", Thread.currentThread().getName(), dataChangeInfo);
                //
                //Phân tích bản ghi, Lưu vào db đích
                //
                // 1. Xử lý nghiệp vụ
                // Đồng  bộ
                // 2. Xử lý xong thì lưu kết quả vào vào collection history
                // 3. Xóa ở collection event -- dư
                // Trường hợp lỗi bản ghi tông ở event, thêm 1 service để chạy job đồng bộ 
                // (quét những bản ghi lưu quá thời gian đặt job, ví dụ chạy job 30 phút 1 lần thì lấy những bản tin quá 30p)

                String res = "";
                IntegratedEvent event = new IntegratedEvent();
                if (Objects.nonNull(dataChangeInfo)) {
                    event.setData(dataChangeInfo);
                    switch(this.dataChangeInfo.getType()) {
                        case 1: {
                            log.info("NPS-Sync: Event dossier with: " + GsonUtils.getJson(this.dataChangeInfo));
                            if(Objects.nonNull(this.dataChangeInfo.getDossier().getNationCode()) && this.dataChangeInfo.getDossier().getNationCode().indexOf("G22") == 0){
                                service.syncPromotionDossier(this.dataChangeInfo, 1, null);
                            }
                            res = service.syncDossier(this.dataChangeInfo).getRes();
                            break;
                        }
                        case 2: {
                            log.info("NPS-Sync: Event dossier status with: " + GsonUtils.getJson(this.dataChangeInfo));
                            if(Objects.nonNull(this.dataChangeInfo.getDossier().getNationCode()) && this.dataChangeInfo.getDossier().getNationCode().indexOf("G22") == 0){
                                service.syncPromotionDossier(this.dataChangeInfo, 2, null);
                            }
                            res = service.syncDossierStatus(this.dataChangeInfo);
                            break;
                        }
                    }
                    try {
                        JSONObject jsonObject = new JSONObject(res);
                        String error_code = jsonObject.getString("error_code");
                        Integer codeNumber = Ints.tryParse(error_code);
                        if (codeNumber != null) {
                            if (codeNumber == 0) {
                                event.setStatus(2);
                            }
                            else {
                                event.setStatus(codeNumber);
                            }
                        }
                        else {
                            if (error_code.contains("E")) {
                                event.setStatus(5);
                            }
                            else {
                                event.setStatus(4);
                            }
                        }
                        event.setMessage(res);
                    } catch (Exception e) {
                        event.setStatus(4);
                        event.setMessage("Exception JSON - Message: " + e.getMessage() + ". res = " + res);
                    }
                } else {
                    event.setStatus(3);
                    event.setMessage("error: data type null");
                }
                event.setUpdatedDate(new Date());
                if (event.getStatus() == 0 || event.getStatus() == 4) {
                    integratedEventRepository.save(event);
                    EventProperties.DOSSIER_EVENT_OP_AVAILABLE = true;
                } else {
                    integratedEventCompletedRepository.save(new IntegratedEventCompleted(event));
                }
            } catch (Exception e) {
                log.error("[Process]: ERROR {}", e.getStackTrace(), e);
            }
        }

        
    }
}
