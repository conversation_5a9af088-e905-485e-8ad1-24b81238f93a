package vn.vnpt.digo.adapter.thread;

import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.adapter.document.VNPostEvent;
import vn.vnpt.digo.adapter.dto.IdDto;
import vn.vnpt.digo.adapter.dto.VNPostResultDto;
import vn.vnpt.digo.adapter.dto.v2.email.SendEmailWithTemplateDto;
import vn.vnpt.digo.adapter.dto.v2.smsbrandname.SendWithTemplateDto;
import vn.vnpt.digo.adapter.exception.DigoHttpException;
import vn.vnpt.digo.adapter.repository.VNPostEventRepository;
import vn.vnpt.digo.adapter.service.LgspHcmVnPostService;
import vn.vnpt.digo.adapter.service.v2.SmsBrandnameService;
import vn.vnpt.digo.adapter.stream.VNPostConsumerStream;
import vn.vnpt.digo.adapter.stream.VNPostProducerStream;
import vn.vnpt.digo.adapter.util.MicroserviceExchange;
import vn.vnpt.digo.adapter.util.Translator;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Service
public class VNPostEventThread {
    @Autowired
    TaskExecutor taskExecutor;

    @Autowired
    private LgspHcmVnPostService lgspHcmVnPostService;

    @Autowired
    private VNPostEventRepository vnPostEventRepository;


    @Autowired
    private VNPostProducerStream vnPostProducerStream;

    @Autowired
    private Translator translator;

    @Value("${digo.thread.sync.vnpost.enable}")
    private Boolean syncEnable;

    @Value("${thread.sleeptime}")
    private Integer sleeptime;

    public static String tokenAuthen = "";
    Logger log = LoggerFactory.getLogger(VNPostEventThread.class);

    @PostConstruct
    public void init() {

        taskExecutor.execute(new VNPostEventThread.InitThreadSMS());
    }

    private class InitThreadSMS implements Runnable {
        @Override
        public void run() {
            while (syncEnable) {
                try {
                    if (VNPostConsumerStream.vnPostEvents == null) {
//                        log.info("Queue empty. Thread sleep " + sleeptime + "ms.");
                        Thread.sleep(sleeptime);
                        continue;
                    } else if (VNPostConsumerStream.vnPostEvents.isEmpty()) {
                        Thread.sleep(sleeptime);
                        continue;
                    }
                    VNPostEvent payload = VNPostConsumerStream.vnPostEvents.poll(5, TimeUnit.SECONDS);

                    //Kiểm tra và tạo token AUTHORIZED để sử dụng đa luồng
//                    if(!smsBrandnameService.isTokenExpired(tokenAuthen)){
//                        log.error("[sendWithTemplate]: get Token[{}] ");
//                        tokenAuthen = MicroserviceExchange.getToken();
//                    }
                    //
                    if (payload != null) {
                        //DataChangeInfo dataChangeInfo = JsonUtils.unJson(payload, DataChangeInfo.class);
                        log.info("[InitThread]: data change capture in processing");
                        //
                        //Khởi tạo multi thread
                        //
                        log.info("[StartThread]: Start execute with payload: " + payload);
                        taskExecutor.execute(new VNPostEventThread.Process(payload));
                    }
                } catch (Exception e) {
                    log.error("[InitThread]: ERROR {}", e.getStackTrace(), e);
                }
            }
        }
    }

    private class Process implements Runnable {
        VNPostEvent vnPostEvent;
        public Process(VNPostEvent vnPostEvent) {
            log.info("[ProcessThread]: DataChangeInfo: " + vnPostEvent);
            try {
                this.vnPostEvent = vnPostEvent;
            } catch (Exception e) {
                log.info("Exception: " + e);
            }
        }

        @Override
        public void run() {
            try {
                log.info("[Process SMS]: Thread [{}] process data {}", Thread.currentThread().getName(), vnPostEvent);

                VNPostEvent event = new VNPostEvent();
                if (Objects.nonNull(vnPostEvent)) {
                    event = vnPostEvent;
                    try {
                        ObjectMapper mapper = new ObjectMapper();
                        Map<String, Object> dtoVnPost = mapper.convertValue(vnPostEvent.getRequest(), Map.class);
                        VNPostResultDto res = lgspHcmVnPostService.getOrder(dtoVnPost);
                        if (Objects.nonNull(res) && res.getStatus() == "100") {
                            event.setStatus(2); //Completed
                        }else {
                            event.setStatus(3);
                        }
                        event.setMessage(res.toString());
                    } catch (Exception e) {
                        event.setStatus(4);
                        event.setMessage("Exception JSON - Message: " + e.getMessage() + ". dataSendWithTemplate = " + vnPostEvent);
                    }
                } else {
                    event.setStatus(3);
                    event.setMessage("Error Event SMS: data type null");
                }
                event.setResync(event.getResync()+1);
                event.setUpdatedDate(new Date());
                if (event.getStatus() == 1 || event.getStatus() == 4) {
                    vnPostEventRepository.save(event);
                    if(event.getResync() < 3){
                        if(!vnPostProducerStream.pushVNPostMessageProcedureDetailMain(vnPostEvent).get()){
                            vnPostEvent.setStatus(0);
                            vnPostEvent.setMessage("Send to kafka fail");
                            vnPostEventRepository.save(vnPostEvent);
                        }
                    }
                } else {
                    vnPostEventRepository.save(event);
                }
            } catch (Exception e) {
                log.error("[Process SMS Event]: ERROR {}", e.getStackTrace(), e);
            }
        }
    }
}
