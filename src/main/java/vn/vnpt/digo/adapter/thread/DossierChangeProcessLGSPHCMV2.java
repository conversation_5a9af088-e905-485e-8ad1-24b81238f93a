/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.thread;

import com.google.common.primitives.Ints;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import vn.vnpt.digo.adapter.repository.IntegratedEventCompletedRepository;
import vn.vnpt.digo.adapter.repository.IntegratedEventRepository;
import vn.vnpt.digo.adapter.stream.LGSPHCMNpsDossierStatusConsumerStream;
import vn.vnpt.digo.adapter.stream.LGSPHCMNpsDossierSyncConsumerStream;
import vn.vnpt.digo.adapter.document.IntegratedEvent;
import vn.vnpt.digo.adapter.util.GsonUtils;
import vn.vnpt.digo.adapter.properties.EventProperties;
import vn.vnpt.digo.adapter.document.IntegratedEventCompleted;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.nps.NpsAsyncReceiveDto;
import vn.vnpt.digo.adapter.service.IntegratedConfigurationService;
import vn.vnpt.digo.adapter.service.NpsDossierSyncService;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DossierChangeProcessLGSPHCMV2 {
    @Autowired
    TaskExecutor taskExecutor;
    @Autowired
    private NpsDossierSyncService service;
    @Autowired
    private IntegratedEventRepository integratedEventRepository;
    @Value("${digo.thread.resync.dvcqg-lgsphcm.enable}")
    private Boolean resyncLGSPHCMEnable;
    @Autowired
    private IntegratedEventCompletedRepository integratedEventCompletedRepository;

    @Autowired
    private IntegratedConfigurationService configurationService;

    @Value(value = "${digo.thread.lgsphcm.dossier-sync-config-id}")
    private ObjectId lgspHCMDossierSyncConfigId;

    private final ObjectId serviceId = new ObjectId("5f7c16069abb62f511890030");



    @Value("${thread.sleeptime}")
    private Integer sleeptime;

    @PostConstruct
    public void init() {
        taskExecutor.execute(new DossierChangeProcessLGSPHCMV2.InitThread());
    }
    private class InitThread implements Runnable {
        @Override
        public void run() {
            while (resyncLGSPHCMEnable) {
                try {
                    if (LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCMv2 == null) {
//                        log.info("Queue empty. Thread sleep " + sleeptime + "ms.");
                        Thread.sleep(sleeptime);
                        continue;
                    } else if (LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCMv2.isEmpty()) {
//                        log.info("Queue empty. Thread sleep " + sleeptime + "ms.");
                        Thread.sleep(sleeptime);
                        continue;
                    }
                    NpsAsyncReceiveDto payload = LGSPHCMNpsDossierSyncConsumerStream.dossierReceiveLGSPHCMv2.poll(5, TimeUnit.SECONDS);
                    //
                    if (payload != null) {
                        // DataChangeInfo dataChangeInfo = JsonUtils.unJson(payload,
                        // DataChangeInfo.class);
                        log.info("[LGSPHCM-InitThread]: data change capture in processing");
                        //
                        // Khởi tạo multi thread
                        //
                        log.info("[LGSPHCM-StartThread]: Start execute with payload: " + payload);
                        taskExecutor.execute(new DossierChangeProcessLGSPHCMV2.Process(payload));
                    }
                } catch (Exception e) {
                    log.error("[LGSPHCM-InitThread]: ERROR {}", e.getStackTrace(), e);
                }
            }
        }
    }

    private class Process implements Runnable {
        NpsAsyncReceiveDto dataChangeInfo;
        public Process(NpsAsyncReceiveDto dataChangeInfo) {
            log.info("[LGSPHCM-ProcessThread]: DataChangeInfo: " + dataChangeInfo);
            try {
                this.dataChangeInfo = dataChangeInfo;
            } catch (Exception e) {
                log.info("Exception: " + e);
            }
        }

        @Override
        public void run() {
            try {
                log.info("[LGSPHCM-Process]: Thread [{}] process data {}", Thread.currentThread().getName(), dataChangeInfo);
                //
                //Phân tích bản ghi, Lưu vào db đích
                //
                // 1. Xử lý nghiệp vụ
                // Đồng  bộ
                // 2. Xử lý xong thì lưu kết quả vào vào collection history
                // 3. Xóa ở collection event -- dư
                // Trường hợp lỗi bản ghi tông ở event, thêm 1 service để chạy job đồng bộ
                // (quét những bản ghi lưu quá thời gian đặt job, ví dụ chạy job 30 phút 1 lần thì lấy những bản tin quá 30p)

                String res = "";
                IntegratedEvent event = new IntegratedEvent();
                event.setIsLGSPHCM(Boolean.TRUE);
                if (Objects.nonNull(dataChangeInfo)) {
                    IntegratedConfigurationDto config;
                    if (Objects.nonNull(dataChangeInfo.getConfigId())) {
                        config = configurationService.getConfig(dataChangeInfo.getConfigId());
                        if (config != null) {
                            if (!config.getService().getId().equals(serviceId)) {
                                dataChangeInfo.setConfigId(lgspHCMDossierSyncConfigId);
                            }
                        }
                    }
                    event.setData(dataChangeInfo);
                    switch(this.dataChangeInfo.getType()) {
                        case 1: {
                            log.info("LGSPHCM-InitThread-NPS-Sync: Event dossier with: " + GsonUtils.getJson(this.dataChangeInfo));
                            if(Objects.nonNull(this.dataChangeInfo.getDossier().getNationCode()) && this.dataChangeInfo.getDossier().getNationCode().indexOf("G22") == 0){
                                service.syncPromotionDossier(this.dataChangeInfo, 1, null);
                            }
                            res = service.syncDossierResyncBDTC(this.dataChangeInfo).getRes();
                            break;
                        }
                        case 2: {
                            log.info("LGSPHCM-InitThread-NPS-Sync: Event dossier status with: " + GsonUtils.getJson(this.dataChangeInfo));
                            if(Objects.nonNull(this.dataChangeInfo.getDossier().getNationCode()) && this.dataChangeInfo.getDossier().getNationCode().indexOf("G22") == 0){
                                service.syncPromotionDossier(this.dataChangeInfo, 2, null);
                            }
                            res = service.syncDossierStatus(this.dataChangeInfo);
                            break;
                        }
                    }
                    try {
                        JSONObject jsonObject = new JSONObject(res);
                        String error_code = jsonObject.getString("error_code");
                        Integer codeNumber = Ints.tryParse(error_code);
                        if (codeNumber != null) {
                            if (codeNumber == 0) {
                                event.setStatus(2);
                            }
                            else {
                                event.setStatus(codeNumber);
                            }
                        }
                        else {
                            if (error_code.contains("E")) {
                                event.setStatus(5);
                            }
                            else {
                                event.setStatus(4);
                            }
                        }
                        event.setMessage(res);
                    } catch (Exception e) {
                        event.setStatus(4);
                        event.setMessage("Exception JSON - Message: " + e.getMessage() + ". res = " + res);
                    }
                } else {
                    event.setStatus(3);
                    event.setMessage("error: data type null");
                }
                event.setUpdatedDate(new Date());
                if (event.getStatus() == 0 || event.getStatus() == 4) {
                    integratedEventRepository.save(event);
                    EventProperties.DOSSIER_EVENT_AVAILABLE_LGSPHCM = true;
                } else {
                    integratedEventCompletedRepository.save(new IntegratedEventCompleted(event));
                }
            } catch (Exception e) {
                log.error("[LGSPHCM-Process]: ERROR {}", e.getStackTrace(), e);
            }
        }
    }

//    private class Process implements Runnable {
//        LGSPHCMAsyncReceiveDto dataChangeInfo;
//        IntegratedEvent event;
//
//        public Process(IntegratedEvent event) {
//            log.info("[LGSPHCM-ProcessThread]: Event" + event);
//            try {
//                this.event = event;
//                this.dataChangeInfo = (LGSPHCMAsyncReceiveDto) event.getData();
//            } catch (Exception e) {
//                log.info("Exception: " + e);
//            }
//        }
//
//        @Override
//        public void run() {
//            try {
//                log.info("[LGSPHCM-Process]: Thread [{}] process data {}", Thread.currentThread().getName(),
//                        dataChangeInfo);
//                //
//                // Phân tích bản ghi, Lưu vào db đích
//                //
//                // 1. Xử lý nghiệp vụ
//                // Đồng bộ
//                // 2. Xử lý xong thì lưu kết quả vào vào collection history
//                // 3. Xóa ở collection event -- dư
//                // Trường hợp lỗi bản ghi tông ở event, thêm 1 service để chạy job đồng bộ
//                // (quét những bản ghi lưu quá thời gian đặt job, ví dụ chạy job 30 phút 1 lần
//                // thì lấy những bản tin quá 30p)
//
//                String res = "";
//                if (Objects.nonNull(dataChangeInfo)) {
//                    switch (this.dataChangeInfo.getType()) {
//                        case 1: {
//                            log.info("LGSPHCM-NPS-Sync: Event dossier with: " + GsonUtils.getJson(this.dataChangeInfo));
//                            // if(Objects.nonNull(this.dataChangeInfo.getDossier().getNationCode()) &&
//                            // this.dataChangeInfo.getDossier().getNationCode().indexOf("G22") == 0){
//                            // service.syncPromotionDossier(this.dataChangeInfo, 1, null);
//                            // }
//                            res = service.syncDossierDVCQG(this.dataChangeInfo);
//                            break;
//                        }
//                        case 2: {
//                            log.info("LGSPHCM-NPS-Sync: Event dossier status with: "
//                                    + GsonUtils.getJson(this.dataChangeInfo));
//                            // if(Objects.nonNull(this.dataChangeInfo.getDossier().getNationCode()) &&
//                            // this.dataChangeInfo.getDossier().getNationCode().indexOf("G22") == 0){
//                            // service.syncPromotionDossier(this.dataChangeInfo, 2, null);
//                            // }
//                            res = service.syncDossierStatusDVCQG(this.dataChangeInfo);
//                            break;
//                        }
//                    }
//                    try {
//                        JSONObject jsonObject = new JSONObject(res);
//                        String error_code = jsonObject.getString("error_code");
//                        Integer codeNumber = Ints.tryParse(error_code);
//                        if (codeNumber != null) {
//                            if (codeNumber == 0) {
//                                event.setStatus(2);
//                            } else {
//                                event.setStatus(codeNumber);
//                            }
//                        } else {
//                            if (error_code.contains("E")) {
//                                event.setStatus(5);
//                            } else {
//                                event.setStatus(4);
//                            }
//                        }
//                        event.setMessage(res);
//                    } catch (Exception e) {
//                        event.setStatus(4);
//                        event.setMessage("LGSPHCM-Exception JSON - Message: " + e.getMessage() + ". res = " + res);
//                    }
//                } else {
//                    event.setStatus(3);
//                    event.setMessage("LGSPHCM-error: data type null");
//                }
//                event.setUpdatedDate(new Date());
//                if (event.getStatus() == 0 || event.getStatus() == 4) {
//                    integratedEventRepository.save(event);
//                    EventProperties.DOSSIER_EVENT_AVAILABLE_LGSPHCM = true;
//                } else {
//                    integratedEventCompletedRepository.save(new IntegratedEventCompleted(event));
//                }
//            } catch (Exception e) {
//                log.error("[LGSPHCM-Process]: ERROR {}", e.getStackTrace(), e);
//            }
//        }
//    }

}