package vn.vnpt.digo.adapter.dto.minhtue.criminalrecords;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CRSendResDto implements Serializable {
    private Integer status;
    private String description;
    private String id;
}
