package vn.vnpt.digo.adapter.dto.bdg;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VNPayCheckTransResponseBDGDto implements Serializable {
    @JsonProperty("code")
    public String code = null;

    @JsonProperty("message")
    public String message = null;

    @JsonProperty("masterMerchantCode")
    public String masterMerchantCode = null;

    @JsonProperty("merchantCode")
    public String merchantCode = null;

    @JsonProperty("terminalID")
    public String terminalID = null;

    @JsonProperty("billNumber")
    public String billNumber = null;

    @JsonProperty("txnId")
    public String txnId = null;

    @JsonProperty("payDate")
    public String payDate = null;

    @JsonProperty("qrTrace")
    public String qrTrace = null;

    @JsonProperty("bankCode")
    public String bankCode = null;

    @JsonProperty("debitAmount")
    public String debitAmount = null;

    @JsonProperty("realAmount")
    public String realAmount = null;

    @JsonProperty("checkSum")
    public String checkSum = null;

}
