package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.Translate;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsTranslateAgencyDto implements Serializable {

    private String code;

    private List<Translate> name;

    public NpsTranslateAgencyDto(String code, Short languageId, String name) {
        this.code = code;
        this.name = new ArrayList<>();
        this.name.add(new Translate((short) 228, name));
    }
}
