package vn.vnpt.digo.adapter.dto.industry_trade_bd;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcedureResDto implements Serializable {
    @JsonProperty("ThuTucID")
    private String ThuTucID;

    @JsonProperty("ThuTucMotCuaID")
    private String ThuTucMotCuaID;

    @JsonProperty("MaThuTuc")
    private String MaThuTuc;

    @JsonProperty("MaThuTucMotCua")
    private String MaThuTucMotCua;

    @JsonProperty("TenThuTuc")
    private String TenThuTuc;

    @JsonProperty("LinhVucMotCuaID")
    private String LinhVucMotCuaID;

    @JsonProperty("LinhVucID")
    private String LinhVucID;

    @JsonProperty("SoNgayGiaiQuyet")
    private Integer SoNgayGiaiQuyet;

}
