package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GLISendDocToQLVBDHDto {
    @JsonProperty("maHoSo")
    public String maHoSo;

    @JsonProperty("tenDuThao")
    public String tenDuThao;

    @JsonProperty("nguoiSoan")
    public String nguoiSoan;

    @JsonProperty("fileDinhKem")
    public ArrayList<TapTin> tapTins;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TapTin {
        @JsonProperty("tenFile")
        public String tenGiayTo;

        @JsonProperty("linkFile")
        public String linkFile;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class sendDocQLVBDHResponse implements Serializable {
        @JsonProperty("Result")
        public Integer result;

        @JsonProperty("Message")
        public String message;
    }
}
