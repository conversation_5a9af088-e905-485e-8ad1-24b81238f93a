package vn.vnpt.digo.adapter.dto.evn;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsumeProvinceResDto implements Serializable {
    private Boolean success;
    private Integer errorCode;
    private String status;
    private Integer statusCode;
    private String message;
    private List<ConsumeProvince> data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class ConsumeProvince {
        @JsonProperty("DIEN_TTHU")
        private Double DIEN_TTHU;
        @JsonProperty("NAM")
        private Integer NAM;
    }
}
