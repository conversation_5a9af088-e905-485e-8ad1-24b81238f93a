package vn.vnpt.digo.adapter.dto.KTM_Monre_Integration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.ktm_social_protection.KTMDossierApply;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Dossier implements Serializable {

    @JsonProperty("id")
    private String id;

    private String code;

    private Date acceptedDate;

    private Date appliedDate;

    private Date appointmentDate;

    private Date dueDate;

    private Date completedDate;

    private Date returnedDate;

    private DossierStatus dossierStatus;

    private DossierTaskStatus dossierTaskStatus;

    private DossierReceivingKind dossierReceivingKind;

    private Agency agency;

    private KTMDossierApply.ApplicantDto applicant;

    private ArrayList<DossierTask> task;

    private List<AttachmentDto> attachment;

    private List<DossierFormFile> dossierFormFile;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierStatus implements Serializable {

        private int id;
        private ArrayList<TranslateName> name;
        @Null
        private String comment;

        private String description;

        private int numberDateRequire;

        private Date dateRequire;

        public DossierStatus(int id, ArrayList<TranslateName> name, String comment) {
            this.id = id;
            this.name = name;
            this.comment = comment;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierTaskStatus implements Serializable{

        private String id;

//        private ArrayList<TranslateName> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TranslateName implements Serializable{

        private Short languageId;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DossierReceivingKind implements Serializable {
        private String id;

        private ArrayList<TranslateName> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Agency implements Serializable{
        private String id;

        private String code;

        @NotNull
        private ArrayList<TranslateName> name;

        @NotNull
        private Agency parent;

        @NotNull
        private ArrayList<Agency> ancestors;

        @NotNull
        private ArrayList<Tag> tag;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Tag implements Serializable{
        private String id;

        private ArrayList<TranslateName> name = new ArrayList<>();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierTask implements Serializable{

        private String id;

        private CandidateUser sender;

        private CandidateUser assignee;

        private ArrayList<CandidateUser> candidateUser;

        private ArrayList<Agency> candidateGroup;

        private CandidatePosition candidatePosition;

        private AgencyType agencyType;

        private IdString processDefinition;

        private Id parent;

        private Agency fromAgency;

        private Agency agency;

        @JsonProperty("eForm")
        private IdName eForm;

        private int isCurrent;

        private int isFirst;

        private int isLast;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date assignedDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date completedDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date dueDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date claimDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date resolvedDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date createdDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date updatedDate;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date confirmFFODate;

        private ActivitiTask activitiTask;

//        private BpmProcessDefinitionTask bpmProcessDefinitionTask;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CandidateUser implements Serializable{

        private String id;

        @NotNull
        private String fullname;

        @NotNull
        private UserAccount account;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserAccount implements Serializable{

        private String id;

        private ArrayList<UserAccountUsername> username;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserAccountUsername implements Serializable{

        String value;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CandidatePosition implements Serializable{

        private String id;

        private ArrayList<TranslateName> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgencyType implements Serializable {

        private String id;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IdString implements Serializable {

        private String id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Id implements Serializable {

        private String id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IdName implements Serializable{

        private String id;

        private String name;

        private String code;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivitiTask implements Serializable{
        private String id;
        private String status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttachmentDto implements Serializable {

        private String id;
        private String filename;
        private Integer size;
        private String group;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierFormFile implements Serializable {
        private String id;
        private String formName;
        private Requirement requirement;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Requirement implements Serializable {
            private String typeId;
            private String typeName;
            private int quantity;
        }
    }
}
