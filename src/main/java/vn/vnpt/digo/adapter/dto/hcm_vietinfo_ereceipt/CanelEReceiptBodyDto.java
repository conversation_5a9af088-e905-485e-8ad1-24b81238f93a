package vn.vnpt.digo.adapter.dto.hcm_vietinfo_ereceipt;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CanelEReceiptBodyDto implements Serializable {
    private ObjectId configId;

    private VietInfoReceipt vietInfoReceipt;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VietInfoReceipt implements Serializable{

        @JsonProperty("AppName")
        private String appName;

        @JsonProperty("PartnerCode")
        private String partnerCode;

        @JsonProperty("MaBienLai")
        private String receiptCode;

        @JsonProperty("Ticket")
        private String ticket;

        @JsonProperty("CanBoXuLy")
        private String staffHandle;
    }
}
