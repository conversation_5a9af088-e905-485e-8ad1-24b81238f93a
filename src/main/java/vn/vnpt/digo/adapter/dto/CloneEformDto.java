package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.CheckConfigParams;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigParams
public class CloneEformDto {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String machineName;
    private String name;
    private Integer check;
}
