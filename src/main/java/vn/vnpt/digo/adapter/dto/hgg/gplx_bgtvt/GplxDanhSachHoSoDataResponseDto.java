package vn.vnpt.digo.adapter.dto.hgg.gplx_bgtvt;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GplxDanhSachHoSoDataResponseDto implements Serializable {
    private String maDangKyCu;
    private String maDangKyDvc;
    private String maHoSo;
    private String hoTen;
    private String hoTenIn;
    private String hoDem;
    private String ten;
    private String gioiTinh;
    private String ngaySinh;
    private String quocTich;
    private String soCmndCu;
    private String soCmnd;
    private String noiCapCmnd;
    private String ngayCapCmnd;
    private String noiCuTru;
    private String dvhcNoiCuTru;
    private String noiThuongTru;
    private String dvhcNoiThuongTru;
    private String anhChanDung;
    private String maDonVi;
    private String ngayNhanHoSo;
    private String nguoiNhanHoSo;
    private String ngayHenTraGplx;
    private String loaiHoSo;
    private String noiHocLaiXe;
    private String namHocLaiXe;
    private String hangDaoTao;
    private String soNamLaiXe;
    private String soKmLxAnToan;
    private String lyDoDoiGplx;
    private String mucDichDoiGplx;
    private String maDonViVpdk;
    private List<gplxDaCap> gplxDaCap;
    private List<hoSoDinhKem> hoSoDinhKem;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class gplxDaCap implements Serializable {
        private String sogplx;
        private String soserigplx;
        private String noicapgplx;
        private String ngaycapgplx;
        private String ngayhethangplx;
        private String hanggplxgplx;
        private String ngaytrungtuyen;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class hoSoDinhKem implements Serializable {
        private String magiayto;
        private String tengiayto;
        private String filedinhkem;
    }
}
