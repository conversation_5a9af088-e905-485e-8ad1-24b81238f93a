package vn.vnpt.digo.adapter.dto.cto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.NpsProceduresResultDto;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NpsProceduresResultCTODto extends NpsProceduresResultDto implements Serializable {

    private Date date;

    private int status;

    private String codeLevels;

    private Boolean isChange;
}