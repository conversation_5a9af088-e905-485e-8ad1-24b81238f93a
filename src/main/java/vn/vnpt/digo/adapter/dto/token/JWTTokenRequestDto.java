package vn.vnpt.digo.adapter.dto.token;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JWTTokenRequestDto implements Serializable {
    
    public String grant_type;
    
    public String username;
    
    public String password;
    
    public String client_id;
    
    public String client_secret;
    
}
