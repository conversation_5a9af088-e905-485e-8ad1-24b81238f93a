package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDetailDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierFeeDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierSyncReqDto;
import vn.vnpt.digo.adapter.dto.utils.*;
import vn.vnpt.digo.adapter.util.ListHelper;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class BdgNpsDossierSyncReqDto implements Serializable {
    private String session;
    private String madonvi;
    private String service = "DongBoHoSoMC";
    private String isUpdating;
    private List<DossierData> data;

    public BdgNpsDossierSyncReqDto(String session, String madonvi, List<DossierData> data, String isUpdate) {
        this.madonvi = madonvi;
        this.isUpdating = isUpdate;
        this.data = data;
        this.session = session;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierData {
        private String MaHoSo;
        private String MaTTHC; // Mã thủ tục hành chính theo CSDL TTHC Quốc gia
        private String TenTTHC;
        private String MaLinhVuc; // Tên lĩnh vực theo CSDL TTHC Quốc gia
        private String TenLinhVuc;
        private String KenhThucHien = "1"; // 1: Trực tiếp 2: Nộp trực tuyến 3: Nộp qua bưu chính công ích
        private String ChuHoSo;
        private String LoaiDoiTuong = "1";
        private String MaDoiTuong = ""; // TechnicalId lấy từ hệ thống VNConnect
        private String ThongTinKhac = ""; // no
        private String Email;
        @JsonProperty("Fax")
        private String Fax;
        @JsonProperty("SoDienThoai")
        private String SoDienThoai;

        @JsonProperty("TrichYeuHoSo")  // no
        private String TrichYeuHoSo;

        @JsonProperty("NgayTiepNhan")
        private String NgayTiepNhan;

        @JsonProperty("NgayHenTra")
        private String NgayHenTra;

        @JsonProperty("TrangThaiHoSo")
        private String TrangThaiHoSo = "1";

        @JsonProperty("NgayTra")
        private String NgayTra;

        @JsonProperty("HinhThuc")
        private String HinhThuc;

        @JsonProperty("NgayKetThucXuLy")
        private String NgayKetThucXuLy;

        @JsonProperty("DonViXuLy")
        private String DonViXuLy;

        @JsonProperty("GhiChu")
        private String GhiChu="";  // no

        @JsonProperty("TaiLieuNop")
        private List<AttachmentDto> TaiLieuNop;

        @JsonProperty("DanhSachLePhi")
        private List<FeeDto> DanhSachLePhi;

        @JsonProperty("DanhSachTepDinhKemKhac")
        private List<AttachmentOtherDto> DanhSachTepDinhKemKhac;

        @JsonProperty("DanhSachHoSoBoSung")
        private List<AdditionalDossierDto> DanhSachHoSoBoSung;

        @JsonProperty("DanhSachGiayToKetQua")
        private List<ResultDto> DanhSachGiayToKetQua;

        @JsonProperty("NoiNopHoSo")
        private String NoiNopHoSo;

        @JsonProperty("HoSoCoThanhPhanSoHoa")
        private String HoSoCoThanhPhanSoHoa;

        @JsonProperty("TaiKhoanDuocXacThucVoiVNeID")
        private String TaiKhoanDuocXacThucVoiVNeID;

        @JsonProperty("DuocThanhToanTrucTuyen")
        private String DuocThanhToanTrucTuyen;

        @JsonProperty("NgayTuChoi")
        private String NgayTuChoi;

        @JsonProperty("DinhDanhCHS")
        private List<OwnerAuthenticatedDto> DinhDanhCHS;

        @JsonProperty("NgayNopHoSo")
        private String NgayNopHoSo;

        @JsonProperty("DSKetNoiCSDL")
        private List<ConnectedDatabaseDto> DSKetNoiCSDL;

        public DossierData(NpsDossierDetailDto dossier, List<NpsDossierFeeDto> fees,
                           Integer status, UserInfoDto user, ProcedureDetailDto procedure, SectorDetailDto sector, IdCodeNameDto receivingKind,
                           IdCodeNameSimpleDto agency, String techId, String filepath) {
            Logger logger = LoggerFactory.getLogger(NpsDossierSyncReqDto.class);
            try {
                // dossier info
                this.MaHoSo = dossier.getCode();

                this.MaTTHC = procedure.getNationCode();
                this.TenTTHC = procedure.getName();

                this.MaLinhVuc = sector.getCode();
                this.TenLinhVuc = procedure.getSector().getName();

                this.KenhThucHien = dossier.getApplyMethod().getId().toString();

                this.ChuHoSo = dossier.getApplicant().getData().getFullname();
                this.LoaiDoiTuong = user.getType().toString();
                this.MaDoiTuong = techId;
                this.NgayTuChoi = "";
                this.Email = dossier.getApplicant().getData().getEmail();
                this.Fax = dossier.getApplicant().getData().getFax();
                this.SoDienThoai = dossier.getApplicant().getData().getPhoneNumber();
                this.DonViXuLy = agency.getName();
                this.TrangThaiHoSo = status.toString();
                SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
                if(Objects.nonNull(dossier.getAppliedDate())){
                    this.NgayTiepNhan = df.format(dossier.getAppliedDate());
                } else {
                    this.NgayTiepNhan = "";
                }
                if(Objects.nonNull(dossier.getAppointmentDate())){
                    this.NgayHenTra = df.format(dossier.getAppointmentDate());
                } else {
                    this.NgayHenTra = "";
                }
                if(Objects.nonNull(dossier.getCompletedDate())){
                    this.NgayTra = df.format(dossier.getCompletedDate());
                } else {
                    this.NgayTra = "";
                }
                if(Objects.nonNull(dossier.getCompletedDate())){
                    this.NgayKetThucXuLy = df.format(dossier.getCompletedDate());
                } else {
                    this.NgayKetThucXuLy = "";
                }
                try {
                    Integer kind = Integer.parseInt(receivingKind.getCode());
                    kind = kind - 1000;
                    this.HinhThuc = kind.toString();
                } catch (Exception e) {
                    this.HinhThuc = "0";
                }

                this.HoSoCoThanhPhanSoHoa = "0";
                this.TaiKhoanDuocXacThucVoiVNeID = "1";
                this.DuocThanhToanTrucTuyen = "2";
                this.NgayNopHoSo = this.NgayTiepNhan;
                this.NoiNopHoSo = "2";
                // attchment info
                List<AttachmentDto> lstAttchment = new ArrayList<>();
                this.TaiLieuNop = lstAttchment;
                // fee info
                List<FeeDto> lstFee = new ArrayList<>();
                fees.forEach(item -> {
                    FeeDto f = new FeeDto(ListHelper.getName(item.getProcost().getType().getName()),
                            item.getId().toHexString(), "1", item.getAmount().intValue(),
                            String.valueOf(item.getProcost().getType().getType() + 1));
                    lstFee.add(f);
                });
                this.DanhSachLePhi = lstFee;

                //attachmentother
                List<AttachmentOtherDto> lstAttchmentOther = new ArrayList<>();
                this.DanhSachTepDinhKemKhac = lstAttchmentOther;

                // result
                List<ResultDto> lstResult = new ArrayList<>();
                if(Objects.nonNull(dossier.getAttachment())){
                    dossier.getAttachment().forEach(item -> {
                        if (Objects.equals(new ObjectId("5f9bd9692994dc687e68b5a6"), item.getGroup())) {
                            try{
                                ResultDto rs = new ResultDto(item.getFilename(), filepath + item.getId().toHexString() + "&uuid="+ item.getUuid().toString(), "", "", "");
                                lstResult.add(rs);
                            }catch (Exception e){
                                ResultDto rs = new ResultDto(item.getFilename(), filepath + item.getId().toHexString(), "", "", "");
                                lstResult.add(rs);
                            }

                        }
                    });
                }
                this.DanhSachGiayToKetQua = lstResult;
                // OwnerAuthenticated
                List<OwnerAuthenticatedDto> lstOwnerAuthenticated = new ArrayList<>();
                String identityType = "";
                if(user.getIdentity().getNumber().length() == 9){
                    identityType = "2";
                } else if(user.getIdentity().getNumber().length() == 12){
                    identityType = "1";
                } else {
                    identityType = "3";
                }
                OwnerAuthenticatedDto owner = new OwnerAuthenticatedDto(identityType, user.getIdentity().getNumber());
                lstOwnerAuthenticated.add(owner);
                this.DinhDanhCHS = lstOwnerAuthenticated;

                // ConnectedDatabase
                List<ConnectedDatabaseDto> lstConnectedDatabase = new ArrayList<>();
                ConnectedDatabaseDto database = new ConnectedDatabaseDto("7");
                lstConnectedDatabase.add(database);
                this.DSKetNoiCSDL = lstConnectedDatabase;

                // AdditionalDossier
                List<AttachmentDto> lstAttchmentAdditional = new ArrayList<>();
                List<AdditionalDossierDto> lstAdditionalDossier = new ArrayList<>();
                dossier.getDossierFormFile().forEach(item -> {
                    item.getFile().forEach(i -> {
                        try{
                            AttachmentDto file = new AttachmentDto(i.getId().toHexString(), i.getFilename(),false,"",filepath + i.getId().toHexString() + "&uuid=" + i.getUuid().toString(), "0", "0", "0", "");
                            lstAttchmentAdditional.add(file);
                        }catch (Exception e){
                            AttachmentDto file = new AttachmentDto(i.getId().toHexString(), i.getFilename(),false,"",filepath + i.getId().toHexString(), "0", "0", "0", "");
                            lstAttchmentAdditional.add(file);
                        }
                    });
                });
                List<FeeDto> lstFeeAdditional = new ArrayList<>();
                fees.forEach(item -> {
                    FeeDto f = new FeeDto(ListHelper.getName(item.getProcost().getType().getName()),
                            item.getId().toHexString(), "1", item.getAmount().intValue(),
                            String.valueOf(item.getProcost().getType().getType() + 1));
                    lstFeeAdditional.add(f);
                });
                ObjectId id = new ObjectId();
                AdditionalDossierDto fileAdditional = new AdditionalDossierDto(id.toString(),"Ng??i y�u c?u b? sung","N?i dung y�u c?u b? sung","","","","",true, lstAttchmentAdditional,lstFeeAdditional,"","");
                lstAdditionalDossier.add(fileAdditional);
                this.DanhSachHoSoBoSung = lstAdditionalDossier;
            } catch (Exception e) {
                logger.debug("Constructor: Create dossier sync DVCQG data failed: " + e.getMessage());
            }
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AttachmentDto {
        @JsonProperty("TepDinhKemId")
        private String TepDinhKemId;
        @JsonProperty("TenTepDinhKem")
        private String TenTepDinhKem;
        @JsonProperty("IsDeleted")
        private Boolean isDeleted;
        @JsonProperty("MaThanhPhanHoSo")
        private String MaThanhPhanHoSo;
        @JsonProperty("DuongDanTaiTepTin")
        private String DuongDanTaiTepTin;
        @JsonProperty("DuocSoHoa")
        private String DuocSoHoa;
        @JsonProperty("DuocTaiSuDung")
        private String DuocTaiSuDung;
        @JsonProperty("DuocLayTuKhoDMQG")
        private String DuocLayTuKhoDMQG;
        @JsonProperty("MaKetQuaThayThe")
        private String MaKetQuaThayThe;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FeeDto {

        @JsonProperty("TenPhiLePhi")
        private String TenPhiLePhi;
        @JsonProperty("MaPhiLePhi")
        private String MaPhiLePhi;
        @JsonProperty("HinhThucThu")
        private String HinhThucThu;
        @JsonProperty("Gia")
        private Integer Gia;
        @JsonProperty("LoaiPhiLePhi")
        private String LoaiPhiLePhi;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ResultDto {

        @JsonProperty("TenGiayTo")
        private String TenGiayTo;

        @JsonProperty("MaThanhPhanHoSo")
        private String MaThanhPhanHoSo;

        @JsonProperty("GiayToId")
        private String GiayToId;

        @JsonProperty("DuongDanTepTinKetQua")
        private String DuongDanTepTinKetQua;

        @JsonProperty("MaGiayToKetQua")
        private String MaGiayToKetQua;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OwnerAuthenticatedDto {

        @JsonProperty("LoaiDinhDanh")
        private String LoaiDinhDanh;

        @JsonProperty("SoDinhDanh")
        private String SoDinhDanh;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ConnectedDatabaseDto {

        @JsonProperty("MaCSDL")
        private String MaCSDL;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AdditionalDossierDto {

        @JsonProperty("HoSoBoSungId")
        private String HoSoBoSungId;

        @JsonProperty("NguoiYeuCauBoSung")
        private String NguoiYeuCauBoSung;

        @JsonProperty("NoiDungBoSung")
        private String NoiDungBoSung;

        @JsonProperty("NgayBoSung")
        private String NgayBoSung;

        @JsonProperty("NguoiTiepNhanBoSung")
        private String NguoiTiepNhanBoSung;

        @JsonProperty("ThongTinTiepNhan")
        private String ThongTinTiepNhan;

        @JsonProperty("NgayTiepNhanBoSung")
        private String NgayTiepNhanBoSung;

        @JsonProperty("TrangThaiBoSung")
        private boolean TrangThaiBoSung;

        @JsonProperty("DanhSachGiayToBoSung")
        private List<AttachmentDto> DanhSachGiayToBoSung;

        @JsonProperty("DanhSachLePhiBoSung")
        private List<FeeDto> DanhSachLePhiBoSung;

        @JsonProperty("NgayHenTraTruoc")
        private String NgayHenTraTruoc;

        @JsonProperty("NgayHenTraMoi")
        private String NgayHenTraMoi;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AttachmentOtherDto {

        @JsonProperty("TenGiayTo")
        private String TenGiayTo;

        @JsonProperty("SoLuong")
        private String SoLuong;

        @JsonProperty("LoaiGiayTo")
        private String LoaiGiayTo;
    }
}
