package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PetitionReporterDto implements Serializable {

    private String id;

    private String username;

    private String fullname;

    private String phone;

    private String identityId;

    private String type;

    private ReporterAddressDto address;
}
