/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.util.CheckConfigBody;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigBody
public class VilisAcceptReqDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId configId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subsystemId;
    @NonNull
    private Integer QuyTrinhId;
    private Integer LoaiGiaoDichId;
    private Integer LoaiHoSoGiaoDichId;
    private Integer NhanVienTiepNhanId;
    private String MaXa;
    private String LaToChuc;
    private String GioiTinh;
    private String HoTenChu;
    private String SoGiayTo;
    private String SoDienThoai;
    private String DiaChiChu;
    private String SoThuTuThua;
    private String SoHieuToBanDo;
    private String DiaChiThua;
    private Integer TuCachPhapNhan;
    private String GhiChuTuCachPhapNhan;
    private String NoiDungHoSo;
    private String NguoiNopHoSo;
    private String SoGiayToNguoiNop;
    private String ChucVuNguoiNop;
    private Boolean SuDungBienNhanMotCua;
    private String SoBienNhanMotCua;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date NgayTiepNhanMotCua;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date NgayHenTraMotCua;
    
    
    
    
    
}
