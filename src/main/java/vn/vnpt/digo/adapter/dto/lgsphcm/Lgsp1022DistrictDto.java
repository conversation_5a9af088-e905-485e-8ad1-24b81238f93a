/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Lgsp1022DistrictDto implements Serializable{
    private String districtCode;
    private String provinceCode;
    private String name;
    
    @JsonSetter("MATINHTHANH")
    public void mapperProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }
    @JsonSetter("TENQUANHUYEN")
    public void mapperDistrictName(String name) {
        this.name = name;
    }
    @JsonSetter("MAQUANHUYEN")
    public void mapperDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }
}
