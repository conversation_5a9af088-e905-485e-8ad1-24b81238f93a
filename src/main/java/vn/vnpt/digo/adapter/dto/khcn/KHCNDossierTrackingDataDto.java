package vn.vnpt.digo.adapter.dto.khcn;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
@JsonIgnoreProperties(ignoreUnknown = true)
public class KHCNDossierTrackingDataDto implements Serializable {

    @JsonProperty("maDonVi")
    @NotEmpty(message = "'maDonVi'")
    private String agencyCode;

    @JsonProperty("maHoSo")
    @NotEmpty(message = "'maHoSo'")
    private String code;

    @JsonProperty("maHoSoLienThong")
    @NotEmpty(message = "'maHoSoLienThong'")
    private String nationCode;

    @JsonProperty("maTTHC")
    @NotEmpty(message = "'maTTHC'")
    private String procedureCode;

    @JsonProperty("trangThai")
    @NotNull(message = "'trangThai'")
    private Integer status;

    @JsonProperty("tenTrangThai")
    private String statusName;

    @JsonProperty("thoiDiemXuLy")
    @NotEmpty(message = "'thoiDiemXuLy'")
    @Size(min = 14, max = 14, message = "'thoiDiemXuLy'")
    private String processTime;

    @JsonProperty("nguoiXuLy")
    private String processor;

    @JsonProperty("phongBanXuLy")
    private String processingDepartment;

    @JsonProperty("noiDungXuLy")
    private String processContent;

    @JsonProperty("ketQuaXuLy")
    private String processResult;

    @JsonProperty("ghiChu")
    private String note;

    @JsonProperty("ngayHenTra")
    private String expectedReturnDate;

    @JsonProperty("lyDoTuChoi")
    private String rejectionReason;

    @JsonProperty("yeuCauBoSung")
    private String supplementRequest;
}
