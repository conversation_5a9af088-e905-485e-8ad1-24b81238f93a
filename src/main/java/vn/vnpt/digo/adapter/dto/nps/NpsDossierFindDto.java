package vn.vnpt.digo.adapter.dto.nps;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsDossierFindDto implements Serializable {
    
    private String code;
    private CodeNameDto procedure;
    private CodeNameDto sector;
    private String dossierTaskStatusCode;
    private String applyMethod;
    private ApplicantDto applicant;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CodeNameDto implements Serializable {
        private String code;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplicantDto implements Serializable {
        private String fullname;
        private String identityNumber;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date identityDate;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date birthday;
        private String phoneNumber = "";
        private String email = "";
        private String fax = "";
    }
}
