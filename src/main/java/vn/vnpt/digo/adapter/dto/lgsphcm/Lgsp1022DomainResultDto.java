/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Lgsp1022DomainResultDto implements Serializable{
    private List<Lgsp1022DomainDto> result;

    private String errorCode;

    @JsonSetter("error_code")
    public void mapperErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    @JsonSetter("result")
    public void mapperResultObject(List<Lgsp1022DomainDto> result) {
        this.result = result;
    }
}
