package vn.vnpt.digo.adapter.dto.qnm.ilis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import vn.vnpt.digo.adapter.dto.AgencyFullyDto;
import vn.vnpt.digo.adapter.dto.SidNameDto;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetEventLogQnmDto implements Serializable {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId pid;

    private SidNameDto key;

    private Object req;

    private Object res;

    private String errMsg;

    private Date exeDate;

    private boolean status;

    private Integer count;

    private Integer num;

    private String code;
    private AgencyFullyDto agency;
    private Integer type;
    private boolean enableCallBack;
    public ObjectId getId() {
        return id;
    }

}
