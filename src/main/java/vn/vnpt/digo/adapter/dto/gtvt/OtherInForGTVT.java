/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.gtvt;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OtherInForGTVT implements Serializable{
    
    private String isGTVT;
    private String MaDoiTuong;
    private String TrichYeuHoSo;
    private String TenTTHC;
    private String TenLinhVuc;
    private String DonViXuLy;
    private String SoDienThoai;
    private String Email;
    private String MaTinh;
    private String TenTinh;
    private String MaHuyen;
    private String TenHuyen;
    private String MaXa;
    private String TenXa;
    private String NgayGuiHoSo;
    private String NgayKetThucXuLy;

}
