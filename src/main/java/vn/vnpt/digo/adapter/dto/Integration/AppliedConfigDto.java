package vn.vnpt.digo.adapter.dto.Integration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.document.ApiIntegration;
import vn.vnpt.digo.adapter.document.ApiIntegrationApplied;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppliedConfigDto {
    private List<ApiIntegration> configs;
    private List<ApiIntegrationApplied> applieds;
}
