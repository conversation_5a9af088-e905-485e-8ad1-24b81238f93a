/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.BusinessRegistrationData;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessRegistrationGetDetailInTimeDto implements Serializable {
    private ArrayList<Map<String, Object>> Data;
    
    private Integer TotalCount;
    
    private Integer DataCount;
    
    private Integer Status;
    
    private String Message;
}
