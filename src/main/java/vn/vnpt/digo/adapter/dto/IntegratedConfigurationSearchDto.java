package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.digo.adapter.pojo.IntegratedService;
import vn.vnpt.digo.adapter.pojo.Subsystem;
import vn.vnpt.digo.adapter.pojo.Tag;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "integratedConfiguration")
public class IntegratedConfigurationSearchDto implements Serializable {

    private Integer num;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String name;

    private IntegratedService service;

    @JsonIgnore
    private List<Subsystem> subsystem;
    @JsonProperty("subsystem")
    private List<SubsystemDto> transSubsystem;

    @JsonIgnore
    private Tag tag;
    @JsonProperty("tag")
    private TagDto transTag;

    private Integer status;
    
    private String code;

    public void setTransSubsystem(Short localeId) {
        if (Objects.nonNull(subsystem)) {
            transSubsystem = new ArrayList<>();
            subsystem.forEach(item -> {
                transSubsystem.add(new SubsystemDto(item, localeId));
            });
        }
    }

    public void setTransTag(Short localeId) {
        if (Objects.nonNull(tag)) {
            transTag = new TagDto(tag, localeId);
        }
    }
}
