package vn.vnpt.digo.adapter.dto.khdt_dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import vn.vnpt.digo.adapter.dto.PadPAddReqDto;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor

public class coop_doc_int {
    @JsonProperty("process_id")
    private int process_id;
    private String IN_JOURNAL_NO;
    private String DOCUMENT_TYPE;
    private String ENTERPRISE_CODE;
    private String ENTERPRISE_GDT_CODE;
    private String NAME;
    private String SITE_ID;
    private String RECEIPT_DATE;
    private String PLAN_DATE;
    private String PROCESS_STATUS;
    private int ID;
    private List<String> files;
}
