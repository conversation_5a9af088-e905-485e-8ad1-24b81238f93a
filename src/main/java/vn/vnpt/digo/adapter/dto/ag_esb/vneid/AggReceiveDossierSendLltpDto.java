package vn.vnpt.digo.adapter.dto.ag_esb.vneid;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.ArrayList;
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AggReceiveDossierSendLltpDto {
    private String maHoSoMCDT;
    private String authKey;
    private ToKhai toKhai;
    private int nguonDangKy;
    private String ngayTiepNhan;
    private String ngayHenTra;
    private String tenNguonDangKy;
    private String thongTinKhac;
    private String tenToChuc;
    private String tenNguoiNop;
    private String diaChiNguoiNop;
    private String soCCCDNguoiNop;
    private String soDienThoaiNguoiNop;
    private String tenDichVuCong;
    private String tenTrangThaiHoSo;
    private String maTrangThaiHoSo;
    private String phongBanXuLy;
    private String chuyenVienXuLy;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ToKhai{
        private String nycTenQuocTich;
        private String nuqDienThoai;
        private String nuqTenQuocTich;
        private String nycEmail;
        private String voChongSoGiayTo;
        private String nycHoTenCha;
        private String nycLoaiGiayTo;
        private String nycNoiCapGiayTo;
        private String nuqNgaySinh;
        private String mucDichKhac;
        private String nuqTenDanToc;
        private String nyqQuanHe;
        private NycNoiSinh nycNoiSinh;
        private String nuqNoiCapGiayTo;
        private String nycNgayCapGiayTo;
        private String nuqDanToc;
        private ArrayList<NycCuTru> nycCuTru;
        private String nycTenGoiKhac;
        private String nycQuocTich;
        private String uyQuyen;
        private String nuqSoGiayTo;
        private String nycHoTenVoChong;
        private String yeuCauCDNCV;
        private String tenMucDich;
        private String nycSoGiayTo;
        private NycTamTru nycTamTru;
        private String nuqGioiTinh;
        private String nuqTenGioiTinh;
        private String voChongNgaySinh;
        private String loaiPhieu;
        private NuqNoiSinh nuqNoiSinh;
        private String ngayHenTra;
        private String thongTinAnTich;
        private String nycTenLoaiGiayTo;
        private String nuqNgayCapGiayTo;
        private String maMucDich;
        private String nycNoiSinhNuocNgoai;
        private String meNgaySinh;
        private String nycHoTenMe;
        private String mucDich;
        private String chaSoGiayTo;
        private String nycDienThoai;
        private NuqThuongTruChiTiet nuqThuongTruChiTiet;
        private String nuqLoaiGiayto;
        private String nycGioiTinh;
        private String nycHoTen;
        private String uyQuyenHoTen;
        private String chaNgaySinh;
        private String nuqQuocTich;
        private String meSoGiayTo;
        private String nycTenGioiTinh;
        private int soLuongCap;
        private NycThuongTru nycThuongTru;
        private String nycTenDanToc;
        private ArrayList<GiayToDinhKem> fileHoSo;
        private String nycDanToc;
        private String nycNgaySinh;
        private String nuqEmail;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NuqNoiSinh{
        private String tenQuanHuyen;
        @SerializedName("chiTiet")
        private String dcChiTiet;
        private String maQuanHuyen;
        private String tenTinhThanh;
        private String maPhuongXa;
        private String maTinhThanh;
        private String tenPhuongXa;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NuqThuongTruChiTiet{
        private String tenQuanHuyen;
        @SerializedName("chiTiet")
        private String dcChiTiet;
        private String maQuanHuyen;
        private String tenTinhThanh;
        private String maPhuongXa;
        private String maTinhThanh;
        private String tenPhuongXa;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NycCuTru{
        private String tuNgay;
        private String denNgay;
        private String noiCuTru;
        private String ngheNghiep;
        private String noiLamViec;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NycNoiSinh{
        private String tenQuanHuyen;
        @SerializedName("chiTiet")
        private String dcChiTiet;
        private String maQuanHuyen;
        private String tenTinhThanh;
        private String maPhuongXa;
        private String maTinhThanh;
        private String tenPhuongXa;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NycTamTru{
        private String tenQuanHuyen;
        @SerializedName("chiTiet")
        private String dcChiTiet;
        private String maQuanHuyen;
        private String tenTinhThanh;
        private String maPhuongXa;
        private String maTinhThanh;
        private String tenPhuongXa;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NycThuongTru{
        private String tenQuanHuyen;
        @SerializedName("chiTiet")
        private String dcChiTiet;
        private String maQuanHuyen;
        private String tenTinhThanh;
        private String maPhuongXa;
        private String maTinhThanh;
        private String tenPhuongXa;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GiayToDinhKem{
        public String tenLoaiFile;
        public String tenFile;
        public String noiDungFile;
    }

}

