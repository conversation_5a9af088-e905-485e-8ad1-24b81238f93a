package vn.vnpt.digo.adapter.dto.bdg.moc;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MocInvestorDto {

    @JsonProperty("TenChuDauTu")
    private String fullName;

    @JsonProperty("NguoiDaiDien")
    private String legalRepresentativeName;

    @JsonProperty("ChucVu")
    private String position="";

    @JsonProperty("DiaChiLienHe")
    private String contactAddress;

    @JsonProperty("MaTinhThanh")
    private String provinceCode;

    @JsonProperty("MaQuanHuyen")
    private String districtCode;

    @JsonProperty("MaPhuongXa")
    private String wardCode;

    @JsonProperty("SoNhaDuongPho")
    private String street;

    @JsonProperty("SoDienThoai")
    private String phone;
}
