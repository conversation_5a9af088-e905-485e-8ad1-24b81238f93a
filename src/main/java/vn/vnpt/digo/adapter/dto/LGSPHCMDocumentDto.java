/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LGSPHCMDocumentDto implements Serializable {
    
    private String code;

    private String name;

    private Integer originalTotal;

    private Integer copyTotal;

    private String formName;

    private String url;

    @JsonSetter("MAGIAYTO")
    public void mapCode(String code) {
        this.code = code;
    }

    @JsonSetter("TENGIAYTO")
    public void mapName(String name) {
        this.name = name;
    }

    @JsonSetter("SOBANCHINH")
    public void mapOriginalTotal(Integer originalTotal) {
        this.originalTotal = originalTotal;
    }

    @JsonSetter("SOBANSAO")
    public void mapCopyTotal(Integer copyTotal) {
        this.copyTotal = copyTotal;
    }

    @JsonSetter("TENMAUDON")
    public void mapFormName(String formName) {
        this.formName = formName;
    }

    @JsonSetter("URL")
    public void mapUrl(String url) {
        this.url = url;
    }
}
