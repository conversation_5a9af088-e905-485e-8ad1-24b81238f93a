package vn.vnpt.digo.adapter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.document.SmartReaderLog;
import vn.vnpt.digo.adapter.pojo.Owner;

import java.io.Serializable;
import java.util.HashMap;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SmartReaderLogRequestDto implements Serializable {
    private Owner owner;
    private String hash;
    private String key;
    private Integer first = 0;
    private HashMap<String, Object> result;
    private SmartReaderLog.Agency agency;
}
