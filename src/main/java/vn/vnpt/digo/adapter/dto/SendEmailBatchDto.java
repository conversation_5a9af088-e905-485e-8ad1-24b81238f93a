package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SendEmailBatchDto implements Serializable {

    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId emailId;

    @NotNull
    private String subject;

    @NotNull
    private String content;

    private String[] emailAddress;
}
