package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProceduresChangeResultDto implements Serializable {

    @JsonProperty("MA")
    private String MA;

    @JsonProperty("TEN")
    private String TEN;

    @JsonProperty("MACOQUAN")
    private String MACOQUAN;

    @JsonProperty("NGAYSUA")
    private String NGAYSUA;

}
