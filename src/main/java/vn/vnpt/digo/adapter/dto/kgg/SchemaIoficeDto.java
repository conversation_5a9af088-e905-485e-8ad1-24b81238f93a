package vn.vnpt.digo.adapter.dto.kgg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SchemaIoficeDto {
    private StatusDTO status;
    private List<AgentDTO> data;
}

@Data
@NoArgsConstructor
@AllArgsConstructor
class StatusDTO {
    private String code;
    private String message;
}

@Data
@NoArgsConstructor
@AllArgsConstructor
class AgentDTO {
    private String agentId;
    private String name;
}