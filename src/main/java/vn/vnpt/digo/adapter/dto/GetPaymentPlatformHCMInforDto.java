package vn.vnpt.digo.adapter.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetPaymentPlatformHCMInforDto implements Serializable {
    private String description;
    private List<DossierFeeInforDto> resultObject;
    private String resultType;
    private String status;
    private Integer statusCode;
    private Boolean throwException;
}
