package vn.vnpt.digo.adapter.dto.khdt_dto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import vn.vnpt.digo.adapter.dto.PadPAddReqDto;
import java.util.List;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DongBoHoSoMC_DP_HTX_LIST {
    private List<DongBoHoSoMC_DP_HTX> DongBoHoSoMC_DP;
}
