package vn.vnpt.digo.adapter.dto.qlvb;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommentQLVBDto implements Serializable {
    private String content;
    private Integer groupId = 2;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId itemId;//dossierId
    private CommentQLVBDto.AutoUser user = new CommentQLVBDto.AutoUser();
    private List<FileCommentItem> file;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class AutoUser implements Serializable {
        private String fullname = "auto.admin (QLVB Minh Tue)";

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id = new ObjectId("000000000000000000000000");
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class FileCommentItem implements Serializable {
        private String filename;
        private String icon = "https://staticv2.vnptigate.vn/icon/files/512x512/pdf.png";
        private String name;
        private long size;
    }
}
