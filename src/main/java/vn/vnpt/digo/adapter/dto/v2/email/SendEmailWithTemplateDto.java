package vn.vnpt.digo.adapter.dto.v2.email;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import javax.validation.constraints.NotNull;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import vn.vnpt.digo.adapter.dto.IdNameFileDto;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SendEmailWithTemplateDto {

    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;

    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subsystemId;

    @NotNull
    private String subject;

    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId contentTemplate;

    private HashMap<String, Object> params;

    @NotNull
    private String[] emailAddress;

    private File[] attachment;
    
    private IdNameFileDto[] attachmentFile;

}
