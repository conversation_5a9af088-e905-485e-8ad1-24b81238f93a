package vn.vnpt.digo.adapter.dto.minhtue.civilstatus;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(
        name = "hoso"
)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class EndGuardianDossierDto implements Serializable {
    private Long id;
    private String so;
    private String quyenSo;
    private String trangSo;
    private String quyetDinhSo;
    private String ngayDangKy;
    private Long loaiGiamHo;
    private String noiDangKy;
    private String nguoiKy;
    private String chucVuNguoiKy;
    private String nguoiThucHien;
    private String ghiChu;
    private String nghHoTen;
    private String nghGioiTinh;
    private String nghNgaySinh;
    private Long nghDanToc;
    private String nghDanTocKhac;
    private String nghQuocTich;
    private String[] nghQuocTichKhac;
    public String[] getNghQuocTichKhac() {
        String[] temp = nghQuocTichKhac;
        if (nghQuocTichKhac.length > 0) {
            temp = new String[]{"<![CDATA['"};
            for (int i = 0; i < nghQuocTichKhac.length; i++) {
                temp[0] += nghQuocTichKhac[i];
                if (nghQuocTichKhac.length - 1 != i) {
                    temp[0] += ",";
                } else {
                    temp[0] += "']]>";
                }
            }
        }
        return temp;
    }
    private Long nghLoaiCuTru;
    private String nghNoiCuTru;
    private Long nghLoaiGiayToTuyThan;
    private String nghGiayToKhac;
    private String nghSoGiayToTuyThan;
    private String nghNgayCapGiayToTuyThan;
    private String nghNoiCapGiayToTuyThan;
    private String dghHoTen;
    private String dghGioiTinh;
    private String dghNgaySinh;
    private String dghNoiSinh;
    private Long dghDanToc;
    private String dghDanTocKhac;
    private String dghQuocTich;
    private String[] dghQuocTichKhac;
    public String[] getDghQuocTichKhac() {
        String[] temp = dghQuocTichKhac;
        if (dghQuocTichKhac.length > 0) {
            temp = new String[]{"<![CDATA['"};
            for (int i = 0; i < dghQuocTichKhac.length; i++) {
                temp[0] += dghQuocTichKhac[i];
                if (dghQuocTichKhac.length - 1 != i) {
                    temp[0] += ",";
                } else {
                    temp[0] += "']]>";
                }
            }
        }
        return temp;
    }
    private Long dghLoaiCuTru;
    private String dghNoiCuTru;
    private Long dghLoaiGiayToTuyThan;
    private String dghGiayToKhac;
    private String dghSoGiayToTuyThan;
    private String dghNgayCapGiayToTuyThan;
    private String dghNoiCapGiayToTuyThan;
    private String dghLyDoCanGiamHo;
    private String ghNoiDangKyGiamHo;
    private String ghSoDangKyGiamHo;
    private String ghNgayDangKyKetHon;
    private String nycHoTen;
    private String nycQuanHe;
    private Long nycLoaiGiayToTuyThan;
    private String nycGiayToKhac;
    private String nycSoGiayToTuyThan;
    private String nycNgayCapGiayToKhac;
    private String nycNoiCapGiayToKhac;
}
