/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.dto.dlk_vbdlis;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.vbdlis.DanhMucTrangThaiDto;

import java.io.Serializable;
import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DLKVBDLISResDto implements Serializable {

    @JsonProperty("Body")
    private Body body;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Body implements Serializable {

        @JsonProperty("ReceivingDossierResponse")
        private ReceivingDossierResponse receivingDossierResponse;

        @JsonProperty("ResultUpdateDossierResponse")
        private ResultUpdateDossierResponse resultUpdateDossierResponse;

        @JsonProperty("AdditionalStatusUpdateDossierResponse")
        private AdditionalStatusUpdateDossierResponse additionalStatusUpdateDossierResponse;

        @JsonProperty("SendProcessingNoticeDossierResponse")
        private SendProcessingNoticeDossierResponse sendProcessingNoticeDossierResponse;

        @JsonProperty("FeedbackProfileResultDossierResponse")
        private FeedbackProfileResultDossierResponse feedbackProfileResultDossierResponse;
        
        @JsonProperty("DanhMucThuTucResponse")
        private DanhMucThuTucResponse danhMucThuTucResponse;
        
        @JsonProperty("DanhMucNguoiDungResponse")
        private DanhMucNguoiDungResponse danhMucNguoiDungResponse;
        
        @JsonProperty("DanhMucTrangThaiResponse")
        private DanhMucTrangThaiResponse danhMucTrangThaiResponse;
        
        @JsonProperty("YeuCauBoSungResponse")
        private YeuCauBoSungResponse yeuCauBoSungResponse;

        @JsonProperty("GuiKetQuaThueResponse")
        private GuiKetQuaThueResponse guiKetQuaThueResponse;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReceivingDossierResponse implements Serializable {

        public Integer data;
        public Status status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResultUpdateDossierResponse implements Serializable {

        public Integer data;
        public Status status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdditionalStatusUpdateDossierResponse implements Serializable {

        public Integer data;
        public Status status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SendProcessingNoticeDossierResponse implements Serializable {

        public Integer data;
        public Status status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeedbackProfileResultDossierResponse implements Serializable {

        public Integer data;
        public Status status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Status implements Serializable {

        public Boolean success;
        public Integer code;
        public String type;
        public String time;
        public String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhMucThuTucResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;
        
        @JsonProperty("Message")
        public String message;
        
        @JsonProperty("Data")
        public ArrayList<DLKDanhMucThuTucDto> data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThuTucResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;

        @JsonProperty("Message")
        public String message;

        @JsonProperty("Data")
        public DLKDanhMucThuTucDto data;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhMucNguoiDungResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;
        
        @JsonProperty("Message")
        public String message;
        
        @JsonProperty("Data")
        public ArrayList<DLKDanhMucNguoiDungDto> data;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhMucTrangThaiResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;
        
        @JsonProperty("Message")
        public String message;
        
        @JsonProperty("Data")
        public ArrayList<DanhMucTrangThaiDto> data;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class YeuCauBoSungResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;
        
        @JsonProperty("Message")
        public String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GuiKetQuaThueResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;

        @JsonProperty("Message")
        public String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChuyenHoSoResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;

        @JsonProperty("Message")
        public String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NhanBoSungHoSoResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;

        @JsonProperty("Message")
        public String message;
    }
}
