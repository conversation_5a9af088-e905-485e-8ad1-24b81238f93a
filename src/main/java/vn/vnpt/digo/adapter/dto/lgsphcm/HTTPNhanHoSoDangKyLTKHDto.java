/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPDossierDetailHTTPDto;
/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HTTPNhanHoSoDangKyLTKHDto implements Serializable{
    private String authKey;
    private String maDonVi;
    private String maHoSo;
    private String module;
    private String ngayTiepNhan;
    private String data;
      
    public HTTPNhanHoSoDangKyLTKHDto(HCMLGSPDossierDetailHTTPDto dossier, String dossierPlaceAdressCode, String authKey) {
        Logger logger = LoggerFactory.getLogger(HTTPNhanHoSoDangKyDto.class);
        try {
            DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            
            //Set authKey
            this.authKey = authKey;
            
            // Set maDonVi
            this.maDonVi = dossierPlaceAdressCode;
            
            // Set maHoSo
            this.maHoSo = dossier.getCode();
            
            //Set Module
            this.module = "LTKH";
            
            //Set ngayTiepNhan
             if(Objects.nonNull(dossier.getAppliedDate())){
                 String appliedDateTemp = dateFormat.format(dossier.getAppliedDate()).concat(" 00:00:00");
                this.ngayTiepNhan = appliedDateTemp;
            } else {
                this.ngayTiepNhan = "";
            }
             
            //Set Data XML
            HCMLGSPDossierDetailHTTPDto.EformData jsonEFormData = dossier.getEForm().getData();
            SimpleDateFormat dateDataXml = new SimpleDateFormat("dd.MM.yyyy");
                     
            //convert data xml
            String ngayDangKy = "";
            //NgayDangKy
            if(Objects.nonNull(jsonEFormData.getNgayDangKy())){
               ngayDangKy = jsonEFormData.getNgayDangKy();
            }
            
            String huyKetHonNgayGhiChu = "";
            if (Objects.nonNull(jsonEFormData.getHuyKetHonNgayGhiChu()) && jsonEFormData.getHuyKetHonNgayGhiChu() != null && jsonEFormData.getHuyKetHonNgayGhiChu() != "") {
                String regexSrc = "^(19|20)\\d{2}$|^(0[1-9]|1[0-2])\\.(19|20)\\d{2}$|^(0[1-9]|1\\d|2\\d|3[01])\\.(0[1-9]|1[0-2])\\.(19|20)\\d{2}$";
                Pattern patternSrc = Pattern.compile(regexSrc, Pattern.CASE_INSENSITIVE);
                Matcher matcherSrc = patternSrc.matcher(jsonEFormData.getHuyKetHonNgayGhiChu());
                if (matcherSrc.find()) {
                    huyKetHonNgayGhiChu = jsonEFormData.getHuyKetHonNgayGhiChu();
                } else {
                    logger.info("HCMLGSP-HTTP KHLT-Dossier errFormatDate: " + jsonEFormData.getHuyKetHonNgayGhiChu());
                }
            }
            
            String congNhanKetHonNgayGhiChu = "";
            if (Objects.nonNull(jsonEFormData.getCongNhanKetHonNgayGhiChu()) && jsonEFormData.getCongNhanKetHonNgayGhiChu() != null && jsonEFormData.getCongNhanKetHonNgayGhiChu() != "") {
                String regexSrc = "^(19|20)\\d{2}$|^(0[1-9]|1[0-2])\\.(19|20)\\d{2}$|^(0[1-9]|1\\d|2\\d|3[01])\\.(0[1-9]|1[0-2])\\.(19|20)\\d{2}$";
                Pattern patternSrc = Pattern.compile(regexSrc, Pattern.CASE_INSENSITIVE);
                Matcher matcherSrc = patternSrc.matcher(jsonEFormData.getCongNhanKetHonNgayGhiChu());
                if (matcherSrc.find()) {
                    congNhanKetHonNgayGhiChu = jsonEFormData.getCongNhanKetHonNgayGhiChu();
                } else {
                    logger.info("HCMLGSP-HTTP KHLT-Dossier errFormatDate: " + jsonEFormData.getCongNhanKetHonNgayGhiChu());
                }
            }
            
            String chongNgaySinh = "";
            if (Objects.nonNull(jsonEFormData.getChongNgaySinh()) && jsonEFormData.getChongNgaySinh() != null && jsonEFormData.getChongNgaySinh() != "") {
                String regexSrc = "^(19|20)\\d{2}$|^(0[1-9]|1[0-2])\\.(19|20)\\d{2}$|^(0[1-9]|1\\d|2\\d|3[01])\\.(0[1-9]|1[0-2])\\.(19|20)\\d{2}$";
                    Pattern patternSrc = Pattern.compile(regexSrc, Pattern.CASE_INSENSITIVE);
                    Matcher matcherSrc = patternSrc.matcher(jsonEFormData.getChongNgaySinh());
                    if (matcherSrc.find()) {
                        chongNgaySinh = jsonEFormData.getChongNgaySinh();
                    } else {
                        chongNgaySinh = "";
                        logger.info("HCMLGSP-HTTP KHLT-Dossier errFormatDate: " + jsonEFormData.getChongNgaySinh());
                    }
            } 

            
            
            String voNgaySinh = "";
            if (Objects.nonNull(jsonEFormData.getVoNgaySinh()) && jsonEFormData.getVoNgaySinh() != null && jsonEFormData.getVoNgaySinh() != "") {
                String regexSrc = "^(19|20)\\d{2}$|^(0[1-9]|1[0-2])\\.(19|20)\\d{2}$|^(0[1-9]|1\\d|2\\d|3[01])\\.(0[1-9]|1[0-2])\\.(19|20)\\d{2}$";
                    Pattern patternSrc = Pattern.compile(regexSrc, Pattern.CASE_INSENSITIVE);
                    Matcher matcherSrc = patternSrc.matcher(jsonEFormData.getVoNgaySinh());
                    if (matcherSrc.find()) {
                        voNgaySinh = jsonEFormData.getVoNgaySinh();
                    } else {
                        voNgaySinh = "";
                        logger.info("HCMLGSP-HTTP KHLT-Dossier errFormatDate: " + jsonEFormData.getVoNgaySinh());
                    }
            } 

            List<String> chongQuocTichKhacValue = new ArrayList<>();
            List<String> voQuocTichKhacValue = new ArrayList<>();
            
            if (jsonEFormData.getChongQuocTichKhac() != null && !jsonEFormData.getChongQuocTichKhac().isEmpty()) {
                chongQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getChongQuocTichKhac());
            } else {
                chongQuocTichKhacValue = null;
            }
            
            if (jsonEFormData.getVoQuocTichKhac() != null && !jsonEFormData.getVoQuocTichKhac().isEmpty()) {
                voQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getVoQuocTichKhac());
            } else {
                voQuocTichKhacValue = null;
            }  
            
            String result = "<?xmlversion='1.0'encoding='utf-8'?>"
                       + "<hotich><hoso>"
                       + "<ngayDangKy>"+ngayDangKy+"</ngayDangKy>"
                       + "<loaiDangKy>"+jsonEFormData.getLoaiDangKy()+"</loaiDangKy>"
                       + "<noiDangKy>"+jsonEFormData.getNoiDangKy().getValue()+"</noiDangKy>"
                       + "<nguoiKy>"+jsonEFormData.getNguoiKy()+"</nguoiKy>"
                       + "<chucVuNguoiKy>"+jsonEFormData.getChucVuNguoiKy()+"</chucVuNguoiKy>"
                       + "<ngayXacLapQuanHeHonNhan>"+jsonEFormData.getNgayXacLapQuanHeHonNhan()+"</ngayXacLapQuanHeHonNhan>"
                       + "<nguoiThucHien>"+jsonEFormData.getNguoiThucHien()+"</nguoiThucHien>"
                       + "<ghiChu>"+jsonEFormData.getGhiChu()+"</ghiChu>"
                       + "<tinhTrangKetHon>"+jsonEFormData.getTinhTrangKetHon()+"</tinhTrangKetHon>"
                       + "<huyKetHonNgayGhiChu>"+ huyKetHonNgayGhiChu +"</huyKetHonNgayGhiChu>"
                       + "<huyKetHonCanCu>"+jsonEFormData.getHuyKetHonCanCu()+"</huyKetHonCanCu>"
                       + "<congNhanKetHonNgayGhiChu>"+ congNhanKetHonNgayGhiChu +"</congNhanKetHonNgayGhiChu>"
                       + "<congNhanKetHonCanCu>"+jsonEFormData.getCongNhanKetHonCanCu()+"</congNhanKetHonCanCu>"
                       + "<soBanSao>"+jsonEFormData.getSoBanSao()+"</soBanSao>"
                       + "<chongHoTen>"+jsonEFormData.getChongHoTen()+"</chongHoTen>"
                       + "<chongNgaySinh>"+ chongNgaySinh +"</chongNgaySinh>"
                       + "<chongDanToc>"+jsonEFormData.getChongDanToc().getValue()+"</chongDanToc>"
                       + "<chongDanTocKhac>"+jsonEFormData.getChongDanTocKhac()+"</chongDanTocKhac>"
                       + "<chongQuocTich>"+jsonEFormData.getChongQuocTich().getValue()+"</chongQuocTich>"
                       + "<chongQuocTichKhac>"+chongQuocTichKhacValue+"</chongQuocTichKhac>"
                       + "<chongLoaiCuTru>"+jsonEFormData.getChongLoaiCuTru()+"</chongLoaiCuTru>"
                       + "<chongNoiCuTru>"+jsonEFormData.getChongNoiCuTru()+"</chongNoiCuTru>"
                       + "<chongMaNoiCuTru>"+jsonEFormData.getChongMaNoiCuTru()+"</chongMaNoiCuTru>"
                       + "<chongLoaiGiayToTuyThan>"+jsonEFormData.getChongLoaiGiayToTuyThan()+"</chongLoaiGiayToTuyThan>"
                       + "<chongGiayToKhac>"+jsonEFormData.getChongGiayToKhac()+"</chongGiayToKhac>"
                       + "<chongSoGiayToTuyThan>"+jsonEFormData.getChongSoGiayToTuyThan()+"</chongSoGiayToTuyThan>"
                       + "<chongNgayCapGiayToTuyThan>"+jsonEFormData.getChongNgayCapGiayToTuyThan()+"</chongNgayCapGiayToTuyThan>"
                       + "<chongNoiCapGiayToTuyThan>"+jsonEFormData.getChongNoiCapGiayToTuyThan()+"</chongNoiCapGiayToTuyThan>"
                       + "<chongSoLanKH>"+jsonEFormData.getChongSoLanKH()+"</chongSoLanKH>"
                       + "<chongTinhTrangHonNhan>"+jsonEFormData.getChongTinhTrangHonNhan()+"</chongTinhTrangHonNhan>"
                       + "<chongYeuCauXNTTHN>"+jsonEFormData.getChongYeuCauXNTTHN()+"</chongYeuCauXNTTHN>"
                       + "<chongNoiXNTTHN>"+jsonEFormData.getChongNoiXNTTHN()+"</chongNoiXNTTHN>"
                       + "<voHoTen>"+jsonEFormData.getVoHoTen()+"</voHoTen>"
                       + "<voNgaySinh>"+ voNgaySinh +"</voNgaySinh>"
                       + "<voDanToc>"+jsonEFormData.getVoDanToc().getValue()+"</voDanToc>"
                       + "<voDanTocKhac>"+jsonEFormData.getVoDanTocKhac()+"</voDanTocKhac>"
                       + "<voQuocTich>"+jsonEFormData.getVoQuocTich().getValue()+"</voQuocTich>"
                       + "<voQuocTichKhac>"+ voQuocTichKhacValue +"</voQuocTichKhac>"
                       + "<voLoaiCuTru>"+jsonEFormData.getVoLoaiCuTru()+"</voLoaiCuTru>"
                       + "<voNoiCuTru>"+jsonEFormData.getVoNoiCuTru()+"</voNoiCuTru>"
                       + "<voMaNoiCuTru>"+jsonEFormData.getVoMaNoiCuTru()+"</voMaNoiCuTru>"
                       + "<voLoaiGiayToTuyThan>"+jsonEFormData.getVoLoaiGiayToTuyThan()+"</voLoaiGiayToTuyThan>"
                       + "<voGiayToKhac>"+jsonEFormData.getVoGiayToKhac()+"</voGiayToKhac>"
                       + "<voSoGiayToTuyThan>"+jsonEFormData.getVoSoGiayToTuyThan()+"</voSoGiayToTuyThan>"
                       + "<voNgayCapGiayToTuyThan>"+jsonEFormData.getVoNgayCapGiayToTuyThan()+"</voNgayCapGiayToTuyThan>"
                       + "<voNoiCapGiayToTuyThan>"+jsonEFormData.getVoNoiCapGiayToTuyThan()+"</voNoiCapGiayToTuyThan>"
                       + "<voSoLanKH>"+jsonEFormData.getVoSoLanKH()+"</voSoLanKH>"
                       + "<voTinhTrangHonNhan>"+jsonEFormData.getVoTinhTrangHonNhan()+"</voTinhTrangHonNhan>"
                       + "<voYeuCauXNTTHN>"+jsonEFormData.getVoYeuCauXNTTHN()+"</voYeuCauXNTTHN>"
                       + "<voNoiXNTTHN>"+jsonEFormData.getVoNoiXNTTHN()+"</voNoiXNTTHN>"
                       + "</hoso></hotich>";
        
        //Set dataMml 
        this.data = result;
        logger.info("HCMLGSP-HTTP Get DATA XML eform dossier: " + result);
 
        } catch (Exception e) {
            logger.info("erro mapping data HTTPDossier:  " + e.getMessage());
        }
    }
    
    private List<String> getQuocTichKhacArr(List<HCMLGSPDossierDetailHTTPDto.LableData> quocTichKhacArr){
        return quocTichKhacArr.stream().map(HCMLGSPDossierDetailHTTPDto.LableData::getValue).collect(Collectors.toList());
    }
}
