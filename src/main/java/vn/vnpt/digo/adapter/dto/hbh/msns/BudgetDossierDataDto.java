/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.hbh.msns;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "RETURN")
public class BudgetDossierDataDto implements Serializable {
    
    private String hsid;
    private String cqtc_ma;
    private String ten_hs;
    private String ma;
    private String kieu_hs;
    private String nguoi_dk;
    private String email;
    private String sdt_didong;
    private String ngay_dk;
    private String nguoi_tao;
    private String ngay_tao;
    private String ngay_tra;
    private String nguoi_pd;
    private String ngay_pd;
    private int trang_thai;
    private int kieu_tiep_nhan;
    private BudgetRow row;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @XmlRootElement(name = "ROW")
    public static class BudgetRow implements Serializable {

        @JsonProperty("DATA_PROCESS")
        private ArrayList<BudgetDataProcess> DATA_PROCESS;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @XmlRootElement(name = "DATA_PROCESS")
    public static class BudgetDataProcess implements Serializable {

        @JsonProperty("ID")
        private String ID;

        @JsonProperty("TRANG_THAI_MA")
        private String TRANG_THAI_MA;

        @JsonProperty("TRANG_THAI_TEN")
        private String TRANG_THAI_TEN;

        @JsonProperty("CAN_BO_XL")
        private String CAN_BO_XL;

        @JsonProperty("NGAY_XL")
        private String NGAY_XL;

        @JsonProperty("DON_VI_XL")
        private String DON_VI_XL;

        @JsonProperty("NOI_DUNG_XL")
        private String NOI_DUNG_XL;

    }
    
}

