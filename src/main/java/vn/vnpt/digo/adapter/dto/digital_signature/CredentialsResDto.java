package vn.vnpt.digo.adapter.dto.digital_signature;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CredentialsResDto implements Serializable {
    @JsonProperty("credentialID")
    private String credentialID;
    @JsonProperty("status")
    private String status;
    @JsonProperty("statusDescription")
    private String statusDescription;
    @JsonProperty("issuerDN")
    private String issuerDN;
    @JsonProperty("subjectDN")
    private String subjectDN;
    @JsonProperty("issuerName")
    private String issuerName;
    @JsonProperty("commonName")
    private String commonName;
    @JsonProperty("notAffter")
    private String notAffter;
    @JsonProperty("notBefore")
    private String notBefore;
}
