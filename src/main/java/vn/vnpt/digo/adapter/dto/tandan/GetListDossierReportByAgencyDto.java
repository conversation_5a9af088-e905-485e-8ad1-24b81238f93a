
package vn.vnpt.digo.adapter.dto.tandan;

import java.io.Serializable;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.ErrorTanDan;
import vn.vnpt.digo.adapter.pojo.ListDossierReportByAgencyId;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetListDossierReportByAgencyDto implements Serializable {
    private ErrorTanDan error;
    
    private String maDonVi;
    
    private ArrayList<ListDossierReportByAgencyId> data;
}
