package vn.vnpt.digo.adapter.dto.Integration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * national: Tr<PERSON><PERSON> thái hồ sơ của DVCQG
 * description: Mô tả trạng thái hồ sơ của DVCQG
 * dossierStatusMaps.id: mã trạng thái hồ sơ igate
 * dossierStatusMaps.name: tên trạng thái hồ sơ igate
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatusMap implements Serializable {
    private String national;
    private String description;
    private List<StatusDescription> dossierStatusMaps;
    private List<StatusDescription> taskStatusMaps;
    private List<StatusDescription> remindStatusMaps;
    private String integrated;

    public static List<String> getAlwayUsing(){
        return List.of("4","5","7","10");
    }

    public static List<String> getHardUsing(){
        return List.of("5","7","8");
    }

    public StatusMap(String national, String description, List<String> dossierStatus, List<StatusDescription> allDossierStatusMaps){
        this.national = national;
        this.description = description;
        this.dossierStatusMaps = this.getDossierStatusMaps(dossierStatus,allDossierStatusMaps);
    }

    private List<StatusDescription> getDossierStatusMaps(List<String> items, List<StatusDescription> dossierStatusMaps){
        if(Objects.nonNull(items) && items.size() > 0){
            String firstValue = items.get(0);
            List<StatusDescription> fitems = dossierStatusMaps.stream().filter(i->items.contains(i.getId())).map(i->new StatusDescription(i.getId(),i.getName(),firstValue.equals(i.getId())?1:0)).collect(Collectors.toList());
            return fitems;
        }
        return new ArrayList<>();
    }

    public static List<StatusMap> initStatus(Integer processIs3Step){
        List<StatusMap> items  = new ArrayList<>();
        List<StatusDescription> dossierStatusMaps = StatusDescription.initDossierStatusMaps();
        items.add(new StatusMap("1","Mới đăng ký",List.of("0","14","15"),dossierStatusMaps));
        items.add(new StatusMap("2","Được tiếp nhận",List.of(""),dossierStatusMaps));
        items.add(new StatusMap("3","Không được tiếp nhận",List.of("6","19"),dossierStatusMaps));
        items.add(new StatusMap("4","Đang xử lý",List.of("2","3","8","9","10","11","16","18"),dossierStatusMaps));
        items.add(new StatusMap("5","Yêu cầu bổ sung giấy tờ",List.of("1","21","23"),dossierStatusMaps));
        items.add(new StatusMap("6","Yêu cầu thực hiện nghĩa vụ tài chính",List.of("0"),dossierStatusMaps));
        items.add(new StatusMap("7","Công dân yêu cầu rút hồ sơ",List.of("13"),dossierStatusMaps));
        items.add(new StatusMap("8","Dừng xử lý",List.of("12"),dossierStatusMaps));
        items.add(new StatusMap("9","Đã xử lý xong",List.of("4","20","22"),dossierStatusMaps));
        items.add(new StatusMap("10","Đã trả kết quả",List.of("5"),dossierStatusMaps));
        if(processIs3Step==1){
            List<String> alwayUsing = StatusMap.getAlwayUsing();
            items = items.stream().filter(i->alwayUsing.contains(i.national)).collect(Collectors.toList());
        }
        return items;
    }
}
