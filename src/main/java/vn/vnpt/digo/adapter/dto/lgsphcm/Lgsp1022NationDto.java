/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Lgsp1022NationDto implements Serializable{
    private String code;
    private String name;
    
    @JsonSetter("MAQUOCGIA")
    public void mapperNationCode(String code) {
        this.code = code;
    }
    @JsonSetter("TEN")
    public void mapperNationName(String name) {
        this.name = name;
    }
}
