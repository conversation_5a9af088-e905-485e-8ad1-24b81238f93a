package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsReporterTypeResponseDto implements Serializable {

    private List<NpsReporterTypeResultDto> result;

    private Integer errorCode;

    @JsonSetter("error_code")
    public void mapperErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    @JsonSetter("DoiTuongPAKN")
    public void mapperResult(List<NpsReporterTypeResultDto> result) {
        this.result = result;
    }
}
