package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsProceduresRequestDto implements Serializable {

    @NotNull
    private String session;

    @NotNull
    @JsonProperty("madonvi")
    private String agencyCode;

    @NotNull
    private String service;
}
