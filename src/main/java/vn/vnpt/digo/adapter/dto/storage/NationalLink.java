package vn.vnpt.digo.adapter.dto.storage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.CategoryResultResponseFile;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NationalLink {
    private String filename;
    private String url;

    public NationalLink(CategoryResultResponseFile obj){
        this.filename = obj.getTenTep();
        this.url = obj.getDuongDan();
    }
}
