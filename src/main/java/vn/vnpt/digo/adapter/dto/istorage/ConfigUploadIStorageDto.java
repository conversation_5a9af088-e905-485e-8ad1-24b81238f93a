package vn.vnpt.digo.adapter.dto.istorage;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigUploadIStorageDto implements Serializable {
    private String departmentCode;
    private int folderId;
    private String storagePeriodId;
    private String backupPlan;
    private String modeOfUse;
    private String physicalState;
    private String sender;
}
