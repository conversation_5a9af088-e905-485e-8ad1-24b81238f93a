/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LGSPHCMCivilStatusRegistrationDto implements Serializable{
    private int StatusCode;
    private String Description;
    //private ResultObject;
    private String ResultType;
    private String Status;
    private Boolean ThrowException;
}
