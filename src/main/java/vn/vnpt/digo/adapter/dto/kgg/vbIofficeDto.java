package vn.vnpt.digo.adapter.dto.kgg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class vbIofficeDto {

    private StatusDTO status;
    private List<DataDTO> data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusDTO {
        private String code;
        private String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataDTO {
        private String id;
        private String trichYeu;
        private String soKihieu;
        private String coQuanBanHanh;
        private String ngayVanBan;
        private String hanxuly;
        private String ngayNhan;
        private String doKhan;
        private String congVanDenDi;
        private String processDefinitionId;
        private String processInstanceId;
        private String isRead;
        private String hasFile;
        private String isComment;
        private String isCheck;
        private String isChuTri;
        private String layLai;
        private String isSign;
        private String isChuyenTiep;
        private String role;
        private String type;
        private String isPhucDap;
        private String noiDungThamMuu;
        private String thoiGianGiaHan;
        private String reason;
        private String phucDap;
        private String hanXuLy;
    }
}