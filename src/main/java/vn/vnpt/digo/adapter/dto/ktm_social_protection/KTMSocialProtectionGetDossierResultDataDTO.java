package vn.vnpt.digo.adapter.dto.ktm_social_protection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class KTMSocialProtectionGetDossierResultDataDTO implements Serializable {
    public String loaiDuLieu;

    public String maHoSo;

    public String maLinhVuc;

    public String tenLinhVuc;

    public String kenhThucHien;

    public String loaiDoiTuong;

    public String hinhThucTra;

    public String maTTHC;

    public String tenTTHC;

    public String hoVaTen;

    public String ngayThangNamSinh;

    public String gioiTinhId;

    public String danTocId;

    public String cmnd;

    public String cmnD_NgayCap;

    public String cmnD_NoiCap;

    public String maTinh;

    public String maHuyen;

    public String maXa;

    public String hktT_MaTinh;

    public String hktT_MaHuyen;

    public String hktT_MaXa;

    public String hktT_MaThon;

    public String hktT_ChiTiet;

    public String nohT_MaTinh;

    public String nohT_MaHuyen;

    public String nohT_MaXa;

    public String nohT_MaThon;

    public String nohT_ChiTiet;

    public String soDienThoai;

    public String email;

    public String ngayTiepNhan;

    public String donViXuLy;

    public String noiNopHoSo;

    public String hoSoCoThanhPhanSoHoa;

    public String taiKhoanDuocXacThucVoiVNeID;

    public String duocThanhToanTrucTuyen;

    public String loaiDinhDanh;

    public String soDinhDanh;

    public String maCSDL;

    public String statusId;

    public String urlDetail;

    public String submitedDate;

    public String certificateExtentData;

    public Fee maiTangPhi;
}