package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.CheckConfigParams;
import vn.vnpt.digo.adapter.util.ParamName;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigParams
public class LgspTanDanParamDto implements Serializable {
    @ParamName("config-id")
    private ObjectId configId;

    @ParamName("agency-id")
    private ObjectId agencyId;

    @ParamName("subsystem-id")
    private ObjectId subsystemId;

    private String msdn;

    private String fromts;

    private String tots;

    private String offset;

    private String limit;

    private String fromdate;
    
    private String todate;

    private String journalno;
}
