package vn.vnpt.digo.adapter.dto.bdg;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VNPTPayVietQrCheckOrderResponseBDGDto implements Serializable {

    @JsonProperty("responseCode")
    public String responseCode = null;

    @JsonProperty("description")
    public String description = null;

    @JsonProperty("merchantClientId")
    public String merchantClientId = null;

    @JsonProperty("merchantCode")
    public String merchantCode = null;

    @JsonProperty("terminalId")
    public String terminalId = null;

    @JsonProperty("billNumber")
    public String billNumber = null;

    @JsonProperty("txnId")
    public String txnId = null;

    @JsonProperty("amount")
    public String amount = null;

    @JsonProperty("customerCode")
    public String customerCode = null;

    @JsonProperty("customerName")
    public String customerName = null;

    @JsonProperty("customerAddress")
    public String customerAddress = null;

    @JsonProperty("customerPhone")
    public String customerPhone = null;

    @JsonProperty("paymentCode")
    public String paymentCode = null;

    @JsonProperty("createDate")
    public String createDate = null;

    @JsonProperty("checksum")
    public String checksum = null;


}
