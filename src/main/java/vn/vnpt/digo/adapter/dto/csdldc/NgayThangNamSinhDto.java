package vn.vnpt.digo.adapter.dto.csdldc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NgayThangNamSinhDto implements Serializable {
    @JsonProperty("NgayThangNam")
    private String NgayThangNam;

    @JsonProperty("Nam")
    private long Nam;
}
