package vn.vnpt.digo.adapter.dto.industry_trade_bd;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserTransformDto implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    @JsonProperty("HoTen")
    private String HoTen;

    @JsonProperty("NgaySinh")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date NgaySinh;

    @JsonProperty("GioiTinh")
    private Byte GioiTinh;

    @JsonProperty("SoDienThoai")
    private ArrayList<String> SoDienThoai;

    @JsonProperty("Email")
    private ArrayList<String> Email;

    public void setSoDienThoai(ArrayList<UserDto.PhoneNumber> lsSDT){
        ArrayList<String> soDienThoai = new ArrayList<>();
        for (UserDto.PhoneNumber sdt:lsSDT) {
            soDienThoai.add(sdt.getValue());
        }
        this.SoDienThoai = soDienThoai;
    }

    public void setEmail(ArrayList<UserDto.Email> lsEmail){
        ArrayList<String> emailN = new ArrayList<>();;
        for (UserDto.Email email:lsEmail) {
            emailN.add(email.getValue());
        }
        this.Email = emailN;
    }

    @JsonProperty("SoDienThoai")
    public ArrayList<String> getSoDienThoai(){
        return this.SoDienThoai;
    }

    @JsonProperty("Email")
    public ArrayList<String> getEmail(){
        return this.Email;
    }
}
