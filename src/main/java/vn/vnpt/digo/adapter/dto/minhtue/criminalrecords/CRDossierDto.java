package vn.vnpt.digo.adapter.dto.minhtue.criminalrecords;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CRDossierDto implements Serializable {
    private Applicant applicant;
    private Eform eForm;
    private Kind dossierReceivingKind = new Kind("0");
    private TaskStatus dossierTaskStatus;
    private Procedure procedure;
    private Place receivingPlace;
    private Sync sync;
    private String infoType;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Applicant implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId eformId;
        private ApplicantEform data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplicantEform implements Serializable {

        private String address;
        private String fullname;
        private String phoneNumber;
        private String email;
        private String identityNumber;
        private Date birthday;
        private Date identityDate;
        private Integer gender;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Procedure implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String code;
        private Tag sector;
        private List<Tag> agency;
        private List<Trans> translate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Tag implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String code;
        private List<Trans> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RawTag implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String code;
        private List<Trans> trans;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskStatus implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private List<Trans> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Kind implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id = new ObjectId("5f8968888fffa53e4c073ded");
        private List<Trans> name = new ArrayList<>() {
            {
                add(new Trans((short)228, "Tr\u1ef1c ti\u1ebfp"));
            }
        };

        public Kind(String type){
            switch(type){
                case "0": {
                    this.id = new ObjectId("5f8968888fffa53e4c073ded");
                    this.name = new ArrayList<>() {
                        {
                            add(new Trans((short)228, "Tr\u1ef1c ti\u1ebfp"));
                        }
                    };
                    break;
                }
                case "1": {
                    this.id = new ObjectId("5f8969018fffa53e4c073dee");
                    this.name = new ArrayList<>() {
                        {
                            add(new Trans((short)228, "G\u1eedi h\u1ed3 s\u01a1 c\u1ee7a t\u00f4i \u0111\u1ebfn \u0111\u1ecba ch\u1ec9"));
                        }
                    };
                    break;
                }
            }
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Sync implements Serializable {

        private String sourceCode;
        private Integer typeId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Trans implements Serializable {

        private Short languageId;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Place implements Serializable {
        private String fullAddress;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Eform implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private EformData data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EformData implements Serializable {

        private DeclarationWsForm declarationForm;
        private List<ResidenceWSForm> residenceForm = new ArrayList<>();
        private MandatorWSForm mandatorForm;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MandatorWSForm implements Serializable {

        private String fullName = "";
        private String birthDateStr = "";
        private String residence = "";
        private String idTypeId = "";
        private String idIssueDate = "";
        private String mandatorRelation = "";
        private String genderId = "";
        private String birthPlaceId = "";
        private String regionId = "";
        private String identifyNo = "";
        private String idIssuePlace = "";
        private String mandatorDate = "";
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResidenceWSForm implements Serializable {

        private String fromDateStr = "";
        private String toDateStr = "";
        private String residencePlace = "";
        private String jobName = "";
        private String workPlace = "";
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DeclarationWsForm implements Serializable {

        private String fullName = "";
        private String birthDateStr = "";
        private String otherName = "";
        private String genderId = "";
        private String birthPlace = "";
        private String nationalityId = "";
        private String residence = "";
        private String residenceTemporary = "";
        private String idTypeId = "";
        private String idIssueDate = "";
        private String phone = "";
        private String ethnicId = "";
        private String reRegionId = "";
        private String rtRegionId = "";
        private String identifyNo = "";
        private String idIssuePlace = "";
        private String email = "";
        private String dadName = "";
        private String momName = "";
        private String partnerName = "";
        private String dadDob = "";
        private String momDob = "";
        private String partnerDob = "";
        private String declareTypeId = "";
        private String purpose = "";
        private String requestQty = "";
        private String agencyRequestId = "";
        private String note = "";
        private String objectRequestId = "";
        private String formType = "";
        private String regionRequestId = "";
        private String isBanPosition = "";
        private String delivery = "";
        private String deliveryAddress = "";
        private String deliveryDistrict = "";
        private String requestQtyAdd = "";
    }
}
