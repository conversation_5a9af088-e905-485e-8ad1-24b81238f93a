package vn.vnpt.digo.adapter.dto.hbh.btxh;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.SidNameDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.pojo.ProcedureForm;
import vn.vnpt.digo.adapter.pojo.Translate;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DossierDetailDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private String id;
    private String code;
    private IdNameCodeStringDto procedure;
    private ProcessDto procedureProcessDefinition;
    private ApplyMethodDto applyMethod;
    private ApplicantDto applicant;
    private AgencyDto agency;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date acceptedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date dueDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date waitUpdateDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private IdCodeNameSimpleDto dossierTaskStatus;
    private DossierStatus dossierStatus;
    private IdCodeNameDto dossierReceivingKind;
    private List<FormFile> dossierFormFile;
    private List<AttachmentDto> attachment;
    private List<TaskDto> task;
    @JsonProperty("eForm")
    private FormIO eForm;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AgencyDto {
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private ArrayList<Translate> name;
        private String code;
        private AgencyDto parent;
        public String getTransName(){
            String result = "";
            if (Objects.nonNull(name)) {
                if(name.size() == 1){
                    result = name.get(0).getName();
                } else {
                    for (Translate item : name) {
                        if (Objects.equals(item.getLanguageId(), (short) 228)) {
                            result = item.getName();
                        }
                    }
                }
            }
            return result;
        }
        
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdNameCodeDto implements Serializable {
        private String id;
        private String code;
        private List<Translate> name;
        public String getTransName(){
            String result = "";
            if (Objects.nonNull(name)) {
                if(name.size() == 1){
                    result = name.get(0).getName();
                } else {
                    for (Translate item : name) {
                        if (Objects.equals(item.getLanguageId(), (short) 228)) {
                            result = item.getName();
                        }
                    }
                }
            }
            return result;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdNameCodeStringDto implements Serializable{

        private String id;

        private ArrayList<Translate> translate;

        private String code;

        private IdNameCodeDto sector;

        public String getTransName(){
            String result = "";
            if (Objects.nonNull(translate)) {
                if(translate.size() == 1){
                    result = translate.get(0).getName();
                } else {
                    for (Translate item : translate) {
                        if (Objects.equals(item.getLanguageId(), (short) 228)) {
                            result = item.getName();
                        }
                    }
                }
            }
            return result;
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplyMethodDto {
        private Integer id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplicantDto {
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId userId;
        private String eformId;
        private EformData data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EformData{
        private String fullname;
        private String identityNumber;
        private String identityDate;
        private SidNameDto identityAgency;
        private String birthday;
        private String phoneNumber = "";
        private String email = "";
        private String fax = "";
        private String ownerFullname;
        private String address = "";
        private String gender = "1";
        private String ownerIdentityNumber;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FormFile{
        private List<AttachmentDto> file;
        private ProcedureForm procedureForm;
        private Requirement requirement;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AttachmentDto {

        private String filename;
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId group;
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private Long size;
        private Integer isNational;
        private Integer reused;
        private String repCode;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AttachmentStorageDto {

        private String code;
        private String filename;
        private String fileURL;
        
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DossierStatus {
        private Integer id;
        private ArrayList<Translate> name;
        private String comment;
        private String description;
        
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskDto {
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date createdDate;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date completedDate;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date confirmFFODate;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date dueDate;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Requirement {

        private Integer quantity;
        private String typeId;
        private String typeName;
        
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FormIO{
        private String id;
        private EFormDataDto data;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProcessDto implements Serializable {

        private String id;
        private Object processDefinition;
    }
}
