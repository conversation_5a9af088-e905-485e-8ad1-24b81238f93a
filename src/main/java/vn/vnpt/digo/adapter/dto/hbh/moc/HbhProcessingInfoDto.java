package vn.vnpt.digo.adapter.dto.hbh.moc;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HbhProcessingInfoDto {
    @JsonProperty("MaTrangThai")
    private String MaTrangThai; //Theo danh muc trang thai QG
    @JsonProperty("NguoiXuLy")
    private String NguoiXuLy;
    @JsonProperty("ChucDanh")
    private String ChucDanh;
    @JsonProperty("ThoiDiemXuLy")
    private String ThoiDiemXuLy;
    @JsonProperty("PhongBanXuLy")
    private String PhongBanXuLy="";
    @JsonProperty("NoiDungXuLy")
    private String NoiDungXuLy="";
    @JsonProperty("NgayBatDau")
    private String NgayBatDau;
    @JsonProperty("NgayKetThucTheoQuyDinh")
    private String NgayKetThucTheoQuyDinh;
}