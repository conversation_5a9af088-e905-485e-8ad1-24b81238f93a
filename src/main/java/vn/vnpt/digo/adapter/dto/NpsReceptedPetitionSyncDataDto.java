package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsReceptedPetitionSyncDataDto implements Serializable {

    @JsonProperty("MaSoPAKN")
    private String code;

    @JsonSetter("code")
    public void mapperCode(String code) {
        this.code = code;
    }
}
