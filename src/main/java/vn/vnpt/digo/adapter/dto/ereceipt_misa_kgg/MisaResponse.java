/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.ereceipt_misa_kgg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MisaResponse {
    public Boolean Success;
    public String Data;
    public String NewData;
    public String[] Errors;
    public String ErrorCode;
    public String ErrorCodeDetail;
}
