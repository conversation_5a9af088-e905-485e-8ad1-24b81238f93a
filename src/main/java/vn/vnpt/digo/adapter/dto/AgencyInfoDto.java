package vn.vnpt.digo.adapter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgencyInfoDto implements Serializable {
    private String id;
    private String code;
    private String typeCode;
    private String name;
    private Temp parent;
    private String parentCode;
    private String levelId;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Temp implements Serializable {
        private String id;
        private String name;
        private String code;
    }
}
