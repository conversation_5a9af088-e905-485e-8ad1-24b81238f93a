package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.pojo.IntegratedService;
import vn.vnpt.digo.adapter.pojo.Translate;

import java.io.Serializable;
import java.util.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogSmsStatisticsWithSectorDto implements Serializable {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private IdCodeNameSimpleDto item;
    private String message;
    private HashMap<String, Object> extend;
    private String agencyName;
    private String agencyId;
    private Integer sendStatus;
    private String sectorId;
    private String sectorName;
    private String sectorCode;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

    public void setData(String id, String code, String name) {
        this.sectorId = id;
        this.sectorCode = code;
        this.sectorName = name;
        HashMap<String, Object> agencies = (HashMap<String, Object>) this.extend.get("agencies");
        if(Objects.nonNull(agencies)) {
            HashMap<String, Object> agency = (HashMap<String, Object>) agencies.get("agency");
            this.agencyName = agency.get("name").toString();
            this.agencyId = agency.get("id").toString();
        }
        else {
            this.agencyName = "Chưa xác định";
            this.agencyId = "404";
        }
        this.sendStatus = Integer.parseInt(this.extend.get("sendStatus").toString());
    }
}
