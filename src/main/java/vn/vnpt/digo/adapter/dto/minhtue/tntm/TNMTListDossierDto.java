package vn.vnpt.digo.adapter.dto.minhtue.tntm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import vn.vnpt.digo.adapter.dto.PadPAddReqDto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TNMTListDossierDto implements Serializable {
//    private ArrayList<Map<String, Object>> ArrayOfHoSoMotCua;
    private String MaHoSo;
    private String MaTTHC;
    private String TenTTHC;
    private String MaLinhVuc ;
    private String TenLinhVuc ;
    private Integer <PERSON>h<PERSON><PERSON><PERSON><PERSON>ien ;
    private ChuHoSo ChuHoSo;
    private String MaHuyen ;
    private String LoaiDoiTuong ;
    private String MaDoiTuong;
    private String  ThongTinKhac;
    private String Email ;
    private String Fax;
    private String SoDienThoai;
    private String TrichYeuHoSo;
    private String NgayTiepNhan ;
    private String NgayHenTra ;
    private String TrangThaiHoSo;
    private String NgayTra;
    private String ThongTinTra;
    private String HinhThuc ;
    private String NgayKetThucXuLy;
    private String DonViXuLy;
    private String GhiChu;
    private DonDangKy DonDangKy;
//    private ArrayList<TaiLieuNop> TaiLieuNop;
    private ArrayList<DanhSachLePhi> DanhSachLePhi;
    private ArrayList<DanhSachTepDinhKem> DanhSachTepDinhKemKhac;
    private ArrayList<DanhSachHoSoBoSung> DanhSachHoSoBoSung;
    private ArrayList<DanhSachGiayToKetQua> DanhSachGiayToKetQua;
    private ArrayList<QuaTrinhXuLy> QuaTrinhXuLy;
    private List<TaiLieuNop> TaiLieuNop;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocsSubmitDto implements Serializable {

        @JsonProperty("TenTepDinhKem")
        private String TenTepDinhKem;

        @JsonIgnore
        @JsonProperty("Base64")
        private String Base64;
    }
}
