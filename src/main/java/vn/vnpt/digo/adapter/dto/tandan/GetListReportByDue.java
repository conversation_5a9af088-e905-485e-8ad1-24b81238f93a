
package vn.vnpt.digo.adapter.dto.tandan;

import java.io.Serializable;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.ErrorTanDan;
import vn.vnpt.digo.adapter.pojo.ListReportByDue;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetListReportByDue implements Serializable {
    private ErrorTanDan error;
    
    private ArrayList<ListReportByDue> data;
}
