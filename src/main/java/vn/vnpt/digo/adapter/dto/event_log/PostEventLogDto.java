package vn.vnpt.digo.adapter.dto.event_log;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import vn.vnpt.digo.adapter.document.EventLogs;
import vn.vnpt.digo.adapter.dto.NameValueDto;
import vn.vnpt.digo.adapter.dto.SidNameDto;
import vn.vnpt.digo.adapter.properties.DigoProperties;
import vn.vnpt.digo.adapter.util.Converter;
import vn.vnpt.digo.adapter.util.Microservice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.lang.annotation.Documented;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PostEventLogDto implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId pid;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId serviceId;

    private SidNameDto key;

    private EventLogRequest req;

    private Object res;

    private String errMsg;

    private boolean status;

    public void setRequest(HttpServletRequest request, Object body){
        EventLogRequest req = new EventLogRequest();
        req.setMethod(request.getMethod());
        Enumeration<String> headerNames = request.getHeaderNames();
        List<NameValueDto> headers = new ArrayList<NameValueDto>();
        ArrayList<String> ArrayParam = new ArrayList<String>(
                Arrays.asList("authorization", "content-type", "accept"));
        ArrayParam.forEach(item ->{
            NameValueDto data = new NameValueDto();
            data.setName(item);
            data.setValue(request.getHeader(item));
            headers.add(data);
        });
        req.setHeader(headers);
        req.setBody(body);
        String url = request.getRequestURL().toString();
        try{
            if (request.getRequestURL().toString().contains("443")) {
                url = request.getRequestURL().toString().replace("443", "/ad");
            }
        }catch (Exception e){

        }
        StringBuilder requestURL = new StringBuilder(url);
        String queryString = request.getQueryString();
        if (queryString != null) {
            requestURL = requestURL.append('?').append(queryString);
        }
        req.setUri(requestURL.toString());
        this.setReq(req);
    }

    public void setRequestAdapter(HttpServletRequest request, Object body,String serviceName){
        EventLogRequest req = new EventLogRequest();
        req.setMethod(request.getMethod());
        Enumeration<String> headerNames = request.getHeaderNames();
        List<NameValueDto> headers = new ArrayList<NameValueDto>();
        ArrayList<String> ArrayParam = new ArrayList<String>(
                Arrays.asList("authorization", "content-type", "accept"));
        ArrayParam.forEach(item ->{
            NameValueDto data = new NameValueDto();
            data.setName(item);
            data.setValue(request.getHeader(item));
            headers.add(data);
        });
        req.setHeader(headers);
        req.setBody(body);

        String  httpProtocol = request.getServerPort() != 80 ? "http" : "https";
        String scheme = httpProtocol;
        String serverPort = request.getServerPort() == 80 ? "" : request.getServerPort() + "";
        String serverName = request.getServerName() + serverPort;
        String serverUri = request.getRequestURI();
        String queryString = request.getQueryString();
        String newServiceName = request.getServerPort() != 80 ? "" : "/" + serviceName;
        String url = scheme + "://" + serverName + newServiceName + serverUri;
        try{
            if (url.contains("http://apidvc.baclieu.gov.vn443")) {
                url = url.replace("http://apidvc.baclieu.gov.vn443", "http://apidvc.baclieu.gov.vn/ad");
            }
        }catch (Exception e){

        }
        StringBuilder requestURL = new StringBuilder(url);
        if (queryString != null) {
            requestURL = requestURL.append('?').append(queryString);
        }
        req.setUri(requestURL.toString());
        this.setReq(req);
    }

}
