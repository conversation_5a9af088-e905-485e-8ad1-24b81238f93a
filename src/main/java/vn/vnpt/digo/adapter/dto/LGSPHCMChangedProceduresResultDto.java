/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LGSPHCMChangedProceduresResultDto implements Serializable {
    
    private String code;

    private String name;

    private String agencyCode;

    private String changedDate;

    @JsonSetter("MACOQUAN")
    public void mapperAgencyCode(String agencyCode) {
        this.agencyCode = agencyCode;
    }

    @JsonSetter("TEN")
    public void mapperName(String name) {
        this.name = name;
    }

    @JsonSetter("MA")
    public void mapperCode(String code) {
        this.code = code;
    }

    @JsonSetter("NGAYSUA")
    public void mapperChangedDate(String changedDate) {
        this.changedDate = changedDate;
    }
}
