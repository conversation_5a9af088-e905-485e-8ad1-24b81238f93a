/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import com.fasterxml.jackson.core.type.TypeReference;
import java.lang.reflect.Array;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPDossierDetailHTTPDto;

/**
 *
 * <AUTHOR> Pham
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HTTPNhanHoSoDangKyDto implements Serializable {
    private String maDonVi;
    private String maHoSo;
    private String module;
    private String ngayTiepNhan;
    private String data;
    
    public HTTPNhanHoSoDangKyDto(HCMLGSPDossierDetailHTTPDto dossier, String dossierPlaceAdressCode) {
        Logger logger = LoggerFactory.getLogger(HTTPNhanHoSoDangKyDto.class);
        try {
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            Date today = new Date();
            
            // Set maDonVi
            this.maDonVi = dossierPlaceAdressCode;
            
            // Set maHoSo
            this.maHoSo = dossier.getCode();
            
            //Set Module
            this.module = dossier.getEForm().getData().getLoaiHTTP();
            
            //Set ngayTiepNhan
             if(Objects.nonNull(dossier.getAppliedDate())){
                this.ngayTiepNhan = dateFormat.format(dossier.getAppliedDate());
            } else {
                this.ngayTiepNhan = dateFormat.format(today);
            }
             
            //Set Data XML
            HCMLGSPDossierDetailHTTPDto.EformData jsonEFormData = dossier.getEForm().getData();
            String HTTPType = dossier.getEForm().getData().getLoaiHTTP();
            SimpleDateFormat dateDataXml = new SimpleDateFormat("dd.MM.yyyy");
                     
            //Data eform "KS"
            String ngayDangKy = "";
            String nksNgaySinh = "";
            String meNgaySinh = "";
            String chaNgaySinh = "";
            List<String> nksQuocTichKhacValue = new ArrayList<>();
            List<String> meQuocTichKhacValue = new ArrayList<>();
            List<String> chaQuocTichKhacValue = new ArrayList<>();
            
                   
            // Data eform "HN" 
            String nxnNgaySinh = "";
            List<String> nxnQuocTichKhacValue = new ArrayList<>();
            
            //Data eform "LH"
            String chongNgaySinh = "";
            String voNgaySinh = "";
            List<String> chongQuocTichKhacValue = new ArrayList<>();
            List<String> voQuocTichKhacValue = new ArrayList<>();

            // Data eform "CMC"
            String cmNgaySinh = "";
            List<String> cmQuocTichKhacValue = new ArrayList<>();
            List<String> ncQuocTichKhacValue = new ArrayList<>();
            
            // Data eform "GH" 
            List<String> nghQuocTichKhacValue = new ArrayList<>();
            List<String> dghQuocTichKhacValue = new ArrayList<>();
            
            // Data eform "KT"
            List<String> nktQuocTichKhacValue = new ArrayList<>();
            String nktNgaySinh = "";
            
            //NgayDangKy
            if(Objects.nonNull(jsonEFormData.getNgayDangKy())){
               ngayDangKy = jsonEFormData.getNgayDangKy();
            } else {
               ngayDangKy = dateFormat.format(today);
            }
            //NKSNgaySinh
            if(Objects.nonNull(jsonEFormData.getNksNgaySinh())){
               nksNgaySinh = jsonEFormData.getNksNgaySinh();
            } else {
               nksNgaySinh = dateFormat.format(today);
            }
            //meNgaySinh
            if(!jsonEFormData.getMeNgaySinh().equalsIgnoreCase("") && Objects.nonNull(jsonEFormData.getMeNgaySinh())){
               meNgaySinh = jsonEFormData.getMeNgaySinh();
            } else {
               meNgaySinh = "";
            }
            //chaNgaySinh
            if(!jsonEFormData.getChaNgaySinh().equalsIgnoreCase("") && Objects.nonNull(jsonEFormData.getChaNgaySinh())){
               chaNgaySinh = jsonEFormData.getChaNgaySinh();
            } else {
               chaNgaySinh = "";
            }
            
            //NxnNgaySinh
            if(Objects.nonNull(jsonEFormData.getNxnNgaySinh())){
               nxnNgaySinh = jsonEFormData.getNxnNgaySinh();
            } else {
               nxnNgaySinh = dateFormat.format(today);
            }
            
            //getCmNgaySinh
            if(Objects.nonNull(jsonEFormData.getCmNgaySinh())){
               cmNgaySinh = jsonEFormData.getCmNgaySinh();
            } else {
               cmNgaySinh = dateFormat.format(today);
            }
            
            //getChongNgaySinh
//            if(Objects.nonNull(jsonEFormData.getChongNgaySinh())){
//               chongNgaySinh = jsonEFormData.getChongNgaySinh();
//            } else {
//               chongNgaySinh = dateFormat.format(today);
//            }
            
            if (Objects.nonNull(jsonEFormData.getChongNgaySinh()) && jsonEFormData.getChongNgaySinh() != null && jsonEFormData.getChongNgaySinh() != "") {
                String regexSrc = "^(19|20)\\d{2}$|^(0[1-9]|1[0-2])\\.(19|20)\\d{2}$|^(0[1-9]|1\\d|2\\d|3[01])\\.(0[1-9]|1[0-2])\\.(19|20)\\d{2}$";
                    Pattern patternSrc = Pattern.compile(regexSrc, Pattern.CASE_INSENSITIVE);
                    Matcher matcherSrc = patternSrc.matcher(jsonEFormData.getChongNgaySinh());
                    if (matcherSrc.find()) {
                        chongNgaySinh = jsonEFormData.getChongNgaySinh();
                    } else {
                        chongNgaySinh = "";
                        logger.info("HCMLGSP-HTTP KHLT-Dossier errFormatDate: " + jsonEFormData.getChongNgaySinh());
                    }
            } 
            //getVoNgaySinh
//            if(Objects.nonNull(jsonEFormData.getVoNgaySinh())){
//               voNgaySinh = jsonEFormData.getVoNgaySinh();
//            } else {
//               voNgaySinh = dateFormat.format(today);
//            }
            
            if (Objects.nonNull(jsonEFormData.getVoNgaySinh()) && jsonEFormData.getVoNgaySinh() != null && jsonEFormData.getVoNgaySinh() != "") {
                String regexSrc = "^(19|20)\\d{2}$|^(0[1-9]|1[0-2])\\.(19|20)\\d{2}$|^(0[1-9]|1\\d|2\\d|3[01])\\.(0[1-9]|1[0-2])\\.(19|20)\\d{2}$";
                    Pattern patternSrc = Pattern.compile(regexSrc, Pattern.CASE_INSENSITIVE);
                    Matcher matcherSrc = patternSrc.matcher(jsonEFormData.getVoNgaySinh());
                    if (matcherSrc.find()) {
                        voNgaySinh = jsonEFormData.getVoNgaySinh();
                    } else {
                        voNgaySinh = "";
                        logger.info("HCMLGSP-HTTP KHLT-Dossier errFormatDate: " + jsonEFormData.getVoNgaySinh());
                    }
            } 
            
            // Get list nksQuocTichKhac from "KS" Eform.
            if (jsonEFormData.getNksQuocTichKhac() != null && !jsonEFormData.getNksQuocTichKhac().isEmpty()) {
                nksQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getNksQuocTichKhac());
            } else {
                nksQuocTichKhacValue = null;
            }
            
            // Get list meQuocTichKhac from "KS" Eform.
            if (jsonEFormData.getMeQuocTichKhac() != null && !jsonEFormData.getMeQuocTichKhac().isEmpty()) {
                meQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getMeQuocTichKhac());
            }  else {
                meQuocTichKhacValue = null;
            }
            
            // Get list chaQuocTichKhac from "KS" Eform
            if (jsonEFormData.getChaQuocTichKhac()!= null && !jsonEFormData.getChaQuocTichKhac().isEmpty() ) {
                chaQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getChaQuocTichKhac());
            } else {
                chaQuocTichKhacValue = null;
            }
       
            // Get list nxnQuocTichKhac from "HN" Eform
            if (jsonEFormData.getNxnQuocTichKhac() != null && !jsonEFormData.getNxnQuocTichKhac().isEmpty()) {
                nxnQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getNxnQuocTichKhac());
            } else {
                nxnQuocTichKhacValue = null;
            }
            
            if (jsonEFormData.getChongQuocTichKhac() != null && !jsonEFormData.getChongQuocTichKhac().isEmpty()) {
                chongQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getChongQuocTichKhac());
            } else {
                chongQuocTichKhacValue = null;
            }
            
            if (jsonEFormData.getVoQuocTichKhac() != null && !jsonEFormData.getVoQuocTichKhac().isEmpty()) {
                voQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getVoQuocTichKhac());
            } else {
                voQuocTichKhacValue = null;
            }    
            
            if (jsonEFormData.getCmQuocTichKhac() != null && !jsonEFormData.getCmQuocTichKhac().isEmpty()) {
                cmQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getCmQuocTichKhac());
            } else {
                cmQuocTichKhacValue = null;
            }  
            
            if (jsonEFormData.getNcQuocTichKhac() != null && !jsonEFormData.getNcQuocTichKhac().isEmpty()) {
                ncQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getNcQuocTichKhac());
            } else {
                ncQuocTichKhacValue = null;
            }  
            
            if (jsonEFormData.getNghQuocTichKhac() != null && !jsonEFormData.getNghQuocTichKhac().isEmpty()) {
                nghQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getNghQuocTichKhac());
            } else {
                nghQuocTichKhacValue = null;
            }  
            
            if (jsonEFormData.getDghQuocTichKhac() != null && !jsonEFormData.getDghQuocTichKhac().isEmpty()) {
                dghQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getDghQuocTichKhac());
            } else {
                dghQuocTichKhacValue = null;
            }  
            
            if (jsonEFormData.getNktQuocTichKhac() != null && !jsonEFormData.getNktQuocTichKhac().isEmpty()) {
                nktQuocTichKhacValue = this.getQuocTichKhacArr(jsonEFormData.getNktQuocTichKhac());
            } else {
                nktQuocTichKhacValue = null;
            }  
            
            if (jsonEFormData.getNktNgaySinh() != null && !jsonEFormData.getNktNgaySinh().isEmpty()) {
                String regexSrc = "^(19|20)\\d{2}$|^(0[1-9]|1[0-2])\\.(19|20)\\d{2}$|^(0[1-9]|1\\d|2\\d|3[01])\\.(0[1-9]|1[0-2])\\.(19|20)\\d{2}$";
                    Pattern patternSrc = Pattern.compile(regexSrc, Pattern.CASE_INSENSITIVE);
                    Matcher matcherSrc = patternSrc.matcher(jsonEFormData.getNktNgaySinh());
                    if (matcherSrc.find()) {
                        nktNgaySinh = jsonEFormData.getNktNgaySinh();
                    } else {
                        logger.info("HCMLGSP-HTTP KT-Dossier errFormatDate: " + jsonEFormData.getNktNgaySinh());
                    }
            } 
            
            String result = "";
            switch(HTTPType) {
            case "KS": 
                result = "<?xmlversion='1.0'encoding='utf-8'?>"
                        + "<hotich><hoso>"
                        + "<id>"+jsonEFormData.getId()+"</id>"
                        + "<so>"+jsonEFormData.getSo()+"</so>"
                        + "<quyenSo>"+jsonEFormData.getQuyenSo()+"</quyenSo>"
                        + "<trangSo>"+jsonEFormData.getTrangSo()+"</trangSo>"
                        + "<ngayDangKy>"+ ngayDangKy +"</ngayDangKy>"
                        + "<loaiDangKy>"+jsonEFormData.getLoaiDangKy()+"</loaiDangKy>"
                        + "<noiDangKy>"+jsonEFormData.getNoiDangKy().getValue()+"</noiDangKy>"
                        + "<nguoiKy>"+jsonEFormData.getNguoiKy()+"</nguoiKy>"
                        + "<chucVuNguoiKy>"+jsonEFormData.getChucVuNguoiKy()+"</chucVuNguoiKy>"
                        + "<nguoiThucHien>"+jsonEFormData.getNguoiThucHien()+"</nguoiThucHien>"
                        + "<ghiChu>"+jsonEFormData.getGhiChu()+"</ghiChu>"
                        + "<nksHoTen>"+jsonEFormData.getNksHoTen()+"</nksHoTen>"
                        + "<nksGioiTinh>"+jsonEFormData.getNksGioiTinh()+"</nksGioiTinh>"
                        + "<nksNgaySinh>"+ nksNgaySinh +"</nksNgaySinh>"
                        + "<nksNgaySinhBangChu>"+jsonEFormData.getNksNgaySinhBangChu()+"</nksNgaySinhBangChu>"
                        + "<nksNoiSinh>"+jsonEFormData.getNksNoiSinh()+"</nksNoiSinh>"
                        + "<nksNoiSinhDVHC>"+jsonEFormData.getNksNoiSinhDVHC().getValue()+"</nksNoiSinhDVHC>"
                        + "<nksQueQuan>"+jsonEFormData.getNksQueQuan()+"</nksQueQuan>"
                        + "<nksDanToc>"+jsonEFormData.getNksDanToc().getValue()+"</nksDanToc>"
                        + "<nksDanTocKhac>"+jsonEFormData.getNksDanTocKhac()+"</nksDanTocKhac>"
                        + "<nksQuocTich>"+jsonEFormData.getNksQuocTich().getValue()+"</nksQuocTich>"
                        + "<nksQuocTichKhac>"+ nksQuocTichKhacValue +"</nksQuocTichKhac>"
                        + "<nksLoaiKhaiSinh>"+jsonEFormData.getNksLoaiKhaiSinh()+"</nksLoaiKhaiSinh>"
                        + "<nksPIN>"+jsonEFormData.getNksPIN()+"</nksPIN>"
                        + "<nksMatTich>"+jsonEFormData.getNksMatTich()+"</nksMatTich>"
                        + "<nksMatTichNgayGhiChuTuyenBo>"+jsonEFormData.getNksMatTichNgayGhiChuTuyenBo()+"</nksMatTichNgayGhiChuTuyenBo>"
                        + "<nksMatTichCanCuTuyenBo>"+jsonEFormData.getNksMatTichCanCuTuyenBo()+"</nksMatTichCanCuTuyenBo>"
                        + "<nksMatTichNgayGhiChuHuyTuyenBo>"+jsonEFormData.getNksMatTichNgayGhiChuHuyTuyenBo()+"</nksMatTichNgayGhiChuHuyTuyenBo>"
                        + "<nksMatTichCanCuHuyTuyenBo>"+jsonEFormData.getNksMatTichCanCuHuyTuyenBo()+"</nksMatTichCanCuHuyTuyenBo>"
                        + "<nksHanCheNangLucHanhVi>"+jsonEFormData.getNksHanCheNangLucHanhVi()+"</nksHanCheNangLucHanhVi>"
                        + "<nksHanCheNangLucHanhViNgayGhiChuTuyenBo>"+jsonEFormData.getNksHanCheNangLucHanhViNgayGhiChuTuyenBo()+"</nksHanCheNangLucHanhViNgayGhiChuTuyenBo>"
                        + "<nksHanCheNangLucHanhViCanCuTuyenBo>"+jsonEFormData.getNksHanCheNangLucHanhViCanCuTuyenBo()+"</nksHanCheNangLucHanhViCanCuTuyenBo>"
                        + "<nksHanCheNangLucHanhViNgayGhiChuHuyTuyenBo>"+jsonEFormData.getNksHanCheNangLucHanhViNgayGhiChuHuyTuyenBo()+"</nksHanCheNangLucHanhViNgayGhiChuHuyTuyenBo>"
                        + "<nksHanCheNangLucHanhViNgayCanCuHuyTuyenBo>"+jsonEFormData.getNksHanCheNangLucHanhViNgayCanCuHuyTuyenBo()+"</nksHanCheNangLucHanhViNgayCanCuHuyTuyenBo>"
                        + "<meHoTen>"+jsonEFormData.getMeHoTen()+"</meHoTen>"
                        + "<meNgaySinh>"+ meNgaySinh +"</meNgaySinh>"
                        + "<meDanToc>"+jsonEFormData.getMeDanToc().getValue()+"</meDanToc>"
                        + "<meDanTocKhac>"+jsonEFormData.getMeDanTocKhac()+"</meDanTocKhac>"
                        + "<meQuocTich>"+jsonEFormData.getMeQuocTich().getValue()+"</meQuocTich>"
                        + "<meQuocTichKhac>"+ meQuocTichKhacValue +"</meQuocTichKhac>"
                        + "<meLoaiCuTru>"+jsonEFormData.getMeLoaiCuTru()+"</meLoaiCuTru>"
                        + "<meNoiCuTru>"+jsonEFormData.getMeNoiCuTru()+"</meNoiCuTru>"
                        + "<meLoaiGiayToTuyThan>"+jsonEFormData.getMeLoaiGiayToTuyThan()+"</meLoaiGiayToTuyThan>"
                        + "<meSoGiayToTuyThan>"+jsonEFormData.getMeSoGiayToTuyThan()+"</meSoGiayToTuyThan>"
                        + "<chaHoTen>"+jsonEFormData.getChaHoTen()+"</chaHoTen>"
                        + "<chaNgaySinh>"+ chaNgaySinh +"</chaNgaySinh>"
                        + "<chaDanToc>"+jsonEFormData.getChaDanToc().getValue()+"</chaDanToc>"
                        + "<chaDanTocKhac>"+jsonEFormData.getChaDanTocKhac()+"</chaDanTocKhac>"
                        + "<chaQuocTich>"+jsonEFormData.getChaQuocTich().getValue()+"</chaQuocTich>"
                        + "<chaQuocTichKhac>"+chaQuocTichKhacValue+"</chaQuocTichKhac>"
                        + "<chaLoaiCuTru>"+jsonEFormData.getChaLoaiCuTru()+"</chaLoaiCuTru>"
                        + "<chaNoiCuTru>"+jsonEFormData.getChaNoiCuTru()+"</chaNoiCuTru>"
                        + "<chaLoaiGiayToTuyThan>"+jsonEFormData.getChaLoaiGiayToTuyThan()+"</chaLoaiGiayToTuyThan>"
                        + "<chaSoGiayToTuyThan>"+jsonEFormData.getChaSoGiayToTuyThan()+"</chaSoGiayToTuyThan>"
                        + "<nycHoTen>"+jsonEFormData.getNycHoTen()+"</nycHoTen>"
                        + "<nycQuanHe>"+jsonEFormData.getNycQuanHe()+"</nycQuanHe>"
                        + "<nycLoaiGiayToTuyThan>"+jsonEFormData.getNycLoaiGiayToTuyThan()+"</nycLoaiGiayToTuyThan>"
                        + "<nycGiayToKhac>"+jsonEFormData.getNycGiayToKhac()+"</nycGiayToKhac>"
                        + "<nycSoGiayToTuyThan>"+jsonEFormData.getNycSoGiayToTuyThan()+"</nycSoGiayToTuyThan>"
                        + "<nycNgayCapGiayToTuyThan>"+jsonEFormData.getNycNgayCapGiayToTuyThan()+"</nycNgayCapGiayToTuyThan>"
                        + "<nycNoiCapGiayToTuyThan>"+jsonEFormData.getNycNoiCapGiayToTuyThan()+"</nycNoiCapGiayToTuyThan>"
                        + "<soDangKyNuocNgoai>"+jsonEFormData.getSoDangKyNuocNgoai()+"</soDangKyNuocNgoai>"
                        + "<ngayDangKyNuocNgoai>"+jsonEFormData.getNgayDangKyNuocNgoai()+"</ngayDangKyNuocNgoai>"
                        + "<cqNuocNgoaiDaDangKy>"+jsonEFormData.getCqNuocNgoaiDaDangKy()+"</cqNuocNgoaiDaDangKy>"
                        + "<qgNuocNgoaiDaDangKy>"+jsonEFormData.getQgNuocNgoaiDaDangKy().getValue()+"</qgNuocNgoaiDaDangKy>"
                        + "<loaiHTTP>"+HTTPType+"</loaiHTTP>"
                        + "</hoso></hotich>";
                break;
            case "HN":
                result = "<?xmlversion='1.0'encoding='utf-8'?>"
                        + "<hotich><hoso>"
                        + "<id>"+jsonEFormData.getId()+"</id>"
                        + "<so>"+jsonEFormData.getSo()+"</so>"
                        + "<quyenSo>"+jsonEFormData.getQuyenSo()+"</quyenSo>"
                        + "<trangSo>"+jsonEFormData.getTrangSo()+"</trangSo>"
                        + "<ngayDangKy>"+ ngayDangKy +"</ngayDangKy>"
                        + "<noiCap>"+jsonEFormData.getNoiCap().getValue()+"</noiCap>"
                        + "<nguoiKy>"+jsonEFormData.getNguoiKy()+"</nguoiKy>"
                        + "<chucVuNguoiKy>"+jsonEFormData.getChucVuNguoiKy()+"</chucVuNguoiKy>"
                        + "<nguoiThucHien>"+jsonEFormData.getNguoiThucHien()+"</nguoiThucHien>"
                        + "<ghiChu>"+jsonEFormData.getGhiChu()+"</ghiChu>"
                        + "<nxnHoTen>"+jsonEFormData.getNxnHoTen()+"</nxnHoTen>"
                        + "<nxnGioiTinh>"+jsonEFormData.getNxnGioiTinh()+"</nxnGioiTinh>"
                        + "<nxnNgaySinh>"+ nxnNgaySinh +"</nxnNgaySinh>"
                        + "<nxnDanToc>"+jsonEFormData.getNxnDanToc().getValue()+"</nxnDanToc>"
                        + "<nxnDanTocKhac>"+jsonEFormData.getNxnDanTocKhac()+"</nxnDanTocKhac>"
                        + "<nxnQuocTich>"+jsonEFormData.getNxnQuocTich().getValue()+"</nxnQuocTich>"
                        + "<nxnQuocTichKhac>"+ nxnQuocTichKhacValue +"</nxnQuocTichKhac>"
                        + "<nxnLoaiCuTru>"+jsonEFormData.getNxnLoaiCuTru()+"</nxnLoaiCuTru>"
                        + "<nxnNoiCuTru>"+jsonEFormData.getNxnNoiCuTru()+"</nxnNoiCuTru>"
                        + "<nxnLoaiGiayToTuyThan>"+jsonEFormData.getNxnLoaiGiayToTuyThan()+"</nxnLoaiGiayToTuyThan>"
                        + "<nxnGiayToKhac>"+jsonEFormData.getNxnGiayToKhac()+"</nxnGiayToKhac>"
                        + "<nxnSoGiayToTuyThan>"+jsonEFormData.getNxnSoGiayToTuyThan()+"</nxnSoGiayToTuyThan>"
                        + "<nxnNgayCapGiayToTuyThan>"+jsonEFormData.getNxnNgayCapGiayToTuyThan()+"</nxnNgayCapGiayToTuyThan>"
                        + "<nxnNoiCapGiayToTuyThan>"+jsonEFormData.getNxnNoiCapGiayToTuyThan()+"</nxnNoiCapGiayToTuyThan>"
                        + "<nxnThoiGianCuTruTai>"+jsonEFormData.getNxnThoiGianCuTruTai()+"</nxnThoiGianCuTruTai>"
                        + "<nxnThoiGianCuTruTu>"+jsonEFormData.getNxnThoiGianCuTruTu()+"</nxnThoiGianCuTruTu>"
                        + "<nxnThoiGianCuTruDen>"+jsonEFormData.getNxnThoiGianCuTruDen()+"</nxnThoiGianCuTruDen>"
                        + "<nxnTinhTrangHonNhan>"+jsonEFormData.getNxnTinhTrangHonNhan()+"</nxnTinhTrangHonNhan>"
                        + "<nxnLoaiMucDichSuDung>"+jsonEFormData.getNxnLoaiMucDichSuDung()+"</nxnLoaiMucDichSuDung>"
                        + "<nxnMucDichSuDung>"+jsonEFormData.getNxnMucDichSuDung()+"</nxnMucDichSuDung>"
                        + "<nycHoTen>"+jsonEFormData.getNycHoTen()+"</nycHoTen>"
                        + "<nycQuanHe>"+jsonEFormData.getNycQuanHe()+"</nycQuanHe>"
                        + "<nycLoaiGiayToTuyThan>"+jsonEFormData.getNycLoaiGiayToTuyThan()+"</nycLoaiGiayToTuyThan>"
                        + "<nycGiayToKhac>"+jsonEFormData.getNycGiayToKhac()+"</nycGiayToKhac>"
                        + "<nycSoGiayToTuyThan>"+jsonEFormData.getNycSoGiayToTuyThan()+"</nycSoGiayToTuyThan>"
                        + "<nycNgayCapGiayToKhac>"+jsonEFormData.getNycNgayCapGiayToKhac()+"</nycNgayCapGiayToKhac>"
                        + "<nycNoiCapGiayToKhac>"+jsonEFormData.getNycNoiCapGiayToKhac()+"</nycNoiCapGiayToKhac>"
                        + "</hoso></hotich>";
                break;
            case "KH": 
                result = "<?xmlversion='1.0'encoding='utf-8'?>"
                        + "<hotich><hoso>"
                        + "<id>"+jsonEFormData.getId()+"</id>"
                        + "<so>"+jsonEFormData.getSo()+"</so>"
                        + "<quyenSo>"+jsonEFormData.getQuyenSo()+"</quyenSo>"
                        + "<trangSo>"+jsonEFormData.getTrangSo()+"</trangSo>"
                        + "<ngayDangKy>"+ngayDangKy+"</ngayDangKy>"
                        + "<loaiDangKy>"+jsonEFormData.getLoaiDangKy()+"</loaiDangKy>"
                        + "<noiDangKy>"+jsonEFormData.getNoiDangKy().getValue()+"</noiDangKy>"
                        + "<nguoiKy>"+jsonEFormData.getNguoiKy()+"</nguoiKy>"
                        + "<chucVuNguoiKy>"+jsonEFormData.getChucVuNguoiKy()+"</chucVuNguoiKy>"
                        + "<ngayXacLapQuanHeHonNhan>"+jsonEFormData.getNgayXacLapQuanHeHonNhan()+"</ngayXacLapQuanHeHonNhan>"
                        + "<nguoiThucHien>"+jsonEFormData.getNguoiThucHien()+"</nguoiThucHien>"
                        + "<ghiChu>"+jsonEFormData.getGhiChu()+"</ghiChu>"
                        + "<tinhTrangKetHon>"+jsonEFormData.getTinhTrangKetHon()+"</tinhTrangKetHon>"
                        + "<huyKetHonNgayGhiChu>"+jsonEFormData.getHuyKetHonNgayGhiChu()+"</huyKetHonNgayGhiChu>"
                        + "<huyKetHonCanCu>"+jsonEFormData.getHuyKetHonCanCu()+"</huyKetHonCanCu>"
                        + "<congNhanKetHonNgayGhiChu>"+jsonEFormData.getCongNhanKetHonNgayGhiChu()+"</congNhanKetHonNgayGhiChu>"
                        + "<congNhanKetHonCanCu>"+jsonEFormData.getCongNhanKetHonCanCu()+"</congNhanKetHonCanCu>"
                        + "<chongHoTen>"+jsonEFormData.getChongHoTen()+"</chongHoTen>"
                        + "<chongNgaySinh>"+ chongNgaySinh +"</chongNgaySinh>"
                        + "<chongDanToc>"+jsonEFormData.getChongDanToc().getValue()+"</chongDanToc>"
                        + "<chongDanTocKhac>"+jsonEFormData.getChongDanTocKhac()+"</chongDanTocKhac>"
                        + "<chongQuocTich>"+jsonEFormData.getChongQuocTich().getValue()+"</chongQuocTich>"
                        + "<chongQuocTichKhac>"+chongQuocTichKhacValue+"</chongQuocTichKhac>"
                        + "<chongLoaiCuTru>"+jsonEFormData.getChongLoaiCuTru()+"</chongLoaiCuTru>"
                        + "<chongNoiCuTru>"+jsonEFormData.getChongNoiCuTru()+"</chongNoiCuTru>"
                        + "<chongLoaiGiayToTuyThan>"+jsonEFormData.getChongLoaiGiayToTuyThan()+"</chongLoaiGiayToTuyThan>"
                        + "<chongGiayToKhac>"+jsonEFormData.getChongGiayToKhac()+"</chongGiayToKhac>"
                        + "<chongSoGiayToTuyThan>"+jsonEFormData.getChongSoGiayToTuyThan()+"</chongSoGiayToTuyThan>"
                        + "<chongNgayCapGiayToTuyThan>"+jsonEFormData.getChongNgayCapGiayToTuyThan()+"</chongNgayCapGiayToTuyThan>"
                        + "<chongNoiCapGiayToTuyThan>"+jsonEFormData.getChongNoiCapGiayToTuyThan()+"</chongNoiCapGiayToTuyThan>"
                        + "<voHoTen>"+jsonEFormData.getVoHoTen()+"</voHoTen>"
                        + "<voNgaySinh>"+ voNgaySinh +"</voNgaySinh>"
                        + "<voDanToc>"+jsonEFormData.getVoDanToc().getValue()+"</voDanToc>"
                        + "<voDanTocKhac>"+jsonEFormData.getVoDanTocKhac()+"</voDanTocKhac>"
                        + "<voQuocTich>"+jsonEFormData.getVoQuocTich().getValue()+"</voQuocTich>"
                        + "<voQuocTichKhac>"+ voQuocTichKhacValue +"</voQuocTichKhac>"
                        + "<voLoaiCuTru>"+jsonEFormData.getVoLoaiCuTru()+"</voLoaiCuTru>"
                        + "<voNoiCuTru>"+jsonEFormData.getVoNoiCuTru()+"</voNoiCuTru>"
                        + "<voLoaiGiayToTuyThan>"+jsonEFormData.getVoLoaiGiayToTuyThan()+"</voLoaiGiayToTuyThan>"
                        + "<voGiayToKhac>"+jsonEFormData.getVoGiayToKhac()+"</voGiayToKhac>"
                        + "<voSoGiayToTuyThan>"+jsonEFormData.getVoSoGiayToTuyThan()+"</voSoGiayToTuyThan>"
                        + "<voNgayCapGiayToTuyThan>"+jsonEFormData.getVoNgayCapGiayToTuyThan()+"</voNgayCapGiayToTuyThan>"
                        + "<voNoiCapGiayToTuyThan>"+jsonEFormData.getVoNoiCapGiayToTuyThan()+"</voNoiCapGiayToTuyThan>"
                        + "<soDangKyNuocNgoai>"+jsonEFormData.getSoDangKyNuocNgoai()+"</soDangKyNuocNgoai>"
                        + "<ngayDangKyNuocNgoai>"+jsonEFormData.getNgayDangKyNuocNgoai()+"</ngayDangKyNuocNgoai>"
                        + "<cqNuocNgoaiDaDangKy>"+jsonEFormData.getCqNuocNgoaiDaDangKy()+"</cqNuocNgoaiDaDangKy>"
                        + "<qgNuocNgoaiDaDangKy>"+jsonEFormData.getQgNuocNgoaiDaDangKy().getValue()+"</qgNuocNgoaiDaDangKy>"
                        + "</hoso></hotich>";
                break;
            case "CMC": 
                result = "<?xmlversion='1.0'encoding='utf-8'?>"
                        + "<hotich><hoso>"
                        + "<id>"+jsonEFormData.getId()+"</id>"
                        + "<so>"+jsonEFormData.getSo()+"</so>"
                        + "<quyenSo>"+jsonEFormData.getQuyenSo()+"</quyenSo>"
                        + "<trangSo>"+jsonEFormData.getTrangSo()+"</trangSo>"
                        + "<quyetDinhSo>"+jsonEFormData.getQuyetDinhSo()+"</quyetDinhSo>"
                        + "<ngayDangKy>"+ ngayDangKy +"</ngayDangKy>"
                        + "<loaiDangKy>"+jsonEFormData.getLoaiDangKy()+"</loaiDangKy>"
                        + "<loaiXacNhan>"+jsonEFormData.getLoaiXacNhan()+"</loaiXacNhan>"
                        + "<noiDangKy>"+jsonEFormData.getNoiDangKy().getValue()+"</noiDangKy>"
                        + "<nguoiKy>"+jsonEFormData.getNguoiKy()+"</nguoiKy>"
                        + "<chucVuNguoiKy>"+jsonEFormData.getChucVuNguoiKy()+"</chucVuNguoiKy>"
                        + "<nguoiThucHien>"+jsonEFormData.getNguoiThucHien()+"</nguoiThucHien>"
                        + "<ghiChu>"+jsonEFormData.getGhiChu()+"</ghiChu>"
                        + "<cmHoTen>"+jsonEFormData.getCmHoTen()+"</cmHoTen>"
                        + "<cmNgaySinh>"+ cmNgaySinh +"</cmNgaySinh>"
                        + "<cmDanToc>"+jsonEFormData.getCmDanToc().getValue()+"</cmDanToc>"
                        + "<cmDanTocKhac>"+jsonEFormData.getCmDanTocKhac()+"</cmDanTocKhac>"
                        + "<cmQuocTich>"+jsonEFormData.getCmQuocTich().getValue()+"</cmQuocTich>"
                        + "<cmQuocTichKhac>"+ cmQuocTichKhacValue +"</cmQuocTichKhac>"
                        + "<cmLoaiCuTru>"+jsonEFormData.getCmLoaiCuTru()+"</cmLoaiCuTru>"
                        + "<cmNoiCuTru>"+jsonEFormData.getCmNoiCuTru()+"</cmNoiCuTru>"
                        + "<cmLoaiGiayToTuyThan>"+jsonEFormData.getCmLoaiGiayToTuyThan()+"</cmLoaiGiayToTuyThan>"
                        + "<cmGiayToKhac>"+jsonEFormData.getCmGiayToKhac()+"</cmGiayToKhac>"
                        + "<cmSoGiayToTuyThan>"+jsonEFormData.getCmSoGiayToTuyThan()+"</cmSoGiayToTuyThan>"
                        + "<cmNgayCapGiayToTuyThan>"+jsonEFormData.getCmNgayCapGiayToTuyThan()+"</cmNgayCapGiayToTuyThan>"
                        + "<cmNoiCapGiayToTuyThan>"+jsonEFormData.getCmNoiCapGiayToTuyThan()+"</cmNoiCapGiayToTuyThan>"
                        + "<ncHoTen>"+jsonEFormData.getNcHoTen()+"</ncHoTen>"
                        + "<ncNgaySinh>"+jsonEFormData.getNcNgaySinh()+"</ncNgaySinh>"
                        + "<ncDanToc>"+jsonEFormData.getNcDanToc().getValue()+"</ncDanToc>"
                        + "<ncDanTocKhac>"+jsonEFormData.getNcDanTocKhac()+"</ncDanTocKhac>"
                        + "<ncQuocTich>"+jsonEFormData.getNcQuocTich().getValue()+"</ncQuocTich>"
                        + "<ncQuocTichKhac>"+ ncQuocTichKhacValue +"</ncQuocTichKhac>"
                        + "<ncQueQuan>"+jsonEFormData.getNcQueQuan()+"</ncQueQuan>"
                        + "<ncLoaiCuTru>"+jsonEFormData.getNcLoaiCuTru()+"</ncLoaiCuTru>"
                        + "<ncLoaiGiayToTuyThan>"+jsonEFormData.getNcLoaiGiayToTuyThan()+"</ncLoaiGiayToTuyThan>"
                        + "<ncGiayToKhac>"+jsonEFormData.getNcGiayToKhac()+"</ncGiayToKhac>"
                        + "<ncSoGiayToTuyThan>"+jsonEFormData.getNcSoGiayToTuyThan()+"</ncSoGiayToTuyThan>"
                        + "<ncNgayCapGiayToTuyThan>"+jsonEFormData.getNcNgayCapGiayToTuyThan()+"</ncNgayCapGiayToTuyThan>"
                        + "<ncNoiCapGiayToTuyThan>"+jsonEFormData.getNcNoiCapGiayToTuyThan()+"</ncNoiCapGiayToTuyThan>"
                        + "<nycHoTen>"+jsonEFormData.getNycHoTen()+"</nycHoTen>"
                        + "<nycQHNguoiDuocNhan>"+jsonEFormData.getNycQHNguoiDuocNhan()+"</nycQHNguoiDuocNhan>"
                        + "<nycQHNguoiNhan>"+jsonEFormData.getNycQHNguoiNhan()+"</nycQHNguoiNhan>"
                        + "<nycLoaiGiayToTuyThan>"+jsonEFormData.getNycLoaiGiayToTuyThan()+"</nycLoaiGiayToTuyThan>"
                        + "<nycGiayToKhac>"+jsonEFormData.getNycGiayToKhac()+"</nycGiayToKhac>"
                        + "<nycSoGiayToTuyThan>"+jsonEFormData.getNycSoGiayToTuyThan()+"</nycSoGiayToTuyThan>"
                        + "<nycNgayCapGiayToTuyThan>"+jsonEFormData.getNycNgayCapGiayToTuyThan()+"</nycNgayCapGiayToTuyThan>"
                        + "<nycNoiCapGiayToTuyThan>"+jsonEFormData.getNycNoiCapGiayToTuyThan()+"</nycNoiCapGiayToTuyThan>"
                        + "<soDangKyNuocNgoai>"+jsonEFormData.getSoDangKyNuocNgoai()+"</soDangKyNuocNgoai>"
                        + "<ngayDangKyNuocNgoai>"+jsonEFormData.getNgayDangKyNuocNgoai()+"</ngayDangKyNuocNgoai>"
                        + "<cqNuocNgoaiDaDangKy>"+jsonEFormData.getCqNuocNgoaiDaDangKy()+"</cqNuocNgoaiDaDangKy>"
                        + "<qgNuocNgoaiDaDangKy>"+jsonEFormData.getQgNuocNgoaiDaDangKy().getValue()+"</qgNuocNgoaiDaDangKy>"
                        + "</hoso></hotich>";
                break;
            case "LH": 
                result = "<?xmlversion='1.0'encoding='utf-8'?>"
                        + "<hotich><hoso>"
                        + "<id>"+jsonEFormData.getId()+"</id>"
                        + "<so>"+jsonEFormData.getSo()+"</so>"
                        + "<quyenSo>"+jsonEFormData.getQuyenSo()+"</quyenSo>"
                        + "<trangSo>"+jsonEFormData.getTrangSo()+"</trangSo>"
                        + "<ngayDangKy>"+ ngayDangKy +"</ngayDangKy>"
                        + "<loaiGhiSo>"+jsonEFormData.getLoaiGhiSo()+"</loaiGhiSo>"
                        + "<noiDangKy>"+jsonEFormData.getNoiDangKy().getValue()+"</noiDangKy>"
                        + "<nguoiKy>"+jsonEFormData.getNguoiKy()+"</nguoiKy>"
                        + "<chucVuNguoiKy>"+jsonEFormData.getChucVuNguoiKy()+"</chucVuNguoiKy>"
                        + "<ngayXacLapQuanHeHonNhan>"+jsonEFormData.getNgayXacLapQuanHeHonNhan()+"</ngayXacLapQuanHeHonNhan>"
                        + "<nguoiThucHien>"+jsonEFormData.getNguoiThucHien()+"</nguoiThucHien>"
                        + "<ghiChu>"+jsonEFormData.getGhiChu()+"</ghiChu>"
                        + "<chongHoTen>"+jsonEFormData.getChongHoTen()+"</chongHoTen>"
                        + "<chongNgaySinh>"+ chongNgaySinh +"</chongNgaySinh>"
                        + "<chongQuocTich>"+jsonEFormData.getChongQuocTich().getValue()+"</chongQuocTich>"
                        + "<chongQuocTichKhac>"+ chongQuocTichKhacValue +"</chongQuocTichKhac>"
                        + "<chongLoaiCuTru>"+jsonEFormData.getChongLoaiCuTru()+"</chongLoaiCuTru>"
                        + "<chongNoiCuTru>"+jsonEFormData.getChongNoiCuTru()+"</chongNoiCuTru>"
                        + "<chongLoaiGiayToTuyThan>"+jsonEFormData.getChongLoaiGiayToTuyThan()+"</chongLoaiGiayToTuyThan>"
                        + "<chongGiayToKhac>"+jsonEFormData.getChongGiayToKhac()+"</chongGiayToKhac>"
                        + "<chongSoGiayToTuyThan>"+jsonEFormData.getChongSoGiayToTuyThan()+"</chongSoGiayToTuyThan>"
                        + "<chongNgayCapGiayToTuyThan>"+jsonEFormData.getChongNgayCapGiayToTuyThan()+"</chongNgayCapGiayToTuyThan>"
                        + "<chongNoiCapGiayToTuyThan>"+jsonEFormData.getChongNoiCapGiayToTuyThan()+"</chongNoiCapGiayToTuyThan>"
                        + "<voHoTen>"+jsonEFormData.getVoHoTen()+"</voHoTen>"
                        + "<voNgaySinh>"+ voNgaySinh +"</voNgaySinh>"
                        + "<voQuocTich>"+jsonEFormData.getVoQuocTich().getValue()+"</voQuocTich>"
                        + "<voQuocTichKhac>"+voQuocTichKhacValue+"</voQuocTichKhac>"
                        + "<voLoaiCuTru>"+jsonEFormData.getVoLoaiCuTru()+"</voLoaiCuTru>"
                        + "<voNoiCuTru>"+jsonEFormData.getVoNoiCuTru()+"</voNoiCuTru>"
                        + "<voLoaiGiayToTuyThan>"+jsonEFormData.getVoLoaiGiayToTuyThan()+"</voLoaiGiayToTuyThan>"
                        + "<voGiayToKhac>"+jsonEFormData.getVoGiayToKhac()+"</voGiayToKhac>"
                        + "<voSoGiayToTuyThan>"+jsonEFormData.getVoSoGiayToTuyThan()+"</voSoGiayToTuyThan>"
                        + "<voNgayCapGiayToTuyThan>"+jsonEFormData.getVoNgayCapGiayToTuyThan()+"</voNgayCapGiayToTuyThan>"
                        + "<voNoiCapGiayToTuyThan>"+jsonEFormData.getVoNoiCapGiayToTuyThan()+"</voNoiCapGiayToTuyThan>"
                        + "<khNoiDangKyKetHon>"+jsonEFormData.getKhNoiDangKyKetHon().getValue()+"</khNoiDangKyKetHon>"
                        + "<khSoDangKyKetHon>"+jsonEFormData.getKhSoDangKyKetHon()+"</khSoDangKyKetHon>"
                        + "<khNgayDangKyKetHon>"+jsonEFormData.getKhNgayDangKyKetHon()+"</khNgayDangKyKetHon>"
                        + "<lhTenGiayXacNhanHuyKetHon>"+jsonEFormData.getLhTenGiayXacNhanHuyKetHon()+"</lhTenGiayXacNhanHuyKetHon>"
                        + "<lhSoGiayXacNhanHuyKetHon>"+jsonEFormData.getLhSoGiayXacNhanHuyKetHon()+"</lhSoGiayXacNhanHuyKetHon>"
                        + "<lhCoQuanCapGiayXacNhanHuyKetHon>"+jsonEFormData.getLhCoQuanCapGiayXacNhanHuyKetHon()+"</lhCoQuanCapGiayXacNhanHuyKetHon>"
                        + "<lhNuocNgoaiCapGiayXacNhanHuyKetHon>"+jsonEFormData.getLhNuocNgoaiCapGiayXacNhanHuyKetHon().getValue()+"</lhNuocNgoaiCapGiayXacNhanHuyKetHon>"
                        + "</hoso></hotich>";
                break;
            case "GH": 
                 result = "<?xmlversion='1.0'encoding='utf-8'?>"
                        + "<hotich><hoso>"
                        + "<id>"+jsonEFormData.getId()+"</id>"
                        + "<so>"+jsonEFormData.getSo()+"</so>"
                        + "<quyenSo>"+jsonEFormData.getQuyenSo()+"</quyenSo>"
                        + "<trangSo>"+jsonEFormData.getTrangSo()+"</trangSo>"
                        + "<quyetDinhSo>"+jsonEFormData.getQuyetDinhSo()+"</quyetDinhSo>"
                        + "<ngayDangKy>"+ ngayDangKy +"</ngayDangKy>"
                        + "<loaiDangKy>"+jsonEFormData.getLoaiDangKy()+"</loaiDangKy>"
                        + "<loaiGiamHo>"+jsonEFormData.getLoaiGiamHo()+"</loaiGiamHo>"
                        + "<noiDangKy>"+jsonEFormData.getNoiDangKy().getValue()+"</noiDangKy>"
                        + "<nguoiKy>"+jsonEFormData.getNguoiKy()+"</nguoiKy>"
                        + "<chucVuNguoiKy>"+jsonEFormData.getChucVuNguoiKy()+"</chucVuNguoiKy>"
                        + "<nguoiThucHien>"+jsonEFormData.getNguoiThucHien()+"</nguoiThucHien>"
                        + "<ghiChu>"+jsonEFormData.getGhiChu()+"</ghiChu>"
                        + "<tinhTrangGiamHo>"+jsonEFormData.getTinhTrangGiamHo()+"</tinhTrangGiamHo>"
                        + "<chamDutGiamHoNgayGhiChu>"+jsonEFormData.getChamDutGiamHoNgayGhiChu()+"</chamDutGiamHoNgayGhiChu>"
                        + "<chamDutGiamHoCanCu>"+jsonEFormData.getChamDutGiamHoCanCu()+"</chamDutGiamHoCanCu>"
                        + "<congNhanGiamHoNgayGhiChu>"+jsonEFormData.getCongNhanGiamHoNgayGhiChu()+"</congNhanGiamHoNgayGhiChu>"
                        + "<congNhanGiamHoCanCu>"+jsonEFormData.getCongNhanGiamHoCanCu()+"</congNhanGiamHoCanCu>"
                        + "<nghHoTen>"+jsonEFormData.getNghHoTen()+"</nghHoTen>"
                        + "<nghGioiTinh>"+jsonEFormData.getNghGioiTinh()+"</nghGioiTinh>"
                        + "<nghNgaySinh>"+jsonEFormData.getNghNgaySinh()+"</nghNgaySinh>"
                        + "<nghDanToc>"+jsonEFormData.getNghDanToc().getValue()+"</nghDanToc>"
                        + "<nghDanTocKhac>"+jsonEFormData.getNghDanTocKhac()+"</nghDanTocKhac>"
                        + "<nghQuocTich>"+jsonEFormData.getNghQuocTich().getValue()+"</nghQuocTich>"
                        + "<nghQuocTichKhac>"+nghQuocTichKhacValue+"</nghQuocTichKhac>"
                        + "<nghLoaiCuTru>"+jsonEFormData.getNghLoaiCuTru()+"</nghLoaiCuTru>"
                        + "<nghNoiCuTru>"+jsonEFormData.getNghNoiCuTru()+"</nghNoiCuTru>"
                        + "<nghLoaiGiayToTuyThan>"+jsonEFormData.getNghLoaiGiayToTuyThan()+"</nghLoaiGiayToTuyThan>"
                        + "<nghGiayToKhac>"+jsonEFormData.getNghGiayToKhac()+"</nghGiayToKhac>"
                        + "<nghSoGiayToTuyThan>"+jsonEFormData.getNghSoGiayToTuyThan()+"</nghSoGiayToTuyThan>"
                        + "<nghNgayCapGiayToTuyThan>"+jsonEFormData.getNghNgayCapGiayToTuyThan()+"</nghNgayCapGiayToTuyThan>"
                        + "<nghNoiCapGiayToTuyThan>"+jsonEFormData.getNghNoiCapGiayToTuyThan()+"</nghNoiCapGiayToTuyThan>"
                        + "<dghHoTen>"+jsonEFormData.getDghHoTen()+"</dghHoTen>"
                        + "<dghGioiTinh>"+jsonEFormData.getDghGioiTinh()+"</dghGioiTinh>"
                        + "<dghNgaySinh>"+jsonEFormData.getDghNgaySinh()+"</dghNgaySinh>"
                        + "<dghNoiSinh>"+jsonEFormData.getDghNoiSinh()+"</dghNoiSinh>"
                        + "<dghDanToc>"+jsonEFormData.getDghDanToc().getValue()+"</dghDanToc>"
                        + "<dghDanTocKhac>"+jsonEFormData.getDghDanTocKhac()+"</dghDanTocKhac>"
                        + "<dghQuocTich>"+jsonEFormData.getDghQuocTich().getValue()+"</dghQuocTich>"
                        + "<dghQuocTichKhac>"+dghQuocTichKhacValue+"</dghQuocTichKhac>"
                        + "<dghLoaiCuTru>"+jsonEFormData.getDghLoaiCuTru()+"</dghLoaiCuTru>"
                        + "<dghNoiCuTru>"+jsonEFormData.getDghNoiCuTru()+"</dghNoiCuTru>"
                        + "<dghLoaiGiayToTuyThan>"+jsonEFormData.getDghLoaiGiayToTuyThan()+"</dghLoaiGiayToTuyThan>"
                        + "<dghGiayToKhac>"+jsonEFormData.getDghGiayToKhac()+"</dghGiayToKhac>"
                        + "<dghSoGiayToTuyThan>"+jsonEFormData.getDghSoGiayToTuyThan()+"</dghSoGiayToTuyThan>"
                        + "<dghNgayCapGiayToTuyThan>"+jsonEFormData.getDghNgayCapGiayToTuyThan()+"</dghNgayCapGiayToTuyThan>"
                        + "<dghNoiCapGiayToTuyThan>"+jsonEFormData.getDghNoiCapGiayToTuyThan()+"</dghNoiCapGiayToTuyThan>"
                        + "<dghLyDoCanGiamHo>"+jsonEFormData.getDghLyDoCanGiamHo()+"</dghLyDoCanGiamHo>"
                        + "<nycHoTen>"+jsonEFormData.getNycHoTen()+"</nycHoTen>"
                        + "<nycQuanHe>"+jsonEFormData.getNycQuanHe()+"</nycQuanHe>"
                        + "<nycLoaiGiayToTuyThan>"+jsonEFormData.getNycLoaiGiayToTuyThan()+"</nycLoaiGiayToTuyThan>"
                        + "<nycGiayToKhac>"+jsonEFormData.getNycGiayToKhac()+"</nycGiayToKhac>"
                        + "<nycSoGiayToTuyThan>"+jsonEFormData.getNycSoGiayToTuyThan()+"</nycSoGiayToTuyThan>"
                        + "<nycNgayCapGiayToKhac>"+jsonEFormData.getNycNgayCapGiayToKhac()+"</nycNgayCapGiayToKhac>"
                        + "<nycNoiCapGiayToKhac>"+jsonEFormData.getNycNoiCapGiayToKhac()+"</nycNoiCapGiayToKhac>"
                        + "<soDangKyNuocNgoai>"+jsonEFormData.getSoDangKyNuocNgoai()+"</soDangKyNuocNgoai>"
                        + "<ngayDangKyNuocNgoai>"+jsonEFormData.getNgayDangKyNuocNgoai()+"</ngayDangKyNuocNgoai>"
                        + "<cqNuocNgoaiDaDangKy>"+jsonEFormData.getCqNuocNgoaiDaDangKy()+"</cqNuocNgoaiDaDangKy>"
                        + "<qgNuocNgoaiDaDangKy>"+jsonEFormData.getQgNuocNgoaiDaDangKy().getValue()+"</qgNuocNgoaiDaDangKy>"
                        + "</hoso></hotich>";
                break;
            case "TGH": 
                 result = "<?xmlversion='1.0'encoding='utf-8'?>"
                        + "<hotich><hoso>"
                        + "<id>"+jsonEFormData.getId()+"</id>"
                        + "<so>"+jsonEFormData.getSo()+"</so>"
                        + "<quyenSo>"+jsonEFormData.getQuyenSo()+"</quyenSo>"
                        + "<trangSo>"+jsonEFormData.getTrangSo()+"</trangSo>"
                        + "<quyetDinhSo>"+jsonEFormData.getQuyetDinhSo()+"</quyetDinhSo>"
                        + "<ngayDangKy>"+ ngayDangKy +"</ngayDangKy>"
                        + "<loaiGiamHo>"+jsonEFormData.getLoaiGiamHo()+"</loaiGiamHo>"
                        + "<noiDangKy>"+jsonEFormData.getNoiDangKy().getValue()+"</noiDangKy>"
                        + "<nguoiKy>"+jsonEFormData.getNguoiKy()+"</nguoiKy>"
                        + "<chucVuNguoiKy>"+jsonEFormData.getChucVuNguoiKy()+"</chucVuNguoiKy>"
                        + "<nguoiThucHien>"+jsonEFormData.getNguoiThucHien()+"</nguoiThucHien>"
                        + "<ghiChu>"+jsonEFormData.getGhiChu()+"</ghiChu>"
                        + "<nghHoTen>"+jsonEFormData.getNghHoTen()+"</nghHoTen>"
                        + "<nghGioiTinh>"+jsonEFormData.getNghGioiTinh()+"</nghGioiTinh>"
                        + "<nghNgaySinh>"+jsonEFormData.getNghNgaySinh()+"</nghNgaySinh>"
                        + "<nghDanToc>"+jsonEFormData.getNghDanToc().getValue()+"</nghDanToc>"
                        + "<nghDanTocKhac>"+jsonEFormData.getNghDanTocKhac()+"</nghDanTocKhac>"
                        + "<nghQuocTich>"+jsonEFormData.getNghQuocTich().getValue()+"</nghQuocTich>"
                        + "<nghQuocTichKhac>"+nghQuocTichKhacValue+"</nghQuocTichKhac>"
                        + "<nghLoaiCuTru>"+jsonEFormData.getNghLoaiCuTru()+"</nghLoaiCuTru>"
                        + "<nghNoiCuTru>"+jsonEFormData.getNghNoiCuTru()+"</nghNoiCuTru>"
                        + "<nghLoaiGiayToTuyThan>"+jsonEFormData.getNghLoaiGiayToTuyThan()+"</nghLoaiGiayToTuyThan>"
                        + "<nghGiayToKhac>"+jsonEFormData.getNghGiayToKhac()+"</nghGiayToKhac>"
                        + "<nghSoGiayToTuyThan>"+jsonEFormData.getNghSoGiayToTuyThan()+"</nghSoGiayToTuyThan>"
                        + "<nghNgayCapGiayToTuyThan>"+jsonEFormData.getNghNgayCapGiayToTuyThan()+"</nghNgayCapGiayToTuyThan>"
                        + "<nghNoiCapGiayToTuyThan>"+jsonEFormData.getNghNoiCapGiayToTuyThan()+"</nghNoiCapGiayToTuyThan>"
                        + "<dghHoTen>"+jsonEFormData.getDghHoTen()+"</dghHoTen>"
                        + "<dghGioiTinh>"+jsonEFormData.getDghGioiTinh()+"</dghGioiTinh>"
                        + "<dghNgaySinh>"+jsonEFormData.getDghNgaySinh()+"</dghNgaySinh>"
                        + "<dghNoiSinh>"+jsonEFormData.getDghNoiSinh()+"</dghNoiSinh>"
                        + "<dghDanToc>"+jsonEFormData.getDghDanToc().getValue()+"</dghDanToc>"
                        + "<dghDanTocKhac>"+jsonEFormData.getDghDanTocKhac()+"</dghDanTocKhac>"
                        + "<dghQuocTich>"+jsonEFormData.getDghQuocTich().getValue()+"</dghQuocTich>"
                        + "<dghQuocTichKhac>"+dghQuocTichKhacValue+"</dghQuocTichKhac>"
                        + "<dghLoaiCuTru>"+jsonEFormData.getDghLoaiCuTru()+"</dghLoaiCuTru>"
                        + "<dghNoiCuTru>"+jsonEFormData.getDghNoiCuTru()+"</dghNoiCuTru>"
                        + "<dghLoaiGiayToTuyThan>"+jsonEFormData.getDghLoaiGiayToTuyThan()+"</dghLoaiGiayToTuyThan>"
                        + "<dghGiayToKhac>"+jsonEFormData.getDghGiayToKhac()+"</dghGiayToKhac>"
                        + "<dghSoGiayToTuyThan>"+jsonEFormData.getDghSoGiayToTuyThan()+"</dghSoGiayToTuyThan>"
                        + "<dghNgayCapGiayToTuyThan>"+jsonEFormData.getDghNgayCapGiayToTuyThan()+"</dghNgayCapGiayToTuyThan>"
                        + "<dghNoiCapGiayToTuyThan>"+jsonEFormData.getDghNoiCapGiayToTuyThan()+"</dghNoiCapGiayToTuyThan>"
                        + "<dghLyDoChamDutGiamHo>"+jsonEFormData.getDghLyDoChamDutGiamHo()+"</dghLyDoChamDutGiamHo>"
                        + "<ghNoiDangKyGiamHo>"+jsonEFormData.getGhNoiDangKyGiamHo().getValue()+"</ghNoiDangKyGiamHo>"
                        + "<ghSoDangKyGiamHo>"+jsonEFormData.getGhSoDangKyGiamHo()+"</ghSoDangKyGiamHo>"
                        + "<ghNgayDangKyKetHon>"+jsonEFormData.getGhNgayDangKyKetHon()+"</ghNgayDangKyKetHon>"
                        + "<ghNgayDangKyGiamHo></ghNgayDangKyGiamHo>"
                        + "<nycHoTen>"+jsonEFormData.getNycHoTen()+"</nycHoTen>"
                        + "<nycQuanHe>"+jsonEFormData.getNycQuanHe()+"</nycQuanHe>"
                        + "<nycLoaiGiayToTuyThan>"+jsonEFormData.getNycLoaiGiayToTuyThan()+"</nycLoaiGiayToTuyThan>"
                        + "<nycGiayToKhac>"+jsonEFormData.getNycGiayToKhac()+"</nycGiayToKhac>"
                        + "<nycSoGiayToTuyThan>"+jsonEFormData.getNycSoGiayToTuyThan()+"</nycSoGiayToTuyThan>"
                        + "<nycNgayCapGiayToKhac>"+jsonEFormData.getNycNgayCapGiayToKhac()+"</nycNgayCapGiayToKhac>"
                        + "<nycNoiCapGiayToKhac>"+jsonEFormData.getNycNoiCapGiayToKhac()+"</nycNoiCapGiayToKhac>"
                        + "</hoso></hotich>";
                break;    
             case "KT": 
                 result = "<?xmlversion='1.0'encoding='utf-8'?>"
                        + "<hotich><hoso>"
                        + "<id>"+jsonEFormData.getId()+"</id>"
                        + "<so>"+jsonEFormData.getSo()+"</so>"
                        + "<quyenSo>"+jsonEFormData.getQuyenSo()+"</quyenSo>"
                        + "<trangSo>"+jsonEFormData.getTrangSo()+"</trangSo>"
                        + "<ngayDangKy>"+ ngayDangKy +"</ngayDangKy>"
                        + "<loaiDangKy>"+jsonEFormData.getLoaiDangKy()+"</loaiDangKy>"
                        + "<noiDangKy>"+jsonEFormData.getNoiDangKy().getValue()+"</noiDangKy>"
                        + "<nguoiKy>"+jsonEFormData.getNguoiKy()+"</nguoiKy>"
                        + "<chucVuNguoiKy>"+jsonEFormData.getChucVuNguoiKy()+"</chucVuNguoiKy>"
                        + "<nguoiThucHien>"+jsonEFormData.getNguoiThucHien()+"</nguoiThucHien>"
                        + "<ghiChu>"+jsonEFormData.getGhiChu()+"</ghiChu>"
                        + "<nktHoTen>"+jsonEFormData.getNktHoTen()+"</nktHoTen>"
                        + "<nktGioiTinh>"+jsonEFormData.getNktGioiTinh()+"</nktGioiTinh>"
                        + "<nktNgaySinh>"+nktNgaySinh+"</nktNgaySinh>"
                        + "<nktDanToc>"+jsonEFormData.getNktDanToc().getValue()+"</nktDanToc>"
                        + "<nktDanTocKhac>"+jsonEFormData.getNktDanTocKhac()+"</nktDanTocKhac>"
                        + "<nktQuocTich>"+jsonEFormData.getNktQuocTich().getValue()+"</nktQuocTich>"
                        + "<nktQuocTichKhac>"+nktQuocTichKhacValue+"</nktQuocTichKhac>"
                        + "<nktLoaiCuTru>"+jsonEFormData.getNktLoaiCuTru()+"</nktLoaiCuTru>"
                        + "<nktNoiCuTru>"+jsonEFormData.getNktNoiCuTru()+"</nktNoiCuTru>"
                        + "<nktLoaiGiayToTuyThan>"+jsonEFormData.getNktLoaiGiayToTuyThan()+"</nktLoaiGiayToTuyThan>"
                        + "<nktGiayToKhac>"+jsonEFormData.getNktGiayToKhac()+"</nktGiayToKhac>"
                        + "<nktSoGiayToTuyThan>"+jsonEFormData.getNktSoGiayToTuyThan()+"</nktSoGiayToTuyThan>"
                        + "<nktNgayCapGiayToTuyThan>"+jsonEFormData.getNktNgayCapGiayToTuyThan()+"</nktNgayCapGiayToTuyThan>"
                        + "<nktNoiCapGiayToTuyThan>"+jsonEFormData.getNktNoiCapGiayToTuyThan()+"</nktNoiCapGiayToTuyThan>"
                        + "<nktNgayChet>"+jsonEFormData.getNktNgayChet()+"</nktNgayChet>"
                        + "<nktGioPhutChet>"+jsonEFormData.getNktGioPhutChet()+"</nktGioPhutChet>"
                        + "<nktNoiChet>"+jsonEFormData.getNktNoiChet()+"</nktNoiChet>"
                        + "<nktNguyenNhanChet>"+jsonEFormData.getNktNguyenNhanChet()+"</nktNguyenNhanChet>"
                        + "<nktTinhTrangTuyenBoViecChet>"+jsonEFormData.getNktTinhTrangTuyenBoViecChet()+"</nktTinhTrangTuyenBoViecChet>"
                        + "<nktNgayGhiChuTuyenBoViecChet>"+jsonEFormData.getNktNgayGhiChuTuyenBoViecChet()+"</nktNgayGhiChuTuyenBoViecChet>"
                        + "<nktCanCuTuyenBoViecChet>"+jsonEFormData.getNktCanCuTuyenBoViecChet()+"</nktCanCuTuyenBoViecChet>"
                        + "<nktNgayGhiChuHuyTuyenBoViecChet>"+jsonEFormData.getNktNgayGhiChuHuyTuyenBoViecChet()+"</nktNgayGhiChuHuyTuyenBoViecChet>"
                        + "<nktCanCuHuyTuyenBoViecChet>"+jsonEFormData.getNktCanCuHuyTuyenBoViecChet()+"</nktCanCuHuyTuyenBoViecChet>"
                        + "<gbtLoai>"+jsonEFormData.getGbtLoai()+"</gbtLoai>"
                        + "<gbtSo>"+jsonEFormData.getGbtSo()+"</gbtSo>"
                        + "<gbtNgay>"+jsonEFormData.getGbtNgay()+"</gbtNgay>"
                        + "<gbtCoQuanCap>"+jsonEFormData.getGbtCoQuanCap()+"</gbtCoQuanCap>"
                        + "<nycHoTen>"+jsonEFormData.getNycHoTen()+"</nycHoTen>"
                        + "<nycQuanHe>"+jsonEFormData.getNycQuanHe()+"</nycQuanHe>"
                        + "<nycLoaiGiayToTuyThan>"+jsonEFormData.getNycLoaiGiayToTuyThan()+"</nycLoaiGiayToTuyThan>"
                        + "<nycGiayToKhac>"+jsonEFormData.getNycGiayToKhac()+"</nycGiayToKhac>"
                        + "<nycSoGiayToTuyThan>"+jsonEFormData.getNycSoGiayToTuyThan()+"</nycSoGiayToTuyThan>"
                        + "<nycNgayCapGiayToKhac>"+jsonEFormData.getNycNgayCapGiayToKhac()+"</nycNgayCapGiayToKhac>"
                        + "<nycNoiCapGiayToKhac>"+jsonEFormData.getNycNoiCapGiayToKhac()+"</nycNoiCapGiayToKhac>"
                        + "<soDangKyNuocNgoai>"+jsonEFormData.getSoDangKyNuocNgoai()+"</soDangKyNuocNgoai>"
                        + "<ngayDangKyNuocNgoai>"+jsonEFormData.getNgayDangKyNuocNgoai()+"</ngayDangKyNuocNgoai>"
                        + "<cqNuocNgoaiDaDangKy>"+jsonEFormData.getCqNuocNgoaiDaDangKy()+"</cqNuocNgoaiDaDangKy>"
                        + "<qgNuocNgoaiDaDangKy>"+jsonEFormData.getQgNuocNgoaiDaDangKy().getValue()+"</qgNuocNgoaiDaDangKy>"
                        + "</hoso></hotich>";
                break;
//                case "LTKH": 
//                result = "<?xmlversion='1.0'encoding='utf-8'?>"
//                        + "<hotich><hoso>"
//                        + "<id>"+jsonEFormData.getId()+"</id>"
////                        + "<so>"+jsonEFormData.getSo()+"</so>"
////                        + "<quyenSo>"+jsonEFormData.getQuyenSo()+"</quyenSo>"
////                        + "<trangSo>"+jsonEFormData.getTrangSo()+"</trangSo>"
//                        + "<ngayDangKy>"+ngayDangKy+"</ngayDangKy>"
//                        + "<loaiDangKy>"+jsonEFormData.getLoaiDangKy()+"</loaiDangKy>"
//                        + "<noiDangKy>"+jsonEFormData.getNoiDangKy().getValue()+"</noiDangKy>"
//                        + "<nguoiKy>"+jsonEFormData.getNguoiKy()+"</nguoiKy>"
//                        + "<chucVuNguoiKy>"+jsonEFormData.getChucVuNguoiKy()+"</chucVuNguoiKy>"
//                        + "<ngayXacLapQuanHeHonNhan>"+jsonEFormData.getNgayXacLapQuanHeHonNhan()+"</ngayXacLapQuanHeHonNhan>"
//                        + "<nguoiThucHien>"+jsonEFormData.getNguoiThucHien()+"</nguoiThucHien>"
//                        + "<ghiChu>"+jsonEFormData.getGhiChu()+"</ghiChu>"
//                        + "<soBanSao>"+jsonEFormData.getSoBanSao()+"</soBanSao>"
//                        + "<chongHoTen>"+jsonEFormData.getChongHoTen()+"</chongHoTen>"
//                        + "<chongNgaySinh>"+ chongNgaySinh +"</chongNgaySinh>"
//                        + "<chongDanToc>"+jsonEFormData.getChongDanToc().getValue()+"</chongDanToc>"
//                        + "<chongDanTocKhac>"+jsonEFormData.getChongDanTocKhac()+"</chongDanTocKhac>"
//                        + "<chongQuocTich>"+jsonEFormData.getChongQuocTich().getValue()+"</chongQuocTich>"
//                        + "<chongQuocTichKhac>"+chongQuocTichKhacValue+"</chongQuocTichKhac>"
//                        + "<chongLoaiCuTru>"+jsonEFormData.getChongLoaiCuTru()+"</chongLoaiCuTru>"
//                        + "<chongNoiCuTru>"+jsonEFormData.getChongNoiCuTru()+"</chongNoiCuTru>"
//                        + "<chongMaNoiCuTru>"+jsonEFormData.getChongMaNoiCuTru()+"</chongMaNoiCuTru>"
//                        + "<chongLoaiGiayToTuyThan>"+jsonEFormData.getChongLoaiGiayToTuyThan()+"</chongLoaiGiayToTuyThan>"
//                        + "<chongGiayToKhac>"+jsonEFormData.getChongGiayToKhac()+"</chongGiayToKhac>"
//                        + "<chongSoGiayToTuyThan>"+jsonEFormData.getChongSoGiayToTuyThan()+"</chongSoGiayToTuyThan>"
//                        + "<chongNgayCapGiayToTuyThan>"+jsonEFormData.getChongNgayCapGiayToTuyThan()+"</chongNgayCapGiayToTuyThan>"
//                        + "<chongNoiCapGiayToTuyThan>"+jsonEFormData.getChongNoiCapGiayToTuyThan()+"</chongNoiCapGiayToTuyThan>"
//                        + "<chongSoLanKH>"+jsonEFormData.getChongSoLanKH()+"</chongSoLanKH>"
//                        + "<chongTinhTrangHonNhan>"+jsonEFormData.getChongTinhTrangHonNhan()+"</chongTinhTrangHonNhan>"
//                        + "<chongYeuCauXNTTHN>"+jsonEFormData.getChongYeuCauXNTTHN()+"</chongYeuCauXNTTHN>"
//                        + "<chongNoiXNTTHN>"+jsonEFormData.getChongNoiXNTTHN()+"</chongNoiXNTTHN>"
//                        + "<voHoTen>"+jsonEFormData.getVoHoTen()+"</voHoTen>"
//                        + "<voNgaySinh>"+ voNgaySinh +"</voNgaySinh>"
//                        + "<voDanToc>"+jsonEFormData.getVoDanToc().getValue()+"</voDanToc>"
//                        + "<voDanTocKhac>"+jsonEFormData.getVoDanTocKhac()+"</voDanTocKhac>"
//                        + "<voQuocTich>"+jsonEFormData.getVoQuocTich().getValue()+"</voQuocTich>"
//                        + "<voQuocTichKhac>"+ voQuocTichKhacValue +"</voQuocTichKhac>"
//                        + "<voLoaiCuTru>"+jsonEFormData.getVoLoaiCuTru()+"</voLoaiCuTru>"
//                        + "<voNoiCuTru>"+jsonEFormData.getVoNoiCuTru()+"</voNoiCuTru>"
//                        + "<voMaNoiCuTru>"+jsonEFormData.getVoMaNoiCuTru()+"</voMaNoiCuTru>"
//                        + "<voLoaiGiayToTuyThan>"+jsonEFormData.getVoLoaiGiayToTuyThan()+"</voLoaiGiayToTuyThan>"
//                        + "<voGiayToKhac>"+jsonEFormData.getVoGiayToKhac()+"</voGiayToKhac>"
//                        + "<voSoGiayToTuyThan>"+jsonEFormData.getVoSoGiayToTuyThan()+"</voSoGiayToTuyThan>"
//                        + "<voNgayCapGiayToTuyThan>"+jsonEFormData.getVoNgayCapGiayToTuyThan()+"</voNgayCapGiayToTuyThan>"
//                        + "<voNoiCapGiayToTuyThan>"+jsonEFormData.getVoNoiCapGiayToTuyThan()+"</voNoiCapGiayToTuyThan>"
//                        + "<voSoLanKH>"+jsonEFormData.getVoSoLanKH()+"</voSoLanKH>"
//                        + "<voTinhTrangHonNhan>"+jsonEFormData.getVoTinhTrangHonNhan()+"</voTinhTrangHonNhan>"
//                        + "<voYeuCauXNTTHN>"+jsonEFormData.getVoYeuCauXNTTHN()+"</voYeuCauXNTTHN>"
//                        + "<voNoiXNTTHN>"+jsonEFormData.getVoNoiXNTTHN()+"</voNoiXNTTHN>"
//                        + "</hoso></hotich>";
//                break;
            default: result = "";break;
        } 
        
        //Set dataMml 
        this.data = result;
        logger.info("HCMLGSP-HTTP Get DATA XML eform dossier: " + result);
 
        } catch (Exception e) {
            logger.info("erro mapping data HTTPDossier:  " + e.getMessage());
        }
    }
    
    private List<String> getQuocTichKhacArr(List<HCMLGSPDossierDetailHTTPDto.LableData> quocTichKhacArr){
        return quocTichKhacArr.stream().map(HCMLGSPDossierDetailHTTPDto.LableData::getValue).collect(Collectors.toList());
    }
}
