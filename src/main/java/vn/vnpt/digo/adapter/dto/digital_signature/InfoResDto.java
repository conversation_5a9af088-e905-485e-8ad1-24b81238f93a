package vn.vnpt.digo.adapter.dto.digital_signature;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InfoResDto implements Serializable {
    @JsonProperty("KetQuaTruyVan")
    private String KetQuaTruyVan;
    @JsonProperty("DuLieu")

    private List<DuLieu> DuLieu;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class DuLieu {
        @JsonProperty("TaiKhoanKySo")

        private String TaiKhoanKySo;
        @JsonProperty("TenDaiDien")

        private String TenDaiDien;
        @JsonProperty("MaDonVi")

        private String MaDonVi;
        @JsonProperty("EmailOTP")

        private String EmailOTP;
        @JsonProperty("LoaiChuKy")

        private String LoaiChuKy;
    }
}
