package vn.vnpt.digo.adapter.dto.industry_trade_bd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String fullname;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date birthday;

    private Byte gender;

    private ArrayList<PhoneNumber> phoneNumber;

    private ArrayList<Email> email;

    private ArrayList<Address> address;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PhoneNumber implements Serializable {

        private String value;
        @JsonIgnore
        private Boolean status = true;
        private Boolean primary = false;

        public PhoneNumber(String value) {
            this.value = value;
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Address implements Serializable {

        private String address;

        private Short type = 1;

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId placeId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Email implements Serializable {

        private String value;

        @JsonIgnore
        private Boolean status = true;

        private Boolean primary = false;

        public Email(String value) {
            this.value = value;
        }
    }
}
