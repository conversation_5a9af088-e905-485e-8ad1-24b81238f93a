package vn.vnpt.digo.adapter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BoTNMTPsGetListResDto implements Serializable {

    private List<BoTNMTPsDossierDto> dosser = new ArrayList<>();
    private Integer totalRows = 0;
    private boolean last = true;
}
