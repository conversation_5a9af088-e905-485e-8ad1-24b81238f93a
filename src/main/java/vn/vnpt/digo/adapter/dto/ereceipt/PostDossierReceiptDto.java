/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.ereceipt;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.data.mongodb.core.mapping.Field;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.bson.types.ObjectId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.EReceiptBodyDto;
import vn.vnpt.digo.adapter.pojo.qbh.DossierReceiptExtend;
import  vn.vnpt.digo.adapter.dto.hcm.extendHCM;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PostDossierReceiptDto implements Serializable {

    @NotNull
    private ReceiptDossier dossier;

    private ReceiptType type;

    @NotNull
    private ReceiptData data;

    private Integer status;

    private String userName;

    private extendHCM extendHCM;

    private String procedureName;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReceiptDossier implements Serializable {

        @Field("id")
        @JsonProperty("id")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        private String code;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReceiptType implements Serializable {

        @Field("id")
        @JsonProperty("id")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        private ArrayList<TranslateName> name;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class TranslateName implements Serializable{

            private Short languageId;
            private String name;
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReceiptData implements Serializable {

        @Field("configId")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId configId;

        @Field("agencyId")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId agencyId;

        @Field("subsystemId")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId subsystemId;

        @NotNull
        private String fkey;

        //QNM : Hoa don dien tu VietTel
        private String invoicePattern;
        private String invoiceNo;

        private String cusCode;
        
        private String cusTaxCode;

        private String cusName;

        private String cusAddress;
        
        private String cusPhoneNumber;

        private String paymentMethod;
        
        @JsonProperty("responeReceiptInfo")
        private EReceiptBodyDto.ResponeReceiptInfo responeReceiptInfo;

        private ArrayList<ProductInvoiceReceipt> product;

        private Double total;

        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date arisingDate;

        private VietInfoData vietInfoData;

        private File file;

        // QNI
        private vn.vnpt.digo.adapter.pojo.qni.EReceiptExtend ereceiptExtendPublisherQni;

        private String email;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class VietInfoData implements Serializable {

            private String receiptCode;

            private String ticket;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class File implements Serializable {

            private String id;

            private String name;
        }
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ProductInvoiceReceipt implements Serializable {

            @Field("prodId")
            @JsonSerialize(using = ToStringSerializer.class)
            private ObjectId prodId;

            private String prodName;

            private String prodUnit;

            private Integer prodQuantity;

            private Double prodPrice;

            private Double prodAmount;

            @JsonSerialize(using = ToStringSerializer.class)
            private ObjectId dossierFeeId;

            private Double paid;

        }

    }

    private DossierReceiptExtend extendQBH;

    private Boolean isReceiptDossierQnm;
}
