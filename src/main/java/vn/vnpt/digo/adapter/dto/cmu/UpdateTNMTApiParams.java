package vn.vnpt.digo.adapter.dto.cmu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdateTNMTApiParams {

    @JsonProperty("MaTinh")
    private String maTinh;

    @JsonProperty("Data")
    private List<Data> dataList;

    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Data {

        @JsonProperty("MaHoSo")
        private String maHoSo;

        @JsonProperty("NgayTiepNhan")
        private String ngayTiepNhan;

        @JsonProperty("NgayHenTra")
        private String ngayHenTra;

        @JsonProperty("TrangThaiHoSo")
        private String trangThaiHoSo;

        @JsonProperty("NgayTra")
        private String ngayTra;

        @JsonProperty("ThongTinTra")
        private Boolean thongTinTra;

        @JsonProperty("HinhThuc")
        private String hinhThuc;

        @JsonProperty("NgayKetThucXuLy")
        private String ngayKetThucXuLy;

        @JsonProperty("DonViXuLy")
        private String donViXuLy;

        @JsonProperty("GhiChu")
        private String ghiChu;

        @JsonProperty("DanhSachHoSoBoSung")
        private List<HoSoBoSung> danhSachHoSoBoSung;

        @JsonProperty("DanhSachGiayToKetQua")
        private List<GiayToKetQua> danhSachGiayToKetQua;

        @JsonProperty("QuaTrinhXuLy")
        private List<QuaTrinhXuLy> quaTrinhXuLyList;
    }

    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class GiayToKetQua{
        @JsonProperty("TenGiayTo")
        private String tenGiayTo;
        @JsonProperty("MaThanhPhanHoSo")
        private String maThanhPhanHoSo;
        @JsonProperty("GiayToId")
        private String giayToId;
        @JsonProperty("DuongDanTepTinKetQua")
        private String duongDanTepTinKetQua;
    }
    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class HoSoBoSung{
        @JsonProperty("HoSoBoSungId")
        private String hoSoBoSungId;
        @JsonProperty("NguoiYeuCauBoSung")
        private String nguoiYeuCauBoSung;
        @JsonProperty("NoiDungBoSung")
        private String noiDungBoSung;
        @JsonProperty("NgayBoSung")
        private String ngayBoSung;
        @JsonProperty("NguoiTiepNhanBoSung")
        private String nguoiTiepNhanBoSung;
        @JsonProperty("ThongTinTiepNhan")
        private String thongTinTiepNhan;
        @JsonProperty("NgayTiepNhanBoSung")
        private String ngayTiepNhanBoSung;
        @JsonProperty("TrangThaiBoSung")
        private String trangThaiBoSung;
        @JsonProperty("DanhSachGiayToBoSung")
        private List<TepDinhKem> danhSachGiayToBoSung;
        @JsonProperty("DanhSachLePhiBoSung")
        private List<LePhi> danhSachLePhiBoSung;
        @JsonProperty("NgayHenTraTruoc")
        private String ngayHenTraTruoc;
        @JsonProperty("NgayHenTraMoi")
        private String ngayHenTraMoi;
    }
    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class QuaTrinhXuLy {

        @JsonProperty("MaHoSo")
        private String maHoSo;

        @JsonProperty("NguoiXuLy")
        private String nguoiXuLy;

        @JsonProperty("ChucDanh")
        private String chucDanh;

        @JsonProperty("ThoiDiemXuLy")
        private String thoiDiemXuLy;

        @JsonProperty("PhongBanXuLy")
        private String phongBanXuLy;

        @JsonProperty("NoiDungXuLy")
        private String noiDungXuLy;

        @JsonProperty("TrangThai")
        private String trangThai;

        @JsonProperty("NgayBatDau")
        private String ngayBatDau;

        @JsonProperty("NgayKetThucTheoQuyDinh")
        private String ngayKetThucTheoQuyDinh;
    }
    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TepDinhKem{

        @JsonProperty("TepDinhKemId")
        private String tepDinhKemId;

        @JsonProperty("TenTepDinhKem")
        private String tenTepDinhKem;

        @JsonProperty("IsDeleted")
        private Boolean isDeleted;

        @JsonProperty("MaThanhPhanHoSo")
        private String maThanhPhanHoSo;

        @JsonProperty("DuongDanTaiTepTin")
        private String duongDanTaiTepTin;
    }
    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class LePhi{
        @JsonProperty("TenPhiLePhi")
        private String tenPhiLePhi;
        @JsonProperty("MaPhiLePhi")
        private String maPhiLePhi;
        @JsonProperty("LoaiPhiLePhi")
        private String loaiPhiLePhi;
        @JsonProperty("HinhThucThu")
        private String hinhThucThu;
        @JsonProperty("Gia")
        private String gia;
    }
}
