package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DpPaymentLookupOutputDto implements Serializable {
    @JsonProperty("MaTraCuuTT")
    private String maTraCuuTT;
    
    @JsonProperty("MaDichVu")
    private String maDichVu = "2";
    
    @JsonProperty("MaDVC")
    private String maDVC;
    
    @JsonProperty("TenDVC")
    private String tenDVC;
    
    @JsonProperty("MaTTHC")
    private String MaTTHC;
    
    @JsonProperty("TenTTHC")
    private String tenTTHC;
    
    @JsonProperty("MaDonVi")
    private String maDonVi;
    
    @JsonProperty("TenDonVi")
    private String tenDonVi;
    
    @JsonProperty("MaHoSo")
    private String maHoSo;
    
    @JsonProperty("PhiLePhi")
    private ArrayList<PhiLePhiDto> phiLePhi;
    
    @JsonProperty("TKThuHuong")
    private String tKThuHuong;
    
    @JsonProperty("MaNHThuHuong")
    private String maNHThuHuong;
    
    @JsonProperty("TenTKThuHuong")
    private String tenTKThuHuong;
    
    @JsonProperty("MaLoaiHinhThuPhat")
    private String maLoaiHinhThuPhat = "";
    
    @JsonProperty("TenLoaiHinhThuPhat")
    private String tenLoaiHinhThuPhat = "";
    
    @JsonProperty("MaCoQuanQD")
    private String maCoQuanQD = "";
    
    @JsonProperty("TenCoQuanQD")
    private String tenCoQuanQD = "";
    
    @JsonProperty("KhoBac")
    private String khoBac = "";
    
    @JsonProperty("NgayQD")
    private String NgayQD = "";
    
    @JsonProperty("SoQD")
    private String soQD = "";
    
    @JsonProperty("ThoiGianViPham")
    private String thoiGianViPham = "";
    
    @JsonProperty("DiaDiemViPham")
    private String diaDiemViPham = "";
    
    @JsonProperty("TenNguoiViPham")
    private String tenNguoiViPham = "";
    
    @JsonProperty("TaiKhoanThuNSNN")
    private String taiKhoanThuNSNN = "";
    
    @JsonProperty("NoiDungThanhToan")
    private String noiDungThanhToan;
    
    @JsonProperty("HoTenNguoiNop")
    private String hoTenNguoiNop;
    
    @JsonProperty("SoCMNDNguoiNop")
    private String soCMNDNguoiNop;
    
    @JsonProperty("DiaChiNguoiNop")
    private String diaChiNguoiNop;
    
    @JsonProperty("HuyenNguoiNop")
    private String huyenNguoiNop;
    
    @JsonProperty("TinhNguoiNop")
    private String tinhNguoiNop;
    
    @JsonProperty("DSKhoanNop")
    private ArrayList<DSKhoanNopDto> dSKhoanNop;
    
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PhiLePhiDto {

        @JsonProperty("LoaiPhiLePhi")
        private String loaiPhiLePhi = "1";
        
        @JsonProperty("MaPhiLePhi")
        private String maPhiLePhi;
        
        @JsonProperty("TenPhiLePhi")
        private String tenPhiLePhi;
        
        @JsonProperty("SoTien")
        private String soTien;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class DSKhoanNopDto {

        @JsonProperty("NoiDung")
        private String noiDung;
        
        @JsonProperty("SoTien")
        private String soTien;
    }
}
