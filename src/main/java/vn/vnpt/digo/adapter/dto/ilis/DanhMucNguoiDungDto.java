/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.dto.ilis;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DanhMucNguoiDungDto implements Serializable {

    @JsonProperty("MaNguoiDung")
    public String maNguoiDung;

    @JsonProperty("TenNguoiDung")
    public String tenNguoiDung;

    @JsonProperty("TenPhongBan")
    public String tenPhongBan;

    @JsonProperty("ChucDanh")
    public String chucDanh;
}
