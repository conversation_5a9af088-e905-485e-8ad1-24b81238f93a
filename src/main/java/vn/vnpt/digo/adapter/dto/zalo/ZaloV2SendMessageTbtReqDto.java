package vn.vnpt.digo.adapter.dto.zalo;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZaloV2SendMessageTbtReqDto implements Serializable {

    private Recipient recipient;
    private Message message;

    public ZaloV2SendMessageTbtReqDto(String userId, String text) {
        this.recipient = new Recipient(userId);
        this.message = new Message(text);
    }

    // Thong bao thue (bussiness template)
    public ZaloV2SendMessageTbtReqDto(ZaloV2UserProfileDto userProfile, ZaloV2SendTemplateTbtDto templates, IntegratedConfigurationDto config) {
        this.recipient = new Recipient(userProfile.getData().getUserId());
        List<Element> lstElement = new ArrayList<>();

        // new element with bussiness template
        TemplateData data = new TemplateData(templates, config);
        Element e1 = new Element(templates.getTitle(), config.getParametersValue("message-template").toString(), data);

        lstElement.add(e1);

        if (Objects.equals(config.getParametersValue("contact-enable"), true)) {
            Element e4 = new Element(config.getParametersValue("contact-icon").toString(),
                    config.getParametersValue("contact-description").toString(),
                    new Action(config.getParametersValue("contact-url").toString()));
            lstElement.add(e4);
        }
        Payload payload = new Payload(lstElement, templates.getType());
        Attachment attachment = new Attachment(payload);
        this.message = new Message(attachment);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Recipient implements Serializable {

        @JsonProperty("user_id")
        private String userId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Message implements Serializable {

        private String text;
        private Attachment attachment;

        public Message(String text) {
            this.text = text;
        }

        public Message(Attachment attachment) {
            this.attachment = attachment;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Attachment implements Serializable {

        private Payload payload;
        private String type = "template";

        public Attachment(Payload pl) {
            this.payload = pl;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Payload implements Serializable {

        private List<Element> elements;

        @JsonProperty("template_type")
        private String templateType;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Element implements Serializable {

        @JsonProperty("template_id")
        private String templateId = "";
        @JsonProperty("image_url")
        private String imageUrl;
        @JsonProperty("default_action")
        private Action defaultAction;
        @JsonProperty("template_data")
        private TemplateData templateData;

        private String title;
        private String subtitle;
        private String payload;

        public Element(String imageUrl, String title, Action act) {
            this.imageUrl = imageUrl;
            this.title = title;
            this.defaultAction = act;
        }

        public Element(String title, String templateId, TemplateData data) {
            this.title = title;
            this.templateId = templateId;
            this.templateData = data;
            this.payload = "";
        }

        public Element(String imageUrl, String title, String subtitle) {
            this.imageUrl = imageUrl;
            this.title = title;
            this.subtitle = subtitle;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Action implements Serializable {

        private String type = "oa.open.url";
        private String url;

        public Action(String url) {
            this.url = url;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TemplateData implements Serializable {

        @JsonProperty("ten_nguoi_dan")
        private String fullname;
        @JsonProperty("ma_ho_so")
        private String dossierCode;
        @JsonProperty("trang_thai")
        private String status;
        @JsonProperty("ten_dich_vu")
        private String serviceName;
        @JsonProperty("loi_nhan")
        private String message;

        private String background;

        private String cta_1_icon;
        private String cta_1_text;
        private String cta_1_link;
        
        public TemplateData(ZaloV2SendTemplateTbtDto templates, IntegratedConfigurationDto config){
            this.fullname = templates.getTemplates().getFullname();
            this.dossierCode = templates.getTemplates().getDossierCode();
            this.status = templates.getTemplates().getStatus();
            this.serviceName = templates.getTemplates().getServiceName();
            this.message = templates.getTemplates().getMessage();

            this.background = config.getParametersValue("message-cover");
            this.cta_1_icon = config.getParametersValue("contact-icon").toString();
            this.cta_1_text = config.getParametersValue("contact-description").toString();
            this.cta_1_link = config.getParametersValue("contact-url").toString();
        }
    }
}
