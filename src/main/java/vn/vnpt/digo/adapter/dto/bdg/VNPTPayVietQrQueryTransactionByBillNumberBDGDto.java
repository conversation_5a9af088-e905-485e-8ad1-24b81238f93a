package vn.vnpt.digo.adapter.dto.bdg;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VNPTPayVietQrQueryTransactionByBillNumberBDGDto implements Serializable {

    @JsonProperty("merchantClientId")
    public String merchantClientId = null;

    @JsonProperty("merchantCode")
    public String merchantCode = null;

    @JsonProperty("terminalId")
    public String terminalId = null;

    @JsonProperty("billNumber")
    public String billNumber = null;

    @JsonProperty("checksum")
    public String checksum = null;

}
