package vn.vnpt.digo.adapter.dto.bdg;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VNPTPayInit3Dto implements Serializable {
    @JsonProperty("merchantClientId")
    public String merchantClientId = null;

    @JsonProperty("merchantName")
    public String merchantName = null;

    @JsonProperty("merchantCode")
    public String merchantCode = null;

    @JsonProperty("terminalId")
    public String terminalId = null;

    @JsonProperty("countryCode")
    public String countryCode = null;

    @JsonProperty("qrCodeType")
    public String qrCodeType = null;

    @JsonProperty("txnId")
    public String txnId = null;

    @JsonProperty("billNumber")
    public String billNumber = null;

    @JsonProperty("amount")
    public String amount = null;

    @JsonProperty("ccy")
    public String ccy = null;

    @JsonProperty("expDate")
    public String expDate = null;

    @JsonProperty("mcc")
    public String mcc = null;

    @JsonProperty("tipAndFee")
    public String tipAndFee = null;

    @JsonProperty("consumerId")
    public String consumerId = null;

    @JsonProperty("purpose")
    public String purpose = null;

    @JsonProperty("addInfo")
    public String addInfo = null;

    @JsonProperty("merchantData")
    public String merchantData = null;

    @JsonProperty("checksum")
    public String checksum = null;

//    @JsonProperty("paymentDetail")
//    public String paymentDetail = null;

    @JsonProperty("paymentDetail")
    public List<VnptVietQrPaymentDetail> paymentDetail = null;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VnptVietQrPaymentDetail {
        @JsonProperty("itemType")
        public int itemType;

        @JsonProperty("itemCode")
        public String itemCode;

        @JsonProperty("itemName")
        public String itemName;

        @JsonProperty("itemAmount")
        public double itemAmount;

        @JsonProperty("itemDescription")
        public String itemDescription;
    }
}
