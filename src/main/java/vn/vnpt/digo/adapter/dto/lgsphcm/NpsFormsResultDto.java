/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.lgsphcm.NpsFormsOtherInfoDto;
/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsFormsResultDto implements Serializable {
    private String code;

    private String name;

    private NpsFormsOtherInfoDto otherInformation = new NpsFormsOtherInfoDto();
    
    @JsonSetter("MAGIAYTO")
    public void mapperCode(String code) {
        this.code = code;
    }

    @JsonSetter("TENGIAYTO")
    public void mapperName(String name) {
        this.name = name;
    }

    @JsonSetter("LOAIGIAYTO")
    public void mapperBranchCode(String branchCode) {
        this.otherInformation.setLoaiGiayTo(branchCode);
    }
    
    @JsonSetter("ROW_STT")
    public void mapperRowStt(String ROW_STT) {
        this.otherInformation.setStt(ROW_STT);
    }
    
    @JsonSetter("TEPDINHKEM")
    public void mapperTepDinhKem(String tepDinhKem) {
        this.otherInformation.setTepDinhKem(tepDinhKem);
    }
}
