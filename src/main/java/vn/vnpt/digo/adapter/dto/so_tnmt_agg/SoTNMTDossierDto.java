package vn.vnpt.digo.adapter.dto.so_tnmt_agg;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.dto.DossierFormFileDto;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
@Data
public class SoTNMTDossierDto implements Serializable {
    //@NotNull
    private ObjectId configId;

    @SerializedName("dossierId")
    @Expose
    private String dossierId;

    @SerializedName("organId")
    @Expose
    //@NotNull
    private String organId;

    @SerializedName("fieldCode")
    @Expose
    //@NotNull
    private String fieldCode;

    @SerializedName("processCode")
    @Expose
    //@NotNull
    private String processCode;

    @SerializedName("thongTinHoSo")
    @Expose
    private ThongTinHoSo thongTinHoSo;

    @SerializedName("duLieuChuyenNganh")
    @Expose
    private DuLieuChuyenNganh duLieuChuyenNganh;

    @SerializedName("listFileIds")
    @Expose
    private ArrayList<FileAttachment> listFileIds;



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ThongTinHoSo {

        @JsonProperty("cVXuLy")
        private String cVXuLy;

        @JsonProperty("diaChiNguoiNop")
        private String diaChiNguoiNop;

        @JsonProperty("dienThoai")
        private String dienThoai;

        @JsonProperty("maBNHS")
        private String maBNHS;

        @JsonProperty("ngayHenTraHS")
        private String ngayHenTraHS;

        @JsonProperty("ngayNhanHS")
        private String ngayNhanHS;

        @JsonProperty("phongBanXuLy")
        private String phongBanXuLy;

        @JsonProperty("soCMND")
        private String soCMND;

        @JsonProperty("tenNguoiNop")
        private String tenNguoiNop;

        @JsonProperty("tenDVCong")
        private String tenDVCong;

        @JsonProperty("tenToChuc")
        private String tenToChuc;

        @JsonProperty("tinhTrangHS")
        private String tinhTrangHS;

        @JsonProperty("trangThaiXuLy")
        private String trangThaiXuLy;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DuLieuChuyenNganh {
        @JsonProperty("toBD")
        private String toBD;

        @JsonProperty("thuaDatSo")
        private String thuaDatSo;

        @JsonProperty("dienTich")
        private String dienTich;

        @JsonProperty("ghiChuNhan")
        private String ghiChuNhan;

        @JsonProperty("hsGom")
        private HoSoGom hsGom;

        @JsonProperty("maDT")
        private String maDT;

        @JsonProperty("maLoaiQT")
        private String maLoaiQT;

        @JsonProperty("maQG")
        private String maQG;

        @JsonProperty("ngayCoKetQua")
        private String ngayCoKetQua;

        @JsonProperty("maNguoiNhan")
        private String maNguoiNhan;

        @JsonProperty("ghiChuTra")
        private String ghiChuTra;

        @JsonProperty("idOrders")
        private String idOrders;

        @JsonProperty("maDTMG")
        private String maDTMG;

        @JsonProperty("maTyLe")
        private String maTyLe;

        @JsonProperty("maDTThu")
        private String maDTThu;

        @JsonProperty("daTamThu")
        private String daTamThu;

        @JsonProperty("tienTamThu")
        private String tienTamThu;

        @JsonProperty("ngayTamThu")
        private String ngayTamThu;

        @JsonProperty("trichDo")
        private String trichDo;

        @JsonProperty("dienTichTS")
        private String dienTichTS;

        @JsonProperty("soLuongGiay")
        private String soLuongGiay;

        @JsonProperty("soLuongHS")
        private String soLuongHS;

        @JsonProperty("maNNhanTam")
        private String maNNhanTam;

        @JsonProperty("diaChiTT")
        private String diaChiTT;

        @JsonProperty("maBoPhan")
        private String maBoPhan;

        @JsonProperty("tongNgay")
        private String tongNgay;

        @JsonProperty("maThietLap")
        private String maThietLap;

        @JsonProperty("chuDauTu")
        private String chuDauTu;

        @JsonProperty("ngayKyHD")
        private String ngayKyHD;

        @JsonProperty("giaThue")
        private String giaThue;

        @JsonProperty("thoiHan")
        private String thoiHan;

        @JsonProperty("tienSDD")
        private String tienSDD;

        @JsonProperty("soHS")
        private String soHS;

        @JsonProperty("maMDSD")
        private String maMDSD;

        @JsonProperty("soHD")
        private String soHD;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HoSoGom {
        @JsonProperty("string")
        private ArrayList<String> string;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileAttachment {
        @JsonProperty("id")
        private String id;

        @JsonProperty("filename")
        private String fileName;

    }
}
