package vn.vnpt.digo.adapter.dto.tandan;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VanBanDuThaoDto implements Serializable {
    @JsonProperty("TrichYeu")
    private String TrichYeu;

    @JsonProperty("NgayTao")
    private String NgayTao;

    @JsonProperty("DinhKem")
    private List<DinhKemItemDto> DinhKem;
}
