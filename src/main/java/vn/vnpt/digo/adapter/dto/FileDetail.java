package vn.vnpt.digo.adapter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileDetail implements Serializable {
    private String fileId;
    private String procedureFormId;
    private String dossierId;
    private String file_hash;
    private String fileType;
    private Integer isVerifed;
    private String message;
    private String logID;
    private Object object;
    private Integer statusCode;
    private String status;
    private Double similarityScore;
}
