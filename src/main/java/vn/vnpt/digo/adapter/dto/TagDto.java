package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.pojo.Tag;
import vn.vnpt.digo.adapter.pojo.Translate;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TagDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String name;

    private String integratedCode;

    private List<Translate> trans;

    public TagDto(Tag tag, Short localeId) {
        id = tag.getId();
        if (Objects.nonNull(tag.getTrans())) {
            tag.getTrans().forEach(nameItem -> {
                if (nameItem.getLanguageId().equals(localeId)) {
                    name = nameItem.getName();
                }
            });
        }
    }
}
