package vn.vnpt.digo.adapter.dto.iworkplace;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.iworkplace.Attribute;
import vn.vnpt.digo.adapter.pojo.iworkplace.File;
import vn.vnpt.digo.adapter.pojo.iworkplace.Receiver;
import vn.vnpt.digo.adapter.pojo.iworkplace.Src;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class IWorkplaceRequestDto {

    private Long tntid; //TenantId

    private String version; //Version tích hợp

    private Src src;    //Thông tin chung về nguồn dữ liệu

    private String title;   //Tiêu đề

    private String content;     //Nội dung post

    private List<Attribute> index_attributes; //<PERSON><PERSON><PERSON> thuộc tính chính của dữ liệu sẽ được đánh index để tìm kiếm.Nếu không có thuộc tính cần đánh index => truyền mảng rỗng.

    private List<Attribute> other_attributes; //Các thuộc tính phụ của dữ liệu. Không có thuộc tính => truyền mảng rỗng.

    private List<Receiver> receivers;   //Danh sách người nhận post. (nếu là request xóa thì truyền rỗng)

    private List<File> files;
}
