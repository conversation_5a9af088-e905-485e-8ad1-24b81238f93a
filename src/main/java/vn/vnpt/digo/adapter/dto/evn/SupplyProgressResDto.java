package vn.vnpt.digo.adapter.dto.evn;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupplyProgressResDto implements Serializable {
    private Boolean success;
    private Integer errorCode;
    private String status;
    private Integer statusCode;
    private String message;
    private List<SupplyProgress> data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class SupplyProgress {
        @JsonProperty("MA_DVIQLY")
        private String MA_DVIQLY;
        @JsonProperty("KQUA_ID_BUOC")
        private Integer KQUA_ID_BUOC;
        @JsonProperty("MA_DDO_DDIEN")
        private String MA_DDO_DDIEN;
        @JsonProperty("MA_YCAU_KNAI")
        private String MA_YCAU_KNAI;
        @JsonProperty("NDUNG_XLY")
        private String NDUNG_XLY;
        @JsonProperty("NGAY_BDAU_HTHI")
        private String NGAY_BDAU_HTHI;
        @JsonProperty("NGAY_KTHUC_HTHI")
        private String NGAY_KTHUC_HTHI;
        @JsonProperty("TRO_NGAI")
        private Integer TRO_NGAI;
        @JsonProperty("NGUYEN_NHAN")
        private String NGUYEN_NHAN;
        @JsonProperty("SO_LAN")
        private Integer SO_LAN;
        @JsonProperty("NGAY_HEN")
        private String NGAY_HEN;
        @JsonProperty("NGAY_THIEN")
        private String NGAY_THIEN;
        @JsonProperty("TEN_CVIEC")
        private String TEN_CVIEC;
        @JsonProperty("LA_TNGAI")
        private String LA_TNGAI;
        @JsonProperty("sNGAY_TAO")
        private String sNGAY_TAO;
    }
}
