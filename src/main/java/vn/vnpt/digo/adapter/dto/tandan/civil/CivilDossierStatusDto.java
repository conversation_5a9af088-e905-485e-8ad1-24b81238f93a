package vn.vnpt.digo.adapter.dto.tandan.civil;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CivilDossierStatusDto implements Serializable {
    private ObjectId configId;
    private ObjectId agencyId;
    private ObjectId subsystemId;
    private String maDonVi;
    private String maHoSo;
    private List<Map> dossiers;
}
