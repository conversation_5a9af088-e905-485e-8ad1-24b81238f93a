package vn.vnpt.digo.adapter.dto.hbh.tnmt;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateMonreDossierDTO implements Serializable {
    @JsonProperty("MaHoSo")
    private String MaHoSo;
    @JsonProperty("NgayTiepNhan")
    private String NgayTiepNhan;
    @JsonProperty("NgayHenTra")
    private String NgayHenTra;
    @JsonProperty("TrangThaiHoSo")
    private String TrangThaiHoSo;
    @JsonProperty("NgayTra")
    private String NgayTra;
    @JsonProperty("ThongTinTra")
    private String ThongTinTra;
    @JsonProperty("HinhThuc")
    private String HinhThuc;
    @JsonProperty("NgayKetThucXuLy")
    private String NgayKetThucXuLy;
    @JsonProperty("DonViXuLy")
    private String DonViXuLy;
    @JsonProperty("GhiChu")
    private String GhiChu;
    @JsonProperty("QuaTrinhXuLy")
    private List<QuaTrinhXuLy> QuaTrinhXuLy;
    @JsonProperty("DanhSachGiayToKetQua")
    private List<DanhSachGiayToKetQua> DanhSachGiayToKetQua;
    @JsonProperty("DanhSachLePhi")
    private List<DanhSachLePhi> DanhSachLePhi;
    @JsonProperty("DanhSachTepDinhKemKhac")
    private List<DanhSachTepDinhKemKhac> DanhSachTepDinhKemKhac;
    @JsonProperty("DanhSachHoSoBoSung")
    private List<DanhSachHoSoBoSung> DanhSachHoSoBoSung;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuaTrinhXuLy {
        @JsonProperty("MaHoSo")
        private String MaHoSo;
        @JsonProperty("NguoiXuLy")
        private String NguoiXuLy;
        @JsonProperty("ChucDanh")
        private String ChucDanh;
        @JsonProperty("ThoiDiemXuLy")
        private String ThoiDiemXuLy;
        @JsonProperty("PhongBanXuLy")
        private String PhongBanXuLy;
        @JsonProperty("NoiDungXuLy")
        private String NoiDungXuLy;
        @JsonProperty("TrangThai")
        private String TrangThai;
        @JsonProperty("NgayBatDau")
        private String NgayBatDau;
        @JsonProperty("NgayKetThucTheoQuyDinh")
        private String NgayKetThucTheoQuyDinh;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhSachGiayToKetQua {
        @JsonProperty("TenGiayTo")
        private String TenGiayTo;
        @JsonProperty("MaThanhPhanHoSo")
        private String MaThanhPhanHoSo;
        @JsonProperty("GiayToId")
        private String GiayToId;
        @JsonProperty("DuongDanTepTinKetQua")
        private String DuongDanTepTinKetQua;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhSachLePhi {
        @JsonProperty("TenPhiLePhi")
        private String TenPhiLePhi;

        @JsonProperty("MaPhiLePhi")
        private String MaPhiLePhi;

        @JsonProperty("HinhThucThu")
        private String HinhThucThu;

        @JsonProperty("Gia")
        private long Gia;

        @JsonProperty("LoaiPhiLePhi")
        private String LoaiPhiLePhi;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhSachTepDinhKemKhac {
        @JsonProperty("TenGiayTo")
        private String TenGiayTo;

        @JsonProperty("SoLuong")
        private int SoLuong;

        @JsonProperty("LoaiGiayTo")
        private int LoaiGiayTo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DanhSachHoSoBoSung {
        @JsonProperty("HoSoBoSungId")
        private int HoSoBoSungId;

        @JsonProperty("NguoiYeuCauBoSung")
        private String NguoiYeuCauBoSung;

        @JsonProperty("NoiDungBoSung")
        private String NoiDungBoSung;

        @JsonProperty("NgayBoSung")
        private String NgayBoSung;

        @JsonProperty("NguoiTiepNhanBoSung")
        private String NguoiTiepNhanBoSung;

        @JsonProperty("ThongTinTiepNhan")
        private String ThongTinTiepNhan;

        @JsonProperty("NgayTiepNhanBoSung")
        private String NgayTiepNhanBoSung;

        @JsonProperty("TrangThaiBoSung")
        private Boolean TrangThaiBoSung;

        @JsonProperty("DanhSachGiayToBoSung")
        private List<DanhSachTepDinhKemKhac> DanhSachGiayToBoSung;

        @JsonProperty("DanhSachLePhiBoSung")
        private List<DanhSachLePhi> DanhSachLePhiBoSung;

        @JsonProperty("NgayHenTraTruoc")
        private String NgayHenTraTruoc;

        @JsonProperty("NgayHenTraMoi")
        private String NgayHenTraMoi;
    }
}