package vn.vnpt.digo.adapter.dto.csdldc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.eform.CaterogyDto;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DiaChiDetailDto implements Serializable {
    @JsonProperty("ChiTiet")
    private String ChiTiet;

    @JsonProperty("TinhThanh")
    private CaterogyCitizenDto TinhThanh;

    @JsonProperty("PhuongXa")
    private CaterogyCitizenDto PhuongXa;

    @JsonProperty("QuanHuyen")
    private CaterogyCitizenDto QuanHuyen;

    @JsonProperty("QuocGia")
    private Object QuocGia;
}
