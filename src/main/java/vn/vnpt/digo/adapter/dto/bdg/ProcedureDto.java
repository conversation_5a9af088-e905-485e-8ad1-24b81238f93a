package vn.vnpt.digo.adapter.dto.bdg;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.pojo.bdg.ProcedureExtend;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcedureDto implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String code;

    private List<Agency> agency;

    private ProcedureExtend extendBDG;

    private NameDto connectType;

    private NameDto integratedType;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Agency implements Serializable {
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        private String name;

        private String code;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NameDto {
        private String id;
        private String name;
    }
}
