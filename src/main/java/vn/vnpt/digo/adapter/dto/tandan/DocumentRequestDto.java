package vn.vnpt.digo.adapter.dto.tandan;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.CheckConfigParams;
import vn.vnpt.digo.adapter.util.ParamName;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigParams
public class DocumentRequestDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId configId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subsystemId;

    @JsonProperty("IDHoSo")
    private String IDHoSo;

    @JsonProperty("MaHoSo")
    private String MaHoSo;

    @JsonProperty("NguoiKy")
    private NguoiDto NguoiKy;

    @JsonProperty("NguoiTrinhKy")
    private NguoiDto NguoiTrinhKy;

    @JsonProperty("DonVi")
    private DonViNhanDto DonVi;

    @JsonProperty("VanBanDuThao")
    private VanBanDuThaoDto VanBanDuThao;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class NguoiDto{
        @JsonProperty("Ten")
        private String Ten;

        @JsonProperty("TaiKhoan")
        private String TaiKhoan;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class DonViNhanDto{
        @JsonProperty("Ten")
        private String Ten;

        @JsonProperty("MaDinhDanh")
        private String MaDinhDanh;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class VanBanDuThaoDto{
        @JsonProperty("TrichYeu")
        private String TrichYeu;

        @JsonProperty("DinhKem")
        private List<DinhKemItemDto> DinhKem;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class DinhKemItemDto{
        @JsonProperty("Ten")
        private String Ten;

        @JsonProperty("Base64")
        private String Base64;
    }
}
