package vn.vnpt.digo.adapter.dto.industry_trade_bd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierFileDto implements Serializable {

    private ProcedureForm procedureForm;

    private ArrayList<File> file;

    private Byte quantity;

    private FileType type;

    private String sourceFile;

    private String filename;


    public String getProcedureForm(){
        return this.procedureForm.getName();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class File implements Serializable {

        private String id;
        private String filename;
        private Integer size;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileType implements Serializable {
        private String id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcedureForm implements Serializable {
        private String id;
        private String name;
    }
}
