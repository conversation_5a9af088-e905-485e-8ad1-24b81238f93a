package vn.vnpt.digo.adapter.dto.dbn.ldtbxh;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "ExtentData")
@XmlAccessorType(XmlAccessType.FIELD)
public class ExtentData implements Serializable {

    @XmlElement(name = "DanhsachTaiLieuNop")
    private ExtentData.DanhsachTaiLieuNop danhsachTaiLieuNop;

    @XmlElement(name = "DanhSachGiayToKetQua")
    private ExtentData.DanhSachGiayToKetQua danhSachGiayToKetQua;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class DanhsachTaiLieuNop {
        @XmlElement(name = "TaiLieuNop")
        private ArrayList<ExtentData.TaiLieuNop> taiLieuNop;
        
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class DanhSachGiayToKetQua {
        @XmlElement(name = "GiayToKetQua")
        private ArrayList<ExtentData.GiayToKetQua> giayToKetQuas;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class TaiLieuNop {
        @XmlElement(name = "TepDinhKemId")
        private String tepDinhKemId;

        @XmlElement(name = "TenTepDinhKem")
        private String tenTepDinhKem;

        @XmlElement(name = "IsDeleted")
        private int isDeleted;

        @XmlElement(name = "DuongDanTaiTepTin")
        private String duongDanTaiTepTin;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class GiayToKetQua {
        @XmlElement(name = "TenGiayTo")
        private String tenGiayTo;

        @XmlElement(name = "MaGiayToKetQua")
        private String maGiayToKetQua;

        @XmlElement(name = "GiayToId")
        private String giayToId;

        @XmlElement(name = "DuongDanTepTinKetQua")
        private String duongDanTepTinKetQua;
    }
}
