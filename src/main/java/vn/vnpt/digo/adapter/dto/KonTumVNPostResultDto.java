package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KonTumVNPostResultDto implements Serializable {
    @JsonProperty("Status")
    private String Status;

    @JsonProperty("Message")
    private String Message;
}
