/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LGSPHCMDossierStatusSyncReqDto implements Serializable {
    private String session;
    private String madonvi;
    private String service = "CapNhatTienDoHoSoMC";
    private List<DossierData> data;

    public LGSPHCMDossierStatusSyncReqDto(String madonvi, List<DossierData> data, String isUpdate){
        this.madonvi = madonvi;
        this.data = data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierData implements Serializable {

        @JsonProperty("MaHoSo")
        private String code;

        @JsonProperty("NguoiXuLy")
        private String officer;
        @JsonProperty("ChucDanh")
        private String position;
        @JsonProperty("PhongBanXuLy")
        private String agency;

        @JsonProperty("ThoiDiemXuLy")
        private String updatedDate;
        @JsonProperty("NoiDungXuLy")
        private String content;
        @JsonProperty("TrangThai")
        private String status;

        @JsonProperty("NgayBatDau")
        private String startDate;
        @JsonProperty("NgayKetThucTheoQuyDinh")
        private String endDate;

        public DossierData(LGSPHCMDossierDto dossier, LGSPHCMDossierStatusDto statusInfo, Integer status) {
            Logger logger = LoggerFactory.getLogger(LGSPHCMDossierSyncReqDto.class);
            try {
                this.code = dossier.getCode();
                this.officer = statusInfo.getAssignee();
                if(Objects.isNull(statusInfo.getAssignee()) || Objects.equals(statusInfo.getAssignee(), "")){
                    this.officer = statusInfo.getAgency().getName();
                }
                this.position = statusInfo.getPosition();
                if (statusInfo.getOriginAgency() != null && !statusInfo.getOriginAgency().isEmpty()) {
                    this.agency = statusInfo.getOriginAgency();
                }
                else {
                    this.agency = !Objects.isNull(statusInfo.getAgency().getName()) && !("").equals(statusInfo.getAgency().getName()) ? statusInfo.getAgency().getName() : statusInfo.getAgency().getParent().getName();
                }
                SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
                if(Objects.nonNull(statusInfo.getUpdatedDate())){
                    this.updatedDate = df.format(statusInfo.getUpdatedDate());
                } else {
                    this.updatedDate = "";
                }
                this.content = statusInfo.getContent();
                this.status = status.toString();

                if(Objects.nonNull(statusInfo.getStartDate())){
                    this.startDate = df.format(statusInfo.getStartDate());
                } else {
                    this.startDate = "";
                }
                if(Objects.nonNull(statusInfo.getEndDate())){
                    this.endDate = df.format(statusInfo.getEndDate());
                } else {
                    this.endDate = "";
                }
            } catch (Exception e) {
                logger.debug("Constructor: Create dossier sync data failed: " + e.getMessage());
            }
        }
    }
}
