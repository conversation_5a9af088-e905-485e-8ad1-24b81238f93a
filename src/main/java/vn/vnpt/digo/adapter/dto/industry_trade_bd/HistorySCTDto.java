package vn.vnpt.digo.adapter.dto.industry_trade_bd;

import com.fasterxml.jackson.annotation.JsonRawValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HistorySCTDto implements Serializable {
    private int groupId = 1;
    private int type = 3;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId itemId;

    private UserInfo user;

    private ArrayList<Action> action;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Action implements Serializable {
        private String fieldNameRbk;

        private String newValue;

        private String originalValue;


    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileInfo implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String type = "file";
        private String name;

    }
}
