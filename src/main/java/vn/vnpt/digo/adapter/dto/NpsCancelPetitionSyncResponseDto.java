/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsCancelPetitionSyncResponseDto implements Serializable {

    private boolean success = false;

    private String message = "";

    @JsonSetter("DongBoKetQuaTuChoiPAKN")
    public void mapperPetitionCode(List<NpsSynchronizePetitionResponseMessageDto> messages) {
        if (Objects.nonNull(messages)) {
            NpsSynchronizePetitionResponseMessageDto mess = messages.get(0);
            if (mess.getResultCode().equals("0")) {
                success = true;
            }
            this.message = mess.getMessage();
        }
    }
}
