package vn.vnpt.digo.adapter.dto.nps;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.PersistenceConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.IdName;
import vn.vnpt.digo.adapter.dto.IdNameCodeStringDto;

import vn.vnpt.digo.adapter.dto.IdNameDto;
import vn.vnpt.digo.adapter.dto.PadPApplyDto.IdNameCodeDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.pojo.Id;
import vn.vnpt.digo.adapter.pojo.ProcedureForm;
import vn.vnpt.digo.adapter.pojo.Translate;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NpsDossierResyncDetailDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String code;
    private String nationCode;
    private IdNameCodeStringDto procedure;
    private Id procedureProcessDefinition;
    private ApplyMethodDto applyMethod;
    private ApplicantDto applicant;
    private AgencyDto agency;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date acceptedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date dueDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date waitUpdateDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date waitDutyPayment;
    private IdCodeNameDto dossierTaskStatus;
    private DossierStatus dossierStatus;

    @Field("extendHCM")
    @JsonProperty("extendHCM")
    private DossierExtend extendHCM;
    private IdCodeNameDto dossierReceivingKind;
    private List<FormFile> dossierFormFile;
    private List<AttachmentDto> attachment;
    private ArrayList<AttachmentStorageDto> attachmentStorages;
    private ArrayList<AttachmentStorageDto> componentsStorages;
    private Integer undefindedCompleteTime;
    private List<TaskDto> task;
    private int isWithdraw;
    private List<UserWithdrawDto> userWithdraw;
    private List<UseRefuseDto> userRefuse;
    private IdName paymentMethod;

    private Boolean defaultPaymentLocal;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AgencyDto {
        
        @Field("id")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private ArrayList<Translate> name;
        private String code;
        private AgencyDto parent;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Reused {
          @JsonSerialize(using = ToStringSerializer.class)
          private int value;
          private Object form;
    }
    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public class DossierExtend implements Serializable {
        @Field("reused")
        @JsonProperty("reused")
        private Reused reused;

        @PersistenceConstructor
        public DossierExtend(Reused reused) {
            this.reused = reused;
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplyMethodDto {
        @Field("id")
        private Integer id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplicantDto {
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId userId;
        private EformData data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EformData{
        private String fullname;
        private String identityNumber;
//        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private String identityDate;
//        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private String birthday;
        private String phoneNumber = "";
        private String email = "";
        private String fax = "";
        private String ownerFullname;
        private String address = "";
        private boolean checkCitizen = false;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FormFile{
        private List<AttachmentDto> file;
        private ProcedureForm procedureForm;
        private Requirement requirement;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AttachmentDto {

        private String filename;
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId group;
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private Long size;
        private Integer isNational;
        private Integer reused;
        private String repCode;
        private UUID uuid;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AttachmentStorageDto {

        private String code;
        private String filename;
        private String fileURL;
        
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DossierStatus {
        @Field("id")
        private Integer id;
        private ArrayList<Translate> name;
        private String comment;
        private String description;
        
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskDto {
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date createdDate;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date completedDate;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date confirmFFODate;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date dueDate;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Requirement {

        private Integer quantity;
        private String typeId;
        private String typeName;
        
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserWithdrawDto {
        private String comment;
        private List<AttachmentDto> attachment;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UseRefuseDto {
        private String comment;
    }
}
