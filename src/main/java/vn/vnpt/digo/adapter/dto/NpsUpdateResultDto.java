/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.File;
import vn.vnpt.digo.adapter.pojo.NpsSync;
import vn.vnpt.digo.adapter.pojo.Result;
import vn.vnpt.digo.adapter.pojo.Tag;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsUpdateResultDto implements Serializable {

    private Tag tag;
    private Result result;
    private List<File> file;
    private NpsSync sync;
}
