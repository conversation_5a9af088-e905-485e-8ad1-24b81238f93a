package vn.vnpt.digo.adapter.dto.tandan.civil;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CivilDossierStatusResDto implements Serializable {
    private InfoStatus loadResultStatusResponse;
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InfoStatus implements Serializable {
        String status;
        String statusDescription;
        String errorCode;
        String errorDescription;
        String dossierCode;
        public InfoStatus(InfoStatus infoStatus, String code){
            this.status = infoStatus.status;
            this.statusDescription = infoStatus.statusDescription;
            this.errorCode = infoStatus.errorCode;
            this.errorDescription = infoStatus.errorDescription;
            this.dossierCode = code;
        }
    }
}
