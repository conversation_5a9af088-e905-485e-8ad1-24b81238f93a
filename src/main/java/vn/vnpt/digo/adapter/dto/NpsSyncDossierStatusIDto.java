package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.util.CheckConfigBody;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigBody
public class NpsSyncDossierStatusIDto implements Serializable {

    private String configId;
    private String agencyId;
    private String subsystemId;
    private String agencyCode;
    @NonNull
    private String code;
    @NonNull
    private String assignee;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @NonNull
    private Date date;
    private String comment;
    @NonNull
    private String status;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date startDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date endDate;

    public NpsSyncDossierStatusIDto(KafkaNpsSyncDossierDto dossierStatus) {
        configId = dossierStatus.getConfigId();
        agencyId = dossierStatus.getAgencyId();
        subsystemId = dossierStatus.getSubsystemId();
        agencyCode = dossierStatus.getAgencyCode();
        code = dossierStatus.getCode();
        assignee = dossierStatus.getAssignee();
        date = dossierStatus.getDate();
        comment = dossierStatus.getComment();
        status = dossierStatus.getStatus();
        startDate = dossierStatus.getStartDate();
        endDate = dossierStatus.getEndDate();
    }
}
