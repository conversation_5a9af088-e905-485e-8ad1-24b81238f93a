package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsDossierComponentDto implements Serializable {

    private String situation;

    private List<NpsDocumentDto> documents;

    @JsonSetter("TRUONGHOP")
    public void mapSituation(String situation) {
        this.situation = situation;
    }

    @JsonSetter("GIAYTO")
    public void mapDocuments(List<NpsDocumentDto> documents) {
        this.documents = documents;
    }
}
