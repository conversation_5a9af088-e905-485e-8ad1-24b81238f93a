/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import vn.vnpt.digo.adapter.util.ParamName;
/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DkdnParamsDto implements Serializable {
    private String msdn;
    private String mst;
    private String from_ts;
//    @ParamName("to_ts")
    private String to_ts;
    private String offset;
    private String limit;
    
//    @ParamName("from_date")
    @NotNull
    private String from_date;
//    @ParamName("to_date")
    @NotNull
    private String to_date;
    
//    @ParamName("in_journal_no")
    private String in_journal_no;


}