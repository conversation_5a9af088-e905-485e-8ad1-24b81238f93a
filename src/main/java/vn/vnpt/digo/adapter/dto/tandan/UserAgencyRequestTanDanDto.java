package vn.vnpt.digo.adapter.dto.tandan;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.CheckConfigParams;
import vn.vnpt.digo.adapter.util.ParamName;

@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigParams
public class UserAgencyRequestTanDanDto implements Serializable {
    @ParamName("config-id")
    private ObjectId configId;

    @ParamName("agency-id")
    private ObjectId agencyId;

    @ParamName("subsystem-id")
    private ObjectId subsystemId;

    @ParamName("code")
    private String code;

    @ParamName("keyword")
    private String keyword;
}
