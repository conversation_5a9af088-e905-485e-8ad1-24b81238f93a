/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SsoHueResDto implements Serializable {

    @JsonProperty("Success")
    private Boolean success;

    @JsonProperty("Message")
    private String message;

    @JsonProperty("ErrCode")
    private String errCode;

    @JsonProperty("Data")
    private AccountData data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AccountData {

        @JsonProperty("ID")
        private String id;
        @JsonProperty("TenTaiKhoan")
        private String tenTaiKhoan;
        @JsonProperty("CaNhan")
        private UserData CaNhan;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserData {

        @JsonProperty("HoVaTen")
        private String hoVaTen;
        @JsonProperty("MaSoCaNhan")
        private String maSoCaNhan;
        @JsonProperty("DienThoai")
        private String DienThoai;

    }
}
