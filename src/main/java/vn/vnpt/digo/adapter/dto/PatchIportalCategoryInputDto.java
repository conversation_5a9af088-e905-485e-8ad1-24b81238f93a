package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatchIportalCategoryInputDto implements Serializable {

    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;
    @Size(min = 1, max = 100, message = "max size category require not greater than 100")
    private List<CategoryInputDto> category;
}
