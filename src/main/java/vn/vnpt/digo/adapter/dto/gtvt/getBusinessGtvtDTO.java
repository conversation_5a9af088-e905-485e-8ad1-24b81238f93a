package vn.vnpt.digo.adapter.dto.gtvt;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.json.JSONObject;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class getBusinessGtvtDTO {

    private String id;

    private String name;

    public static  getBusinessGtvtDTO mapJsonToGetBusinessGtvtDTO(JSONObject businessObject) {
        getBusinessGtvtDTO dto = new getBusinessGtvtDTO();

        if(businessObject.has("MaDoanhNghiep")){
            dto.setId(businessObject.getString("MaDoanhNghiep"));
        }

        if(businessObject.has("TenDoanhNghiep")){
            dto.setName(businessObject.getString("TenDoanhNghiep"));
        }

        return dto;
    }
}