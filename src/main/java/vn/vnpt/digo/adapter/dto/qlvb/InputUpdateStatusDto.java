package vn.vnpt.digo.adapter.dto.qlvb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class InputUpdateStatusDto implements Serializable {
    private Integer messageId;
    private Integer updateStatus;
    private String detailStatus;
}
