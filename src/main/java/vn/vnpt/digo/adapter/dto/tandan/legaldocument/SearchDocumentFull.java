package vn.vnpt.digo.adapter.dto.tandan.legaldocument;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchDocumentFull implements Serializable {
    private ObjectId configId;
    private ObjectId agencyId;
    private ObjectId subsystemId;
    private ArrayList<Integer> donVi;
    private int kieuTimKiem;
    private  String tuNgay;
    private  String denNgay;
    private ArrayList<Integer> loaiVanBan;
    private String keyword;
    private ArrayList<String> searchIn;
    private String t_gridRequest;
}
