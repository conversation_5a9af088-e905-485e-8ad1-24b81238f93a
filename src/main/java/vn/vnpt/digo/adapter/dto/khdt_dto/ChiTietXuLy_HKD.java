package vn.vnpt.digo.adapter.dto.khdt_dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChiTietXuLy_HKD {
    private String MaNoiBo;
    private String MaSoHKD;
    private String MaSoDangKyHKD;
    private String FOUNDING_DATE;
    private String MaLoaiHinhHKD;
    private String LoaiHinhHKD;
    private String TenTiengViet;
    private String TenVietTat;
    private String TenNuocNgoai;
    private String NgayDangKyLanDau;
    private String NgayDangKyThayDoi;
    private int SoLanDangKyThayDoi;
    private List<ThongTinCaNhanDTO> ChuHoKinhDoanh;
    private List<DiaChiDTO> DiaChiTruSo;
    private List<NganhNgheKinhDoanhDTO> NganhNgheKinhDoanh;

}
