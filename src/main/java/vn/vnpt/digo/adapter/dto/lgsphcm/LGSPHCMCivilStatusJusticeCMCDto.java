/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR> <PERSON>am
 */
@Data
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class LGSPHCMCivilStatusJusticeCMCDto implements Serializable {
    private long id;
    private String so;
    private String quyenSo;
    private String trangSo;
    private String quyetDinhSo;
    private String ngayDangKy;
    private long loaiDangKy;
    private long loaiXacNhan;
    private String noiDangKy;
    private String nguoiKy;
    private String chucVuNguoiKy;
    private String nguoiThucHien;
    private String ghiChu;
    private String cmHoTen;
    private String cmNgaySinh;
    private String cmDanToc;
    private String cmDanTocKhac;
    private String cmQuocTich;
    private List<String> cmQuocTichKhac;
    private String cmQueQuan;
    private long cmLoaiCuTru;
    private String cmNoiCuTru;
    private long cmLoaiGiayToTuyThan;
    private String cmGiayToKhac;
    private String cmSoGiayToTuyThan;
    private String cmNgayCapGiayToTuyThan;
    private String cmNoiCapGiayToTuyThan;
    private String ncHoTen;
    private String ncNgaySinh;
    private long ncDanToc;
    private String ncDanTocKhac;
    private String ncQuocTich;
    private List<String> ncQuocTichKhac;
    private String ncQueQuan;
    private long ncLoaiCuTru;
    private String ncNoiCuTru;
    private long ncLoaiGiayToTuyThan;
    private String ncGiayToKhac;
    private String ncSoGiayToTuyThan;
    private String ncNgayCapGiayToTuyThan;
    private String ncNoiCapGiayToTuyThan;
    private String nycHoTen;
    private String nycQHNguoiDuocNhan;
    private String nycQHNguoiNhan;
    private long nycLoaiGiayToTuyThan;
    private String nycGiayToKhac;
    private String nycSoGiayToTuyThan;
    private String nycNgayCapGiayToTuyThan;
    private String nycNoiCapGiayToTuyThan;
    private String soDangKyNuocNgoai;
    private String ngayDangKyNuocNgoai;
    private String cqNuocNgoaiDaDangKy;
    private String qgNuocNgoaiDaDangKy;
}
