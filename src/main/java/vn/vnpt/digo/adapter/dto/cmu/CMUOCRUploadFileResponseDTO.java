package vn.vnpt.digo.adapter.dto.cmu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CMUOCRUploadFileResponseDTO {
    @JsonProperty("fileName")
    private String fileName;

    @JsonProperty("tokenId")
    private String tokenId;

    @JsonProperty("description")
    private String description;

    @JsonProperty("storageType")
    private String storageType;

    @JsonProperty("id")
    private String id;

    @JsonProperty("title")
    private String title;

    @JsonProperty("uploadedDate")
    private String uploadedDate;

    @JsonProperty("hash")
    private String hash;

    @JsonProperty("fileType")
    private String fileType;
}
