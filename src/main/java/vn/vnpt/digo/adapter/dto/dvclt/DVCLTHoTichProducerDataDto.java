package vn.vnpt.digo.adapter.dto.dvclt;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DVCLTHoTichProducerDataDto implements Serializable {

    @NotEmpty(message = "{lang.word.tag} 'securityKey' {lang.phrase.may-can-not-empty}")
    private String securityKey;
    
    private DossierInfo data;
    
    private String logId;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DossierInfo implements Serializable {
        
        @JsonProperty("maDonVi")
        @NotEmpty(message = "'maDonVi'")
        private String agencyCode;

        @JsonProperty("maHoSo")
        @NotEmpty(message = "'maHoSo'")
        private String code;

        @JsonProperty("maHoSoLienThong")
        @NotEmpty(message = "'maHoSoLienThong'")
        private String nationCode;

        @JsonProperty("maTTHC")
        @NotEmpty(message = "'maTTHC'")
        private String procedureCode;

        private ProcedureDto procedure;

        @JsonProperty("module")
        @NotEmpty(message = "'module'")
        private String module;

        @JsonProperty("ngayTiepNhan")
        @NotEmpty(message = "'ngayTiepNhan'")
        @Size(min = 14, max = 14, message = "'ngayTiepNhan'")
        private String acceptedDate;

        @JsonProperty("techId")
        @NotEmpty(message = "'techId'")
        private String techId;

        @JsonProperty("data")
        @NotEmpty(message = "'data'")
        private HoTichEform data;

        @JsonProperty("fileDinhKem")
        private List<DVCLTHoTichRequestDto.FileAttachment> attachment;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HoTich implements Serializable {
        @JsonProperty("hoTich")
        private HoTichEform hoTich;
    }

    // @Data
    // @NoArgsConstructor
    // @AllArgsConstructor
    // @JsonIgnoreProperties(ignoreUnknown = true)
    // public static class HoTichWarper implements Serializable {
    //     @JsonProperty("hoTich")
    //     private HoTichEform hoTich;
    // }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HoTichEform implements Serializable {

        //KS
        private String maGCS;
        private String ngayCapGCS;
        private Object nksHoTen;
        private Long nksGioiTinh;
        private Object nksNgaySinh;
        private String nksNgaySinhBangChu;
        private Long nksNoiSinhNuocNgoai;
        private Object nksNoiSinh;
        private Object nksQueQuan;
        private Long nksDanToc;
        private String nksDanTocKhac;
        private String nksQuocTich;
        private Long nksLoaiKhaiSinh;
        private Object meHoTen;
        private Object meNgaySinh;
        private Long meDanToc;
        private String meDanTocKhac;
        private String meQuocTich;
        private Long meLoaiCuTru;
        private Object meNoiCuTru;
        private Long meLoaiGiayToTuyThan;
        private String meSoGiayToTuyThan;
        private String meSoDDCN;
        private Long meXacThucThongTin;
        private Object chaHoTen;
        private Object chaNgaySinh;
        private Long chaDanToc;
        private String chaDanTocKhac;
        private String chaQuocTich;
        private Long chaLoaiCuTru;
        private Object chaNoiCuTru;
        private Long chaLoaiGiayToTuyThan;
        private String chaSoGiayToTuyThan;
        private String chaSoDDCN;
        private Long chaXacThucThongTin;
        private String giayCnkhSo;
        private String giayCnkhQuyenSo;
        private String giayCnkhNgayCap;
        private String giayCnkhNoiCap;

        //KT
        private String maGBT;
        private String ngayCapGBT;
        private Object nktHoTen;
        private String nktGioiTinh;
        private Object nktNgaySinh;
        private Long nktDanToc;
        private String nktDanTocKhac;
        private String nktQuocTich;
        private Long nktLoaiCuTru;
        private Object nktNoiCuTru;
        private Long nktLoaiGiayToTuyThan;
        private String nktSoGiayToTuyThan;
        private String nktNgayCapGiayToTuyThan;
        private String nktNoiCapGiayToTuyThan;
        private String nktSoDDCN;
        private String nktNgayChet;
        private String nktGioPhutChet;
        private Object nktNoiChet;
        private String nktNguyenNhanChet;
        private Long nktXacThucThongTin;
        private Long gbtLoai;
        private String gbtSo;
        private String gbtNgay;
        private String gbtCoQuanCap;

        //Thong tin chung
        private Long loaiDangKy;
        private String noiDangKy;
        private Long soLuongBanSao;

        private Object nycHoTen;
        private String nycQuanHe;
        private Long nycLoaiGiayToTuyThan;
        private String nycSoGiayToTuyThan;
        private String nycNgayCapGiayToTuyThan;
        private String nycNoiCapGiayToTuyThan;
        private String nycSoDDCN;
        private String nycSoDienThoai;
        private String nycEmail;
        private Long nycLoaiCuTru;
        private Object nycNoiCuTru;
        private Long nycXacThucThongTin;
        
        // ho tich cu
        private String nksNoiSinhDVHC;
        private String nksQuocTichKhac;
        private String meQuocTichKhac;
        private String chaQuocTichKhac;
        private String nktQuocTichKhac;
        private String nycGiayToKhac;
    }
}
