/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.util.CheckConfigParams;
import vn.vnpt.digo.adapter.util.ParamName;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "integratedLogs")
public class LGSPHCMLogSyncNpsListDto {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
       
    @JsonProperty("service")
    private IntegratedService service;
    
    @JsonProperty("config")
    private IntegratedConfig config;
    
    @JsonProperty("item")
    private DossierInformation item;
    
    @JsonProperty("status")
    private Integer status;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("data")
    private String data;
    
    private String function;
    
    private String result;
    
    private String procedureName;
    
    @JsonProperty("createdDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;
    
    @JsonProperty("updatedDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IntegratedService implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        private String name;

    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IntegratedConfig implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        private String name;

    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DossierInformation implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        
        private String code;
        
        private String name;

    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @CheckConfigParams
    public class DVCQGSyncResponseDto implements Serializable {

        @ParamName("error_code")
        public String error_code;
        @ParamName("message")
        public String message;

    }
}
