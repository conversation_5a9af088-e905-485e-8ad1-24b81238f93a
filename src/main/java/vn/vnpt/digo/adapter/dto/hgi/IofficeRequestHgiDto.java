package vn.vnpt.digo.adapter.dto.hgi;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class IofficeRequestHgiDto implements Serializable  {
    private String soKyHieu;
    private String trichYeu;
    private String tuNgay;
    private String denNgay;
    private int page;
    private int size;
    private String userAccount;
}