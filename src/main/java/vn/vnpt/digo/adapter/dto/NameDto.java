package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NameDto implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String name;
}
