package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdNameDto implements Serializable{

    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    ObjectId id;
    
    private String name;
    
    private long size;

    public IdNameDto(ObjectId id, String name){
            this.id = id;
            this.name = name;
    }
}
