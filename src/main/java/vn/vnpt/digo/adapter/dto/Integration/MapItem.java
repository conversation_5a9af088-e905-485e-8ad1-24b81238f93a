package vn.vnpt.digo.adapter.dto.Integration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * from.code: mã giấy tờ phía iGate
 * from.name: tên giấy tờ phía igate
 * to.code: mã giấy tờ phía chuyên ngành
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MapItem implements Serializable {
    private CodeName from;
    private String toCode;
}
