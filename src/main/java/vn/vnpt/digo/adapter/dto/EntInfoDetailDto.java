/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntInfoDetailDto implements Serializable {
    private String MaNoiBo;
    private String MaSoDoanhNghiep;
    private String SoGCNDKKD;
    private String MaLoaiHinhDN;
    private String LoaiHinhDN;
    private String TenTiengViet;
    private String TenNuocNgoai;
    private String TenVietTat;
    private String NgayDangKyLanDau;
    private String NgayDangKyThayDoi;
    private String SoLanDangKyThayDoi;
    private ArrayList<Map<String, Object>> TinhTrangPhapLy;
    private ArrayList<Map<String, Object>> DaiDienPhapLuat;
    private ArrayList<Map<String, Object>> DiaChiTruSo;
    private ArrayList<Map<String, Object>> NganhNgheKinhDoanh;
}
