package vn.vnpt.digo.adapter.dto.khcn;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
@JsonIgnoreProperties(ignoreUnknown = true)
public class KHCNPostDto implements Serializable {
    
    private String maTTHC;
    private String soHoSoLT;
    private Integer coQuanXuLy = 6; // Mã cơ quan xử lý cho KHCN
    private String maHoSo;
    private Integer trangThai;
    private String thoiGianThucHien;
    private String ghiChu;
    private String ketQuaXuLy;
    private String nguoiXuLy;
    private String chucDanh;
    private String phongBanXuLy;
    private String noiDungXuLy;
    private String ngayBatDau;
    private String ngayKetThucTheoQuyDinh;
    private String ngayHenTraTruoc;
    private String ngayHenTraMoi;
    private String hanBoSungHoSo;
    private String lyDoTuChoi;
    private String yeuCauBoSung;
}
