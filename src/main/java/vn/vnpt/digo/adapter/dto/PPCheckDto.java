/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PPCheckDto implements Serializable {

    @JsonProperty("MaDoiTac")
    private String MaDoiTac;
    @JsonProperty("ThoiGianGD")
    private String ThoiGianGD;
    @JsonProperty("MaXacThuc")
    private String MaXacThuc;
}
