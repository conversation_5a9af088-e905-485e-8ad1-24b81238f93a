/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LGSPHCMAsyncReceiveDto implements Serializable {
    public static final Integer SYNC_DOSSIER = 1;
    public static final Integer SYNC_STATUS = 2;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId configId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subsystemId;

    private Integer type; //1: sync dossier, 2: sync status
    
    private LGSPHCMDossierDto dossier;
    private LGSPHCMDossierStatusDto status;
}
