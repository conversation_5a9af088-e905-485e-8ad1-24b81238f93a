package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsQuestionResultDto implements Serializable {

    private Integer hoiDapID;
    private String maTTHCDP;
    private String hoVaTen;
    private String diaChi;
    private String thuDienTu;
    private String soDienThoai;
    private String tieuDe;
    private String noiDung;
    private String maDonVi;
    private String tenDonVi;
    private Integer trangThai;
    private List<Object> tepDinhKem;

    @JsonSetter("HOIDAPID")
    public void mapHoiDapID(Integer hoiDapID) {
        this.hoiDapID = hoiDapID;
    }

    @JsonSetter("MATTHCDP")
    public void mapMaTTHCDP(String maTTHCDP) {
        this.maTTHCDP = maTTHCDP;
    }

    @JsonSetter("HOVATEN")
    public void mapHoVaTen(String hoVaTen) {
        this.hoVaTen = hoVaTen;
    }

    @JsonSetter("DIACHI")
    public void mapDiaChi(String diaChi) {
        this.diaChi = diaChi;
    }

    @JsonSetter("THUDIENTU")
    public void mapThuDienTu(String thuDienTu) {
        this.thuDienTu = thuDienTu;
    }

    @JsonSetter("SODIENTHOAI")
    public void mapSoDienThoai(String soDienThoai) {
        this.soDienThoai = soDienThoai;
    }

    @JsonSetter("TIEUDE")
    public void mapTieuDe(String tieuDe) {
        this.tieuDe = tieuDe;
    }

    @JsonSetter("NOIDUNG")
    public void mapNoiDung(String noiDung) {
        this.noiDung = noiDung;
    }

    @JsonSetter("MADONVI")
    public void mapMaDonVi(String maDonVi) {
        this.maDonVi = maDonVi;
    }

    @JsonSetter("TENDONVI")
    public void mapTenDonVi(String tenDonVi) {
        this.tenDonVi = tenDonVi;
    }

    @JsonSetter("TRANGTHAI")
    public void mapTrangThai(Integer trangThai) {
        this.trangThai = trangThai;
    }

    @JsonSetter("TEPDINHKEM")
    public void mapTepDinhKem(List<Object> tepDinhKem) {
        this.tepDinhKem = tepDinhKem;
    }
}
