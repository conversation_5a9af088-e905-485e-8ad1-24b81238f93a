package vn.vnpt.digo.adapter.dto.bdg.moc;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MocConstructionInfoDto {

    @JsonProperty("DiaDiemXayDung")
    private String location;

    @JsonProperty("LoDatSo")
    private String serialLand;

    @JsonProperty("DienTich")
    private String acreage;

    @JsonProperty("MaTinhThanh")
    private String provinceCode;

    @JsonProperty("MaQuanHuyen")
    private String districtCode;

    @JsonProperty("MaPhuongXa")
    private String wardCode;

    @JsonProperty("SoNhaDuongPho")
    private String street;

}
