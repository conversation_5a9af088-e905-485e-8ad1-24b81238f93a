package vn.vnpt.digo.adapter.dto.qti;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgencyDto implements Serializable {

    private ObjectId id;

    private String code;

    private ArrayList<Name> name;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Name implements Serializable {

        private Short languageId;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Tag implements Serializable {

        @Field("id")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        private List<Name> trans;
    }
}
