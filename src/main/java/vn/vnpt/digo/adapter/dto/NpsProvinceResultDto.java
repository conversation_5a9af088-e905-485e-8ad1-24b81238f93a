package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsProvinceResultDto implements Serializable {

    private String id;
    private String name;

    @JsonSetter("MATINHTHANH")
    public void mapperId(String maTinhThanh) {
        this.id = maTinhThanh;
    }

    @JsonSetter("TENTINHTHANH")
    public void mapperName(String tenTinhThanh) {
        this.name = tenTinhThanh;
    }
}
