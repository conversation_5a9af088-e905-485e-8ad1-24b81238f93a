/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import vn.vnpt.digo.adapter.util.CheckConfigBody;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigBody
public class PPInitReqDto implements Serializable {
    
    public PPInitReqDto(PPInitDto req, IntegratedConfigurationDto config){
        this.loaiBanTin = req.getLoaiBanTin();
        this.phienBan = req.getPhienBan();
        this.maDoiTac = req.getMaDoiTac();
        this.maThamChieu = req.getMaThamChieu();
        this.soTien = req.getSoTien();
        this.loaiHinhThanhToan = req.getLoaiHinhThanhToan();
        this.maKenhThanhToan = req.getMaKenhThanhToan();
        this.maThietBi = req.getMaThietBi();
        this.ngonNgu = req.getNgonNgu();
        this.maTienTe = req.getMaTienTe();
        this.maNganHang = req.getMaNganHang();
        this.thongTinGiaoDich = req.getThongTinGiaoDich();
        this.thoiGianGD = req.getThoiGianGD();
        this.ip = req.getIp();
        this.thongTinBienLai = new PPInitReceiptDto(req.getThongTinBienLai());
        String plainText = req.getLoaiBanTin() + "|" + req.getPhienBan() + "|"
                + req.getMaDoiTac() + "|" + req.getMaThamChieu() + "|"
                + req.getSoTien() + "|" + req.getLoaiHinhThanhToan() + "|"
                + req.getMaKenhThanhToan() + "|" + req.getMaThietBi() + "|"
                + req.getNgonNgu() + "|" + req.getMaTienTe() + "|"
                + req.getMaNganHang() + "|" + req.getThongTinGiaoDich() + "|"
                + req.getThoiGianGD() + "|" + req.getIp() + "|" + config.getParametersValue("secret-code");
        this.maXacThuc = DigestUtils.sha256Hex(plainText);
    }
    
    @JsonProperty("LoaiBanTin")
    private String loaiBanTin;
    @JsonProperty("PhienBan")
    private String phienBan;
    @JsonProperty("MaDoiTac")
    private String maDoiTac;
    @NotNull
    @JsonProperty("MaThamChieu")
    private String maThamChieu;
    @NotNull
    @JsonProperty("SoTien")
    private String soTien;
    @NotNull
    @JsonProperty("LoaiHinhThanhToan")
    private String loaiHinhThanhToan;
    @NotNull
    @JsonProperty("MaKenhThanhToan")
    private String maKenhThanhToan;
    @NotNull
    @JsonProperty("MaThietBi")
    private String maThietBi;
    @NotNull
    @JsonProperty("NgonNgu")
    private String ngonNgu;
    @NotNull
    @JsonProperty("MaTienTe")
    private String maTienTe;
    @NotNull
    @JsonProperty("MaNganHang")
    private String maNganHang;
    @NotNull
    @JsonProperty("ThongTinGiaoDich")
    private String thongTinGiaoDich;
    @NotNull
    @JsonProperty("ThoiGianGD")
    private String thoiGianGD;
    @NotNull
    @JsonProperty("Ip")
    private String ip;
    @NotNull
    @JsonProperty("ThongTinBienLai")
    private PPInitReceiptDto thongTinBienLai;
    @JsonProperty("MaXacThuc")
    private String maXacThuc;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PPInitReceiptDto implements Serializable {

        public PPInitReceiptDto(PPInitDto.PPInitReceiptDto receipt){
            this.maDichVu = receipt.getMaDichVu();
            this.tkThuHuong = receipt.getTkThuHuong();
            this.maNHThuHuong = receipt.getMaNHThuHuong();
            this.tenTKThuHuong = receipt.getTenTKThuHuong();
            List<PPInitReceiptFeeDto> lstFee = new ArrayList<>();
            for (PPInitDto.PPInitReceiptFeeDto item : receipt.getPhiLePhi()){
                lstFee.add(new PPInitReceiptFeeDto(item));
            }
            this.phiLePhi = lstFee;
            this.maDonVi = receipt.getMaDonVi();
            this.tenDonVi = receipt.getTenDonVi();
            this.maHoSo = receipt.getMaHoSo();
            this.maDVC = receipt.getMaDVC();
            this.tenDVC = receipt.getTenDVC();
            this.maTTHC = receipt.getMaTTHC();
            this.tenTTHC = receipt.getTenTTHC();
            this.noiDungThanhToan = receipt.getNoiDungThanhToan();
            this.maLoaiHinhThuPhat = receipt.getMaLoaiHinhThuPhat();
            this.tenLoaiHinhThuPhat = receipt.getTenLoaiHinhThuPhat();
            this.hoTenNguoiNop = receipt.getHoTenNguoiNop();
            this.soCMNDNguoiNop = receipt.getSoCMNDNguoiNop();
            this.diaChiNguoiNop = receipt.getDiaChiNguoiNop();
            this.huyenNguoiNop = receipt.getHuyenNguoiNop();
            this.tinhNguoiNop = receipt.getTinhNguoiNop();
            this.maCoQuanQD = receipt.getMaCoQuanQD();
            this.tenCoQuanQD = receipt.getTenCoQuanQD();
            this.khoBac = receipt.getKhoBac();
            this.ngayQD = receipt.getNgayQD();
            this.soQD = receipt.getSoQD();
            this.thoiGianViPham = receipt.getThoiGianViPham();
            this.diaDiemViPham = receipt.getDiaDiemViPham();
            this.tenNguoiViPham = receipt.getTenNguoiViPham();
            this.taiKhoanThuNSNN = receipt.getTaiKhoanThuNSNN();
            List<PPInitReceiptItemDto> lstItem = new ArrayList<>();
            for (PPInitDto.PPInitReceiptItemDto item2 : receipt.getDsKhoanNop()){
                lstItem.add(new PPInitReceiptItemDto(item2));
            }
            this.dsKhoanNop = lstItem;
        }
        
        @NotNull
        @JsonProperty("MaDichVu")
        private String maDichVu;
        @JsonProperty("TKThuHuong")
        private String tkThuHuong;
        @JsonProperty("MaNHThuHuong")
        private String maNHThuHuong;
        @JsonProperty("TenTKThuHuong")
        private String tenTKThuHuong;
        @JsonProperty("PhiLePhi")
        private List<PPInitReceiptFeeDto> phiLePhi;
        @JsonProperty("MaDonVi")
        private String maDonVi;
        @JsonProperty("TenDonVi")
        private String tenDonVi;
        @JsonProperty("MaHoSo")
        private String maHoSo;
        @JsonProperty("MaDVC")
        private String maDVC;
        @JsonProperty("TenDVC")
        private String tenDVC;
        @JsonProperty("MaTTHC")
        private String maTTHC;
        @JsonProperty("TenTTHC")
        private String tenTTHC;
        @NotNull
        @JsonProperty("NoiDungThanhToan")
        private String noiDungThanhToan;
        @JsonProperty("MaLoaiHinhThuPhat")
        private String maLoaiHinhThuPhat;
        @JsonProperty("TenLoaiHinhThuPhat")
        private String tenLoaiHinhThuPhat;
        @NotNull
        @JsonProperty("HoTenNguoiNop")
        private String hoTenNguoiNop;
        @NotNull
        @JsonProperty("SoCMNDNguoiNop")
        private String soCMNDNguoiNop;
        @NotNull
        @JsonProperty("DiaChiNguoiNop")
        private String diaChiNguoiNop;
        @JsonProperty("HuyenNguoiNop")
        private String huyenNguoiNop;
        @JsonProperty("TinhNguoiNop")
        private String tinhNguoiNop;
        @NotNull
        @JsonProperty("MaCoQuanQD")
        private String maCoQuanQD;
        @NotNull
        @JsonProperty("TenCoQuanQD")
        private String tenCoQuanQD;
        @JsonProperty("KhoBac")
        private String khoBac;
        @JsonProperty("NgayQD")
        private String ngayQD;
        @JsonProperty("SoQD")
        private String soQD;
        @JsonProperty("ThoiGianViPham")
        private String thoiGianViPham;
        @JsonProperty("DiaDiemViPham")
        private String diaDiemViPham;
        @JsonProperty("TenNguoiViPham")
        private String tenNguoiViPham;
        @JsonProperty("TaiKhoanThuNSNN")
        private String taiKhoanThuNSNN;
        @NotNull
        @JsonProperty("DSKhoanNop")
        private List<PPInitReceiptItemDto> dsKhoanNop;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PPInitReceiptItemDto implements Serializable {

        public PPInitReceiptItemDto(PPInitDto.PPInitReceiptItemDto item){
            this.noiDung = item.getNoiDung();
            this.soTien = item.getSoTien();
        }
        
        @NotNull
        @JsonProperty("NoiDung")
        private String noiDung;
        @NotNull
        @JsonProperty("SoTien")
        private String soTien;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PPInitReceiptFeeDto implements Serializable {

        public PPInitReceiptFeeDto(PPInitDto.PPInitReceiptFeeDto fee){
            this.loaiPhiLePhi = fee.getLoaiPhiLePhi();
            this.maPhiLePhi = fee.getMaPhiLePhi();
            this.soTien = fee.getSoTien();
            this.tenPhiLePhi = fee.getTenPhiLePhi();
        }
        
        @NotNull
        @JsonProperty("LoaiPhiLePhi")
        private String loaiPhiLePhi;
        @NotNull
        @JsonProperty("MaPhiLePhi")
        private String maPhiLePhi;
        @NotNull
        @JsonProperty("TenPhiLePhi")
        private String tenPhiLePhi;
        @NotNull
        @JsonProperty("SoTien")
        private String soTien;
    }
}

