/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.vnptsmartca;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SmartCaSignatureDto implements Serializable {
    private ObjectId configId;
    private ObjectId agencyId;
    private ObjectId subsystemId;
    
    private ObjectId personId;
    private final List<SignatureCommentDto> comments = new ArrayList<>();
    @NotNull
    private ObjectId contentFileId;
    @NotNull
    private ObjectId signatureTypeId;
    private String reason;
    private int fontSize;
    private String fontName;
    private String fontStyle;
    private String textColor;
    @NotNull
    private List<SignaturePosition> signaturePositions = new ArrayList<>();
    private String visibleType = "LOGO_ONLY";
    
    private transient byte[] fileContent;
    private transient String fileName;
    private transient String hashAlgorithm = "SHA256";
    private transient String signImage;
    private transient String signerName;
}
