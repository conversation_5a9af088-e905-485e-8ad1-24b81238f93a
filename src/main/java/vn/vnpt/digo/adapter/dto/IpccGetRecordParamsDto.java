/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.CheckConfigParams;
import vn.vnpt.digo.adapter.util.ParamName;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigParams
public class IpccGetRecordParamsDto implements Serializable {
    
    @ParamName("config-id")
    private ObjectId configId;
    
    @ParamName("agency-id")
    private ObjectId agencyId;
    
    @ParamName("subsystem-id")
    private ObjectId subsystemId;
    
    @ParamName("call-id")
    @NotNull
    private Integer callId;
    
    @NotNull
    private String caller;
}
