package vn.vnpt.digo.adapter.dto.Integration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.List;

/**
 *        {
 *            error:{
 *                message: "<PERSON><PERSON> hồ sơ không tồn tại"
 *            },
 *            data: {
 *                status: 203
 *            }
 *            items: []
 *        }
 * httpStatus: trạng thái gói tin trả về http, ví dụ: 200, 302...
 * statusKey: trường trả về trạng thái trong response
 * statusValue: danh sách value của statusKey
 * method: 0/1 (0: lỗi, 1: không lỗi)
 *
 * content: nội dung có chứa content được xem là lỗi
 * errMsgKey: là trường mô tả lỗi, ví dụ: errMsgKey = error.message đối với gói tin sau

 * customErrMsg: mô tả lỗi do dev tự định nghĩa nếu errMsgKey NULL hoặc không lấy được giá trị từ errMsgKey
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErrorDefine {
    @NonNull
    private Integer httpStatus;

    private String statusKey;
    private List<String> statusValue;
    private Integer method;

    private String errMsgKey;
    private List<String> content;
    private String customErrMsg;
}
