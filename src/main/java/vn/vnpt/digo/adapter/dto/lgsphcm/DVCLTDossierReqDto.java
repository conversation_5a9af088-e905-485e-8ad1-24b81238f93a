package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.URL;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DVCLTDossierReqDto implements Serializable {



    @NotEmpty(message = "{lang.word.tag} 'securityKey' {lang.phrase.may-can-not-empty}")
    private String securityKey;
    
    
    @NotEmpty(message = "{lang.word.tag} 'data' {lang.phrase.may-can-not-empty}")
    private List<@Valid DVCLTDossierDataDto> data;

    private String logId;

    public DVCLTDossierReqDto(List<DVCLTDossierDataDto> data) {
        this.securityKey = securityKey;
        this.logId = logId;
        this.data = data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierData implements Serializable {

        @JsonProperty("MaHoSoLienThong")
        @NotEmpty(message = "{lang.word.tag} 'MaHoSoLienThong' {lang.phrase.may-can-not-empty}")
        private String nationCode;

        @JsonProperty("MaHoSo")
        @NotEmpty(message = "{lang.word.tag} 'MaHoSo' {lang.phrase.may-can-not-empty}")
        private String code;

        @JsonProperty("MaTTHC")
        @NotEmpty(message = "{lang.word.tag} 'MaTTHC' {lang.phrase.may-can-not-empty}")
        private String procedureCode; // Mã thủ tục hành chính theo CSDL TTHC Quốc gia

        @JsonProperty("TenTTHC")
        @NotEmpty(message = "{lang.word.tag} 'TenTTHC' {lang.phrase.may-can-not-empty}")
        private String procedureName;

        @JsonProperty("MaLinhVuc")
        @NotEmpty(message = "{lang.word.tag} 'MaLinhVuc' {lang.phrase.may-can-not-empty}")
        private String sectorCode; // Tên lĩnh vực theo CSDL TTHC Quốc gia

        @JsonProperty("TenLinhVuc")
        @NotEmpty(message = "{lang.word.tag} 'TenLinhVuc' {lang.phrase.may-can-not-empty}")
        private String sectorName;

        @JsonProperty("KenhThucHien")
        private String applyMethod = "1"; // 1: Trực tiếp 2: Nộp trực tuyến 3: Nộp qua bưu chính công ích

        @JsonProperty("ChuHoSo")
        private String applicantName;

        @JsonProperty("LoaiDoiTuong")
        @NotEmpty(message = "{lang.word.tag} 'LoaiDoiTuong' {lang.phrase.may-can-not-empty}")
        private String applicantType = "1";

        @JsonProperty("MaDoiTuong")
        private String applicantCode = "";

        @JsonProperty("ThongTinKhac")
        private String applicantOtherInfo = "";

        @JsonProperty("Email")
        private String applicantEmail;
        @JsonProperty("Fax")
        private String applicantFax;
        @JsonProperty("SoDienThoai")
        private String applicantPhoneNumber;

        @JsonProperty("TrichYeuHoSo")
        private String applicantAbstract;

        @JsonProperty("NgayTiepNhan")
        @NotEmpty(message = "{lang.word.tag} 'NgayTiepNhan' {lang.phrase.may-can-not-empty}")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayTiepNhan' {lang.phrase.is-not-valid}")
        private String acceptedDate;

        @JsonProperty("NgayHenTra")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayHenTra' {lang.phrase.is-not-valid}")
        private String appointmentDate;

        @JsonProperty("TrangThaiHoSo")
        @NotEmpty(message = "{lang.word.tag} 'TrangThaiHoSo' {lang.phrase.may-can-not-empty}")
        private String status = "1";

        @JsonProperty("NgayTra")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayTra' {lang.phrase.is-not-valid}")
        private String completedDate;

        @JsonProperty("ThongTinTra")
        private String returneData;

        @JsonProperty("HinhThuc")
        @NotEmpty(message = "{lang.word.tag} 'HinhThuc' {lang.phrase.may-can-not-empty}")
        private String returnedMethod;

        @JsonProperty("NgayKetThucXuLy")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayKetThucXuLy' {lang.phrase.is-not-valid}")
        private String endDate;

        @JsonProperty("DonViXuLy")
        @NotEmpty(message = "{lang.word.tag} 'DonViXuLy' {lang.phrase.may-can-not-empty}")
        private ArrayList<handlingAgencyDto> handlingAgency;

        @JsonProperty("GhiChu")
        private String note;

        @JsonProperty("TaiLieuNop")
        private ArrayList<@Valid AttachmentDto> attachment;

        @JsonProperty("DanhSachLePhi")
        private ArrayList<@Valid FeeDto> fees;

        @JsonProperty("DanhSachTepDinhKemKhac")
        private ArrayList<@Valid AttachmentOtherDto> attachmentOther;

        @JsonProperty("DanhSachHoSoBoSung")
        private ArrayList<@Valid AdditionalDossierDto> additionalDossiers;

        @JsonProperty("DanhSachGiayToKetQua")
        private ArrayList<@Valid ResultDto> attachmentResult;

        @JsonProperty("NoiNopHoSo")
        @NotEmpty(message = "{lang.word.tag} 'NoiNopHoSo' {lang.phrase.may-can-not-empty}")
        private String appliedPlace;//1: Nộp từ Cổng DVC Quốc gia 2: Nôp từ Cổng DVC của BNĐP
        
        @JsonProperty("HoSoCoThanhPhanSoHoa")
        @NotEmpty(message = "{lang.word.tag} 'HoSoCoThanhPhanSoHoa' {lang.phrase.may-can-not-empty}")
        private String dossierDigitizing = "0";//0: không 1: có


        @JsonProperty("TaiKhoanDuocXacThucVoiVNeID")
        @NotEmpty(message = "{lang.word.tag} 'TaiKhoanDuocXacThucVoiVNeID' {lang.phrase.may-can-not-empty}")
        private String accountAuthenticatedWithVNeID = "1";// 0: có 1: không


        @JsonProperty("DuocThanhToanTrucTuyen")
        @NotEmpty(message = "{lang.word.tag} 'DuocThanhToanTrucTuyen' {lang.phrase.may-can-not-empty}")
        private String onlinePayment = "3"; //0: Hồ sơ không phát sinh thanh toán 1: Thanh toán trực tuyến trên cổng DVCQG 2: Thanh toán trực tuyến qua Cổng thanh toán BNĐP 3: Thanh toán trực tiếp


        @JsonProperty("NgayTuChoi")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayTuChoi' {lang.phrase.is-not-valid}")
        private String rejectionDate;

        @JsonProperty("DinhDanhCHS")
        @NotEmpty(message = "{lang.word.tag} 'DinhDanhCHS' {lang.phrase.may-can-not-empty}")
        private ArrayList<@Valid OwnerAuthenticatedDto> ownerAuthenticated;

        @JsonProperty("NgayNopHoSo")
        @NotEmpty(message = "{lang.word.tag} 'NgayNopHoSo' {lang.phrase.may-can-not-empty}")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayNopHoSo' {lang.phrase.is-not-valid}")
        private String appliedDate;

        @JsonProperty("DSKetNoiCSDL")
        private ArrayList<@Valid ConnectedDatabaseDto> ConnectedDatabase;

        @JsonProperty("isUpdate")
        @NotEmpty(message = "{lang.word.tag} 'isUpdate' {lang.phrase.may-can-not-empty}")
        private String isUpdate;
        
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AttachmentDto implements Serializable {

        @JsonProperty("TepDinhKemId")
        private String code;
        
        @JsonProperty("TenTepDinhKem")
        @NotEmpty(message = "{lang.word.tag} 'TenTepDinhKem' {lang.phrase.may-can-not-empty}")
        private String name;

        @JsonProperty("IsDeleted")
        private String IsDeleted;

        @JsonProperty("MaThanhPhanHoSo")
        @NotEmpty(message = "{lang.word.tag} 'MaThanhPhanHoSo' {lang.phrase.may-can-not-empty}")
        private String codeForm;

        @JsonProperty("DuongDanTaiTepTin")
        @NotEmpty(message = "{lang.word.tag} 'DuongDanTaiTepTin' {lang.phrase.may-can-not-empty}")
        @URL(message = "{lang.word.tag} 'DuongDanTaiTepTin' {lang.phrase.is-not-valid}")
        private String fileLink;

        @JsonProperty("DuocSoHoa")
        @NotEmpty(message = "{lang.word.tag} 'DuocSoHoa' {lang.phrase.may-can-not-empty}")
        private String digitized;

        @JsonProperty("DuocTaiSuDung")
        @NotEmpty(message = "{lang.word.tag} 'DuocTaiSuDung' {lang.phrase.may-can-not-empty}")
        private String reused;

        @JsonProperty("DuocLayTuKhoDMQG")
        @NotEmpty(message = "{lang.word.tag} 'DuocLayTuKhoDMQG' {lang.phrase.may-can-not-empty}")
        private String takeFromStockDMQG;

        @JsonProperty("MaKetQuaThayThe")
        private String resultCode;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FeeDto implements Serializable {

        @JsonProperty("TenPhiLePhi")
        @NotEmpty(message = "{lang.word.tag} 'TenPhiLePhi' {lang.phrase.may-can-not-empty}")
        private String name;
        @JsonProperty("MaPhiLePhi")
        @NotEmpty(message = "{lang.word.tag} 'MaPhiLePhi' {lang.phrase.may-can-not-empty}")
        private String code;
        @JsonProperty("HinhThucThu")
        @NotEmpty(message = "{lang.word.tag} 'HinhThucThu' {lang.phrase.may-can-not-empty}")
        private String feeMethod;
        @JsonProperty("Gia")
        @NotEmpty(message = "{lang.word.tag} 'Gia' {lang.phrase.may-can-not-empty}")
        private String amount;
        @JsonProperty("LoaiPhiLePhi")
        @NotEmpty(message = "{lang.word.tag} 'LoaiPhiLePhi' {lang.phrase.may-can-not-empty}")
        private String type;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ResultDto implements Serializable {

        @JsonProperty("TenGiayTo")
        @NotEmpty(message = "{lang.word.tag} 'TenGiayTo' {lang.phrase.may-can-not-empty}")
        private String name;

        @JsonProperty("MaThanhPhanHoSo")
        private String codeForm;

        @JsonProperty("GiayToId")
        private String code;

        @JsonProperty("DuongDanTepTinKetQua")
        @NotEmpty(message = "{lang.word.tag} 'DuongDanTepTinKetQua' {lang.phrase.may-can-not-empty}")
        @URL(message = "{lang.word.tag} 'DuongDanTaiTepTin' {lang.phrase.is-not-valid}")
        private String fileLink;

        @JsonProperty("MaGiayToKetQua")
        @NotEmpty(message = "{lang.word.tag} 'MaGiayToKetQua' {lang.phrase.may-can-not-empty}")
        private String resultCode;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OwnerAuthenticatedDto implements Serializable {

        @JsonProperty("LoaiDinhDanh")
        @NotEmpty(message = "{lang.word.tag} 'LoaiDinhDanh' {lang.phrase.may-can-not-empty}")
        private String identityType;// 1: Căn cước công dân 2: Chứng minh nhân dân 3: Mã số thuế DN


        @JsonProperty("SoDinhDanh")
        @NotEmpty(message = "{lang.word.tag} 'SoDinhDanh' {lang.phrase.may-can-not-empty}")
        private String identityNumber;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ConnectedDatabaseDto implements Serializable {

        @JsonProperty("MaCSDL")
        private String codeCSDL = "1";
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AdditionalDossierDto implements Serializable {

        @JsonProperty("HoSoBoSungId")
        @NotEmpty(message = "{lang.word.tag} 'HoSoBoSungId' {lang.phrase.may-can-not-empty}")
        private String additionalId;

        @JsonProperty("NguoiYeuCauBoSung")
        private String accepter;

        @JsonProperty("NoiDungBoSung")
        private String content;

        @JsonProperty("NgayBoSung")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayBoSung' {lang.phrase.is-not-valid}")
        private String additionalDate;

        @JsonProperty("NguoiTiepNhanBoSung")
        private String additionalter;

        @JsonProperty("ThongTinTiepNhan")
        private String contentAccept;

        @JsonProperty("NgayTiepNhanBoSung")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayTiepNhanBoSung' {lang.phrase.is-not-valid}")
        private String acceptDate;

        @JsonProperty("TrangThaiBoSung")
        private String status;

        @JsonProperty("DanhSachGiayToBoSung")
        private List<AttachmentDto> attachment;

        @JsonProperty("DanhSachLePhiBoSung")
        private List<FeeDto> fee;

        @JsonProperty("NgayHenTraTruoc")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayHenTraTruoc' {lang.phrase.is-not-valid}")
        private String appointmentOldDate;

        @JsonProperty("NgayHenTraMoi")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayHenTraMoi' {lang.phrase.is-not-valid}")
        private String appointmentNewDate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AttachmentOtherDto implements Serializable {

        @JsonProperty("TenGiayTo")
        @NotEmpty(message = "{lang.word.tag} 'TenGiayTo' {lang.phrase.may-can-not-empty}")
        private String name;

        @JsonProperty("SoLuong")
        @NotEmpty(message = "{lang.word.tag} 'SoLuong' {lang.phrase.may-can-not-empty}")
        private String quantity;

        @JsonProperty("LoaiGiayTo")
        @NotEmpty(message = "{lang.word.tag} 'LoaiGiayTo' {lang.phrase.may-can-not-empty}")
        private String type;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class handlingAgencyDto implements Serializable {

        @JsonProperty("TenDonViXuLy")
        @NotEmpty(message = "{lang.word.tag} 'TenDonViXuLy' {lang.phrase.may-can-not-empty}")
        private String agencyName;

        @JsonProperty("MaTinhDonViXuLy")
        @NotEmpty(message = "{lang.word.tag} 'MaTinhDonViXuLy' {lang.phrase.may-can-not-empty}")
        private String province;

        @JsonProperty("MaHuyenDonViXuLy")
        @NotEmpty(message = "{lang.word.tag} 'MaHuyenDonViXuLy' {lang.phrase.may-can-not-empty}")
        private String district;

        @JsonProperty("MaXaDonViXuLy")
        @NotEmpty(message = "{lang.word.tag} 'MaXaDonViXuLy' {lang.phrase.may-can-not-empty}")
        private String village;

    }
}
