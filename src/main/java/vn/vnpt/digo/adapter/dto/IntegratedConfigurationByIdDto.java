package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.kha.IntegratedConfigurationKHAExtend;
import vn.vnpt.digo.adapter.pojo.Agency;
import vn.vnpt.digo.adapter.pojo.Parameters;
import vn.vnpt.digo.adapter.pojo.IntegratedService;
import vn.vnpt.digo.adapter.pojo.Subsystem;
import vn.vnpt.digo.adapter.pojo.Tag;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IntegratedConfigurationByIdDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String name;

    private IntegratedService service;

    @JsonIgnore
    private List<Subsystem> subsystem;
    @JsonProperty("subsystem")
    private List<SubsystemDto> transSubsystem;

    @JsonIgnore
    private Tag tag;
    @JsonProperty("tag")
    private TagDto transTag;

    @JsonIgnore
    private List<Parameters> parameters;
    @JsonProperty("parameters")
    private List<ParameterDto> transParameters;

    private Boolean applyAll;

    @JsonIgnore
    private List<Agency> applyAgencies;
    @JsonProperty("applyAgencies")
    private List<AgencyConfigurationDto> transApplyAgencies;

    @JsonIgnore
    private List<Agency> exceptAgencies;
    @JsonProperty("exceptAgencies")
    private List<AgencyConfigurationDto> transExceptAgencies;
    private IntegratedConfigurationKHAExtend extendKHA;;
    private Boolean applyAllUpdateAgency;
    @JsonProperty("updateAgencies")
    private List<AgencyConfigurationDto> transUpdateAgencies;

    private Integer status;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

    private String code;

    private List<String> encryptedFieldList = new ArrayList<>();

    public void setTransSubsystem(Short localeId) {
        if (Objects.nonNull(subsystem)) {
            transSubsystem = new ArrayList<>();
            subsystem.forEach(item -> transSubsystem.add(new SubsystemDto(item, localeId)));
        }
    }

    public void setTransParameters() {
        if (Objects.nonNull(parameters)) {
            transParameters = new ArrayList<>();
            parameters.forEach(item -> transParameters.add(new ParameterDto(item)));
        }
    }

    public void setTransApplyAgencies(Short localeId) {
        if (Objects.nonNull(applyAgencies)) {
            transApplyAgencies = new ArrayList<>();
            applyAgencies.forEach(item -> transApplyAgencies.add(new AgencyConfigurationDto(item, localeId)));
        }
    }

    public void setTransExceptAgencies(Short localeId) {
        if (Objects.nonNull(exceptAgencies)) {
            transExceptAgencies = new ArrayList<>();
            exceptAgencies.forEach(item -> transExceptAgencies.add(new AgencyConfigurationDto(item, localeId)));
        }
    }

    public void setTransUpdateAgencies(Short localeId) {
        if (Objects.nonNull(extendKHA.getUpdateAgencies())) {
            transUpdateAgencies = new ArrayList<>();
            extendKHA.getUpdateAgencies().forEach(item -> transUpdateAgencies.add(new AgencyConfigurationDto(item, localeId)));
        }
    }

    public void setTransTag(Short localeId) {
        if (Objects.nonNull(tag)) {
            transTag = new TagDto(tag, localeId);
        }
    }
}
