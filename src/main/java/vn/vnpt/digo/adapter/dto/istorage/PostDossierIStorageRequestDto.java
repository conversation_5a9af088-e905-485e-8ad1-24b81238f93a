package vn.vnpt.digo.adapter.dto.istorage;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PostDossierIStorageRequestDto{
    private String src;
    private String type;
    private int year;
    private int num;
    private String code;
    private String title;
    private String departmentCode;
    private int folderId;
    private String storagePeriodId;
    private String language;
    private String backupPlan;
    private String fromDate;
    private String toDate;
    private String modeOfUse;
    private String physicalState;
    private String sender;




}
