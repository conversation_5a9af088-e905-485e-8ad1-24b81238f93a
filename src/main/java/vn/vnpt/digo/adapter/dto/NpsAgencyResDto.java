/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsAgencyResDto implements Serializable {
    
    private List<NpsAgencyResultDto> result;
    private Integer errorCode;

    @JsonSetter("error_code")
    public void mapperErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }
}
