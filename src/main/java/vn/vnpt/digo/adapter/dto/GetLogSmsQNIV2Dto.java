package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.digo.adapter.document.IntegratedServiceLogSmsbrandname;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Document(collection = "integratedServiceLogSmsbrandname")
public class GetLogSmsQNIV2Dto implements Serializable {
    private CodeDto dossier;
    private String content;
    private List<String> phoneNumbers;
    private ApplyMethodDto applyMethod;
    private NameDto currentTask;
    private AgenciesDto agencies;
    private Date createdDate;
    private Integer sendStatus = 1;
    private Integer autoSend = 1;
    private String responseSendSms;
    private Integer resendStatus;

    public GetLogSmsQNIV2Dto(IntegratedServiceLogSmsbrandname obj){
        ObjectMapper mapper = new ObjectMapper();
        this.createdDate = obj.getCreatedDate();

        if(Objects.nonNull(obj.getItem())){
            CodeDto dossier = new CodeDto();
            dossier.setId(obj.getItem().getId());
            dossier.setCode(obj.getItem().getCode());
            this.dossier = dossier;
        }
        if (Objects.nonNull(obj.getExtend())){
            LinkedHashMap<String,Object> extend = (LinkedHashMap<String,Object>) obj.getExtend();
            this.content = extend.get("content").toString();
            this.phoneNumbers = (List<String>)extend.get("phoneNumber");
            try{
                String json = mapper.writeValueAsString(extend.get("agencies"));
                this.agencies = mapper.readValue(json, AgenciesDto.class);
            }catch (Exception ex){}
            try{
                String json = mapper.writeValueAsString(extend.get("applyMethod"));
                this.applyMethod = mapper.readValue(json, ApplyMethodDto.class);
            }catch (Exception ex){}
            try{
                String json = mapper.writeValueAsString(extend.get("currentTask"));
                this.currentTask = mapper.readValue(json, NameDto.class);
            }catch (Exception ex){}

            if(Objects.nonNull(extend.get("sendStatus"))){
                Integer sendStatus = (Integer)extend.get("sendStatus");
                this.sendStatus = sendStatus;
            }

            if(Objects.nonNull(extend.get("autoSend"))){
                Integer autoSend = (Integer)extend.get("autoSend");
                this.autoSend = autoSend;
            }
        }
        if (Objects.nonNull(obj.getExtendQNI())){
            this.responseSendSms = obj.getExtendQNI().getBodyResponse();
            this.resendStatus = obj.getExtendQNI().getStatus();
        }
        if(Objects.isNull(dossier)){
            this.autoSend = 0;
        }
    }
}
