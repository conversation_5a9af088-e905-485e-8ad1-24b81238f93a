package vn.vnpt.digo.adapter.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

public class PlaceGsoDto {
    @Getter
    private String placeId;
    @Getter
    private String gsoId;
    @Getter
    private int level;
    @Getter
    private final List<String> ancestor = new ArrayList<>();

    public PlaceGsoDto(String placeId, List<String> ancestors) {
        level = -1;
        if(placeId == null) {
            return;
        }
        if(ancestors != null) {
            this.ancestor.addAll(ancestors);
        }
        this.placeId = placeId;
        if(placeId.startsWith("5def47c5f47614018c0000")) {
            level = 1;
            gsoId = placeId.substring(placeId.length() - 2);
            return;
        }
        if(placeId.startsWith("5def47c5f47614018c001")) {
            level = 2;
            gsoId = placeId.substring(placeId.length() - 3);
            return;
        }
        if(placeId.startsWith("5def47c5f47614018c1")) {
            level = 3;
            gsoId = placeId.substring(placeId.length() - 5);
        }
    }
}
