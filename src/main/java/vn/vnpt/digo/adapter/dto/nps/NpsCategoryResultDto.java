package vn.vnpt.digo.adapter.dto.nps;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsCategoryResultDto implements Serializable {
    @JsonProperty("MAGIAYTO")
    private String MAGIAYTO;

    @JsonProperty("TENGIAYTO")
    private String TENGIAYTO;

    @JsonProperty("LOAIGIAYTO")
    private String LOAIGIAYTO;

    @JsonProperty("MAGIAYTOKQTHAYTHE")
    private String MAGIAYTOKQTHAYTHE;

    @JsonProperty("TEPDINHKEM")
    private String TEPDINHKEM;
    
}
