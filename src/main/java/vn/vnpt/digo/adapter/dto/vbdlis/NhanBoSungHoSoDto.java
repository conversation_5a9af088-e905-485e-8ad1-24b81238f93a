package vn.vnpt.digo.adapter.dto.vbdlis;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NhanBoSungHoSoDto implements Serializable {

    @NotNull
    @JsonProperty("SoBienNhan")
    public String soBienNhan;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @JsonProperty("NgayNhanBoSung")
    public Date ngayNhanBoSung;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @JsonProperty("NgayHenTraMoi")
    public Date ngayHenTraMoi;

    @JsonProperty("GhiChu")
    public String ghiChu;

    @JsonProperty("TapTin")
    public DongBoTrangThaiKetThucDto.TapTin tapTin;

}
