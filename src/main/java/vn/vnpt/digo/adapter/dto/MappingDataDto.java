/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.pojo.MappingDataType;
import vn.vnpt.digo.adapter.pojo.MappingDataValue;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MappingDataDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private MappingDataType type;

    private MappingDataValue source;

    private MappingDataValue dest;

    private Integer status;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate;

    private Boolean ignoreDeployment;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;

    public String getSourceId() {
        if (Objects.nonNull(this.source)) {
            return this.source.getId();
        } else {
            return null;
        }
    }

    public String getDestId() {
        if (Objects.nonNull(this.dest)) {
            return this.dest.getId();
        } else {
            return null;
        }
    }
}
