package vn.vnpt.digo.adapter.dto.vnptctdt;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.qlvb.FileResponseDto;
import vn.vnpt.digo.adapter.pojo.File;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultRequestDto implements Serializable {
    @JsonProperty("MaCoQuan")
    private String maCoQuan;
    @JsonProperty("CoQuanChuQuan")
    private String coQuanChuQuan;
    @JsonProperty("NgayTiepNhan")
    private String ngayTiepNhan;
    @JsonProperty("MaHoSo")
    private String maHoSo;
    @JsonProperty("MaTTHC")
    private String maTTHC;
    @JsonProperty("TenNguoiNop")
    private String tenNguoiNop;
    @JsonProperty("SoDienThoai")
    private String soDienThoai;
    @JsonProperty("SoDinhDanh")
    private String soDinhDanh;
    @JsonProperty("MaSoThue")
    private String maSoThue;
    @JsonProperty("Email")
    private String email;
    @JsonProperty("TechnicalID")
    private String technicalID;
    @JsonProperty("ChungThucDienTu")
    private List<GiayToChungThucResult> chungThucDienTu;
    @JsonProperty("ChungThucGiay")
    private List<GiayToChungThucResult> chungThucGiay;
    @JsonProperty("PhiLePhiTong")
    private String phiLePhiTong;
    @JsonProperty("TrangThai")
    private String trangThai;
    @JsonProperty("YKienTraoDoi")
    private String yKienTraoDoi;
    @JsonProperty("ThoiGianHoanThanh")
    private String thoiGianHoanThanh;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GiayToChungThucResult implements Serializable {
        @JsonProperty("MaGiayTo")
        private String maGiayTo;
        @JsonProperty("TenGiayTo")
        private String tenGiayTo;
        @JsonProperty("Url")
        private String url;
        @JsonProperty("SoTrang")
        private int soTrang;
        @JsonProperty("SoBan")
        private int soBan;
        @JsonProperty("LoaiGiayTo")
        private int loaiGiayTo;
        @JsonProperty("UrlSign")
        private String urlSign;
        @JsonProperty("StatusGetFileSign")
        private Boolean statusGetFileSign;
        @JsonProperty("FileSignInfo")
        private FileSignInfoDto fileSignInfo; // có khi đã lưu về iGate
        @JsonProperty("SoCT")
        private String soCT;
        @JsonProperty("QuyenSo")
        private String quyenSo;
        @JsonProperty("ThanhTien")
        private String thanhTien;
        @JsonProperty("DongY")
        private int dongY;
        @JsonProperty("LyDo")
        private String lyDo;
    }
}
