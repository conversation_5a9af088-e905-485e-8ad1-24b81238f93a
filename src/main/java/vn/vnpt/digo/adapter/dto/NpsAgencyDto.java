package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsAgencyDto implements Serializable {

    private String code;

    private String name;

    @JsonSetter("MADONVI")
    public void mapperCode(String code) {
        this.code = code;
    }

    @JsonSetter("TENDONVI")
    public void mapperName(String name) {
        this.name = name;
    }
}
