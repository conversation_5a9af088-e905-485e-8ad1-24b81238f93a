package vn.vnpt.digo.adapter.dto.bdg;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.JsonObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VNPTPayVietQrNotifyPaymentResponseBDGDto implements Serializable {

    @JsonProperty("responseCode")
    public String responseCode = null;

    @JsonProperty("description")
    public String description = null;

    @JsonProperty("data")
    public JsonObject data = null;
}
