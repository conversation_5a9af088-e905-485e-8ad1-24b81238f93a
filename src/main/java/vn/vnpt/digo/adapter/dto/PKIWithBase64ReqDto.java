package vn.vnpt.digo.adapter.dto;

import com.vnpt.hashsignature.pdf.PdfParameter;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.CheckConfigBody;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PKIWithBase64ReqDto implements Serializable {
    private ObjectId configId;
    private ObjectId agencyId;
    private ObjectId subsystemId;
    private String base64File;
    private String fileName;
    private String base64Image;
    private Integer signType;
    private String phone;
    private String messageDisplay;
    private String reason;
    private String location;
    private String signPosition;
    private Integer signPage;
    private List<String> textSearch;
    private Boolean autoSign;
    private String msspProvider;
    private String side;
    private Float width;
    private Float height;
    public PdfParameter.RenderMode getRenderMode(){
        switch(this.signType){
            case 0: return PdfParameter.RenderMode.TEXT_ONLY;
            case 1: return PdfParameter.RenderMode.IMAGE_ONLY;
            case 2: return PdfParameter.RenderMode.BOTH_IMAGE_AND_TEXT;
            default: return PdfParameter.RenderMode.TEXT_ONLY;
        }
    }
}
