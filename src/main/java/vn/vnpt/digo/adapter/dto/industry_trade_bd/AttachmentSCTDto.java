package vn.vnpt.digo.adapter.dto.industry_trade_bd;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttachmentSCTDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String filename;

    private Integer size;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId group =  new ObjectId("5f9bd9692994dc687e68b5a6");;

}
