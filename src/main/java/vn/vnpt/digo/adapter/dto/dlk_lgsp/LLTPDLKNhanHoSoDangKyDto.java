/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.dlk_lgsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPDossierDetailLLTPDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LLTPDeclarationDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LLTPMandatorDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.LLTPResidentDto;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LLTPDLKNhanHoSoDangKyDto implements Serializable {
    private String idReceivedDec;
    private String dateReceivedDec;
    private String datePromissoryDec;
    private String idMoneyReceipt;
    @JsonProperty("declarationForm")
    private LLTPDLKDeclarationDto declarationForm;
    @JsonProperty("residenceForm")
    private List<LLTPResidentDto> residenceForm;
    @JsonProperty("mandatorForm")
    private LLTPDLKMandatorDto mandatorForm;

    /*public LLTPDLKNhanHoSoDangKyDto(HCMLGSPDossierDetailLLTPDto dossier) {
        Logger logger = LoggerFactory.getLogger(LLTPDLKNhanHoSoDangKyDto.class);
        try {
            DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            Date today = new Date();
//            String currenDate = dateFormat.format(new Date());

            // dossier info
            this.idReceivedDec = dossier.getCode();
            //AcceptedDate
            if(Objects.nonNull(dossier.getAcceptedDate())){
                this.dateReceivedDec = dateFormat.format(dossier.getAcceptedDate());
            } else {
                this.dateReceivedDec = dateFormat.format(today);
            }
            //AppointmentDate
            if(Objects.nonNull(dossier.getAppointmentDate())){
                this.datePromissoryDec = dateFormat.format(dossier.getAppointmentDate());
            } else {
                this.datePromissoryDec = dateFormat.format(today);
            }
            HCMLGSPDossierDetailLLTPDto.EformData efData = dossier.getEForm().getData();
            this.idMoneyReceipt = efData.getIdMoneyReceipt();

            this.declarationForm = new LLTPDLKDeclarationDto();
            this.declarationForm.setFullName(efData.getNdkfullName());
            this.declarationForm.setGenderId(efData.getNdkgenderId());
            this.declarationForm.setBirthDateStr(efData.getNdkbirthDateStr());
            this.declarationForm.setBirthPlace(efData.getNdkbirthPlace());
            this.declarationForm.setNationalityId(efData.getNdknationalityId() != null ? efData.getNdknationalityId().getId() : "");
            this.declarationForm.setEthnicId(efData.getNdkethnicId() != null ? efData.getNdkethnicId().getId(): "");
            this.declarationForm.setResidence(efData.getNdkresidence());
//            this.declarationForm.setReRegionId(efData.getNdkreRegionId() != null ? efData.getNdkreRegionId().getValue() : "");
            this.declarationForm.setResidenceTemporary(efData.getNdkresidenceTemporary());
//            this.declarationForm.setRtRegionId(efData.getNdkrtRegionId() != null ? efData.getNdkrtRegionId().getValue() : "");
            this.declarationForm.setIdTypeId(efData.getNdkidTypeId());
            this.declarationForm.setIdentifyNo(efData.getNdkidentifyNo());
            this.declarationForm.setIdIssueDate(efData.getNdkidIssueDate());
            this.declarationForm.setIdIssuePlace(efData.getNdkidIssuePlace());
            this.declarationForm.setDadName(efData.getChadadName());
            this.declarationForm.setDadDob(efData.getChadadDob());
            this.declarationForm.setMomName(efData.getMemomName());
            this.declarationForm.setMomDob(efData.getMemomDob());
            this.declarationForm.setPartnerName(efData.getNdkpartnerName());
            this.declarationForm.setPartnerDob(efData.getNdkpartnerDob());
            this.declarationForm.setPhone(efData.getNdkphone());
            this.declarationForm.setEmail(efData.getNdkemail());
//            this.declarationForm.setMinistryJusticeId(efData.getNdkministryJusticeId() != null ? efData.getNdkministryJusticeId().getId() : "");

            // DeclareDate
//            this.declarationForm.setDeclareDate(efData.getNdkdeclareDate());
//            if(Objects.nonNull(dossier.getAppointmentDate())){
//                this.datePromissoryDec = dateFormat.format(dossier.getAppointmentDate());
//                this.declarationForm.setDeclareDate(efData.getNdkdeclareDate());
//            } else {
//                this.datePromissoryDec = dateFormat.format(today);
//            }

            this.declarationForm.setDeclareTypeId(efData.getDeclareTypeId());
            this.declarationForm.setRequestQty(efData.getRequestQty());
            this.declarationForm.setRequestQtyAdd(efData.getRequestQtyAdd());
            this.declarationForm.setObjectRequestId(efData.getObjectRequestId());
//            this.declarationForm.setAgencyRequestId(efData.getAgencyRequestId() != null ? efData.getAgencyRequestId().getId() : "");
//            this.declarationForm.setRegionRequestId(efData.getRegionRequestId());
            this.declarationForm.setFormType(efData.getFormType());
            this.declarationForm.setIsBanPosition(efData.getIsBanPosition() != null ? efData.getIsBanPosition().getValue() : "");
//            this.declarationForm.setDelivery(efData.getDelivery());
//            this.declarationForm.setDeliveryAddress(efData.getDeliveryAddress());
//            this.declarationForm.setDeliveryDistrict(efData.getNdkdeliveryDistrict() != null ? efData.getNdkdeliveryDistrict().getValue() : "");
            this.declarationForm.setNote(efData.getNote());
            this.declarationForm.setPurpose(efData.getPurpose());
//            this.declarationForm.setReceiveNo(dossier.getCode());
//            this.declarationForm.setDeclarationPortalID(efData.getDeclarationPortalID());
            this.declarationForm.setOtherName(efData.getOtherName());

            this.residenceForm = new ArrayList<LLTPResidentDto>();
            LLTPResidentDto residentDto = null;
            for(HCMLGSPDossierDetailLLTPDto.ThongTinCuTru item: efData.getThongTinCuTrus()) {
                residentDto = new LLTPResidentDto();
                residentDto.setFromDateStr(item.getFromDateStr());
                residentDto.setJobName(item.getJobName());
                residentDto.setResidencePlace(item.getResidencePlace());
                residentDto.setToDateStr(item.getToDateStr());
                residentDto.setWorkPlace(item.getWorkPlace());

                this.residenceForm.add(residentDto);
            }

            this.mandatorForm = new LLTPDLKMandatorDto();
            this.mandatorForm.setFullName(efData.getNuqfullName());
            this.mandatorForm.setGenderId(efData.getNuqgenderId());
            this.mandatorForm.setBirthDateStr(efData.getNuqbirthDateStr());
            this.mandatorForm.setBirthPlaceId(efData.getNuqbirthPlaceId() != null ? efData.getNuqbirthPlaceId().getId() : "");
            this.mandatorForm.setResidence(efData.getNuqresidence());
            this.mandatorForm.setRegionId(efData.getNuqregionId() != null ? efData.getNuqbirthPlaceId().getId() : "");
            this.mandatorForm.setIdTypeId(efData.getNuqidTypeId());
            this.mandatorForm.setIdentifyNo(efData.getNuqidentifyNo());
            this.mandatorForm.setIdIssueDate(efData.getNuqidIssueDate());
            this.mandatorForm.setIdIssuePlace(efData.getNuqidIssuePlace());
            this.mandatorForm.setMandatorRelation(efData.getNuqmandatorRelation());
            this.mandatorForm.setMandatorDate(efData.getNuqmandatorDate());
        } catch (Exception e) {
            logger.debug("Constructor: Create LLTPNhanHoSoDangKyDto sync data failed: " + e.getMessage());
        }
    }*/
}
