/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LGSPHCMCivilStatusJusticePostRegistratioDto implements Serializable {
    private String status;
    private String statusDescription;
    private String errorCode;
    private String errorDescription; 
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class response implements Serializable {
        private String receiveRecordResponse;
    }
}
