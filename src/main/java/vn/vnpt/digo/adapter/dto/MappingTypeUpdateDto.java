package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.MappingTypeDataAccess;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MappingTypeUpdateDto implements Serializable {

    private String name;
    private String description;
    private MappingTypeDataAccess sourceDataAccess;
    private MappingTypeDataAccess destDataAccess;
    private Integer status;
}
