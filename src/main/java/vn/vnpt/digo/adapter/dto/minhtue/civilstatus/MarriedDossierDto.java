package vn.vnpt.digo.adapter.dto.minhtue.civilstatus;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(
        name = "hoso"
)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MarriedDossierDto implements Serializable {
    private String id;
    private String so;
    private String quyenSo;
    private String trangSo;
    private String ngayDangKy;
    private Long loaiDangKy;
    private String noiDangKy;
    private String nguoiKy;
    private String chucVuNguoiKy;
    private String ngayXacLapQuanHeHonNhan;
    private String nguoiThucHien;
    private String ghiChu;
    private Long tinhTrangKetHon;
    private String huyKetHonNgayGhiChu;
    private String huyKetHonCanCu;
    private String congNhanKetHonNgayGhiChus;
    private String congNhanKetHonCanCu;
    private String chongHoTen;
    private String chongNgaySinh;
    private Long chongDanToc;
    private Long chongDanTocKhac;
    private String chongQuocTich;
    private String[] chongQuocTichKhac;
    public String[] getChongQuocTichKhac() {
        String[] temp = chongQuocTichKhac;
        if (chongQuocTichKhac.length > 0) {
            temp = new String[]{"<![CDATA['"};
            for (int i = 0; i < chongQuocTichKhac.length; i++) {
                temp[0] += chongQuocTichKhac[i];
                if (chongQuocTichKhac.length - 1 != i) {
                    temp[0] += ",";
                } else {
                    temp[0] += "']]>";
                }
            }
        }
        return temp;
    }
    private Long chongLoaiCuTru;
    private String chongNoiCuTru;
    private Long chongLoaiGiayToTuyThan;
    private String chongGiayToKhac;
    private String chongSoGiayToTuyThan;
    private String chongNgayCapGiayToTuyThan;
    private String chongNoiCapGiayToTuyThan;
    private String voHoTen;
    private String voNgaySinh;
    private Long voDanToc;
    private String voDanTocKhac;
    private String voQuocTich;
    private String[] voQuocTichKhac;
    public String[] getVoQuocTichKhac() {
        String[] temp = voQuocTichKhac;
        if (voQuocTichKhac.length > 0) {
            temp = new String[]{"<![CDATA['"};
            for (int i = 0; i < voQuocTichKhac.length; i++) {
                temp[0] += voQuocTichKhac[i];
                if (voQuocTichKhac.length - 1 != i) {
                    temp[0] += ",";
                } else {
                    temp[0] += "']]>";
                }
            }
        }
        return temp;
    }
    private String voNoiCuTru;
    private Long voLoaiGiayToTuyThan;
    private String voGiayToKhac;
    private String voSoGiayToTuyThan;
    private Long   voLoaiCuTru;
    private String voNgayCapGiayToTuyThan;
    private String voNoiCapGiayToTuyThan;
    private String soDangKyNuocNgoai;
    private String ngayDangKyNuocNgoai;
    private String cqNuocNgoaiDaDangKy;
    private String qgNuocNgoaiDaDangKy;
}
