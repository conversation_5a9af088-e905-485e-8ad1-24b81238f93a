/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.dto.ilis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DanhMucThuTucDto implements Serializable {

    @JsonProperty("MaThuTuc")
    public String maThuTucH;
    
    @JsonProperty("TenThuTuc")
    public String tenThuTuc;

    @JsonProperty("TenLinhVuc")
    public String tenLinhVuc;

    @JsonProperty("MaLinhVuc")
    public String maLinhVuc;

    @JsonProperty("MaQuyTrinh")
    public String maQuyTrinh;

    @JsonProperty("TenQuyTrinh")
    public String tenQuyTrinh;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProcedureDto implements Serializable {

        private String procedureCode;
        private String procedureName;
        private String secterCode;
        private String secterName;
        private String processDefinitionCode;
        private String processDefinitionName;
    }
}
