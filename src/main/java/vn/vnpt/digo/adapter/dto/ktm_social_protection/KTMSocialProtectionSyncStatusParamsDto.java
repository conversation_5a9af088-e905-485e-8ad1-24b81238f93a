package vn.vnpt.digo.adapter.dto.ktm_social_protection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.util.CheckConfigParams;

import java.io.Serializable;
@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigParams
public class KTMSocialProtectionSyncStatusParamsDto implements Serializable {
    public String AcceptKey;

    //    @ParamName("config-id")
    public String MaHoSo;

    //    @ParamName("agency-id")
    public String TaiKhoanXuLy;

    //    @ParamName("subsystem-id")
    public String NguoiXuLy;

    //    @ParamName("dossier")
    public String ChucDanh;

    public String ThoiDiemXuLy;

    public String DonViXuLy;

    public String NoiDungXuLy;

    public String StatusId;

    public String NgayBatDau;

    public String NgayKetThucTheoQuyDinh;

    public String UseridCreated;

    public String UseridEdited;

    public String DateCreated;

    public String DateEdited;
}
