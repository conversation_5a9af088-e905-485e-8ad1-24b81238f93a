package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsAnswerMinistryResultDto implements Serializable {

    private Integer hoiDapID;
    private Integer traLoiId;
    private String noiDungTraLoi;
    private String maDonVi;
    private String tenDonVi;

    @JsonSetter("HOIDAPID")
    public void mapHoiDapID(Integer hoiDapID) {
        this.hoiDapID = hoiDapID;
    }

    @JsonSetter("MADONVI")
    public void mapMaDonVi(String maDonVi) {
        this.maDonVi = maDonVi;
    }

    @JsonSetter("TENDONVI")
    public void mapTenDonVi(String tenDonVi) {
        this.tenDonVi = tenDonVi;
    }

    @JsonSetter("TRALOIID")
    public void mapTraLoiId(Integer traLoiId) {
        this.traLoiId = traLoiId;
    }

    @JsonSetter("NOIDUNGTRALOI")
    public void mapNoiDungTraLoi(String noiDungTraLoi) {
        this.noiDungTraLoi = noiDungTraLoi;
    }

}
