/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VNPostInforPostDto implements Serializable {
    @JsonProperty("status")
    private Status status;

    @JsonProperty("postInfomation")
    private List<PostInfor> postInfomation;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PostInfor implements Serializable {
        @JsonProperty("provinceCode")
        private float provinceCode;

        @JsonProperty("districtCode")
        private float districtCode;

        @JsonProperty("postId")
        private float postId;

        @JsonProperty("communeCode")
        private String communeCode; 

        @JsonProperty("name")
        private String name;

        @JsonProperty("tel")
        private String tel;

        @JsonProperty("address")
        private String address;

        @JsonProperty("longtitude")
        private String longtitude;
        
        @JsonProperty("lattitude")
        private String lattitude;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Status implements Serializable {
        @JsonProperty("code")
        private String code;

        @JsonProperty("desc")
        private String desc;

    }
}
