/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FptPsDossierDto implements Serializable {

    private String agency;
    private String code;
    private Applicant applicant;
    private List<Attachment> attachment;
    private Kind dossierReceivingKind = new Kind();
    private Procedure procedure;
    private Place receivingPlace;
    private Sync sync;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Applicant implements Serializable {

        private Address address;
        private String fullname;
        private String phoneNumber;
        private String email;
        private Identity identity;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Identity implements Serializable {
        
        private String number;
    }
    

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Address implements Serializable {

        private String address;
        private String place;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Attachment implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String filename;
        private String group;
        private Integer size;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Procedure implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String code;
        private Tag sector;
        private List<Trans> translate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Tag implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String code;
        private List<Trans> name;
        public void setName(String name) {
            this.name = new ArrayList<>(){{add(new Trans((short)228,name));}};
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class Kind implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id = new ObjectId("5f8968888fffa53e4c073ded");
        private List<Trans> name = new ArrayList<>() {
            {
                add(new Trans((short)228, "Trực tiếp"));
            }
        };
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Place implements Serializable {

        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String fullAddress;
        private String nationName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class Sync implements Serializable {

        private String sourceCode;
        private Integer typeId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Trans implements Serializable {

        private Short languageId;
        private String name;
    }

}
