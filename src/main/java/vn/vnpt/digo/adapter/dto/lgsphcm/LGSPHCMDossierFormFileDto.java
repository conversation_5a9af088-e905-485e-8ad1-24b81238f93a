package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.bson.types.ObjectId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;
import vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMPadApplyDto.AttachmentDto;
import vn.vnpt.digo.adapter.pojo.ProcedureForm;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LGSPHCMDossierFormFileDto implements Serializable {

    //    @JsonSerialize(using = ToStringSerializer.class)
    private String id;

    private vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMDossierFormFileDto.Id dossier;

    private ProcedureForm procedureForm;

    @Field("case")
    @JsonProperty("case")
    private vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMDossierFormFileDto.Id caze;

    private vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMDossierFormFileDto.ProcedureFormDetail detail;

    private int order;

    private int quantity;

    private ArrayList<AttachmentDto> file;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Id implements Serializable {

        private String id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcedureFormDetail implements Serializable {

        private vn.vnpt.digo.adapter.dto.lgsphcm.LGSPHCMDossierFormFileDto.ProcedureFormDetailType type;
        private int quantity;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcedureFormDetailType implements Serializable {

        private String id;

        private String name;
    }

}
