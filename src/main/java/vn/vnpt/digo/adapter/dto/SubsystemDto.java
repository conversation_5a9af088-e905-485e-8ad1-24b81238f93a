package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.pojo.Subsystem;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubsystemDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String code;

    private String name;

    public SubsystemDto(Subsystem subsystem, Short localeId) {
        id = subsystem.getId();
        if (Objects.nonNull(subsystem.getCode())) {
            code = subsystem.getCode();
        }
        if (Objects.nonNull(subsystem.getTrans())) {
            subsystem.getTrans().forEach(nameItem -> {
                if (nameItem.getLanguageId().equals(localeId)) {
                    name = nameItem.getName();
                }
            });
        }
    }
}
