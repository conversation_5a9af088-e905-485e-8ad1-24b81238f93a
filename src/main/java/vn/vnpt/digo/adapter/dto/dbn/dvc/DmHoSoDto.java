package vn.vnpt.digo.adapter.dto.dbn.dvc;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DmHoSoDto implements Serializable {
    @JsonProperty("MaHoSo")
    private String maHoSo;
    @JsonProperty("IdHoSo")
    private String idHoSo;
    @JsonProperty("MaTTHC")
    private String maTTHC;
    @JsonProperty("TenTTHC")
    private String tenTTHC;
    @JsonProperty("MaLinhVuc")
    private String maLinhVuc;
    @JsonProperty("TenLinhVuc")
    private String tenLinhVuc;
    @JsonProperty("KenhThucHien")
    private Integer kenhThucHien;
    @JsonProperty("ChuHoSo")
    private String chuHoSo;
    @JsonProperty("LoaiDoiTuong")
    private String loaiDoiTuong;
    @JsonProperty("TrichYeuHoSo")
    private String trichYeuHoSo;
    @JsonProperty("NgayTiepNhan")
    private String ngayTiepNhan;
    @JsonProperty("NgayHenTra")
    private String ngayHenTra;
    @JsonProperty("TrangThaiHoSo")
    private Integer trangThaiHoSo;
    @JsonProperty("NgayTra")
    private String ngayTra;
    @JsonProperty("ThongTinTra")
    private String thongTinTra;
    @JsonProperty("HinhThuc")
    private Integer hinhThuc;
    @JsonProperty("NgayKetThucXuLy")
    private String ngayKetThucXuLy;
    @JsonProperty("DonViXuLy")
    private String donViXuLy;
    @JsonProperty("IdDonViXuLy")
    private String idDonViXuLy;
    @JsonProperty("GhiChu")
    private String ghiChu;
    @JsonProperty("DanhSachLePhi")
    private List<LePhiDto> danhSachLePhi;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LePhiDto implements Serializable {
        @JsonProperty("TenPhiLePhi")
        private String tenPhiLePhi;
        @JsonProperty("MaPhiLePhi")
        private String maPhiLePhi;
        @JsonProperty("HinhThucThu")
        private String hinhThucThu;
        @JsonProperty("Gia")
        private Integer gia;
    }
}
