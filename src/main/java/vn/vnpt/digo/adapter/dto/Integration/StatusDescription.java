package vn.vnpt.digo.adapter.dto.Integration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatusDescription implements Serializable {
    private String id;
    private String name;
    private Integer primary;

    public StatusDescription(String id,String name){
        this.id = id;
        this.name = name;
    }

    public static List<StatusDescription> initDossierStatusMaps(){
        List<StatusDescription> items = new ArrayList<>();
        items.add(new StatusDescription("0","Chờ tiếp nhận"));
        items.add(new StatusDescription("1","Chờ bổ sung"));
        items.add(new StatusDescription("2","Đang xử lý"));
        items.add(new StatusDescription("3","Tạm dừng"));
        items.add(new StatusDescription("4","<PERSON>ã có kết quả"));
        items.add(new StatusDescription("5","Đã trả kết quả"));
        items.add(new StatusDescription("6","<PERSON><PERSON> hủy"));
        items.add(new StatusDescription("8","Chờ phê duyệt bổ sung"));
        items.add(new StatusDescription("9","Chờ phê duyệt tạm dừng"));
        items.add(new StatusDescription("10","Chờ phê duyệt gia hạn"));
        items.add(new StatusDescription("11","Chờ phê duyệt để hủy"));
        items.add(new StatusDescription("12","Hủy xử lý"));
        items.add(new StatusDescription("13","Yêu cầu rút"));
        items.add(new StatusDescription("14","Đề nghị thanh toán (Chờ tiếp nhận)"));
        items.add(new StatusDescription("15","Đã thanh toán (Chờ tiếp nhận)"));
        items.add(new StatusDescription("16","Đã thanh toán (Đang xử lý)"));
        items.add(new StatusDescription("17","Đề nghị thanh toán (Đang xử lý)"));
        items.add(new StatusDescription("18","Gửi thông báo thanh toán trực tiếp"));
        items.add(new StatusDescription("19","Chuyên ngành từ chối"));
        items.add(new StatusDescription("20","Xác nhận thanh toán"));
        items.add(new StatusDescription("21","Yêu cầu nộp bản giấy"));
        items.add(new StatusDescription("22","Được cấp biên lai"));
        items.add(new StatusDescription("23","Yêu cầu bổ sungn"));
        return items;
    }
}
