package vn.vnpt.digo.adapter.dto.hbh.tnmt;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.bdg.SelectFormIO;

import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DonDangKy implements Serializable{
    @JsonProperty("ChuSuDung")
    private ArrayList<ChuSuDung> ChuSuDung;

    @JsonProperty("SoPhatHanh")
    private String SoPhatHanh;

    @JsonProperty("SoVaoSo")
    private String SoVaoSo;

    @JsonProperty("NgayCapGCN")
    private String NgayCapGCN;

    @JsonProperty("DiaChiThuaDat")
    private DiaChiThuaDat DiaChiThuaDat;

    @JsonProperty("NoiDungBienDong")
    private String NoiDungBienDong;

    @JsonProperty("LyDoBienDong")
    private String LyDoBienDong;

    @JsonProperty("NhuCauCapMoi")
    private String NhuCauCapMoi;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiaChiThuaDat implements Serializable {

        @JsonProperty("MaTinh")
        private SelectFormIO MaTinh;

        @JsonProperty("MaHuyen")
        private SelectFormIO MaHuyen;

        @JsonProperty("MaXa")
        private SelectFormIO MaXa;

        @JsonProperty("DiaChiChiTiet")
        private String DiaChiChiTiet;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChuSuDung {
        @JsonProperty("HoVaTen")
        private String HoVaTen;

        @JsonProperty("NgaySinh")
        private String NgaySinh;

        @JsonProperty("SoCMND")
        private String SoCMND;

        @JsonProperty("DiaChi")
        private String DiaChi;

    }
}
