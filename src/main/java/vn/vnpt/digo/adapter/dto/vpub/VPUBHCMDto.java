package vn.vnpt.digo.adapter.dto.vpub;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
@NotNull
public class VPUBHCMDto implements Serializable {
    @JsonProperty("ThongTin")
    public String ThongTin;

    @JsonProperty("SoToTrinh")
    public String SoToTrinh;

    @JsonProperty("NgayToTrinh")
    public String NgayToTrinh;

    @JsonProperty("TrichYeuToTrinh")
    public String TrichYeuToTrinh;

    @JsonProperty("NguoiKyToTrinh")
    public String NguoiKyToTrinh;

    @JsonProperty("TinhThanhPho")
    public String TinhThanhPho;

    @JsonProperty("QuanHuyen")
    public String QuanHuyen;

    @JsonProperty("PhuongXa")
    public String PhuongXa;

    @JsonProperty("SoNhaDuong")
    public String SoNhaDuong;

    @JsonProperty("SoDienThoai")
    public String SoDienThoai;

    @JsonProperty("fax")
    public String fax;

    @JsonProperty("email")
    public String email;

    @JsonProperty("SoToTrinhVub")
    public String SoToTrinhVub;

    @JsonProperty("CoQuanBanHanhVub")
    public String CoQuanBanHanhVub;

    @JsonProperty("NgayBanHanhVub")
    public String NgayBanHanhVub;

    @JsonProperty("TrichYeuVanBanVub")
    public String TrichYeuVanBanVub;

    @JsonProperty("LoaiVanBanVub")
    public String LoaiVanBanVub;

    @JsonProperty("MaHoSo")
    public String MaHoSo;

    @JsonProperty("LoaiHoSo")
    public String LoaiHoSo;

    @JsonProperty("TenLinhVuc")
    public String TenLinhVuc;

    @JsonProperty("NgayTiepNhan")
    public String NgayTiepNhan;

    @JsonProperty("NgayHenTra")
    public String NgayHenTra;

    @JsonProperty("HanTraHoSo")
    public String HanTraHoSo;

    @JsonProperty("ThoiGianHanTra")
    public String ThoiGianHanTra;

    @JsonProperty("TaiKhoanCanBo")
    public String TaiKhoanCanBo;

    @JsonProperty("madonvi")
    public String madonvi;

    @JsonProperty("dossierId")
    public String dossierId;

    @JsonProperty("urlFile")
    public ArrayList<TapTinDto> urlFile;


}
