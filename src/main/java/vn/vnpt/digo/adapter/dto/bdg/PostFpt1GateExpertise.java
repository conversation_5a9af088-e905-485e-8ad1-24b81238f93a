package vn.vnpt.digo.adapter.dto.bdg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.DossierFormFileDto;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PostFpt1GateExpertise {
    @NotNull
    private String dossierOnlineApplyId;

    @NotNull
    private String fpt1GateSectorCode;

    @NotNull
    private String fpt1GateSectorId;

    @NotNull
    private String fpt1GateProcedureCode;

    @NotNull
    private String fpt1GateProcedureId;

    @NotNull
    private String fpt1GateAgencyId;

    @NotNull
    private String applicantFullName;

    @NotNull
    private String applicantGender;

    @NotNull
    private String applicantIDNumber;

    @NotNull
    private String applicantAdress;

    private String applicantPhoneNumber = "";

    private String applicantEmail = "";

    @NotNull
    private String applicantDistrictId;

    @NotNull
    private String applicantVillageId;

    @NotNull
    private String procedureLevel;

    @NotNull
    private String registerDate;

    private List<DossierFormFileDto> dossierFormFile = new ArrayList<>();
}

