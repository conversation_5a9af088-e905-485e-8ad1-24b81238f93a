package vn.vnpt.digo.adapter.dto.event_log;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.dto.NameValueDto;
import vn.vnpt.digo.adapter.dto.SidNameDto;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class EventLogRequest implements Serializable {
    private String method;
    private String uri;
    private Object body;
    private List<NameValueDto> header;
}
