/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import vn.vnpt.digo.adapter.pojo.IportalAgency;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetIportalCategoryDto implements Serializable {
    
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private String iportalCategoryId;

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId categoryId;
}
