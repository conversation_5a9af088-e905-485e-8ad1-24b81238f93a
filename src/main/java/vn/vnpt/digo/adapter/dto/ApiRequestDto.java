package vn.vnpt.digo.adapter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;
import vn.vnpt.digo.adapter.pojo.KeyValue;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiRequestDto {
    private String feature;
    private String url;
    private String clientId;
    private String clientSecret;
    private String method;
    private Object body;
    private String bodyType;
    private String authorType;
    private String responseKey;
    private List<KeyValue> authorParams;
    private List<KeyValue> headerParams;
    private List<KeyValue> bodyParams;
    private List<KeyValue> urlParams;
    private String extend;
}
