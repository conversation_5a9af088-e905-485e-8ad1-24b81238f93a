package vn.vnpt.digo.adapter.dto.zalo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZaloSendMessageKTMDto implements Serializable {
    private String event_name;
    private String app_id;
    public Sender sender;
    private Recipient recipient;
    public Message message;
    public String timestamp;
    private String user_id_by_app;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class Sender implements Serializable {
        public String id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class Recipient implements Serializable {
        private String id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class Message implements Serializable {
        public String text;
        private String msg_id;

    }
}
