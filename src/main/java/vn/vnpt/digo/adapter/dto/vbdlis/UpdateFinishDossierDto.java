/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.dto.vbdlis;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.pojo.vbdlis.DanhSachGiayTo;
import vn.vnpt.digo.adapter.pojo.vbdlis.ThongTinNguoiNopDon;
import vn.vnpt.digo.adapter.pojo.vbdlis.ThongTinQuyTrinh;
import vn.vnpt.digo.adapter.pojo.vbdlis.ThongTinThuaDat;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateFinishDossierDto implements Serializable {

    @JsonProperty("LinkFileVbdlis")
    private String linkFileVbdlis;

    @JsonProperty("ConfigId")
    private ObjectId configId;

    @JsonProperty("AgencyId")
    private ObjectId agencyId;

    @JsonProperty("SubsystemId")
    private ObjectId subsystemId;

    @NotNull
    @JsonProperty("SoBienNhan")
    public String soBienNhan;

    @NotNull
    @JsonProperty("NgayTra")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    public Date ngayTra;

}
