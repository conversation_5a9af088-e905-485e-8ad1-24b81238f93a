/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.gtvt;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PadPApplyGTVTDto implements Serializable{
    private ProcedureDto procedure; //bd/procedure/{id}
    private RecvKindDto dossierReceivingKind = new RecvKindDto(); //static code
    private List<AttachmentDto> attachment;
    private ApplicantDto applicant; ///ba/place/{id}/--address
    private AgencyDto agency; ///ba/agency/{id}/name+code+parent+ancestor/--fully
    @JsonProperty("eForm")
    private FormDto eForm;
    private SyncDto sync;
    private ProcessDto procedureProcessDefinition;
    private RecvKindDto dossierMenuTaskRemind;
    private String code;
    private IdNameDto procedureLevel;
    private ApplyMethod ApplyMethod;
    private int dossierStatus = 0;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date acceptedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date returnedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;
    private DossierExtend extendHCM;
    private DossierTaskStatus dossierTaskStatus;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierTaskStatus implements Serializable{

        @Field("id")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        private ArrayList<TranslateName> name;

        public DossierTaskStatus (String id, String nameVi) {
            this.id = new ObjectId(id);
            ArrayList<TranslateName> nameList = new ArrayList();
            TranslateName translateName = new TranslateName();
            translateName.setLanguageId((short) 228);
            translateName.setName(nameVi);
            nameList.add(translateName);
            this.name = nameList;
        }

        public String getViStatus() {
            String status = "";
            try {
                if (this.name != null && !this.name.isEmpty()) {
                    for (TranslateName translateName : name) {
                        if (translateName.getLanguageId().equals((short) 228)) {
                            status = translateName.getName();
                            break;
                        }
                    }
                }
            } catch (Exception e) {
            }
            return status;
        }
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierExtend {
        private OtherInForGTVT otherInForGTVT = null;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplyMethod implements Serializable {
        private String id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplicantDto implements Serializable {
        private String eformId;
        private  ApplicantDataDto data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplicantDataDto implements Serializable {
        private String id;
        private String fullname;
        private IdentityDto identity;
        //        private AddressDto address;
        private String address;
        private AddressDetailDto province;
        private AddressDetailDto district;
        private AddressDetailDto village;
        private String registerNumber;
        private String taxCode;
        private String contactPerson;
        private String promotionName;
        private String phone;
        private String fax;
        private String email;
        private String phoneNumber;
        private String area = "";
        private String promotionMethod;
        private Integer productCount;
        private String customer;
        private String product;
        private String startDate;
        private String endDate;
        private String structure;
        private String details;
        private String totalValue;
        private String bussinessmans;
        private Integer chonDoiTuong;
        private Integer hinhThucNop;
        private String ownerFullname;
        private String identityNumber;
        private ArrayList<PostDossierFee> dossierFee;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressDto implements Serializable {

        private String address;
        private PlaceDto place;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressDetailDto implements Serializable {

        private String label;
        private String value;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PlaceDto implements Serializable {

        private String id;
        private String name;
        private IdNameTypeDto parent;
        private IdNameDto type;
        private List<IdNameTypeDto> ancestors;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IdentityDto implements Serializable {

        private String number;
        private IdNameDto agency;
        private String date;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdNameTypeDto implements Serializable {

        private String id;
        private String name;
        private IdNameDto type;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdNameDto implements Serializable {

        private String id;
        private List<NameDto> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdNameCodeDto implements Serializable {

        private String id;
        private String code;
        private List<NameDto> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcedureDtoTemp implements Serializable {
        private String id;
        private String code;
        private List<NameDto> translate;
        private IdNameCodeDto sector;
        private IdNameDto level;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcedureDto implements Serializable {

        private String id;
        private String code;
        private List<NameDto> translate;
        private IdNameCodeDto sector;
        private ArrayList<Tag> agency = new ArrayList<>();
        private Integer processDefinitionCount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecvKindDto implements Serializable {

        private String id = "5f8968888fffa53e4c073ded";
        private List<NameDto> name = new ArrayList<>() {
            {
                add(new NameDto((short) 46, "Receive directly"));
                add(new NameDto((short) 228, "Nhận trực tiếp"));
            }
        };
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttachmentDto implements Serializable {

        private String id;
        private String filename;
        private Integer size;
        private String group;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgencyDto implements Serializable {

        private String id;
        private String code;
        private ArrayList<TranslateName> name;

        private PadPApplyGTVTDto.AgencyDto parent;
//        private List<AgencyDto> ancestors;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NameDto implements Serializable {

        private Short languageId;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormDto implements Serializable {

        private String id;
        private FormDataDto data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FormDataDto implements Serializable {
        private String dateApply;
        private String registerNumber;
        private String taxCode;
        private String contactPerson;
        private String promotionName;
        private String phone;
        private String area = "";
        private String promotionMethod;
        private Integer productCount;
        private String customer;
        private String product;
        private String startDate;
        private String endDate;
        private String structure;
        private String details;
        private String totalValue;
        private String bussinessmans;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncDto implements Serializable {

        private Integer typeId = 1;
        private String sourceCode;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncWithNoteDto implements Serializable {

        private Integer typeId = 1;
        private String sourceCode;
        private String note;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessDto implements Serializable {

        private String id;
        private Object processDefinition;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Tag implements Serializable{

        @Field("id")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        @Field("code")
        private String code;

        @Field("name")
        private ArrayList<TranslateName> name;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @org.codehaus.jackson.annotate.JsonIgnoreProperties(ignoreUnknown = true)
    public static class TranslateName implements Serializable{

        private Short languageId;

        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class PostDossierFee  implements Serializable{

        private Procost procost;

        private Double amount;

        private int quantity;

        private Double paid;

        private int required;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class Procost implements Serializable{

        @Field("id")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        private Type type;

        private ArrayList<TranslateName> description;

        private IdName monetaryUnit;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class Type implements Serializable {

        @Field("id")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        private int type;

        private ArrayList<TranslateName> name;

        private int quantityEditable;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class IdName implements Serializable{

        @Field("id")
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;

        private String name;

        private String code;

        public IdName(ObjectId id, String code) {
            this.id = id;
            this.code = code;
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocsSubmitDto implements Serializable {

        @JsonProperty("TenTepDinhKem")
        private String TenTepDinhKem;

        @JsonIgnore
        @JsonProperty("Base64")
        private String Base64;
    }
}
