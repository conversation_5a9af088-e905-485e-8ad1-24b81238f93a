package vn.vnpt.digo.adapter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgencyAddressPlaceDto implements Serializable {
    private String id;
    private Address address;
    private Id level;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Id implements Serializable {
        private String id;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Address implements Serializable {
        private Place place;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Place implements Serializable {
        private String id;
        private List<Id> ancestor;
    }
}
