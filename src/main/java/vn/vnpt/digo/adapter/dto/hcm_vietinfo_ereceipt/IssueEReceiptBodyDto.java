package vn.vnpt.digo.adapter.dto.hcm_vietinfo_ereceipt;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.dto.ereceipt.PostDossierReceiptDto;
import vn.vnpt.digo.adapter.dto.hcm.extendHCM;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class IssueEReceiptBodyDto implements Serializable {
    private ObjectId configId;

    private PostDossierReceiptDto dossierReceipt;

    private VietInfoReceipt vietInfoReceipt;

    private vn.vnpt.digo.adapter.dto.hcm.extendHCM extendHCM;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VietInfoReceipt implements Serializable{
//        private String appName1;
        @JsonProperty("AppName")
        private String appName;

        @JsonProperty("PartnerCode")
        private String partnerCode;

        @JsonProperty("SoPhieuBao")
        private String formNumber;

        @JsonProperty("MaBienLai")
        private String receiptCode;

        @JsonProperty("MaLinhVuc")
        private String sectorCode;

        @JsonProperty("TenLinhVuc")
        private String sectorName;

        @JsonProperty("MaTTHC")
        private String procedureCode;

        @JsonProperty("TenTTHC")
        private String procedureName;

        @JsonProperty("MaHoSo")
        private String dossierCode;

        @JsonProperty("CanBoXuLy")
        private String staffHandle;

        @JsonProperty("TrangThaiHoSo")
        private String dossierStatus;

        @JsonProperty("HoTenNguoiNop")
        private String applicantFullName;

        @JsonProperty("DiaChiNguoiNop")
        private String applicantAddress;

        @JsonProperty("SDTNguoiNop")
        private String applicantPhone;

        @JsonProperty("EmailNguoiNop")
        private String applicantEmail;

        @JsonProperty("HinhThucThanhToan")
        private String paymentMethod;
        
        @JsonProperty("email")
        private String email;
    }
}
