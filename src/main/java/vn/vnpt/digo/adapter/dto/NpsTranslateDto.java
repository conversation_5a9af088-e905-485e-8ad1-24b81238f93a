package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NpsTranslateDto implements Serializable {

    private Integer languageId = 228;

    private String name;

    private String results;

    private String requirement;

    private String legalGrounds;

    private String steps;

    private String implementMethod;

    private String implementer;

    private String dossierComponent;

    private String agencyAccept;

    public void addResult(String results) {
        if (Objects.isNull(this.results)) {
            this.results = "";
            this.results += results;
        } else {
            this.results += "\n" + results;
        }
    }

    public void addDossierComponent(String dossierComponent, int index) {
        if (Objects.isNull(this.dossierComponent)) {
            this.dossierComponent = "";
            this.dossierComponent += "<p>"+ index + ". " + dossierComponent + "</p>";
        } else {
            this.dossierComponent += "<p>"+ index + ". " + dossierComponent + "</p>";
        }
    }

    public void addLegalGrounds(String legalGrounds) {
        if (Objects.isNull(this.legalGrounds)) {
            this.legalGrounds = "";
            this.legalGrounds += legalGrounds;
        } else {
            this.legalGrounds += "\n" + legalGrounds;
        }
    }
    public void addLegalGroundsV2(List<NpsLegalGroundDto> legalGrounds) {
        this.legalGrounds = "";
        if(legalGrounds != null && !legalGrounds.isEmpty()) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyy");
            StringBuilder builder = new StringBuilder();
            builder.append("<table class=\"procedure-table-data\" style=\"width: 100%;\">" +
                    "            <thead>" +
                    "             <tr>" +
                    "              <th style=\"width: 20%;\">Số ký hiệu</th>" +
                    "              <th style=\"width: 45%;\">Trích yếu</th>" +
                    "              <th style=\"width: 10%;\">Ngày ban hành</th>" +
                    "              <th style=\"width: 25%;\">Cơ quan ban hành</th>" +
                    "             </tr> \n" +
                    "            </thead> ")
                    .append("<tbody>");;
            for(NpsLegalGroundDto legalGroundDto : legalGrounds) {
                builder.append("<tr>");
                builder.append("<td>").append(legalGroundDto.getCode()).append("</td>");
                builder.append("<td>").append(legalGroundDto.getName()).append("</td>");
                String date = "";
                try {
                    date = sdf.format(legalGroundDto.getIssuedDate());
                } catch (Exception e) {
                    date = "";
                }
                builder.append("<td>").append(date).append("</td>");
                builder.append("<td>").append(legalGroundDto.getIssuedAgency()).append("</td>");
                builder.append("</tr>");
            }
            builder.append("</tbody></table>");
            this.legalGrounds = builder.toString();
        }
    }

    public void addImplementMethod(String implementMethod) {
        if (Objects.isNull(this.implementMethod)) {
            this.implementMethod = "";
            this.implementMethod += implementMethod;
        } else {
            this.implementMethod += "\n" + implementMethod;
        }
    }
    
    public void addAgencyAccept(String agencyAccept) {
        if (Objects.isNull(this.agencyAccept)) {
            this.agencyAccept = "";
            this.agencyAccept += agencyAccept;
        } else {
            this.agencyAccept += "\n" + agencyAccept;
        }
    }

    public void addStep(String step) {
        if (Objects.isNull(steps)) {
            steps = "";
            steps += "<p>" + step + "</p>";
        } else {
            steps += "<p>" + step + "</p>";
        }
    }
}
