package vn.vnpt.digo.adapter.dto.bdg.moc;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MocApplicantDto {
    @JsonProperty("HoTenNguoiNop")
    private String fullName;

    @JsonProperty("SoCMND")
    private String identity;

    @JsonProperty("EmailNguoiNop")
    private String email;

    @JsonProperty("SoDienThoaiNguoiNop")
    private String phone;

    @JsonProperty("DiaChiThuongTruNguoiNop")
    private String address;
}
