package vn.vnpt.digo.adapter.dto.minhtue.civilstatus;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(
        name = "hoso"
)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MarriedSttDossierDto implements Serializable {
    private Long id;
    private String so;
    private String quyenSo;
    private String trangSo;
    private String ngayDangKy;
    private String noiCap;
    private String nguoiKy;
    private String chucVuNguoiKy;
    private String nguoiThucHien;
    private String ghiChu;
    private String nxnHoTen;
    private String nxnGioiTinh;
    private String nxnNgaySinh;
    private Long nxnDanToc;
    private String nxnDanTocKhac;
    private String nxnQuocTich;
    private String[] nxnQuocTichKhac;
    public String[] getNxnQuocTichKhac() {
        String[] temp = nxnQuocTichKhac;
        if (nxnQuocTichKhac.length > 0) {
            temp = new String[]{"<![CDATA['"};
            for (int i = 0; i < nxnQuocTichKhac.length; i++) {
                temp[0] += nxnQuocTichKhac[i];
                if (nxnQuocTichKhac.length - 1 != i) {
                    temp[0] += ",";
                } else {
                    temp[0] += "']]>";
                }
            }
        }
        return temp;
    }
    private Long nxnLoaiCuTru;
    private String nxnNoiCuTru;
    private Long nxnLoaiGiayToTuyThan;
    private String nxnGiayToKhac;
    private String nxnSoGiayToTuyThan;
    private String nxnNgayCapGiayToTuyThan;
    private String nxnNoiCapGiayToTuyThan;
    private String nxnThoiGianCuTruTai;
    private String nxnThoiGianCuTruTu;
    private String nxnThoiGianCuTruDen;
    private String nxnTinhTrangHonNhan;
    private Long nxnLoaiMucDichSuDung;
    private String nxnMucDichSuDung;
    private String nycHoTen;
    private String nycQuanHe;
    private Long nycLoaiGiayToTuyThan;
    private String nycGiayToKhac;
    private String nycSoGiayToTuyThan;
    private String nycNgayCapGiayToKhac;
    private String nycNoiCapGiayToKhac;
}
