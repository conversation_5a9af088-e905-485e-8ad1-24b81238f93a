/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VNPTPayInitDto implements Serializable {
    @JsonProperty("action")
    private String action;
    @JsonProperty("paymentDomain")
    private String paymentDomain;
    @JsonProperty("version")
    private String version;
    @JsonProperty("merchantServiceId")
    private String merchantServiceId;
    @NotNull
    @JsonProperty("secretCode")
    private String secretCode;
    @NotNull
    @JsonProperty("apiKey")
    private String apiKey;

}
