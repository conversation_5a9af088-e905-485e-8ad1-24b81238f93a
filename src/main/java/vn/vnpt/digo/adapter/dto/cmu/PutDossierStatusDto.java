package vn.vnpt.digo.adapter.dto.cmu;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.gtvt.PadPApplyGTVTDto;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PutDossierStatusDto implements Serializable {

    private ObjectId dossierId;

    private int dossierStatus;

    private String comment;

    private PadPApplyGTVTDto.DossierTaskStatus dossierTaskStatus;

    private PadPApplyGTVTDto.DossierTaskStatus dossierMenuTaskRemind;


}
