package vn.vnpt.digo.adapter.dto.hgg.gplx_bgtvt;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GplxTraCuuHoSoDataResponseDto implements Serializable {
    @JsonProperty("MaTTHC")
    private String MaTTHC;
    @JsonProperty("MaDVC")
    private String MaDVC;
    @JsonProperty("TenDVC")
    private String TenDVC;
    @JsonProperty("MaHoSo")
    private String MaHoSo;
    @JsonProperty("HoTenNguoiNop")
    private String HoTenNguoiNop;
    @JsonProperty("MaTrangThai")
    private String MaTrangThai;
    @JsonProperty("TrangThaiHoSo")
    private String TrangThaiHoSo;
    @JsonProperty("DonViXuLy")
    private String DonViXuLy;
    @JsonProperty("MaDonViXuLy")
    private String MaDonViXuLy;
    @JsonProperty("NgayNop")
    private String NgayNop;
    @JsonProperty("NgayTiepNhan")
    private String NgayTiepNhan;
    @JsonProperty("NgayHenTra")
    private String NgayHenTra;
    @JsonProperty("HinhThucNhanKQ")
    private String HinhThucNhanKQ;
}
