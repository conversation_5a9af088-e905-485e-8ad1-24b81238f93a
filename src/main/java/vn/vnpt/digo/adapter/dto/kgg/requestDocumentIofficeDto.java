package vn.vnpt.digo.adapter.dto.kgg;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Array;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class requestDocumentIofficeDto {

    private String tenHoso;
    private String soKyhieu;
    private String maHoSo;
    private String coquanBanhanh;
    private ArrayList<filesDinhKemDto> filesDinhKem;
    private ArrayList<filesDinhKemDto> filesLienquan;
    private String token;
}
