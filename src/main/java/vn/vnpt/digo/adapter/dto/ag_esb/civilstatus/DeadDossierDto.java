package vn.vnpt.digo.adapter.dto.ag_esb.civilstatus;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@XmlRootElement(
        name = "hoso"
)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DeadDossierDto implements Serializable {
    private Long id;
    private String so;
    private String quyenSo;
    private String trangSo;
    private String ngayDangKy;
    private Long loaiDangKy;
    private String noiDangKy;
    private String nguoiKy;
    private String chucVuNguoiKy;
    private String nguoiThucHien;
    private String ghiChu;
    private String nktHoTen;
    private String nktGioiTinh;
    private String nktNgaySinh;
    private Long nktDanToc;
    private String nktDanTocKhac;
    private String nktQuocTich;
    private String[] nktQuocTichKhac;
    public String[] getNktQuocTichKhac() {
        String[] temp = nktQuocTichKhac;
        if (nktQuocTichKhac.length > 0) {
            temp = new String[]{"<![CDATA['"};
            for (int i = 0; i < nktQuocTichKhac.length; i++) {
                temp[0] += nktQuocTichKhac[i];
                if (nktQuocTichKhac.length - 1 != i) {
                    temp[0] += ",";
                } else {
                    temp[0] += "']]>";
                }
            }
        }
        return temp;
    }
    private Long nktLoaiCuTru;
    private String nktNoiCuTru;
    private Long nktLoaiGiayToTuyThan;
    private String nktGiayToKhac;
    private String nktSoGiayToTuyThan;
    private String nktNgayCapGiayToTuyThan;
    private String nktNoiCapGiayToTuyThan;
    private String nktNgayChet;
    private String nktGioPhutChet;
    private String nktNoiChet;
    private String nktNguyenNhanChet;
    private Long nktTinhTrangTuyenBoViecChet;
    private String nktNgayGhiChuTuyenBoViecChet;
    private String nktCanCuTuyenBoViecChet;
    private String nktNgayGhiChuHuyTuyenBoViecChet;
    private String nktCanCuHuyTuyenBoViecChet;
    private Long gbtLoai;
    private String gbtSo;
    private String gbtNgay;
    private String gbtCoQuanCap;
    private String nycHoTen;
    private String nycQuanHe;
    private Long nycLoaiGiayToTuyThan;
    private String nycGiayToKhac;
    private String nycSoGiayToTuyThan;
    private String nycNgayCapGiayToTuyThan;
    private String nycNoiCapGiayToTuyThan;
    private String soDangKyNuocNgoai;
    private String ngayDangKyNuocNgoai;
    private String cqNuocNgoaiDaDangKy;
    private String qgNuocNgoaiDaDangKy;
}
