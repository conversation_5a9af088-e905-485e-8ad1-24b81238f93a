package vn.vnpt.digo.adapter.dto.hbh.moc;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HbhEFormDto {
    @JsonProperty("ThongTinVeChuDauTu")
    private ChuDauTuDto chuDauTu;
    @JsonProperty("ThongTinCongTrinh")
    private CongTrinhDto congTrinh;
    @JsonProperty("ThoiGianHoanThanh")
    private String endDateConstruction;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ChuDauTuDto {
        @JsonProperty("TenChuDauTu")
        private String fullNameInvestor;
        @JsonProperty("NguoiDaiDien")
        private String legalRepresentativeName;
        @JsonProperty("ChucVu")
        private String positionInvestor;
        @JsonProperty("SoDienThoai")
        private String phoneInvestor;
        @JsonProperty("MaTinhThanh")
        private SelectFormIO provinceInvestor;
        @JsonProperty("MaQuanHuyen")
        private SelectFormIO districtInvestor;
        @JsonProperty("MaPhuongXa")
        private SelectFormIO wardInvestor;
        @JsonProperty("SoNhaDuongPho")
        private String streetInvestor;
        @JsonProperty("DiaChiLienHe")
        private String contactAddressInvestor;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CongTrinhDto {
        @JsonProperty("DiaDiemXayDung")
        private String locationConstruction;
        @JsonProperty("LoDatSo")
        private String serialLandConstruction;
        @JsonProperty("DienTich")
        private String acreageConstruction;
        @JsonProperty("MaTinhThanh")
        private SelectFormIO provinceConstruction;
        @JsonProperty("MaQuanHuyen")
        private SelectFormIO districtConstruction;
        @JsonProperty("MaPhuongXa")
        private SelectFormIO wardConstruction;
        @JsonProperty("SoNhaDuongPho")
        private String streetConstruction;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SelectFormIO implements Serializable {
        private String value;
        private String label;
    }
}
