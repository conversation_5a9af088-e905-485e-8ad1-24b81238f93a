package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import vn.vnpt.digo.adapter.pojo.AccountOTP;
import vn.vnpt.digo.adapter.pojo.Username;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountOauth2Dto implements Serializable {

    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    private String password;
    
    private Boolean enable = true;  
    
    private UserDto user;
    
    @JsonProperty("username")
    private List<Username> username;
    
    private AccountOTP otp;
    
    private List<PermissionAttrDto> permissions = new ArrayList<>();
    
    private RoleOidcDto realm_access = new RoleOidcDto();
    
    private List<String> groups = new ArrayList<>();
    
    public void setHashPassword(String password){
        String hash = new BCryptPasswordEncoder().encode(password);;
        this.password = hash;
    }
}
