package vn.vnpt.digo.adapter.dto.lgspqni;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatusCriminalJudicalResponseDto implements Serializable{
    private String status;
    private String description;
    private String decStatusId;
    private String decStatusName;
    private String approveDate;
    private String issueDate;

    private String dossierId;

    public StatusCriminalJudicalResponseDto(StatusCriminalJudicalResponseDto statusCriminalJudicalResponseDto, String dossierId){
        this.status = statusCriminalJudicalResponseDto.getStatus();
        this.description = statusCriminalJudicalResponseDto.getDescription();
        this.decStatusName = statusCriminalJudicalResponseDto.getDecStatusName();
        this.approveDate = statusCriminalJudicalResponseDto.getApproveDate();
        this.issueDate = statusCriminalJudicalResponseDto.getIssueDate();
        this.decStatusId = statusCriminalJudicalResponseDto.getDecStatusId();
        this.dossierId = dossierId;
    }
}
