/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.io.Serializable;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPDossierDetailDto;
import vn.vnpt.digo.adapter.dto.lgsphcm.HCMLGSPDossierDetailDto.ProcedureAdministrationWithAssignedCode;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDetailDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierFeeDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierSyncReqDto;
import vn.vnpt.digo.adapter.dto.qnm.einvoice.viettel.PaymentsDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.dto.utils.ProcedureDetailDto;
import vn.vnpt.digo.adapter.dto.utils.SectorDetailDto;
import vn.vnpt.digo.adapter.dto.utils.UserInfoDto;
import vn.vnpt.digo.adapter.service.UtilService;
import vn.vnpt.digo.adapter.util.ListHelper;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HCMLGSPDossierSyncReqDto implements Serializable {
    private String RequestId;
    private String MaDonVi;
    private String MaHoSo;
    private String MaDinhDanhTTCDS;
    private String MaThuTuc;
    private String TenThuTuc;
    private String MaLinhVuc;
    private String TenLinhVuc;
    private String TenNguoiNop;
    private String LoaiGoiTin;
    private String Data;

    public HCMLGSPDossierSyncReqDto(String madonvi, DossierData data, String requestId, String hcmMDDCDT) throws JsonProcessingException {
        this.RequestId = requestId;
        this.MaDonVi = madonvi;
        this.MaHoSo = data.getCode();
        if (!hcmMDDCDT.equalsIgnoreCase("")) {
            this.MaDinhDanhTTCDS = hcmMDDCDT;
        }
        this.MaThuTuc = data.getProcedureCode();
        this.TenThuTuc = data.getProcedureName();
        this.MaLinhVuc = data.getSectorCode();
        this.TenLinhVuc = data.getSectorName();
        this.TenNguoiNop = data.getApplicantName();
        this.LoaiGoiTin = data.getPackageType();
        ObjectMapper mapper = new ObjectMapper();
        System.out.println("dossier "+data.getCode());
        System.out.println("HCMLGSPDossierSyncReqDto ");
        String jsonBody = mapper.writeValueAsString(data);
        System.out.println(jsonBody);
        String dataBase64;
        dataBase64 = Base64.getEncoder().encodeToString(jsonBody.getBytes());
        this.Data = dataBase64;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierData implements Serializable {

        @JsonProperty("MaDinhDanhHS")
        private String id;

        @JsonProperty("MaHoSo")
        private String code;
        
        @JsonProperty("MaHoSoQuocGia")
        private String nationCode;
        
        @JsonProperty("MaTTHC")
        private String procedureCode; 
        @JsonProperty("TenTTHC")
        private String procedureName;

        @JsonProperty("MaLinhVuc")
        private String sectorCode; 
        
        @JsonProperty("TenLinhVuc")
        private String sectorName;
        
        @JsonProperty("LoaiGoiTin")
        private String packageType; //Loại gói tin: 1: Gói tin mới; 2: Gói tin cập nhật (trường hợp bổ sung, thực hiện nghĩa vụ tài chính); 3: Gói tin rút hồ sơ

        @JsonProperty("KenhThucHien")
        private String receptionMethod = "1"; //Kênh nộp hồ sơ bao gồm: 1: Trực tiếp; 2: Nộp trực tuyến; 3: Nộp qua bưu chính công ích
        
        @JsonProperty("ChuHoSo")
        private String applicantName;
        @JsonProperty("LoaiDoiTuong")
        private String applicantType = "1"; //Kiểu đối tượng nộp hồ sơ 1: Công dân; 2: Doanh nghiệp
        @JsonProperty("MaDoiTuong")
        private String applicantCode = ""; 

        @JsonProperty("ThongTinKhac")
        private String applicantOtherInfo = "";

        @JsonProperty("Email")
        private String applicantEmail;
        @JsonProperty("Fax")
        private String applicantFax;
        @JsonProperty("SoDienThoai")
        private String applicantPhoneNumber;
        
        @JsonProperty("TrichYeuHoSo")
        private String dossierTitle;
        
        @JsonProperty("NgayTiepNhan")
        private String receptedDate;
        
        @JsonProperty("NgayHenTra")
        private String appointmentDate;

        @JsonProperty("TrangThaiHoSo")
        private String status = "1";

        @JsonProperty("NgayTra")
        private String completedDate;
        
        @JsonProperty("ThongTinTra")
        private Boolean completedInformation;

        @JsonProperty("HinhThuc")
        private String returnedMethod; //Hình thức trả kết quả: 0: Trả kết quả tại bộ phận tiếp nhận và trả kết quả; 1: Trả kết quả qua đường bưu điện
        
        @JsonProperty("NgayKetThucXuLy")
        private String endDate;
        
        @JsonProperty("DonViXuLy")
        private String handlingAgency;

        @JsonProperty("GhiChu")
        private String note;
        
        @JsonProperty("TaiLieuNop")
        private List<AttachmentDto> attachment;
        
        @JsonProperty("DanhSachLePhi")
        private List<FeeDto> fees;
        
        @JsonProperty("DanhSachTepDinhKemKhac")
        private List<AttachmentOtherDto> attachmentOther;
        
        @JsonProperty("DanhSachHoSoBoSung")
        private List<AdditionalDossierDto> additionalDossier;
        
        @JsonProperty("DanhSachGiayToKetQua")
        private List<ResultDto> result;

        @JsonProperty("NoiNopHoSo")
        private String submissionPlace;
        
        @JsonProperty("NguoiXuLy")
        private String assignee;
        
        @JsonProperty("CongViec")
        private String definitionTask;
        
        @JsonProperty("CoQuanDoanhNghiep")
        private String organization;
        
        @JsonProperty("MaSoDoanhNghiep")
        private String taxCode;
        
        @JsonProperty("TinhThanhDoanhNghiep")
        private String province1;
        
        @JsonProperty("QuanHuyenDoanhNghiep")
        private String district1;
        
        @JsonProperty("PhuongXaDoanhNghiep")
        private String village1;
        
        @JsonProperty("DiaChiDoanhNghiep")
        private String address1;
        
        @JsonProperty("TinhThanhNguoiNop")
        private String province;
        
        @JsonProperty("QuanHuyenNguoiNop")
        private String district;
        
        @JsonProperty("PhuongXaNguoiNop")
        private String village;
        
        @JsonProperty("DiaChiNguoiNop")
        private String address;
        
        @JsonProperty("ThongTinEform")
        private Object eForm;
        
        @JsonProperty("DanhSachChungChi")
        private List<CertificateDto> certificateList;
        
        @JsonProperty("TaiKhoanNguoiXuLy")
        private String assigneeAccount;
        
        @JsonProperty("ChucVuNguoiXuLy")
        private String assigneePosition;
        
        @JsonProperty("ChuyenXuly")
        private Boolean statusPrPdossier = false;

    public DossierData(HCMLGSPDossierDetailDto dossier, List<NpsDossierFeeDto> fees,
            Integer status, UserInfoDto user, ProcedureDetailDto procedure, SectorDetailDto sector, IdCodeNameDto receivingKind,
            IdCodeNameSimpleDto agency, String techId, String filepath, String packageType, IntegratedConfigurationDto config, UtilService utilService) {
        Logger logger = LoggerFactory.getLogger(HCMLGSPDossierSyncReqDto.class);
        try {
            // dossier info
            this.id = dossier.getId().toHexString();
            this.code = dossier.getCode();
            this.nationCode = dossier.getNationCode();

            this.procedureCode = procedure.getCode();
            this.procedureName = procedure.getName();

            this.sectorCode = sector.getCode();
            this.sectorName = procedure.getSector().getName();
            
            this.packageType = packageType;
            
            this.receptionMethod = dossier.getApplyMethod().getId().toString();

            this.applicantName = dossier.getApplicant().getData().getFullname();
            this.applicantType = "1";
            if (user != null && user.getType() != null && user.getType() != 3) {
                this.applicantType = user.getType().toString();
            }
            this.applicantCode = techId;

            this.applicantEmail = dossier.getApplicant().getData().getEmail();
            this.applicantFax = dossier.getApplicant().getData().getFax();
            this.applicantPhoneNumber = dossier.getApplicant().getData().getPhoneNumber();

            this.dossierTitle = "";
            
            this.status = status.toString();
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            if(Objects.nonNull(dossier.getAppliedDate())){
                this.receptedDate = df.format(dossier.getAcceptedDate());
            } else {
                this.receptedDate = "";
            }
            if(Objects.nonNull(dossier.getAppointmentDate())){
                this.appointmentDate = df.format(dossier.getAppointmentDate());
            } else {
                this.appointmentDate = "";
            }
            if(Objects.nonNull(dossier.getCompletedDate())){
                this.completedDate = df.format(dossier.getCompletedDate());
            } else {
                this.completedDate = "";
            }
            
            this.completedInformation = false;
           
            try {
                Integer kind = Integer.parseInt(receivingKind.getCode());
                kind = kind - 1000;
                this.returnedMethod = kind.toString();
            } catch (Exception e) {
                this.returnedMethod = "0";
            }
            
            //Mapping ng?y k?t th?c 25/06
            if(Objects.nonNull(dossier.getCompletedDate())){
                this.endDate = df.format(dossier.getCompletedDate());
            } else {
                this.endDate = "";
            }
            
            this.handlingAgency = agency.getName();
//            this.dossierDigitizing = "0";
//            this.accountAuthenticatedWithVNeID = "1";
//            this.onlinePayment = "2";
//            this.submissionDate = this.receptedDate;

            this.note = "";
            this.submissionPlace = "Nộp từ cổng DVC BNĐP";
            // attchment info
            try{
                List<AttachmentDto> lstAttchment = new ArrayList<>();
                List<HCMLGSPDossierDetailDto.FormFile> formFileListDto = dossier.getDossierFormFile();
                if (formFileListDto != null && !formFileListDto.isEmpty()) {
                    for (HCMLGSPDossierDetailDto.FormFile formFile : formFileListDto) {
                        if (formFile.getFile() != null && !formFile.getFile().isEmpty()) {
                            IdCodeDto info = utilService.getFormDetail(config, formFile.getProcedureFormId());
                            for (HCMLGSPDossierDetailDto.AttachmentDto i : formFile.getFile()) {
                                try{
                                    AttachmentDto file = new AttachmentDto(i.getId().toHexString(), i.getFilename(), filepath + i.getId().toHexString() + "&uuid=" +i.getUuid().toString(), false, formFile.getProcedureFormId().toHexString(), info.getCode(), formFile.getFormName());
                                    lstAttchment.add(file);
                                }catch (Exception e){
                                    AttachmentDto file = new AttachmentDto(i.getId().toHexString(), i.getFilename(), filepath + i.getId().toHexString(), false, formFile.getProcedureFormId().toHexString(), info.getCode(), formFile.getFormName());
                                    lstAttchment.add(file);
                                }
                            }
                        }
                    }
                }
                this.attachment = lstAttchment;
            }catch(Exception e)
            {
                 this.attachment = null;
            }

            // fee info
            List<FeeDto> lstFee = new ArrayList<>();
            
            fees.forEach(item -> {
                String paymentMethodNameValue = "";
                String statusPaid = "1"; //trạng thái thanh toán: 1: Chưa thanh toán (mặc định); 2: Đã thanh toán
                if(Objects.nonNull(dossier.getPaymentMethod()) && Objects.nonNull(dossier.getPaymentMethod().getName())){
                    paymentMethodNameValue = dossier.getPaymentMethod().getName();
                }else{
                    paymentMethodNameValue = "Trực tiếp";
                }
                if(item.getPaid().intValue() == (item.getAmount().intValue() * item.getQuantity().intValue())){
                    statusPaid = "2";
                }
                FeeDto f = new FeeDto(ListHelper.getName(item.getProcost().getType().getName()),
                        item.getId().toHexString(), 
                        "1",
                        paymentMethodNameValue, 
                        item.getAmount().intValue(),
                        String.valueOf(item.getProcost().getType().getType() + 1),
                        statusPaid
                );
                lstFee.add(f);
            });
            this.fees = lstFee;
            // result
            //ch?a mapping ???c code 25/06
            List<ResultDto> lstResult = new ArrayList<>();
            if(packageType.equals("4")){ //dong bo ket qua qua LGSP HCM
                if(Objects.nonNull(dossier.getAttachment())){
                    dossier.getAttachment().forEach(item -> {
                        if (Objects.equals(new ObjectId("5f9bd9692994dc687e68b5a6"), item.getGroup())) {
                            try{
                                ResultDto rs = new ResultDto(item.getFilename(),"", item.getId().toHexString(), filepath + item.getId().toHexString()+ "&uuid=" +item.getUuid().toString());
                                lstResult.add(rs);
                            }catch (Exception e){
                                ResultDto rs = new ResultDto(item.getFilename(),"", item.getId().toHexString(), filepath + item.getId().toHexString());
                                lstResult.add(rs);
                            }

                        }
                    });
                }
            }
            
            this.result = lstResult;
            
            //Danh s?ch t?p ??nh k?m kh?c
            List<AttachmentOtherDto> lstAttchmentOther = new ArrayList<>();
//            dossier.getDossierFormFile().forEach(item -> {
//                item.getFile().forEach(i -> {
//                    AttachmentOtherDto fileOther = new AttachmentOtherDto(i.getFilename(), "1","1");
//                    lstAttchmentOther.add(fileOther);
//                });
//            });
            this.attachmentOther = lstAttchmentOther;
            
            //Danh s?ch h? s? b? sung 26/06
            if(packageType.equals("2")){
                List<AttachmentDto> lstAttchmentAdditional = new ArrayList<>();
                List<AdditionalDossierDto> lstAdditionalDossier = new ArrayList<>();
                List<HCMLGSPDossierDetailDto.FormFile> formFileListDto2 = dossier.getDossierFormFile();
                if (formFileListDto2 != null && !formFileListDto2.isEmpty()) {
                    for (HCMLGSPDossierDetailDto.FormFile formFile : formFileListDto2) {
                        if (formFile.getFile() != null && !formFile.getFile().isEmpty()) {
                            IdCodeDto formInfo = utilService.getFormDetail(config, formFile.getProcedureFormId());
                            for (HCMLGSPDossierDetailDto.AttachmentDto i : formFile.getFile()) {
                                try{
                                    AttachmentDto file = new AttachmentDto(i.getId().toHexString(), i.getFilename(), filepath + i.getId().toHexString() + "&uuid=" +i.getUuid().toString(), false, formFile.getProcedureFormId().toHexString(), formInfo.getCode(), formFile.getFormName());
                                    lstAttchmentAdditional.add(file);
                                }catch (Exception e){
                                    AttachmentDto file = new AttachmentDto(i.getId().toHexString(), i.getFilename(), filepath + i.getId().toHexString(), false, formFile.getProcedureFormId().toHexString(), formInfo.getCode(), formFile.getFormName());
                                    lstAttchmentAdditional.add(file);
                                }
                            }
                        }
                    }
                }
                List<FeeDto> lstFeeAdditional = new ArrayList<>();
                fees.forEach(item -> {
                    String statusPaid = "1"; //trạng thái thanh toán: 1: Chưa thanh toán (mặc định); 2: Đã thanh toán
                    String paymentMethodNameValue = "";
                    if(item.getPaid().intValue() == (item.getAmount().intValue() * item.getQuantity().intValue())){
                        statusPaid = "2";
                    }
                    if(Objects.nonNull(dossier.getPaymentMethod()) && Objects.nonNull(dossier.getPaymentMethod().getName())){
                        paymentMethodNameValue = dossier.getPaymentMethod().getName();
                    }else{
                        paymentMethodNameValue = "Trực tiếp";
                    }
                    FeeDto f = new FeeDto(ListHelper.getName(item.getProcost().getType().getName()),
                            item.getId().toHexString(), 
                            "1", 
                            paymentMethodNameValue, 
                            item.getAmount().intValue(),
                            String.valueOf(item.getProcost().getType().getType() + 1),
                            statusPaid
                    );
                    lstFeeAdditional.add(f);
                });
                ObjectId id = new ObjectId();
                AdditionalDossierDto fileAdditional = new AdditionalDossierDto(id.toString(), "Người yêu cầu bổ sung", "Nội dung yêu cầu bổ sung", "", "", "", "", true, lstAttchmentAdditional, lstFeeAdditional, "", this.appointmentDate);
                lstAdditionalDossier.add(fileAdditional);
                this.additionalDossier = lstAdditionalDossier;
            }
            
            
            try{
                ObjectMapper mapper = new ObjectMapper();
                String jsonEFormData = mapper.writeValueAsString(dossier.getEForm().getData());
                //eform
                this.eForm = jsonEFormData;
            }catch(Exception e){
                  this.eForm = null;
            }

            if(dossier.getCurrentTask().size() > 0){ 
                //assignee
                if(Objects.nonNull(dossier.getCurrentTask()) && Objects.nonNull(dossier.getCurrentTask().get(0).getAssignee()) && Objects.nonNull(dossier.getCurrentTask().get(0).getAssignee().getFullname())){
                    this.assignee = dossier.getCurrentTask().get(0).getAssignee().getFullname();
                }
                //assignee position
                if(Objects.nonNull(dossier.getCurrentTask().get(0).getCandidatePosition()) && Objects.nonNull(dossier.getCurrentTask().get(0).getCandidatePosition().getName()) && Objects.nonNull(dossier.getCurrentTask().get(0).getCandidatePosition().getName().get(0).getName())){
                    this.assigneePosition = dossier.getCurrentTask().get(0).getCandidatePosition().getName().get(0).getName();
                }
                
                if(Objects.nonNull(dossier.getCurrentTask().get(0).getAssignee()) && Objects.nonNull(dossier.getCurrentTask().get(0).getAssignee().getAccount()) && Objects.nonNull(dossier.getCurrentTask().get(0).getAssignee().getAccount().getUsername().get(0).getValue())){
                    this.assigneeAccount = dossier.getCurrentTask().get(0).getAssignee().getAccount().getUsername().get(0).getValue();
                }

                //definitionTask
                if(Objects.nonNull(dossier.getCurrentTask().get(0).getBpmProcessDefinitionTask()) && Objects.nonNull(dossier.getCurrentTask().get(0).getBpmProcessDefinitionTask().getDefinitionTask()) && Objects.nonNull(dossier.getCurrentTask().get(0).getBpmProcessDefinitionTask().getDefinitionTask().getName())){
                    this.definitionTask = dossier.getCurrentTask().get(0).getBpmProcessDefinitionTask().getDefinitionTask().getName();
                }
            }
            
            
            //organization
            if(Objects.nonNull(dossier.getApplicant().getData().getOrganization())){
                this.organization = dossier.getApplicant().getData().getOrganization();
            }
            
            //taxCode
            if(Objects.nonNull(dossier.getApplicant().getData().getTaxCode())){
                this.taxCode = dossier.getApplicant().getData().getTaxCode();
            }
            
            //province1
            try{
                if(Objects.nonNull(dossier.getApplicant().getData().getProvince1().getLabel())){
                    this.province1 = dossier.getApplicant().getData().getProvince1().getLabel();
                }
            }catch(Exception e){
                logger.debug("HCMLGSPDossierSyncReqDto.DossierData: DossierCode: "+dossier.getCode()+"; Error province1: " + e.getMessage());
                this.province1 = "";
            }
            
            //district1
            try{
                if(Objects.nonNull(dossier.getApplicant().getData().getDistrict1().getLabel())){
                    this.district1 = dossier.getApplicant().getData().getDistrict1().getLabel();
                }
            }catch(Exception e){
                logger.debug("HCMLGSPDossierSyncReqDto.DossierData: DossierCode: "+dossier.getCode()+"; Error district1: " + e.getMessage());
                this.district1 = "";
            }
            
            //village1
            try{
                if(Objects.nonNull(dossier.getApplicant().getData().getVillage1().getLabel())){
                    this.village1 = dossier.getApplicant().getData().getVillage1().getLabel();
                }
            }catch(Exception e){
                logger.debug("HCMLGSPDossierSyncReqDto.DossierData: DossierCode: "+dossier.getCode()+"; Error village1: " + e.getMessage());
                this.village1 = "";
            }
            
            //address1
            try{
                if(Objects.nonNull(dossier.getApplicant().getData().getAddress1())){
                    this.address1 = dossier.getApplicant().getData().getAddress1();
                }
            }catch(Exception e){
                logger.debug("HCMLGSPDossierSyncReqDto.DossierData: DossierCode: "+dossier.getCode()+"; Error address1: " + e.getMessage());
                this.address1 = "";
            }
            
            try{
                //province
                if(Objects.nonNull(dossier.getApplicant().getData().getProvince().getLabel())){
                    this.province = dossier.getApplicant().getData().getProvince().getLabel();
                }
            }catch(Exception e){
                logger.debug("HCMLGSPDossierSyncReqDto.DossierData: DossierCode: "+dossier.getCode()+"; Error province: " + e.getMessage());
                this.province = "";
            }
            
            //district
            try{
                if(Objects.nonNull(dossier.getApplicant().getData().getDistrict().getLabel())){
                    this.district = dossier.getApplicant().getData().getDistrict().getLabel();
                }
            }catch(Exception e){
                logger.debug("HCMLGSPDossierSyncReqDto.DossierData: DossierCode: "+dossier.getCode()+"; Error district: " + e.getMessage());
                this.district = "";
            }
            
            //village
            try{
                if(Objects.nonNull(dossier.getApplicant().getData().getVillage().getLabel())){
                    this.village = dossier.getApplicant().getData().getVillage().getLabel();
                }
            }catch(Exception e){
                logger.debug("HCMLGSPDossierSyncReqDto.DossierData: DossierCode: "+dossier.getCode()+"; Error village: " + e.getMessage());
                this.village = "";
            }
            
            //address
            try{
                if(Objects.nonNull(dossier.getApplicant().getData().getAddress())){
                    this.address = dossier.getApplicant().getData().getAddress();
                }
            }catch(Exception e){
                logger.debug("HCMLGSPDossierSyncReqDto.DossierData: DossierCode: "+dossier.getCode()+"; Error address: " + e.getMessage());
                this.address = "";
            }
            
            
            List<CertificateDto> certificateList = new ArrayList<>();
            for(ProcedureAdministrationWithAssignedCode element : dossier.getExtendHCM().getProcedureAdministrationWithAssignedCode()){
                CertificateDto newCertificateDto = new CertificateDto();
                newCertificateDto.setCode(element.getCode());
                newCertificateDto.setTitle(element.getSubject());
                newCertificateDto.setPublishDate(element.getReleaseDate().toString());
                if(Objects.nonNull(element.getProcessingStatus())){
                    switch(element.getProcessingStatus()) {
                        case "0":
                          newCertificateDto.setDossierStatus("9");
                          break;
                        case "1":
                          newCertificateDto.setDossierStatus("5");
                          break;
                        case "2":
                          newCertificateDto.setDossierStatus("8");
                          break;
                        default:
                          newCertificateDto.setDossierStatus("");
                      }
                }
                List<FilePathDto> newFileList = new ArrayList<>();
                try {
                    for (HCMLGSPDossierDetailDto.AttachmentTakeNumber fileElement : element.getResultFile()) {
                        String fileResultPath = filepath + fileElement.getId().toHexString();
                        FilePathDto newFilePath = new FilePathDto();
                        newFilePath.setFilePath(fileResultPath);
                        newFileList.add(newFilePath);
                    }
                    newCertificateDto.setFileList(newFileList);

                    //nhipttt-IGATESUPP-117195 [HCM iGate V2] Sở Y Tế - Bổ sung dữ liệu Tệp tin dự thảo vào gói tin khi gửi qua hệ thống của SYT
                    newFileList = new ArrayList<>();
                    for (HCMLGSPDossierDetailDto.AttachmentTakeNumber fileElement : element.getAttachment()) {
                        String attachmentPath = filepath + fileElement.getId().toHexString();
                        FilePathDto newFilePath = new FilePathDto();
                        newFilePath.setFilePath(attachmentPath);
                        newFileList.add(newFilePath);
                    }
                    newCertificateDto.setFileListDaft(newFileList);
                } catch (Exception e){
                    logger.debug("HCMLGSPDossierDetailDto: create listFile result and attachment failed: " + e.getMessage());
                }
                certificateList.add(newCertificateDto);
            }
            this.certificateList = certificateList;
            // OwnerAuthenticated
//            List<OwnerAuthenticatedDto> lstOwnerAuthenticated = new ArrayList<>();
//            String identityType = "";
//            if(user.getIdentity().getNumber().length() == 9){
//                identityType = "2";
//            } else if(user.getIdentity().getNumber().length() == 12){
//                identityType = "1";
//            } else {
//                identityType = "3";
//            }
//            OwnerAuthenticatedDto owner = new OwnerAuthenticatedDto(identityType, user.getIdentity().getNumber());
//            lstOwnerAuthenticated.add(owner);
//            this.ownerAuthenticated = lstOwnerAuthenticated;
            // ConnectedDatabase
//            List<ConnectedDatabaseDto> lstConnectedDatabase = new ArrayList<>();
//            ConnectedDatabaseDto database = new ConnectedDatabaseDto("7");
//            lstConnectedDatabase.add(database);
//            this.ConnectedDatabase = lstConnectedDatabase;
        } catch (Exception e) {
            logger.debug("Constructor: Create dossier sync data failed: " + e.getMessage());
        }
    }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AttachmentDto implements Serializable {

        @JsonProperty("TepDinhKemId")
        private String id;
        
        @JsonProperty("TenTepDinhKem")
        private String name;
        
        @JsonProperty("DuongDanTaiTepTin")
        private String fileLink;
        
        @JsonProperty("IsDeleted")
        private Boolean isDelete;
        
        @JsonProperty("IdThanhPhanHoSo")
        private String formId;
        
        @JsonProperty("MaThanhPhanHoSo")
        private String code;
        
        @JsonProperty("TenThanhPhanHoSo")
        private String formName;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AttachmentOtherDto implements Serializable {

        @JsonProperty("TenGiayTo")
        private String name;
        
        @JsonProperty("SoLuong")
        private String quantity;
        
        @JsonProperty("LoaiGiayTo")
        private String type; //Loại giấy tờ thu: 1: Giấy tờ thu khi tiếp nhận hồ sơ; 2: Giấy tờ thu khi bổ sung hồ sơ; 4: Giấy tờ thu khi trả kết quả
        
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AdditionalDossierDto implements Serializable {

        @JsonProperty("HoSoBoSungId")
        private String id;
        
        @JsonProperty("NguoiYeuCauBoSung")
        private String requester;
        
        @JsonProperty("NoiDungBoSung")
        private String content;
        
        @JsonProperty("NgayBoSung")
        private String additionalDate;
        
        @JsonProperty("NguoiTiepNhanBoSung")
        private String reciever;
        
        @JsonProperty("ThongTinTiepNhan")
        private String informationReceived;
        
        @JsonProperty("NgayTiepNhanBoSung")
        private String recievDate;
        
        @JsonProperty("TrangThaiBoSung")
        private boolean status; //Trạng thái bổ sung hồ sơ: True: Công dân đã bổ sung đầy đủ thủ tục hồ sơ; False: Công dân không bổ sung hồ sơ (quá hạn bổ sung); Null: Công dân chưa tới bổ sung hồ sơ (còn hạn).
        
        @JsonProperty("DanhSachGiayToBoSung")
        private List<AttachmentDto> attachment;
        
        @JsonProperty("DanhSachLePhiBoSung")
        private List<FeeDto> fees;
        
        @JsonProperty("NgayHenTraTruoc")
        private String oldAppointmentDate;
        
        @JsonProperty("NgayHenTraMoi")
        private String newAppointmentDate;      
        
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FeeDto implements Serializable {

        @JsonProperty("TenPhiLePhi")
        private String name;
        @JsonProperty("MaPhiLePhi")
        private String code;
        @JsonProperty("HinhThucThu")
        private String feeMethod; //Loại lệ phí thu: 1: Lệ phí thu khi tiếp nhận hồ sơ; 2: Lệ phí thu khi bổ sung hồ sơ; 4: Lệ phí thu khi trả kết quả.
        
        @JsonProperty("LoaiHinhThucThu")
        private String paymentMethodName;
        @JsonProperty("Gia")
        private Integer price;
        @JsonProperty("LoaiPhiLePhi")
        private String type;
        
        @JsonProperty("TrangThaiThanhToan")
        private String statusPaid; //trạng thái thanh toán: 1: chưa thanh toán (mặc định), 2: đã thanh toán
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ResultDto implements Serializable {

        @JsonProperty("TenGiayTo")
        private String name;
        
        @JsonProperty("MaThanhPhanHoSo")
        private String code;
        
        @JsonProperty("GiayToId")
        private String id;
        
        @JsonProperty("DuongDanTepTinKetQua")
        private String fileLink;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CertificateDto  implements Serializable {

        @JsonProperty("MaSo")
        private String code;
        
        @JsonProperty("TrichYeu")
        private String title;
        
        @JsonProperty("NgayBanHanh")
        private String publishDate;
        
        @JsonProperty("DanhSachTepTin")
        private List<FilePathDto> fileList;
        
        @JsonProperty("TrangThaiHoSo")
        private String dossierStatus;

        @JsonProperty("DanhSachTepTinDuThao")
        private List<FilePathDto> fileListDaft;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static  class FilePathDto implements Serializable{
        
        @JsonProperty("DuongDanTaiTepTin")
        private String filePath;
    }
    

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OwnerAuthenticatedDto implements Serializable {

        @JsonProperty("LoaiDinhDanh")
        private String identityType;

        @JsonProperty("SoDinhDanh")
        private String identityNumber;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ConnectedDatabaseDto implements Serializable {

        @JsonProperty("MaCSDL")
        private String codeCSDL;
    }
}
