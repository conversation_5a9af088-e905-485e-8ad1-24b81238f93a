/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.ParametersType;
import vn.vnpt.digo.adapter.pojo.ListType;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParameterDoubleDto implements Serializable {
    
    private String key;
    
    private ParametersType type = ListType.DOUBLE;
    
    private Double value;
    
    private String description;
    
    public ParameterDoubleDto(String key, Double value, String desc){
        this.key = key;
        this.value = value;
        this.description = desc;
    }
}
