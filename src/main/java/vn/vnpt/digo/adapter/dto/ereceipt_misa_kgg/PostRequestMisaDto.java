package vn.vnpt.digo.adapter.dto.ereceipt_misa_kgg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PostRequestMisaDto {
    private ArrayList<InvoiceDataList> InvoiceDataList;
    private String userName;
    private String passWord;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InvoiceDataList implements Serializable {
        private String RefID;
        private String InvoiceType;
        private String TemplateCode;
        private String InvoiceSeries;
        private String InvDate;
        private String CurrencyCode;
        private int AdjustmentType;
        private String PaymentMethodName;
        private String SellerTaxCode;
        private String SellerLegalName;
        private String SellerAddressLine;
        private String SellerBankAccount;
        private String SellerBankName;
        private String BuyerLegalName;
        private String BuyerDisplayName;
        private String BuyerTaxCode;
        private String BuyerAddressLine;
        private String BuyerPhoneNumber;
        private String BuyerEmail;
        private String AccountObjectCode;
        private String BuyerBankAccount;
        private String BuyerBankName;
        private int ExchangeRate;
        private String VatPercentage;
        private long DiscountAmount;
        private long TotalAmountWithoutVAT;
        private long TotalVATAmount;
        private long TotalAmountWithVAT;
        private long TotalAmountWithVATFrn;
        private String TotalAmountWithVATInWords;
        private boolean IsSendEmail;
        private String ReceiverName;
        private String ReceiverEmail;
        private String ReceiptName;
        private ArrayList<OriginalInvoiceDetail> OriginalInvoiceDetail;
        private OptionUserDefined OptionUserDefined;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OriginalInvoiceDetail implements Serializable {
        private int LineNumber;
        private String ItemCode;
        private String ItemName;
        private String UnitName;
        private int Quantity;
        private Double Amount;
        private String VatPercentage;
        private long VatAmount;
        private Double UnitPrice;
        private boolean Promotion;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionUserDefined implements Serializable {
        private String MainCurrency;
        private String AmountDecimalDigits;
        private String AmountOCDecimalDigits;
        private String UnitPriceOCDecimalDigits;
        private String UnitPriceDecimalDigits;
        private String QuantityDecimalDigits;
        private String CoefficientDecimalDigits;
        private String ExchangRateDecimalDigits;
    }
}
