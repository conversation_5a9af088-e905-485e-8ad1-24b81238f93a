package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

/**
 *
 * <AUTHOR> ThursDay 29/10/2022
 */
@Data
@NoArgsConstructor
@AllArgsConstructor

public class DVC_V2TrangthaiHoSoReqDto implements Serializable {

    @NotEmpty(message = "{lang.word.tag} 'session' {lang.phrase.may-can-not-empty}")
    private String session;

    @NotEmpty(message = "{lang.word.tag} 'madonvi' {lang.phrase.may-can-not-empty}")
    private String madonvi;

    @NotEmpty(message = "{lang.word.tag} 'service' {lang.phrase.may-can-not-empty}")
    private String service;

    @NotEmpty(message = "{lang.word.tag} 'service' {lang.phrase.may-can-not-empty}")
    private List<@Valid DVC_TrangthaiHoSoDataDto> data;

    private String deploymentId;

    public DVC_V2TrangthaiHoSoReqDto(String session, String ma_donvi, String dich_vu, DVC_TrangthaiHoSoDataDto data) {
        this.session = session;
        this.madonvi = ma_donvi;
        this.service = dich_vu;
        this.data = new ArrayList<DVC_TrangthaiHoSoDataDto>();
        this.data.add(data);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DVC_TrangthaiHoSoDataDto implements Serializable {

        @JsonIgnore
        private final SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");

        @JsonProperty("HoSoMotCuaID")
        @NotEmpty(message = "{lang.word.tag} 'HoSoMotCuaID' {lang.phrase.may-can-not-empty}")
        private String HoSoMotCuaID;

        @JsonProperty("MaTrangThaiHoSo")
        @NotEmpty(message = "{lang.word.tag} 'MaTrangThaiHoSo' {lang.phrase.may-can-not-empty}")
        private String MaTrangThaiHoSo;

        @JsonProperty("TrangThaiHoSo")
        @NotEmpty(message = "{lang.word.tag} 'TrangThaiHoSo' {lang.phrase.may-can-not-empty}")
        private String TrangThaiHoSo;

        @JsonProperty("NgayKetThucTTTruoc")
        @Size(min = 14, max = 20, message = "{lang.word.tag} 'NgayKetThucTTTruoc' {lang.phrase.is-not-valid}")
        private String NgayKetThucTTTruoc;

        @JsonProperty("NgayXuLy")
        @NotEmpty(message = "{lang.word.tag} 'NgayXuLy' {lang.phrase.may-can-not-empty}")
        @Size(min = 14, max = 20, message = "{lang.word.tag} 'NgayXuLy' {lang.phrase.is-not-valid}")
        private String NgayXuLy;

        @JsonProperty("HanXuLy")
        @Size(min = 14, max = 20, message = "{lang.word.tag} 'HanXuLy' {lang.phrase.is-not-valid}")
        private String HanXuLy;

        @JsonProperty("MaNguoiXuLy")
        private String MaNguoiXuLy;

        @JsonProperty("TenNguoiXuLy")
        @NotEmpty(message = "{lang.word.tag} 'TenNguoiXuLy' {lang.phrase.may-can-not-empty}")
        private String TenNguoiXuLy;

        @JsonProperty("PhongBanNguoiXuLy")
        private String PhongBanNguoiXuLy;

        @JsonProperty("ChucVuNguoiXuLy")
        @NotEmpty(message = "{lang.word.tag} 'ChucVuNguoiXuLy' {lang.phrase.may-can-not-empty}")
        private String ChucVuNguoiXuLy;

        @JsonProperty("NoiDungXuLy")
        private String NoiDungXuLy;

        @JsonProperty("NgayHenTra")
        @Size(min = 14, max = 20, message = "{lang.word.tag} 'NgayHenTra' {lang.phrase.is-not-valid}")
        private String NgayHenTra;

        @JsonProperty("SoBienNhan")
        private String SoBienNhan;

        @JsonProperty("MaHoSo")
        private String MaHoSo;

        @JsonProperty("PhiDichVu")
        private String PhiDichVu;

        @JsonProperty("NgayThanhToan")
        @Size(min = 14, max = 20, message = "{lang.word.tag} 'NgayThanhToan' {lang.phrase.is-not-valid}")
        private String NgayThanhToan;

        @JsonProperty("MaGiaoDich")
        private String MaGiaoDich;

        @JsonProperty("NgayYeuCauThanhToan")
        @Size(min = 14, max = 20, message = "{lang.word.tag} 'NgayYeuCauThanhToan' {lang.phrase.is-not-valid}")
        private String NgayYeuCauThanhToan;

        @JsonProperty("Ticket")
        @NotEmpty(message = "{lang.word.tag} 'Ticket' {lang.phrase.may-can-not-empty}")
        private String Ticket;

        @JsonProperty("MaDonViC1")
        @NotEmpty(message = "{lang.word.tag} 'MaDonViC1' {lang.phrase.may-can-not-empty}")
        private String MaDonViC1;

        @JsonProperty("TenDonViC1")
        @NotEmpty(message = "{lang.word.tag} 'TenDonViC1' {lang.phrase.may-can-not-empty}")
        private String TenDonViC1;

        @JsonProperty("MaDonViC2")
        @NotEmpty(message = "{lang.word.tag} 'MaDonViC2' {lang.phrase.may-can-not-empty}")
        private String maDonViC2;

        @JsonProperty("TenDonViC2")
        @NotEmpty(message = "{lang.word.tag} 'TenDonViC2' {lang.phrase.may-can-not-empty}")
        private String TenDonViC2;

        @JsonProperty("MaDonViC3")
        @NotEmpty(message = "{lang.word.tag} 'MaDonViC3' {lang.phrase.may-can-not-empty}")
        private String MaDonViC3;

        @JsonProperty("TenDonViC3")
        @NotEmpty(message = "{lang.word.tag} 'TenDonViC3' {lang.phrase.may-can-not-empty}")
        private String TenDonViC3;

        @JsonProperty("Version")
        @NotEmpty(message = "{lang.word.tag} 'Version' {lang.phrase.may-can-not-empty}")
        private String Version;

    }
}
