package vn.vnpt.digo.adapter.dto.qlvb;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DossierIdResDto implements Serializable {
    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    ObjectId id;
}
