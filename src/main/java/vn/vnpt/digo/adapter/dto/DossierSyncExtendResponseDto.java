/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierStatusDto;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DossierSyncExtendResponseDto {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    
    private String code;
    
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId dossierTaskStatusId;
    
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId currentTaskId;
    
    private String DossierMenuTaskRemindName;
    
    private NpsDossierStatusDto.AgencyDto agency; 
    
    private String nationCode;
    
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subsystemId;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date startDate; //== updatedDate
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date endDate;
}
