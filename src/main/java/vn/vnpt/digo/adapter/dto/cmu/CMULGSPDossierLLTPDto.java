package vn.vnpt.digo.adapter.dto.cmu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CMULGSPDossierLLTPDto implements Serializable {
    @JsonProperty("id")
    private ObjectId _id;
    @JsonProperty("code")
    private String code;
    @JsonProperty("appointmentDate")
    private Date appointmentDate;
    @JsonProperty("acceptedDate")
    private Date acceptedDate;
    @JsonProperty("extendKTM")
    private ExtendKTMDto extendKTMDto;
    private ApplyMethod applyMethod;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplyMethod
    {
        @JsonProperty("id")
        private Long id;
        @JsonProperty("name")
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtendKTMDto implements Serializable{
        @JsonSerialize(using = ToStringSerializer.class)
        @JsonProperty("isSyncFromMonre")
        private Boolean isSyncFromMonre;
        @JsonProperty("isFinishedSyncToMonre")
        private Boolean isFinishedSyncToMonre;
        @JsonProperty("exportEForm")
        private List<ExportEForm> exportEForm;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExportEForm implements Serializable{
        @JsonProperty("eForm")
        private EForm eForm;
    }
    @JsonProperty("eForm")
    private EForm eForm;
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EForm implements Serializable{
        @JsonProperty("id")
        private ObjectId id;
        @JsonProperty("data")
        private ToKhaiEform data;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class QuaTrinhCuTru implements Serializable{
        @JsonProperty("tuNgay") // dd/mm/yyy or mm/yyyy or yyyy
        private String tuNgay;
        @JsonProperty("denNgay") // dd/mm/yyy or mm/yyyy or yyyy
        private String denNgay;
        @JsonProperty("noiCuTru")
        private String noiCuTru;
        @JsonProperty("ngheNghiep")
        private String ngheNghiep;
        @JsonProperty("noiLamViec")
        private String noiLamViec;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LabelAndValue implements Serializable{
        @JsonProperty("value")
        private String value;
        @JsonProperty("label")
        private String label;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ToKhaiEform implements Serializable{
        // Thông tin chung
        @JsonProperty("loaiPhieu")
        private Long loaiPhieu;
        @JsonProperty("yeuCauCDNCV")
        private Long yeuCauCDNCV;
        @JsonProperty("maMucDich")
        private Long maMucDich;
        @JsonProperty("tenMucDich")
        private String tenMucDich;
        @JsonProperty("mucDichKhac")
        private String mucDichKhac;
        @JsonProperty("mucDich")
        private String mucDich;
        @JsonProperty("soLuongCap")
        private String soLuongCap;

        // Thông tin người yêu cầu cấp phiếu LLTP
        @JsonProperty("nycHoTen")
        private String nycHoTen;
        @JsonProperty("nycTenGoiKhac")
        private String nycTenGoiKhac;
        @JsonProperty("nycGioiTinh")
        private Long nycGioiTinh;
        @JsonProperty("nycTenGioiTinh")
        private String nycTenGioiTinh;
        @JsonProperty("nycNgaySinh")
        private String nycNgaySinh;

        @JsonProperty("tinhThanhThuongTru")
        private LabelAndValue tinhThanhThuongTru;
        @JsonProperty("quanHuyenThuongTru")
        private LabelAndValue quanHuyenThuongTru;
        @JsonProperty("xaPhuongThuongTru")
        private LabelAndValue xaPhuongThuongTru;
        @JsonProperty("dcChiTietThuongTru")
        private String dcChiTietThuongTru;

        @JsonProperty("tinhThanhTamTru")
        private LabelAndValue tinhThanhTamTru;
        @JsonProperty("quanHuyenTamTru")
        private LabelAndValue quanHuyenTamTru;
        @JsonProperty("xaPhuongTamTru")
        private LabelAndValue xaPhuongTamTru;
        @JsonProperty("dcChiTietTamTru")
        private String dcChiTietTamTru;

        @JsonProperty("nycDienThoai")
        private String nycDienThoai;
        @JsonProperty("nycEmail")
        private String nycEmail;
        @JsonProperty("nycLoaiGiayto")
        private Long nycLoaiGiayto;
        @JsonProperty("nycTenLoaiGiayTo")
        private String nycTenLoaiGiayTo;
        @JsonProperty("nycSoGiayTo")
        private String nycSoGiayTo;
        //        @DateTimeFormat(pattern = "dd/mm/yyyy")
        @JsonProperty("nycNgayCapGiayTo")
        private String nycNgayCapGiayTo;
        @JsonProperty("nycNoiCapGiayTo")
        private String nycNoiCapGiayTo;
        @JsonProperty("nycQuocTich")
        private LabelAndValue nycQuocTich;
        @JsonProperty("nycDanToc")
        private LabelAndValue nycDanToc;
        @JsonProperty("nycHoTenCha")
        private String nycHoTenCha;
        @JsonProperty("chaNgaySinh") // format dd/mm/yyyy
        private String chaNgaySinh;
        @JsonProperty("chaLoaiGiayTo")
        private Long chaLoaiGiayTo;
        @JsonProperty("chaTenLoaiGiayTo")
        private String chaTenLoaiGiayTo;
        @JsonProperty("chaSoGiayTo")
        private String chaSoGiayTo;
        @JsonProperty("nycHoTenMe")
        private String nycHoTenMe;
        @JsonProperty("meNgaySinh") // format dd/mm/yyyy
        private String meNgaySinh;
        @JsonProperty("meLoaiGiayTo")
        private Long meLoaiGiayTo;
        @JsonProperty("meTenLoaiGiayTo")
        private String meTenLoaiGiayTo;
        @JsonProperty("meSoGiayTo")
        private String meSoGiayTo;
        @JsonProperty("nycHoTenVoChong")
        private String nycHoTenVoChong;
        @JsonProperty("voChongNgaySinh") // format dd/mm/yyyy
        private String voChongNgaySinh;
        @JsonProperty("voChongLoaiGiayTo")
        private Long voChongLoaiGiayTo;
        @JsonProperty("voChongTenLoaiGiayTo")
        private String voChongTenLoaiGiayTo;
        @JsonProperty("voChongSoGiayTo")
        private String voChongSoGiayTo;

        @JsonProperty("tinhThanhNoiSinh")
        private LabelAndValue tinhThanhNoiSinh;
        @JsonProperty("quanHuyenNoiSinh")
        private LabelAndValue quanHuyenNoiSinh;
        @JsonProperty("xaPhuongNoiSinh")
        private LabelAndValue xaPhuongNoiSinh;

        @JsonProperty("uyQuyen")
        private Long uyQuyen;
        @JsonProperty("thongTinAnTich")
        private String thongTinAnTich;

        // Thông tin người được ủy quyền yêu cầu cấp phiếu LLTP (chỉ dùng khi trường uyQuyen = 1)
        @JsonProperty("uyQuyenHoTen")
        private String uyQuyenHoTen;
        @JsonProperty("nuqTenGoiKhac")
        private String nuqTenGoiKhac;
        @JsonProperty("nuqGioiTinh")
        private Long nuqGioiTinh;
        @JsonProperty("nuqNgaySinh")
        private String nuqNgaySinh;

        @JsonProperty("tinhThanhNoiSinhNuq")
        private LabelAndValue tinhThanhNoiSinhNuq;
        @JsonProperty("quanHuyenNoiSinhNuq")
        private LabelAndValue quanHuyenNoiSinhNuq;
        @JsonProperty("xaPhuongNoiSinhNuq")
        private LabelAndValue xaPhuongNoiSinhNuq;
        @JsonProperty("dcChiTietNoiSinhNuq")
        private String dcChiTietNoiSinhNuq;

        @JsonProperty("tinhThanhThuongTruNuq")
        private LabelAndValue tinhThanhThuongTruNuq;
        @JsonProperty("quanHuyenThuongTruNuq")
        private LabelAndValue quanHuyenThuongTruNuq;
        @JsonProperty("xaPhuongThuongTruNuq")
        private LabelAndValue xaPhuongThuongTruNuq;
        @JsonProperty("dcChiTietThuongTruNuq")
        private String dcChiTietThuongTruNuq;

        @JsonProperty("nuqThuongTruDVHC")
        private String nuqThuongTruDVHC;
        @JsonProperty("nuqDienThoai")
        private String nuqDienThoai;
        @JsonProperty("nuqLoaiGiayto")
        private Long nuqLoaiGiayto;
        @JsonProperty("nuqSoGiayTo")
        private String nuqSoGiayTo;
        //        @DateTimeFormat(pattern = "dd/mm/yyyy")
        @JsonProperty("nuqNgayCapGiayTo")
        private String nuqNgayCapGiayTo;
        @JsonProperty("nuqNoiCapGiayTo")
        private String nuqNoiCapGiayTo;
        @JsonProperty("nuqEmail")
        private String nuqEmail;
        @JsonProperty("nuqQuocTich")
        private LabelAndValue nuqQuocTich;
        @JsonProperty("nuqDanToc")
        private LabelAndValue nuqDanToc;
        @JsonProperty("nyqQuanHe")
        private String nyqQuanHe;

        // Thông tin quá trình cư trú của người yêu cầu cấp phiếu LLTP (dạng list nhiều thông tin quá trình cư trú)
        @JsonProperty("nycCuTru")
        private List<QuaTrinhCuTru> nycCuTru;
    }
}
