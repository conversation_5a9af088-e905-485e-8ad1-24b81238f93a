/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.util.CheckConfigParams;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigParams
/**
 *
 * <AUTHOR>
 */

public class SIGetTtHgdByMaSoBhxhResDto implements Serializable {
    private String hoTen;
    private String soSoCu;
    private String ngaySinh;
    private String loaiNgaySinh;
    private String gioiTinh;
    private String maTinhKs;
    private String maHuyenKs;
    private String maXaKs;
    private String trangThai;
}
