package vn.vnpt.digo.adapter.dto.v2.vnptsmartca;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SignDto extends PrivateInfo implements Serializable {
    private String transaction_desc;
    private List<FileSignDto> sign_files;
    private String transaction_id;
    private String serial_number;
    private String time_stamp;
    public SignDto(PrivateInfo privateInfo){
        this.setSp_id(privateInfo.getSp_id());
        this.setSp_password(privateInfo.getSp_password());
        this.setUser_id(privateInfo.getUser_id());
    }
}
