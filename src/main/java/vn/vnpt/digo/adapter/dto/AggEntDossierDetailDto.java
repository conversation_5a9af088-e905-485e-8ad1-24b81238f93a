package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AggEntDossierDetailDto implements Serializable {
    private String MaNoiBo;
    private String MaSoDoanhNghiep;
    private String MaSoDangKyHoKinhDoanh;
    private String TenDoanhNghiep;
    private String MaHoSo;
    private String ma_tthc;
    private String ten_tthc;
    private String MaLinhVuc;
    private String TenLinhVuc;
    private Integer KenhThucHien;
    private String ChuHoSo;
    private String LoaiDoiTuong;
    private String MaDoiTuong;
    private String ThongTinKhac;
    private String Email;
    private String Fax;
    private String SoDienThoai;
    private String TrichYeuHoSo;
    private String NgayTiepNhan;
    private String NgayHenTra;
    private Integer TrangThaiHoSo;
    private String NgayTra;
    private String ThongTinTra;
    private String HinhThuc;
    private String NgayKetThucXuLy;
    private String DonViXuLy;
    private String GhiChu;
    private String NoiNopHoSo;
    private String HoSoCoThanhPhanSoHoa;
    private Integer TaiKhoanDuocXacThucVoiVNeID;
    private String DuocThanhToanTrucTuyen;
    private String NgayTuChoi;
    private String LoaiDinhDanh;
    private String SoDinhDanh;
    private String NgayNopHoSo;
    private Integer LoaiCoSo;
    private ArrayList<Map<String, Object>> TaiLieuNop;
    private ArrayList<Map<String, Object>> DanhSachLePhi;
    private ArrayList<Map<String, Object>> DanhSachTepDinhKemKhac;
    private ArrayList<Map<String, Object>> DanhSachHoSoBoSung;
    private ArrayList<Map<String, Object>> DanhSachGiayToKetQua;
    private ArrayList<Map<String, Object>> DinhDanhCHS;
}
