package vn.vnpt.digo.adapter.dto.tandan;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FilemanResponseDto implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String filename;
    private long size;
    private UUID uuid;
}
