package vn.vnpt.digo.adapter.dto.hbh.msns;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "GETDETAILHOSORESPONSE")
public class BudgetDossierDetailReturnDto implements Serializable {
    
    private ArrayList<BudgetDossierDataDto> RETURN;
    
}