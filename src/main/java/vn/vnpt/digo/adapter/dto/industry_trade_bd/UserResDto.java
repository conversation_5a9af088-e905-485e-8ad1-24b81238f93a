package vn.vnpt.digo.adapter.dto.industry_trade_bd;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserResDto extends StatusResDto implements Serializable {
    private ArrayList<UserTransformDto> data;
}
