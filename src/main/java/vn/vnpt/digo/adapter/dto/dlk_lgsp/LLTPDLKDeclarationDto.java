/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.dlk_lgsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LLTPDLKDeclarationDto implements Serializable {
    private String fullName;
    private String otherName;
    private String genderId;
    private String birthDateStr;
    private String birthPlace;
    private String nationalityId;
    private String ethnicId;
    private String residence;
    private String residenceTemporary;
    private String idTypeId;
    private String identifyNo;
    private String idIssueDate;
    private String idIssuePlace;
    private String dadName;
    private String dadDob;
    private String momName;
    private String momDob;
    private String partnerName;
    private String partnerDob;
    private String phone;
    private String email;
    private String formType;
    private String isBanPosition;
    private String purpose;
    private Integer requestQtyAdd;
    private Integer requestQty;
    private String declareTypeId;
    private String note;
    private String objectRequestId;
    private String declareDate;
    private Integer delivery;
    private String deliveryAddress;
    private Integer deliveryDistrict;
    private Integer reRegionId;
    private Integer rtRegionId;
    private String agencyRequestId;
    private Integer regionRequestId;
}
