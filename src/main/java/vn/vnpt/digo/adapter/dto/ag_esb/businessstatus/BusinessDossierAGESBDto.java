package vn.vnpt.digo.adapter.dto.ag_esb.businessstatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessDossierAGESBDto implements Serializable {
    private ObjectId configId;
    private ObjectId agencyId;
    private ObjectId subsystemId;
    @SerializedName("maDonVi")
    @Expose
    private String maDonVi;

    @SerializedName("idReceivedDec")
    @Expose
    private String idReceivedDec;

    @SerializedName("dateReceivedDec")
    @Expose
    private String dateReceivedDec;

    @SerializedName("datePromissoryDec")
    @Expose
    private String datePromissoryDec;

    @SerializedName("tenNguoiNop")
    @Expose
    private String tenNguoiNop;
    @SerializedName("module")
    @Expose
    private String module;
    @SerializedName("maHoSo")
    @Expose
    private String maHoSo;
    @SerializedName("ngayTiepNhan")
    @Expose
    private Date ngayTiepNhan;
    @SerializedName("diaChiNguoiNop")
    @Expose
    private String diaChiNguoiNop;

    @SerializedName("soCMND")
    @Expose
    private String soCMND;

    @SerializedName("dienThoai")
    @Expose
    private String dienThoai;

    @SerializedName("tenDichVuCong")
    @Expose
    private String tenDichVuCong;

    @SerializedName("ngayNhanHS")
    @Expose
    private Date ngayNhanHS;

    @SerializedName("ngayHenTraHS")
    @Expose
    private Date ngayHenTraHS;

    @SerializedName("tinhTrangHS")
    @Expose
    private String tinhTrangHS;

    @SerializedName("trangThaiXuLy")
    @Expose
    private Integer trangThaiXuLy;

    @SerializedName("phongBanXuLy")
    @Expose
    private String phongBanXuLy;

    @SerializedName("canBoXuLy")
    @Expose
    private String canBoXuLy;

    @SerializedName("maDonViGui")
    @Expose
    private String maDonViGui;

    @SerializedName("tenToChuc")
    @Expose
    private String tenToChuc;

    @SerializedName("filedcodeDKKD")
    @Expose
    private String filedcodeDKKD;

    @SerializedName("processcodeDKKD")
    @Expose
    private Map<String,String> processcodeDKKD;

    @SerializedName("chuyenVienXuLy")
    @Expose
    private String chuyenVienXuLy;

    @SerializedName("listOrganId")
    @Expose
    private Map<String,String > listOrganId;

    @SerializedName("data")
    @Expose
    private String data;
    private Map<String,Object> dataEform;

    @SerializedName("listFileIds")
    @Expose
    private ArrayList<FileAttachment> listFileIds;
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileAttachment {
        @JsonProperty("id")
        private String id;

        @JsonProperty("filename")
        private String fileName;

    }


}
