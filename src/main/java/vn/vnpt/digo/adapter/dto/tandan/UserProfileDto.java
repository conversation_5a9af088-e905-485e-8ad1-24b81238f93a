package vn.vnpt.digo.adapter.dto.tandan;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserProfileDto implements Serializable {
    @JsonProperty("Account")
    private String Account;

    @JsonProperty("FullName")
    private String FullName;
}
