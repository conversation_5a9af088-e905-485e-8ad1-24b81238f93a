package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TokenRequestDto implements Serializable {

    @NotNull
    private String username;

    @NotNull
    private String password;
}
