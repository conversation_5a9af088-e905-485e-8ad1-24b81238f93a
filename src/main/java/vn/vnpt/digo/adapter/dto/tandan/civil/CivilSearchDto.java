package vn.vnpt.digo.adapter.dto.tandan.civil;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.ParamName;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CivilSearchDto implements Serializable {
    private ObjectId configId;
    private ObjectId agencyId;
    private ObjectId subsystemId;
    private String module;
    private String maHoSo;
}
