/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.vnpt.digo.adapter.dto.Statistical468And761Dto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierDetailDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierFeeDto;
import vn.vnpt.digo.adapter.dto.nps.NpsDossierSyncReqDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.dto.utils.ProcedureDetailDto;
import vn.vnpt.digo.adapter.dto.utils.SectorDetailDto;
import vn.vnpt.digo.adapter.dto.utils.UserInfoDto;
import vn.vnpt.digo.adapter.util.ListHelper;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LGSPHCMDossierSyncReqDto implements Serializable {
    private String session;
    private String madonvi;
    private String service = "DongBoHoSoMC";
    private String isUpdating = "true";
    private List<DossierData> data;

    public LGSPHCMDossierSyncReqDto(String madonvi, List<DossierData> data, String isUpdate) {
        this.madonvi = madonvi;
        this.isUpdating = isUpdate;
        this.data = data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DossierData implements Serializable {

        @JsonProperty("MaHoSo")
        private String code;

        @JsonProperty("MaTTHC")
        private String procedureCode; // Mã thủ tục hành chính theo CSDL TTHC Quốc gia
        @JsonProperty("TenTTHC")
        private String procedureName;

        @JsonProperty("MaLinhVuc")
        private String sectorCode; // Tên lĩnh vực theo CSDL TTHC Quốc gia
        @JsonProperty("TenLinhVuc")
        private String sectorName;

        @JsonProperty("KenhThucHien")
        private String receptionMethod = "1"; // 1: Trực tiếp 2: Nộp trực tuyến 3: Nộp qua bưu chính công ích

        @JsonProperty("ChuHoSo")
        private String applicantName;
        @JsonProperty("LoaiDoiTuong")
        private String applicantType = "1";
        @JsonProperty("MaDoiTuong")
        private String applicantCode = ""; // TechnicalId lấy từ hệ thống VNConnect

        @JsonProperty("ThongTinKhac")
        private String applicantOtherInfo = "";

        @JsonProperty("Email")
        private String applicantEmail;
        @JsonProperty("Fax")
        private String applicantFax;
        @JsonProperty("SoDienThoai")
        private String applicantPhoneNumber;
        
        @JsonProperty("TrichYeuHoSo")
        private String title;
        

        @JsonProperty("TrangThaiHoSo")
        private String status = "1";

        @JsonProperty("NgayTiepNhan")
        private String receptedDate;

        @JsonProperty("NgayHenTra")
        private String appointmentDate;

        @JsonProperty("NgayTra")
        private String completedDate;
        
        @JsonProperty("ThongTinTra")
        private Boolean completedInfo;

        @JsonProperty("HinhThuc")
        private String returnedMethod;
        
        @JsonProperty("NgayKetThucXuLy")
        private String endDate;

        @JsonProperty("HoSoCoThanhPhanSoHoa")
        private String dossierDigitizing = "0";

        @JsonProperty("TaiKhoanDuocXacThucVoiVNeID")
        private String accountAuthenticatedWithVNeID = "1";

        @JsonProperty("DuocThanhToanTrucTuyen")
        private String onlinePayment = "3";

        @JsonProperty("DinhDanhCHS")
        private List<OwnerAuthenticatedDto> ownerAuthenticated;

        @JsonProperty("NgayNopHoSo")
        private String submissionDate;

        @JsonProperty("DSKetNoiCSDL")
        private List<ConnectedDatabaseDto> ConnectedDatabase;

        @JsonProperty("NoiNopHoSo")
        private String submissionPlace;

        @JsonProperty("DonViXuLy")
        private String handlingAgency;
        
        @JsonProperty("GhiChu")
        private String note;

        @JsonProperty("TaiLieuNop")
        private List<AttachmentDto> attachment;

        @JsonProperty("DanhSachLePhi")
        private List<FeeDto> fees;
        
        @JsonProperty("DanhSachTepDinhKemKhac")
        private List<AttachmentDifDto> attachmentDif;
        
        @JsonProperty("DanhSachHoSoBoSung")
        private List<AddDocumentDto> addDocuments;

        @JsonProperty("DanhSachGiayToKetQua")
        private List<ResultDto> result;
        
        @JsonProperty("NgayTuChoi")
        private String rejectionDate;
        
        

    public DossierData(LGSPHCMDossierDetailDto dossier, List<LGSPHCMDossierFeeDto> fees,
            Integer status, UserInfoDto user, ProcedureDetailDto procedure, SectorDetailDto sector, IdCodeNameDto receivingKind,
            IdCodeNameSimpleDto agency, String techId, String filepath, Statistical468And761Dto additionalInfo) {
        Logger logger = LoggerFactory.getLogger(NpsDossierSyncReqDto.class);
        try {
            // dossier info
            this.code = dossier.getCode();

            this.procedureCode = procedure.getNationCode();
            this.procedureName = procedure.getName();

            this.sectorCode = sector.getCode();
            this.sectorName = procedure.getSector().getName();

//            this.receptionMethod = dossier.getApplyMethod().getId().toString();
            switch (dossier.getApplyMethod().getId()){
                case 0:
                    this.receptionMethod = "2";
                    break;
                case 1:
                    this.receptionMethod = "1";
                    break;
                case 2:
                    this.receptionMethod = "3";
                    break;
                default:
                    this.receptionMethod = "1";
                    break;
            }

            this.applicantName = dossier.getApplicant().getData().getFullname();
            this.applicantType = user.getType().toString();
            this.applicantCode = techId;

            this.applicantEmail = dossier.getApplicant().getData().getEmail();
            this.applicantFax = dossier.getApplicant().getData().getFax();
            this.applicantPhoneNumber = dossier.getApplicant().getData().getPhoneNumber();

            this.status = status.toString();
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            if(Objects.nonNull(dossier.getAppliedDate())){
                this.receptedDate = df.format(dossier.getAppliedDate());
            } else {
                this.receptedDate = "";
            }
            if(Objects.nonNull(dossier.getAppointmentDate())){
                this.appointmentDate = df.format(dossier.getAppointmentDate());
            } else {
                this.appointmentDate = "";
            }
            if(Objects.nonNull(dossier.getCompletedDate())){
                this.completedDate = df.format(dossier.getCompletedDate());
            } else {
                this.completedDate = "";
            }
            try {
                Integer kind = Integer.parseInt(receivingKind.getCode());
                kind = kind - 1000;
                this.returnedMethod = kind.toString();
            } catch (Exception e) {
                this.returnedMethod = "0";
            }
            this.handlingAgency = agency.getName();
            this.note = "";
            this.accountAuthenticatedWithVNeID = "1";
            this.onlinePayment = additionalInfo.getOnlinePayment();
            this.submissionDate = this.receptedDate;
            this.submissionPlace = "2";
            String isDigitizing = "0";
//            if(additionalInfo.getDossierDigitizing() != null){
//                for(Statistical468And761Dto.DigitizingResponDto file:additionalInfo.getDossierDigitizing()){
//                    if(file.getStorageId() != null){
//                        isDigitizing = "1";
//                        break;
//                    }
//                }
//            }
//            this.dossierDigitizing = isDigitizing;
            // attchment info
            List<AttachmentDto> lstAttchment = new ArrayList<>();
            if (dossier.getDossierFormFile() != null && !dossier.getDossierFormFile().isEmpty()) {
                isDigitizing = "1";
                this.dossierDigitizing = isDigitizing;
                dossier.getDossierFormFile().forEach(item -> {
                    item.getFile().forEach(i -> {
                        String isDigitized = "0";
                        String reUsed = "0";
                        if(item.getProcedureForm() != null){
                            if(item.getProcedureForm().getType() != null && item.getProcedureForm().getType() == 2){
                                reUsed = "1";
                            }
                        }
                        if(Objects.nonNull(i.getId())){
                            isDigitized = "1";
                        }
                        AttachmentDto file = new AttachmentDto(i.getFilename(),false,"", filepath + i.getId().toHexString(),isDigitized,reUsed,"0","");
                        lstAttchment.add(file);
                    });
                });
            }
            this.attachment = lstAttchment;
            // fee info
            List<FeeDto> lstFee = new ArrayList<>();
            fees.forEach(item -> {
                FeeDto f = new FeeDto(ListHelper.getName(item.getProcost().getType().getName()),
                        item.getId().toHexString(), "1", item.getAmount().intValue(),
                        String.valueOf(item.getProcost().getType().getType() + 1));
                lstFee.add(f);
            });
            this.fees = lstFee;
            // result
            List<ResultDto> lstResult = new ArrayList<>();
            if(Objects.nonNull(dossier.getAttachment())){
                dossier.getAttachment().forEach(item -> {
                    if (Objects.equals(new ObjectId("5f9bd9692994dc687e68b5a6"), item.getGroup())) {
                        ResultDto rs = new ResultDto(item.getFilename(),"", "", filepath + item.getId().toHexString(), "KQ.G18.000025");
                        lstResult.add(rs);
                    }
                });
            }
            this.result = lstResult;
            // OwnerAuthenticated
            List<OwnerAuthenticatedDto> lstOwnerAuthenticated = new ArrayList<>();
            String identityType = "";
            String identityString = "123456789";
            if(Objects.nonNull(user) && Objects.nonNull(user.getIdentity()) && Objects.nonNull(user.getIdentity().getNumber())){
                identityString = user.getIdentity().getNumber();
            }
            else{
                if(Objects.nonNull(dossier.getApplicant()) && Objects.nonNull(dossier.getApplicant().getData()) 
                        && Objects.nonNull(dossier.getApplicant().getData().getIdentityNumber()) && !dossier.getApplicant().getData().getIdentityNumber().equals("")){
                    identityString = dossier.getApplicant().getData().getIdentityNumber();
                }
            }
            if(identityString.length() == 9){
                identityType = "2";
            } else if(identityString.length() == 12){
                identityType = "1";
            } else {
                identityType = "3";
            }
            OwnerAuthenticatedDto owner = new OwnerAuthenticatedDto(identityType, identityString);
            lstOwnerAuthenticated.add(owner);
            this.ownerAuthenticated = lstOwnerAuthenticated;
            // ConnectedDatabase
            List<ConnectedDatabaseDto> lstConnectedDatabase = new ArrayList<>();
            ConnectedDatabaseDto database = new ConnectedDatabaseDto("7");
            lstConnectedDatabase.add(database);
            this.ConnectedDatabase = lstConnectedDatabase;
        } catch (Exception e) {
            logger.debug("Constructor: Create dossier sync data failed: " + e.getMessage());
        }
    }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AttachmentDto implements Serializable {

        @JsonProperty("TenTepDinhKem")
        private String name;
        @JsonProperty("IsDeleted")
        private Boolean isDeleted;
        @JsonProperty("MaThanhPhanHoSo")
        private String code;
        @JsonProperty("DuongDanTaiTepTin")
        private String fileLink;
        @JsonProperty("DuocSoHoa")
        private String digitized;
        @JsonProperty("DuocTaiSuDung")
        private String reused;
        @JsonProperty("DuocLayTuKhoDMQG")
        private String takeFromStockDMQG;
        @JsonProperty("MaKetQuaThayThe")
        private String resultCode;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FeeDto implements Serializable {

        @JsonProperty("TenPhiLePhi")
        private String name;
        @JsonProperty("MaPhiLePhi")
        private String code;
        @JsonProperty("HinhThucThu")
        private String feeMethod;
        @JsonProperty("Gia")
        private Integer price;
        @JsonProperty("LoaiPhiLePhi")
        private String type;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AttachmentDifDto implements Serializable {

        @JsonProperty("TenGiayTo")
        private String name;
        @JsonProperty("SoLuong")
        private Integer quantity;
        @JsonProperty("LoaiGiayTo")
        private Integer type;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AddDocumentDto implements Serializable {

        @JsonProperty("HoSoBoSungId")
        private String id;
        @JsonProperty("NguoiYeuCauBoSung")
        private String name;
        @JsonProperty("NoiDungBoSung")
        private String content;
        @JsonProperty("NgayBoSung")
        private String addDate;
        @JsonProperty("NguoiTiepNhanBoSung")
        private String reciever;
        @JsonProperty("ThongTinTiepNhan")
        private String info;
        @JsonProperty("NgayTiepNhanBoSung")
        private String recieveDate;
        @JsonProperty("TrangThaiBoSung")
        private String status;
        @JsonProperty("DanhSachGiayToBoSung")
        private List<AttachmentDto> attachments;
        @JsonProperty("DanhSachLePhiBoSung")
        private List<FeeDto> fees;
        @JsonProperty("NgayHenTraTruoc")
        private String beforeDate;
        @JsonProperty("NgayHenTraMoi")
        private String afterDate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ResultDto implements Serializable {

        @JsonProperty("TenGiayTo")
        private String name;
        
        @JsonProperty("MaThanhPhanHoSo")
        private String code;
        
        @JsonProperty("GiayToId")
        private String id;
        
        @JsonProperty("DuongDanTepTinKetQua")
        private String fileLink;
        
        @JsonProperty("MaGiayToKetQua")
        private String resultCode = "KQ.G18.000025";
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OwnerAuthenticatedDto implements Serializable {

        @JsonProperty("LoaiDinhDanh")
        private String identityType;

        @JsonProperty("SoDinhDanh")
        private String identityNumber;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ConnectedDatabaseDto implements Serializable {

        @JsonProperty("MaCSDL")
        private String codeCSDL = "1";
    }
}
