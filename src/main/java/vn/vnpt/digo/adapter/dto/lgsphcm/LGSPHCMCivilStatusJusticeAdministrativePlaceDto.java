/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class LGSPHCMCivilStatusJusticeAdministrativePlaceDto implements Serializable{
            public String idDanhMuc;
            public String maLoaiDonViHanhChinh;
            public String maDonViHanhChinh;
            public String tenDonViHanhChinh;
            public String tenDayDuDonViHanhChinh;
            public int maDonViHanhChinhCha;
            public int maTinhThanh;
            public String tenTinhThanh;
            public String donViQuanLy;
            public String trangThaiHieuLuc;
}
