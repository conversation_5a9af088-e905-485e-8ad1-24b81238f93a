/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PPConfirmDto implements Serializable {
    
    @JsonProperty("LoaiBanTin")
    private String loaiBanTin;
    @JsonProperty("MaLoi")
    private String maLoi;
    @JsonProperty("MaDoiTac")
    private String maDoiTac;
    @JsonProperty("MaThamChieu")
    private String maThamChieu;
    @JsonProperty("SoTien")
    private String soTien;
    @JsonProperty("MaTienTe")
    private String maTienTe;
    @JsonProperty("MaGiaoDich")
    private String maGiaoDich;
    @JsonProperty("MaNganHang")
    private String maNganHang;
    @JsonProperty("ThoiGianGD")
    private String thoiGianGD;
    @JsonProperty("ThongTinGiaoDich")
    private String thongTinGiaoDich;
    @JsonProperty("MaXacThuc")
    private String maXacThuc;
    
    //E-Payment HCM LGSP
    @JsonProperty("paygate")
    String paygate;
    
    @JsonProperty("requestCode")
    String requestCode;
    
    @JsonProperty("orderId")
    String orderId;
    
    @JsonProperty("orderPayId")
    String orderPayId;
    
    @JsonProperty("amount")
    Double amount;
    
    @JsonProperty("orderInfo")
    String orderInfo;
    
    @JsonProperty("payTransId")
    String payTransId;
    
    @JsonProperty("payDate")
    String payDate;
    
    @JsonProperty("checksum")
    String checksum;
    
    @JsonProperty("type")
    String type;
}
