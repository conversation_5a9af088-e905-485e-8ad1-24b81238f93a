package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.hpsf.Decimal;
import org.apache.poi.ss.formula.functions.Code;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DanhSachLePhi implements Serializable {
    @JsonProperty("TenPhiLePhi")
    private String tenPhiLePhi;
    @JsonProperty("MaPhiLePhi")
    private String maPhiLePhi;
    @JsonProperty("HinhThucThu")
    private String hinhThucThu;
    @JsonProperty("Gia")
    private Long gia;
    @JsonProperty("LoaiPhiLePhi")
    private String loaiPhiLePhi;
}
