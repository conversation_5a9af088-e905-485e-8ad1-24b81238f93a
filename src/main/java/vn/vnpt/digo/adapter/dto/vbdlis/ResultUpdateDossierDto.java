/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.dto.vbdlis;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.pojo.vbdlis.DanhSachGiayTo;
import vn.vnpt.digo.adapter.pojo.vbdlis.ThongTinNguoiNopDon;
import vn.vnpt.digo.adapter.pojo.vbdlis.ThongTinQuyTrinh;
import vn.vnpt.digo.adapter.pojo.vbdlis.ThongTinThuaDat;
import vn.vnpt.digo.adapter.util.ParamName;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultUpdateDossierDto implements Serializable {

    @ParamName("config-id")
    @JsonProperty("ConfigId")
    private ObjectId configId;

    @JsonProperty("AgencyId")
    @ParamName("agency-id")
    private ObjectId agencyId;

    @JsonProperty("SubsystemId")
    @ParamName("subsystem-id")
    private ObjectId subsystemId;

    @NotNull
    @JsonProperty("SoBienNhan")
    public String soBienNhan;

    @NotNull
    @JsonProperty("NgayTra")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    public Date ngayTra;

}
