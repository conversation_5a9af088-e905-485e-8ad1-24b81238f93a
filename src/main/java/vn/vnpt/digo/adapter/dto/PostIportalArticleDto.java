/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;


/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PostIportalArticleDto implements Serializable {
    
    @NotNull
    private String iportalId;
    
    @NotNull
    private String iportalUrl;
    
    @NotNull
    private ObjectId agencyId;
    
    @NotNull
    private ObjectId imageId;
    
    @NotNull
    private List<String> category;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date publishedDate;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date endDate;
    
    private Boolean publish;
    
    private Integer orderNumber;
    
    private Boolean isHot;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date hotEndDate;
    
    private Boolean isNew;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date newEndDate;
    
    @NotNull
    private String title;
    
    @NotNull
    private String summary;
    
    @NotNull
    private String content;
}
