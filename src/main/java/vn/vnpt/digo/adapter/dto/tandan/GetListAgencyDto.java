
package vn.vnpt.digo.adapter.dto.tandan;

import java.io.Serializable;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.ErrorTanDan;
import vn.vnpt.digo.adapter.pojo.ListAgencyReport;
import vn.vnpt.digo.adapter.pojo.ListDossierReportByAgencyId;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetListAgencyDto implements Serializable {
    private ErrorTanDan error;
    
    private ArrayList<ListAgencyReport> data;
}
