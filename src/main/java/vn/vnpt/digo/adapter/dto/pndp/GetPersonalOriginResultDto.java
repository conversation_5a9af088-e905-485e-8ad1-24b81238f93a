package vn.vnpt.digo.adapter.dto.pndp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.dto.GetConfigDto;
import vn.vnpt.digo.adapter.pojo.CategoryResult;
import vn.vnpt.digo.adapter.pojo.CategoryResultResponse;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetPersonalOriginResultDto  implements Serializable  {
    @JsonProperty("DanhSachGiayToKetQua")
    private List<CategoryResultResponse> DanhSachGiayToKetQua;
}
