package vn.vnpt.digo.adapter.dto.khcn;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class K<PERSON>NResultDto implements Serializable {

    private String status;
    private String message;
    private String errorCode;
    private String errorMessage;
    private Object data;

    public KHCNResultDto(String status, String message) {
        this.status = status;
        this.message = message;
    }

    public KHCNResultDto(String status, String message, String errorCode, String errorMessage) {
        this.status = status;
        this.message = message;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    @Override
    public String toString() {
        return "KHCNResultDto{" +
                "status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", data=" + data +
                '}';
    }
}
