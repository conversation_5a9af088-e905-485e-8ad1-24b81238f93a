package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * use for api DVCQG PAKN: DongBoKetQuaTraLoiPAKN_CongKhaiLai
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NpsUpdateAgainResultItemDto implements Serializable {

    @JsonProperty("MaSoPAKN")
    private String maSoPAKN;

    @JsonProperty("PhanLoaiPAKN")
    private String phanLoaiPAKN;

    @JsonProperty("LinhVucPAKN")
    private String linhVucPAKN;

    @JsonProperty("NoiDungTraLoi")
    private String noiDungTraLoi;

    @JsonProperty("TepVanBanTraLoi")
    private List<FileDto> file;

    @JsonProperty("NgayTraLoi")
    private String ngayTraLoi;

    @JsonProperty("NguoiTraLoi")
    private String nguoiTraLoi;

    @JsonProperty("DoiTuongGuiPakn")
    private String doiTuongGuiPakn;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FileDto {

        @JsonProperty("TenTepDinhKem")
        private String filename;

        @JsonProperty("Base64")
        private String base64;

        @JsonProperty("ThoiGianTaiLen")
        @DateTimeFormat(pattern = "yyyyMMddHHmmss")
        private Date updatedDate;
    }
}
