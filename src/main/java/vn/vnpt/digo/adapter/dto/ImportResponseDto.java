/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportResponseDto {
    private int successRows;

    private String successMessage;

    private String insertedRows;

    private List<String> errorMessages = new ArrayList<>();

}
