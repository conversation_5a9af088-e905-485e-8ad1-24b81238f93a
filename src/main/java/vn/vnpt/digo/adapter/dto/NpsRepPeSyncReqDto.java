package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsRepPeSyncReqDto implements Serializable {

    @NotNull
    private String session;

    @NotNull
    @JsonProperty("madonvi")
    private String agencyCode;

    @NotNull
    private String service;

    @NotNull
    @JsonProperty("DanhSachDongBoKetQuaTraLoiPAKN")
    private List<NpsRepPeSyncDataDto> data;

    public NpsRepPeSyncReqDto(String session, String agencyCode, String service, NpsRepPeSyncDataDto input) {
        this.session = session;
        this.agencyCode = agencyCode;
        this.service = service;
        this.data = new ArrayList<>();
        this.data.add(input);
    }
}
