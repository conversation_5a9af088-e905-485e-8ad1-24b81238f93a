package vn.vnpt.digo.adapter.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EFormCloneDto implements Serializable{
    
    private ObjectId _id;

    private String type;
    
    private List<String>  tags;
    
    private String owner;
    
    private String deleted;
    
    private List<Object> components;

    private String title;

    private String name;

    private String machineName;
    
    private String display;
    
    private String path;
    
   private  List<Object>  access;
   
   private  List<Object> submissionAccess;
  
   private Date created;
   
   private Date modified;
   
   private int __v;
    
}
