/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LGSPHCMDossierComponentDto implements Serializable {
    
    private String situation;

    private List<LGSPHCMDocumentDto> documents;

    @JsonSetter("TRUONGHOP")
    public void mapSituation(String situation) {
        this.situation = situation;
    }

    @JsonSetter("GIAYTO")
    public void mapDocuments(List<LGSPHCMDocumentDto> documents) {
        this.documents = documents;
    }
}
