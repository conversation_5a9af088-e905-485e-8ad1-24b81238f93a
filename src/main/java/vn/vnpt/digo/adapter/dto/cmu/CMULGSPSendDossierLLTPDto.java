package vn.vnpt.digo.adapter.dto.cmu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CMULGSPSendDossierLLTPDto implements Serializable {
    // Thông tin xác thực
    private String authKey;
    private String maTinh;
    private String tenTinh;
    // Thông tin hồ sơ
    private String maHoSoMCDT;
    @DateTimeFormat(pattern = "dd/MM/yyyy")
    private String ngayTiepNhan;
    private Long nguonDangKy;
    private String tenNguonDangKy;
    private ToKhai toKhai;
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ThongTinThuongTru implements Serializable{
        private String maTinhThanh;
        private String maQuanHuyen;
        private String maPhuongXa;
        private String tenTinhThanh;
        private String tenQuanHuyen;
        private String tenPhuongXa;
        private String dcChiTiet;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NoiSinhNuq implements Serializable{
        private String maTinhThanh;
        private String tenTinhThanh;
    }
    public ThongTinThuongTru convertThongTinThuonTru(String maTinh, String tenTinh, String maHuyen, String tenHuyen, String maXa, String tenXa, String dcChiTiet){
        ThongTinThuongTru thongTinThuongTru = new ThongTinThuongTru();
        thongTinThuongTru.maTinhThanh=stringIsNull(maTinh);
        thongTinThuongTru.maQuanHuyen=stringIsNull(maHuyen);
        thongTinThuongTru.maPhuongXa=stringIsNull(maXa);
        thongTinThuongTru.tenTinhThanh=stringIsNull(tenTinh);
        thongTinThuongTru.tenQuanHuyen=stringIsNull(tenHuyen);
        thongTinThuongTru.tenPhuongXa=stringIsNull(tenXa);
        thongTinThuongTru.dcChiTiet=stringIsNull(dcChiTiet);
        return thongTinThuongTru;
    }
    public NoiSinhNuq convertNoiSinhNuq(String maTinh, String tenTinh, String maHuyen, String tenHuyen, String maXa, String tenXa, String dcChiTiet){
        NoiSinhNuq noiSinhNuq = new NoiSinhNuq();
        noiSinhNuq.maTinhThanh=stringIsNull(maTinh);
        noiSinhNuq.tenTinhThanh=stringIsNull(tenTinh);
        return noiSinhNuq;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NoiSinh implements Serializable{
        private String maTinhThanh;
        private String tenTinhThanh;
    }
    public NoiSinh convertNoiSinh(String maTinh, String tenTinh, String maHuyen, String tenHuyen, String maXa, String tenXa){
        NoiSinh noiSinh = new NoiSinh();
        noiSinh.maTinhThanh=stringIsNull(maTinh);
        noiSinh.tenTinhThanh=stringIsNull(tenTinh);
        return noiSinh;
    }
    public String convertGioiTinh(Long maGioiTinh){
        String tenGioiTnh =null;
        if(maGioiTinh==null){
            return "";
        }
        if (maGioiTinh == 1){
            tenGioiTnh="Nam";
        }else{
            tenGioiTnh="Nữ";
        }
        return tenGioiTnh;
    }
    public String convertLoaiGiayTo(Long maLoaiGiayTo){
        String tenLoaiGiayTo =null;
        if(maLoaiGiayTo==null){
            return "";
        }
        if(maLoaiGiayTo == 1){
            tenLoaiGiayTo = "Căn cước công dân";
        }else if(maLoaiGiayTo == 2){
            tenLoaiGiayTo = "Chứng minh nhân dân";
        }else if(maLoaiGiayTo == 3){
            tenLoaiGiayTo = "Định danh cá nhân";
        }else if(maLoaiGiayTo == 4){
            tenLoaiGiayTo = "Hộ chiếu";
        }else {
            tenLoaiGiayTo =null;
        }
        return tenLoaiGiayTo;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class QuaTrinhCuTru implements Serializable{
        private String tuNgay;
        private String denNgay;
        private String noiCuTru;
        private String ngheNghiep;
        private String noiLamViec;
    }
    public List<QuaTrinhCuTru> convertQuaTrinhCuTru(List<CMULGSPDossierLLTPDto.QuaTrinhCuTru> listQuaTrinhCuTruEform){
        List<QuaTrinhCuTru> listQuaTrinhCuTru = new ArrayList<>();
        if(listQuaTrinhCuTruEform!=null){
            listQuaTrinhCuTruEform.forEach(quaTrinhCuTruEform -> {
                QuaTrinhCuTru  quaTrinhCuTru = new QuaTrinhCuTru();
                quaTrinhCuTru.tuNgay = stringIsNull(quaTrinhCuTruEform.getTuNgay());
                quaTrinhCuTru.denNgay = stringIsNull(quaTrinhCuTruEform.getDenNgay());
                quaTrinhCuTru.noiCuTru = stringIsNull(quaTrinhCuTruEform.getNoiCuTru());
                quaTrinhCuTru.ngheNghiep = stringIsNull(quaTrinhCuTruEform.getNgheNghiep());
                quaTrinhCuTru.noiLamViec = stringIsNull(quaTrinhCuTruEform.getNoiLamViec());
                listQuaTrinhCuTru.add(quaTrinhCuTru);
            });
        }
        return listQuaTrinhCuTru;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileHoSo implements Serializable{
        private String tenLoaiFile;
        private String tenFile;
        private String noiDungFile;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ToKhai implements Serializable{
        // Thông tin chung
        private Long loaiPhieu;
        private Long yeuCauCDNCV;
        private Long maMucDich;
        private String tenMucDich;
        private String mucDichKhac;
        private String mucDich;
        private String soLuongCap;
        private String ngayHenTra;

        // Thông tin người yêu cầu cấp phiếu LLTP
        private String nycHoTen;
        private String nycTenGoiKhac;
        private Long nycGioiTinh;
        private String nycTenGioiTinh;
        private String nycNgaySinh;
        private String nycDoiTuongNopPhi;
        private String nycTenDoiTuongNopPhi;
        private ThongTinThuongTru nycThuongTru;
        private ThongTinThuongTru nycTamTru;
        private String nycDienThoai;
        private String nycEmail;
        private Long nycLoaiGiayTo;
        private String nycTenLoaiGiayTo;
        private String nycSoGiayTo;
        private String nycNgayCapGiayTo;
        private String nycNoiCapGiayTo;
        private String nycQuocTich;
        private String nycTenQuocTich;
        private String nycDanToc;
        private String nycTenDanToc;
        private String nycHoTenCha;
        private String chaNgaySinh;
        private Long chaLoaiGiayTo;
        private String chaTenLoaiGiayTo;
        private String chaSoGiayTo;
        private String nycHoTenMe;
        private String meNgaySinh;
        private Long meLoaiGiayTo;
        private String meTenLoaiGiayTo;
        private String meSoGiayTo;
        private String nycHoTenVoChong;
        private String voChongNgaySinh;
        private Long voChongLoaiGiayTo;
        private String voChongTenLoaiGiayTo;
        private String voChongSoGiayTo;
        private NoiSinh nycNoiSinh;
        private Long uyQuyen;
        private String thongTinAnTich;
        private List<FileHoSo> fileHoSo;
        private Long nycNoiSinhNuocNgoai;

        // Thông tin người được ủy quyền yêu cầu cấp phiếu LLTP (chỉ dùng khi trường uyQuyen = 1)
        private String uyQuyenHoTen;
        private String nuqTenGoiKhac;
        private Long nuqGioiTinh;
        private String nuqNgaySinh;
        private NoiSinhNuq nuqNoiSinh;
        private ThongTinThuongTru nuqThuongTruChiTiet;
        private String nuqThuongTruDVHC;
        private String nuqDienThoai;
        private Long nuqLoaiGiayto;
        private String nuqSoGiayTo;
        //        @DateTimeFormat(pattern = "dd/mm/yyyy")
        private String nuqNgayCapGiayTo;
        private String nuqNoiCapGiayTo;
        private String nuqEmail;
        private String nuqQuocTich;
        private String nuqTenQuocTich;
        private String nuqDanToc;
        private String nuqTenDanToc;
        private String nyqQuanHe;

        // Thông tin quá trình cư trú của người yêu cầu cấp phiếu LLTP (dạng list nhiều thông tin quá trình cư trú)
        private List<QuaTrinhCuTru> nycCuTru;
    }



    public ToKhai convetTokhai(CMULGSPDossierLLTPDto dossier, List<FileHoSo> fileHoSo){
        CMULGSPDossierLLTPDto.ToKhaiEform toKhaiEform = dossier.getEForm().getData();

        CMULGSPDossierLLTPDto.LabelAndValue tinhThanhThuongTru = toKhaiEform.getTinhThanhThuongTru();
        CMULGSPDossierLLTPDto.LabelAndValue quanHuyenThuongTru= toKhaiEform.getQuanHuyenThuongTru();
        CMULGSPDossierLLTPDto.LabelAndValue phuongXaThuongTru= toKhaiEform.getXaPhuongThuongTru();
        String dcChiTietThuongTru=toKhaiEform.getDcChiTietThuongTru();
        String tinhThanhThuongTruValue = "";
        String quanHuyenThuongTruValue = "";
        String phuongXaThuongTruValue = "";

        String tinhThanhThuongTruTen = "";
        String quanHuyenThuongTruTen = "";
        String phuongXaThuongTruTen = "";
        if(tinhThanhThuongTru!=null){
            if(tinhThanhThuongTru.getValue()!=null){
                tinhThanhThuongTruTen = tinhThanhThuongTru.getLabel();
                tinhThanhThuongTruValue = tinhThanhThuongTru.getValue();
                tinhThanhThuongTruValue = tinhThanhThuongTruValue.substring(tinhThanhThuongTruValue.length() -2);
            }
        }
        if(quanHuyenThuongTru!=null){
            if(quanHuyenThuongTru.getValue()!=null){
                quanHuyenThuongTruTen = quanHuyenThuongTru.getLabel();
                quanHuyenThuongTruValue = quanHuyenThuongTru.getValue();
                quanHuyenThuongTruValue = quanHuyenThuongTruValue.substring(quanHuyenThuongTruValue.length() -3);
            }
        }
        if(phuongXaThuongTru!=null){
            if(phuongXaThuongTru.getValue()!=null){
                phuongXaThuongTruTen = phuongXaThuongTru.getLabel();
                phuongXaThuongTruValue = phuongXaThuongTru.getValue();
                phuongXaThuongTruValue = phuongXaThuongTruValue.substring(phuongXaThuongTruValue.length() -5);
            }
        }

        CMULGSPDossierLLTPDto.LabelAndValue tinhThanhTamTru= toKhaiEform.getTinhThanhTamTru();
        CMULGSPDossierLLTPDto.LabelAndValue quanHuyenTamTru= toKhaiEform.getQuanHuyenTamTru();
        CMULGSPDossierLLTPDto.LabelAndValue phuongXaTamTru= toKhaiEform.getXaPhuongTamTru();
        String dcChiTietTamTru= toKhaiEform.getDcChiTietTamTru();
        String tinhThanhTamTruValue = "";
        String quanHuyenTamTruValue = "";
        String phuongXaTamTruValue = "";

        String tinhThanhTamTruTen = "";
        String quanHuyenTamTruTen = "";
        String phuongXaTamTruTen = "";
        if(tinhThanhTamTru!=null){
            if(tinhThanhTamTru.getValue()!=null){
                tinhThanhTamTruTen = tinhThanhTamTru.getLabel();
                tinhThanhTamTruValue = tinhThanhTamTru.getValue();
                tinhThanhTamTruValue = tinhThanhTamTruValue.substring(tinhThanhTamTruValue.length() -2);
            }
        }
        if(quanHuyenTamTru!=null){
            if(quanHuyenTamTru.getValue()!=null){
                quanHuyenTamTruTen = quanHuyenTamTru.getLabel();
                quanHuyenTamTruValue = quanHuyenTamTru.getValue();
                quanHuyenTamTruValue = quanHuyenTamTruValue.substring(quanHuyenTamTruValue.length() -3);
            }
        }
        if(phuongXaTamTru!=null){
            if(phuongXaTamTru.getValue()!=null){
                phuongXaTamTruTen = phuongXaTamTru.getLabel();
                phuongXaTamTruValue = phuongXaTamTru.getValue();
                phuongXaTamTruValue = phuongXaTamTruValue.substring(phuongXaTamTruValue.length() -5);
            }
        }
        CMULGSPDossierLLTPDto.LabelAndValue tinhThanhNoiSinh= toKhaiEform.getTinhThanhNoiSinh();
        CMULGSPDossierLLTPDto.LabelAndValue quanHuyenNoiSinh= toKhaiEform.getQuanHuyenNoiSinh();
        CMULGSPDossierLLTPDto.LabelAndValue phuongXaNoiSinh= toKhaiEform.getXaPhuongNoiSinh();
        String tinhThanhNoiSinhValue = "";
        String quanHuyenNoiSinhValue = "";
        String phuongXaNoiSinhValue = "";

        String tinhThanhNoiSinhTen = "";
        String quanHuyenNoiSinhTen = "";
        String phuongXaNoiSinhTen = "";
        if(tinhThanhNoiSinh!=null){
            if(tinhThanhNoiSinh.getValue()!=null){
                tinhThanhNoiSinhTen = tinhThanhNoiSinh.getLabel();
                tinhThanhNoiSinhValue = tinhThanhNoiSinh.getValue();
                tinhThanhNoiSinhValue = tinhThanhNoiSinhValue.substring(tinhThanhNoiSinhValue.length() -2);
            }
        }
        if(quanHuyenNoiSinh!=null){
            if(quanHuyenNoiSinh.getValue()!=null){
                quanHuyenNoiSinhTen = quanHuyenNoiSinh.getLabel();
                quanHuyenNoiSinhValue = quanHuyenNoiSinh.getValue();
                quanHuyenNoiSinhValue = quanHuyenNoiSinhValue.substring(quanHuyenNoiSinhValue.length() -3);
            }
        }
        if(phuongXaNoiSinh!=null){
            if(phuongXaNoiSinh.getValue()!=null){
                phuongXaNoiSinhTen = phuongXaNoiSinh.getLabel();
                phuongXaNoiSinhValue = phuongXaNoiSinh.getValue();
                phuongXaNoiSinhValue = phuongXaNoiSinhValue.substring(phuongXaNoiSinhValue.length() -5);
            }
        }


        CMULGSPDossierLLTPDto.LabelAndValue tinhThanhNoiSinhNuq= toKhaiEform.getTinhThanhNoiSinhNuq();
        CMULGSPDossierLLTPDto.LabelAndValue phuongXaNoiSinhNuq= toKhaiEform.getXaPhuongNoiSinhNuq();
        CMULGSPDossierLLTPDto.LabelAndValue quanHuyenNoiSinhNuq= toKhaiEform.getQuanHuyenNoiSinhNuq();
        String dcChiTietNoiSinhNuq= toKhaiEform.getDcChiTietNoiSinhNuq();
        String tinhThanhNoiSinhNuqValue = "";
        String quanHuyenNoiSinhNuqValue = "";
        String phuongXaNoiSinhNuqValue = "";

        String tinhThanhNoiSinhNuqTen = "";
        String quanHuyenNoiSinhNuqTen = "";
        String phuongXaNoiSinhNuqTen = "";
        if(tinhThanhNoiSinhNuq!=null){
            if(tinhThanhNoiSinhNuq.getValue()!=null){
                tinhThanhNoiSinhNuqTen = tinhThanhNoiSinhNuq.getLabel();
                tinhThanhNoiSinhNuqValue = tinhThanhNoiSinhNuq.getValue();
                tinhThanhNoiSinhNuqValue = tinhThanhNoiSinhNuqValue.substring(tinhThanhNoiSinhNuqValue.length() -2);
            }
        }
        if(quanHuyenNoiSinhNuq!=null){
            if(quanHuyenNoiSinhNuq.getValue()!=null){
                quanHuyenNoiSinhNuqTen = quanHuyenNoiSinhNuq.getLabel();
                quanHuyenNoiSinhNuqValue = quanHuyenNoiSinhNuq.getValue();
                quanHuyenNoiSinhNuqValue = quanHuyenNoiSinhNuqValue.substring(quanHuyenNoiSinhNuqValue.length() -3);
            }
        }
        if(phuongXaNoiSinhNuq!=null){
            if(phuongXaNoiSinhNuq.getValue()!=null){
                phuongXaNoiSinhNuqTen = phuongXaNoiSinhNuq.getLabel();
                phuongXaNoiSinhNuqValue = phuongXaNoiSinhNuq.getValue();
                phuongXaNoiSinhNuqValue = phuongXaNoiSinhNuqValue.substring(phuongXaNoiSinhNuqValue.length() -5);
            }
        }

        CMULGSPDossierLLTPDto.LabelAndValue tinhThanhThuongTruNuq= toKhaiEform.getTinhThanhThuongTruNuq();
        CMULGSPDossierLLTPDto.LabelAndValue quanHuyenThuongTruNuq= toKhaiEform.getQuanHuyenThuongTruNuq();
        CMULGSPDossierLLTPDto.LabelAndValue phuongXaThuongTruNuq= toKhaiEform.getXaPhuongThuongTruNuq();
        String dcChiTietThuongTruNuq= toKhaiEform.getDcChiTietThuongTruNuq();

        String tinhThanhThuongTruNuqValue = "";
        String quanHuyenThuongTruNuqValue = "";
        String phuongXaThuongTruNuqValue = "";

        String tinhThanhThuongTruNuqTen = "";
        String quanHuyenThuongTruNuqTen = "";
        String phuongXaThuongTruNuqTen = "";
        if(tinhThanhThuongTruNuq!=null){
            if(tinhThanhThuongTruNuq.getValue()!=null){
                tinhThanhThuongTruNuqTen=tinhThanhThuongTruNuq.getLabel();
                tinhThanhThuongTruNuqValue = tinhThanhThuongTruNuq.getValue();
                tinhThanhThuongTruNuqValue = tinhThanhThuongTruNuqValue.substring(tinhThanhThuongTruNuqValue.length() -2);
            }
        }
        if(quanHuyenThuongTruNuq!=null){
            if(quanHuyenThuongTruNuq.getValue()!=null){
                quanHuyenThuongTruNuqTen=quanHuyenThuongTruNuq.getLabel();
                quanHuyenThuongTruNuqValue = quanHuyenThuongTruNuq.getValue();
                quanHuyenThuongTruNuqValue = quanHuyenThuongTruNuqValue.substring(quanHuyenThuongTruNuqValue.length() -3);
            }
        }
        if(phuongXaThuongTruNuq!=null){
            if(phuongXaThuongTruNuq.getValue()!=null){
                phuongXaThuongTruNuqTen=phuongXaThuongTruNuq.getLabel();
                phuongXaThuongTruNuqValue = phuongXaThuongTruNuq.getValue();
                phuongXaThuongTruNuqValue = phuongXaThuongTruNuqValue.substring(phuongXaThuongTruNuqValue.length() -5);
            }
        }



        ToKhai toKhai = new ToKhai();
        toKhai.loaiPhieu=toKhaiEform.getLoaiPhieu();
        toKhai.yeuCauCDNCV=toKhaiEform.getYeuCauCDNCV();
        toKhai.maMucDich=toKhaiEform.getMaMucDich();
        toKhai.tenMucDich=stringIsNull(toKhaiEform.getTenMucDich());
        toKhai.mucDichKhac=stringIsNull(toKhaiEform.getMucDichKhac());
        toKhai.mucDich=stringIsNull(toKhaiEform.getMucDich());
        toKhai.soLuongCap=stringIsNull(toKhaiEform.getSoLuongCap());

        Date dateHenTra = dossier.getAppointmentDate();
        String dateStr="";
        if(dateHenTra!=null){
            LocalDate localDate = dateHenTra.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            dateStr = localDate.format(formatter);
        }
        // Nếu ngày hẹn trả
        toKhai.ngayHenTra = stringIsNull(dateStr);

        // Thông tin người yêu cầu cấp phiếu LLTP
        toKhai.nycHoTen=stringIsNull(toKhaiEform.getNycHoTen());
        toKhai.nycTenGoiKhac=stringIsNull(toKhaiEform.getNycTenGoiKhac());
        toKhai.nycGioiTinh=toKhaiEform.getNycGioiTinh();
        toKhai.nycTenGioiTinh=stringIsNull(convertGioiTinh(toKhaiEform.getNycGioiTinh()));
        toKhai.nycNgaySinh=stringIsNull(toKhaiEform.getNycNgaySinh());
        toKhai.nycThuongTru=convertThongTinThuonTru(tinhThanhThuongTruValue,tinhThanhThuongTruTen,quanHuyenThuongTruValue,quanHuyenThuongTruTen,phuongXaThuongTruValue,phuongXaThuongTruTen,dcChiTietThuongTru);
        toKhai.nycTamTru=convertThongTinThuonTru(tinhThanhTamTruValue,tinhThanhTamTruTen,quanHuyenTamTruValue,quanHuyenTamTruTen,phuongXaTamTruValue,phuongXaTamTruTen,dcChiTietTamTru);
        toKhai.nycDienThoai=stringIsNull(toKhaiEform.getNycDienThoai());
        toKhai.nycEmail=stringIsNull(toKhaiEform.getNycEmail());
        toKhai.nycLoaiGiayTo=toKhaiEform.getNycLoaiGiayto(); // check lại
        toKhai.nycTenLoaiGiayTo=stringIsNull(convertLoaiGiayTo(toKhaiEform.getNycLoaiGiayto()));
        toKhai.nycSoGiayTo=stringIsNull(toKhaiEform.getNycSoGiayTo());
        toKhai.nycNgayCapGiayTo=stringIsNull(toKhaiEform.getNycNgayCapGiayTo());
        toKhai.nycNoiCapGiayTo=stringIsNull(toKhaiEform.getNycNoiCapGiayTo());
        toKhai.nycQuocTich=stringIsNull(toKhaiEform.getNycQuocTich().getValue());
        toKhai.nycTenQuocTich=stringIsNull(toKhaiEform.getNycQuocTich()!=null?toKhaiEform.getNycQuocTich().getLabel():null);
        toKhai.nycDanToc=stringIsNull(toKhaiEform.getNycDanToc().getValue());
        toKhai.nycTenDanToc=stringIsNull(toKhaiEform.getNycDanToc().getLabel());
        toKhai.nycHoTenCha=stringIsNull(toKhaiEform.getNycHoTenCha());
        toKhai.chaNgaySinh=stringIsNull(toKhaiEform.getChaNgaySinh());
//        toKhai.chaLoaiGiayTo=toKhaiEform.getChaLoaiGiayTo();
//        toKhai.chaTenLoaiGiayTo=stringIsNull(convertLoaiGiayTo(toKhaiEform.getChaLoaiGiayTo()));
//        toKhai.chaSoGiayTo=stringIsNull(toKhaiEform.getChaSoGiayTo());
        toKhai.nycHoTenMe=stringIsNull(toKhaiEform.getNycHoTenMe());
        toKhai.meNgaySinh=stringIsNull(toKhaiEform.getMeNgaySinh());
//        toKhai.meLoaiGiayTo=toKhaiEform.getMeLoaiGiayTo();
//        toKhai.meTenLoaiGiayTo=stringIsNull(convertLoaiGiayTo(toKhaiEform.getMeLoaiGiayTo()));
//        toKhai.meSoGiayTo=stringIsNull(toKhaiEform.getMeSoGiayTo());
        toKhai.nycHoTenVoChong=stringIsNull(toKhaiEform.getNycHoTenVoChong());
        toKhai.voChongNgaySinh=stringIsNull(toKhaiEform.getVoChongNgaySinh());
//        toKhai.voChongLoaiGiayTo=toKhaiEform.getVoChongLoaiGiayTo();
//        toKhai.voChongTenLoaiGiayTo=stringIsNull(convertLoaiGiayTo(toKhaiEform.getVoChongLoaiGiayTo()));
//        toKhai.voChongSoGiayTo=stringIsNull(toKhaiEform.getVoChongSoGiayTo());
        toKhai.nycNoiSinh=convertNoiSinh(tinhThanhNoiSinhValue,tinhThanhNoiSinhTen,quanHuyenNoiSinhValue,quanHuyenNoiSinhTen,phuongXaNoiSinhValue,phuongXaNoiSinhValue);
        toKhai.uyQuyen=toKhaiEform.getUyQuyen();
        toKhai.thongTinAnTich=stringIsNull(toKhaiEform.getThongTinAnTich());
        toKhai.fileHoSo=fileHoSo;

        // Thông tin người được ủy quyền yêu cầu cấp phiếu LLTP (chỉ dùng khi trường uyQuyen = 1)
        String tinhnoiSinhNuq = tinhThanhNoiSinhNuq!=null?tinhThanhNoiSinhNuq.getLabel():"";
        String huyennoiSinhNuq = quanHuyenNoiSinhNuq!=null?quanHuyenNoiSinhNuq.getLabel():"";
        String xanoiSinhNuq = phuongXaNoiSinhNuq!=null?phuongXaNoiSinhNuq.getLabel():"";

        String tinhThuongTruNuq = tinhThanhThuongTruNuq!=null?tinhThanhThuongTruNuq.getLabel():"";
        String huyenThuongTruNuq = quanHuyenThuongTruNuq!=null?quanHuyenThuongTruNuq.getLabel():"";
        String xaThuongTruNuq = phuongXaThuongTruNuq!=null?phuongXaThuongTruNuq.getLabel():"";

        String quocTichNuq = toKhaiEform.getNuqQuocTich()!=null?toKhaiEform.getNuqQuocTich().getValue():null;
        String danTocNuq = toKhaiEform.getNuqDanToc()!=null?toKhaiEform.getNuqDanToc().getValue():null;

        String tenQuocTichNuq = toKhaiEform.getNuqQuocTich()!=null?toKhaiEform.getNuqQuocTich().getLabel():null;
        String tenDanTocNuq = toKhaiEform.getNuqDanToc()!=null?toKhaiEform.getNuqDanToc().getLabel():null;

        toKhai.uyQuyenHoTen=stringIsNull(toKhaiEform.getUyQuyenHoTen());
        toKhai.nuqTenGoiKhac=stringIsNull(toKhaiEform.getNuqTenGoiKhac());
        toKhai.nuqGioiTinh=toKhaiEform.getNuqGioiTinh();
        toKhai.nuqNgaySinh=stringIsNull(toKhaiEform.getNuqNgaySinh());
//        toKhai.nuqNoiSinh=convertNoiSinhNuq(tinhThanhNoiSinhNuqValue,tinhnoiSinhNuq,quanHuyenNoiSinhNuqValue,huyennoiSinhNuq,phuongXaNoiSinhNuqValue,xanoiSinhNuq,dcChiTietNoiSinhNuq);
        toKhai.nuqThuongTruChiTiet=convertThongTinThuonTru(tinhThanhThuongTruNuqValue,tinhThuongTruNuq,quanHuyenThuongTruNuqValue,huyenThuongTruNuq,phuongXaThuongTruNuqValue,xaThuongTruNuq,dcChiTietThuongTruNuq);
        toKhai.nuqThuongTruDVHC=stringIsNull(toKhaiEform.getNuqThuongTruDVHC());
        toKhai.nuqDienThoai=stringIsNull(toKhaiEform.getNuqDienThoai());
        toKhai.nuqLoaiGiayto=toKhaiEform.getNuqLoaiGiayto();
        toKhai.nuqSoGiayTo=stringIsNull(toKhaiEform.getNuqSoGiayTo());
        toKhai.nuqNgayCapGiayTo=stringIsNull(toKhaiEform.getNuqNgayCapGiayTo());
        toKhai.nuqNoiCapGiayTo=stringIsNull(toKhaiEform.getNuqNoiCapGiayTo());
        toKhai.nuqEmail=stringIsNull(toKhaiEform.getNuqEmail());
//        toKhai.nuqQuocTich=stringIsNull(quocTichNuq);
//        toKhai.nuqTenQuocTich=stringIsNull(tenQuocTichNuq);
//        toKhai.nuqDanToc=stringIsNull(danTocNuq);
//        toKhai.nuqTenDanToc=stringIsNull(tenDanTocNuq);
        toKhai.nyqQuanHe=stringIsNull(toKhaiEform.getNyqQuanHe());

        // Thông tin quá trình cư trú của người yêu cầu cấp phiếu LLTP (dạng list nhiều thông tin quá trình cư trú)
        toKhai.nycCuTru = convertQuaTrinhCuTru(toKhaiEform.getNycCuTru());
        return toKhai;
    }

    public CMULGSPSendDossierLLTPDto(CMULGSPDossierLLTPDto dossier, List<FileHoSo> fileHoSo, String password, String matinh, String tenTinh) {
        Logger logger = LoggerFactory.getLogger(CMULGSPSendDossierLLTPDto.class);
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            String today = LocalDate.now().format(formatter);

            CMULGSPDossierLLTPDto.ApplyMethod applyMethod = dossier.getApplyMethod();
            Long applyMethodId = applyMethod!=null?applyMethod.getId():null;
            Long nguonDangKy = Long.valueOf(1);
            String nguonDangKyTen = "Hồ sơ đăng ký trực tuyến qua cổng DVC của địa phương";
            if (applyMethodId == 1) {
                nguonDangKy = Long.valueOf(3);
                nguonDangKyTen = "Hồ sơ nộp trực tiếp tại Sở Tư pháp";
            }
            this.authKey = password;
            this.maTinh= matinh;
            this.tenTinh= tenTinh;
            this.maHoSoMCDT = stringIsNull(dossier.getCode());
            this.ngayTiepNhan = today;
            this.nguonDangKy= nguonDangKy;
            this.tenNguonDangKy = nguonDangKyTen;
            this.toKhai = convetTokhai(dossier,fileHoSo);
        }catch (Exception e) {
            logger.debug("Constructor: Create HGILGSPDossierLLTPDto_v2 sync data failed: " + e.getMessage());
        }
    }
    String stringIsNull(String value){
        return value!=null?value.toString():"";
    }
}
