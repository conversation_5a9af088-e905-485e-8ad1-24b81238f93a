package vn.vnpt.digo.adapter.dto.khdt_dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.NganhNgheKinhDoanhDto;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor

public class ChiTiethopTacXa {

    private String MaNoiBo;
    private String MaSoHTX;
    private String MaLoaiHinhHTX;
    private String LoaiHinhHTX;
    private String TenTiengViet;
    private String TenVietTat;
    private String TenNuocNgoai;
    private String NgayDangKyLanDau;
    private String NgayDangKyThayDoi;
    private int SoLanDangKyThayDoi;
    private List<DiaChiDTO> DiaChiTruSo;
    private List<NganhNgheKinhDoanhDTO> NganhNgheKinhDoanh;
}
