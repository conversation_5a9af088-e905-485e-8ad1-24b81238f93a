/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.ParametersType;
import vn.vnpt.digo.adapter.pojo.ListType;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParameterIntegerDto implements Serializable {
    
    private String key;
    
    private ParametersType type = ListType.INTEGER;
    
    private Integer value;
    
    private String description;
    
    public ParameterIntegerDto(String key, Integer value, String desc){
        this.key = key;
        this.value = value;
        this.description = desc;
    }
}
