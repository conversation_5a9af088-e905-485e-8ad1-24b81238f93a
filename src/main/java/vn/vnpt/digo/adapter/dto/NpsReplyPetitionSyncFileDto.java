package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsReplyPetitionSyncFileDto implements Serializable {

    @JsonProperty("TenTepDinhKem")
    private String filename;

    @JsonProperty("ChuoiMaHoa")
    private String source;

    @JsonProperty("ThoiGianTaiLen")
    @DateTimeFormat(pattern = "yyyyMMddHHmmss")
    private String time;
}
