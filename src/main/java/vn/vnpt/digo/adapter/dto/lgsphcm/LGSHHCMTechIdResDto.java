/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LGSHHCMTechIdResDto implements Serializable {
    @JsonProperty("error_code")
    private String errorCode;
    private List<Result> result;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result implements Serializable {
        
        @JsonProperty("TECHNICALID")
        private String TECHNICALID;
    }
}
