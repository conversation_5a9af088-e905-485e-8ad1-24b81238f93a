package vn.vnpt.digo.adapter.dto.industry_trade_bd;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcedureBasicDto implements Serializable {
    private String id;

    private String code;

    private ArrayList<TranslateName> translate;


    private SectorDto sector;

}
