package vn.vnpt.digo.adapter.dto.khcn;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
@JsonIgnoreProperties(ignoreUnknown = true)
public class KHCNHoSoRequestDto implements Serializable {

    @JsonProperty("maDonVi")
    @NotNull(message = "'maDonVi'")
    private String agencyCode;

    @JsonProperty("maHoSo")
    @NotEmpty(message = "'maHoSo'")
    private String code;

    @JsonProperty("maHoSoMCDT")
    private String maHoSoMCDT;

    @JsonProperty("maTTHC")
    @NotEmpty(message = "'maTTHC'")
    private String procedureCode;

    @JsonProperty("module")
    @NotEmpty(message = "'module'")
    private String module;

    @JsonProperty("ngayTiepNhan")
    @NotEmpty(message = "'ngayTiepNhan'")
    @Size(min = 19, max = 19, message = "'ngayTiepNhan'")
    private String acceptedDate;

    @JsonProperty("techId")
    @NotEmpty(message = "'techId'")
    private String techId;

    @JsonProperty("data")
    @NotEmpty(message = "'data'")
    private String data;

    @JsonProperty("fileDinhKem")
    private List<FileAttachment> attachment;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileAttachment implements Serializable {

        @JsonProperty("id")
        @NotEmpty(message = "'id'")
        private Integer id;

        @JsonProperty("loaiGiayTo")
        @NotEmpty(message = "'loaiGiayTo'")
        private Integer attachmentType;

        @JsonProperty("tenGiayTo")
        @NotEmpty(message = "'tenGiayTo'")
        private String attachmentName;

        @JsonProperty("tenTepDinhKem")
        @NotEmpty(message = "'tenTepDinhKem'")
        private String filename;

        @JsonProperty("huyGiayTo")
        private Integer canceled;

        @JsonProperty("duLieuTepDinhKem")
        private String filedata;

        @JsonProperty("size")
        private Long size;
    }
}
