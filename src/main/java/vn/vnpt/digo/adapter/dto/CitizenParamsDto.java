/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

/**
 *
 * <AUTHOR>
 */
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.CheckConfigParams;
import vn.vnpt.digo.adapter.util.ParamName;

@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigParams
public class CitizenParamsDto implements Serializable {
    @ParamName("config-id")
    private ObjectId configId;
    @ParamName("agency-id")
    private ObjectId agencyId;
    @ParamName("subsystem-id")
    private ObjectId subsystemId;
}
