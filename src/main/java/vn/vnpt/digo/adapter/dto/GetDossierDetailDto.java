package vn.vnpt.digo.adapter.dto;

import vn.vnpt.digo.adapter.dto.nps.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.ArrayList;

import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;

import vn.vnpt.digo.adapter.dto.IdNameDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.pojo.Translate;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetDossierDetailDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String code;
    private IdNameDto procedure;
    private ApplyMethodDto applyMethod;
    private ApplicantDto applicant;
    private AccepterDto accepter;
    private AgencyDto agency;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;
//    private IdCodeNameSimpleDto dossierTaskStatus;
    private DossierStatus dossierStatus;
    private IdCodeNameDto dossierReceivingKind;
    private List<FormFile> dossierFormFile;
    private List<AttachmentDto> attachment;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AgencyDto {
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String code;
        private ArrayList<Translate> name;
        private AgencyDto parent;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplyMethodDto {
        private Integer id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplicantDto {
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId userId;
        private EformData data;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AccepterDto {
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private String fullname;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EformData{
        private String fullname;
        private String identityNumber;
//        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
//        private Date identityDate;
//        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
//        private Date birthday;
        private String phoneNumber = "";
        private String email = "";
        private String fax = "";
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FormFile{
        private List<AttachmentDto> file;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AttachmentDto {

        private String filename;
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId group;
        @JsonSerialize(using = ToStringSerializer.class)
        private ObjectId id;
        private Long size;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DossierStatus {
        private Integer id;
        private ArrayList<Translate> name;
        private String comment;
        private String description;
        
    }
}
