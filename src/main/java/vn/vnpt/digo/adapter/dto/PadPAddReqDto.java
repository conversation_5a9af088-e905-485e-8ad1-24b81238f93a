/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.Serializable;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PadPAddReqDto implements Serializable {

    @JsonProperty("MaHoSo")
    private String MaHoSo;
    @JsonProperty("MaTTHC")
    private String MaTTHC;
    @JsonProperty("SoVanBan")
    private String SoVanBan;
    @JsonProperty("NgayNopHoSo")
    private String NgayNopHoSo;
    @JsonProperty("TenThuongNhan")
    private String TenThuongNhan;
    @JsonProperty("DiaChiDoanhNghiep")
    private EntpAddrDto DiaChiDoanhNghiep;
    @JsonProperty("DienThoai")
    private String DienThoai;
    @JsonProperty("Email")
    private String Email;
    @JsonProperty("Fax")
    private String Fax;
    @JsonProperty("MaSoThue")
    private String MaSoThue;
    @JsonProperty("NguoiLienHe")
    private String NguoiLienHe;
    @JsonProperty("SoDienThoaiNguoiLienHe")
    private String SoDienThoaiNguoiLienHe;
    @JsonProperty("TenChuongTrinhKhuyenMai")
    private String TenChuongTrinhKhuyenMai;
    @JsonProperty("HinhThucKhuyenMai")
    private String HinhThucKhuyenMai;
    @JsonProperty("ThoiGianKhuyenMaiTu")
    private String ThoiGianKhuyenMaiTu;
    @JsonProperty("ThoiGianKhuyenMaiDen")
    private String ThoiGianKhuyenMaiDen;
    @JsonProperty("HangHoaDichVuKhuyenMai")
    private String HangHoaDichVuKhuyenMai;
    @JsonProperty("SoLuongHangHoaDichVu")
    private String SoLuongHangHoaDichVu;
    @JsonProperty("KhachHang")
    private String KhachHang;
    @JsonProperty("CoCauGiaiThuong")
    private String CoCauGiaiThuong;
    @JsonProperty("TongGiaTriHangHoa")
    private String TongGiaTriHangHoa;
    @JsonProperty("NoiDungChiTiet")
    private String NoiDungChiTiet;
    @JsonProperty("TenCacThuongNhanCungThucHien")
    private String TenCacThuongNhanCungThucHien;
    @JsonProperty("TenTepDonDangKy")
    private String TenTepDonDangKy;
    @JsonProperty("TepDonDangKy")
    private String TepDonDangKy;
    @JsonProperty("TaiLieuNop")
    private List<DocsSubmitDto> TaiLieuNop;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocsSubmitDto implements Serializable {

        @JsonProperty("TenTepDinhKem")
        private String TenTepDinhKem;

        @JsonIgnore
        @JsonProperty("Base64")
        private String Base64;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EntpAddrDto implements Serializable {

        @JsonProperty("MaTinh")
        private String MaTinh;
        @JsonProperty("MaHuyen")
        private String MaHuyen;
        @JsonProperty("MaXa")
        private String MaXa;
        @JsonProperty("DiaChiChiTiet")
        private String DiaChiChiTiet;
    }

    @Override
    public String toString() {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(this);
        } catch (Exception ex) {
            return "";
        }
    }
}
