package vn.vnpt.digo.adapter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatisticTransportDossierDto implements Serializable {
    public int tongSoXuLy;
    private int phanTramChuaXuLyTreHan;
    private int soTonKyTruoc;
    private int tongChuaXuLyTrongHan;
    private int phanTramXuLyTreHan;
    private int tongXuLyTreHan;
    private int phanTramXuLyDungHan;
    private int tongChuaXuLyTreHan;
    private int soNhanTrongKy;
    private int tongChuaXuLy;
    private int thang;
    private int  nam;
    private int  phanTramChuaXuLyTrongHan;
    private int tongDaXuLy;
    private int tongXuLyDungHan;


}
