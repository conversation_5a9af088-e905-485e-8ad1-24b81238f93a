package vn.vnpt.digo.adapter.dto.Integration;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CodeName implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String code;
    private String subCode;
    private String name;
}
