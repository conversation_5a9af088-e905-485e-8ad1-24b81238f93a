package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NpsPetitionTypeResponseDto implements Serializable {

    private List<NpsPetitionTypeResultDto> result;

    private Integer errorCode;

    @JsonSetter("error_code")
    public void mapperErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    @JsonSetter("DanhSachChuDePAKN")
    public void mapperResult(List<NpsPetitionTypeResultDto> result) {
        this.result = result;
    }
}
