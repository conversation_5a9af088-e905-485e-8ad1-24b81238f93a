/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.vnpt.hashsignature.pdf.PdfParameter;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NEACSignRequestDto implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId configId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subsystemId;
    
    private String user_id;
    private String ca_name;
    private String serial_number;
    
    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId fileId;
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId imageId;
    
    private Integer signType = 0;
    
    private String phone;
    
    private String messageDisplay = "Vui long nhap PIN ky so";
    
    private String name;
    
    private String reason;
    
    private String position;
    
    private String agency;
    
    private String location;
    
    @Pattern(message = "signPosition does not match regex \\d{1,4},\\d{1,4},\\d{1,4},\\d{1,4}", regexp = "\\d{1,4},\\d{1,4},\\d{1,4},\\d{1,4}")
    private String signPosition;
    
    @NotNull
    private Integer signPage;
    
    private Integer fontSize;
    
    private String msspProvider;
    
    private Boolean autoSign = false;
    
    private List<String> textSearch;
    
    private String side = "BOTTOM";
    
    private Integer width;
    
    private Integer height;
    
    private Integer x;
    
    private Integer y;
}
