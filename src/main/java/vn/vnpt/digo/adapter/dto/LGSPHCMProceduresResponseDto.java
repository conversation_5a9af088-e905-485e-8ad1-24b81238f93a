/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LGSPHCMProceduresResponseDto implements Serializable {
    private String error_code;

    private List<LGSPHCMProceduresResultDto> result;
   

    @JsonSetter("error_code")
    public void mapperErrorCode(String error_code) {
        this.error_code = error_code;
    }
    
    @JsonSetter("ResultObject")
    public void mapperResultObject(List<LGSPHCMProceduresResultDto> result) {
        this.result = result;
    }
    
}
