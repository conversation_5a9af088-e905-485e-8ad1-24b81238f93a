package vn.vnpt.digo.adapter.dto;

import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdDto implements Serializable {

    @NotNull
    @JsonSerialize(using = ToStringSerializer.class)
    ObjectId id;

    private int status = 200;

    private String message;

    public IdDto(ObjectId id) {
        this.id = id;
    }
}
