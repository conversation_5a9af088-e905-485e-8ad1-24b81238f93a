package vn.vnpt.digo.adapter.dto.lgspbogtvt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DossierLGSPGplxResponseDto implements Serializable {
    private String name;
    private String nationality;
    private Date birthDay;
    private String birthPlace;
    private String indentify;
    private Integer gender;
    private String regisNo;
    private String receivingPlace;
    private String receivingDate;
    private String appointmentDate;
    private String createDate;
    private String updateDate;
    private String drivingLicense;
    private String typeCode;
    private String typeName;
    private String statusNo;
    private String status;
    private String sysNo;

}
