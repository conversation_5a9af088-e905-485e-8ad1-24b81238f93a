/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR> <PERSON>am
 */
@Data
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class LGSPHCMCivilStatusJusticeGHDto implements Serializable {
    private long id;
    private String so;
    private String quyenSo;
    private String trangSo;
    private String quyetDinhSo;
    private String ngayDangKy;
    private long loaiDangKy;
    private long loaiGiamHo;
    private String noiDangKy;
    private String nguoiKy;
    private String chucVuNguoiKy;
    private String nguoiThucHien;
    private String ghiChu;
    private long tinhTrangGiamHo;
    private String chamDutGiamHoNgayGhiChu;
    private String chamDutGiamHoCanCu;
    private String congNhanGiamHoNgayGhiChu;
    private String congNhanGiamHoCanCu;
    private String nghHoTen;
    private String nghGioiTinh;
    private String nghNgaySinh;
    private long nghDanToc;
    private String nghDanTocKhac;
    private String nghQuocTich;
    private List<String> nghQuocTichKhac;
    private long nghLoaiCuTru;
    private String nghNoiCuTru;
    private long nghLoaiGiayToTuyThan;
    private String nghGiayToKhac;
    private String nghSoGiayToTuyThan;
    private String nghNgayCapGiayToTuyThan;
    private String nghNoiCapGiayToTuyThan;
    private String dghHoTen;
    private String dghGioiTinh;
    private String dghNgaySinh;
    private String dghNoiSinh;
    private long dghDanToc;
    private String dghDanTocKhac;
    private String dghQuocTich;
    private List<String> dghQuocTichKhac;
    private long dghLoaiCuTru;
    private String dghNoiCuTru;
    private long dghLoaiGiayToTuyThan;
    private String dghGiayToKhac;
    private String dghSoGiayToTuyThan;
    private String dghNgayCapGiayToTuyThan;
    private String dghNoiCapGiayToTuyThan;
    private String dghLyDoCanGiamHo;
    private String nycHoTen;
    private String nycQuanHe;
    private long nycLoaiGiayToTuyThan;
    private String nycGiayToKhac;
    private String nycSoGiayToTuyThan;
    private String nycNgayCapGiayToKhac;
    private String nycNoiCapGiayToKhac;
    private String soDangKyNuocNgoai;
    private String ngayDangKyNuocNgoai;
    private String cqNuocNgoaiDaDangKy;
    private String qgNuocNgoaiDaDangKy;
}
