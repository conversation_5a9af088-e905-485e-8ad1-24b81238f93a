package vn.vnpt.digo.adapter.dto.tandan.legaldocument;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vnpt.digo.adapter.pojo.tandan.legaldocument.Detail;
import vn.vnpt.digo.adapter.pojo.tandan.legaldocument.FileAttach;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetDetailResponseDto implements Serializable {
    private int Status;
    private String message;
    private Detail data;
}
