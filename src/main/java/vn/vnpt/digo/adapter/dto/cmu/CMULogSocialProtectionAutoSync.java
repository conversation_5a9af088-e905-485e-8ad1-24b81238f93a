package vn.vnpt.digo.adapter.dto.cmu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.dto.IdNameDto;
import vn.vnpt.digo.adapter.dto.IntegratedConfigurationDto;
import vn.vnpt.digo.adapter.dto.utils.IdCodeNameSimpleDto;
import vn.vnpt.digo.adapter.pojo.IntegratedService;

import java.io.Serializable;
import java.util.Date;
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "CMULogSocialProtectionAutoSync")
public class CMULogSocialProtectionAutoSync implements Serializable {
    @Id
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;

    private IntegratedService service;

    private IdNameDto config;

    private IdCodeNameSimpleDto item;

    private Integer status = 1;

    private String message;

    private String data;

    private Object extend;

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate = new Date();

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date updatedDate = new Date();

    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId deploymentId;

    public CMULogSocialProtectionAutoSync(IntegratedConfigurationDto config, IdCodeNameSimpleDto item, Integer status, String message, String data){
        this.service = config.getService();
        this.config = new IdNameDto(config.getId(), config.getName(), 0);
        this.item = item;
        this.message = message;
        this.status = status;
        this.data = data;
        this.createdDate = new Date();
        this.updatedDate = new Date();
        this.deploymentId = config.getDeploymentId();
    }
}
