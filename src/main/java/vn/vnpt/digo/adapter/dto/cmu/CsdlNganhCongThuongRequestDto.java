package vn.vnpt.digo.adapter.dto.cmu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CsdlNganhCongThuongRequestDto {

    @JsonProperty("ConfigId")
    private String configId;

    @JsonProperty("MaHoSo")
    private String maHoSo;

    @JsonProperty("TenHoSo")
    private String tenHoSo;

    @JsonProperty("TenThuTuc")
    private String tenThuTuc;

    @JsonProperty("LinhVuc")
    private String linhVuc;

    @JsonProperty("HoVaTenNguoiNop")
    private String hoVaTenNguoiNop;

    @JsonProperty("CCCD")
    private String cccd;

    @JsonProperty("DienThoai")
    private String dienThoai;

    @JsonProperty("TrangThai")
    private String trangThai;

}


