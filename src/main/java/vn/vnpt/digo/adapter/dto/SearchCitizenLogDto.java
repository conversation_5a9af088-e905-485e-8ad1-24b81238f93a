package vn.vnpt.digo.adapter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.RequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchCitizenLogDto implements Serializable {
    
    private String startDate;
    private String endDate;
    private ObjectId officerId;
    private String dossierCode;
    private Integer workHourStart;
    private Integer workHourEnd;
    private List<Integer> workDayOfWeek;
    private Boolean isSpam;
    private Boolean withoutAction;
    private Boolean missingInfo;
    private String ip;
    private List<String> menuCode;
    private Boolean onlyOfficerId;
    
    public SearchCitizenLogDto (HttpServletRequest request) {
        this.startDate = RequestUtils.getParammeter("from-date", String.class, "");
        this.endDate = RequestUtils.getParammeter("to-date", String.class, "");
        this.officerId = RequestUtils.getParammeter("officer", ObjectId.class, null);
        this.dossierCode = RequestUtils.getParammeter("dossier-code", String.class, "");
        this.workHourStart = RequestUtils.getParammeter("work-start", Integer.class, null);
        this.workHourEnd = RequestUtils.getParammeter("work-end", Integer.class, null);
        this.workDayOfWeek = RequestUtils.getParameterValues("work-day", Integer.class);
        this.isSpam = RequestUtils.getParammeter("spam", Boolean.class, false);
        this.withoutAction = RequestUtils.getParammeter("without-action", Boolean.class, false);
        this.missingInfo = RequestUtils.getParammeter("missing-info", Boolean.class, false);
        this.ip = RequestUtils.getParammeter("ip", String.class, "");
        this.menuCode = RequestUtils.getParameterValues("menu-code", String.class);
        this.onlyOfficerId = RequestUtils.getParammeter("only-officer-id", Boolean.class, false);
    }
    
}
