package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.pojo.Translate;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DossierResponseDto implements Serializable {

    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date acceptedDate;
    private AccepterDto accepter;
    private TemplateDto activitiProcessInstance;
    private AgencyDto agency;
    private ApplicantDto applicant;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appliedDate;
    private TemplateDto applyMethod;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date appointmentDate;
    private List<AttachmentDto> attachment;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date cancelledDate;
    @NotNull
    private String code;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date completedDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date createdDate;
    private DossierReceivingKindDto dossierReceivingKind;
    private DossierStatusDto dossierStatus;
    private FormDto form;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date lastedAddreqDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date lastedImplDate;
    private ProcedureDto procedure;
    private Integer processingTime;
    private String processingTimeUnit;
    private String receivingAddress;
    private PlaceDto receivingPlace;
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date returnedDate;
    private String workflowId;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AccepterDto implements Serializable {

        private String fullname;
        private String id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplicantDto implements Serializable {

        private AddressDto address;
        private String fullname;
        private Short gender;
        private String id;
        private String phoneNumber;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date birthday;
        private IdentityDto identity;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdentityDto implements Serializable {

        private TagDto agency;
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
        private Date date;
        private String number;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AddressDto implements Serializable {

        private String address;
        private AddressPlaceDto place;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AddressPlaceDto implements Serializable {

        private List<AddressPlaceAncestorDto> ancestors;
        private String id;
        private String name;
        private TagDto type;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AddressPlaceAncestorDto implements Serializable {

        private String id;
        private String name;
        private TagDto type;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AttachmentDto implements Serializable {

        private String filename;
        private String group;
        private String id;
        private Long size;
        private Boolean isQLVBDocument;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DossierReceivingKindDto implements Serializable {

        private String id;
        private List<Translate> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DossierStatusDto implements Serializable {

        private String id;
        private List<Translate> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FormDto implements Serializable {

        private Object data;
        private String id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProcedureDto implements Serializable {

        private String code;
        private String id;
        private SectorDto sector;
        private List<Translate> translate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SectorDto implements Serializable {

        private String id;
        private List<Translate> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PlaceDto implements Serializable {

        private String fullAddress;
        private String id;
        private String nationName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TemplateDto implements Serializable {

        private String id;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AgencyDto implements Serializable {

        private List<AncestorDto> ancestors;
        private String code;
        private String id;
        private List<Translate> name;
        private ParentDto parent;
        private List<TagDto> tag;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TagDto implements Serializable {

        private String id;
        private List<Translate> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AncestorDto implements Serializable {

        private String id;
        private List<Translate> name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ParentDto implements Serializable {

        private String id;
        private List<Translate> name;
        private List<TagDto> tag;
    }
}
