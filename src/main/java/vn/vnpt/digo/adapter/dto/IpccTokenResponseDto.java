/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IpccTokenResponseDto implements Serializable {

    private Integer status;
    private Boolean success;
    private String message;
    private IpccTokenData data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IpccTokenData implements Serializable {

        private String token;
    }
}
