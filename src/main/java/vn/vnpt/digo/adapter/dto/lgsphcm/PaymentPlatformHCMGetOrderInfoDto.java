/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentPlatformHCMGetOrderInfoDto implements Serializable {
    private String partnerCode;
    private String secretKey;
    private String accessKey;
    private String transactionNo;
    private String orderId;
    private String checksum;
    
    public void setChecksum()
    {
        String secureString = this.secretKey  
                + this.partnerCode 
                + this.accessKey
                + this.transactionNo 
                + this.orderId;
        
        this.checksum = DigestUtils.sha256Hex(secureString);
    }
}
