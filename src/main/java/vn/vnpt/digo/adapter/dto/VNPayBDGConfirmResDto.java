/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VNPayBDGConfirmResDto implements Serializable {

    @JsonProperty("code")
    private String code;

    @JsonProperty("message")
    private String message;

    @JsonProperty("data")
    private VNPayBDGdata data;

//    @JsonSerialize(using = ToStringSerializer.class)
//    private JSONObject data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VNPayBDGdata implements Serializable {
        @JsonProperty("txnId")
        private String txnId;

        @JsonProperty("amount")
        private String amount;

        @JsonProperty("productid")
        private String productid;

        @JsonProperty("qty")
        private String qty;
    }
}
