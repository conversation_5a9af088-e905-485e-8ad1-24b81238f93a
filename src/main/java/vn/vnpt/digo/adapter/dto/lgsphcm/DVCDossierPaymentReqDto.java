package vn.vnpt.digo.adapter.dto.lgsphcm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DVCDossierPaymentReqDto implements Serializable {

    @NotEmpty(message = "{lang.word.tag} 'session' {lang.phrase.may-can-not-empty}")
    private String session;

    @NotEmpty(message = "{lang.word.tag} 'madonvi' {lang.phrase.may-can-not-empty}")
    @JsonProperty("madonvi")
    private String madonvi;

    @NotEmpty(message = "{lang.word.tag} 'service' {lang.phrase.may-can-not-empty}")
    private String service;

    @NotEmpty(message = "{lang.word.tag} 'data' {lang.phrase.may-can-not-empty}")
    private List<@Valid DVCUpdateProgressDossierDataDto> data;
        
    private String deploymentId;

    public DVCDossierPaymentReqDto(String session, String agencyCode, String service, DVCUpdateProgressDossierDataDto data) {
        this.session = session;
        this.madonvi = agencyCode;
        this.service = service;
        this.data = new ArrayList<DVCUpdateProgressDossierDataDto>();
        this.data.add(data);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DVCUpdateProgressDossierDataDto implements Serializable {

        @JsonIgnore
        private final SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");

        @JsonProperty("MaHoSo")
        @NotEmpty(message = "{lang.word.tag} 'MaHoSo'{lang.phrase.may-can-not-empty}")
        private String MaHoSo;

        @JsonProperty("MaPhiLePhi")
        @NotEmpty(message = "{lang.word.tag} 'MaPhiLePhi' {lang.phrase.may-can-not-empty}")
        private String MaPhiLePhi;

        @JsonProperty("TenPhiLePhi")
        @NotEmpty(message = "{lang.word.tag} 'TenPhiLePhi' {lang.phrase.may-can-not-empty}")
        private String TenPhiLePhi;

        @JsonProperty("HinhThucThu")
        @NotNull(message = "{lang.word.tag} 'HinhThucThu' {lang.phrase.may-can-not-empty}")
        @Min(value = 1, message = "{lang.word.tag} 'HinhThucThu' {lang.phrase.is-not-valid}")
        @Max(value = 4, message = "{lang.word.tag} 'HinhThucThu' {lang.phrase.is-not-valid}")
        private Integer HinhThucThu;

        @JsonProperty("Gia")
        @NotNull(message = "{lang.word.tag} 'Gia' {lang.phrase.may-can-not-empty}")
        @Min(value = 1, message = "{lang.word.tag} 'Gia' {lang.phrase.is-not-valid}")
        private Integer Gia;

        @JsonProperty("LoaiPhiLePhi")
        @NotNull(message = "{lang.word.tag} 'LoaiPhiLePhi' {lang.phrase.may-can-not-empty}")
        @Min(value = 1, message = "{lang.word.tag} 'LoaiPhiLePhi' {lang.phrase.is-not-valid}")
        @Max(value = 2, message = "{lang.word.tag} 'LoaiPhiLePhi' {lang.phrase.is-not-valid}")
        private String LoaiPhiLePhi;
        
        @JsonProperty("SoNgayTamDungThanhToan")
        @NotNull(message = "{lang.word.tag} 'SoNgayTamDungThanhToan' {lang.phrase.may-can-not-empty}")
        @Min(value = 1, message = "{lang.word.tag} 'SoNgayTamDungThanhToan' {lang.phrase.is-not-valid}")
        private Long SoNgayTamDungThanhToan;

        @JsonProperty("NgayYeuCau")
        @NotEmpty(message = "{lang.word.tag} 'NgayYeuCau' {lang.phrase.may-can-not-empty}")
        @Size(min = 14, max = 14, message = "{lang.word.tag} 'NgayYeuCau' {lang.phrase.is-not-valid}")
        private String NgayYeuCau;
        
        @JsonProperty("TrangThaiThanhToan")
        // Value: 1 - payment has not been done; 2 - completed payment
        private Integer TrangThaiThanhToan = 1;

    }
}
