/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.dto.ilis;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.format.annotation.DateTimeFormat;
import vn.vnpt.digo.adapter.pojo.ilis.ThongTinNguoiNopDon;
import vn.vnpt.digo.adapter.pojo.ilis.ThongTinThuaDat;
import vn.vnpt.digo.adapter.pojo.ilis.DanhSachGiayTo;
import vn.vnpt.digo.adapter.util.ParamName;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalStatusUpdateDossierDto implements Serializable {

    @ParamName("config-id")
    @JsonProperty("ConfigId")
    private ObjectId configId;

    @JsonProperty("AgencyId")
    @ParamName("agency-id")
    private ObjectId agencyId;

    @JsonProperty("SubsystemId")
    @ParamName("subsystem-id")
    private ObjectId subsystemId;

    @JsonProperty("LinkFileIlis")
    public String linkFileIlis;

    @NotNull
    @JsonProperty("SoBienNhan")
    public String soBienNhan;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    @JsonProperty("NgayHenTraMoi")
    public Date ngayHenTraMoi;

    @JsonProperty("GhiChu")
    public String ghiChu;

    @JsonProperty("ThongTinNguoiNopDon")
    public ThongTinNguoiNopDon thongTinNguoiNopDon;

    @JsonProperty("ThongTinThuaDat")
    public ArrayList<ThongTinThuaDat> thongTinThuaDat;

    @JsonProperty("XaId")
    public String xaId;

    @JsonProperty("DanhSachGiayToBoSung")
    public ArrayList<DanhSachGiayTo> danhSachGiayToBoSung;

    @JsonProperty("LoaiChoBoSung")
    public Integer loaiChoBoSung = 0;

}
