package vn.vnpt.digo.adapter.dto.dbn;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DBNHoSoGiayToDto implements Serializable {
    @JsonProperty("Nguon")
    @NotEmpty(message = "{lang.word.tag} 'Nguon' {lang.phrase.may-can-not-empty}")
    private String nguon= "";
    @JsonProperty("TenLoaiGiayTo")
    @NotEmpty(message = "{lang.word.tag} 'TenLoaiGiayTo' {lang.phrase.may-can-not-empty}")
    private String tenLoaiGiayTo= "";
    @JsonProperty("MaLoaiGiayTo")
    @NotEmpty(message = "{lang.word.tag} 'MaLoaiGiayTo' {lang.phrase.may-can-not-empty}")
    private String maLoaiGiayTo= "";
    @JsonProperty("MaGiayTo")
    private String maGiayTo= "";
    @JsonProperty("ThoiHan")
    private String thoiHan= "";
    @JsonProperty("PhamVi")
    private String phamVi= "";
    @JsonProperty("TrichYeu")
    private String trichYeu= "";

    @JsonProperty("ThongTinKhac")
    private String thongTinKhac= "";
    @JsonProperty("NgayKy")
    private String ngayKy= "";
    @JsonProperty("NguoiKy")
    private String nguoiKy= "";
    @JsonProperty("ChucVuNguoiKy")
    private String chucVuNguoiKy= "";
    @JsonProperty("CoQuanBanHanh")
    private String coQuanBanHanh= "";
    @JsonProperty("NgayBanHanh")
    private String ngayBanHanh= "";
    @JsonProperty("TenGiayTo")
    private String tenGiayTo= "";
    @JsonProperty("SoKyHieu")
    private String soKyHieu= "";
    @JsonProperty("MaDonVi")
    private String maDonVi= "";
    @JsonProperty("DinhKem")
    @NotEmpty(message = "{lang.word.tag} 'DinhKem' {lang.phrase.may-can-not-empty}")
    private List<@Valid DBNInputDinhKemDto> dinhKem;
}