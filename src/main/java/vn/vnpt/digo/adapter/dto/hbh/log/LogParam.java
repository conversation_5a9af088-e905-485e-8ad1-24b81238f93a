package vn.vnpt.digo.adapter.dto.hbh.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpHeaders;
import vn.vnpt.digo.adapter.dto.NameValueDto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LogParam {
    private String serviceName;
    private String key;
    private String id;
    private String requestMethod;
    private String requestUrl;
    private Object requestBody;
    private List<NameValueDto> requestHeader;

    public static List<NameValueDto> convertHeaders(HttpHeaders reqHeader) {
        List<NameValueDto> headers = new ArrayList<NameValueDto>();
        if(reqHeader == null) {
            return headers;
        }
        for(Map.Entry<String, List<String>> entry: reqHeader.entrySet()) {
            NameValueDto data = new NameValueDto();
            data.setName(entry.getKey());
            List<String> values = entry.getValue();
            if(values != null && !values.isEmpty()) {
                data.setValue(values.get(0));
            }
            headers.add(data);
        }
        return headers;
    }

    public static List<NameValueDto> convertHeaders(Map<String, String> reqHeader) {
        List<NameValueDto> headers = new ArrayList<NameValueDto>();
        if(reqHeader == null) {
            return headers;
        }
        for(Map.Entry<String, String> entry: reqHeader.entrySet()) {
            NameValueDto data = new NameValueDto();
            data.setName(entry.getKey());
            data.setValue(entry.getValue());
            headers.add(data);
        }
        return headers;
    }
}
