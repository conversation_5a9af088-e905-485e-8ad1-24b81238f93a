/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VGCAResponseDto implements Serializable {
    
    @JsonProperty("Status")
    private boolean status = false;
    
    @JsonProperty("Message")
    private String message;
    
    @JsonProperty("FileName")
    private String filename;
    
    @JsonProperty("FileServer")
    private String fileserver;
}
