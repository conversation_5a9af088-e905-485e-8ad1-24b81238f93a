package vn.vnpt.digo.adapter.dto.bdg;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VNPTPayCreateBillNumberDto implements Serializable {
    @JsonProperty("dossierPaymentId")
    public String dossierPaymentId = null;

    @JsonProperty("MaBillNumberRutGon")
    public String MaBillNumberRutGon = null;
}
