package vn.vnpt.digo.adapter.dto;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.pojo.MappingTypeDataAccess;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MappingTypeInputDto implements Serializable {

    private ObjectId id;
    @NotNull
    private String name;
    private String description;
    private MappingTypeDataAccess sourceDataAccess;
    private MappingTypeDataAccess destDataAccess;
    private Integer status = 1;
}
