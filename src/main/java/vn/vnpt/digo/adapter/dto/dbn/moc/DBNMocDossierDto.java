package vn.vnpt.digo.adapter.dto.dbn.moc;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DBNMocDossierDto {

    private String MaDonVi;

    private String TenDonVi;

    private String MaHoSo;

    private String NgayTiepNhan;

    private String NgayHenTraKetQua;

    private String MaTrangThai;

    private DBNMocApplicantDto ThongTinNguoiNop;

    private DBNMocInvestorDto ThongTinVeChuDauTu;

    private DBNMocConstructionInfoDto ThongTinCongTrinh;

    private String ThoiGianHoanThanh;

    private List<DBNMocDossierAttachmentDto> ThanhPhanHoSo;

}
