package vn.vnpt.digo.adapter.dto.eform;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemComponent implements Serializable {
    private String key;
    private String label;
    private String type;
    private String format;
    private boolean require = false;
    @JsonProperty("isIdentity")
    private boolean isIdentity = false;
    private String entityKey;
    private Integer entityIndex;
    private MapDto map;
    private HashMap<String, Object> properties;
}
