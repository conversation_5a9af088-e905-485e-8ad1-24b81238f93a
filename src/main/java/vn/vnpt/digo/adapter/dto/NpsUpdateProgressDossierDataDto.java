package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.TimeZone;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;
import vn.vnpt.digo.adapter.pojo.Translate;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NpsUpdateProgressDossierDataDto implements Serializable {

    @JsonIgnore
    private final SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
    @Field("MaHoSo")
    @JsonProperty("MaHoSo")
    private String MaHoSo;
    @JsonProperty("NguoiXuLy")
    private String NguoiXuLy;
    @JsonProperty("ChucDanh")
    private String ChucDanh;
    @JsonProperty("ThoiDiemXuLy")
    private String ThoiDiemXuLy;
    @JsonProperty("PhongBanXuLy")
    private String PhongBanXuLy;
    @JsonProperty("NoiDungXuLy")
    private String NoiDungXuLy;
    @JsonProperty("TrangThai")
    private String TrangThai;
    @JsonProperty("NgayBatDau")
    private String NgayBatDau;
    @JsonProperty("NgayKetThucTheoQuyDinh")
    private String NgayKetThucTheoQuyDinh;

    public NpsUpdateProgressDossierDataDto(NpsSyncDossierStatusIDto input, Short localeId) {

        this.MaHoSo = input.getCode();
        this.NguoiXuLy = input.getAssignee();
        format.setTimeZone(TimeZone.getTimeZone("UTC"));
        String date = format.format(input.getDate());
        this.ThoiDiemXuLy = date;
        this.TrangThai = input.getStatus();
        if (Objects.nonNull(input.getStartDate())) {
            String startDate = format.format(input.getStartDate());
            this.NgayBatDau = startDate;
        }
        if (Objects.nonNull(input.getEndDate())) {
            String endDate = format.format(input.getEndDate());
            this.NgayKetThucTheoQuyDinh = endDate;
        }
        if (Objects.nonNull(input.getComment())) {
            this.NoiDungXuLy = input.getComment();
        }
    }

    private static String getTransName(List<Translate> name, Short localeId) {
        String result = "";
        if (Objects.nonNull(name)) {
            for (Translate item : name) {
                if (item.getLanguageId().equals(localeId)) {
                    result = item.getName();
                }
            }
        }
        return result;
    }
}
