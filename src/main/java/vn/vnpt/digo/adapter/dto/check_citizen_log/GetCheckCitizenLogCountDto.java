package vn.vnpt.digo.adapter.dto.check_citizen_log;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.digo.adapter.pojo.AgencyCodeName;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "checkCitizenLog")
public class GetCheckCitizenLogCountDto implements Serializable {
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId id;
    private String username;
    private AgencyCodeName agency;
    private int count;
}
