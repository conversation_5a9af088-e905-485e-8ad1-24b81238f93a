package vn.vnpt.digo.adapter.dto.csdldc;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.CheckConfigParams;
import vn.vnpt.digo.adapter.util.ParamName;

@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigParams
public class ShareCitizenInfoRequestDto implements Serializable {
    @ParamName("config-id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId configId;

    @ParamName("agency-id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId agencyId;

    @ParamName("subsystem-id")
    @JsonSerialize(using = ToStringSerializer.class)
    private ObjectId subsystemId;

    @ParamName("indentity-number")
    private String indentityNumber;

    @ParamName("fullname")
    private String fullname;

    @ParamName("birthday")
    private String birthday;

    @ParamName("eform-id")
    private ObjectId eformId;

    @ParamName("map-fill-data")
    private String mapfilldata;

    @ParamName("key")
    private String key;
    @ParamName("get-origin-info")
    private boolean getOriginInfo;

    @ParamName("await")
    private boolean await = false;

    @ParamName("ma-can-bo")
    private String maCanBo;

    @ParamName("procedure-id")
    private String procedureId = null;
    
    @ParamName("dossier-id")
    private ObjectId dossierId;

    @ParamName("dossier-code")
    private String dossierCode = null;

    @ParamName("enable-log-csdldc-bdh")
    private boolean enableLogCSDLDCBDH = false;
}
