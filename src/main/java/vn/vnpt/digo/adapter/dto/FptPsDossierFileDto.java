/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.vnpt.digo.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FptPsDossierFileDto implements Serializable {

    @JsonProperty("HoSoKemTheoID")
    private Integer hoSoKemTheoID;
    @JsonProperty("TenHoSoKemTheo")
    private String tenHoSoKemTheo;
    @JsonProperty("SoBanChinh")
    private Integer soBanChinh;
    @JsonProperty("SoBanSao")
    private Integer soBanSao;
    @JsonProperty("SoBanPhoTo")
    private Integer soBanPhoTo;
    @JsonProperty("HoSoID")
    private Integer hoSoID;
    @JsonProperty("FileName")
    private String fileName;
    @JsonProperty("FilePath")
    private String filePath;
    @JsonProperty("UploadName")
    private String uploadName;
    @JsonProperty("GhiChu")
    private String ghiChu;
    @JsonProperty("DMHoSoKemTheoID")
    private String dMHoSoKemTheoID;
    @JsonProperty("LanBoSung")
    private String lanBoSung;
    @JsonProperty("DatDai_MaHoSoKemTheo")
    private String datDai_MaHoSoKemTheo;
}
