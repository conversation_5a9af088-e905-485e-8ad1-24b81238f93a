package vn.vnpt.digo.adapter.dto.minhtue.tntm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OtherInForBoTNMT implements Serializable {
    private String isBoTNMT;
    private String MaHuyen ;
    private String LoaiDoiTuong ;
    private String MaDoiTuong;
    private String  ThongTinKhac;
    private String TrichYeuHoSo;
    private String ThongTinTra;
    private String GhiChu;
    private DonDangKy DonDangKy;

}
