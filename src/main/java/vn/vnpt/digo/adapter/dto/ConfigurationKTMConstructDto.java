package vn.vnpt.digo.adapter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import vn.vnpt.digo.adapter.util.CheckConfigParams;
import vn.vnpt.digo.adapter.util.ParamName;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@CheckConfigParams
public class ConfigurationKTMConstructDto implements Serializable {
    @ParamName("config-id")
    private ObjectId configId;

    @ParamName("agency-id")
    private ObjectId agencyId;

    @ParamName("subsystem-id")
    private ObjectId subsystemId;

}
