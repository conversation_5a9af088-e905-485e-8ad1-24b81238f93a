/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package vn.vnpt.digo.adapter.dto.bhtn;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BhtnResDto implements Serializable {

    @JsonProperty("Body")
    private Body body;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Body implements Serializable {

        @JsonProperty("ReceivingDossierResponse")
        private ReceivingDossierResponse receivingDossierResponse;

        @JsonProperty("ResultUpdateDossierResponse")
        private ResultUpdateDossierResponse resultUpdateDossierResponse;

        @JsonProperty("AdditionalStatusUpdateDossierResponse")
        private AdditionalStatusUpdateDossierResponse additionalStatusUpdateDossierResponse;

        @JsonProperty("SendProcessingNoticeDossierResponse")
        private SendProcessingNoticeDossierResponse sendProcessingNoticeDossierResponse;

        @JsonProperty("FeedbackProfileResultDossierResponse")
        private FeedbackProfileResultDossierResponse feedbackProfileResultDossierResponse;
        
        @JsonProperty("DanhMucThuTucResponse")
        private DanhMucThuTucResponse danhMucThuTucResponse;
        
        @JsonProperty("DanhMucNguoiDungResponse")
        private DanhMucNguoiDungResponse danhMucNguoiDungResponse;
        
        @JsonProperty("DanhMucTrangThaiResponse")
        private DanhMucTrangThaiResponse danhMucTrangThaiResponse;
        
        @JsonProperty("YeuCauBoSungResponse")
        private YeuCauBoSungResponse yeuCauBoSungResponse;

        @JsonProperty("GuiKetQuaThueResponse")
        private GuiKetQuaThueResponse guiKetQuaThueResponse;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReceivingDossierResponse implements Serializable {

        public Boolean isError;
        public Integer code;
        public String data;
        public String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BhtnResponse implements Serializable {
        public String code;
        public String message;
        public HashMap<String, Object> data;
    }



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResultUpdateDossierResponse implements Serializable {

        public Boolean isError;
        public Integer code;
        public String data;
        public String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdditionalStatusUpdateDossierResponse implements Serializable {

        public Boolean isError;
        public Integer code;
        public String data;
        public String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SendProcessingNoticeDossierResponse implements Serializable {

        public Integer data;
        public Status status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeedbackProfileResultDossierResponse implements Serializable {

        public Integer data;
        public Status status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Status implements Serializable {

        public Boolean success;
        public Integer code;
        public String type;
        public String time;
        public String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhMucThuTucResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;
        
        @JsonProperty("Message")
        public String message;
        
        @JsonProperty("Data")
        public ArrayList<DanhMucThuTucDto> data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhMucThuTucDto implements Serializable {

        @JsonProperty("MaThuTuc")
        public String maThuTucH;

        @JsonProperty("TenThuTuc")
        public String tenThuTuc;

        @JsonProperty("TenLinhVuc")
        public String tenLinhVuc;

        @JsonProperty("MaLinhVuc")
        public String maLinhVuc;

        @JsonProperty("MaQuyTrinh")
        public String maQuyTrinh;

        @JsonProperty("TenQuyTrinh")
        public String tenQuyTrinh;


        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class ProcedureDto implements Serializable {

            private String procedureCode;
            private String procedureName;
            private String secterCode;
            private String secterName;
            private String processDefinitionCode;
            private String processDefinitionName;
        }
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhMucNguoiDungResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;
        
        @JsonProperty("Message")
        public String message;
        
        @JsonProperty("Data")
        public ArrayList<DanhMucNguoiDungDto> data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhMucNguoiDungDto implements Serializable {

        @JsonProperty("MaNguoiDung")
        public String maNguoiDung;

        @JsonProperty("TenNguoiDung")
        public String tenNguoiDung;

        @JsonProperty("TenPhongBan")
        public String tenPhongBan;

        @JsonProperty("ChucDanh")
        public String chucDanh;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhMucTrangThaiResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;
        
        @JsonProperty("Message")
        public String message;
        
        @JsonProperty("Data")
        public ArrayList<DanhMucTrangThaiDto> data;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class YeuCauBoSungResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;
        
        @JsonProperty("Message")
        public String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GuiKetQuaThueResponse implements Serializable {

        @JsonProperty("Result")
        public Integer result;

        @JsonProperty("Message")
        public String message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DanhMucTrangThaiDto implements Serializable {

        @JsonProperty("MaTrangThai")
        public String maTrangThai;

        @JsonProperty("TenTrangThai")
        public String tenTrangThai;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class DossierStatusDto implements Serializable {

            private String id;
            private String name;
        }
    }
}
