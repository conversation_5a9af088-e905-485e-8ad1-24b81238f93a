debug=false

server.port=8081
server.max-http-header-size=48000

## gRPC
grpc.server.port=9090

grpc.client.svc-adapter.address=static://localhost:9090
grpc.client.svc-basecat.address=static://localhost:9090
grpc.client.svc-basedata.address=static://localhost:9090
grpc.client.svc-bpm.address=static://localhost:9090
grpc.client.svc-eform.address=static://localhost:9090
grpc.client.svc-fileman.address=static://localhost:9090
grpc.client.svc-human.address=static://localhost:9090
grpc.client.svc-logman.address=static://localhost:9090
grpc.client.svc-messenger.address=static://localhost:9090
grpc.client.svc-modeling.address=static://localhost:9090
grpc.client.svc-reporter.address=static://localhost:9090
grpc.client.svc-sysman.address=static://localhost:9090
grpc.client.svc-system.address=static://localhost:9090
grpc.client.svc-basepad.address=static://localhost:9090
grpc.client.svc-padman.address=static://localhost:9090
grpc.client.svc-rbonegate.address=static://localhost:9090
grpc.client.svc-storage.address=static://localhost:9090
grpc.client.svc-surfeed.address=static://localhost:9090
grpc.client.GLOBAL.negotiation-type=plaintext
## TLS
grpc.server.max-inbound-message-size=10485760
grpc.server.max-inbound-metadata-size=10485760
#grpc.server.security.enabled=true
#grpc.server.security.privateKey=file:/etc/grpc/ssl/tls.key
#grpc.server.security.certificateChain=file:/etc/grpc/ssl/tls.crt

#grpc.client.local-grpc-server.address=static://svc-reporter.applications-test.svc.k8s.operator:8443
#grpc.client.local-grpc-server.negotiationType=tls
#grpc.client.local-grpc-server.max-inbound-message-size=10485760
#grpc.client.local-grpc-server.security.authorityOverride=svc-reporter.applications-test.svc.k8s.operator
#grpc.client.local-grpc-server.security.privateKey=file:/etc/grpc/ssl/tls.key
#grpc.client.local-grpc-server.security.certificateChain.=file:/etc/grpc/ssl/tls.crt
#grpc.client.local-grpc-server.security.trustCertCollection=file:/etc/grpc/ssl/ca.crt

management.server.port=9990
management.endpoints.enabled-by-default=false
management.endpoints.web.base-path=/igate-probes
management.endpoints.web.cors.allowed-origins=https://apitest.vnptigate.vn
management.endpoints.web.cors.allowed-methods=GET
management.endpoint.health.show-details=never
management.endpoint.health.roles=admin

management.endpoint.health.enabled=true
management.health.db.enabled=true
management.health.mongo.enabled=true
management.health.mail.enabled=false
management.health.ldap.enabled=false
spring.cloud.discovery.client.composite-indicator.enabled=false
telegram.bot.token=**********:AAHuEtIF4X6vuA5fB1R5mqfoYiUZc0E3EEA
telegram.chat.id=-1002215146785
telegram.chat.topic=0

spring.cache.type=hazelcast
spring.hazelcast.config=classpath:hazelcast.xml
spring.session.store-type=hazelcast
digo.promotion-dossier.onlinereceivingKind=false
digo.promotion-dossier.onlinereceivingKindId=65f3f82ab8deed28a9c4c358

## Redis
#spring.cache.type=redis
#spring.session.store-type=redis
spring.cache.redis.time-to-live=900
#spring.cache.redis.cache-null-values=false
##spring.redis.cluster.nodes:ip:port,ip:port:ip:port
#spring.redis.host=ip
#spring.redis.port=port
#spring.redis.password=password
spring.cache.redis.key-prefix=svc-adapter

#spring.data.mongodb.uri=mongodb://*************:30013/digoAdapter
#spring.data.mongodb.uri=mongodb://************:27017/digoAdapter
spring.data.mongodb.uri=mongodb://*************:30004/svcAdapter
spring.data.rest.default-page-size=15
spring.data.rest.max-page-size=50
#spring.security.oauth2.resourceserver.jwt.issuer-uri=https://digo-oidc.vnptigate.vn/auth/realms/digo
#spring.security.oauth2.resourceserver.jwt.issuer-uri=https://ssokontum.digigov.vn/auth/realms/digo
spring.security.oauth2.resourceserver.jwt.issuer-uri=https://ssotest.vnptigate.vn/auth/realms/digo

digo.oidc.client-id=svc-adapter
digo.oidc.client-secret=62ca3e28-2878-473c-8956-22f5cf288b3a
digo.tandan.off=true

mongobee.enable=false
mongobee.change-logs-scan-package=vn.vnpt.digo.adapter.changelogs

digo.dbname.svc-padman=svcPadman
digo.microservice.communication-strategy=gateway
#digo.microservice.gateway-url=https://digo-api.vnptigate.vn
#digo.microservice.gateway-url=https://apikontum.digigov.vn
digo.microservice.gateway-url=https://apitest.vnptigate.vn
digo.microservice.service-name-prefix=svc-
digo.microservice.service-default-port=8080
digo.microservice.service-default-protocol=http
digo.microservice.cluster-digo-namespace=.applications-test
digo.microservice.cluster-igate-namespace=.applications-test
digo.microservice.cluster-petition-namespace=.applications-test
digo.microservice.cluster-vndrive-namespace=.applications-test
digo.microservice.cluster-path=.svc.k8s.operator
digo.locale-code=vi
digo.supported-locales={"vi":228,"en":46,"zh":232}
digo.oauth2.resource-id=digo-api-fileman
digo.cors-mappings=[{"path-pattern":"/**","allowed-origins":"https://digo-swagger-ui.vnptioffice.vn,https://digo-swagger-ui.vnptigate.vn,https://digo-swagger-editor.vnptioffice.vn,https://digo-formio.vnptigate.vn,capacitor://localhost,ionic://localhost,http://localhost,http://localhost:8080,http://localhost:8100,http://localhost:8101,http://localhost:4200,http://localhost:4300,http://localhost:4400,http://************:4200,https://digo-onegate.vnptigate.vn,http://************:30142,https://digo-news.vnptigate.vn,https://digo-citizens-admin.vnptigate.vn,https://digo-tgg-citizens-admin.vnptigate.vn,file://,https://digo-padsvc.vnptigate.vn,https://digo-petition.vnptigate.vn,https://digo-api.vnptigate.vn,*","allowed-methods":"GET,POST,PUT,DELETE"}]
digo.schedule.synchronize-dossier-statistic.enable=false
digo.schedule.synchronize-dossier-statistic-hpg.enable=false
statistic.dossier.hpg.sync.configid = 7fa35c1d8e9b47b2f14a6c08
digo.schedule.synchronize-rating-result-office.enable=false
digo.schedule.nps-dossier-integrated-event.enable=true
digo.schedule.nps-dossier-integrated-re-call-event.enable=true
digo.schedule.lgsp-hcm-nps-dossier-integrated-event.enable=false
digo.schedule.nps-resync-dossier-v2-integrated-event.enable=false
digo.schedule.lgsp-hcm-nps-dossier-v2-integrated-event.enable=false
digo.schedule.dossier-get-bill-event.enable=false
digo.schedule.hcmlgsp-synchronize-dossier-lltp.enable=false
digo.schedule.hcmlgsp-synchronize-dossier-http.enable=false
digo.schedule.hcmlgsp-sync-dossier-from-lgsp.enable=false
digo.schedule.tan-dan-receiving-document.enable=true
digo.schedule.lgsp-hcm-tracking-vnpost.enable=false
digo.schedule.lgsp-hcm-sync-procedure.enable=false
digo.schedule.get-dossier-document-qlvb.enable=true
digo.config.NpsDossierSyncService.defaultOnlinePaymentBDTC=false
digo.config.NpsDossierSyncService.reusedCriterialResidential=false
digo.config.NpsDossierSyncService.reusedCriteriaEnterprise=false
digo.nbrs.sync.dossier.hcm=false
digo.config.NpsDossierSyncService.checkCSDLHCM=false
digo.config.NpsDossierSyncService.checkCompletedDateHCM=false
digo.config.NpsDossierSyncService.updateDossierSyncHCM=false
digo.schedule.tracking-vnpost.enable=true
digo.schedule.ktm-tracking-vnpost.enable=true
digo.schedule.bdg-tracking-vnpost.enable=true
digo.schedule.lgsp-hcm-dossier-chuyen-nganh-integrated-event.enable=false
digo.schedule.lgsp-hcm-dossier-chuyen-nganh-integrated-event.exchange.status.enable=false
digo.schedule.integrated-event.chuyennganh-hcm.resendDossierChuyenNganh.cron=0 0 11,23 * * *
digo.config.get-status-dossier-sync-dvcqg=SS
digo.config.NpsDossierSyncReqDto.defaultOnlinePayment=-1
digo.config.NpsDossierSyncReqDto.defaultDigitized=0
digo.config.NpsDossierSyncService.defaultAddingDate=false
digo.config.integrated-event.lgsp-hcm.resync=10
digo.schedule.integrated-event.lgsp-hcm.resendDossierNPS.cron=0 0/30 * * * ?
digo.schedule.resync-hcm-nps-dossier-integrated-event.enable=false
digo.schedule.resync-hcm-nps-dossier-accepted-date-integrated-event.enable=false
digo.schedule.hcm-nps-dossier-status-by-list-integrated-event.enable=false
digo.schedule.zalo.auto-refresh-token.enable=true
digo.schedule.zalo.auto-refresh-token.cron=0 0 1 * * *
digo.schedule.nps-resync-dossier-integrated-event.enable=true
digo.thread.sync.nbrs.dossier.hcm.enable=false
digo.config.NpsDossierSyncService.defaultOnlinePaymentHGI=false

digo.schedule.moc.get-dossier.enable=false
digo.schedule.moc.get-dossier.cron=0 0 6,12,18 ? * * 
digo.schedule.moc-dbn.get-dossier.cron=0 0 */2 ? * *

#iworkplace
digo.integration.iworkplace-enable=false
digo.schedule.iworkplace-enable=false

digo.microservice.subsystemIdPadsvc=5f7c16069abb62f511880006
digo.sysman.system=
#spring.cloud.stream.kafka.binder.brokers=************
#spring.cloud.stream.kafka.binder.defaultBrokerPort=31241

spring.cloud.stream.kafka.binder.brokers=*************
spring.cloud.stream.kafka.binder.defaultBrokerPort=30185

digo.schedule.LGSPHCMJudicialCivilStatus.enable=false
digo.schedule.VBDLISServiceGetTokenV2.enable=false

digo.thread.sync.notifyEvent.enable=false
spring.cloud.stream.bindings.notifyEventReceive.group=adaper-notify-event
spring.cloud.stream.bindings.notifyEventReceive.destination=notifyEventRequest
spring.cloud.stream.kafka.bindings.notifyEventReceive.consumer.autoCommitOffset=false
spring.cloud.stream.bindings.notifyEventRequest.destination=notifyEventRequest

digo.thread.sync.lgsp.enable=false
spring.cloud.stream.bindings.lgspEventReceive.group=adaper-lgsp-event
spring.cloud.stream.bindings.lgspEventReceive.destination=lgspEventRequest
spring.cloud.stream.kafka.bindings.lgspEventReceive.consumer.autoCommitOffset=false
spring.cloud.stream.bindings.lgspEventRequest.destination=lgspEventRequest

digo.thread.sync.vnpost.enable=false
spring.cloud.stream.bindings.VNPostEventReceive.group=adaper-vnpost-event
spring.cloud.stream.bindings.VNPostEventReceive.destination=vnpostEventRequest
spring.cloud.stream.kafka.bindings.VNPostEventReceive.consumer.autoCommitOffset=false
spring.cloud.stream.bindings.VNPostEventRequest.destination=vnpostEventRequest

spring.cloud.stream.bindings.npsSendNotificationSyncReceive.destination=LGSPHCMSendNotificationLater
spring.cloud.stream.bindings.dataLoggerRequestOut.destination=data-logger
spring.cloud.stream.bindings.npsDossierStatusReceive.destination=nps-dossier-status
spring.cloud.stream.bindings.npsDossierStatusReceive.group=adaper-jobworker
spring.cloud.stream.bindings.npsDossierProducerRequestOut.destination=nps-dossier-status
spring.cloud.stream.bindings.npsDossierReceive.group=adaper-jobworker
spring.cloud.stream.bindings.npsDossierReceive.destination=npsDossier.test2
spring.cloud.stream.kafka.bindings.npsDossierReceive.consumer.autoCommitOffset=false
spring.cloud.stream.bindings.npsDossierConsumerHCMRequestIn.group=adaper-jobworker
spring.cloud.stream.bindings.npsDossierConsumerHCMRequestIn.destination=syncDossierHCM.temp
spring.cloud.stream.kafka.bindings.npsDossierConsumerHCMRequestIn.consumer.autoCommitOffset=false
spring.cloud.stream.bindings.authenticateDossierHCMRequestOut.destination=syncAuthDossierHCM.temp
spring.cloud.stream.bindings.authenticateDossierHCMFromAuthenSystemRequestOut.destination=syncAuthDossierHCMFromAuthenSystem.temp
spring.cloud.stream.bindings.npsReCallDossierReceive.group=adaper-jobworker
spring.cloud.stream.bindings.npsReCallDossierReceive.destination=npsReCallDossier.test2
spring.cloud.stream.kafka.bindings.npsReCallDossierReceive.consumer.autoCommitOffset=false
spring.cloud.stream.bindings.npsDossierTestOut.destination=npsDossier.test2
spring.cloud.stream.bindings.nbrsDossierHCMReceive.destination=syncNBRSDossierHCM.temp
spring.cloud.stream.kafka.bindings.nbrsDossierHCMReceive.consumer.autoCommitOffset=false
spring.cloud.stream.bindings.nbrsDossierHCMReceive.group=adaper-jobworker
spring.cloud.stream.bindings.EMCDossierHCMSyncIn.destination=EMCDossierHCM
spring.cloud.stream.kafka.bindings.EMCDossierHCMSyncIn.consumer.autoCommitOffset=false
spring.cloud.stream.bindings.EMCDossierHCMSyncIn.group=adaper-jobworker

spring.cloud.stream.bindings.procedureRequestOut.destination=svc.basepad.procedure
spring.cloud.stream.bindings.procedureDetailRequestOut.destination=svc.basepad.procedure.detail
spring.cloud.stream.bindings.procedureSyncRequestOut.destination=svc.basepad.procedure.sync
spring.cloud.stream.bindings.npsDossierBdgSyncOut.destination=npsDossierBdg
spring.cloud.stream.bindings.npsDossierBdgStatusSyncOut.destination=npsDossierStatusBdg

spring.cloud.stream.bindings.nbrsDossierHCMRequestOut.destination=nbrsDosssierHCM
spring.cloud.stream.bindings.LGSPHCMDossierOut.destination=LGSPHCMDossier
spring.cloud.stream.bindings.LGSPHCMDossierTrackingOut.destination=LGSPHCMDossierTracking
spring.cloud.stream.bindings.LGSPHCMDossierPaymentOut.destination=LGSPHCMDossierPayment
spring.cloud.stream.bindings.npsDossierLGSPHCMSyncIn.destination=npsDossierLGSPHCM
spring.cloud.stream.bindings.npsDossierLGSPHCMSyncIn.group=LGSPHCM
spring.cloud.stream.kafka.bindings.npsDossierLGSPHCMSyncIn.consumer.autoCommitOffset=false
spring.cloud.stream.bindings.npsDossierLGSPHCMStatusSyncIn.destination=npsDossierStatusLGSPHCM
spring.cloud.stream.bindings.npsDossierLGSPHCMStatusSyncIn.group=LGSPHCM
spring.cloud.stream.kafka.bindings.npsDossierLGSPHCMStatusSyncIn.consumer.autoCommitOffset=false
spring.cloud.stream.bindings.dataLoggerBDGReceive.destination=streamLogDBG
spring.cloud.stream.bindings.LGSPHCMDossierChuyenNganhIn.destination=LGSPHCMDossierChuyenNganh
spring.cloud.stream.bindings.LGSPHCMDossierChuyenNganhIn.group=LGSPHCM
spring.cloud.stream.bindings.dataMinistryConnectReceive.destination=ministryConnect

spring.cloud.stream.bindings.DVCLTDossierTrackingOut.destination=DVCLTDossierTracking
spring.cloud.stream.bindings.DVCLTDossierTrackingOut.group=DVCLT
spring.cloud.stream.bindings.DVCLTDossierOut.destination=DVCLTDossier
spring.cloud.stream.bindings.DVCLTDossierOut.group=DVCLT
spring.cloud.stream.bindings.DVCLTDossierHoTichOut.destination=DVCLTHoTichDossier
spring.cloud.stream.bindings.DVCLTDossierHoTichOut.group=DVCLT
spring.cloud.stream.bindings.DVCLTDossierLogIn.destination=DVCLTDossierLog
spring.cloud.stream.bindings.DVCLTDossierLogIn.group=DVCLT

# KHCN Kafka Configuration
spring.cloud.stream.bindings.KHCNDossierTrackingOut.destination=KHCNDossierTracking
spring.cloud.stream.bindings.KHCNDossierTrackingOut.group=KHCN
spring.cloud.stream.bindings.KHCNDossierOut.destination=KHCNDossier
spring.cloud.stream.bindings.KHCNDossierOut.group=KHCN
spring.cloud.stream.bindings.KHCNDossierLogIn.destination=KHCNDossierLog
spring.cloud.stream.bindings.KHCNDossierLogIn.group=KHCN
spring.cloud.stream.bindings.syncDossierPMCLogOut.destination=syncDossierPMCLog.test-1
spring.cloud.stream.bindings.syncDossierStatusPMCLogOut.destination=syncDossierStatusPMCLog.test-1

spring.cloud.stream.bindings.smartReaderIn.destination=OCRSmartReaderFileHCM
spring.cloud.stream.bindings.smartReaderIn.group=OCRSmart
digo.ocrSmartReader.build.config=5f7c16069abb62f511890042
digo.ocrSmartReader.token= 8928skjhfa89298jahga1771vbvb
digo.ocrSmartReader.client_session= 00-14-22-01-23-45-1548211589291
digo.dossier.ocrsmart-showAI-classify-percentageValid=1

spring.servlet.multipart.enabled= true
spring.servlet.multipart.max-file-size= 200MB
spring.servlet.multipart.max-request-size= 200MB

logging.level.org.springframework.web.client.RestTemplate=DEBUG
logging.level.org.springframework.data.mongodb.core.MongoTemplate=DEBUG
logging.level.org.springframework.cache=TRACE


vnpt.permission.debbuger.enable=true
vnpt.permission.interceptor.enable=true
vnpt.permission.rbac.enable=true
digo.permission.rbac.secret=VnptRbac@2022
vnpt.permission.integratedconfiguration=manageDigo
vnpt.permission.vpub=manageQLVB
vnpt.permission.dvcqg=manageDigo
vnpt.permission.integrationservice=manageDigo
vnpt.permission.ktm=manageDigo
vnpt.permission.mappingdata=manageDigo
vnpt.permission.mappingtype=manageDigo
vnpt.permission.chungthuc=manageDigo
vnpt.permission.authorize=manageDigo
vnpt.permission.integrated=manageDigo
vnpt.permission.addtexttopdf=manageDigo
vnpt.permission.asxh=manageDigo
vnpt.permission.auth=oneGateAuthIdentity
vnpt.permission.bdg-connect=manageDigo
vnpt.permission.bdg-connect-log=manageDigo
vnpt.permission.moc=manageDigo
vnpt.permission.business-household-registration=manageDigo
vnpt.permission.budget-relationship=manageDigo
vnpt.permission.chatbot=manageDigo
vnpt.permission.citizen=manageDigo
vnpt.permission.construct-ktm=manageDigo
vnpt.permission.citizen-info=manageDigo
vnpt.permission.dbn-connect-bxd=manageDigo
vnpt.permission.dbn-digitize=manageDigo
vnpt.permission.dbn-vnpost=manageDigo
vnpt.permission.digital-signature=manageDigo
vnpt.permission.dossier-event-log-error=manageDigo
vnpt.permission.dvc-kontum=manageDigo
vnpt.permission.e-invoice=manageDigo
vnpt.permission.e-receipt=manageDigo
vnpt.permission.e-receipt-viettel=manageDigo
digo.permission.e-receipt-mobi-kgg=manageDigo
digo.permission.e-receipt-misa-kgg=manageDigo
vnpt.permission.hcmlgsp-dongbo-danhmuc-philephi=manageDigo
vnpt.permission.ilis=manageDigo
vnpt.permission.bdg-sct=manageDigo
vnpt.permission.hcm-vietinfo-receipt=manageDigo
vnpt.permission.integrated-gtvt=manageDigo
vnpt.permission.inter-log=manageDigo
vnpt.permission.ipcc-voice=manageDigo
vnpt.permission.iportal-article=manageDigo
vnpt.permission.civil-status=manageDigo
vnpt.permission.judicial=manageDigo
vnpt.permission.btxh-ktm=manageDigo
vnpt.permission.lgsp-dmdungchung-btttt=manageDigo
vnpt.permission.lgsp-hcm-1022=manageDigo
vnpt.permission.xacnhangoitinmc=manageDigo
vnpt.permission.lgsphcm-thanhphanhoso=manageDigo
vnpt.permission.lgsphcm-dkdn=manageDigo
vnpt.permission.lgsp-hcm-dossier=manageDigo
vnpt.permission.lgsp-hcm-civil-status=manageDigo
vnpt.permission.lgsp-hcm-lltp=manageDigo
vnpt.permission.lgsp-hcm-log=manageDigo
vnpt.permission.log-sync-nps=manageDigo
vnpt.permission.log-sync-integrated=manageDigo
vnpt.permission.lgsp-hcm-procedures=manageDigo
vnpt.permission.lgsp-hcm-question-answer=manageDigo
vnpt.permission.lgsphcm-dongbodanhmuclinhvuc=manageDigo
vnpt.permission.lgsphcm-dvcqg=manageDigo
vnpt.permission.lgsphcm-vnpost=manageDigo
vnpt.permission.lgsp-hcm-gplx=manageDigo
vnpt.permission.lgspminhtue-bo-tuphap=manageDigo
vnpt.permission.lgsp-minhtue=manageDigo
vnpt.permission.evn=manageDigo
vnpt.permission.lgsp-tandan=manageDigo
vnpt.permission.lgsp-tandan-jr=manageDigo
vnpt.permission.mapping-category=manageDigo
vnpt.permission.transport=manageDigo
vnpt.permission.national-citizens=manageDigo
vnpt.permission.national-for-storage=manageDigo
vnpt.permission.neac-sign=manageDigo
vnpt.permission.notification-app-citizens=manageDigo
vnpt.permission.nps-dossier=manageDigo
vnpt.permission.ocr=manageDigo
vnpt.permission.payment-platform=manageDigo
vnpt.permission.payment-platform-hcm=manageDigo
vnpt.permission.petition-nps=manageDigo
vnpt.permission.qlvb=manageDigo
vnpt.permission.family=oneGateHouseholdInfo
vnpt.permission.bhxh=manageDigo
vnpt.permission.btxh=manageDigo
vnpt.permission.sso-hue=manageDigo
vnpt.permission.tbnohtttlqni=manageDigo
vnpt.permission.tthc-hue=manageDigo
vnpt.permission.ubmttq=manageDigo
vnpt.permission.vbdlis=manageDigo
vnpt.permission.viettel-pki=manageDigo
vnpt.permission.vilis=manageDigo
vnpt.permission.vnpay-bdg=manageDigo
vnpt.permission.vnpost-bdg=manageDigo
vnpt.permission.vnpost=manageDigo
vnpt.permission.kontum-vnpost=manageDigo
vnpt.permission.vnpost-status=manageDigo
vnpt.permission.smart-ca=manageDigo
vnpt.permission.zalo=manageDigo
vnpt.permission.zalov2=manageDigo
vnpt.permission.smart-ca-v2=manageDigo
vnpt.permission.integrated.scope=manageIntegratedConfig
vnpt.permission.integrated.SCT=SCT
vnpt.permission.npadsvc=manageDigo
vnpt.permission.edig=manageDigo
vnpt.permission.integratedevent=manageDigo
vnpt.permission.email=admin
vnpt.permission.sms=admin
vnpt.permission.vnptpay=manageDigo
vnpt.permission.budget=manageDigo
vnpt.permission.business-registration=manageDigo
vnpt.permission.business-registration-tandan=manageDigo
vnpt.permission.lgsp-hcm=manageDigo
vnpt.permission.v2.sms=manageDigo
vnpt.permission.v2.email=manageDigo
vnpt.permission.dvc-lt=manageDigo
vnpt.permission.lgspqnm-digital-signature=manageDigo
vnpt.permission.check-user=manageDigo
vnpt.permission.gtvt=manageDigo
vnpt.permission.gplx-gtvt-hgg=manageDigo
vnpt.permission.qti-gtvt=manageDigo
vnpt.permission.khcn=manageDigo
vnpt.permission.api.allow=POST:/lgsp-hgi/--list-vanban,POST:/lgsp-hgi/--total-vanban,POST:/lgsp-hgi/--danh-sach-file-hgi,POST:/hbh-connect-msns/--sys-dossier,GET:/lgsp-hbh/root/**,POST:/lgsp-hbh/root/**,GET:/tsdc/--get-info-student,GET:/bdg-connect-log/--resend-failed,GET:/bo-tuphap/get-all-don-vi,GET:/bo-tuphap/get-by-id/{id},GET:/budget/--by-period,GET:/budget/--detail,GET:/budget/--list,GET:/budget-relationship/--file-details,GET:/budget-relationship/--list-file,GET:/budget-relationship/--results-list,GET:/chatbot,GET:/citizen/--info,GET:/civil-status/--category,GET:/dkdn/--get-detail-enterprise-get,GET:/dkdn/--get-detail-file-register-get,GET:/dkdn/--get-detail-in-time-get,GET:/dkdn/--get-handling-file-in-day-get,GET:/e-receipt/--cancel,GET:/e-receipt/--download-receipt-by-fkey,GET:/e-receipt/--payment,GET:/e-receipt/--print,GET:/e-receipt/--print-convert,GET:/e-receipt/--print-no-pay,GET:/e-receipt/--unpayment,GET:/integrated-configuration/--params,GET:/judicial/--category,GET:/lgsp-dkdn/--layDanhSachHoSoTrongKhoangThoiGian,GET:/lgsp-dkdn/--layDanhSachHoSoTrongNgay,GET:/lgsp-dkdn/--layThongTinChiTietDoanhNghiep,GET:/lgsp-dkdn/--layTinhTrangHoSo,GET:/lgsp-dvcqg/--traCuuHoSo,GET:/lgsp-hbh/trinam/**,GET:/lgsp-hbh/trinam/Budget/**,GET:/lgsp-hbh/trinam/BusinessRegistration/**,GET:/lgsp-hcm-question-answer/--get-question-lgsp-hcm,GET:/lgspHcmVnpost/getOrder,GET:/lgspHcmVnpost/getPrice,GET:/lgsp-minhtue/--search-dossier-transportation,GET:/lgsp-minhtue/--statistical-transportation,GET:/lgsp-tandan/--detail-business,GET:/lgsp-tandan/--detail-record,GET:/lgsp-tandan/getThongTinDangKyPhuongTien,GET:/lgsp-tandan/--list-file,GET:/lgsp-tandan/--reception-record,GET:/lgsp-tandan-jr/--category,GET:/mapping-data,GET:/npadsvc/--question-ministry,GET:/payment-platform/--get-agency-beneficiary-account-bdg,GET:/payment-platform/--get-beneficiary-account,GET:/payment-platform/--get-init-qrimage-paymentplatform-bdg,GET:/registry/--detail,GET:/sms-brandname/--send-sms-public,GET:/transport/--detail,GET:/transport/--list,GET:/transport/--statistic,GET:/vgca/--upload-signed-file,GET:/vnpay-bdg/--get-init-qrimage-vnpay-bdg,GET:/vnpost/token,GET:/vnpostBdg/--delete-order,GET:/vnpt-pay/--get-agency-beneficiary-account-bdg,GET:/zalov2/--send-template,POST:/api/lienthongDVCLT/capNhatTrangThaiHoSoDVCLT,POST:/api/lienthongDVCLT/nhanHoSoDVCLT,POST:/api/lienthongDVCLT/receiveRecord,POST:/api/lienthongDVCLT/capNhatTrangThaiHoSoDVCLTHoTich,POST:/bdg-connect-log/--payment,POST:/bdg-connect-log/--post-fpt1gate-expertise,POST:/bdg-connect-log/--post-fpt1gate-payment,POST:/bhxh/--get-masobhxh-by-tieuchi,POST:/bhxh/hcm/--get-masobhxh-by-tieuchi,POST:/bhxh/hcm/--get-tthgd-by-masobhxh,POST:/bhxh/hcm/--get-tthgd-by-tieuchi,POST:/bo-tuphap/get-all-document-type,POST:/bo-tuphap/get-list-attach,POST:/bo-tuphap/timkiem-vanban,POST:/btxh/--push-sync-dossier,POST:/btxh/--push-sync-status-dossier,POST:/btxh-ktm/--push-sync-dossier,POST:/btxh-ktm/--push-sync-status-dossier,POST:/civil-status/--send,POST:/construct/--sync-info-dossier,POST:/digital-signature/--sign,POST:/dkdn/--get-detail-enterprise,POST:/dkdn/--get-detail-file-register,POST:/dkdn/--get-detail-in-time,POST:/dkdn/--get-handling-file-in-day,POST:/ekyc/--translate,POST:/e-receipt/--issue,POST:/e-receipt/--issue-payment,POST:/evn/lookup-consume-district,POST:/evn/lookup-consume-province,POST:/evn/lookup-invoice,POST:/evn/lookup-power-cut,POST:/evn/lookup-progress,POST:/judicial/--get-category,POST:/judicial/--send,POST:/KonTumVnpost/--get-price,POST:/KonTumVnpost/--post-order,POST:/lgsp-minhtue/--request-list-dossier-transportation,POST:/lgsp-tandan/dvc/v1/getToken,POST:/lgsp-tandan/--get-all-coquanbientap,POST:/lgsp-tandan/--get-all-loai-vanban,POST:/lgsp-tandan/--get-all-vanban,POST:/lgsp-tandan/--get-by-id,POST:/lgsp-tandan/--get-list-attach,POST:/neac-sign/--get-certificate,POST:/neac-sign/--sign,POST:/npadsvc/{configId}/{deploymentId}/addtthcKhuyenMai,POST:/npadsvc/{configId}/{deploymentId}/updatetthcKhuyenMai,POST:/npadsvc{configId}/{deploymentId}/addtthcKhuyenMai,POST:/npadsvc{configId}/{deploymentId}/QG/addtthcKhuyenMai,POST:/npadsvc{configId}/{deploymentId}/update/nhanhsdvcqg,POST:/npadsvc{configId}/{deploymentId}/updatetthcKhuyenMai,POST:/payment-platform/--confirm,POST:/payment-platform/--query-update,POST:/payment-platform/--confirm-bdg,POST:/payment-platform/dpnhankqthanhtoanhs,POST:/payment-platform/dptracuuthanhtoanhs,POST:/payment-platform/--get-bill,POST:/payment-platform/--init,POST:/payment-platform/--xac-nhan-thanh-toan,POST:/payment-platform/--tra-cuu-thanh-toan,POST:/payment-platform/--nhan-ket-qua-thanh-toan,POST:/payment-platform/--init-dvc,POST:/payment-platform/--init-bdg,POST:/payment-platform-hcm/--payGate,POST:/payment-platform-hcm/--payReceive,POST:/smart-ca/--sign,POST:/v2/email/--send-with-template,POST:/v2/sms-brandname/--send-with-template,POST:/vgca/--upload-signed-file,POST:/viettel-pki/{id}/--sign-by-file,POST:/vnpay-bdg/--confirm-vnpay-bdg,POST:/vnpost/--get-price,POST:/vnpost/--post-order,POST:/vnpostBdg/--get-price,POST:/vnpostBdg/--post-order,POST:/vnpt-pay/--confirm,POST:/vnpt-pay/--confirm-bdg,POST:/vnpt-pay/get-init,POST:/vnpt-pay/get-init-bdg,POST:/vnpt-pay/--init,POST:/zalo/--send-message,POST:/zalov2/--send,POST:/zalov2/--send-template-by-step,GET:/transport-dbn/--detail,GET:/transport-dbn/--list,GET:/transport-dbn/--statistic,GET:/vnpt-pay/--generate-qr-code,POST:/vnpt-pay/--check-order-qr-code,POST:/vnpt-pay/--confirm-qr-code,POST:/bhxh/--get-masobhxh-by-tieuchi-bdg,POST:/payment-platform/--xac-nhan-thanh-toan,POST:/lgsp-cmu/--danh-sach-vbdi-dph-lt-igate,POST:/lgsp-cmu/--danh-sach-file-lien-thong-igate,POST:/sms-brandname/--send-batch,GET:/lgsp-kgg/--get-ent-kgg,GET:/lgsp-kgg/--get-coop-kgg,GET:/lgsp-kgg/--get-house-kgg,GET:/lgsp-kgg/--get-ent-dossiers-kgg,GET:/lgsp-kgg/--get-coop-dossiers-kgg,GET:/lgsp-kgg/--get-house-dossiers-kgg,POST:/v2/sms-brandname/--send-with-template-qnm,POST:/lgsp-minhtue-cr/--send,GET:/agg-budget/--list,GET:/agg-budget/--insert,GET:/agg-budget/--detail,GET:/agg-budget/--by-period,POST:/qti-gtvt/--apply-online,GET:/transport-dbn/gplx/list-dossier,GET:/transport-dbn/gplx/detail-dossier,GET:/vnpay-bdg/--create-body-init-bdg-ver2-image,POST:/email/--send-by-agency,GET:/nps-dossier/{configId}/downloadfile,POST:/lgspHcmVnpost/getPrice,PUT:/dossier-payment/{id}/--update-bill,GET:/e-receipt-viettel/--payment-soap,DELETE:/e-receipt-viettel/--cancel-soap,GET:/e-receipt-viettel/--unpayment-soap,POST:/e-receipt-viettel/--issue-soap,GET:/lgsp-tandan/KHDT/**,GET:/lgsp-tandan/KHDT/listDossier/**,GET:/lgsp-tandan/KHDT/process/**,GET:/lgsp-dkdn/--layThongTinDangKyKinhDoanhDoanhNghiep,GET:/lgsp-hbh/KHDT/listDossier/**,POST:/api-integration/{service}/--update-dossier-no-token,GET:/BKHDT/getDanhSachDN,GET:/BKHDT/getDanhSachHTX,GET:/BKHDT/getDanhSachHKD,GET:/BKHDT/getDetailDN,GET:/BKHDT/getDetailHKD,GET:/BKHDT/getDetailHTX,GET:/BKHDT/getQTXL_DN,GET:/BKHDT/getQTXL_HTX,GET:/BKHDT/getQTXL_HKD,GET:/BKHDT/getChiTietDoanhNghiep,GET:/BKHDT/getChiTietHKD,GET:/BKHDT/getChiTietHTX,GET:/BKHDT/getChiTietThongTinMoiNhatDN,GET:/BKHDT/getChiTietThongTinMoiNhatHKD,GET:/BKHDT/getChiTietThongTinMoiNhatHTX,GET:/integrated-configuration/{id},POST:/kgg-eoffice/put-attachment-to-ioffice,POST:/api/lienthongKHCN/nhanHoSoKHCN,POST:/api/lienthongKHCN/capNhatTrangThaiHoSoKHCN,POST:/api/lienthongKHCN/receiveRecord,POST:/api/lienthongKHCN/capNhatTrangThaiHoSoKHCNPost,GET:/api/lienthongKHCN/getLog,GET:/api/lienthongKHCN/getLog/{id}
vnpt.permission.credentials.allow=POST:/lgsp-hgi/--list-vanban,POST:/lgsp-hgi/--total-vanban,POST:/lgsp-hgi/--danh-sach-file-hgi,GET:/petition/--search-accept,POST:/vnpt-pay/--confirm,POST:/payment-platform/--confirm,POST:/lgsp-tandan/dvc/v1/getToken,POST:/npadsvc/{configId}/{deploymentId}/addtthcKhuyenMai,POST:/npadsvc/{configId}/{deploymentId}/updatetthcKhuyenMai,POST:/npadsvc{configId}/{deploymentId}/addtthcKhuyenMai,POST:/npadsvc{configId}/{deploymentId}/updatetthcKhuyenMai,GET:/civil-status/--category,GET:/judicial/--category,GET:/npadsvc/--question-ministry,POST:/bhxh/--get-masobhxh-by-tieuchi,POST:/vgca/--upload-signed-file,POST:/bhxh/hcm/--get-masobhxh-by-tieuchi,POST:/bhxh/hcm/--get-tthgd-by-masobhxh,POST:/bhxh/hcm/--get-tthgd-by-tieuchi,POST:/bdg-connect-log/--post-fpt1gate-expertise,POST:/vnpt-pay/--confirm-bdg,POST:/payment-platform/--confirm-bdg,POST:/api/lienthongDVCLT/nhanHoSoDVCLT,POST:/api/lienthongDVCLT/capNhatTrangThaiHoSoDVCLT,POST:/api/lienthongDVCLT/capNhatTrangThaiHoSoDVCLTHoTich,POST:/api/lienthongDVCLT/receiveRecord,GET:/payment-platform/--get-beneficiary-account,POST:/payment-platform/--init,POST:/vnpostBdg/--get-price,POST:/payment-platform/dptracuuthanhtoanhs,POST:/payment-platform/dpnhankqthanhtoanhs,GET:/lgsp-dkdn/--layThongTinChiTietDoanhNghiep,GET:/vnpt-pay/--generate-qr-code,POST:/vnpt-pay/--check-order-qr-code,POST:/vnpt-pay/--confirm-qr-code,POST:/bhxh/--get-masobhxh-by-tieuchi-bdg,POST:/payment-platform/--xac-nhan-thanh-toan,POST:/lgsp-cmu/--danh-sach-vbdi-dph-lt-igate,POST:/lgsp-cmu/--danh-sach-file-lien-thong-igate,GET:/transport/--list,GET:/transport/--detail,GET:/transport/--statistic,GET:/lgsp-kgg/--get-ent-kgg,GET:/lgsp-kgg/--get-coop-kgg,GET:/lgsp-kgg/--get-house-kgg,POST:/lgsp-minhtue-cr/--send,POST:/email/--send-batch,GET:/lgsp-tandan/KHDT/**,GET:/lgsp-tandan/KHDT/listDossier/**,GET:/lgsp-tandan/KHDT/process/**,POST:/api-integration/{service}/--update-dossier-no-token,POST:/kgg-eoffice/put-attachment-to-ioffice,POST:/api/lienthongKHCN/nhanHoSoKHCN,POST:/api/lienthongKHCN/capNhatTrangThaiHoSoKHCN,POST:/api/lienthongKHCN/receiveRecord,POST:/api/lienthongKHCN/capNhatTrangThaiHoSoKHCNPost
digo.microservice.deployment.id=5ee091507d567c9fe29f82fa
vnpt.permission.checkCitizenLog=manageDigo
vnpt.permission.lgsp-hgi-lltp=manageDigo
vnpt.permission.apiintegration=manageDigo
vnpt.permission.errorlog=manageDigo
vnpt.permission.grpc=manageDigo
vnpt.permission.bdg-connect-v2=manageDigo,bdgConnectShared
vnpt.permission.vnpost-5343=VNPOST5343
smartCA.config-id=61a04ea9719131c01b89b036
smartCA.domain=https://gwsca.vnptit.vn
v2.smartCA.config-id=63c6158782c8637ebd94289f
v2.smartCA.domain=https://gwsca.vnpt.vn/sca/sp769/v1
vnpt.permission.idCheck=idCheck
vnpt.permission.construct-hpg=manageDigo

lgsp.hcm.token=ewoiQWNjZXNzS2V5IjoiNTc4OGFhYmNlNGIwODM2ZGVmMzY3YTI3IiwKIlNlY3JldEtleSI6IkE4OGpzRjFLMUZ2Z1djZjl2V2IzMGVDQno4NFVVaXdSVlNLZEppeUc3diIsCiJBcHBOYW1lIjogInZucHRoY20iLAoiUGFydG5lckNvZGUiOiAiMDAwLjAwLjE1LkgyOSIsCiJQYXJ0bmVyQ29kZUN1cyI6ICIwMDAuMDAuMTUuSDI5Igp9

ocr.eKyc.token-id=d22733ed-4082-5ed7-e053-62199f0a464b
ocr.eKyc.token-key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAICYD05LgDQXk+Yp8Mspq/QA+qDo1XB2EUlz+KO+NeKer4kGMLPfT8iszTKc6AV5Wez+SumELeWIlJVISIyTVeMCAwEAAQ==
ocr.eKyc.access-token=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.E4atYbrQvAKUdlCxQe3ORXsA2iUVU4qwG4_riyJJAz4DSzKad7WAB6QHb5sXVsg5GNXLtL3pUWORuBnbXzzddKO-LV80L_gIJKN4GpwbLvyVYESzGgZWlil8Bl9x5gzHUr17fQa2AyViXgPvCcmjk9REL1Ymn8CEgvAlh9A18fJmTKK7clWVUNsmdJ-p1eB2cgT0ijDfrUcTfOMu1b_RvXUQZdxIgxm3qTGDZ8nTiXV2qo1tp41tM97YTImQcf8y_jetDi-NcMY8mhXCkfUdDkf--JHKryEtGHeu7QiLtHrl9oqBftEbI8ZqyrbjjaEP5gVpHatlvvih8cS1NPfbQw
ocr.eKyc.origin=https://api.idg.vnpt.vn
digo.http.delay-time=5
report.agency.id="60c868a4289bad69c7cbffea"
report.url.web=https://motcuatest.vnptigate.vn/vi
vnpost.schedule.token=d261129d-7213-32bf-a63b-15548342caf5
vnpost.schedule.url=https://api.dienbien.gov.vn/
vnpost.schedule.deploymentId=5ee091507d567c9fe29f82fa
vnpost.schedule.configId=6285a10a76739019277b9b12
lgspminhtue.bdg.configId=62bec102e305ba7cf0803562
#digo.lgsphcm.configid=6285cbfe76739019277b9b69
digo.lgsphcm.configid = 6285cbfe76739019277b9b69
digo.thread.sync.lgsphcm.dossier.max.poll=4000
digo.thread.sync.lgsphcm.dossier.max.poll.enable=false
digo.rest.connection.create.hcm=false
digo.rest.connection.max.total=1000
digo.rest.connection.max.per.total=100
digo.rest.connection.request-connect-timeout=1000
digo.lgsphcm.gtvt.agencyCode = 000.00.00.H29
digo.gtvtagg.configid = 66c2fcd6b0398b0f4cc3fce7
digo.lgsphcm.1022.configid=6285cbfe76739019277b9b96
digo.lgsphcm.configMsns=5f7c16069abb62f511890035
statistic.dossier.sync.configid=61a71563ead60447c1134853
#lgspminhtue.tntm.configid=640168109d4e892e87db4240
lgspminhtue.tntm.configid=665e6e3344449e08e2fdfc67
lgspminhtue.tntm.configAgencyBoTNMT.enable=false
lgsp.tnmt.isgli=true
lgsp.tnmt.isqnm=false
qlvb.minhtue.configid=62bec102e305ba7cf0803562
lgspminhtue.kgg.configId=634fa08d1802493a28dd8f72
digo.config.ereceipt.hcm.transmitdossiercodeviabldt=false
digo.config.ereceipt.hcm.agency.transmitdossiercodeviabldtagencyids=
lgsp.kgg.url=https://api.kiengiang.gov.vn
lgsp.kgg.authorization=NUtZaEx4bHd4cG5HMVozcWFDZmg1SDVoMnBVYTpTdnM4Nnh3MlkxVF9xaFo3WVpmaW5lM1ZZZFlh
nationpublic.kgg.configId=630d652730da0b35a078b32e
zalov2.kgg.configId=634fc8611802493a28ddb033
eoffice.kgg.configId=67eba7d1d8dca26fa53c5c10
vnpt.permission.kgg.eoffice=manageDigo
digo.tnmt.configid=5fc704bc86940eab2fb45998
vnpt.connect-csdldc=false
vnpt.sync-national=0
vnpt.sync-national.ignore-check-reused=false
vnpt.sync-national.document-as-result=false
vnpt.sync-national.document-as-input=false
vnpt.sync-national.sendMaKQThayThe=true
vnpt.sync-national.callCodeFee=false
vnpt.sync-national.checkPaymentAutoOnline=false
vnpt.sync-national.checkPaymentFreeDni=false
vnpt.sync-national.autoCheckMaCSDL=false

lgsp.tnmt.hcm.configid=646ee20c4fd67e7cef292bc4
lgsp.tnmt.ishcm=false
lgsp.tnmt.hcm.schedule.enable=true
lgsp.tnmt.hcm.schedule.moc.get-dossier.cron=0 0 6,12,18 ? * * 

hcm.socialprotection.enable=false
hcm.sendsms.sendDistinctMessages.enable=false
hcm.removeToneMarkSMS.enable=0
digo.thread.sync.hcm.lgsp.enable=false

digo.dvclt.configid = 62948261ce7a6d62d41c10c1

#gtvt.qti.configid=65b1cfd5a93e5d24cad648b3
gtvt.qti.configid=66441fe44f5228741a30dfea
gtvt.qti.codeAgency=H50.13

ktm.socialprotection.serviceid=9fec110694fc62f5c1897d22
ktm.socialprotection.mapProvince.serviceid=b02f707a62681a8b4f9e5f04
ktm.socialprotection.mapDistrict.serviceid=5f417a5d9268af8bef919f04
ktm.socialprotection.mapCommue.serviceid=5fcd8b5d92b5c3aa6f9e1104
socialprotection.configid=63f7300570754e2f140e3743
socialprotection.serviceid=9f7c11069afc62f511897022
socialprotection.mapProvince.serviceid=5fc0707a62681a8bef9e5f04
socialprotection.mapDistrict.serviceid=5fc07a5d92681a8bef9e5f04
socialprotection.mapCommue.serviceid=5fc07a5d92b5c38bef9e5f04
socialprotection.mapStatus.serviceid=5fc07c9c8c1fc38bef9e5f04
socialprotection.getDossierWithToken.enable=false
socialprotection.oidc.client-id=localhost
socialprotection.oidc.client-secret=clientsecret

ktm.monre.serviceid=58fcb704c1d24e05ce5851cb
ktm.monre.mapProvince.serviceid=9ace807a62681a8b4f9b19f4
ktm.monre.mapDistrict.serviceid=88ae807a62681a8b4f9b1c3f
ktm.monre.mapCommue.serviceid=b4c97b5d92b5c3aa6f94a87f
ktm.monre.mapDossierStatus.servceid=be697c9c8c1fc38bef9e56cc
ktm.enterpriseTax.serviceid=65fb91eb8264b2e3cf0498e3
nbrs.serviceid=6618a3a94957f324c305a61b
nbrs.mapDossierStatus.serviceid=6629b093728c6a6df3c566d2
nbrs.mapDossierAgency.serviceid=6629b093728c6a6df3c568a1
vnpt.integration.nbrs.sync.enable=false


#btttt.lgspminhtue.configId=641d1aba08ed4d5367d4400a
btttt.lgspminhtue.configId=642138e7451314560db55c9d
fpt1gate.tokengateway=https://api.binhduong.gov.vn:8687/token
fpt1gate.key=iFibF2hYfVVNtFDbuGAr0vRtniUa
fpt1gate.secret=zpn1y2EdaSwMSjWTtK0DGF39Lvca
digo.adapter.bdg.log.enable=true
#prd
digo.gplxgtvt.configid=641aaef622442a0293406332
#test
#digo.gplxgtvt.configid=643f6a0f6dbd594d2e976def
#Cache
digo.cache.security-key=Vnpt2021
digo.payment-platform.check=true
digo.vnptpay.check=true
digo.vnptpay.url.allow=https://sandboxpaydev.vnptmedia.vn,https://paymentgw.vnptmoney.vn,https://sandboxpay.vnptmedia.vn/rest/payment/v1.0.5/,https://api.binhduong.gov.vn:8687/API-DVCQG/1.0/payment-api/rest/payment/v1.0.6/,https://paymentapi.vnptmoney.vn

spring.jackson.time-zone=Etc/GMT-7

#actoutor
management.endpoints.web.exposure.include=*

#mail
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=hxpgatnlyaoenadn
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

#start - spring clound config
spring.test-value = test001
spring.cloud.config.name=svcAdapter
spring.cloud.config.profile=tgg
spring.cloud.bus.id=${spring.cloud.config.name}-${spring.cloud.config.profile}
spring.profiles.active=${spring.cloud.config.profile}
spring.cloud.bus.enabled=true
spring.cloud.bus.refresh.enabled=true
spring.cloud.config.fail-fast=false
spring.cloud.config.retry.max-attempts=10
spring.cloud.config.retry.max-interval=10000
spring.cloud.config.retry.initial-interval=2000
#end - spring clound config

#config connection rest teamplate
digo.rest.connection.connect-timeout=10000
digo.rest.connection.read-timeout=25000
#set timeout for each module
digo.rest.connection.module.set-timeout=false

digo.dvclt.security-key=igate2.service.dvclt08062023.test#TransformINS

# KHCN Configuration
digo.khcn.security-key=igate2.service.khcn08062023.test#TransformINS
digo.khcn.enable=true
digo.khcn.configid=khcn-config-id-placeholder

#config igate1

digo.oidc.client.id=igate1
digo.oidc.client.secret=49bcdbed-033b-4a37-a556-e4a696e4a4bd
#bdg properties
notify.telegram.id=
notify.telegram.token=
notify.telegram.groupId=
#Quota
alert.quota.enable=true
alert.quota.time=300
alert.quota.limit=5
vnpt.permission.configController=manageDigo
vnpt.permission.lltp-vneid=manageDigo
#DLK
dlk.lgsp.lltp.configId=659e14b3945fa14a8b19a148
vnpt.permission.dlk-lgsp-lltp=manageDigo
dlk.lgsp.http.configId=659e131e945fa14a8b19a147
vnpt.permission.dlk-lgsp-http=manageDigo

#DBN-C?p ??n v?
digo.dbn.level-agency-1=5f39f4335224cf235e134c5b
digo.dbn.level-agency-2=5f39f4155224cf235e134c59
digo.dbn.level-agency-3=5fe2f3cdc229f60186af8cdf
digo.dbn.rootAgency.id=60a4d2589b05fd04a5381ffd
digo.dbn.check=true
digo.schedule.ldxh-dbn.get-dossier.cron=0 0 22 * * *

#CMU - config
cmu.iLisCode=0
digo.cmu.iLisCode=0
digo.permission.lgsp-cmu-lltp=manageDigo

# ===================================================================
#poolexecutor: 1->AboutPolicy, 2->CallerRunsPolicy, 3->DiscardPolicy, 4->DiscardOldestPolicy
#corepoolsize: is the minimum number of workers to keep alive
#maxpoolsize: defines the maximum number of threads that can ever be created
# 1. If the number of threads is less than the corepoolsize, create a new Thread to run a new task
# 2. If the number of threads is equal (or greater than) the corepoolsize, put the task into the queue
# 3. If the queue is full, and the number of threads is less than the maxpoolsize, create a new thread to run tasks in
# 4. If the queue is full, and the number of threads is greater than of equal to maxpoolsize, reject the task follow rule of poolexecutor
# ===================================================================
thread.poolexecutor=2
thread.corepoolsize=50
thread.maxpoolsize=100
thread.queuecapacity=1000
thread.sleeptime=1000
scheduler.thread.corepoolsize=10
digo.thread.sync.dvcqg.enable=true
digo.thread.sync.hcm.emc.enable=false
digo.thread.sync.dvcqg-lgsphcm.enable=true
digo.thread.resync.dvcqg-lgsphcm.enable=false
digo.thread.resync.dvcqg.enable=false
digo.thread.sync.dossier.hcm.enable=false
digo.thread.sync.dossier.hcm.dvcqg.enable=false
digo.thread.sync.dossier.hcm.dvcqg-lgsphcm.enable=false
digo.thread.sync.dossier.cdtd.hcm.enable=false

digo.sync-dossier.unsetAppointmentDate=false

digo.payment-platform.listStatusNotCall=6,12,13

lgsp.tnmt.hbh.configid=653b6c622c2aa064e6156f28
lgsp.tnmt.hbh.schedule.get-dossier.cron=-
digo.upload-by-url-whitelist=tiengiang.gov.vn;i.imgur.com;10.0.24.131;10.159.13.199;14.225.12.119;10.128.2.50;10.144.124.5;10.159.136.9;10.178.124.3;10.201.0.7;110.201.0.8;10.201.0.9;10.201.0.10;dt.mplis.gov.vn;qng.mplis.gov.vn;sct-qlvb.neoegov.vn;vpdt-sct.tphcm.gov.vn;hcm.mplis.gov.vn;mdm1.monre.gov.vn;mdm.monre.gov.vn;dvcbtxh.molisa.gov.vn;api.kiengiang.gov.vn;api-thuphiviahe.hcmtelecom.vn;dangkykinhdoanh.angiang.gov.vn;
digo.thread.lgsphcm.dossier-sync-config-id=653aa89b0c0dd4389a8d1dae
digo.thread.emc.dossier-sync-config-id=678dfcae93872e678ece7e07
digo.schedule.hbh-bxd.sync-nohtttl-hoso.cron=-
digo.thread.lgsphcm.dossier-pmc-sync-config-id=653aa89b0c0dd4389a8d1dae
digo.thread.lgsphcm.check-nation-code=false

#hgi lltp
digo.integration.businessRegistrationHgi.enable=true
digo.schedule.hgilgsp-synchronize-dossier-lltp-resend-fail=false
digo.schedule.hgilgsp-synchronize-dossier-lltp-resend-fail_v2=false
digo.schedule.hgilgsp-synchronize-dossier-lltp-resend-count=10
digo.schedule.hgilgsp-synchronize-dossier-lltp-update-status-auto=false

#hgi bldt IGATESUPP-97593
hgi.bldt.onFeeDescription = false

#hcm lltp
digo.integration.businessRegistrationHCM.enable=true
digo.schedule.hcmlgsp-synchronize-dossier-lltp-resend-fail=false
digo.schedule.hcmlgsp-synchronize-dossier-lltp-resend-count=10
digo.schedule.hcmlgsp-synchronize-dossier-lltp-resend-fail-auto=true

#aims
vnpt.permission.aims=manageDigo
digo.X-Tenant-Config=blu-718a3edf-3e0d-11ec-8082-005056886ce6

digo.schedule.hbh-ldtbxh.sync-ketQuaCuoiCung.cron=-
digo.schedule.hbh-ldtbxh.sync-hoSoDangKy.cron=-
digo.schedule.hbh-ldtbxh.sync-tienTrinhXuLy.cron=-
cmu.socialprotection.disable=false
cmu.socialprotection.check-status.enable=false
cmu.socialprotection.check-agency.enable=false
cmu.socialprotection.check-address.enable=false
cmu.socialprotection.check-file.enable = false
cmu.socialprotection.set-dossier-field-value.enable = false
digo.schedule.hcmlgsp-synchronize-dossier-lltp-resend-deplay=10

cmu.csdlNganhCT.agencyId=637d7434f217d52a06d6d0f4

#VNPT PAY
bdg.digo.payment.vnptmoney.configid= 5fe9863a96be6e73af794674
bdg.digo.payment.microservice.subsystemId.onegate=5f7c16069abb62f511880003

#HT & LLTP
digo.judicial.civil.status.case=false
digo.judicial.criminal.records.case=false
digo.schedule.hbh-btc.sync-capMsDvNs.cron=-

#connect v2
digo.microservice.domain-ssov2=https://ssov2.vnptigate.vn
digo.oidc.client.idv2=storage
digo.oidc.client.secretv2=7b5b196b-4fcd-4dc7-a20f-8eb99a1ffd6d
digo.microservice.domain-apiv2=https://apiv2.vnptigate.vn
digo.deploymentid=5ee091507d567c9fe29f82fa

#QTI
digo.microservice.bussinessEnable=false
digo.integration.budgetEnable=false
digo.resyncdossier.upload-new-code=false
digo.due-dossier.enable=false
digo.due-dossier.config-id=65b85bea44b70669c9be3cbc
digo.due-dossier.sms-template=6629ef95ba7dc8449d6ecea2
digo.due-dossier.status=2,8,9,10,11,16,17
digo.due-dossier.ignore=false
digo.agency.qti.cap_so = 5f39f42d5224cf235e134c5a
digo.agency.qti.cap_huyen = 5f39f4155224cf235e134c59
digo.agency.qti.cap_xa = 5febfe2295002b5c79f0fc9f
digo.agency.qti.ubnd_tinh = 60b87fb59adb921904a0213e
digo.agency.qti.cap_phong_ban_thuoc_huyen = 641d4e6b6a47234197f97a0b

vnpt.gtvt.allow-public=false

digo.vbdlisKetThuc.used.v2=false
digo.vbdlisKetQuaThue.used.v2=false

digo.payment.callContentTT01=true
#T\u00c3\u00aan db eform
db.eform-db=svcFormio
vnpt.human.updateCmndCccd=true
lgsp.tnmt.hgg=false
lgsp.bxd.hgg=false
#QNM
digo.ilis.logilisQnm=false
digo.ilis.qnm.time-cron=0 0 18 * * *
digo.ilis.qnm.run-job=false
#AGG
digo.agg.agesb-config-id=65c03cd31b06f141b2e7e873
digo.agg.schedule.dkkd.cron=0 0 17,19,21,23 * * ?
digo.agg.schedule.dkkd.enable=false 
digo.qni.integreated-tnmt-id=5f8d0d55b54764421b7156a6
agesb.tnmt.configid=667bdc131c9ba678c9447090
#log http
digo.log.http.enable=false
digo.log.http.minhtue.enable=false

#log http vnpt
digo.log.http.vnpt.enable=false
digo.url.enable=false

#hcm
digo.dossier.paymentmethod.direction=5f7fca83b80e603d5300dcf4
digo.dossier.paymentmethod.online=5fd1c7811b53d8779bc9ea79

#pmcn
digo.pmcn.sync-hcm-dossiers-mddcdt=H29.123

#timezone
TZ=Etc/GMT-7
vnpt.permission.qlvbdh=manageDigo
lgsp.gli.qlvbdh.configid=66b974a61f7e451e6d542a26

digo.schedule.get-data-from-gtvt.year=2024
digo.schedule.get-data-from-gtvt.enable=false
digo.schedule.get-data-from-gtvt.cron-list=0 */15 21-23 * * *
digo.schedule.get-data-from-gtvt.cron-detail=0 */5 0-9 * * *
digo.schedule.get-data-gtvt.donvixuly=79
digo.schedule.sync-data-daily-gtvt.cron-list=0 */15 0-3 * * *
digo.schedule.sync-data-daily-gtvt.cron-detail=0 */5 1-6 * * *
digo.schedule.sync-data-daily-gtvt.enable=false
digo.schedule.sync-data-daily-gtvt.synchronization=false
digo.chungthuc.skip-file-url-sign=false
digo.chungthuc.allow-next-step-if-upload-file-fail=false

digo.schedule.vpc-ldtbxh.sync-ketQuaCuoiCung.cron=-
digo.schedule.vpc-ldtbxh.sync-hoSoDangKy.cron=-
digo.schedule.vpc-ldtbxh.sync-tienTrinhXuLy.cron=-
#deployhelper
digo.deploy-helper.enable=false
digo.deploy-helper.secret-key=Vnpt#2024@!
digo.batch.processing.size=10
resilience4j.retry.instances.myRetry.maxAttempts=3
resilience4j.retry.instances.myRetry.waitDuration= 500ms
resilience4j.retry.instances.myRetry.retryExceptions=java.io.IOException,java.net.ConnectException,java.util.concurrent.TimeoutException
resilience4j.retry.instances.myRetry.ignoreExceptionsWhenCircuitBreakerIsOpen= true
# cau hinh danh gia so luong cuoc goi dua tren khoang thoigian xac dinh
resilience4j.circuitbreaker.instances.myCircuitBreaker.slidingWindowType=COUNT_BASED
# xac dinh so luong cuoc goi duoc tinh vao ty le loi. neu slidingWindowType=TIME_BASED thi slidingWindowSize se la donvi thoigian
resilience4j.circuitbreaker.instances.myCircuitBreaker.slidingWindowSize= 10
# Nguong ty le loi, neu vuot qu? 50%, CircuitBreaker se chuyen sang trang thai "Open".
resilience4j.circuitbreaker.instances.myCircuitBreaker.failureRateThreshold= 50
# cau hinh so luong cuoc goi toi thieu de bat dau tinh nguong loi
resilience4j.circuitbreaker.instances.myCircuitBreaker.minimumNumberOfCalls=5
# thoigian cho trong trang thai "Open" truoc khi chuyen sang trang thai "Half-Open".
resilience4j.circuitbreaker.instances.myCircuitBreaker.waitDurationInOpenState= 20s
# So luong cuoc goi duoc phep trong trong thai "Half-Open". neu van loi se tiep tuc OPEN
resilience4j.circuitbreaker.instances.myCircuitBreaker.permittedNumberOfCallsInHalfOpenState= 3
resilience4j.circuitbreaker.instances.myCircuitBreaker.automaticTransitionFromOpenToHalfOpenEnabled = false

# CircuitBreakerVnPost
resilience4j.circuitbreaker.instances.myVNPostCircuitBreaker.slidingWindowType=COUNT_BASED
resilience4j.circuitbreaker.instances.myVNPostCircuitBreaker.slidingWindowSize= 10
resilience4j.circuitbreaker.instances.myVNPostCircuitBreaker.failureRateThreshold= 50
resilience4j.circuitbreaker.instances.myVNPostCircuitBreaker.minimumNumberOfCalls=5
resilience4j.circuitbreaker.instances.myVNPostCircuitBreaker.waitDurationInOpenState= 20s
resilience4j.circuitbreaker.instances.myVNPostCircuitBreaker.permittedNumberOfCallsInHalfOpenState= 3
resilience4j.circuitbreaker.instances.myVNPostCircuitBreaker.automaticTransitionFromOpenToHalfOpenEnabled = false

# CircuitBreakerLGSP
resilience4j.circuitbreaker.instances.myLGSPCircuitBreaker.slidingWindowType=COUNT_BASED
resilience4j.circuitbreaker.instances.myLGSPCircuitBreaker.slidingWindowSize= 10
resilience4j.circuitbreaker.instances.myLGSPCircuitBreaker.failureRateThreshold= 50
resilience4j.circuitbreaker.instances.myLGSPCircuitBreaker.minimumNumberOfCalls=5
resilience4j.circuitbreaker.instances.myLGSPCircuitBreaker.waitDurationInOpenState= 20s
resilience4j.circuitbreaker.instances.myLGSPCircuitBreaker.permittedNumberOfCallsInHalfOpenState= 3
resilience4j.circuitbreaker.instances.myLGSPCircuitBreaker.automaticTransitionFromOpenToHalfOpenEnabled = false

digo.csdldc-search.check.hcm = false
digo.list-csdldc.hcm = HoVaTen, SoDinhDanh, SoCMND, QuocTich, GioiTinh, NgayThangNamSinh, NoiOHienTai, ThuongTru
agg.integratedpowaco.serviceid=663d7eb442c515015770919e
agg.powaco.listStatus = [{'tenTrangThai':'huyhoso','giaTri':'0'},{'tenTrangThai':'datiepnhan','giaTri':'1'},{'tenTrangThai':'giahanhoso','giaTri':'2'},{'tenTrangThai':'dangxulyhosotiepnhan','giaTri':'3'},{'tenTrangThai':'tamdunghoso','giaTri':'4'},{'tenTrangThai':'tieptuchoso','giaTri':'5'},{'tenTrangThai':'tamdunghoso','giaTri':'6'},{'tenTrangThai':'ketthuchoso','giaTri':'7'},{'tenTrangThai':'tralaidan','giaTri':'8'}]

#dni
digo.api-integration.secret=Dni@secret123
digo.permission.tsdc=manageDigo

#HTTP cauhinhv2
digo.http.v2.enable=false

#bogiaothongvantai
digo.lgsp.gtvt.transportationV2Enable=false
#HPG-Tra cuu ho so dong bo DVCQG
digo.config.NpsDossierSyncService.syncHPG=true

digo.domain.list.api=judicial-civil-status/--send,judicial-records/--send-dossier,judicial-records/--send-dossier-2
#AGG VNEID LLTP
digo.permission.ag-esb-vneid-lltp=manageDigo
digo.agg.integrated-vneid-lltp-id-config=672190774675185e2d25bd74
digo.agg.integrated-vneid-lltp-organ-id=28
digo.agg.integrated-vneid-lltp-field-code=06
digo.agg.integrated-vneid-lltp-process-code=2000
digo.agg.integrated-vneid-lltp-type-code=3000

digo.blu.integrated-tnmt=false

digo.qni.enable-vbdlis-timeout=false
digo.ilis.get-code-tthc=false
digo.max-length-name-tthc-payment-platform=200

#AGG DVCLT
lienthong.dvclt.configid=671f57724675185e2d25b7d2
digo.permission.dvclt-agg=manageDigo
digo.khdt.config-id=670390e001167a1256128bdf

#EMC
digo.emc.sync.enable=false
digo.emc.sync.time-cron=30 30 * * * *
digo.custom-header=0

#tham so cau hinh day ho so sang vnpost bang code
digo.hcm.transmitDossierCodeViaVNPost=0

#Tham sá» Äá»ng bá» DVCQG - bá» sung
#Tham sá» láº¥y updatedDate lÃ m ngÃ y tiáº¿p nháº­n khi khÃ´ng cÃ³ acceptedDate
vnpt.sync-national.use-updated-date-as-recepted-date=false
digo.chungthuc.changeCallKafkaDossierAuthen=false

#client-credential
vnpt.client.id=svc-padman
vnpt.client.serket=efe42c1f-ac2f-47e6-a065-b37ea012d66d

#fixATTT
vnpt.permission.role.admin=admin,SUPER_ADMIN,oidcAdminMaster
#retry log service config v2
digo.schedule.sync-service-log=0 0 3 * * *
digo.sync-service-log.enable=false

#vbdilis FPT KHA Dang SD
#true thÃ¬ set token vs api gá»i Äi cÃ³ trÆ°á»ng Lgspaccesstoken set token default trong trÆ°á»ng token-value trong cáº¥u hÃ¬nh site tÃ­ch há»£p Vd : id config kha 6826a62cc426f735e693ee3a
digo.vbdilis.fbt.enable=false

#luá»ng cá»§
digo.http.fbt.enable=false

#kafak dvclt HTTP : true thÃ¬ gá»i qua báº±ng kafka, false thÃ¬ khÃ´ng gá»i báº¯ng kafka
digo.http.dvclt.kafka.enable=true

digo.http.dvclt.new.enable=false

#kafka khcn HTTP : true thì g?i qua b?ng kafka, false thì không g?i b?ng kafka
digo.http.khcn.kafka.enable=true
digo.http.khcn.new.enable=false

digo.ioffice.sms-enable=false
digo.ioffice.sms-template=683692d3b982267cd00cfd65

#eform
vnpt.permission.revieweform=manageDigo
digo.type.id.district1=
digo.type.id.district2=

#DVCQG 175 - Begin
vnpt.permission.dvcqg175=manageDigo
#DVCQG 175 - End

#test CSDLDC
digo.csdldc.citizen.district.off=true
