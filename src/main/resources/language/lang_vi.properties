label.id=M\u00e3
label.filename=T\u00ean file
label.path=\u0110\u01b0\u1eddng d\u1eabn
label.size=Dung l\u01b0\u1ee3ng {0}
label.updated-date=Ng\u00e0y c\u1eadp nh\u1eadt
label.category= Lo\u1ea1i b\u00e0i vi\u1ebft 
# DigoHttpException message
digo.http.response.error.10000={0} l\u00e0 b\u1eaft bu\u1ed9c
digo.http.response.error.10002=Kh\u00f4ng t\u00ecm th\u1ea5y {0}
digo.http.response.error.10003=Kh\u00f4ng l\u1ea5y \u0111\u01b0\u1ee3c {0}
digo.http.response.error.10004=Kh\u00f4ng th\u1ec3 l\u1ea5y d\u1eef li\u1ec7u
digo.http.response.error.10005=Kh\u00f4ng th\u1ec3 g\u1eedi d\u1eef li\u1ec7u
digo.http.response.error.10006=Kh\u00f4ng th\u1ec3 c\u1eadp nh\u1eadt d\u1eef li\u1ec7u
digo.http.response.error.10007={0} \u0111\u00e3 t\u1ed3n t\u1ea1i
digo.http.response.error.10008=Kh\u00f4ng th\u1ec3 th\u00eam {0}
digo.http.response.error.10009=Kh\u00f4ng th\u1ec3 c\u1eadp nh\u1eadt {0}
digo.http.response.error.10010=Kh\u00f4ng th\u1ec3 x\u00f3a {0}
digo.http.response.error.10011=\u0110\u1ecbnh d\u1ea1ng {0} ph\u1ea3i l\u00e0 {1}
digo.http.response.error.10012=Kh\u00f4ng th\u1ec3 t\u00ecm {0} v\u1edbi m\u00e3 {1}
digo.http.response.error.10013=Kh\u00f4ng th\u1ec3 t\u00ecm th\u1ea5y th\u1ef1c th\u1ec3
digo.http.response.error.10014={0} kh\u00f4ng th\u00e0nh c\u00f4ng
digo.http.response.error.10015={0} \u0111\u00e3 \u0111\u01b0\u1ee3c \u0111\u1ed3ng b\u1ed9 v\u1edbi {1}.
digo.http.response.error.10016={0} \u0111\u00e3 \u0111\u01b0\u1ee3c c\u1ea5u h\u00ecnh m\u1eb7c \u0111\u1ecbnh b\u1edfi {1}.
digo.http.response.error.10017=Kh\u00F4ng th\u1EC3 l\u1EA5y access token
digo.http.response.error.10018=T\u1EA1o chu\u1ED7i XML th\u1EA5t b\u1EA1i
digo.http.response.error.10019=T\u1EA1o chu\u1ED7i XML th\u1EA5t b\u1EA1i
digo.http.response.error.10020=Li\u00EAn th\u00F4ng S\u1EDF T\u00E0i nguy\u00EAn v\u00E0 M\u00F4i tr\u01B0\u1EDDng th\u1EA5t b\u1EA1i
digo.http.response.error.10021=C\u1EADp nh\u1EADt tr\u1EA1ng th\u00E1i h\u1ED3 s\u01A1 th\u1EA5t b\u1EA1i
digo.http.response.error.10022={0} \u0043\u0069\u0072\u0063\u0075\u0069\u0074\u0042\u0072\u0065\u0061\u006b\u0065\u0072\u0020\u0111\u0061\u006e\u0067\u0020\u006d\u1edf
digo.http.response.error.10023={0} \u006b\u0068\u00f4\u006e\u0067\u0020\u0074\u0068\u00e0\u006e\u0068\u0020\u0063\u00f4\u006e\u0067
digo.http.response.error.11000=Kh\u00f4ng t\u00ecm th\u1ea5y {0}
digo.http.response.error.11001=Kh\u00f4ng t\u00ecm th\u1ea5y d\u1eef li\u1ec7u
digo.http.response.error.11002=C\u00f3 l\u1ed7i x\u1ea3y ra khi th\u1ef1c hi\u1ec7n \u0111\u1ed3ng b\u1ed9: {0}
digo.http.response.error.11003=L\u1ed7i {0}
digo.http.response.error.11004=\u0110\u00e3 c\u00f3 ti\u1ebfn tr\u00ecnh \u0111ang th\u1ef1c hi\u1ec7n \u0111\u1ed3ng b\u1ed9.
digo.http.response.error.10403=B\u1ea1n kh\u00f4ng c\u00f3 quy\u1ec1n th\u1ef1c hi\u1ec7n thao t\u00e1c n\u00e0y
digo.http.response.error.11005=L\u1ed7i tham s\u1ed1: {0}.
digo.http.response.error.11006=Kh\u00f4ng th\u1ec3 th\u1ef1c hi\u1ec7n k\u00fd s\u1ed1: {0}.
digo.http.response.error.11007=H\u1ebft th\u1eddi gian x\u00e1c nh\u1eadn k\u00fd s\u1ed1.
digo.http.response.error.11008=Ng\u01b0\u1eddi d\u00f9ng \u0111\u00e3 h\u1ee7y k\u00fd s\u1ed1
digo.http.response.error.11009=Kh\u00f4ng th\u1ec3 th\u1ef1c hi\u1ec7n k\u00fd s\u1ed1
digo.http.response.error.11010={0}
digo.http.response.error.11011=B\u1ea1n kh\u00f4ng c\u00f3 quy\u1ec1n th\u1ef1c hi\u1ec7n thao t\u00e1c n\u00e0y
digo.http.response.error.11012=Vui l\u00f2ng c\u1eadp nh\u1eadt H\u1ecd t\u00ean, CMND/CCCD, ng\u00e0y sinh tr\u01b0\u1edbc khi s\u1eed d\u1ee5ng ch\u1ee9c n\u1eb1ng \u0111\u1ed3ng b\u1ed9 t\u1eeb CSDLDC
digo.http.response.error.11014=Kh\u00f4ng t\u00ecm th\u1ea5y th\u00f4ng tin h\u1ed3 s\u01a1
digo.http.response.error.11015=Kh\u00f4ng th\u1ec3 thanh to\u00e1n v\u1edbi s\u1ed1 ti\u1ec1n b\u1eb1ng 0
digo.http.response.error.10442=B\u1EA1n \u0111\u00E3 th\u1EF1c hi\u1EC7n tra c\u1EE9u qu\u00E1 s\u1ED1 l\u1EA7n cho ph\u00E9p. Vui l\u00F2ng li\u00EAn h\u1EC7 qu\u1EA3n tr\u1ECB vi\u00EAn ho\u1EB7c th\u1EED l\u1EA1i sau.
digo.http.response.error.10443=\u0110\u00E3 th\u1EF1c hi\u1EC7n tra c\u1EE9u qu\u00E1 s\u1ED1 l\u1EA7n cho ph\u00E9p. Vui l\u00F2ng li\u00EAn h\u1EC7 qu\u1EA3n tr\u1ECB vi\u00EAn ho\u1EB7c th\u1EED l\u1EA1i sau.
digo.http.response.error.10444=V\u01B0\u1EE3t qu\u00E1 s\u1ED1 l\u1EA7n th\u1EF1c hi\u1EC7n
digo.http.response.error.10445=Tr\u01B0\u1EDDng key kh\u00F4ng h\u1EE3p l\u1EC7
digo.http.response.error.10446=kh\u00F4ng t\u00ECm th\u1EA5y c\u1EA5u h\u00ECnh t\u00EDch h\u1EE3p iWorkplace
digo.http.response.error.10447=kh\u00F4ng t\u00ECm th\u1EA5y c\u1EA5u h\u00ECnh t\u00EDch h\u1EE3p iStorage
digo.http.response.error.10448=\u0110\u1ECBa ch\u1EC9 URL kh\u00F4ng \u0111\u01B0\u1EE3c ph\u00E9p
digo.http.response.error.10400=\u0042\u006f\u0064\u0079\u0020\u0072\u0065\u0071\u0075\u0065\u0073\u0074\u0020\u0068\u006f\u1eb7\u0063\u0020\u0070\u0061\u0072\u0061\u006d\u0061\u0074\u0065\u0072\u0020\u006b\u0068\u00f4\u006e\u0067\u0020\u0111\u00fa\u006e\u0067\u002e\u0020\u0056\u0075\u0069\u0020\u006c\u00f2\u006e\u0067\u0020\u006b\u0069\u1ec3\u006d\u0020\u0074\u0072\u0061\u0020\u006c\u1ea1\u0069
digo.http.response.error.10401=KhÃ´ng tÃ¬m tháº¥y cáº¥u hÃ¬nh nÃ o cÃ³ Äá»a chá» {0} Äá» {1}
digo.http.response.error.10402=TÃ¬m tháº¥y nhiá»u hÆ¡n 1 cáº¥u hÃ¬nh cÃ³ Äá»a chá» {0} Äá» {1}, vui lÃ²ng truyá»n thÃªm mÃ£ há» thá»ng
digo.http.response.error.10404=ÄÃ£ xáº£y ra lá»i á» bÆ°á»c {0}, xem chi tiáº¿t táº¡i: {1}
digo.http.response.error.10405=ÄÃ£ xáº£y ra lá»i á» bÆ°á»c {0}, chi tiáº¿t lá»i: {1}
digo.http.response.error.10406=KhÃ´ng tÃ¬m tháº¥y domain hoáº·c IP nÃ o cáº§n thay tháº¿
digo.http.response.error.10407=S\u1ED1 CCCD c\u1EE7a c\u00E1n b\u1ED9 ch\u01B0a t\u1ED3n t\u1EA1i . Vui l\u00F2ng c\u1EADp nh\u1EADt l\u1EA1i th\u00F4ng tin.
digo.http.response.error.10408=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i c\u1EE7a c\u00E1n b\u1ED9 ch\u01B0a t\u1ED3n t\u1EA1i. Vui l\u00F2ng c\u1EADp nh\u1EADt l\u1EA1i th\u00F4ng tin.

# others
digo.number=s\u1ed1
lang.word.article=b\u00e0i vi\u1ebft
lang.word.category=Lo\u1ea1i b\u00e0i vi\u1ebft
lang.word.configuration=c\u1ea5u h\u00ecnh
lang.word.record=t\u1eadp tin ghi \u00e2m
lang.word.file=t\u1eadp tin
lang.word.callLGSP=C\u00f3 l\u1ed7i khi g\u1ecdi sang tr\u1ee5c LGSP
lang.word.image=h\u00ecnh \u1ea3nh
lang.word.cert=ch\u1ee9ng ch\u1ec9
lang.word.signature=ch\u1eef k\u00fd
lang.word.dossier=h\u1ed3 s\u01a1
lang.word.token=token ch\u1ee9ng th\u1ef1c
lang.word.ticket=phi\u1ebfu
lang.word.sector=nguoi thuc hien demo
lang.word.date-infor=Th\u00f4ng tin ng\u00e0y
lang.word.date-compare-5=Kho\u1ea3ng c\u00e1ch kh\u00f4ng qu\u00e1 5 ng\u00e0y
lang.word.file-id=M\u00e3 h\u1ed3 s\u01a1
lang.phrase.sync-category=lo\u1ea1i b\u00e0i vi\u1ebft \u0111\u01b0\u1ee3c \u0111\u1ed3ng b\u1ed9
lang.phrase.integration-service=d\u1ecbch v\u1ee5 \u0111\u01b0\u1ee3c t\u00edch h\u1ee3p
lang.phrase.mapping-data=d\u1eef li\u1ec7u t\u00edch h\u1ee3p
lang.phrase.procedure-mapping=th\u1ee7 t\u1ee5c \u0111\u01b0\u1ee3c li\u00ean k\u1ebft
lang.phrase.get-data-from-nps=l\u1ea5y d\u1eef li\u1ec7u t\u1eeb c\u1ed5ng d\u1ecbch v\u1ee5 c\u00f4ng qu\u1ed1c gia
lang.phrase.get-data-from-nc=l\u1ea5y d\u1eef li\u1ec7u t\u1eeb c\u01a1 s\u1edf d\u1eef li\u1ec7u qu\u1ed1c gia v\u1ec1 d\u00e2n c\u01b0
lang.sentence.petition-category-mapping=Kh\u00f4ng t\u00ecm th\u1ea5y chuy\u00ean m\u1ee5c ph\u1ea3n \u00e1nh \u0111\u01b0\u1ee3c li\u00ean k\u1ebft
lang.sentence.petition-reception-method-mapping=Kh\u00f4ng t\u00ecm th\u1ea5y h\u00ecnh th\u1ee9c ti\u1ebfp nh\u1eadn ph\u1ea3n \u00e1nh \u0111\u01b0\u1ee3c li\u00ean k\u1ebft
lang.phrase.file-to-sign=file c\u1ea7n k\u00fd
lang.phrase.signature-image=\u1ea3nh ch\u1eef k\u00fd
lang.sentence.signature-support-pdf-format=\u1ee8ng d\u1ee5ng ch\u1ec9 h\u1ed7 tr\u1ee3 k\u00fd tr\u00ean t\u00e0i li\u1ec7u pdf
lang.sentence.check-signature-config=ki\u1ec3m tra c\u1ea5u h\u00ecnh
lang.word.user=ng\u01b0\u1eddi d\u00f9ng
lang.word.username=t\u00e0i kho\u1ea3n
lang.word.password=m\u1eadt kh\u1ea9u
lang.phrase.signature.text=K\u00fd b\u1edfi: {0}\nNg\u00e0y k\u00fd: {1}
lang.word.issued=\u00c4\u0090\u00c3\u00a3 ban h\u00c3\u00a0nh
lang.word.approved=\u00c4\u0090\u00c3\u00a3 k\u00c3\u00bd duy\u00e1\u00bb\u0087t
lang.word.request-reprocessing=Y\u00c3\u00aau c\u00e1\u00ba\u00a7u x\u00e1\u00bb\u00ad l\u00c3\u00bd l\u00e1\u00ba\u00a1i
lang.word.waiting-issued=Ch\u00e1\u00bb\u009d ban h\u00c3\u00a0nh
lang.word.status.success=Th\u00e0nh c\u00f4ng
lang.word.status.fail=Th\u1ea5t b\u1ea1i
lang.word.dossier.task.status.ctn=H\u1ed3 s\u01a1 ch\u1edd ti\u1ebfp nh\u1eadn
lang.word.dossier.task.status.dtn=H\u1ed3 s\u01a1 \u0111ang ti\u1ebfp nh\u1eadn
lang.word.dossier.task.status.cxl=H\u1ed3 s\u01a1 ch\u1edd x\u1eed l\u00fd
lang.word.dossier.task.status.ckd=H\u1ed3 s\u01a1 ch\u1edd k\u00fd duy\u1ec7t
lang.word.dossier.task.status.tkq=H\u1ed3 s\u01a1 tr\u1ea3 k\u1ebft qu\u1ea3
lang.word.dossier.overdue.status.ontime=\u0110\u00fang h\u1ea1n
lang.word.dossier.overdue.status.overdue=Qu\u00e1 h\u1ea1n
lang.word.date.compare=Ng\u00e0y b\u1eaft \u0111\u1ea7u ph\u1ea3i nh\u1ecf h\u01a1n ng\u00e0y k\u1ebft th\u00fac.
lang.phrase.error-payment-incorrect=H\u1ed3 s\u01a1 thanh to\u00e1n ho\u1eb7c giao d\u1ecbch cho y\u00eau c\u1ea7u n\u00e0y kh\u00f4ng h\u1ee3p l\u1ec7.
lang.phrase.error-dossier-find=Kh\u00f4ng t\u00ecm \u0111\u01b0\u1ee3c h\u1ed3 so
lang.word.electronic-receipt=Bi\u00ean lai \u0111i\u1ec7n t\u1eed
lang.word.block-sms=C\u01a1 quan \u0111\u00e3 t\u1eaft t\u00ednh n\u0103ng g\u1eedi sms
lang.word.tag=Tr\u01b0\u1eddng
lang.phrase.may-can-not-empty=kh\u00f4ng \u0111\u01b0\u1ee3c \u0111\u1ec3 tr\u1ed1ng
lang.phrase.is-not-valid=kh\u00f4ng h\u1ee3p l\u1ec7
lang.phrase.is-not-exist=\u0063\u0068\u01b0\u0061\u0020\u0111\u01b0\u1ee3\u0063\u0020\u0063\u1ea5\u0075\u0020\u0068\u00ec\u006e\u0068
lang.phrase.lgsp-successful-response=B\u1ea3n tin \u0111\u00e3 \u0111\u01b0\u1ee3c nh\u1eadn th\u00e0nh c\u00f4ng, vi\u1ec7c x\u1eed l\u00fd s\u1ebd ho\u00e0n th\u00e0nh trong ch\u1ed1c l\u00e1t.
lang.phrase.lgsp-can-not-support-create=H\u1ed3 s\u01a1 m\u1edbi kh\u00f4ng \u0111\u01b0\u1ee3c h\u1ed7 tr\u1ee3. Vui l\u00f2ng thay \u0111\u1ed5i gi\u00e1 tr\u1ecb c\u1ee7a 'isUpdating' th\u00e0nh 'true' v\u00e0 ki\u1ec3m tra l\u1ea1i.
lang.word.just-signed-up=M\u1edbi \u0111\u0103ng k\u00fd
lang.word.receive-dossier-kgg=Nh\u1eadn tr\u1ef1c tuy\u1ebfn
lang.word.sca-sp769-sp-id="ID smart CA"
lang.word.BTXH-dossier= H\u1ED3 s\u01A1 BTXH
lang.word.BTXH="\u0062\u1ea3\u006f \u0074\u0072\u1ee3 \u0078\u00e3 \u0068\u1ed9\u0069"
lang.word.hcm.vietinfo.ereceipt.failed=L\u1ED7i g\u1ECDi v\u00E0 x\u1EED l\u00FD bi\u00EAn lai VietInfo
lang.word.dvclt-failed=\u0043\u1ead\u0070\u0020\u006e\u0068\u1ead\u0074\u0020\u0074\u0068\u1ea5\u0074\u0020\u0062\u1ea1\u0069
lang.word.dvclt-successful=\u0043\u1ead\u0070\u0020\u006e\u0068\u1ead\u0074\u0020\u0074\u0068\u00e0\u006e\u0068\u0020\u0063\u00f4\u006e\u0067
lang.word.dvclt-failed-securitykey=\u004c\u1ed7\u0069\u0020\u0073\u0065\u0063\u0075\u0072\u0069\u0074\u0079\u006b\u0065\u0079
lang.word.dvclt-exist=\u0053\u1ed1\u0020\u0068\u1ed3\u0020\u0073\u01a1 {0} \u0111\u00e3\u0020\u0074\u1ed3\u006e\u0020\u0074\u1ea1\u0069\u002c\u0020\u006b\u0068\u00f4\u006e\u0067\u0020\u0074\u0068\u1ec3\u0020\u0074\u0068\u1ef1\u0063\u0020\u0068\u0069\u1ec7\u006e\u0020\u0074\u0068\u00ea\u006d\u0020\u006d\u1edb\u0069\u0020\u0076\u00e0\u006f\u0020\u0068\u1ec7\u0020\u0074\u0068\u1ed1\u006e\u0067\u0021
lang.word.dvclt-not-exist=\u0053\u1ed1\u0020\u0068\u1ed3\u0020\u0073\u01a1 {0} \u0063\u0068\u01b0\u0061\u0020\u0111\u01b0\u1ee3\u0063\u0020\u0074\u0068\u00ea\u006d\u0020\u006d\u1edb\u0069\u0020\u0076\u00e0\u006f\u0020\u0068\u1ec7\u0020\u0074\u0068\u1ed1\u006e\u0067\u002c\u0020\u006b\u0068\u00f4\u006e\u0067\u0020\u0074\u0068\u1ec3\u0020\u0074\u0068\u1ef1\u0063\u0020\u0068\u0069\u1ec7\u006e\u0020\u0063\u1ead\u0070\u0020\u006e\u0068\u1ead\u0074\u0021
lang.word.dvclt-date-error=\u0020\u006b\u0068\u00f4\u006e\u0067\u0020\u0111\u00fa\u006e\u0067\u0020\u0111\u1ecb\u006e\u0068\u0020\u0064\u1ea1\u006e\u0067\u0020\u0079\u0079\u0079\u0079\u004d\u004d\u0064\u0064\u0048\u0048\u006d\u006d\u0073\u0073\u0021
lang.word.dvcl-is-not-valid=\u006b\u0068\u00f4\u006e\u0067\u0020\u0068\u1ee3\u0070\u0020\u006c\u1ec7\u0021
lang.word.dvclt-invalid-input=\u0054\u0068\u0061\u006d\u0020\u0073\u1ed1\u0020\u0111\u1ea7\u0075\u0020\u0076\u00e0\u006f\u0020\u006b\u0068\u00f4\u006e\u0067\u0020\u0068\u1ee3\u0070\u0020\u006c\u1ec7\u002e
lang.word.dvclt-errors=\u0043\u00f3\u0020\u006c\u1ed7\u0069\u0020\u0078\u1ea3\u0079\u0020\u0072\u0061\u0020\u006b\u0068\u0069\u0020\u0111\u1ed3\u006e\u0067\u0020\u0062\u1ed9\u0020\u0068\u1ed3\u0020\u0073\u01a1\u002e
lang.word.deploymentId=MÃ£ há» thá»ng triá»n khai
lang.word.circuit-breaker=\u0043\u0069\u0072\u0063\u0075\u0069\u0074\u0042\u0072\u0065\u0061\u006b\u0065\u0072\u0020\u0027\u006d\u0079\u0043\u0069\u0072\u0063\u0075\u0069\u0074\u0042\u0072\u0065\u0061\u006b\u0065\u0072\u0027\u0020\u0111\u0061\u006e\u0067\u0020\u004d\u1ede\u0020\u0076\u00e0\u0020\u006b\u0068\u00f4\u006e\u0067\u0020\u0063\u0068\u006f\u0020\u0070\u0068\u00e9\u0070\u0020\u0063\u00e1\u0063\u0020\u0063\u0075\u1ed9\u0063\u0020\u0067\u1ecd\u0069\u0020\u0074\u0069\u1ebf\u0070\u0020\u0074\u0068\u0065\u006f\u0021
lang.word.send-brandname=\u0047\u1eed\u0069\u0020\u006c\u1ed7\u0069
lang.word.circuit-breaker.send-lgsp= \u0073\u0065\u006e\u0064\u0020\u004c\u0047\u0053\u0050\u0020\u0066\u0061\u0069\u006c\u0021

