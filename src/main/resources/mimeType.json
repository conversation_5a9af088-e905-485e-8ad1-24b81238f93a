[{"type": "application/octet-stream", "description": "", "extension": ""}, {"type": "application/vnd.hzn-3d-crossword", "description": "3D Crossword Plugin", "extension": "x3d"}, {"type": "video/3gpp", "description": "3GP", "extension": "3gp"}, {"type": "video/3gpp2", "description": "3GP2", "extension": "3g2"}, {"type": "application/vnd.mseq", "description": "3GPP MSEQ File", "extension": "mseq"}, {"type": "application/vnd.3m.post-it-notes", "description": "3M Post It Notes", "extension": "pwn"}, {"type": "application/vnd.3gpp.pic-bw-large", "description": "3rd Generation Partnership Project  Pic Large", "extension": "plb"}, {"type": "application/vnd.3gpp.pic-bw-small", "description": "3rd Generation Partnership Project  Pic Small", "extension": "psb"}, {"type": "application/vnd.3gpp.pic-bw-var", "description": "3rd Generation Partnership Project  Pic Var", "extension": "pvb"}, {"type": "application/vnd.3gpp2.tcap", "description": "3rd Generation Partnership Project  Transaction Capabilities Application Part", "extension": "tcap"}, {"type": "application/x-7z-compressed", "description": "7-<PERSON><PERSON>", "extension": "7z"}, {"type": "application/x-abiword", "description": "AbiWord", "extension": "abw"}, {"type": "application/x-ace-compressed", "description": "Ace Archive", "extension": "ace"}, {"type": "application/vnd.americandynamics.acc", "description": "Active Content Compression", "extension": "acc"}, {"type": "application/vnd.acucobol", "description": "ACU Cobol", "extension": "acu"}, {"type": "application/vnd.acucorp", "description": "ACU Cobol", "extension": "atc"}, {"type": "audio/adpcm", "description": "Adaptive differential pulse-code modulation", "extension": "adp"}, {"type": "application/x-authorware-bin", "description": "Adobe (Macropedia) Authorware  Binary File", "extension": "aab"}, {"type": "application/x-authorware-map", "description": "Adobe (Macropedia) Authorware  Map", "extension": "aam"}, {"type": "application/x-authorware-seg", "description": "Adobe (Macropedia) Authorware  Segment File", "extension": "aas"}, {"type": "application/vnd.adobe.air-application-installer-package+zip", "description": "Adobe AIR Application", "extension": "air"}, {"type": "application/x-shockwave-flash", "description": "Adobe Flash", "extension": "swf"}, {"type": "application/vnd.adobe.fxp", "description": "Adobe Flex Project", "extension": "fxp"}, {"type": "application/pdf", "description": "Adobe Portable Document Format", "extension": "pdf"}, {"type": "application/vnd.cups-ppd", "description": "Adobe PostScript Printer Description File Format", "extension": "ppd"}, {"type": "application/x-director", "description": "Adobe Shockwave Player", "extension": "dir"}, {"type": "application/vnd.adobe.xdp+xml", "description": "Adobe XML Data Package", "extension": "xdp"}, {"type": "application/vnd.adobe.xfdf", "description": "Adobe XML Forms Data Format", "extension": "xfdf"}, {"type": "audio/x-aac", "description": "Advanced Audio Coding (AAC)", "extension": "aac"}, {"type": "application/vnd.ahead.space", "description": "Ahead AIR Application", "extension": "ahead"}, {"type": "application/vnd.airzip.filesecure.azf", "description": "AirZip FileSECURE", "extension": "azf"}, {"type": "application/vnd.airzip.filesecure.azs", "description": "AirZip FileSECURE", "extension": "azs"}, {"type": "application/vnd.amazon.ebook", "description": "Amazon Kindle eBook format", "extension": "azw"}, {"type": "application/vnd.amiga.ami", "description": "AmigaDE", "extension": "ami"}, {"type": "application/vnd.android.package-archive", "description": "Android Package Archive", "extension": "apk"}, {"type": "application/vnd.anser-web-certificate-issue-initiation", "description": "ANSER-WEB Terminal Client  Certificate Issue", "extension": "cii"}, {"type": "application/vnd.anser-web-funds-transfer-initiation", "description": "ANSER-WEB Terminal Client  Web Funds Transfer", "extension": "fti"}, {"type": "application/vnd.antix.game-component", "description": "Antix Game Player", "extension": "atx"}, {"type": "application/vnd.apple.installer+xml", "description": "Apple Installer Package", "extension": "mpkg"}, {"type": "application/applixware", "description": "Applixware", "extension": "aw"}, {"type": "application/vnd.hhe.lesson-player", "description": "Archipelago Lesson Player", "extension": "les"}, {"type": "application/vnd.aristanetworks.swi", "description": "Arista Networks Software Image", "extension": "swi"}, {"type": "text/x-asm", "description": "Assembler Source File", "extension": "s"}, {"type": "application/atomcat+xml", "description": "Atom Publishing Protocol", "extension": "atomcat"}, {"type": "application/atomsvc+xml", "description": "Atom Publishing Protocol Service Document", "extension": "atomsvc"}, {"type": "application/atom+xml", "description": "Atom Syndication Format", "extension": "xml"}, {"type": "application/atom+xml", "description": "Atom Syndication Format", "extension": "atom"}, {"type": "application/pkix-attr-cert", "description": "Attribute Certificate", "extension": "ac"}, {"type": "audio/x-aiff", "description": "Audio Interchange File Format", "extension": "aif"}, {"type": "video/x-msvideo", "description": "Audio Video Interleave (AVI)", "extension": "avi"}, {"type": "application/vnd.audiograph", "description": "Audiograph", "extension": "aep"}, {"type": "image/vnd.dxf", "description": "AutoCAD DXF", "extension": "dxf"}, {"type": "model/vnd.dwf", "description": "Autodesk Design Web Format (DWF)", "extension": "dwf"}, {"type": "text/plain-bas", "description": "BAS Partitur Format", "extension": "par"}, {"type": "application/x-bcpio", "description": "Binary CPIO Archive", "extension": "bcpio"}, {"type": "application/octet-stream", "description": "Binary Data", "extension": "bin"}, {"type": "image/bmp", "description": "Bitmap Image File", "extension": "bmp"}, {"type": "application/x-bittorrent", "description": "BitTorrent", "extension": "torrent"}, {"type": "application/vnd.rim.cod", "description": "Blackberry COD File", "extension": "cod"}, {"type": "application/vnd.blueice.multipass", "description": "Blueice Research Multipass", "extension": "mpm"}, {"type": "application/vnd.bmi", "description": "BMI Drawing Data Interchange", "extension": "bmi"}, {"type": "application/x-sh", "description": "Bourne Shell Script", "extension": "sh"}, {"type": "image/prs.btif", "description": "BTIF", "extension": "btif"}, {"type": "application/vnd.businessobjects", "description": "BusinessObjects", "extension": "rep"}, {"type": "application/x-bzip", "description": "Bzip Archive", "extension": "bz"}, {"type": "application/x-bzip2", "description": "Bzip2 Archive", "extension": "bz2"}, {"type": "application/x-csh", "description": "C <PERSON>", "extension": "csh"}, {"type": "text/x-c", "description": "C Source File", "extension": "c"}, {"type": "application/vnd.chemdraw+xml", "description": "CambridgeSoft Chem Draw", "extension": "cdxml"}, {"type": "text/css", "description": "Cascading Style Sheets (CSS)", "extension": "css"}, {"type": "chemical/x-cdx", "description": "ChemDraw eXchange file", "extension": "cdx"}, {"type": "chemical/x-cml", "description": "Chemical Markup Language", "extension": "cml"}, {"type": "chemical/x-csml", "description": "Chemical Style Markup Language", "extension": "csml"}, {"type": "application/vnd.contact.cmsg", "description": "CIM Database", "extension": "cdbcmsg"}, {"type": "application/vnd.claymore", "description": "Claymore Data Files", "extension": "cla"}, {"type": "application/vnd.clonk.c4group", "description": "Clonk Game", "extension": "c4g"}, {"type": "image/vnd.dvb.subtitle", "description": "Close Captioning  Subtitle", "extension": "sub"}, {"type": "application/cdmi-capability", "description": "Cloud Data Management Interface (CDMI)  Capability", "extension": "cdmia"}, {"type": "application/cdmi-container", "description": "Cloud Data Management Interface (CDMI)  Contaimer", "extension": "cdmic"}, {"type": "application/cdmi-domain", "description": "Cloud Data Management Interface (CDMI)  Domain", "extension": "cdmid"}, {"type": "application/cdmi-object", "description": "Cloud Data Management Interface (CDMI)  Object", "extension": "cdmio"}, {"type": "application/cdmi-queue", "description": "Cloud Data Management Interface (CDMI)  Queue", "extension": "cdmiq"}, {"type": "application/vnd.cluetrust.cartomobile-config", "description": "ClueTrust CartoMobile  Config", "extension": "c11amc"}, {"type": "application/vnd.cluetrust.cartomobile-config-pkg", "description": "ClueTrust CartoMobile  Config Package", "extension": "c11amz"}, {"type": "image/x-cmu-raster", "description": "CMU Image", "extension": "ras"}, {"type": "model/vnd.collada+xml", "description": "COLLADA", "extension": "dae"}, {"type": "text/csv", "description": "Comma-Seperated Values", "extension": "csv"}, {"type": "application/mac-compactpro", "description": "Compact Pro", "extension": "cpt"}, {"type": "application/vnd.wap.wmlc", "description": "Compiled Wireless Markup Language (WMLC)", "extension": "wmlc"}, {"type": "image/cgm", "description": "Computer Graphics Metafile", "extension": "cgm"}, {"type": "x-conference/x-cooltalk", "description": "CoolTalk", "extension": "ice"}, {"type": "image/x-cmx", "description": "Corel Metafile Exchange (CMX)", "extension": "cmx"}, {"type": "application/vnd.xara", "description": "CorelXARA", "extension": "xar"}, {"type": "application/vnd.cosmocaller", "description": "CosmoCaller", "extension": "cmc"}, {"type": "application/x-cpio", "description": "CPIO Archive", "extension": "cpio"}, {"type": "application/vnd.crick.clicker", "description": "CrickSoftware  Clicker", "extension": "clkx"}, {"type": "application/vnd.crick.clicker.keyboard", "description": "CrickSoftware  Clicker  Keyboard", "extension": "clkk"}, {"type": "application/vnd.crick.clicker.palette", "description": "CrickSoftware  Clicker  Palette", "extension": "clkp"}, {"type": "application/vnd.crick.clicker.template", "description": "CrickSoftware  Clicker  Template", "extension": "clkt"}, {"type": "application/vnd.crick.clicker.wordbank", "description": "CrickSoftware  Clicker  Wordbank", "extension": "clkw"}, {"type": "application/vnd.criticaltools.wbs+xml", "description": "Critical Tools  PERT Chart EXPERT", "extension": "wbs"}, {"type": "application/vnd.rig.cryptonote", "description": "CryptoNote", "extension": "cryptonote"}, {"type": "chemical/x-cif", "description": "Crystallographic Interchange Format", "extension": "cif"}, {"type": "chemical/x-cmdf", "description": "CrystalMaker Data Format", "extension": "cmdf"}, {"type": "application/cu-seeme", "description": "CU-SeeMe", "extension": "cu"}, {"type": "application/prs.cww", "description": "CU-Writer", "extension": "cww"}, {"type": "text/vnd.curl", "description": "<PERSON><PERSON><PERSON>", "extension": "curl"}, {"type": "text/vnd.curl.dcurl", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extension": "dcurl"}, {"type": "text/vnd.curl.mcurl", "description": "<PERSON><PERSON><PERSON>ifest File", "extension": "mcurl"}, {"type": "text/vnd.curl.scurl", "description": "Curl  Source Code", "extension": "scurl"}, {"type": "application/vnd.curl.car", "description": "CURL Applet", "extension": "car"}, {"type": "application/vnd.curl.pcurl", "description": "CURL Applet", "extension": "pcurl"}, {"type": "application/vnd.yellowriver-custom-menu", "description": "CustomMenu", "extension": "cmp"}, {"type": "application/dssc+der", "description": "Data Structure for the Security Suitability of Cryptographic Algorithms", "extension": "dssc"}, {"type": "application/dssc+xml", "description": "Data Structure for the Security Suitability of Cryptographic Algorithms", "extension": "xdssc"}, {"type": "application/x-debian-package", "description": "Debian Package", "extension": "deb"}, {"type": "audio/vnd.dece.audio", "description": "DECE Audio", "extension": "uva"}, {"type": "image/vnd.dece.graphic", "description": "DECE Graphic", "extension": "uvi"}, {"type": "video/vnd.dece.hd", "description": "DECE High Definition Video", "extension": "uvh"}, {"type": "video/vnd.dece.mobile", "description": "DECE Mobile Video", "extension": "uvm"}, {"type": "video/vnd.uvvu.mp4", "description": "DECE MP4", "extension": "uvu"}, {"type": "video/vnd.dece.pd", "description": "DECE PD Video", "extension": "uvp"}, {"type": "video/vnd.dece.sd", "description": "DECE SD Video", "extension": "uvs"}, {"type": "video/vnd.dece.video", "description": "DECE Video", "extension": "uvv"}, {"type": "application/x-dvi", "description": "Device Independent File Format (DVI)", "extension": "dvi"}, {"type": "application/vnd.fdsn.seed", "description": "Digital Siesmograph Networks  SEED Datafiles", "extension": "seed"}, {"type": "application/x-dtbook+xml", "description": "Digital Talking Book", "extension": "dtb"}, {"type": "application/x-dtbresource+xml", "description": "Digital Talking Book  Resource File", "extension": "res"}, {"type": "application/vnd.dvb.ait", "description": "Digital Video Broadcasting", "extension": "ait"}, {"type": "application/vnd.dvb.service", "description": "Digital Video Broadcasting", "extension": "svc"}, {"type": "audio/vnd.digital-winds", "description": "Digital Winds Music", "extension": "eol"}, {"type": "image/vnd.djvu", "description": "DjVu", "extension": "djvu"}, {"type": "application/xml-dtd", "description": "Document Type Definition", "extension": "dtd"}, {"type": "application/vnd.dolby.mlp", "description": "Dolby Meridian Lossless Packing", "extension": "mlp"}, {"type": "application/x-doom", "description": "Doom Video Game", "extension": "wad"}, {"type": "application/vnd.dpgraph", "description": "DPGraph", "extension": "dpg"}, {"type": "audio/vnd.dra", "description": "DRA Audio", "extension": "dra"}, {"type": "application/vnd.dreamfactory", "description": "DreamFactory", "extension": "dfac"}, {"type": "audio/vnd.dts", "description": "DTS Audio", "extension": "dts"}, {"type": "audio/vnd.dts.hd", "description": "DTS High Definition Audio", "extension": "dtshd"}, {"type": "image/vnd.dwg", "description": "DWG Drawing", "extension": "dwg"}, {"type": "application/vnd.dynageo", "description": "DynaGeo", "extension": "geo"}, {"type": "application/ecmascript", "description": "ECMAScript", "extension": "es"}, {"type": "application/vnd.ecowin.chart", "description": "EcoWin Chart", "extension": "mag"}, {"type": "image/vnd.fujixerox.edmics-mmr", "description": "EDMICS 2000", "extension": "mmr"}, {"type": "image/vnd.fujixerox.edmics-rlc", "description": "EDMICS 2000", "extension": "rlc"}, {"type": "application/exi", "description": "Efficient XML Interchange", "extension": "exi"}, {"type": "application/vnd.proteus.magazine", "description": "EFI Proteus", "extension": "mgz"}, {"type": "application/epub+zip", "description": "Electronic Publication", "extension": "epub"}, {"type": "message/rfc822", "description": "Email Message", "extension": "eml"}, {"type": "application/vnd.enliven", "description": "<PERSON><PERSON><PERSON>", "extension": "nml"}, {"type": "application/vnd.is-xpr", "description": "Express by Infoseek", "extension": "xpr"}, {"type": "image/vnd.xiff", "description": "eXtended Image File Format (XIFF)", "extension": "xif"}, {"type": "application/vnd.xfdl", "description": "Extensible Forms Description Language", "extension": "xfdl"}, {"type": "application/emma+xml", "description": "Extensible MultiModal Annotation", "extension": "emma"}, {"type": "application/vnd.ezpix-album", "description": "EZPix Secure Photo Album", "extension": "ez2"}, {"type": "application/vnd.ezpix-package", "description": "EZPix Secure Photo Album", "extension": "ez3"}, {"type": "image/vnd.fst", "description": "FAST Search & Transfer ASA", "extension": "fst"}, {"type": "video/vnd.fvt", "description": "FAST Search & Transfer ASA", "extension": "fvt"}, {"type": "image/vnd.fastbidsheet", "description": "FastBid Sheet", "extension": "fbs"}, {"type": "application/vnd.denovo.fcselayout-link", "description": "FCS Express Layout Link", "extension": "fe_launch"}, {"type": "video/x-f4v", "description": "Flash Video", "extension": "f4v"}, {"type": "video/x-flv", "description": "Flash Video", "extension": "flv"}, {"type": "image/vnd.fpx", "description": "FlashPix", "extension": "fpx"}, {"type": "image/vnd.net-fpx", "description": "FlashPix", "extension": "npx"}, {"type": "text/vnd.fmi.flexstor", "description": "FLEXSTOR", "extension": "flx"}, {"type": "video/x-fli", "description": "FLI/FLC Animation Format", "extension": "fli"}, {"type": "application/vnd.fluxtime.clip", "description": "FluxTime Clip", "extension": "ftc"}, {"type": "application/vnd.fdf", "description": "Forms Data Format", "extension": "fdf"}, {"type": "text/x-fortran", "description": "Fortran Source File", "extension": "f"}, {"type": "application/vnd.mif", "description": "FrameMaker Interchange Format", "extension": "mif"}, {"type": "application/vnd.framemaker", "description": "FrameMaker Normal Format", "extension": "fm"}, {"type": "image/x-freehand", "description": "FreeHand MX", "extension": "fh"}, {"type": "application/vnd.fsc.weblaunch", "description": "Friendly Software Corporation", "extension": "fsc"}, {"type": "application/vnd.frogans.fnc", "description": "Frogans Player", "extension": "fnc"}, {"type": "application/vnd.frogans.ltf", "description": "Frogans Player", "extension": "ltf"}, {"type": "application/vnd.fujixerox.ddd", "description": "Fujitsu  Xerox 2D CAD Data", "extension": "ddd"}, {"type": "application/vnd.fujixerox.docuworks", "description": "Fujitsu  Xerox DocuWorks", "extension": "xdw"}, {"type": "application/vnd.fujixerox.docuworks.binder", "description": "Fujitsu  Xerox DocuWorks Binder", "extension": "xbd"}, {"type": "application/vnd.fujitsu.oasys", "description": "Fujitsu Oasys", "extension": "oas"}, {"type": "application/vnd.fujitsu.oasys2", "description": "Fujitsu Oasys", "extension": "oa2"}, {"type": "application/vnd.fujitsu.oasys3", "description": "Fujitsu Oasys", "extension": "oa3"}, {"type": "application/vnd.fujitsu.oasysgp", "description": "Fujitsu Oasys", "extension": "fg5"}, {"type": "application/vnd.fujitsu.oasysprs", "description": "Fujitsu Oasys", "extension": "bh2"}, {"type": "application/x-futuresplash", "description": "FutureSplash Animator", "extension": "spl"}, {"type": "application/vnd.fuzzysheet", "description": "FuzzySheet", "extension": "fzs"}, {"type": "image/g3fax", "description": "G3 Fax Image", "extension": "g3"}, {"type": "application/vnd.gmx", "description": "GameMaker ActiveX", "extension": "gmx"}, {"type": "model/vnd.gtw", "description": "Gen-Trix Studio", "extension": "gtw"}, {"type": "application/vnd.genomatix.tuxedo", "description": "Genomatix Tuxedo Framework", "extension": "txd"}, {"type": "application/vnd.geogebra.file", "description": "GeoGebra", "extension": "ggb"}, {"type": "application/vnd.geogebra.tool", "description": "GeoGebra", "extension": "ggt"}, {"type": "model/vnd.gdl", "description": "Geometric Description Language (GDL)", "extension": "gdl"}, {"type": "application/vnd.geometry-explorer", "description": "GeoMetry Explorer", "extension": "gex"}, {"type": "application/vnd.geonext", "description": "GEONExT and JSXGraph", "extension": "gxt"}, {"type": "application/vnd.geoplan", "description": "GeoplanW", "extension": "g2w"}, {"type": "application/vnd.geospace", "description": "GeospacW", "extension": "g3w"}, {"type": "application/x-font-ghostscript", "description": "Ghostscript Font", "extension": "gsf"}, {"type": "application/x-font-bdf", "description": "Glyph Bitmap Distribution Format", "extension": "bdf"}, {"type": "application/x-gtar", "description": "GNU Tar Files", "extension": "gtar"}, {"type": "application/x-texinfo", "description": "GNU Texinfo Document", "extension": "texinfo"}, {"type": "application/x-gnumeric", "description": "Gnumeric", "extension": "gnumeric"}, {"type": "application/vnd.google-earth.kml+xml", "description": "Google Earth  KML", "extension": "kml"}, {"type": "application/vnd.google-earth.kmz", "description": "Google Earth  Zipped KML", "extension": "kmz"}, {"type": "application/vnd.grafeq", "description": "GrafEq", "extension": "gqf"}, {"type": "image/gif", "description": "Graphics Interchange Format", "extension": "gif"}, {"type": "text/vnd.graphviz", "description": "Graphviz", "extension": "gv"}, {"type": "application/vnd.groove-account", "description": "Groove  Account", "extension": "gac"}, {"type": "application/vnd.groove-help", "description": "Groove  Help", "extension": "ghf"}, {"type": "application/vnd.groove-identity-message", "description": "Groove  Identity Message", "extension": "gim"}, {"type": "application/vnd.groove-injector", "description": "Groove  Injector", "extension": "grv"}, {"type": "application/vnd.groove-tool-message", "description": "Groove  Tool Message", "extension": "gtm"}, {"type": "application/vnd.groove-tool-template", "description": "Groove  Tool Template", "extension": "tpl"}, {"type": "application/vnd.groove-vcard", "description": "Groove  Vcard", "extension": "vcg"}, {"type": "video/h261", "description": "H.261", "extension": "h261"}, {"type": "video/h263", "description": "H.263", "extension": "h263"}, {"type": "video/h264", "description": "H.264", "extension": "h264"}, {"type": "application/vnd.hp-hpid", "description": "Hewlett Packard Instant Delivery", "extension": "hpid"}, {"type": "application/vnd.hp-hps", "description": "Hewlett-Packard's WebPrintSmart", "extension": "hps"}, {"type": "application/x-hdf", "description": "Hierarchical Data Format", "extension": "hdf"}, {"type": "audio/vnd.rip", "description": "Hit'n'Mix", "extension": "rip"}, {"type": "application/vnd.hbci", "description": "Homebanking Computer Interface (HBCI)", "extension": "hbci"}, {"type": "application/vnd.hp-jlyt", "description": "HP Indigo Digital Press  Job Layout Languate", "extension": "jlt"}, {"type": "application/vnd.hp-pcl", "description": "HP Printer Command Language", "extension": "pcl"}, {"type": "application/vnd.hp-hpgl", "description": "HP-GL/2 and HP RTL", "extension": "hpgl"}, {"type": "application/vnd.yamaha.hv-script", "description": "HV <PERSON>", "extension": "hvs"}, {"type": "application/vnd.yamaha.hv-dic", "description": "HV Voice Dictionary", "extension": "hvd"}, {"type": "application/vnd.yamaha.hv-voice", "description": "HV Voice Parameter", "extension": "hvp"}, {"type": "application/vnd.hydrostatix.sof-data", "description": "Hydrostatix Master Suite", "extension": "sfd-hdstx"}, {"type": "application/hyperstudio", "description": "Hyperstudio", "extension": "stk"}, {"type": "application/vnd.hal+xml", "description": "Hypertext Application Language", "extension": "hal"}, {"type": "text/html", "description": "HyperText Markup Language (HTML)", "extension": "html"}, {"type": "application/vnd.ibm.rights-management", "description": "IBM DB2 Rights Manager", "extension": "irm"}, {"type": "application/vnd.ibm.secure-container", "description": "IBM Electronic Media Management System  Secure Container", "extension": "sc"}, {"type": "text/calendar", "description": "iCalendar", "extension": "ics"}, {"type": "application/vnd.iccprofile", "description": "ICC profile", "extension": "icc"}, {"type": "image/x-icon", "description": "Icon Image", "extension": "ico"}, {"type": "application/vnd.igloader", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extension": "igl"}, {"type": "image/ief", "description": "Image Exchange Format", "extension": "ief"}, {"type": "application/vnd.immervision-ivp", "description": "ImmerVision PURE Players", "extension": "ivp"}, {"type": "application/vnd.immervision-ivu", "description": "ImmerVision PURE Players", "extension": "ivu"}, {"type": "application/reginfo+xml", "description": "IMS Networks", "extension": "rif"}, {"type": "text/vnd.in3d.3dml", "description": "In3D  3DML", "extension": "3dml"}, {"type": "text/vnd.in3d.spot", "description": "In3D  3DML", "extension": "spot"}, {"type": "model/iges", "description": "Initial Graphics Exchange Specification (IGES)", "extension": "igs"}, {"type": "application/vnd.intergeo", "description": "Interactive Geometry Software", "extension": "i2g"}, {"type": "application/vnd.cinderella", "description": "Interactive Geometry Software Cinderella", "extension": "cdy"}, {"type": "application/vnd.intercon.formnet", "description": "Intercon FormNet", "extension": "xpw"}, {"type": "application/vnd.isac.fcs", "description": "International Society for Advancement of Cytometry", "extension": "fcs"}, {"type": "application/ipfix", "description": "Internet Protocol Flow Information Export", "extension": "ipfix"}, {"type": "application/pkix-cert", "description": "Internet Public Key Infrastructure  Certificate", "extension": "cer"}, {"type": "application/pkixcmp", "description": "Internet Public Key Infrastructure  Certificate Management Protocole", "extension": "pki"}, {"type": "application/pkix-crl", "description": "Internet Public Key Infrastructure  Certificate Revocation Lists", "extension": "crl"}, {"type": "application/pkix-pkipath", "description": "Internet Public Key Infrastructure  Certification Path", "extension": "pkipath"}, {"type": "application/vnd.insors.igm", "description": "IOCOM Visimeet", "extension": "igm"}, {"type": "application/vnd.ipunplugged.rcprofile", "description": "IP Unplugged Roaming Client", "extension": "rcprofile"}, {"type": "application/vnd.irepository.package+xml", "description": "iRepository / Lucidoc Editor", "extension": "irp"}, {"type": "text/vnd.sun.j2me.app-descriptor", "description": "J2ME App Descriptor", "extension": "jad"}, {"type": "application/java-archive", "description": "Java Archive", "extension": "jar"}, {"type": "application/java-vm", "description": "Java Bytecode File", "extension": "class"}, {"type": "application/x-java-jnlp-file", "description": "Java Network Launching Protocol", "extension": "jnlp"}, {"type": "application/java-serialized-object", "description": "Java Serialized Object", "extension": "ser"}, {"type": "text/x-java-source,java", "description": "Java Source File", "extension": "java"}, {"type": "application/javascript", "description": "JavaScript", "extension": "js"}, {"type": "application/json", "description": "JavaScript Object Notation (JSON)", "extension": "json"}, {"type": "application/vnd.joost.joda-archive", "description": "Joda Archive", "extension": "joda"}, {"type": "video/jpm", "description": "JPEG 2000 Compound Image File Format", "extension": "jpm"}, {"type": "image/jpeg", "description": "JPEG Image", "extension": "jpeg"}, {"type": "image/jpeg", "description": "JPEG Image", "extension": "jpg"}, {"type": "video/jpeg", "description": "JPGVideo", "extension": "jpgv"}, {"type": "application/vnd.kahootz", "description": "<PERSON><PERSON><PERSON>", "extension": "ktz"}, {"type": "application/vnd.chipnuts.karaoke-mmd", "description": "Karaoke on Chipnuts Chipsets", "extension": "mmd"}, {"type": "application/vnd.kde.karbon", "description": "KDE KOffice Office Suite  Karbon", "extension": "karbon"}, {"type": "application/vnd.kde.kchart", "description": "KDE KOffice Office Suite  KChart", "extension": "chrt"}, {"type": "application/vnd.kde.kformula", "description": "KDE KOffice Office Suite  Kformula", "extension": "kfo"}, {"type": "application/vnd.kde.kivio", "description": "KDE KOffice Office Suite  Kivio", "extension": "flw"}, {"type": "application/vnd.kde.kontour", "description": "KDE KOffice Office Suite  Kontour", "extension": "kon"}, {"type": "application/vnd.kde.kpresenter", "description": "KDE KOffice Office Suite  Kpresenter", "extension": "kpr"}, {"type": "application/vnd.kde.kspread", "description": "KDE KOffice Office Suite  Kspread", "extension": "ksp"}, {"type": "application/vnd.kde.kword", "description": "KDE KOffice Office Suite  Kword", "extension": "kwd"}, {"type": "application/vnd.kenameaapp", "description": "Kenamea App", "extension": "htke"}, {"type": "application/vnd.kidspiration", "description": "Kidspiration", "extension": "kia"}, {"type": "application/vnd.kinar", "description": "Kinar Applications", "extension": "kne"}, {"type": "application/vnd.kodak-descriptor", "description": "Kodak Storyshare", "extension": "sse"}, {"type": "application/vnd.las.las+xml", "description": "Laser App Enterprise", "extension": "lasxml"}, {"type": "application/x-latex", "description": "LaTeX", "extension": "latex"}, {"type": "application/vnd.llamagraphics.life-balance.desktop", "description": "Life Balance  Desktop Edition", "extension": "lbd"}, {"type": "application/vnd.llamagraphics.life-balance.exchange+xml", "description": "Life Balance  Exchange Format", "extension": "lbe"}, {"type": "application/vnd.jam", "description": "Lightspeed Audio Lab", "extension": "jam"}, {"type": "application/vnd.lotus-1-2-3", "description": "Lotus 1-2-3", "extension": "123"}, {"type": "application/vnd.lotus-approach", "description": "Lotus Approach", "extension": "apr"}, {"type": "application/vnd.lotus-freelance", "description": "Lotus Freelance", "extension": "pre"}, {"type": "application/vnd.lotus-notes", "description": "Lotus Notes", "extension": "nsf"}, {"type": "application/vnd.lotus-organizer", "description": "Lotus Organizer", "extension": "org"}, {"type": "application/vnd.lotus-screencam", "description": "Lotus Screencam", "extension": "scm"}, {"type": "application/vnd.lotus-wordpro", "description": "Lotus Wordpro", "extension": "lwp"}, {"type": "audio/vnd.lucent.voice", "description": "Lucent Voice", "extension": "lvp"}, {"type": "audio/x-mpegurl", "description": "M3U (Multimedia Playlist)", "extension": "m3u"}, {"type": "video/x-m4v", "description": "M4v", "extension": "m4v"}, {"type": "application/mac-binhex40", "description": "Macintosh BinHex 4.0", "extension": "hqx"}, {"type": "application/vnd.macports.portpkg", "description": "MacPorts Port System", "extension": "portpkg"}, {"type": "application/vnd.osgeo.mapguide.package", "description": "MapGuide DBXML", "extension": "mgp"}, {"type": "application/marc", "description": "MARC Formats", "extension": "mrc"}, {"type": "application/marcxml+xml", "description": "MARC21 XML Schema", "extension": "mrcx"}, {"type": "application/mxf", "description": "Material Exchange Format", "extension": "mxf"}, {"type": "application/vnd.wolfram.player", "description": "Mathematica Notebook Player", "extension": "nbp"}, {"type": "application/mathematica", "description": "Mathematica Notebooks", "extension": "ma"}, {"type": "application/mathml+xml", "description": "Mathematical Markup Language", "extension": "mathml"}, {"type": "application/mbox", "description": "Mbox database files", "extension": "mbox"}, {"type": "application/vnd.medcalcdata", "description": "MedCalc", "extension": "mc1"}, {"type": "application/mediaservercontrol+xml", "description": "Media Server Control Markup Language", "extension": "mscml"}, {"type": "application/vnd.mediastation.cdkey", "description": "MediaRemote", "extension": "cdkey"}, {"type": "application/vnd.mfer", "description": "Medical Waveform Encoding Format", "extension": "mwf"}, {"type": "application/vnd.mfmp", "description": "Melody Format for Mobile Platform", "extension": "mfm"}, {"type": "model/mesh", "description": "Mesh Data Type", "extension": "msh"}, {"type": "application/mads+xml", "description": "Metadata Authority Description Schema", "extension": "mads"}, {"type": "application/mets+xml", "description": "Metadata Encoding and Transmission Standard", "extension": "mets"}, {"type": "application/mods+xml", "description": "Metadata Object Description Schema", "extension": "mods"}, {"type": "application/metalink4+xml", "description": "Metalink", "extension": "meta4"}, {"type": "application/vnd.ms-powerpoint.template.macroenabled.12", "description": "Micosoft PowerPoint  Macro-Enabled Template File", "extension": "potm"}, {"type": "application/vnd.ms-word.document.macroenabled.12", "description": "Micosoft Word  Macro-Enabled Document", "extension": "docm"}, {"type": "application/vnd.ms-word.template.macroenabled.12", "description": "Micosoft Word  Macro-Enabled Template", "extension": "dotm"}, {"type": "application/vnd.mcd", "description": "Micro CADAM Helix D&D", "extension": "mcd"}, {"type": "application/vnd.micrografx.flo", "description": "Micrografx", "extension": "flo"}, {"type": "application/vnd.micrografx.igx", "description": "Micrografx iGrafx Professional", "extension": "igx"}, {"type": "application/vnd.eszigno3+xml", "description": "MICROSEC e-Szign¢", "extension": "es3"}, {"type": "application/x-msaccess", "description": "Microsoft Access", "extension": "mdb"}, {"type": "video/x-ms-asf", "description": "Microsoft Advanced Systems Format (ASF)", "extension": "asf"}, {"type": "application/x-msdownload", "description": "Microsoft Application", "extension": "exe"}, {"type": "application/vnd.ms-artgalry", "description": "Microsoft Artgalry", "extension": "cil"}, {"type": "application/vnd.ms-cab-compressed", "description": "Microsoft Cabinet File", "extension": "cab"}, {"type": "application/vnd.ms-ims", "description": "Microsoft Class Server", "extension": "ims"}, {"type": "application/x-ms-application", "description": "Microsoft ClickOnce", "extension": "application"}, {"type": "application/x-msclip", "description": "Microsoft Clipboard Clip", "extension": "clp"}, {"type": "image/vnd.ms-modi", "description": "Microsoft Document Imaging Format", "extension": "mdi"}, {"type": "application/vnd.ms-fontobject", "description": "Microsoft Embedded OpenType", "extension": "eot"}, {"type": "application/vnd.ms-excel", "description": "Microsoft Excel", "extension": "xls"}, {"type": "application/vnd.ms-excel.addin.macroenabled.12", "description": "Microsoft Excel  Add-In File", "extension": "xlam"}, {"type": "application/vnd.ms-excel.sheet.binary.macroenabled.12", "description": "Microsoft Excel  Binary Workbook", "extension": "xlsb"}, {"type": "application/vnd.ms-excel.template.macroenabled.12", "description": "Microsoft Excel  Macro-Enabled Template File", "extension": "xltm"}, {"type": "application/vnd.ms-excel.sheet.macroenabled.12", "description": "Microsoft Excel  Macro-Enabled Workbook", "extension": "xlsm"}, {"type": "application/vnd.ms-htmlhelp", "description": "Microsoft Html Help File", "extension": "chm"}, {"type": "application/x-mscardfile", "description": "Microsoft Information Card", "extension": "crd"}, {"type": "application/vnd.ms-lrm", "description": "Microsoft Learning Resource Module", "extension": "lrm"}, {"type": "application/x-msmediaview", "description": "Microsoft MediaView", "extension": "mvb"}, {"type": "application/x-msmoney", "description": "Microsoft Money", "extension": "mny"}, {"type": "application/vnd.openxmlformats-officedocument.presentationml.presentation", "description": "Microsoft Office  OOXML  Presentation", "extension": "pptx"}, {"type": "application/vnd.openxmlformats-officedocument.presentationml.slide", "description": "Microsoft Office  OOXML  Presentation (Slide)", "extension": "sldx"}, {"type": "application/vnd.openxmlformats-officedocument.presentationml.slideshow", "description": "Microsoft Office  OOXML  Presentation (Slideshow)", "extension": "ppsx"}, {"type": "application/vnd.openxmlformats-officedocument.presentationml.template", "description": "Microsoft Office  OOXML  Presentation Template", "extension": "potx"}, {"type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "description": "Microsoft Office  OOXML  Spreadsheet", "extension": "xlsx"}, {"type": "application/vnd.openxmlformats-officedocument.spreadsheetml.template", "description": "Microsoft Office  OOXML  Spreadsheet Teplate", "extension": "xltx"}, {"type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "description": "Microsoft Office  OOXML  Word Document", "extension": "docx"}, {"type": "application/vnd.openxmlformats-officedocument.wordprocessingml.template", "description": "Microsoft Office  OOXML  Word Document Template", "extension": "dotx"}, {"type": "application/x-msbinder", "description": "Microsoft Office Binder", "extension": "obd"}, {"type": "application/vnd.ms-officetheme", "description": "Microsoft Office System Release Theme", "extension": "thmx"}, {"type": "application/onenote", "description": "Microsoft OneNote", "extension": "onetoc"}, {"type": "audio/vnd.ms-playready.media.pya", "description": "Microsoft PlayReady Ecosystem", "extension": "pya"}, {"type": "video/vnd.ms-playready.media.pyv", "description": "Microsoft PlayReady Ecosystem Video", "extension": "pyv"}, {"type": "application/vnd.ms-powerpoint", "description": "Microsoft PowerPoint", "extension": "ppt"}, {"type": "application/vnd.ms-powerpoint.addin.macroenabled.12", "description": "Microsoft PowerPoint  Add-in file", "extension": "ppam"}, {"type": "application/vnd.ms-powerpoint.slide.macroenabled.12", "description": "Microsoft PowerPoint  Macro-Enabled Open XML Slide", "extension": "sldm"}, {"type": "application/vnd.ms-powerpoint.presentation.macroenabled.12", "description": "Microsoft PowerPoint  Macro-Enabled Presentation File", "extension": "pptm"}, {"type": "application/vnd.ms-powerpoint.slideshow.macroenabled.12", "description": "Microsoft PowerPoint  Macro-Enabled Slide Show File", "extension": "ppsm"}, {"type": "application/vnd.ms-project", "description": "Microsoft Project", "extension": "mpp"}, {"type": "application/x-mspublisher", "description": "Microsoft Publisher", "extension": "pub"}, {"type": "application/x-msschedule", "description": "Microsoft Schedule+", "extension": "scd"}, {"type": "application/x-silverlight-app", "description": "Microsoft Silverlight", "extension": "xap"}, {"type": "application/vnd.ms-pki.stl", "description": "Microsoft Trust UI Provider  Certificate Trust Link", "extension": "stl"}, {"type": "application/vnd.ms-pki.seccat", "description": "Microsoft Trust UI Provider  Security Catalog", "extension": "cat"}, {"type": "application/vnd.visio", "description": "Microsoft Visio", "extension": "vsd"}, {"type": "video/x-ms-wm", "description": "Microsoft Windows Media", "extension": "wm"}, {"type": "audio/x-ms-wma", "description": "Microsoft Windows Media Audio", "extension": "wma"}, {"type": "audio/x-ms-wax", "description": "Microsoft Windows Media Audio Redirector", "extension": "wax"}, {"type": "video/x-ms-wmx", "description": "Microsoft Windows Media Audio/Video Playlist", "extension": "wmx"}, {"type": "application/x-ms-wmd", "description": "Microsoft Windows Media Player Download Package", "extension": "wmd"}, {"type": "application/vnd.ms-wpl", "description": "Microsoft Windows Media Player Playlist", "extension": "wpl"}, {"type": "application/x-ms-wmz", "description": "Microsoft Windows Media Player Skin Package", "extension": "wmz"}, {"type": "video/x-ms-wmv", "description": "Microsoft Windows Media Video", "extension": "wmv"}, {"type": "video/x-ms-wvx", "description": "Microsoft Windows Media Video Playlist", "extension": "wvx"}, {"type": "application/x-msmetafile", "description": "Microsoft Windows Metafile", "extension": "wmf"}, {"type": "application/x-msterminal", "description": "Microsoft Windows Terminal Services", "extension": "trm"}, {"type": "application/msword", "description": "Microsoft Word", "extension": "doc"}, {"type": "application/x-mswrite", "description": "Microsoft Wordpad", "extension": "wri"}, {"type": "application/vnd.ms-works", "description": "Microsoft Works", "extension": "wps"}, {"type": "application/x-ms-xbap", "description": "Microsoft XAML Browser Application", "extension": "xbap"}, {"type": "application/vnd.ms-xpsdocument", "description": "Microsoft XML Paper Specification", "extension": "xps"}, {"type": "audio/midi", "description": "MIDI  Musical Instrument Digital Interface", "extension": "mid"}, {"type": "application/vnd.ibm.minipay", "description": "MiniPay", "extension": "mpy"}, {"type": "application/vnd.ibm.modcap", "description": "MO:DCA-P", "extension": "afp"}, {"type": "application/vnd.jcp.javame.midlet-rms", "description": "Mobile Information Device Profile", "extension": "rms"}, {"type": "application/vnd.tmobile-livetv", "description": "MobileTV", "extension": "tmo"}, {"type": "application/x-mobipocket-ebook", "description": "Mobipocket", "extension": "prc"}, {"type": "application/vnd.mobius.mbk", "description": "Mobius Management Systems  Basket file", "extension": "mbk"}, {"type": "application/vnd.mobius.dis", "description": "Mobius Management Systems  Distribution Database", "extension": "dis"}, {"type": "application/vnd.mobius.plc", "description": "Mobius Management Systems  Policy Definition Language File", "extension": "plc"}, {"type": "application/vnd.mobius.mqy", "description": "Mobius Management Systems  Query File", "extension": "mqy"}, {"type": "application/vnd.mobius.msl", "description": "Mobius Management Systems  Script Language", "extension": "msl"}, {"type": "application/vnd.mobius.txf", "description": "Mobius Management Systems  Topic Index File", "extension": "txf"}, {"type": "application/vnd.mobius.daf", "description": "Mobius Management Systems  UniversalArchive", "extension": "daf"}, {"type": "text/vnd.fly", "description": "mod_fly / fly.cgi", "extension": "fly"}, {"type": "application/vnd.mophun.certificate", "description": "<PERSON><PERSON><PERSON>", "extension": "mpc"}, {"type": "application/vnd.mophun.application", "description": "<PERSON><PERSON>un V<PERSON>", "extension": "mpn"}, {"type": "video/mj2", "description": "Motion JPEG 2000", "extension": "mj2"}, {"type": "audio/mpeg", "description": "MPEG Audio", "extension": "mpga"}, {"type": "video/vnd.mpegurl", "description": "MPEG Url", "extension": "mxu"}, {"type": "video/mpeg", "description": "MPEG Video", "extension": "mpeg"}, {"type": "application/mp21", "description": "MPEG-21", "extension": "m21"}, {"type": "audio/mp4", "description": "MPEG-4 Audio", "extension": "mp4a"}, {"type": "video/mp4", "description": "MPEG-4 Video", "extension": "mp4"}, {"type": "application/vnd.apple.mpegurl", "description": "Multimedia Playlist Unicode", "extension": "m3u8"}, {"type": "application/vnd.musician", "description": "MUsical Score Interpreted Code Invented for the ASCII designation of Notation", "extension": "mus"}, {"type": "application/vnd.muvee.style", "description": "Muvee Automatic Video Editing", "extension": "msty"}, {"type": "application/xv+xml", "description": "MXML", "extension": "mxml"}, {"type": "application/vnd.nokia.n-gage.data", "description": "N-Gage Game Data", "extension": "ngdat"}, {"type": "application/vnd.nokia.n-gage.symbian.install", "description": "N-Gage Game Installer", "extension": "n-gage"}, {"type": "application/x-dtbncx+xml", "description": "Navigation Control file for XML (for ePub)", "extension": "ncx"}, {"type": "application/x-netcdf", "description": "Network Common Data Form (NetCDF)", "extension": "nc"}, {"type": "application/vnd.neurolanguage.nlu", "description": "neuroLanguage", "extension": "nlu"}, {"type": "application/vnd.dna", "description": "New Moon Liftoff/DNA", "extension": "dna"}, {"type": "application/vnd.noblenet-directory", "description": "NobleNet Directory", "extension": "nnd"}, {"type": "application/vnd.noblenet-sealer", "description": "NobleNet Sealer", "extension": "nns"}, {"type": "application/vnd.noblenet-web", "description": "NobleNet Web", "extension": "nnw"}, {"type": "application/vnd.nokia.radio-preset", "description": "Nokia Radio Application  Preset", "extension": "rpst"}, {"type": "application/vnd.nokia.radio-presets", "description": "Nokia Radio Application  Preset", "extension": "rpss"}, {"type": "text/n3", "description": "Notation3", "extension": "n3"}, {"type": "application/vnd.novadigm.edm", "description": "Novadigm's RADIA and EDM products", "extension": "edm"}, {"type": "application/vnd.novadigm.edx", "description": "Novadigm's RADIA and EDM products", "extension": "edx"}, {"type": "application/vnd.novadigm.ext", "description": "Novadigm's RADIA and EDM products", "extension": "ext"}, {"type": "application/vnd.flographit", "description": "NpGraphIt", "extension": "gph"}, {"type": "audio/vnd.nuera.ecelp4800", "description": "Nuera ECELP 4800", "extension": "ecelp4800"}, {"type": "audio/vnd.nuera.ecelp7470", "description": "Nuera ECELP 7470", "extension": "ecelp7470"}, {"type": "audio/vnd.nuera.ecelp9600", "description": "Nuera ECELP 9600", "extension": "ecelp9600"}, {"type": "application/oda", "description": "Office Document Architecture", "extension": "oda"}, {"type": "application/ogg", "description": "<PERSON><PERSON>", "extension": "ogx"}, {"type": "audio/ogg", "description": "Ogg Audio", "extension": "oga"}, {"type": "video/ogg", "description": "Ogg Video", "extension": "ogv"}, {"type": "application/vnd.oma.dd2+xml", "description": "OMA Download Agents", "extension": "dd2"}, {"type": "application/vnd.oasis.opendocument.text-web", "description": "Open Document Text Web", "extension": "oth"}, {"type": "application/oebps-package+xml", "description": "Open eBook Publication Structure", "extension": "opf"}, {"type": "application/vnd.intu.qbo", "description": "Open Financial Exchange", "extension": "qbo"}, {"type": "application/vnd.openofficeorg.extension", "description": "Open Office Extension", "extension": "oxt"}, {"type": "application/vnd.yamaha.openscoreformat", "description": "Open Score Format", "extension": "osf"}, {"type": "audio/webm", "description": "Open Web Media Project  Audio", "extension": "weba"}, {"type": "video/webm", "description": "Open Web Media Project  Video", "extension": "webm"}, {"type": "application/vnd.oasis.opendocument.chart", "description": "OpenDocument Chart", "extension": "odc"}, {"type": "application/vnd.oasis.opendocument.chart-template", "description": "OpenDocument Chart Template", "extension": "otc"}, {"type": "application/vnd.oasis.opendocument.database", "description": "OpenDocument Database", "extension": "odb"}, {"type": "application/vnd.oasis.opendocument.formula", "description": "OpenDocument Formula", "extension": "odf"}, {"type": "application/vnd.oasis.opendocument.formula-template", "description": "OpenDocument Formula Template", "extension": "odft"}, {"type": "application/vnd.oasis.opendocument.graphics", "description": "OpenDocument Graphics", "extension": "odg"}, {"type": "application/vnd.oasis.opendocument.graphics-template", "description": "OpenDocument Graphics Template", "extension": "otg"}, {"type": "application/vnd.oasis.opendocument.image", "description": "OpenDocument Image", "extension": "odi"}, {"type": "application/vnd.oasis.opendocument.image-template", "description": "OpenDocument Image Template", "extension": "oti"}, {"type": "application/vnd.oasis.opendocument.presentation", "description": "OpenDocument Presentation", "extension": "odp"}, {"type": "application/vnd.oasis.opendocument.presentation-template", "description": "OpenDocument Presentation Template", "extension": "otp"}, {"type": "application/vnd.oasis.opendocument.spreadsheet", "description": "OpenDocument Spreadsheet", "extension": "ods"}, {"type": "application/vnd.oasis.opendocument.spreadsheet-template", "description": "OpenDocument Spreadsheet Template", "extension": "ots"}, {"type": "application/vnd.oasis.opendocument.text", "description": "OpenDocument Text", "extension": "odt"}, {"type": "application/vnd.oasis.opendocument.text-master", "description": "OpenDocument Text Master", "extension": "odm"}, {"type": "application/vnd.oasis.opendocument.text-template", "description": "OpenDocument Text Template", "extension": "ott"}, {"type": "image/ktx", "description": "OpenGL Textures (KTX)", "extension": "ktx"}, {"type": "application/vnd.sun.xml.calc", "description": "OpenOffice  Calc (Spreadsheet)", "extension": "sxc"}, {"type": "application/vnd.sun.xml.calc.template", "description": "OpenOffice  Calc Template (Spreadsheet)", "extension": "stc"}, {"type": "application/vnd.sun.xml.draw", "description": "OpenOffice  Draw (Graphics)", "extension": "sxd"}, {"type": "application/vnd.sun.xml.draw.template", "description": "OpenOffice  Draw Template (Graphics)", "extension": "std"}, {"type": "application/vnd.sun.xml.impress", "description": "OpenOffice  Impress (Presentation)", "extension": "sxi"}, {"type": "application/vnd.sun.xml.impress.template", "description": "OpenOffice  Impress Template (Presentation)", "extension": "sti"}, {"type": "application/vnd.sun.xml.math", "description": "OpenOffice  Math (Formula)", "extension": "sxm"}, {"type": "application/vnd.sun.xml.writer", "description": "OpenOffice  Writer (Text  HTML)", "extension": "sxw"}, {"type": "application/vnd.sun.xml.writer.global", "description": "OpenOffice  Writer (Text  HTML)", "extension": "sxg"}, {"type": "application/vnd.sun.xml.writer.template", "description": "OpenOffice  Writer Template (Text  HTML)", "extension": "stw"}, {"type": "application/x-font-otf", "description": "OpenType Font File", "extension": "otf"}, {"type": "application/vnd.yamaha.openscoreformat.osfpvg+xml", "description": "OSFPVG", "extension": "osfpvg"}, {"type": "application/vnd.osgi.dp", "description": "OSGi Deployment Package", "extension": "dp"}, {"type": "application/vnd.palm", "description": "PalmOS Data", "extension": "pdb"}, {"type": "text/x-pascal", "description": "Pascal Source File", "extension": "p"}, {"type": "application/vnd.pawaafile", "description": "PawaaFILE", "extension": "paw"}, {"type": "application/vnd.hp-pclxl", "description": "PCL 6 Enhanced (Formely PCL XL)", "extension": "pclxl"}, {"type": "application/vnd.picsel", "description": "Pcsel eFIF File", "extension": "efif"}, {"type": "image/x-pcx", "description": "PCX Image", "extension": "pcx"}, {"type": "image/vnd.adobe.photoshop", "description": "Photoshop Document", "extension": "psd"}, {"type": "application/pics-rules", "description": "PICSRules", "extension": "prf"}, {"type": "image/x-pict", "description": "PICT Image", "extension": "pic"}, {"type": "application/x-chat", "description": "pIRCh", "extension": "chat"}, {"type": "application/pkcs10", "description": "PKCS #10  Certification Request Standard", "extension": "p10"}, {"type": "application/x-pkcs12", "description": "PKCS #12  Personal Information Exchange Syntax Standard", "extension": "p12"}, {"type": "application/pkcs7-mime", "description": "PKCS #7  Cryptographic Message Syntax Standard", "extension": "p7m"}, {"type": "application/pkcs7-signature", "description": "PKCS #7  Cryptographic Message Syntax Standard", "extension": "p7s"}, {"type": "application/x-pkcs7-certreqresp", "description": "PKCS #7  Cryptographic Message Syntax Standard (Certificate Request Response)", "extension": "p7r"}, {"type": "application/x-pkcs7-certificates", "description": "PKCS #7  Cryptographic Message Syntax Standard (Certificates)", "extension": "p7b"}, {"type": "application/pkcs8", "description": "PKCS #8  Private-Key Information Syntax Standard", "extension": "p8"}, {"type": "application/vnd.pocketlearn", "description": "PocketLearn Viewers", "extension": "plf"}, {"type": "image/x-portable-anymap", "description": "Portable Anymap Image", "extension": "pnm"}, {"type": "image/x-portable-bitmap", "description": "Portable Bitmap Format", "extension": "pbm"}, {"type": "application/x-font-pcf", "description": "Portable Compiled Format", "extension": "pcf"}, {"type": "application/font-tdpfr", "description": "Portable Font Resource", "extension": "pfr"}, {"type": "application/x-chess-pgn", "description": "Portable Game Notation (Chess Games)", "extension": "pgn"}, {"type": "image/x-portable-graymap", "description": "Portable Graymap Format", "extension": "pgm"}, {"type": "image/png", "description": "Portable Network Graphics (PNG)", "extension": "png"}, {"type": "image/x-portable-pixmap", "description": "Portable Pixmap Format", "extension": "ppm"}, {"type": "application/pskc+xml", "description": "Portable Symmetric Key Container", "extension": "pskcxml"}, {"type": "application/vnd.ctc-posml", "description": "PosML", "extension": "pml"}, {"type": "application/postscript", "description": "PostScript", "extension": "ai"}, {"type": "application/x-font-type1", "description": "PostScript Fonts", "extension": "pfa"}, {"type": "application/vnd.powerbuilder6", "description": "PowerBuilder", "extension": "pbd"}, {"type": "application/pgp-signature", "description": "Pretty Good Privacy  Signature", "extension": "pgp"}, {"type": "application/vnd.previewsystems.box", "description": "Preview Systems ZipLock/VBox", "extension": "box"}, {"type": "application/vnd.pvi.ptid1", "description": "Princeton Video Image", "extension": "ptid"}, {"type": "application/pls+xml", "description": "Pronunciation Lexicon Specification", "extension": "pls"}, {"type": "application/vnd.pg.format", "description": "Proprietary P&G Standard Reporting System", "extension": "str"}, {"type": "application/vnd.pg.osasli", "description": "Proprietary P&G Standard Reporting System", "extension": "ei6"}, {"type": "text/prs.lines.tag", "description": "PRS Lines Tag", "extension": "dsc"}, {"type": "application/x-font-linux-psf", "description": "PSF Fonts", "extension": "psf"}, {"type": "application/vnd.publishare-delta-tree", "description": "PubliShare Objects", "extension": "qps"}, {"type": "application/vnd.pmi.widget", "description": "Qualcomm's Plaza Mobile Internet", "extension": "wg"}, {"type": "application/vnd.quark.quarkxpress", "description": "QuarkXpress", "extension": "qxd"}, {"type": "application/vnd.epson.esf", "description": "QUASS Stream Player", "extension": "esf"}, {"type": "application/vnd.epson.msf", "description": "QUASS Stream Player", "extension": "msf"}, {"type": "application/vnd.epson.ssf", "description": "QUASS Stream Player", "extension": "ssf"}, {"type": "application/vnd.epson.quickanime", "description": "QuickAnime Player", "extension": "qam"}, {"type": "application/vnd.intu.qfx", "description": "Quicken", "extension": "qfx"}, {"type": "video/quicktime", "description": "Quicktime Video", "extension": "qt"}, {"type": "application/x-rar-compressed", "description": "RAR Archive", "extension": "rar"}, {"type": "audio/x-pn-realaudio", "description": "Real Audio Sound", "extension": "ram"}, {"type": "audio/x-pn-realaudio-plugin", "description": "Real Audio Sound", "extension": "rmp"}, {"type": "application/rsd+xml", "description": "Really Simple Discovery", "extension": "rsd"}, {"type": "application/vnd.rn-realmedia", "description": "RealMedia", "extension": "rm"}, {"type": "application/vnd.realvnc.bed", "description": "RealVNC", "extension": "bed"}, {"type": "application/vnd.recordare.musicxml", "description": "Recordare Applications", "extension": "mxl"}, {"type": "application/vnd.recordare.musicxml+xml", "description": "Recordare Applications", "extension": "musicxml"}, {"type": "application/relax-ng-compact-syntax", "description": "Relax NG Compact Syntax", "extension": "rnc"}, {"type": "application/vnd.data-vision.rdz", "description": "RemoteDocs R-Viewer", "extension": "rdz"}, {"type": "application/rdf+xml", "description": "Resource Description Framework", "extension": "rdf"}, {"type": "application/vnd.cloanto.rp9", "description": "RetroPlatform Player", "extension": "rp9"}, {"type": "application/vnd.jisp", "description": "RhymBox", "extension": "jisp"}, {"type": "application/rtf", "description": "Rich Text Format", "extension": "rtf"}, {"type": "text/richtext", "description": "Rich Text Format (RTF)", "extension": "rtx"}, {"type": "application/vnd.route66.link66+xml", "description": "ROUTE 66 Location Based Services", "extension": "link66"}, {"type": "application/rss+xml", "description": "RSS  Really Simple Syndication", "extension": "rss"}, {"type": "application/rss+xml", "description": "RSS  Really Simple Syndication", "extension": "xml"}, {"type": "application/shf+xml", "description": "S Hexdump Format", "extension": "shf"}, {"type": "application/vnd.sailingtracker.track", "description": "SailingTracker", "extension": "st"}, {"type": "image/svg+xml", "description": "Scalable Vector Graphics (SVG)", "extension": "svg"}, {"type": "application/vnd.sus-calendar", "description": "ScheduleUs", "extension": "sus"}, {"type": "application/sru+xml", "description": "Search/Retrieve via URL Response Format", "extension": "sru"}, {"type": "application/set-payment-initiation", "description": "Secure Electronic Transaction  Payment", "extension": "setpay"}, {"type": "application/set-registration-initiation", "description": "Secure Electronic Transaction  Registration", "extension": "setreg"}, {"type": "application/vnd.sema", "description": "Secured eMail", "extension": "sema"}, {"type": "application/vnd.semd", "description": "Secured eMail", "extension": "semd"}, {"type": "application/vnd.semf", "description": "Secured eMail", "extension": "semf"}, {"type": "application/vnd.seemail", "description": "SeeMail", "extension": "see"}, {"type": "application/x-font-snf", "description": "Server Normal Format", "extension": "snf"}, {"type": "application/scvp-vp-request", "description": "Server-Based Certificate Validation Protocol  Validation Policies  Request", "extension": "spq"}, {"type": "application/scvp-vp-response", "description": "Server-Based Certificate Validation Protocol  Validation Policies  Response", "extension": "spp"}, {"type": "application/scvp-cv-request", "description": "Server-Based Certificate Validation Protocol  Validation Request", "extension": "scq"}, {"type": "application/scvp-cv-response", "description": "Server-Based Certificate Validation Protocol  Validation Response", "extension": "scs"}, {"type": "application/sdp", "description": "Session Description Protocol", "extension": "sdp"}, {"type": "text/x-setext", "description": "Setext", "extension": "etx"}, {"type": "video/x-sgi-movie", "description": "SGI Movie", "extension": "movie"}, {"type": "application/vnd.shana.informed.formdata", "description": "<PERSON><PERSON> Informed Filler", "extension": "ifm"}, {"type": "application/vnd.shana.informed.formtemplate", "description": "<PERSON><PERSON> Informed Filler", "extension": "itp"}, {"type": "application/vnd.shana.informed.interchange", "description": "<PERSON><PERSON> Informed Filler", "extension": "iif"}, {"type": "application/vnd.shana.informed.package", "description": "<PERSON><PERSON> Informed Filler", "extension": "ipk"}, {"type": "application/thraud+xml", "description": "Sharing Transaction Fraud Data", "extension": "tfi"}, {"type": "application/x-shar", "description": "Shell Archive", "extension": "shar"}, {"type": "image/x-rgb", "description": "Silicon Graphics RGB Bitmap", "extension": "rgb"}, {"type": "application/vnd.epson.salt", "description": "SimpleAnimeLite Player", "extension": "slt"}, {"type": "application/vnd.accpac.simply.aso", "description": "Simply Accounting", "extension": "aso"}, {"type": "application/vnd.accpac.simply.imp", "description": "Simply Accounting  Data Import", "extension": "imp"}, {"type": "application/vnd.simtech-mindmapper", "description": "SimTech MindMapper", "extension": "twd"}, {"type": "application/vnd.commonspace", "description": "Sixth Floor Media  CommonSpace", "extension": "csp"}, {"type": "application/vnd.yamaha.smaf-audio", "description": "SMAF Audio", "extension": "saf"}, {"type": "application/vnd.smaf", "description": "SMAF File", "extension": "mmf"}, {"type": "application/vnd.yamaha.smaf-phrase", "description": "SMAF Phrase", "extension": "spf"}, {"type": "application/vnd.smart.teacher", "description": "SMART Technologies Apps", "extension": "teacher"}, {"type": "application/vnd.svd", "description": "SourceView Document", "extension": "svd"}, {"type": "application/sparql-query", "description": "SPARQL  Query", "extension": "rq"}, {"type": "application/sparql-results+xml", "description": "SPARQL  Results", "extension": "srx"}, {"type": "application/srgs", "description": "Speech Recognition Grammar Specification", "extension": "gram"}, {"type": "application/srgs+xml", "description": "Speech Recognition Grammar Specification  XML", "extension": "grxml"}, {"type": "application/ssml+xml", "description": "Speech Synthesis Markup Language", "extension": "ssml"}, {"type": "application/vnd.koan", "description": "SSEYO Koan Play File", "extension": "skp"}, {"type": "text/sgml", "description": "Standard Generalized Markup Language (SGML)", "extension": "sgml"}, {"type": "application/vnd.stardivision.calc", "description": "StarOffice  Calc", "extension": "sdc"}, {"type": "application/vnd.stardivision.draw", "description": "StarOffice  Draw", "extension": "sda"}, {"type": "application/vnd.stardivision.impress", "description": "StarOffice  Impress", "extension": "sdd"}, {"type": "application/vnd.stardivision.math", "description": "StarOffice  Math", "extension": "smf"}, {"type": "application/vnd.stardivision.writer", "description": "StarOffice  Writer", "extension": "sdw"}, {"type": "application/vnd.stardivision.writer-global", "description": "StarOffice  Writer (Global)", "extension": "sgl"}, {"type": "application/vnd.stepmania.stepchart", "description": "StepMania", "extension": "sm"}, {"type": "application/x-stuffit", "description": "Stuffit Archive", "extension": "sit"}, {"type": "application/x-stuffitx", "description": "Stuffit Archive", "extension": "sitx"}, {"type": "application/vnd.solent.sdkm+xml", "description": "SudokuMagic", "extension": "sdkm"}, {"type": "application/vnd.olpc-sugar", "description": "Sugar Linux Application Bundle", "extension": "xo"}, {"type": "audio/basic", "description": "Sun Audio  Au file format", "extension": "au"}, {"type": "application/vnd.wqd", "description": "SundaHus WQ", "extension": "wqd"}, {"type": "application/vnd.symbian.install", "description": "Symbian Install Package", "extension": "sis"}, {"type": "application/smil+xml", "description": "Synchronized Multimedia Integration Language", "extension": "smi"}, {"type": "application/vnd.syncml+xml", "description": "SyncML", "extension": "xsm"}, {"type": "application/vnd.syncml.dm+wbxml", "description": "SyncML  Device Management", "extension": "bdm"}, {"type": "application/vnd.syncml.dm+xml", "description": "SyncML  Device Management", "extension": "xdm"}, {"type": "application/x-sv4cpio", "description": "System V Release 4 CPIO Archive", "extension": "sv4cpio"}, {"type": "application/x-sv4crc", "description": "System V Release 4 CPIO Checksum Data", "extension": "sv4crc"}, {"type": "application/sbml+xml", "description": "Systems Biology Markup Language", "extension": "sbml"}, {"type": "text/tab-separated-values", "description": "Tab Seperated Values", "extension": "tsv"}, {"type": "image/tiff", "description": "Tagged Image File Format", "extension": "tiff"}, {"type": "application/vnd.tao.intent-module-archive", "description": "Tao Intent", "extension": "tao"}, {"type": "application/x-tar", "description": "Tar File (Tape Archive)", "extension": "tar"}, {"type": "application/x-tcl", "description": "Tcl Script", "extension": "tcl"}, {"type": "application/x-tex", "description": "TeX", "extension": "tex"}, {"type": "application/x-tex-tfm", "description": "TeX Font Metric", "extension": "tfm"}, {"type": "application/tei+xml", "description": "Text Encoding and Interchange", "extension": "tei"}, {"type": "text/plain", "description": "Text File", "extension": "txt"}, {"type": "application/vnd.spotfire.dxp", "description": "TIBCO Spotfire", "extension": "dxp"}, {"type": "application/vnd.spotfire.sfs", "description": "TIBCO Spotfire", "extension": "sfs"}, {"type": "application/timestamped-data", "description": "Time Stamped Data Envelope", "extension": "tsd"}, {"type": "application/vnd.trid.tpt", "description": "TRI Systems Config", "extension": "tpt"}, {"type": "application/vnd.triscape.mxs", "description": "Triscape Map Explorer", "extension": "mxs"}, {"type": "text/troff", "description": "troff", "extension": "t"}, {"type": "application/vnd.trueapp", "description": "True BASIC", "extension": "tra"}, {"type": "application/x-font-ttf", "description": "TrueType Font", "extension": "ttf"}, {"type": "text/turtle", "description": "Turtle (Terse RDF Triple Language)", "extension": "ttl"}, {"type": "application/vnd.umajin", "description": "UMAJIN", "extension": "umj"}, {"type": "application/vnd.uoml+xml", "description": "Unique Object Markup Language", "extension": "uoml"}, {"type": "application/vnd.unity", "description": "Unity 3d", "extension": "unityweb"}, {"type": "application/vnd.ufdl", "description": "Universal Forms Description Language", "extension": "ufd"}, {"type": "text/uri-list", "description": "URI Resolution Services", "extension": "uri"}, {"type": "application/vnd.uiq.theme", "description": "User Interface Quartz  Theme (Symbian)", "extension": "utz"}, {"type": "application/x-ustar", "description": "Ustar (Uniform Standard Tape Archive)", "extension": "ustar"}, {"type": "text/x-uuencode", "description": "UUEncode", "extension": "uu"}, {"type": "text/x-vcalendar", "description": "vCalendar", "extension": "vcs"}, {"type": "text/x-vcard", "description": "vCard", "extension": "vcf"}, {"type": "application/x-cdlink", "description": "Video CD", "extension": "vcd"}, {"type": "application/vnd.vsf", "description": "Viewport+", "extension": "vsf"}, {"type": "model/vrml", "description": "Virtual Reality Modeling Language", "extension": "wrl"}, {"type": "application/vnd.vcx", "description": "VirtualCatalog", "extension": "vcx"}, {"type": "model/vnd.mts", "description": "Virtue MTS", "extension": "mts"}, {"type": "model/vnd.vtu", "description": "Virtue VTU", "extension": "vtu"}, {"type": "application/vnd.visionary", "description": "Visionary", "extension": "vis"}, {"type": "video/vnd.vivo", "description": "Vivo", "extension": "viv"}, {"type": "application/ccxml+xml", "description": "Voice Browser Call Control", "extension": "ccxml"}, {"type": "application/voicexml+xml", "description": "VoiceXML", "extension": "vxml"}, {"type": "application/x-wais-source", "description": "WAIS Source", "extension": "src"}, {"type": "application/vnd.wap.wbxml", "description": "WAP Binary XML (WBXML)", "extension": "wbxml"}, {"type": "image/vnd.wap.wbmp", "description": "WAP Bitamp (WBMP)", "extension": "wbmp"}, {"type": "audio/x-wav", "description": "Waveform Audio File Format (WAV)", "extension": "wav"}, {"type": "application/davmount+xml", "description": "Web Distributed Authoring and Versioning", "extension": "dav<PERSON>"}, {"type": "application/x-font-woff", "description": "Web Open Font Format", "extension": "woff"}, {"type": "application/wspolicy+xml", "description": "Web Services Policy", "extension": "wspolicy"}, {"type": "image/webp", "description": "WebP Image", "extension": "webp"}, {"type": "application/vnd.webturbo", "description": "WebTurbo", "extension": "wtb"}, {"type": "application/widget", "description": "Widget Packaging and XML Configuration", "extension": "wgt"}, {"type": "application/winhlp", "description": "WinHelp", "extension": "hlp"}, {"type": "text/vnd.wap.wml", "description": "Wireless Markup Language (WML)", "extension": "wml"}, {"type": "text/vnd.wap.wmlscript", "description": "Wireless Markup Language Script (WMLScript)", "extension": "wmls"}, {"type": "application/vnd.wap.wmlscriptc", "description": "WMLScript", "extension": "wmlsc"}, {"type": "application/vnd.wordperfect", "description": "Wordperfect", "extension": "wpd"}, {"type": "application/vnd.wt.stf", "description": "Worldtalk", "extension": "stf"}, {"type": "application/wsdl+xml", "description": "WSDL  Web Services Description Language", "extension": "wsdl"}, {"type": "image/x-xbitmap", "description": "X BitMap", "extension": "xbm"}, {"type": "image/x-xpixmap", "description": "X PixMap", "extension": "xpm"}, {"type": "image/x-xwindowdump", "description": "X Window Dump", "extension": "xwd"}, {"type": "application/x-x509-ca-cert", "description": "X.509 Certificate", "extension": "der"}, {"type": "application/x-xfig", "description": "Xfig", "extension": "fig"}, {"type": "application/xhtml+xml", "description": "XHTML  The Extensible HyperText Markup Language", "extension": "xhtml"}, {"type": "application/xml", "description": "XML  Extensible Markup Language", "extension": "xml"}, {"type": "application/xcap-diff+xml", "description": "XML Configuration Access Protocol  XCAP Diff", "extension": "xdf"}, {"type": "application/xenc+xml", "description": "XML Encryption Syntax and Processing", "extension": "xenc"}, {"type": "application/patch-ops-error+xml", "description": "XML Patch Framework", "extension": "xer"}, {"type": "application/resource-lists+xml", "description": "XML Resource Lists", "extension": "rl"}, {"type": "application/rls-services+xml", "description": "XML Resource Lists", "extension": "rs"}, {"type": "application/resource-lists-diff+xml", "description": "XML Resource Lists Diff", "extension": "rld"}, {"type": "application/xslt+xml", "description": "XML Transformations", "extension": "xslt"}, {"type": "application/xop+xml", "description": "XML-Binary Optimized Packaging", "extension": "xop"}, {"type": "application/x-xpinstall", "description": "XPInstall  Mozilla", "extension": "xpi"}, {"type": "application/xspf+xml", "description": "XSPF  XML Shareable Playlist Format", "extension": "xspf"}, {"type": "application/vnd.mozilla.xul+xml", "description": "XUL  XML User Interface Language", "extension": "xul"}, {"type": "chemical/x-xyz", "description": "XYZ File Format", "extension": "xyz"}, {"type": "text/yaml", "description": "YAML Ain't Markup Language / Yet Another Markup Language", "extension": "yaml"}, {"type": "application/yang", "description": "YANG Data Modeling Language", "extension": "yang"}, {"type": "application/yin+xml", "description": "YIN (YANG  XML)", "extension": "yin"}, {"type": "application/vnd.zul", "description": "Z.U.L. Geometry", "extension": "zir"}, {"type": "application/zip", "description": "Zip Archive", "extension": "zip"}, {"type": "application/vnd.handheld-entertainment+xml", "description": "ZVUE Media Manager", "extension": "zmm"}, {"type": "application/vnd.zzazz.deck+xml", "description": "Zzazz Deck", "extension": "zaz"}]