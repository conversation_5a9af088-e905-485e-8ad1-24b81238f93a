package vn.vnpt.digo.adapter.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import vn.vnpt.digo.adapter.dto.khcn.*;
import vn.vnpt.digo.adapter.service.KHCNService;
import vn.vnpt.digo.adapter.util.Translator;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(KHCNController.class)
public class KHCNControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private KHCNService khcnService;

    @MockBean
    private Translator translator;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testNhanHoSoKHCN_Success() throws Exception {
        // Prepare test data
        KHCNDossierDataDto.FileAttachment attachment = new KHCNDossierDataDto.FileAttachment();
        attachment.setId(1);
        attachment.setAttachmentType(1);
        attachment.setAttachmentName("Test Document");
        attachment.setFilename("test.pdf");
        attachment.setCanceled(0);
        attachment.setFiledata("dGVzdCBkYXRh"); // base64 for "test data"
        attachment.setSize(1024L);
        attachment.setMimeType("application/pdf");

        KHCNDossierDataDto dossier = new KHCNDossierDataDto();
        dossier.setAgencyCode("TEST001");
        dossier.setCode("HS001");
        dossier.setNationCode("LT001");
        dossier.setProcedureCode("TTHC001");
        dossier.setProcedureName("Test Procedure");
        dossier.setModule("KHCN");
        dossier.setAcceptedDate("20231201120000");
        dossier.setTechId("TECH001");
        dossier.setData("test data");
        dossier.setAttachment(Arrays.asList(attachment));
        dossier.setApplicantName("Nguyen Van A");
        dossier.setPhoneNumber("0123456789");
        dossier.setEmail("<EMAIL>");
        dossier.setAddress("Ha Noi");
        dossier.setNote("Test note");

        List<KHCNDossierDataDto> request = Arrays.asList(dossier);

        KHCNResultDto expectedResponse = new KHCNResultDto("1", "Đồng bộ hồ sơ KHCN thành công");

        // Mock service response
        when(khcnService.dongBoHoSo(any(), any(), any())).thenReturn(expectedResponse);

        // Perform request
        mockMvc.perform(post("/api/lienthongKHCN/nhanHoSoKHCN")
                .header("securityKey", "test-security-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("1"))
                .andExpect(jsonPath("$.message").value("Đồng bộ hồ sơ KHCN thành công"));
    }

    @Test
    public void testCapNhatTrangThaiHoSoKHCN_Success() throws Exception {
        // Prepare test data
        KHCNDossierTrackingDataDto tracking = new KHCNDossierTrackingDataDto();
        tracking.setAgencyCode("TEST001");
        tracking.setCode("HS001");
        tracking.setNationCode("LT001");
        tracking.setProcedureCode("TTHC001");
        tracking.setStatus(1);
        tracking.setStatusName("Đang xử lý");
        tracking.setProcessTime("20231201120000");
        tracking.setProcessor("Nguyen Van B");
        tracking.setProcessingDepartment("Phong KHCN");
        tracking.setProcessContent("Xử lý hồ sơ");
        tracking.setProcessResult("Đang xử lý");
        tracking.setNote("Test tracking note");

        List<KHCNDossierTrackingDataDto> request = Arrays.asList(tracking);

        KHCNResultDto expectedResponse = new KHCNResultDto("1", "Cập nhật tiến độ hồ sơ KHCN thành công");

        // Mock service response
        when(khcnService.capNhatTienDoHoSo(any(), any(), any())).thenReturn(expectedResponse);

        // Perform request
        mockMvc.perform(post("/api/lienthongKHCN/capNhatTrangThaiHoSoKHCN")
                .header("securityKey", "test-security-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("1"))
                .andExpect(jsonPath("$.message").value("Cập nhật tiến độ hồ sơ KHCN thành công"));
    }

    @Test
    public void testReceiveRecord_Success() throws Exception {
        // Prepare test data
        KHCNHoSoRequestDto.FileAttachment attachment = new KHCNHoSoRequestDto.FileAttachment();
        attachment.setId(1);
        attachment.setAttachmentType(1);
        attachment.setAttachmentName("Test Document");
        attachment.setFilename("test.pdf");
        attachment.setCanceled(0);
        attachment.setFiledata("dGVzdCBkYXRh"); // base64 for "test data"
        attachment.setSize(1024L);

        KHCNHoSoRequestDto request = new KHCNHoSoRequestDto();
        request.setAgencyCode("TEST001");
        request.setCode("HS001");
        request.setMaHoSoMCDT("MCDT001");
        request.setProcedureCode("TTHC001");
        request.setModule("KHCN");
        request.setAcceptedDate("2023-12-01T12:00:00");
        request.setTechId("TECH001");
        request.setData("test data");
        request.setAttachment(Arrays.asList(attachment));

        KHCNHoSoResponseDto expectedResponse = new KHCNHoSoResponseDto("200", "Nhận hồ sơ KHCN thành công", "200", "Nhận hồ sơ KHCN thành công");

        // Mock service response
        when(khcnService.nhanHoSoKHCN(any(), any(), any(), any())).thenReturn(expectedResponse);

        // Perform request
        mockMvc.perform(post("/api/lienthongKHCN/receiveRecord")
                .header("securityKey", "test-security-key")
                .param("kafkaEnable", "true")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("200"))
                .andExpect(jsonPath("$.statusDescription").value("Nhận hồ sơ KHCN thành công"));
    }

    @Test
    public void testGetLog_Success() throws Exception {
        // Perform request
        mockMvc.perform(get("/api/lienthongKHCN/getLog")
                .param("code", "HS001")
                .param("nationCode", "LT001")
                .param("api", "/api/lienthongKHCN/nhanHoSoKHCN")
                .param("status", "1")
                .param("error", "false")
                .param("page", "0")
                .param("size", "20"))
                .andExpect(status().isOk());
    }

    @Test
    public void testNhanHoSoKHCN_Unauthorized() throws Exception {
        // Prepare test data with invalid security key
        KHCNDossierDataDto dossier = new KHCNDossierDataDto();
        dossier.setAgencyCode("TEST001");
        dossier.setCode("HS001");
        dossier.setNationCode("LT001");
        dossier.setProcedureCode("TTHC001");
        dossier.setModule("KHCN");
        dossier.setAcceptedDate("20231201120000");
        dossier.setTechId("TECH001");
        dossier.setData("test data");

        List<KHCNDossierDataDto> request = Arrays.asList(dossier);

        // Perform request with invalid security key
        mockMvc.perform(post("/api/lienthongKHCN/nhanHoSoKHCN")
                .header("securityKey", "invalid-security-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    public void testNhanHoSoKHCN_ValidationError() throws Exception {
        // Prepare test data with missing required fields
        KHCNDossierDataDto dossier = new KHCNDossierDataDto();
        // Missing required fields

        List<KHCNDossierDataDto> request = Arrays.asList(dossier);

        // Perform request
        mockMvc.perform(post("/api/lienthongKHCN/nhanHoSoKHCN")
                .header("securityKey", "test-security-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
}
